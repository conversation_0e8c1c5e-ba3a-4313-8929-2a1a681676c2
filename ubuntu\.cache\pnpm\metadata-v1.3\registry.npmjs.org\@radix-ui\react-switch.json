{"name": "@radix-ui/react-switch", "dist-tags": {"next": "1.2.5-rc.1746560904918", "latest": "1.2.4"}, "versions": {"0.0.1": {"name": "@radix-ui/react-switch", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-label": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "efcdfe20cd290d5bd43d331b9bb1f1ef63da9690", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-5eQ4CNmtySWy2Sfaqd7pEnBD582A4fcA0iEP+Yd3UivKK4cunzPJBkwppbYbNEfkVoSsTLZr30ILzG3U8EQtRQ==", "signatures": [{"sig": "MEYCIQCpq/AWk3Zh/s2fqfyDzc/XBgd0vJhRntA9lku/DWRUzAIhAPY2DnKBXBDloOREdCy3we38he7G8SBmRl03R/Z+4lEJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24028, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbYCRA9TVsSAnZWagAA06AP/2sFl/qRBIKXVvzLeWsr\no76XdFwQDEwI7lrfTa5MiZHfDSL8TITYbMG6mR9rdF6FQq6yhsOr2r7kJdqC\ntZHc+Mg5r44fPLKyR4zNox9yMg7oDhiHsbCCoMIzhOJocy+YH3Qv8HR5dzvf\n8iOkzYgeTM1T8+e33DqCNknmewl2qrQ6pSqMmHpKaMKdZ+nsUVY3fqxq3w50\nl+5OksO7uLqZIoI8EAmV1rqfqeXAL6ufZuqqi1jyYsDUSERqGNg4n8LxBvnl\nPe7u0SeTirZpBl5KsVgEeMlC0pcGyUqmhDjy4xnjGLJJSLjZqEnmYoODG0ee\nho5m8wupQ5X69Cl0v7ofVot7ISZLffan/4B6W0h4VjnPDw4oaIIhBKt/omoG\nyjoExk8BQfKdxrH0VJFDGKoxriA8V3mWCDAtxRmyEt+NznD/qezfFcH6CbtD\nEDbxYW44KurW0kyt0fbEcXjq7pk+bc1KIJA601EWkXtNRCLaIUWyYZXBHGps\ngAy0zYMvThcLQGjGK2TWbwJevzr0DOpG/UEOWIvssg2lq2HXTu4T9bJ4pJPE\nvC+5oIN7tj4W6dlzba/GptZf8dIDn9IF2QzYN9W2f+5SXL5jSL5fNF/SxL4+\nMNSEWjrDHixdS8GsrSaVQfxkzBGVWplemieJpxiGQE6dCpK6jFmteUcAH5tw\n7yMI\r\n=5x1+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-switch", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-label": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e277f5266e8f2102d62764972c8edf67b4422c52", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-pKZGz1lTzVLNKXrHkxX14Q2rJUNwwjN4yCn0ZMnfRcuhiMWt/+FRJBrp0y0xW4N/vfukDyxJ5kyicgEGupwg0A==", "signatures": [{"sig": "MEUCIQDnoz2i4z7khkEKlQuJVbPZmARmkE1Z1cUoTxHTVkvFlgIgPseq38JFRcvqKcQr18mmwfM089mT8O6fBJ0IB7QaTvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24191, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvXCRA9TVsSAnZWagAAHLUP+wSplS/VQCMZjM1UcuWQ\np4rNo0NFxIXN4wylBk+0L3p008QBMq38NB/jhT2/jxkNVenuC3XB/G7D32O4\nhfpW19iDbScgd6HiaTv3C+bjoAs+0zvjL+pL0xKHPBpqMUjZfMMRgKOH8//X\nFtGktb2w/l9AIjHEne6KhT1xMtdlxLEd3roXa8bW5Nv/w9RxDay1dLVFN81d\nuM2yKJtU861hZtKw971DdOYH642R+K8YB5/tY0MmJq63r1Bn5s9hSDv4p99b\nEPGuqX5loZemJw04MxrjqOX/NF5+DnRw/o/IyjOAtxZUdA/5+LiPA7qefQq3\n97WvYT5a0oPjlfTUx6k4ubAF/4yf4K/wFSbZMIWn23NTu3ugejc+VmPOOYdU\nwslVGL1bAWAsx/HzR+q4i6pdVfwCWoTT97sKpbG43cBA7KoMhrCEgyjEnMMg\n28zy0Z0a8Dczia01qQi/J2VK8eTP3MfsKS6DKNXwM2UuL4xOeJ7l70V7yusY\nO2BQF6DAsEVD9x0KGWAhKGs8Pi9/eUTxIamiwfDFv1v+aiEl9VgrxmaJSPlz\nj+DLawVGQuaJO5yE2EN/PKub/xRzRdcF9+OcpD2edX4s2Y4kTsh8hlZbyxL0\n6LSVfeRI1oB5PMcxovI+yU8mAqKg3XAz8fnsAia5svsNurx07PhKDEjt8GgB\n5Y4D\r\n=s9J5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-switch", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-label": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9a3d867b578b27b667e31300b0749da297047830", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-z2vbBG7+OyN0L/lSYcIYGI4482OtW2F2UevvUfbaKjxeXY0vIJydkCdcO8yfZbyntTPbI7+fgvZhWmT8I24kFQ==", "signatures": [{"sig": "MEQCICxOyBOLtAzOkQJxBYT7ATNovOzo+MDrfPqGsPEPJXD1AiB2vU+EvrzKliR8O0c+zimV7N2OpWXdrvy1exPK8Fb3vg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25679, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtkCRA9TVsSAnZWagAAYCMQAJrHEwWWfLH7SaQ6ug7E\nmrCRdeXvOJtHIz/C/dYn+WnpXpASgqo9bci7fAYEJY7zjg/VTW1f4moXgCIL\nzfv+Mpo4kBBxPvRgoSyP+WsZOYWd3cCtddKlyJOjwR9nJH2TP28hFtjN+w9t\nX0g0SwMuMAuKIkTRpZKYZkqe3JSbuZqJXeUSVNCbyxM9eoGWfiRQMq7kNpox\nFDm+PeDQNH2807IbkTb13JpAbNtmmdf1eg7YXi8qeTc57yVaLAYwZV5sl2UR\nVzLvHtu213Vt+0LU1bT/njic3R2ygj1XnCxd0+2l0qDxMPXthKeNJyNN1nmC\nmGoUELtM3jaLc6EFfbp+/gL0HSH+ofJNbAqiXV5RZnynjLStRUuYVue3N1N5\nNKX0tvaT9S1RP9SanQ+6CF0q0rjeVT5glQsXXiaCtzyxtzEKAzYZLeD7oTz3\n3ft38v47+T4VlyHdGC6HWja/UG9DTrBVd1hFdsQrles3/5EJB/udTHwkYR4V\nSZZgfa9YxMbksgem04aFeLB67K+dFgL8TRZD6A9rTnQ4CMYw4LMLynloYR5/\n/7d0OQr/hY5/wkOMGszN1RawdeJNXaevPxfGORweQOpPU1bVExGjS3Rzpkbd\n3Gu9fxCiJzTORD6oITTtHWjnRc2IY27ajWnPFsJuWjNI6dZfaJm5wtZ0ZIV+\nDZFi\r\n=D4I8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-switch", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-label": "0.0.4", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "514458d6d74ba803e0a598d8da9e6bfb93533f19", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-j2LxaWDaFJXOH+1xo3udEQIVkspMa4hWRfZgfWaSqUzK2i60wXgWCrnL6SK8HE/2O3X77OKYS53Wr2k3VB55jg==", "signatures": [{"sig": "MEUCIQDeFbtG7FOk94RhynXh29foseis7bJ9DbZVSe10ryxmmQIgDuuRRp0GpHIcgZiR6ml/5X3qMZwetCmkN1evCF1+KZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25563, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAoCRA9TVsSAnZWagAAgb0P/37XG/oODGWHmCqhb8k9\n1hNX7bV+Ijzy2hIreP4h8k3ZbrDjFs1A8i8+sKl1yWDWAcTtArEY3GykpsSP\n21EcAwjB5W55jyN/4WVxBJ0egCUHj6IJhryG4HqBQ1RGC1Qs8uFBjKzWYBM3\nsYqEnzORqmn7LHytvF9xOoZI/ZccQ1iV9YJq3bzkncYMJqi+NWRLET0DIJ4m\np3Ub46u/VRW42ef3QfjtHgWaNWBy5C5akYYRsqBXtle7sLRRJWMsyXxFXYVa\nheWt6TwXvECnlt1RAoNQBBCQbcfT5bpGB0LZUI9itQg0JNzCGGH62kJroyoc\n37NAczfag2DRc8RVYPmo8LzzFNhRFFYhftRwMLF1xBU0bQPHxDgKA3+FBHjQ\nld5sx2cLQdWPxoXWQSSS8lpLSMLd5Egv/pV087a6+ZWQUwdejir4nbpY+M6d\nZkowXf7MrHgtElGzSsFLa89AmLH8LDuP+ABHjauqh+ATyVQ2I5IygDbfDNxr\nUa07lb4aqoo387CGvxP3qgxAb8kO9Wn3IPoRZDBenCLTLKxG4/Q8e2+l0ud2\n8wgDFA4Pp1akzMzHHXmORfj2YPBALCWIss6LFOi1JuU3g15wYWwbuOaWO2Tk\nbTzz5bFyf7Hv7eq3Maj8YGXHQZPSdTCfuHF2kmVsSUPGkXOC23/3HwrSE/H9\n+RFa\r\n=ZwyS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-switch", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-label": "0.0.5", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7b8f3de4c22ad92a35e33139467cd1d7a058588f", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-DauiyEfMMVRH2zRbXjPrJDXbK4hqS4ebZYzO8iE3T9VDGgEay/07VooBru67NTWgwiWxuJSPXV1atM5m8IGpmg==", "signatures": [{"sig": "MEQCIEI4V0RRhjHO6+BkEZppjh1Tbgv3BiZvwGdt/58/tA/dAiA71MHqmfxGGLET6Qc7P8jaPWcob70yB9sIUqec9O5Dtw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26780, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/WACRA9TVsSAnZWagAAbiEP/0urdkVYMMn6lIxhWYUv\n0COqH+CV+KhPMSYWphyiSdl5bu17YgZOp1MvV+fwtoeOxaUcB9VuHoXXFtfn\n7vIE80whWPPRoMQwswITycuiOZtXw7/D6B0S41IV/i5sSHvWUwtRGtp7gBay\nvv4HJ3JEiAgJLDnt96KUClhxpWMYvCeNwfVK0npjh/+U/vQSbVNwZU3OrgFd\nlyKW7ayBInrqY56mJgAbDhHMK78ge+abX30fzOcNe+AtHCnMsWh5MtfqqLeU\nXkmoJPGajCdBdpgh6UCL8v5Dyq97WJr30xZ4AuI1vcrr2UyqGMoHK8aQCdfn\n1fxfg56XkoILf1lOEO2dgEWpIL4v9vIGVTcTh6NP9BEzFM0RZSiHJm1IAdo7\nKJ+fJWAMQtnLojeX9zjDTif3SE17bSR2+ToA5zCHCTHoMSoc+XS5kdJgk4T3\ndcpnpyi6ZSgn2B7XpEJKlWXWF2LGJEPzBWeM6D76P9QcDG65wQ2wW6Ir7Gfw\nLw+Q4Rh+XR1Qa+WrHqsJzRAMCH3vHpc3hjEV0JWLv2USPBuqZazBTz5i5mqm\n30AfuwffylkG8ldTpUEMSMnHLBOP4ww3Xyjmd9rYXx8GFUqe67a/lKJauCsV\nfA5q5Nn5uBkrCNqx05+SXhI+rdytnuNTcPRNKJn1ZVMTz7rfIWlXJR87cc5g\n7HaR\r\n=25Ai\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-switch", "version": "0.0.6", "dependencies": {"@radix-ui/primitive": "0.0.1", "@radix-ui/react-label": "0.0.6", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5aead70629cdf5cf5ea75f2719883a27819684ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-LEFDhApes2XYD72WhgUd2ICUv5brl5ZcZsAdxmVQjkBF8O9gcn9zTAAftyqz6xiGU25UN5If5lHDZsn6h1QOew==", "signatures": [{"sig": "MEYCIQCBO4I0UpAdcgompdG60Z8iz0POAYzxtB3AuZnK4IRQ+wIhAJLCZrSwp6jvGAtfRnzVJVEl46sxn9qW7tk+N7ojEDTI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25051, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VaCRA9TVsSAnZWagAAJYIP/jcDjBcBLTkRQmxHfCNn\n5nEp2M3deUOVwss0neP+PZiZpKpnRz0kXwFmCMfMbO05WcD9y7RoVSY2NXc1\ncviMwBzUxGTPE56bSDe148ZuHqdFP1V1CherNMEPQM7zg4ui5bas2UvcfQrf\ndI3UBnd01UiV/GlSBJk8IzLz08JqfWQobOybLRCZEfE82E0r0SGNnFsnknDD\nfFVUvkj9Cj+Tp48oPpBSM3Fx1XvID+xgVlIpnWwK67iKGz2wyieUOdaOwyK4\nb5fy5O5zKzxWoe0MS/Mj+8lM1rjtGPi8fYsMnXh6xhf993/TVTgFWSApLSP6\ngszY1L6FGbgx1KG2oJSb1o8kVT+/m8rG+ilIM3jjUgG/h5a2pVXg0tyehyEN\nr62DGqFBPS9uMsSXBtcQ2/5MJHEC85P8my3qZps3zVY7M4yRo5DEszUWzfEp\n9GSZzPgc4IQ8Tq+UoZuCbBTrzozKUTzw1W/I18gU06LgNJMvWz6cELOjYOcM\nj6H5u3b/k31MlXeZzRQodqINWgPyyWa5W67SJ0RN2EdDL8Npgd7zy5cQ/3FE\n8ejZ90YfO5p3KXBVIzJ5yM/OJ7SxnwFDicEOpWUJV8hnWQkim/HFN2KqoGtx\n9yrZgdbdKZQ770CxHXCT/NUlmiq7Z+fpElo2UKrEWE41qXgOKkPQHdMHPwRF\nq3jR\r\n=Y0I2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-switch", "version": "0.0.7", "dependencies": {"@radix-ui/primitive": "0.0.2", "@radix-ui/react-label": "0.0.7", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "680d20d3d249bb4e03e80f842fc784e22783b320", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-OVC7xdmFKDic6tX5dCDhYPqLQyfpPW31u/oEkGh+vQT6o3APLs1bpjk15N4ipxr/Hw1VjJLCjTMtCq6ACnipYg==", "signatures": [{"sig": "MEQCIBK0vwmLgmWHaC9iJk1q8UeQ7L2FpxufnAti1sXVfK/vAiApkyAnT7gL+WbWEHxtzjNBZQpL0suwDUsO8StZo6oLfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25116, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPCCRA9TVsSAnZWagAAx1gP/119ZhVj5JPk6ca2SXoL\nnwEWuD4v4B36IsSiH8qJt10pYWAJUkAb2GJSStu0aWQme/FmA1tFFeHdI0C/\nNFd0Yu/nQkYJeTvfEcypibgb1jHVedpPWJK7WOPDqZg9DydKCBk7010ieqA/\naJ6W2Q3WBrvpKaud8e5T9yb8j9lrn5RbI2d1J7ELdoUMellmhUhDfXC+JWHo\nhmFgxuB6OfwQEs7o/mbnh/jCZXBGtKeLM8Yi38TkDhLSdWxoANONHQTTmM/S\nvg8hDAry+NfFLGbCDlDrcy1d0dyBW8ABtWHrUdE24amZrHsA+QRZBcJG+YwG\nSPHhoI5+WT2eM94qfQLkG2WC6N6jK9UnI3xUjVv7eZ4yVdiV+00YO9YLanOH\nCmrJN5PwL8kaBoFx4bimoHfFuridtpRmaobIEmo5479vZ4aL6w0UcTMRNoAl\nk6re2ER73tI/Wlpy5x9k5M86ZnSYbP4Cn8VHt6+gDAa03YXkzoUu+jUq8w8Z\nQsC1Zic4laquALfsMoS2twshNdTLoXnQINljQ5RpCnLAk6aRPWNp9RXoiB1T\n15nOAGJt5V9F+Gt1IIDgwk8aWvexkkXELo7PoRTR8+46uUwhBwG5YzrpSTY7\nxf+6rqMhzaKHmmqmmCDkN52KG7wAiaq9OdzJr2s/PbzOYKNk6niHEbsUGO8b\nzuBm\r\n=r6Gc\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-switch", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-label": "0.0.8", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "083841e6a17e86096e76c7296dc18ca0120019e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-OmEZ+eRE9F2puzF6tHFX7BT0/y/1pMOEN52rQRtYNHruL0L/HMQjNMzToVBPgt2KDbjrJKt0hHvfeFbOorOs3A==", "signatures": [{"sig": "MEUCIQDSaqu+TDu57KEjnIYam9aPHyUPNZM4C5IVOqQRTwU9pAIgd+cNTJTsI78accUuuWBP7RdE3557RGXv9v9ngv3/6M4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0g4CRA9TVsSAnZWagAALAgQAKD/+Ng3DDRULYHIamJh\njQK8SUVuJFguB8lRj4hnpSThGBQCYjDz6MHp5xNvQ9bJHFbEp9TyPk6NgjWM\n6vKglCG8OWCF+3+CahotlyWGua4TTL5v3sEar2ATuTvodHRN55QPTUsqCTA4\nZpMh+FuUjQ7ZaGGl4G9jbA/vvL4QoEfayU5IXOgQQCkkkuo7p4y77+tqrPSV\nkNA+O0BuukVv03rAT+YgEoby+gH4qeT7jniHcmhG85/TztIjvjZApaFOASmj\nfshlp8jrxabRN9067n/T4Liin1xE/i3hNFMBZ5jyhOcxFP4JX9cCXa6Jtubh\nS5pInD3xNc7ycBQkUZzNVdtcqRadzyAPlIe70bFMAbq4YCtkR6flC5SlSz/3\n0KRDYM5t4apyKsNMly7TNnZdIehTOV3PircLTvA3JkS0xZoIV8oXKT2elNzM\niXPPZAcJ9II4aEgSYv8PWA5M4M2edAMYz+i6+4CHfIZjO/XLhUqZjZnbKGWp\neuC4TCc/N7ry0QnHHgE4cSYPGrt+76KSboTz7yBVT/wd1qavx8H55c/guaub\nZwFRsZoxrhBiGopTggcWptu+pge4HZZsO3OtsUyxb1ch0AfSrVy2kzaJiZ7D\n1cZ9Qce8wplsOQHbRf0arCRRPFlqvKpfYIqfss5cPC6HZJD3aeuTXJYbcD9C\nLgBs\r\n=vXlY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-switch", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-label": "0.0.9", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "16de72fae7857e016fb4c241485896c5b9b44f28", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-MDU6KTf2fGd+/kTLQ37Qt8Ax1a7IofBxwYHMmAE6A3bQKECSuWINnR4u69jB2h7v+XUf3kUEfbUsnrqKymSZ5A==", "signatures": [{"sig": "MEUCIQCjODO3fM7O959nIgNismBbN6crA0L7d2evPqxaa+vDdgIge+KYqLoYoVx3Ybz6A0j6p9GHgyJPHW6N2P8Rcc8zoGs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23557, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1IFCRA9TVsSAnZWagAAyeMP/Rb/C6lOmF4Y/O7oXp1F\nK/6/qTTlweXUNwDLlkAedNozqcJkl0ry/HwAoR6SIRiHb7OTvxsFEmi8Wqly\nCN9kG1PRZWaffRDjigQ5Bc7aYlneimGqxUmPx5aKYjLXAFISeiJRWel25284\nONfTadC+uUAOL9c04gwQk03ht23VTTvlTgLSPffeOMjT/axIeveaBXEkmy4U\nHXtLq5ve2m8FGE9w7rM3nEE+dMeV+9f4ZgKsxqvNEphyypplS5HIHd7R3QZn\npFDmQ4GnsXqfrrfdWjYHR7qffK9R0z/W9LXEkE+inwcVUFbeb+crm/8rsX/d\nvviUO6J3ZIPjZBKzDal6GNb7KV6ejeuP2DJfPNbenVm7AalLLiPQzNPEftGN\nYgzQTArJ0AxJgfEBj1xXf+3UftBBNOs8rlkyV1D7UesNh8SvlzgnFC7ylBFG\n+TQhhY8x+7oNgT7ROUAodF9yRo0tF4ppcIFyNwCe/mMhVzf17uOltL9s2fu7\najiMTaC0XsTy0gcGDUyTMEAv9BpwSKtwgh8QRHeW6Z1Y9rcIHvNrAMWuzmUO\nAvTSjL1UHvhKQvnOW9/FGsdIvBy2SbyFdpQSNaSVBipEX35VZXPQJP5CZ6iL\nXEgAmJB4IPXrnyvdqtpcGi3Z1nOAHBHE/cYmxA307EKH/UNhrgnGvxmQ07B+\n0cub\r\n=yt3O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-switch", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-label": "0.0.10", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "17eed07700ad914bdf195404ea151d9120e5cb50", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-8NHuvuub6HXkZlXd/5hinxo4Uc/j5DUCSqEQEeCprNvjPedifS8Jw47Lh30hdBEv48jD9j+lBiIeFhDBsS8+OA==", "signatures": [{"sig": "MEYCIQD/hDQ19l+uL+jGkI3kPoJsvrNj1tLNBfgMpXWapTOIPgIhANBho9boXoxwDir+T87+z+7vS1EPqPIgnW3QCzhlsi/I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22828, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3v9CRA9TVsSAnZWagAAXxoP/RDeM3i38yUJcH0/eQW3\nuEAgfSnBj6CDhbL9RFSwTNAAAkUsV+DhPIuZUEvFWVaXbIvKKWzKmG1XSbAj\ng3dTblDAH+wT7LfkWToI8K5lTJq54OcBdusfFhoB1TpHy4Xa0b2pj/FB0ub3\n8sTsvZDqQQdk5HYrpHlpWPDs+MFWWOIKzP9wMy+OL2Ha/r+sF1gUrLEPacza\nE+F4alYoYGAV4y2qCzI8aQdQnOy5eiSv3HwZhYc8/sjrgXLNQlyQqWZWml8R\nhEE5Ehec6EZHy1FgS1oHadjA8qIwgXawfmEVh6dyx47QBqwzHynvQSv1IkaN\nNVapp1vgAz/LhhT55IP+o+RTUgtFHspHuhRX2C6P9ULZpxRButiCJulRcE5p\n4COSiiJhrn/OO/E+ES9SR8k635KtdWjk6FJedXNFrq53lumy42CkiKgP2KJu\nUMRm7gzeRNLuemf3jlyIWfuuVpuxtG1eBWtQxReQIAhrvgus2MKPvAt5dwxB\ni662imUefoyIITq4W6z9okbHX9NMQ0Gzkl0Wp5MLAxiOXPJWoJefArJTIarM\n3nPNAMa9IWGmcdH+n0MmQdGyllqHdqYl+EDtsvO+BCYovnx/Kk54i5LY2LMT\ngICRr/hJ3ba5WfCuaaVew5Rpqcv0SW/9Zt1LtrkeQTNmcSABLDzpdrFzb/J7\nhZUz\r\n=qZw5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-switch", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.11", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "41866308887e53434c70c49440c78d73c3596641", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-uDIFDWmq8v/zm+qM2yisna7xbESWL7oCVIgJSQ9kIjfU6u7Xk2WXBHsU+1qgdg68Y4xilyfT6m0hfUncQjNL7w==", "signatures": [{"sig": "MEYCIQDQIirDgijL2sJ3kfHYHT3wl7o+UNthazhFNJJap29LEwIhAOQfEXejO6pAwaF3ZqsrorR6pV97s//ly07BtwFvn2OO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22822, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmwCRA9TVsSAnZWagAAnPIP+gOEP0nCVGejF0fgDtg6\nuGELsJndVgOJcljp7XVkqxajXZz8dd37Agk/A6/f3eIftvUxPJ2ziwmDuvMS\nBbjyKXU0BNDeaSW5JRDJbuVBe3YcShEswR/3X6X6aELggpnoJZGkt/9CQXVn\nFO/LhBPGvyrBLNbZJkB87tJQfT96d6nXc8ZDff1NJ52uq7U0SLWlGJFXqhJa\nDzzKWHiuNxUs8Wkoh+qFDbOrCABOnFNE7mVltkKORrKhQC62oTlRU8OHYTkJ\nadVAGXYTrnkoOKxZcvzDckemF1L7N6RZZGpCkdZmRarJy3SmDn+FYyY+Ho6m\nS+JF4wYXzpEunA6QwHI6sRHH+ZivFdY76dY6i+q8ddZIQVPkPRnRmvxTpoGl\n4QAqO5MLaddANKSlLEcDLJ4peAluL9MNJkgprmqmlYpIGFpQqMbZrB7rsxZB\nvHdpXZeiyEiR/hikBB4PGmC6+mv9uaAFh80It0qfkQIujkCunhXN2YPDJ0Mt\nBk/mrFCU/PhXxrkR70cJaWEissSdSeN1qmBLR8c4fgwjTon5SZY7+Wf6BbRl\nhp+cgNlwdXwUnuHpbqHTDT7A6sPwiveqjtY8AlUGg5LHoEGOlcO8m0fiVQ8t\nvJf9prUk4WrC4+c7mlbRy85si3lDwX7upFhpM+Pk/ng4YnCQGo+zP7M1HP+M\nuVdS\r\n=sbDe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-switch", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.12", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e2d843b43bd8cdaa09fdfcfbc7dda40730bdc802", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-5VVk6xeaQ0GBnvn8xkJGp3tCuPUnm2VRs1K58rBWIrAHA9m1fyQZjZVdoYnVev6yx4mJA9Ao3dRJPEIpG/2mTw==", "signatures": [{"sig": "MEUCIQCsdNO+wHh0jcSuaMqzJm1dGxLMLLmYwqkCfblVZ9f2qgIgXEuIIBudCKkCwa/3xjvTAyEcbop1b3gPBX1VNgYkE/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22644, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7bCRA9TVsSAnZWagAAvXUP/jIGGopBmhHE5SnQ749g\nsfNYO4hvG0shRX2xdHUOIdKzptb82DGQf8ANPG4J/zplANEv4VQzU6Mgp9so\nM2mmqirAeN/YUsoySG+HZninZesiQ5B0J/Gktt9lRi3eGWqk/hVYGh7i+zem\n9qRcsDG9swjg1vcxW0fo5FGbcHq214CFmeUyahAITk+mTMA5jj8T21X9mB0d\navRuv2Bvcp+F4F9Ud4VlTVzHL2BYPTUrARlYQB4+vDShYeWiML8/Ve0uh+dX\nYQ7y0tz8ZUiaT3OQU0QGYZo9WpFMafEdDzOWf2AldpbFHBGHY+476e3jTqQ7\nJgv6wcyi/bu4ZjXSIcYWQQvfx6zpeF1CUVKj7k+KBQT8i3gq0qa6Eb8VsHHf\nxBrcPW/Aij+KNolSFYKmmot3h7Kd2lVwzsBqj+xvHRikl8DVBrOIh0UGCgG1\nW+MT25Ce6k5jIVdP66Y+9lPVA0w26Cr40bf364TRTDcPRDvfJbIkmcXI+5K1\nwUq+J4dCIOgvc7uNBv7wpzgzHWnc7Z9sVJLyIstmKvl8ie2aJKwNEhBcznOI\nOWazf7pdRzILRQsrVsHN9rbAqwbGqnVZ7csakWajjogiBYVd/ltie59dNxqG\ngvxRiycyu/ewXG0OfXYcIgT/R30o0IIyjEvvceHg1O/PoywiVXISajG1HJ1s\nmes8\r\n=1+Pd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-switch", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.13", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3ddc9588bc10d4feb9ff640d674da03d70e8eab8", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-zGAFTBrBTADn5dRROuioHsxU/pHcvyN0sUk1uN4KXJakxsXyeRn4SNRqgvErsF+3nhe8lSUiqAxiPRs/mndv4g==", "signatures": [{"sig": "MEUCIQCRp3R9r3Yunpll3pkeFBIoXR+0rD4M4/cCF25MJeOTMgIgLsDKGxiCGp7Y1dwOi0fTYmY/9euT9BcWO/WbPiQpXs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 22770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYHCRA9TVsSAnZWagAAzu8P/jTwjN6kVIDs9hZxcRlh\nBLnEf/txwFSefkig99BIao+MiZdV5x7uc68sqdvbhkGuHSwAEjX9ylFyIvOR\nAww0eiOAEPzrfT0Je33o5+rjW2IzAmi8KZbG4usFVhGgeApOADgwjaUiX73s\nvHTa91F8mc4GoSxnoTp+n7TBczSytN7r5Km22FEYf0DHxcClM2weZhMmQtoY\nzUAf2435uKvE+Y3io5G5tpfrVB4WE3PCPeTjToPuL+xYBU546PTGB6wCedUH\nrKNVQHHp00XvttAIuueqX5ghvI2K0OBEtNLTzyk9eJsDilKxthKZxyP26Ioc\nvK+AKuwY5eItPYNJrrcH0OoHgj93S5e+q9CobSjY8AQVixm51hMWkrunG1n+\nHEGStKSYHamLsscKLZ5L4Mw5NFiswkmgvNVSiztuF5x1wJSqbU6gbycdV4iC\nY4M12vk+NJHjZD+oS7Q3yNLBoPLxR55kTeQ2q8EZsPJ2VEZ0GETPQUtprH0X\nW9WyAUOVItRX2veBeQImhX1eFrHJxfVwApAQLES2RheoUDSzaxR+dlNhMUu+\nZADsSNJaLDb15eEbM3FcrmNZq7WnVCpJ116FcHrWPsv8+Aq4zX65FSiRWReG\n7qM54XnXW3C2jTdF2SP626jQBsRezZr3eywWnsAZakhjNSNsOj9IIMN8XtIQ\nmaQe\r\n=0Yrj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-switch", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.14", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "544b5269fe3e680205801a5ed2fadc61a9fae58a", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-AU7kLGmhsyTWO6dkNVxxICHWrqSaJ4S9peKT3+Dl5S7tx5jdvq2DjbiXwLiFTl8L16g489kXarWPwhuUNoPqiQ==", "signatures": [{"sig": "MEUCIQCnEtEbNX/nyauxu3ZN0pBwAugaOTvmBQIP8NIlXcYUfgIgMLo8mFF8CizwJL009HkcsE8IoCJ0EjGenLUxoahkj9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ92CRA9TVsSAnZWagAAvuQQAJTn77becLPxTJ14TDeX\ny6ZzRMWSDRMqKA/E0rDYUIfPcnqT0p3u4DWWfRsKvLpJdl8fqP3sU6S4J4QR\naJcPQJ9V5aJx2kdT8A+ZgTYRYUmfCFV5mJo7Pqb6vtW3zIB9Qfh+ElwxyeHE\nqIsGvHaDNiAO1Wr6FO/HCawbiEJ/iuqQPWPp+spn16YoUuIeWPsnXcmBk9SX\njAt8rHx+XAw4red08qKrU/RKjqKUAnPyyi5SkfqhpqRBky6uEVuV4wCWX4aY\nliiR0w1xSegZhwnRLZ1DoE+9hIsdmT34lmexdcxUgwvbjRt50A6DkiVAg9aQ\nBfB/OuTYzkRqUiiFvsn5SwtXiQxlZaFuKu6Q9bjTBFEPr1l9uztfVg9nxkGP\n7e88/AZrYiP8Wz0BkI79Iguyo7DKq1nNp1EVD70gB+WeV+jD164h8zkyM6OO\nlHyvOvxj1A5rx7punzdekbRiQXKiZQLHZOkNg7eCeHMTvysEYvdC2XLyQig2\nxzGd2Lj55AhSM5GOFhNqiFUQTzqP9aGlQ9YGLZG51dCQkI5M7Cn1l+WZ7bBV\nphNSF/xx9RCfcevq42dFuG5m5vQ03B8TOIawStdS9OrlI/XZX/URW8HYh4Ql\nSvMfhcGSmB9uMprD6dT7F1MtyxpGNc51yA6ndmeucvOxu7I/ljJaZzfgNKFB\nqaC8\r\n=JTah\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-switch", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-label": "0.0.15", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-use-size": "0.0.6", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-previous": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "675e0abd509ac211f6c9193fab786f17bd335de3", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-2f2fhxvZSb21N+Va1lV4wvyY+zgPkJoKZOiK3rEH9zAmkyQ1nIDeI6eKwipeRO9WcGMeftOZBgVQTZhWSK0Rag==", "signatures": [{"sig": "MEYCIQDo6peLh4eMy4kqm/lv/b49LVqTkFQtFxZound2dBR+kAIhAKswPtf3rmI47iDnz7wG2yx+51csOs3kjLKjQBh2WvlS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29640, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTyCRA9TVsSAnZWagAA2LQP/RIlKKHmaCensXLFihGA\nRKs2COlQwEmtz1Bc/QRYk3/246Btl557h4ibAKQYq4kGNfY7kkAKa4vH4ChX\nvn/1WARekL4HI1E7fg8MAKsaB5nIHkm1hd8AhVEvqsiZL2Bx59jVhnhwsDDK\nMyq53qDBbjHSbtU+JHu2DkJNfFH/MKIJ3wqskJnM6vMEv45RvWw59qFtvXiP\nGqw0KH4QxzOPzbt6ASJiT9W0n8XSLk6aUh2HWtVoHrm9FIEFJnSuS+pf4Lz/\nVNQ6Biecr2c2wksL1PnAFq3QR+0KCKpwy+SbQJ9IsI/RsQTRXcI8zrxbEOMX\nKM4aFTtQ+9Do4e33SJ+0QBAqrfgOQ2lYwr3lfa2bypPMuCZ+wSns60f7zJCo\nIS6nMcI+qvg+2NDKZn12lf7gOaXAwIZpl627OJVKSOLNt3NUuc9sScxPrsML\n5D7jBZQq+6uJdjSRoO6XVJpBpmlcTDb/sVzYr7BhvRvxELlp9wNGypphwbqF\nZaGawL38FebVIr6EJCOOwEkx5sxWqTMUjtOww4qUAyDcsqB9OPcIOd4os4ex\nKdtMPMYKqRnbowO6OI7M7hB134n31nxcGN6nSAWsPZGMl0I+5R8tHBJgA49I\nPIMvjAXMbhH0GmvvneLfRPw5u32ZtJLqLg+Qo8dQyJfY9Xu0NS1pZrnjpUJk\njb4G\r\n=j6Fn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-switch", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-label": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-use-size": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1", "@radix-ui/react-use-previous": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "da4d6b00588e3e3bfea2241faa5b9bd30c063300", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-aFLkcm8YJjWJKFbIXFNgrIh8Goh3EtpkxaXfdj0RwM3JaZ29fTeV5Io9YoFRToWDXJPZ6QuLmUGfy4vwV1slmQ==", "signatures": [{"sig": "MEYCIQDxsF4mc2fbueTo+sv4GFCskwBGMlh+3BW4ile7BtO0OQIhAJ/qMFjKMwqA2hOdO4NbuAP2iFxXlivsQd4oeEQtWwe0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgprCRA9TVsSAnZWagAAPM0P+QCJE5uaSfu5p1NIxz3t\nIqZS/Dzv9zUaMTgALudiXqHlodch2kZOJwvsjFslAp1qLPmefNvvxsr1sPAR\nDw2Nem0e4Etc6HzqUjb2iBPz+F6oNMLjnuJdN+EZS7kw3MowSqUKDelmeA3Q\nxPG94KLEGe9kSCHHj7urjXiI4MfAeqRLde/A+gL5B/07OLV427DSHpE2OjD7\nyLIYufuKt/MNS700Vc3ywOzDDXGU4InN5Rd1i4+3VjP6X5UGy4pE1vphK4eO\n0YZKTazCZsc+BKlsnJO4MM1bDLUkkEQXLj0TuEUmI8aGsGoMLUaJiOSd8Yzr\ncK8bbtDqbON6ozE/mR3I2/dvFFDU5xeJbLIkHw1RruKGu4GmkcUgK1Rq+ZcZ\nC9MGGFn+gOM3Rr0teMVSSnOGgrK1ReGYSjE38TOhxjd1iz77w8Z987vd+IRe\nF7LOse9xqP9U4/bp5ChOTtq+1DOMs+KieCmwtA9/2TVvrgGY4gqN6OU3QNPV\nSag7NM1+Brzpgvcpqxjl+NCeF4WIlJMVwU6eIY2y0YmA7asqMv5gG2vPnPJA\nVfdE8v43uHWaNn5sOVnTTdZ8mti1LCBl3Nl3qPaV07CCjh2lkYYe2bKk5wuL\nriMPkhm17BRUQaZjggsn5bYFAMfYibW9bBn3MziNwy7xYt9Sv1bvj5geIHF0\nv7Ns\r\n=IjUP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-switch", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-label": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-use-size": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2", "@radix-ui/react-use-previous": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "97444485f899fce616f669728782a5776dbb5260", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-HSUfzrF79D5dlZ/SnQ0doZ0q7UxmUZwey214HvChn2sTeBOKX9dMZNEzvcy1UzSZhH//2Jqh0+DrsQzDMKPJEg==", "signatures": [{"sig": "MEQCIDJbpB7RPT5yLcWhkc2Toe+7wweBwwUrVnUhIxiVGBxJAiAxSzX8mFDPuDx+F6gRqcOIzg3PvmBnDcxd6ob5kWjXWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhydCRA9TVsSAnZWagAAWxoP/0zDIj1+Z1R39H7EguDE\nM7Dm2+SEEelGGtpB4U9HUT1w5ptG4hbCALPO+E8TRb+fHETCR1JNyLigK/Ol\nHq1Q0eCvYSjsSVODs2kPPLAjBTmUq3BQMLBviLYXnQlGrECGwHVbyLd0WEn5\n5fYMx5CTGf15bbnxx5r+eR4vh8aknQA6W5XDGnvLQ8qRjl2JfVtLcS6OTycQ\nKMV3ZhFYdQdrNbddQeyFvM1QJJGadFDBfHHC+fAS0HmGZFyw3EoCeqFd8/ED\nkThDsYppY6qUJliRkZ3eeehFMONxHRLozKoeXKz3RMyAAtUQTT2Z/qHgYVUA\nsdKZNqBPGlNiz1G1vjJAiBX9BY+LZ20E6mXSJVXir4Uw0ewy/3wj6dHhnjoa\nhcTX1iupYWU97mXGirVV7J9b8o99++t4G2ECvh/L9Py5DCDkiKfG7j0V01ch\nNju4hhqzyvxsifLnNhB+LXEHBmDcJh4d2E0NRBWZuMT3tJ/RXG5TUPmmZeYx\nAvFae25HQ1mmJPJakCASiqMWsJZPDf6reVV8B37XxOs/x+ArBgBBEdvO1Ba7\nFOObhNSDm9+yUDkj1plTD8oHLPb7+jzE6VxuR0UI+r0Df9MaLU4Ok/VaacRt\ns7G2XPTY95UfwXdod/WWfrVsaU2ZBdgpYnBdYOOkOGhm8gqrD5XCX/valKrP\nS+Ah\r\n=mvh8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-switch", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c5cca26f350fba1b48be7667311720c78437bd80", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-RWUbinlxY1msYVwlUwpuUsggnMkjO6iV0zhmgRZqfhQSPrsEhaAG3nDXSuGwp6S3muZo+E54C8cdyt8tWvI5EQ==", "signatures": [{"sig": "MEUCIQCh0htzgjtvUTz+mh4fwRjpy3lY5YmVFB/Vj3+aWVOZVAIgD/Q7cCqcTH13GLSL9ZoW0TwaHwVBHTFCwXeud7wuDkE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28528, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmqCRA9TVsSAnZWagAAh10P+wS5V8IKvOFQHE9lYJOt\nvMYOfNWS6jlEWaBItuCj5rUx34omT0U0g+m9B7CMdoUJkUrwgcsU9JFuzY4B\nnnFLeaAYjceJpyzTVgzd29Fz0NNd5KKoxKglOfWP5evL/SpWJKWMVEZaRMep\nw7GjEgUeDg9gXBhbNrO5EgYKrtLRx1lPEgwFXxJOGLDdy2s15tfpThv01Xy+\nmh0SvYCFhM8MJpNRt5GyTE93ZRMDSOcJpfMJkc9i15F8RTEMCqH9Fmr7YXJc\nt3w45/oAYA/Vxpi2nMlL/KcUIfds3SKlaq/ibmqvTF2Zco3ESH0sUJeY3kKH\nsvkgGZnc6bAlY8e5zOW17hH0EL1ox9Tog60LdN+do297tAAywGb+jf/1TR5t\nQ72J6ONzDjbRvD+/2v+SkjttQNGPW0ywrHECYCixAu/Jj4kWXZXF+oizbgqg\nsCcRevaMGWyhw0Y7J/LTE+meeq9gEFh0ANDTyxFhK8cVgphZfFKmc0q+Dv3i\nVhCYMxbz/d9txe7+5zRBbZA2SB/AnQV4z4hPpg/aHfPsaFPEqNqvJS7gl49x\n8sqqfaJG21+vtoXXOA4iUZWIu4KuDfqg0yKp7AQJyUxlvU5wjchkjR8QO2N3\n+/DHrHsKEcLDReOfvv5REhJ6Ouatw+0TYPA9PB4LtdfJKycejCFzz2gVGwHF\nnNap\r\n=XdHV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "52703fb53964b209b0c0dd1f1cb93511d7112496", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-7WhipYLWhEziJs7Ufgxr/8cItJ4gkh2kK3VYV5QJre2yVINoon0huXSdsRuBv0Jg3oFaIn2YqhBrQKc0W7q7wg==", "signatures": [{"sig": "MEYCIQC1w+8kVVxbMlAQDWCizY2XB6baL84iB3KgikhGzSjwFQIhAKxXl+TfZy9vfFXitAxXATo5bNAFykUctnjxvVoqQ1lV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQIn3CRA9TVsSAnZWagAAfewP/3T3IzK7BU0TH58McDHz\n7IrGCPtPXuQvvNGm2JT3vse3ztgd+3e4G6upOJjIFFHyDUvyary30ffYnMX0\nh3FB1mWTMQRBqz0F2hqaP13+xGVkk7LeJ82rv4PvwG3ph6oVuKousuKN64qn\nNcEfJFTGUodr+w6a6BHhSnjip1xvOprp0ceIxsdr2DJ404r7BUmdgavgTIfu\nFYV+6D+92Sr2dJxVTq1pMklFzyT80mnES916gKJtomcKtNMQH+2w8k1fWCN1\npfVihsUchxDCQzrqeaQAa0Cpq3JEQIkEi2zlFI4ZsfEJapCkSKrcDsAgpWRc\nPbUAHceNsZtxu6BeCy3MPVd7ORptKcI1rf5/lXTxm99ATuipVFAtqS2BSKAY\njdVNC/3srxJBC6Y12gkztFuBujYVdPd2WQPbBYvqLUAOo/W7Xt/cvOl5PP7X\nEPIqYF76NdwhQ5NsClwDNLSUOo/MV+9VdneugwFW/STVQKFiJkOO1eJfD2pp\njYPKaXDlJEtyxbBek6i4ybiv3ujZC7Iyt1m29f4g9i8bbks5iGeNUosOj0LP\nUujg23+xqHJkzsXO9obzYbswIiQqErGMPg2wsT6H8wte6O9ZF24VNJLFHwIk\nS7DHXHlOcMYpzXlWZOc/VkxFNVB0L5co2J7idX1bk6TDkmZi02a6Mil6wYYH\nemAZ\r\n=0kYq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6fb6862bc8fa523b6457ccf77e537f6f039ca994", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-yapMR6r9b0iECCAli0AkldGiY5DdfMlAU1BNsFRZnw6FsMSDIko5+6DMlVMGqTZyYdOZLL7J8FAETry0GTRVUw==", "signatures": [{"sig": "MEYCIQDoAceTF6GW6Y9aq0llIcEchykAO8XfS5LHG4xNV9GVOAIhAOftyJ0HEUQmLoiNutJxVVPVDDwWFDZkGrVspzSsYPZ4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwkCRA9TVsSAnZWagAAA/oP/jvosbfXsVbwFWPkHQQs\nRbiwNm5c8tYzqhWREXHGQQq3myRbYKwidzW7kLeB0ZdiTOSi/lY/CfUDSaXt\nVtEoTXWD9T/K5xMR93+DqSku0PWbOiwwYtq2sezoKeklA/Zq2+qKm/MPvn2k\nprpWuO9DGeYX1MsuBdGLSm7YDa0FXWaR3DEIa64tpf5YqYuENxtM8boB1J0N\n9u2CD62d3a6o4y41om9puhukqUhV7q83cIq+b/dhh1kVEMIYesmAT4px7taL\nUEui61N5avsMkwqRxY1HFAXtt5HBYmucwU4utV17GFo7XADKA0zQPXbFeiTh\niC6o6AsZk1ffWMszx/qMs06bqRVP0gsedTQSByB5tpqZk+CmphlJib+/SLSF\nDIURr1ErcFAoih1I4N/MRoB9PlkiLHNud7ZnxM1OTDrd5tf+MLpT6u80lfcg\nMaMduPi/7Ns7Wtr2YMNkNnGwQP3QVLfSWBUjesJqQWw5jINHEdtCQEQ9Qnun\nEqdr3QhZGX25h/SgEecHkJ1cT0UOoBhAsld3UJjfwKqF8x1w+utajWDWAp/y\n9H6QX7M/pu/8/yVyJHMgcYwVx17IoKGizma6tgOdBnf6S1rdi28uV748hpJL\n0CaoY3R7Ah8iINAMX8EHhPMa8nMgXcF8jdXzheFj5tg6ujMtHh8cvnv5fmYj\ncOD+\r\n=W4Qq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "276618c2aab15d65f565e0fd12a1e17bee7377a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-v7dLBcR1nodWsFYeywVYQqz7rE/t3gADkveX2WQeqsUzmwqzjA6XpgJ3uvknBM1emtoII78+HuB6sEPMCO9i7w==", "signatures": [{"sig": "MEQCICAjBaHmWWWRUaua/pd/AESjkxuSlJbePGAMgi6UQJo5AiB3aJgpImJ/eqeW+VIWGJEJXI5++T9bcpBLeKxY+1lF5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UkCRA9TVsSAnZWagAAJZcP/3SpWl2mGuvUw9fmJVBw\nE1tWV8faMtb+WE012V2oQZN61BAh4+hWuKLs/icimGEiWXQkT643Brt8S1/v\n8Up5k5wljGO2UdLRb2I5pu1icGks87jbTKhoYAv9G9ObT8A+tSF4BDwy3Zho\nQWMmHWUAxUUhPiB4KYx5TSmZTCoZ7JgobdZeWnNHmVrg4if+Dn0QBcmjpR8t\naH2nKsDumFzeDsmEYxbyErKuD8uPntz9rGykImLg0QXdRjNiaf787G2Brx/G\nQyoGdjFoWWFUhdmmB/bJVbjc07IjDiqK2ib1UtxAFG6J/PWB5oVwdgagOj9S\nj6qVaXtG/6C9EWUP+z//hIQEKLVX+Ib5jyUmcjgkc0WRaBonaaJ2mrCqY75J\nMqHm5flevtr/BBIPL6zh5r3pt7Ddu1343W2Td8sYgY4Jqn+5rEfwIQsLnFMT\nE3SrAogzhhygID/YE7h7tlj6vOMZ4IfZRq1YeY4VF+rFjgrTLJd6/xZzoyGf\nw7s/9GxM9/+lM4sZRUeM8mIsQHrMDdUgRlH4ClyHuYtGy2h4Jlwabnr0pFnx\nOnfr9TQ+jrqPdO7pGs7xT+kVXHXWVoEK3paXBfvwTKFaFlQkScM655iyjZQD\nVewOzCBgPbk3RvvlS++6Uqfb6LHM859S+si5pVgq3uEYVwqOs8IoXMwxMLc3\naXu4\r\n=VfJE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d1bcdc4b5f9cd750f9c1effe087e230ab11d9f75", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-wPLXRrb6SoBhHrv3C0CV7foIiDQr+uQBgYBCimBx83BT6N2jomeMSkaOhv150lNDrDriH4QwAjJ23HGjXFmMnw==", "signatures": [{"sig": "MEYCIQDR/xiGvOZcwufsZ628jTfFC7IngHGNTrLQiOXttCDDJQIhAPg1lIDWRyjw5NGLbxi6pZOWKi2b1edvt5m2hYVIdk4y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ10cCRA9TVsSAnZWagAAbHoQAJ04a5GXTeRRO4pujMwN\nMLlWCnAaYYwz73ebJPNoJrmQWJd09xzCz1z1NO/NPI3YeL7g6s11rCI8hb1e\nbeif96W/AVYOmOlw2el1c9H6sEtYDmrF9heNh0l7LA/6IsoioqUnrx1IHu+p\nM69u8t30OytNBphTAN7Mo8PGWAGOQ9g6hFAvWZJYAQE708WCZ0ecOsbPTNde\n3pKW+Q3cLh5MmFIyLUnoxIMrPcY+4Eg/FA6LWmnLrFEd8UCTJ5oahHElcPTd\nVZ+Qw7ovvjm51AzEa2iCWkNGQFq/Zy/rQyCtZoIW0Q3rEU3vMaOXsA3eVNCX\n6QFFdzjoB3gmJ7LI/dsfc8dYLSs/QHUhtpGWxFhOJqxQ74sVWqTKYwGkbi7u\nlgUf7izpP8OAONucO2VC9tu/IQqyxTSWbTMg5Ixru/4n5rtBfKGq7KLV/x39\n+MqlbGErsoy3m+TzAOv/1tyxEJPSrkSmYSmq9pMAM7nYYUYZf1IZTtlmenhz\nqtiZKch5DIECx3WGlsq/rJMZd725EXSyY3mvsAVGmGdzoNHYUb5I5K+wGz6E\nOuGT47q3ISFmkPVW/7oALHZa8uXOyNxEjRaoQBG5UzunTo/8Aauy8o44WapU\nh8SH9I04vJL/ziarGjZ7Bd1j+95coEvSOFz7YD5r5YQpsgHzHHEeNmM95VOj\n75G1\r\n=82zT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "803386c2645b48ef1b779a4645be412e6dcd89c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-X1laucpPchKM4PXvljBixyAAKslFq4tqfHIgu+xGcqh53C4RYxaAGR8L8xm/IYCM6oDz5mt0zdIPH19ZouUx7w==", "signatures": [{"sig": "MEQCIC3F3iUZTKSPJuKct8QM/AkAQCeh6Dfuwd/ouMg3dBXIAiAnWjdw5PE1u7NqB1IxfUNx4Ti3iumWTk/xAXOa2/Zi1g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28571, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFkyCRA9TVsSAnZWagAAhL8P/AiIIrThHNjlZWEwfKIT\n4+x/HNXVZal8DffI5DyUJQNIQOVShH2DfEfH5EWrZJO4dYefo9F8ENNJLSnX\nlUsbE1CqK78zxx4FycflPq2DGs1LjVfaxVDdJVlOF0p8A4rm5gf2X5oV2OZn\nZMxZ51c5Xpd33cdiyAeNbOp7rNbeov7Jsma7yF4nFy20UdJQ+Ck9I1+Zk3On\nyoNK8apcsITdqrjdCHuuZAdfQW9gHjGwzswwwYI2rpUNHnmEWjnvh+HjBAeq\nhbK+i7hkEJ2N4h1MvvODRp72v+wk1LY4ZoCfRRpSD4spIUM5KVzunxbFLAl8\nL8Mkyyf00uM7bxuzVgtywilSkjdnv8jkWrWBbg6xKserMdHsAieY4lAb5hYg\nc77L9znaD31Nqj6RD9tspeP2mAqX7x6kRjopY4szArqk6K0pDC990myZMgr/\nDdY/7ms2nK14+4WsJxZ+8BVBb0gGmVFLHBFs7PDau1zPcmXp14/pYsRem6W0\nQRR8NYPZmCYp4afEg5S9fvZiqz6Imrw085KbDQl3k0qlxXOJNQUyjWnYVJlI\nJLIjWJTvVb0aVukwJXQ1WqVJVrYBeCNUFc9XvAeG2K+yMO7Ch+iRYZsCIkvY\n9fSOqfKPH7owCG4wTdZkYCGXO93uA3c7R+WvBPl4LS+68QYOelz6UbjwH5+9\nVhYh\r\n=Mbzo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "311fc0a9d3c8852c6b93eca3194f5e976276d911", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-KwUYQ+mVvPfqw1M61+XmK5+Kx56USROQvgDGbg849WBlj7HikN4LutlRNLzV+5P16Q0ji4zS/A4LyltpajLB+w==", "signatures": [{"sig": "MEQCIGwHjP8cTRwW6nMGt274gruMBhqWkJD3Z7poSeuvXWvMAiA75TzFjIOYWHBZVTKbrjNxdLObIDVi4fX0F/dNuLLFFA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28571}}, "0.1.1-rc.7": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "aead241c0c0bd4d1b1d9e2f4a237e34dabf06918", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Y74LBg1dQidUUVDZhkFrxb9qbjOxVsvu4KX5eUrtiDToMH+JuBt96j+KXio+oT0kBbCpHrm/tcPp1IpWY/mIVQ==", "signatures": [{"sig": "MEYCIQCrBpiWR85a1UtsdZLNema+Tm6eDdFWbNPpzoSoQ1oM4QIhAJ0STlDsgtyNs78MpcxiESfmWstifCeMGLKLZQaiMQgP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28663}}, "0.1.1-rc.8": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.8", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e1ff59fa17f6791c6cba2852f2c4b4702897e77d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-RNeo65YvSZayz5KGDlX7DEZX/uluQsr4man1L8JVhqA53op+Y5BKrqpRBSqt8McDMmrUkdpVgwOEtV56ED9ISQ==", "signatures": [{"sig": "MEQCIFcVVYYeRXaEGwqSCrRgXgAFgm0+n+uk3rpAYmTYe7EhAiBrvwHAmL37wQ106jETfmsT5AB1DTp6f8mXDmrkcN5r4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28663}}, "0.1.1-rc.9": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.9", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3708ed03167b425b65915a53c410377b0acfe417", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-JqwRTR5uPOP1yAP3i8GMFrFt3J8JFJynGmghjVGa1YmsKQPTwvo7euf5Ofpgmw0CQzjx1JxD+pW0l3cgZRZ9PQ==", "signatures": [{"sig": "MEYCIQCKEgzs6zbbbMMh5arBWycyo6xrZQfoGJ4f3xrD7IFLuwIhAOVWHi4j9RH0eO+kzLjxoRoqMfClY9JNKGgf14gxhWEk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28663}}, "0.1.1-rc.10": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.10", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e6473cf0d4efb6b2a1293802682cc52a75cf202c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-ZTESqZzQ9Fwd49PfrGKcynLlNscRQ2EVhBosSsbBHc2/u7imKXAWmK5ICaReLTm/j/BXqf8J1bgdAgMKUN423A==", "signatures": [{"sig": "MEYCIQCfy4ruzLLqKTo3YaSo5YRF2V/SHILVkrvQrp2lBhinnQIhALcxoqULKJWyUHnCUm/WPx8kLK8M0R4I2b6a8iR1xkDv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28666}}, "0.1.1-rc.11": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.11", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "517fb2663a8f3b78591726a5a23b0e1388a2b8ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-mu1u/UPiMTpami2no/I3WIrq473n3aUso3tyIITKWUfHTHHGbceNVkgn7pP33A5juTanhcO/Eq5jb3hg5Sxs7g==", "signatures": [{"sig": "MEUCIQCDOmiIVgSFEfpJTd919SjuQMsPke21ekiVGBgCHGMA5QIgevhOcFVJKg1ljgzi7H47W/shelc8CGa8mA4y+vO9h98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28666}}, "0.1.1-rc.12": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.12", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3474c098052523f1a0e35fbe62cee706a0c82b6d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-qtM8Q1BPymBUSdy6Xd/7cFGgJSd+9iD4w67RoLxxfnIsyyCOsq08e6nWwFcNNGfCv4Gfrc1tPSVFQrKp23VFcA==", "signatures": [{"sig": "MEYCIQCi7WK2Ec+AhNGm8xNjjFijHY0K3ROG6Yxn8iXeF7m64wIhAMLfRp0KGFQaYGAw1OYtxXKzm74uxQ04YQ3RKfykG5oe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28666}}, "0.1.1-rc.13": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.13", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ae9b90bc6d6748ed3a245365c61234a79b2c1ce3", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-bsLu01sP8ZjCR/gjBFEspXuvdLFMGRgAN84VRVY/1y1TdCciUwwtuossTOFUSE1Ppn0pyjH/jGA2vSHdCH7t1g==", "signatures": [{"sig": "MEUCICigrR4dN5Q/g2m5OhEfkMzJIEMSu1p1updBmV20Lm44AiEArvNT4XC1W/uaRTpREsom65KEoDj5KErGHWhHgXhPULw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28666}}, "0.1.1-rc.14": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.14", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e91f00f432230d7d0887e5d3fdb6bcab2fabc246", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-uUD/aT5kpD2BFZpO6rezMynis2obcrTNwpY//nG5J+QkqC7itGdeXm+CEayeIdcvfsfbN8ZcE0vRQxWJYTG4bw==", "signatures": [{"sig": "MEYCIQCwCu0rf7wsNA4VD+gqP05n7dYUneypB74VXWr5p4QDWgIhANIel4jDs2n1s8iCQjZIuHZZyWw7iYiS8YdbKn3iMe3H", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28666}}, "0.1.1-rc.15": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.15", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a62229a8fab2ccd8bd481b74b53420f88a34c457", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-eh0sfvW+PJRFjeEYsIxl59WjBEFwqZHFK2ExftDaA2i9P4ZwgnORpNEuyzerUFH6A/Bu00vTUewTfDPD4MNb5Q==", "signatures": [{"sig": "MEUCIQDHsFfPss1kHTbCL6vFaHV+o38Y0/5aHW0wWW3rdjeEBQIgN5A7UvEHaqkqnbj/+zqW9nLConUW5yDWjFNpfCA1FLI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28666}}, "0.1.1-rc.16": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.16", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9e686111eab3bf7012752ed07cfca0cee9f9530c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-oXm9sLxf6+kiSe//HvI3iU3nY8S170uriNmLNsBWijIeM5atbycjUAjR2+9GQQNLFM0gcV2A9XgKhh7EkH3qtA==", "signatures": [{"sig": "MEQCID/FUBbXhB0EZPinj0eAnnEiCfE84SpjCGO3w2rB2AdFAiAH/b1sI1zyV89qWfcY4GUPkubzoABwqI6Ma1sPly0Fcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28666}}, "0.1.1-rc.17": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.17", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b4abb61dccca1510faa9d4dc216a1ab50aec72a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-sCZ9VENZHMCck0KVGlDGaZmOuRC9I74xViMcg2RiChYrhYLv8sYtAr7ktEUeIPJertlNXEyucJfay55nDfcxlA==", "signatures": [{"sig": "MEUCIBtxUf+EiUAuCTwwDv8jsckJk1IcmMYMedgY8oglZGrhAiEAvZJbqFngB4PxeAY/q9VUs2W1BrPlgs79e/4jTDeXMwE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30441}}, "0.1.1-rc.18": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.18", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4e370465148616a3ce5eac0212d70dc9a13a338c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-nxDgjqXZemCN7w6QGba64tATLa35uWVDoC77gJu3cp0MHv4zNQdSL0BeZuh0a9GTIgXO0rOA9Py2odZn8jKc3g==", "signatures": [{"sig": "MEUCIBZRnI0nrsOl+lXiAJVgOzjfeWRrk8Cj7lo+KlEpENxLAiEA9dI/NVX/y5WLxt5YHdyEEx1R54WB+/VxX2xQB5q2NS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30441}}, "0.1.1-rc.19": {"name": "@radix-ui/react-switch", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1-rc.19", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6c1b1328f48e2cfb7f025ff8ee220ad4191e4ed4", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-pydHomfpM+/YJzH3CNtU+C1Y+pIJeaaUEpkj/b4ryCBxTGMZPUpxD9F+RkZ3TjcTjEBh63/LBlbCagpfzF7VtQ==", "signatures": [{"sig": "MEYCIQCWr8IOeYK4HeqvBmjnWhVep4yhJrX6T4Z6wfG3JVAcgwIhAI8jMin2fwgT6rBO5InGh9pI3yBHx7kVcv2X0bL6wbM5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30441}}, "0.1.1": {"name": "@radix-ui/react-switch", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a60090a26387fc018feecb0eb8ae108e2d693472", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-QlLGNj7NnMUoYj+9msP4q9/X7ZtHLqVLlH5CSzEgoDxxU+wh5+65oTyBynWJLsF9QligE9pJqOwMdqo4qqnBhg==", "signatures": [{"sig": "MEYCIQDyUDBZEddP1GbQDfaEm/+g5fkSXhEe9LlapGpeLtZIggIhAM7iL+MOswO/cSx6PcQ14T/kLreHm5GRk6fN8XY7jKJP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30390}}, "0.1.2-rc.1": {"name": "@radix-ui/react-switch", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "642d71f9b85481f18f8bd0e7c179ab5dea46f916", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4dzIOyqQ3UVYEPpQ5Yhcs9r+6FUhWJh8ykjEic+qy2GAsHTIdIQ/6GtL6SZy1EFtZQA08DsL1QklU3tQjVwpzQ==", "signatures": [{"sig": "MEUCIEoqRrLEOc8w242oeoWlmlMmGy8xsaYJN9gAbng1I55HAiEA35lkOswv558SEdS54Xv3+nS0iHVvPU8QYh/Qm/92yxg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoUjCRA9TVsSAnZWagAA3C4P/1OYQhda6yksyBNt2XDi\nRDJR1jEEWda3HrBN2mxh9WY5xdchye+3psDYdW8eMtnG6VEgkoUIMqtoWRcL\nMWLFL+MnixOtLZ6GqiMiS9bABflGfp3pKxrOZ4CNda69l/DlgzXlxgloGFev\niQx98NGde7WuMK92QGeMEU8QwrK6ttoU60m049RDZNul4uGEYvfHZXpHNjDE\nCvkOU8OtlujBcXZPhOStByxg7QoOfjKtgMjBbf/9oEx/Xz+pB8iA/Rizjx7n\n2Swy75xmzwNr3xva7NLstpV/c4DeQENp1zzEhmgE4hyXT6nkztUa9Nvt7UPU\nXnP/9YyxePAIG1HOuWgV71+6DCwRM53Q25IBPdZcFf4noW+7+1voTBJrNKk2\nkNj88sT7RJWX0nszfTMq/OrcgX0m53awl+BSk3W55ItuHRXs5xL6IQZyjOFS\nF86J31WUxzYkP36IalSXZbv+/sHD+OL7imkDwWHLtAo8OIGKoW7JV0+lA3PE\nVYvgkvjSXiyMSvtlWIKwLlG4yVl+It+mYovb3Ky68jWd2t7NZYpshRrqFajJ\now/jTYwzsn4AXLTntu6shqp6N5h3gD/DJS4lZ0xFNat4ojJdnhqeWBlGDU95\n6O9H8LvzK58Pz8VVew8s1hoPX6yGCylRJY3VwuMQsLhhrHa+Vq4SrkBMoIfo\n27+C\r\n=RFp6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-switch", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e0607fba29338fedd2a9167facea40690bc90c36", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-jevNHYhY7z9/x2LD3mkTFKaHFyViHFNWlCv5GzNUXbnNGth18paY7OtIh3i3RyQJMfSj4UjVLJZ/ihGSeFPdHg==", "signatures": [{"sig": "MEUCIEnbPUqFmsNkv5hChyOZDizmkfugrimx7IoXO5vFQToNAiEAyVOmTt3hByvXqtK3sPsasMTKEsXdsYJ++pPRfwQ+oa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiBFCRA9TVsSAnZWagAACigP/AmFb5vejJRdDQ+3axqM\n+vMUlJDTaLBH7aAVQXM8douFHwqhOC5Ey3mTxZKgaljKzemCRwBkqAAuAu0q\nHSvVGZrC4dJWiTPdoCgpLEe/MSwDF/lsRym/ZXEsWuwRu6Tc0NQH1axdSJOL\nYD/R9b57R5ItYpOmNdMZbiCe87rC+sdem/WVPFWdf1REc7C0+y+iQ12Q0zm+\n7LrHL5XErJKZpPqYi9/OsvDYSq4BkVDY478bzQxLXfQw6FokuDUfk854cE2K\nvB5jqokak5x9pMsNehjaOZZGJnQxViu17IxbWMWXFPgRmzA8ePA/6Alnfo4z\neBwi65bNuKITICwKx3WOLPXyGYZgxX/kRQ4eYLZH3WivVjV7b5LIYTIuee2B\nFKtu6gjWIMpdzx+Lsq2dFoXSvNSbTNgvMzoQQX1M1U2rrqi692IkRWxQzJNJ\nfoN11+F2fF73tDE/jbphM8kVLEYquGCN51LQfavEWLF5/6sPCTAnchstbacN\nQ0unXjHhXKAXKD3p16Ssr4aCdaHsSCDma/8kkUzFqIUvnUk5c6FVmePjFzKM\noJH3f1teAm1wqmzHGM933dyDLhIuu1VkJwNlO3b9qRztwDcDh+myD3Pk21/G\nTm3cJ+nJ4fT37i8fPYaCt72ty7ZascUzEmQfUtb9KfbrPN7f7VDNGs8F0C7l\ncRkD\r\n=MDRl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-switch", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a39aa4ad3601f25a1a7cb0417b521dd1e35fbe1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-p0USYIWKK+MzNTEHL8Yk8keSL5heZ71u/s0RPClWjEvr8LjMNVWbK7936pp8Bo6fAIpJ4B2EjaMO7g7cjc5rSA==", "signatures": [{"sig": "MEQCIF4uU4uC8T0q5VpO7sFlpgp3qvnjbrIf30CMkppZUy8xAiB/VSRHBPYpGMCf6a6htlHxxij6Ydhj8El8z067hP7+yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiO0CRA9TVsSAnZWagAAOTgP/isfr0ZEJgSvD5jgCeq+\nNiFCn6Q147+J4DgHKWozv09/eWIUGl4haqL59nvbHogbgmX2Jgdtmeqa0Bbc\nwxS4XGf/Q5ypw5p+Zx5s/RpKIxl7A7CM3cwGHqT6bIOwmfFu7F4HeRWisamE\nhhB7xpsF3eMhal9Ut5Uf+s5QG8IErmwe2Y2yPw3CyPgRiGFGh9e7qyf5rFik\nmi6Lfy3s2E+jlGV9DnmimAiLJhpLe5G4FTx3ChL1ZGYxpYdJ1uItX5BlLzqf\nHm9GEC0/o78XioS14s7sNtdBMOJf1wbtLvnBdh2PYJXpRU399Fy0LCftbkpi\nT9YsKf3/omlANLUbXfTLJ/urbVIcMH/rCcG7TN5to2VRMidQMFHHojDRQjs2\nyPODKBw7TvKdcTlXBno0smNprdcUsndSbTcrsIdL3ynHBnDK+BKJBarjp41u\nSPpcB2bSWVqBbIxIqPJmGqtEd/qo1GvSRLlF9LtdY+Lf235mm8Zf0h3VFc9Y\nYUIS8dbj1zWV3ktvBD5SMT0roJ4TNSnXL+EBeq6/ESj4G1eq6Ni6ufScVgcN\nTIpJPBXqrYBUNHQLpB4nx8lu4swqwrhoghL5aSxyM6eurhU6CDCf7uEkPIj1\nIrLMC9e8CverjicefsD8Mt+wbxGoDtX2AZoEjTYZHEL6rhjhoziSmz+n0ZoH\n1uFy\r\n=ubv9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-switch", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a27ca989aadb87f78f7d64f1156660d3bad7f938", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-2cKNifdu3+hciemFOr7SB57WeDGZF3/VRcmfg2OVRS+QmL2OEiFf40hafLnqmah4TFUYHRtJIEpx46MmhXy6jA==", "signatures": [{"sig": "MEQCIGJ8J+x5vIwGQuV6ChO3yERrILL+JW203mfu8Xp+IlOGAiBYNAWcTWmT0K57xgl5Lb546VChgGed5JjjwFIfXQoziw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryjzCRA9TVsSAnZWagAAMTgP/1BesK9Wv1M5wkWEFiK7\nZsiHoZXIkgecal3SDJ5vL7wXbHNM/TJz6bJK9zopQvoHU53PTnAjyorUbM3T\naqTjqOpIjqF9WVnKlAKQPOCOtqaL5vb0/e1qP7fs8UKNgWFWmexFdzrWFZia\nghx119Vyi6paFotP2FBaGq48Znv/eRoIAGcPQ9+c2FjEnGryzRmluap1Cfo+\nlAErbNOLqvFajTckIQ9EFV3ZcqWzpp6l4bU/9LfYu0SBQvDabk5ZYLIDvOwf\nyqKzl5mvtP/DTNx+WXD8uay4L19iZeLa6Yjya8zOY5o91a9e/NWLfVZWRiMz\nbD8rDZV0a7eYRLdiGoXWUpES3QNbC1TKUrAFCvXpnSsYl/awGkkDW1DNGbkQ\nKjVdWDKVcC0HIpnWLQqFx5YcLwXGzxVquMVKs634b98jAlmMHZ6ZmH+Q4HDp\nm0/wvCY7PonaQxq9t+m1MuSbWPHOaT8oazCgzV07lDMVT7eCqIzs93v6tH39\nZ35rWm5A44aGYB7WDgvdbui71Vhy+r2MtDDl+8Y5nF5lY3lx20jlmwASdysC\nXOwJNQNcB3vouYO0I9UdXP9TQl9D+lHdYhkm2q492POfofdjF2LCF7DJNAcx\ndUwUlVLzGhkNQFw+6eT1Walh05ExK0vDVm/fL0Q+4R3T2rxjjLCi9A+xmzJC\nsySc\r\n=k+K7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-switch", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "994eae1b8321e4e86ddb23a2b801c055d77a2b79", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-MQlsFnU5NJz1U7Ldfo7YIm4lwwjCeTjDStdB5NN6G2W6devl6LHJlPsWAbIGnXOyzkuUr1rNOprgFFBj2OAkkA==", "signatures": [{"sig": "MEUCIQCTQciooUJmmIWPHoU1NsciSNKPBPMOBWSV3d1otSMiLQIgKizbbbaG5ctz5Xrd1lu7y3WYfuGO5QlMBKxf6HbVcJQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzR7CRA9TVsSAnZWagAAxRoP/3Ugoz49iCCo146xY0NE\n3Jf7pmeZuhG2lnLOy93x19xkqs6K801vI+0gmvb+epS84qWlfirrBxt3Sj5d\nnYlLKpeD2TpH1YgQqCJ4LvuwFcmzW2zZvOqebRCHoCsBmnae3tOrh2RVYMNP\nvieMJoLUHQex4+SOWfmNAGyoS4+tkLOueIJ9iFQ6tJ49bHvdT85Ca0vDY0Pr\niR5meH65MVCgtfKaEjOtZSOQ3Z9jKhP0RKg53Dvf8PBXVBjMp405ZZ6rpqVh\nK8MN/BipkTr6h9RNh1Wg3QZUEA9Bzkq5Smdz+DhTfhWeMX29m2gRBOB3H4D5\nBO6M7PsBq9sa1cxo/XBw0o5gdLi+4bOCYA87oJIb7znDtCcS7QAvk4YPG5uL\ngB305eOFesqB1Xfg77G7E1SUj1T/IHp7DILCgz39nGC6/Djnw4M5AMazEWgE\nwApCKrwtMB5zgiSf7L652sU1/6Z45Rpd96KQ4jUtkc7JlPbqCGd+xlp0b30A\nRQqEgSkFh9/HKgpfKovH4QrYtgtUMqBmt8H47JDm1Tm0ija0jtDmlDXUp69q\nyyR9jbW2vIEUruW+cUnRZ5kJyUMqSWDlvCFEkCrPdAhTXGkibhvBCbkFEUzt\nOsUEXtO7hz6Zl4kfiRDMNvrn3aXFLS/NL1zs2ds10l0+HMrxM7YXQA04lHjh\nlnhZ\r\n=WrYf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-switch", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fdf2d33cd15ad8cd512fea95926ed0b72df46d2e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-FsAvIjCqxGx0ZPb/MiKnLToYT5KvGlZw/O2FVcbTu0HA+XErx7RGHyZNuhT9NptvVyyq0M6OksR2p73Oe0wZLw==", "signatures": [{"sig": "MEUCIQD428X/fGr/lxaWV4vgYKkVbstj6+4VOOgQMDTTRjsi6wIgBryhUWqQMf9GNktoA+l0Rh4VZeCjFDchpRopKv8xOFg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr43PCRA9TVsSAnZWagAAAOsP/3dqPyLas9AWwXpOV8hy\nEKhrb0nTHG0x0hVC0YQtROmbOyADhpKheOTYn5xdcZ35pvwtQvyoOi9nFEim\nmqBzgLVSVUI6J+a20GcU1U50KA0JG8wp1c8Pkjw5UAtat8zvDnkVQxRaDz1j\nWhvD0xJ1OYGCE1MbXmj+T1KLX02qhyNKRnMgI+GcfsYL0twQXJeaSQ0pnnm/\n3s/09tvaXDUuLqIG4KJqSx94vUG6WNoEVgZwfHNiyEycckvzJMDqSqbiH0PN\ncu/rtPtwztMXNE83vI2HrdoYU5m1WxEXjGceKcIR0Y5gXTbXrkMwpXmObyds\namDHA2YV/nkHJNxdD5R6tfjV/xoR8bG1CkzEosXomP6ZeaIPje1hQaJVE0t3\n2gWnSFth2aj90lzcZJBx/qYhjzwzj5XRH7sqyYKAHn4j3iSOxu2AT12vEocO\nVDOrLkB4qhmaG258bgsffgVHHzsw2m0GTNolj9a1xmExQanp6zeqEQ0IHVjz\nj+MTwR9exfdxTrc1WRY60Al8JnSvg0DIwN2KKohJCBhIrxNvwvPgxq+3X+E9\njvaNeMZKVGsSEzOlAM/CYpko/eP5/F7p1BGhR4bXP+8cMkDwUA/ywVOF990O\nxu0RUagp0yk9qAmuME64w/pcwaaPbbVFLv0BPErFX/4Z/5ts0bxz02R+w1ME\nato/\r\n=RBH6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-switch", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f46d901cf562b992ee20ec4a5ccb31086f68cf1b", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-J<PERSON>K8+tPnU3HT4uTij4dP+XDJpk6lDTFBUpIG7fOzzIMRRGnOHksKsbaEXAAgJ7ecRQJddvsOWfsJTsGwaszTiA==", "signatures": [{"sig": "MEUCIH/zKGnrxBcctssVD9opulzLgX1HKv2DZ0qxvyzbigxfAiEArNzbkDH8R7xN7wN+gZ8wzlTqNJYz6ByQ0hoEsCA6ATw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshD7CRA9TVsSAnZWagAA0QoP/jA52s0AFceUixd1U6u9\np/hXnbOeJ6r4EVrl7pwyZV5crXUdPJvXPz/iHBBOdsnNEPOoTQPL1yNyrdtE\nEItojLWDvJDJM9uFrWcIgcJnuOjquW6/JMRtSkMJauO2tb2xfqgnE4CR0ehM\nZS0J8jtSqxmENJ0Z9bObUZETzxKSzwdzAE51iu5nYLNIkXDssqOJcqCRJwDN\nO3PjwoVAebi2LFmSCs7ahBXoubOPwXy58RYlZpE9CpO0f2aVeqwUB68L8TUX\n1x0LtbIkFu+CDlneC+bPkDvpJPCiMCLk13mfj/mvzUU/Yo+q04J20pWRlGcU\nfENQjJrhIDjkgdKDo/ql2McGDMHvorWuqY3uInzG/E62lvxO+uV6PSHcD32e\nNgXF4xmP9xjYGIap5rz1yb09+6LSjyqxaRHer+NqT3sETALfsWyu4PcLRpa2\n8DmCxqwD1aj4VCwFBPp+WKK6u+1mQDexTIWaXcS2G10u3UaeqSsiCVG8XCI8\n8wqulRIuqGsRONi19mgovvE8c7H2XluLj8aqLcFeMvtXyz0Qq7Eq8eVyA8Ys\nQqqSKXQ93kEnJZE+XmLZwMDFAEXrrvSBlo6KgGWilPTVVe+E7BYM6DX9by20\nSYNqcBTgJg+SyffeTUYu2tCNBs6zGPP8DU+pitQyzgPE9qczzjsw7xbjz/hj\nwRIS\r\n=X4VM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-switch", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.3-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2662ff579378153e145b786541ec01072215c12a", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-BYlvLbn9KwS51nef5KC94z5Bzp766hiUEKFgRSaNamza/mVp1kRXQYOeUqawNV2F48UK1aBNl+BYQpRRO/iJsQ==", "signatures": [{"sig": "MEUCIHNip5y7qZLYaM92yl9Jl2yeW/nqrlJvVH9Oe3eYHffKAiEAgBoLelH1MsoKtxOT8XA7h/g+1gbu+6GReQWOzhjtBh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszAyCRA9TVsSAnZWagAAO34P/3ysuRg92JO6JgBLaAnY\nVwBVTteKMB19zcsO11lTHuaLuucsA/PlDLl2Kr9bmxpnrM1YZrt+xCbZQWjW\nHzBG99jJpZjmaD7QdTE5JeVXgV3ojQ5WeMbGwJeq18mq7wcX6aO7e5lfJF7W\n764bBJNfETa+bM0BKOLMBbhuNQAiUBrWN7H8n1olwoz7d/YqvYd7Of2geOgS\nYDxcZE7D0+vNp0Wj52C9d7DfPNdR6yqpGnH79LQGIYq9OTBdWNmEIfXuC3oS\nrz5FiczF+JP0vKoS5yczgEf/iSLADcFwzp4cf69jTO8dIKo5wJBb+UJUS7Ap\nYm3hN3vK7StbLTm48+f5J891u805EDkfutwqtdN0JJbJKvIrH8WJu5bWru0k\nhE13kl0vDFiErKx2Nv4Z7N9rKoGPP3U6phBu3fqaWWFK+LlP9Miae3myQujR\nPFTcu5Qky/7KS4Lk0A2VlDqPOK8A7XxrhA0twrellHEMp0CkOQwgQ/LaIBhK\nTp64RLJ7oiSu48eMLDFkPxGXatAWsv2XJBO6Zu9YrcN+EMOLfDjw2a0mDUIX\nmduM2TgimOqyko8G9x/TauZqOv5NZK9chZoOJhJa01TxyrN3do/IUscFsmDo\nN88A5ig1wqk6s2hHDsKenJdYkr++it4TPSkCNouke6aFDW8z52CFiVWXMwKv\nM5zy\r\n=1svs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-switch", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e1e11279daac71b0a993ae907ff9be57c045d2b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-fPfshhfxzzjatkHFscOd3KoqQWco4z+KM5YCvvfNShWYtI518nzFyNkYJQSFBFw/JCA2ThK7gpeNCyoNRe7kVg==", "signatures": [{"sig": "MEUCIAT5LyoyzjIjrhb7NLTqNDZ2/HlHP8DVaXr/LFFXjOy5AiEArMUMaAzn2s9GRjHDq3jGL3tYzS8j4dyexFUXQ29PNxE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszr8CRA9TVsSAnZWagAAGrAQAJ4OocjufpAdi3vMzir6\n4OnYX+41NT/fJ3ZteJ9VmjwcUIkGesNkHtDwWVXTjaaiK2DrNOg2FPuomFCN\ntsBzxH1WWwFnY4EXk0ChImOelHz0FVty7MiqCUHUlIr/f04B1Brf8sGAgbgW\ngQ4SzIrgx/WX1lCgI4O3pxou9Gxu+rL+qGqTNFIah2RoDcgN/5l7V/M5jRiF\nsxDEabxtBn5c1WB52u6wz4zAHDEYs460MLYeOnWVc7nbBaTtKz3a/XldHHxG\nL7Os3o3KKC1TCJcYRsjbK9ttSC+ZH+SzhBcoJJ/bnk1SlmFf3ogl78kfrv+8\nsm+gRLjy0UGjopUymrJAwJ9KiUanYTv6ZOJ/MzmY6PX/K6QP58BBSs9NS+3r\nXABfEk38GNm850kzW4oNuTY76cQY4W9BwrJYypT0jkEbMt+qIszrDf9WD6GG\nsSLlCm3iB1VyS4YyjlbeBH8bPf3D5h6PBssVLoUQe1qSYHLg28Pddn9rhHbW\nDWQotv42jR3MiVMUeJZEv8zxtlv/OR5Bq3h8w/Umj0Sb1GEnuwstTmpiHtoQ\n46HIfQIWcn+7TEHRLkaGCb6RIx2j9xe8em35P5Agjx0r+asuOMo0f31ISBJW\nk6DHMBSTanzvcTGBr3P14bgBVJyIkcW0wG8n6r7/7Bo3RleyLSJAHZMhDpH7\n/ma7\r\n=x/Qa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-switch", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "92f21a5150c55535ea432998e40e5fb4f059730d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-GUK0hw/FQX/MJ5Zp5W6e69fqk/KO6bRP0t0Sx89Sl//WSdcgpwk3CimV5seqk8LxL1wwBkDR5zE185U+LdBCOQ==", "signatures": [{"sig": "MEMCIGS5+7llKdrdTOCOF68PyhzfcHdr6OvE6gdwVE8RFWGRAh8W26nQYuZtno5rznzukjp2sgfu/JRDiME9o2wO2Z6I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLirCRA9TVsSAnZWagAAciQP/A5DLdBQoQG2BMZmz8pp\nX/ylPp/Bw8TD/AA/UW4gx5xvwtAE9mOCf752CoKFKYVtFdOomUlW4qZyOWUF\nDDyyq7NuVYPKO/ERhOuaEvHClcfAcytZD/rIyV5AHZE0ZxG1zi12Nq173Uan\njytDe92HnAdRJCYZPVF12d8PoYC8LFgl2QrVMpoiow/KPMl+FgdlUA/AP5xS\nx3cBgWUZX+hXZJSjiGZkLsrjHskW5baEIt9DtWkj81hSupzgAv/0YNR+GMS8\n4T8FxQXYsSzJv/R67+AI4POQ8gAm47BOO4EWHjy2qeYQ8FYv54Hx1pf6xF/B\nM7HMSJuZ6bcC038+qtuc9BFS/b4c+HCQLfP6nQBgjmDYvj4OcfR1FmnbuHze\nAMzRJ7CTnOjP+7fzepepBl/an30CGiGxtc00LTfA/onu0+Ohp7QEi6mwLGMR\nnFYX/RSkNb4ZT2v8oYvzKyBbdLL6G0ZTYFNV2L7QJkiY47e/3uueIFNExViE\nBIk3kqq3FMywMC41RcTXuXYE0JrLJJWreFvTocCFm5FTG33xQMIw/B7Qegln\nPgv3TRsBRk4+PzRBUYnV5jiQQNaZ3kDABdSGgYbSfPQkvUIjSm6J0yJEiEGg\nyQOi3QxypWG4d0gDFpYpopUDpwspEzX4mAZ0ua41fbzvh+7ypMeS+nKBQtGV\nCPgn\r\n=yU4l\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-switch", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.4-rc.1", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b67d2f5ee63be18659f5315b7c8d3ad10b7ae60e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-9oaSBhQ/P55Ojs+6njIjAxR9qC/hvetNmKJ9U1KNbxw4gNJdTCPty2ub8rOUOClOMXwdeVYGLfwendcdpLsygg==", "signatures": [{"sig": "MEUCIQCUfgjL89wGMFgb0oR/c2EheFMmtstl5xdpeuGIPQP5/QIgG04DBKhYcy6EWk7xbtY5z4F95/Gps6f6CYnV7VuRD70=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLkCCRA9TVsSAnZWagAAfrQP/iFWImCX0CluJ9u/gRaq\nmEw/ogdlgd1QvL9bvtrmJC6h9MhteXZRhpYmjlH8xlusfZRJU99I56Rq8/zo\nDTUVC9vEzMapljEXfqG3Or+ETIldlBiDR1Um3wI3J6dSAn6i8l1mdSQRgV/c\nBzgaBtzc65AnJ22FVdWt2keFU/NUV82no5X28NtAujn9qVAR7ulZnM1a5iZo\n7uOsVvm0HVhDo4Os3QIQsb7KVBaPE4i2FsYAVdaqyxJ1OVHGzN+Y0KQ2gf9C\n+bqCSXdL2wtveCDXxov5yTaS6b6GdmDGxR9RHYxd4mde02pQ9idJEA6f19IQ\nbhuhJ/EqNpnx7bq8IkWODKnbxNNfNdmUx2rlFOg7WjLfLtQ/UVIVyxiOCWwD\nIQ7P8tDceCSwqrj6hLfv4KjQzbfo5EXVx9KjcwkNvu0itOHB7WZdfjuUYeNr\nWElYv6EHyE6POWoACKtaXXlEtEHfblVGyvcVjMQ8UieZFg4+zkofPp5fYMKo\nixVLWlEK/EAWWMCY8XpWqNT7hLZubXZbo0nmrzC71q+DD4qAelPTmZvXwcpy\nnkx+j5j/bRu9XbQ/fT2ZrWaFsQOOh7b7FBNKPUtQ59FoPo1cIn4t4iPJ7/71\nFa4tI0zlcsGE7SC6kGXjLviuvl9a3VFrVj+Yvjysi/6dy6aSsTpXNTeRO7Ok\nB9PU\r\n=n4pF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "af153f7879e532820546b1a453c6a944655b4087", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WyE2o18w6/SPxvzZB9Lj6JGrVg/WQmq9QxFDhtZk0GXRzZLIopO0NuIFmOrZrczLC/G3pqlaRjWbyrFeEKlI4Q==", "signatures": [{"sig": "MEUCIQChJqOcM1Cu6xXdHTrnyAQrQbRl6X/W0gduoInyDivjYgIgIbGSY9orSfG7Rs1GDkMW9yFJuTCZgL6l1STWZOIMNqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30428, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh3du3CRA9TVsSAnZWagAAzT0P/jukRY5C3eVna0e80wO3\nw23ZgA3WtmrvBEB/IdGmvVvmsGSAID1tbAmCouTJ0XfPbW5/XTsic3jpHSbG\nQKTJZgS7MigkgRjXfBoGzMZOBWGLbPbABRS8ZJ7jcGHGWZhrNoXBw4WzHqBU\ndHejn4oj2indOhCHwrLN+kYrD12ih8zsmhl0eBxMNFPcp2lPAOOXg0VH3agC\nFaCb9oaknZwMa+SZ4FvW1NmNQe3CT3Lc27G3XyqRcREXelJLqC5TTTvZlOVR\n+z8uNbes1wPeo5AKNVFkrIvL4+6pQRIWf2yKWNJN2stIkCpX2AemapvRFAr9\ntdBxQyqYSJ9yP0WeJcSh0DW2Dm1V3HMi3loRAdR0AbCJwz/Eymq3FtfbW/VX\nVeWlrw4U8Pv8XVK1IYLEEYlfuTg5wqjDeZCmCYR2XoWuCMyuWqo4BiCHD2ct\nt/HGljCHMhQaabPokazgxuv9fCojMCnD4bJWfmDTT2P6ixyb0npDKi4jpQ2A\nfWaZfw3lzY2KeXRtTfWtiPHe7S+IVlrIR+mNam7lIApfK1d/jVOSAGrOesI6\nYGkp6PMnHMKiDdYvXK8NaH+M52J+OZw8wROVLccA1At9e4HuInuU17OExB8b\nTxRGfmXv0In/FN2XhPIE15EE7fqHacyrQp+5w7D6jm2Dm0CfdwB0ngxYzS3r\n/ru+\r\n=P0Oz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.2", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7569a7c9d49f5577e9431e96df40666c4e8b2dbd", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-tmWZYb/21xwDJOufELWH0cSqlZWrHIOGcjt7bJmcXWuhBg7JW0OZO4akUrq4yQlZl2esqmTSr3Uf5F+jhJ6GvQ==", "signatures": [{"sig": "MEUCIQC6L6TMqG2L9429cqxWE1uk1fAgyooRLCTVU1cNyoaC/wIgBj3n2iqniAUqwPvuH9Dr6YU3pQQUiLvVmttrnPefXz4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BEdCRA9TVsSAnZWagAAGxAQAJXIlqdAXjzlowxIQJZK\np+lywNT/VkcdUrTpOrzMZx/Bf9pcIqEhlj8tXuc+g4zVPsJQCPDIu+k0AQaF\n7GXDkUH3IFshoNwy/hn76CtrrqUgPOAzL46ho5YfTZ44S4Awue1JYhKC7+VE\noGpkmJo6494Wj9GSgRmsnsRXdiizDPXNvIg7zhArX+vWUx1l6RiNI3La3Rp5\n3dDAiOeFu75EHFk9SYI2+ZW4sAs0iSQwzO/jIrgDBTUIRYDZdwu9jMNgNyZ3\n6W8eRMy10hTMDSG2P8QKkOdRRG4eInS05xEFrqLLdNBAXsb7vhF/cKijFj/U\ngbhe9cFTUoAr6XygWwvDNBaK8SYWHOUUUqsS9KITQ+TMTEvKpmFRV7fcr1EU\naC1444y0liuWdFnRM9HePNUPEB1lUFtRizfqOOnOJkAqwLrlDKm73HJMooE9\nGZShHqgmfTI0U/m7StApSQ7tu/SoR7tQflByDuljccSKpi3aAzoR9KxOJSrg\nvHefprR4TA7nM3Fj8nCzbBEVTsst4g9qRCOxgjZBqURyilVkLL9meSngOk3T\ntaEb1Bdp6BW0sIN9x/Nfi1E0OqTGgOfvyzLwyn6ZEJb0tLNNHC66V+ufcpqH\nnFdQ6Hlk2vYPuqLFw5HROAW3ZwEtKpJpEA8xU6vhaYrRoS+gJagFQKABWBR8\nWwrV\r\n=iU+G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.3", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "72bd368f3f991fa28608bcd7112f0f2c5c566a56", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-UI2LPlJotiGwxd/mQZFj3lbaqw6/N+TVa9SI4PRZFO5M09NOUyzkBZQWYs80RQTjcL0w7Zht2XGuRg7RGVolfQ==", "signatures": [{"sig": "MEQCIGW+ZQtaDrnE0PCOAxYDofUdF7mzK2QCrenI57HoF5cKAiB9ftWUiPK5CnDi+t2lXKrCAlZgMYf+MfrwdT/Nv6yGqw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CnECRA9TVsSAnZWagAAmm8QAJ8bQ9tc0J/xhqb7TxKE\nzNJcg3sqzyN/pqfkuH2XoegIzJpKuEsgSQDnTD4S3liZWKgbV+ak601b+hVo\n3q1ilj3vhn1VuHZvIYdWRYrbkA4HImDdi13aEY0oZjgy990i6/XEEyUv5jKH\nNWzBFO0ALQHDL43+TL1ag1qCzm8EILP8Ce1qZAxJ7iwvVHErmwu+jx8ZO9qs\nIxG++GXMySspLwR7R3vkfUuXQqrc+ETdeblR7z2chlzqfcSGcIO6A2PaEgIN\nr7z7LChan/EGgPQxbH6rkmXKNQjDVMEA2JdCiOC7rEeJZ2fSKIVMlpfuFsMQ\nXXaTvsD1NJo8Il4MENe3aobRS/Z16nrI/bKvxD/hMdVS1nOtYTDh89pnnNH4\nbGAaybVfi5BS93K+BoS9HfOIGvudtHSo/vq7ZpxORpvBbSdfyPiMghWpJLmW\nd/nf4K+SfRlSXlH2qhwqJk7eOPx1aQV4fPAMTqcRGxlaOxHMOZbAZJCgHJgq\ngE9mK4GP0kJ/n+qFRxV46iLHs8vhwKf0/FyGGRfJE9Rl4gzKkbyeVrJsQY43\nL6BFCAx00Sx7julzmU4O7ZZ2HVSatRR+CR/WncUXMJ9LniV/gHeQgoCJIVWp\nnlppQRJp/uwi0aJiPufJQFLqp1x4R98t6L8UshXP9mGfdLOJHBy7SrixT83I\nmuQy\r\n=7A4/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.4", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a3ce9a100c11734b327521d628e62b821d4a7772", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-FVuJ3PxGQin4GCdRus088RphEYwkvMRVsc3jDuprDrezBLc1ICLUjhRKOQuh93u51S+3iNboqDZPvbzPPiJKuQ==", "signatures": [{"sig": "MEYCIQDUFjJK4AUWfq4VdeBPC3VT99ycy1CxI3Dg4Adh/qDnfwIhAOB+LYbKgf459j6wuuMySq7XX4+OPGun9kRflDLJhPWl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GrDCRA9TVsSAnZWagAACaIP/3Etp0VcOlrJ/WysF0BH\nOJGb+tQJErK7P1GRUyc/LbOkBG9PmB+/WGBg11Uadgh/KRzow44azcn55kt9\neGxnhK+eH5CWz8WyZwtvMMvykIYjY2rzarwssq+gfiB7o25zqA7y/ymfb54I\nHasFr7Tpa1v8Si6h9P4vGBsJMLd+6175d3LvyloocPWKxIZoq6O0qsy8rxoB\nUYQ2VEFP9Tl9aXqKwVSDDfgmfdBu2Uoa4R8/IG2MNN/mwOcaYDOhUojldIWu\nWLfjnqwdWk+YI7srAQGON+ysMHaSlmnQdGNWm6eN9k95RGJCmdR2hn9EzvPe\nWt2PibPVy7CxiROO87zIT6nCofodVEzJUArSXNmToaw7N0/wvYOh+/Axc1hz\nGNV/eDkXAgcUGCk/ZKEzdVlOAPTJmCTV+hz4toWzqxEN6MR9nuSeujJakQKO\nsoz2DiVBYxncAA1s3Pw4t+KttOmnSik+rVi3DvD8KJ/neEtWzi3SXjrmv9wc\nOk5F1PhbPlJkjflt8HsyWxq8zToDM62kw+t/qMONNb0y1tC3J1Ju0MXCE4RS\nXbDMqzfXyUuZwpgiS5RZo3TIMf1NySGTlxWKE+U6VZsJw1FgJ5vNXJFMEJHg\no2zbRmohHKCsWY8pZbiEWzZ2WGSVRWXFgxN3S6+Du7IggDwDFK6rLKROvSXC\ny1mO\r\n=NL4x\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dd8b289f0a9a711173396983240aec28ca12e28d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-XRs6UHFRBPcNa+FLS/jer3fa/Nti+Lu4hDvXIJCrF2tGzB5cLp9WDD90VYEqiRmetgYPcRUi2LNpfbQ/V6zKDg==", "signatures": [{"sig": "MEUCIQCZx2cRxym66rIudjpCi19ky/TQgM6c8GZPQOqZKA90qgIgIIQogwXIe2Hvt28XkxOQvuAo4xheClaeO16IFuNeSlY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5Zc3CRA9TVsSAnZWagAAwNkQAJHOGTn3iMNsBHQ+5SIn\nGIV9BARP9qFMJgnLHnZPPcIdGGkH7aF/CnR6rfIhwXwnKx6kiTsJ2+81EAuk\ng/q8GrgubM/N3cSNgdigu0A+99Fe/fS0cRjWKsne7ko/a4lKi9t0Wmh+X2eL\nJXVCFZ2nTkBfRPRXrqpT2Gi4Q8Oec15IsvPzuRy7WzfMZ223aAckCq5FREtK\nUKVZL0dC/LzcnAuBdsQ45HbuN0pkNY2nG6khaWAFQpp2t9nDScxm1W3JtGJk\nMz3ZQB46w7usV4S3EDLAwvInWHT7b+4OhOLHue6CmXGlzseZzBoYe3EPXlZX\nQWi53ukQ68fLEKoN6IESSt0LDw81fj3BL9pbpeIi82mGUL+t/uMIqKjYhuWI\nAcY/waIU3Jq91Ryc20yBngcOYVD2WCC+l9G0dgo73FsANxYkaviwsRJ1KhlF\nHuPr2kQ3FTUxHQ6Epw4HN4ZalhZfheaR+QZZwnegZ4BRLUG2ySZOMKeY5thk\nznMqtiUKGjY5g5Wv5S3IkV6zE+4uZfRmpM01QkGEegINmC4QUQZTltsi4ENu\n2ho4r0xvJogZCT+oi/oRRkWyREu1ee3ukHMkf+bJivqj9jQ6EqKEDdxV9m8g\nUQMkIfES1OxPg9nCv2F2lqoBZ4pfewg8BbOQXXtjmaJUDA3vISnqf4fTeguV\nTNhv\r\n=b6Ri\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.6", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a1d06ec56735750c143a92da101ed97ed698fa49", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Okp7wkUlrzGEpYdGg5Wyk8rCpdDkR1LVkv8Rj5yrvvhJvhbj2PeziQXPi85CHzKEK3zeTdU+1LQK6cOQ/y1NSw==", "signatures": [{"sig": "MEQCIEHJNF0GUiyQ9JyL92QjLh6s2yj7cPgkyTC8JA0mymoSAiBduPIblRqYxDNCitEeJGi/uWkqf0hLsWuZiOCMfg2cVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YtXCRA9TVsSAnZWagAAbvMP/1CoT2i6yIu7h3/uF3MV\nUbH4pmIu5dGTCSi/eDjxB1WbyRVQeSAkMcGOXHdajKLVUWGbK9Wi2PFsjWqF\n2w3zag4dHH4EHFLPOT6z2eZ498HHG/eG4d01Ek5G/GOfhd4c26OJDe4WwMxQ\nuhP5KhFRhJGBXTGcvyEk9lQ8ngApgI1wn2fl8gtSKI0EHWNRyVDiiJKbPNJl\n1u3JXi9ZLTpDBeirRdFTE1xUtl8uePoy0i0JabXSo7cO06LWSNkh4oGv+w8U\nmkecd99Vy/cDOAnZUOTW7LencX+m8JEj8pxYLcpgGwj400y9d4suHg3MbA1+\nMaODISGkEIGFwBVLuTCViHNjVs5tHqdW0cBTjX3S45IUSMYYcdnCqMcCqole\nvr/JzI9GyXedH+id2NXaxsbw0lD2ugpnHmmnNCWl+68KLRBmCQF5Z0J/i5RX\nuP/lUlOqPxXSFhzRBd0Gswi1aTDOG1nqODaJFtNc4GQA+72VdgzF1UwIlyYj\n1EH130IDMsWPmEGkRvdZ1fov+UUzpTOwN9iWz53jGpRzYDCzENu87as+YWLA\nLPbVK8P15jysbcCTa0QWyoT+Yqt1t0JpxrnXGkoW+IRe2ZBZzmc9nVklQgac\nt1f3mI1RdrYfkeEKwZFvhX1uI7bmiELjk1BczsNLZMNTXTUWMrtkeTfsJ2c7\nIF7K\r\n=iwnM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.7", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "000e46c6a434c73a11ba9d5972c1ee3380b4594e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-NAp5n47IoMuu/sQTYrZ0Uvz+bKu0Ds5gz0WpDY//fgrDdPFrE+Gk9mQ3+Pe4VhPmvg2QHvh3Iz1e0S9jNT/6kg==", "signatures": [{"sig": "MEUCICv6Bs1QL+XNDn68FhF0BXAvj4Nd5f+uHNgN5tBnxrBKAiEAl/K8qXHyqsXE8VT4Fg3s/p33OjR67RGkmiKo9pD12BU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sdwCRA9TVsSAnZWagAABnEP/3DB4jBDtU/noOAY8MnI\nuimYca1lWPSpYy523aeiLi5MTt4npjmzkb8l9MPcU3gX8u4xqieQBO/D7o4/\n78emYlShcjwr592guw1KeJWK4E2eLrRMXxYeO9t3Ey9RW8zMvwUXEVu05j33\nP8zOAS4p4kMk1fBpcM56a4H8COUXet5IsjykNbPl9S1KYCe7PdaQKsO72rSO\nZTnCK5F0H9lcUw0rV9xTrQKLS0PiXXqduxUE+z2yY71k/vk5cvCnkeXMgrGw\noZmvyA5EiPAjEZrFg4dWil341YKUnQyarYU2GsyehuB/1CeUFyzBtMCer8nW\npEpdxzs4uwrqZj18yrbLMoSTgIQ/TDq00ePaSKhCBlA22gXa0m0tPzhePD+3\ngSqEqq4oOC8ToyC3895Z1MNn5jzy9kIbg5auL/orrFvNLb1d6XM4IZJZKIzk\nZpGpr0o36JkXc7nEGGjP4Zw0gGp2AkCFvkhyufwsQIurV9+j+7g1llosLJYi\nGtWm2Ovuq5m5rfiroda//Z42dBzbzASn2RNv7lNQtxqAf+8tTvc0x8GumDgy\nDWyPvEGyehIFoyHl1Y+lqgvU8jmiTqKZUkamnq/KuGFF86WPerihCKt6hMC/\noCOZFzoDd9BwpxjpjG30M70dvaBJM87uavd6fWnJE9aep+xdBdzBg7m9BhS9\nSnne\r\n=RgYl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.8", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.0", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "36fe686a16b0bad4e7c3dd4ef5e5dd95810ec51c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-JFWkYLo+dx2H/oi0LkclCrGEZCKWSb0KJspEawA/g8Hl+QKBJiMEIkQHfFfBl5lK+Re//AhhUpU2+AjZPFGFgg==", "signatures": [{"sig": "MEUCIQDqw8VdLrFUncApwDqBmKVthCV9pRRptJ3nR9lvb0N6tgIgKwC+MdcTfGx+H50whTMNuhDjvj2eV6hVLqC7xEiK7XA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30438, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xDuCRA9TVsSAnZWagAAXd8P/1/K1thtVbAFlEBqlu3s\nhpp4JYBPEgmzoL7es/l4yQ0n905Y5OkYzIszVYasT0wqUj+g4dyrcXcfSNmL\nYx90/6QZgZntJ+kNhqdW/vu04q95vQ3Y7QUwVdQMG+Ge53uTjOEPZ6wkIeVj\nPeejA/de5IPfqravomwB/S8JjmVaCy14NXT2NvEjxn+KZTxLhx5Z4/54QDjg\nUYhDph1cYeVfhzHdQvNnws/i26n5Pwy/fimlUy60Z40tsFQUjB9AuPSWM+/E\nabOuA8+jHkKB5EV7us1/S1A8uEqOIzv//gJ87ssVGG9F1Jj7vaY7thAVH0gG\nsLynkoNvoaqbz7HBOPAx0SHHmwRDQNznrdMGhgtKSgDmopY+TEc0Wngr5tk7\nCi4BPIBl/kKSzwElpGQ7kOZDriXVGmegK2hX7XVTv4yq6PUecurNfysBVP9v\nGApUoQXnTwkfyrdVEZ5dAoMCotJhQ0gd9seZuWzawdXT6jtHT5x9IYEi6L1i\ndAlIDjRTm2NcRTfTin79LMIFYiS/nAGtd+HGKmVhlpS2FiyW5Qan82hTbDUH\n7a1w8sDxDe9H84VX1boXlDK4nksQBta0o/6SBlqmy+N5dzLaUA0aWlJfK7ya\nqEjrZBel6F/8WBep320TGNKWVnfpKQX7Nnhh6dZ03jxUo44gjPUUy/RY+X2T\nbBzZ\r\n=8d2N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.9", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a211a8a89b21d7fc9c4aa821fd694833fd6ddf42", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-U3lXLqW9HsxKLojMlGX64lDsj/cGhhMAZeKtxiecxLLQLWb6Y+jG7hkHbx4GhTBZH/cXS/ZA9qERqmy9TE8Tkg==", "signatures": [{"sig": "MEUCIQCFGO8A8+gQ54YrLB/56HQ8TS7FuFwEbfXj/Ee5i5qe3gIgS0sAGsopX/mD0amxuh0RnE57GCN49c6XuWjMwES8deE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30443, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLwCRA9TVsSAnZWagAAaWQP/RGqrnF3PCHXlYjL+OeE\ngyMH6+VbTDImXtuDO/FOpGCc2O28c9s9j+x5n/UGIur5KHz2mbwaupC9VTmP\nRevzNHBc8uFuEFujaqEP0hY1YKQTOf4EQVhpSbc9uOeWG1EXYvJCkhGdVxmQ\nQVUqNQH8i7ceDE9q4R0DFH5bpe4kWeqd8iftCWtqj1rBnAe0/pTQUAotRk4h\n6wdU+zMfd1tPor6ALmSkThwEuWoQ+zphbDbfsPU6WpzslUkjaZ3ErtSkKu9o\n97ZVkQhJuycO0PyNRa+l57zyzTB3DLpqcijs5m/hIAQMvhuOVIRDYtvYmSH+\ncNMwIBP7AuB+GjvG5RXDoqE0s7ikdSARrJLjE8XTG3vn54mYq+4PhQrysO4q\nVlu+yxXc/bY3beil7+v7Xb2zW5k7Wg2Q2WqELCfarTPMjhThsHaZWyQ3m7ew\nij+joJMrFZm0AqxDqUI5ULnQUMLCGX2akGtAlIpYcl+u2Ys84CACy7VZrVsQ\nVrQ3bG3ESb8XMxHrFJ6n2V626YBp/xyzbPlEmIMai7Edty8mVaw5hy/toTQ5\nolnAPrvDMJ4cuiqtM4VB8bL+IxCxuOH9zhwGXfZOf/jChL38ZeDCrppv4LZe\nX1RILT2iHyfpf/uO/OeAJWXICj0him0v+GYSc+kGb2HbaFiFdYTyvyA7+CpO\nN/pG\r\n=1UtD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.10", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e59a3fb8660f7b543e89b272e3a5fab1ede5b74d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-mZxoIRhvW9LPSkFl3IaBG6xHQayshKsItAXnKM8D92mTGlbFR4OBGYvk8d6Hfyr7x0M320zOpC6/Kj29+VipJg==", "signatures": [{"sig": "MEUCIH2u1nVCYm4YkJuHgvtHuHACof0qKFnNrxyMBLmCIa84AiEAo6W+jGZnzoMzshilwHTT+X1AwIiIeOUKPWZfFHHFHQY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D0yCRA9TVsSAnZWagAAfbwP/jp2vUSLd3vtRVQssufB\nZAhdcoiV8QV36LlXJMKC8Byh4BG7I41tSkJ19nhUKDssSe6dnZbXb7m+0SB0\nznXaEW26TCuUnM1u3k1ZQwileeMx3fBoroA9aDj0k/XrSvidxrSC39IM3+Bs\nLKZAxi7z5NouF8K26vuLBGoxht7W2yzTBWhQyOoLG87THKCTBdwo8WVYndZx\niNRxyhSji7T8dAR+wN6nY3KAjxUp1p9BKVZR1ddPu6BhGxW7hBgqvdqVdm57\n67uPCsWv1O5oZ3iK/qkf6cp01t0KjmgQrGlqI5fTygy/yJjP50whI0+GcPQN\njuVvURs4T0pzTCgDUs3hn3rZetMMfo+G7ijWPRo+tzBK3NfsUEkhLwuqaYXK\nRldRnL54i11L6kADxmRyfzzTkG6nOACnSCf1nowATojvrR1tyv30evwkxh74\n1wpAM8CyaOrd/wlPsUFDhWD86IgmbYPcvk5H5b2KM3cXaQePAFUN/AOLIYoP\nk1w+jCqT2a/ZLVl0Bjbj8Sqq3fdKmVLA6h9F1MAit7YOiO9LCFuAO+Ax8QME\nxrh9qVXT3pChFSnsjUp6KWmlBkDM7lFU/ZmdDeu1KZ1PrTCxYRjtPJD/9s1/\nMp3dC6qAZW81r+qdHMR8eC00R4GmDjukGGYrGIyHHHYFQtUXPa8oLWTBxTU8\nl8eK\r\n=SDCu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.11", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "688fd1e7a51c7d4ee5858384ef769257ab0dc4fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-lyBqh2GoxJSmw6vxNO/7gPufB1cYF7MF6Syvj7WlDPjlh5+sWMzsnWnOQ1e1rLdW/tBlGsCPgZvKHOX+fFMM9w==", "signatures": [{"sig": "MEQCICMnZKbYKTN1c3C9ZjYelO89208NAb97qzdpYaxyqNJzAiA/6/tjJS+gvfh6Yln8uXGEy14DFeEq5wGE0sua/cR55Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8STcCRA9TVsSAnZWagAAcGYP/0AoarTsbkVccetZMV3a\nVJLMLfdMXM3VPserGPH3290IVTXLCu8f71slUenona8OLd8SL6sMWqeJc5/o\nCHvgdM9GkEuZ2tw1G05km/LUhszjVkGPKqPHvdD4/73HAtCIFesOmsuSEAK5\n3R6w4M/2tLPrfDRdBCFXCMBCtO6/p+kFt5nfCM7hWtkUmXiJ1CrHE33P9aDr\n193JbbVUI2u68P3eiEE0OmmhcnYZDTLDxC9TlYDb0gUtwyC4wvwMbSHS3MCF\nnd5xXqgK7jTkPnMuq7IkkPccYT7pwDJ7L1gnnhs1ALc+ONStsVe0v3UOPuws\n9D4LA82kLGr619ZryjSbdSLJRiY0sF+vTLdKdVt/vWs4SZ9v3gsW4UyTWMnZ\neaZHFh9hUr8kvD6V6PHLeXGNj/9A7DVjd1vzmRA4yIHgIs481yGr8yBVsrOr\n7oSESks/uHh5/cxpIa0uhON9+mPgMi+fLl/gsXmtURqrm1IQ2yNWmxMlKkUj\nn1DwxcWktJAqF0RV3AOenUh0b3/f9yEdM13rgrHR1lb6fdDallikIt7Pb+Gh\nN6D1WbMEhma9jYHAKUO6nBrC23yWJPbjOGmf6qfyCQFEs3wm5k26VhzYRpSH\nS0Xn1R7rQ8p9HcKcmtkpa2sgfqge+RSU51NSL3OO+bWXt6SpujvHsLtS9IBY\nRUn0\r\n=i+SM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.12", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f10f8ef4dcd1f198e67148b5b4341657199b8e48", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-VNNfVnCEup0Y86z3F/NjH6MIXqRo6U5f31UB624tcz1msMkgLcdS/CRdSYC6+mpzYbWMliKHazTqhIj37J9jOw==", "signatures": [{"sig": "MEUCIGBYzhNP5t9phUqvHzVNFHGK16vXkMTFGriskLNI5+rIAiEA5cUELj/ELgNg9L3cupsk72rJg3naddC3alqNkCc+M6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DaWCRA9TVsSAnZWagAAbb4QAIr29+y68zZ0/rysMMTe\n8aNvePAOIHVffyFh7KF+cAPYBecGIh7XtJ6iadJdDWETGWqHvfugR1JqS8/I\nx81XF1XhAEDHPBfKWIX1PVCv1RquV0BxBqr4eDWAk9E6NJsm2NvriKBKX4xC\n9OQDBaGy0/SFXI28DDy2v+JmtgUyhqXuvS8/zI60as71wbIOBjEN2p20w4jh\nmRX6Pk0Aips+JJY2J3D+R9djHnuLQcgUXrgO/QQZEvrLdrJ6BB7pQvIxf+4V\nRGXT/P+63icKAjT0p3Y7kCwK+d7WvhwBNMBdfAdA69PiGFkqpf0ww0hQyAE7\nq1Hvkgiy5fd5SZFKkm3gm88637CGt3kODvmQbiKEA4/195Gr3avdi1FGDCg4\njnydGq7JHw3GXP33YjhMZ2R9UiacCMVPYmFfHM9yD9+RsN9lJQONkxUKpkIB\nq2UmZgm3gJXJrxsaFoWi+k8IFwl+JRH4+iyRqYF0tslkSLOBNRYbF9pLIdjX\noiYFrQDmtgUZgkLAgm/HfxBfIVftIemp2xr5OQVJAY/uXghEXLTIitLcQBKF\nAV0QH/XPsBEiWOfN/DekEBVdCdGAEbm3038WVuX4auP9Q21M3l4W78Phwd3N\nZ72OqV9qk4xlvWBuPRrbBqvhu8f1C1wq0Kg4riECGgF3DZtI6d+4btwT8R6A\nv9K4\r\n=gPAt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.13", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4cd04366c7c52f6e3cbc40b81214271987dc0ec5", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-TS6XTElWPzXy6Ok4zRp8QkID8KB2EqUhXmB1uE3mMaI5KmKILo9L5Up6qllDOF3U7ISPFn5mdPtQsT/43u6g4Q==", "signatures": [{"sig": "MEQCIEHgFrpOmWAjYi4Y1ROaFTPGlr5BAc8030xySm68oSQ1AiAkzv7YSTsZ02mO14aytTvxdbwTOjOuJuaS4xqSeebodQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WojCRA9TVsSAnZWagAA80oP/R6RZo66/ppVb9BOpkBp\nXCA+zj5qQD8vL6CNdVjYdc7/I5dEEzxzaUsViQK08/djIRKJLbuFJ2NJBAKB\n6NirgUDz4N8UhkH/Qcg0Y7sNsqCEFiJkBdPxsWQRrR65VKOCN4EkbnxMd7px\nAi3w1kKKGqFSqfwByCBFPFTf3OW8cfHl4MXS8LQJppTLotqdqszWxocdMnnU\nUtpob37aB1skx4lEnb2VzJJNM3UIxmDPjmfpIleY7ANsiNXdnT2/H7CsDSrj\nCmH4kNX29BXv5pR9qMyl2OkWHx4G4Ey7mUaaS3agakJVwqphyo3aUxO60afb\nv8BgRtvhdX2Gqm67Q7N1Eohlh9Wd1H9MpbYw8cZtflrqWDx0T1ilQk9oFxLD\n76l5ZcpxOdiV8Da2ClLbJ6c8+bImRlabULlpgepowkvAcVd11zCdNecO0213\ntJk0vkHVAV3udeHIadUpUTXY4amSuICYiFVHLACoj9ntL+mVolmZGwr1/01r\nYoDCOuRmbJArosYbNPkSgkSUQsKuVuLuTwTrU3vT5hmZcm6U+FvJsDy++Zfw\n6HzR4oJwCmzDJA0TejiuEP+psPqP4fHitDD8BT046eQxFb+ey4lOTq/wuK4O\nqRsFJH2GUP347N+75BXBls3BciibciFzi/A7I1pxDJoAef3yJdL87RiXwBAO\nOa+t\r\n=dwoe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.14", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8c6f2d608d5656ea83a6ce2e981be46a10fc66b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-AaSc4sZlb5WA0ZJXvf7lRZwIDsKonXMqLVpesRokyKtL5H+QCknDEkpEL4xB+pQe0UeAHKCTHDjTJNq126n+7g==", "signatures": [{"sig": "MEYCIQD/YfJzrVDsyeTxknidZJ+Lb6ChwX4eWYdlzYcDX+R6jAIhAKr2m2ro20nLVAPT70Xh5TQRrMaZQ5OOKqLeULxUpake", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rU8CRA9TVsSAnZWagAACAoP/iAJHF7+0vYPfskTW5vA\ngjPenhfPlCNY9KZgTRtqQc/iNDmW1XvFcx1mNhNFi5pKtYTvTjH7NPuXzgh6\n295fbBRgZ6M5itRR50OfV8UYeyjtvGNB63tozvPgcfgatIGwGIYsiqb+oipv\nB77T9KBTLQz+8qir7vhxPH7oEYluB51kc2VyNwhe/AMnBQAFDGWOWkNvsHDz\nxHpGoG4UqsqWq+80HqSTYf10hcps0BGCm2LpbK9kNsUT9Si6CPN/ig6qnDrq\nrpXPOVwxrWDRGBtR1FUN7hcVdgqve9aEGVwsRVfRTwurwQo/7Y1vNQlsPYA9\neeKLQesi0Lgv3BX6x+Yh0x7dfaEH9aQ9yC2/ZWCQyZSb9BnhaVBaM2SuxejU\nhIkw8Do9e3IcgH+dilhlARBDC1+UyFZItvXGyeYlX7l1ylXggF0rPvMT7D1h\n6YcOQQ8BkwZkkrkdbEv6t88hdr9Ag6vkiDnsXkQvWCJOEL87VdS6tdUni8X5\nrWJgjejBnQZ0YbawqIBi7MLLijDJgqJVIS/kCaGRx26Vo/KzjMkmIjmHYCgs\nbLegGVcGPENDZQsHLKFwE9p1lAaHhO1PtUpF2XSGsUuZBhkO/Xi7vfBvOxCy\nmtnvUj8pjkkr3ZvgLmT4lvULlWSO2XDOc3eg5wsEdWuabN/orgvDC06M4Jd5\nLPlt\r\n=MRlR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.15", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "819bb0bc7cab64a466e857eb0957133a4d71e7a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-G31ppzgVpzXqgx0v0S8gk+Z4RdE+UadDikBk0nZWWTpgxBUyL970CEbQlnlAVNTK0rZ1Tlfx1siCnxqWPtvDAw==", "signatures": [{"sig": "MEUCIBGp6zKmeXqvr2wXv6/TmDzxPsqck0cHdjvwUdOUEU2MAiEAoBSERwpxTOTWSJsIeoQN5MxrGrWGlDnSRslIOw8KMCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/oBCRA9TVsSAnZWagAA9zcP/RF9DKGDmp12J2tAlXXV\nRCnKjV+pyMGNvCvpTQ6H4Lu9Efm2GsfFgjvZ3/LHWhk+EMlWlpxCDbzEs3oy\n9WCEeONF24Znibv2zAAGdI1WosXLkZwikbqslZScBrwVpxVKmtQLAra77Vrt\nxeBiu+aneoMPDBaE/2U43Zacx3ZBu7uNjz1Wojm4gX/6tOtEblrdlLw2o7De\nI9DOObXfvVZkvtGeJbb8LP21yOuDIEOEvoytjMfhtSHs4/sjGLOGJzy3r1qh\nr0swzKDfAwskmi50mgKLmVZOGUr5+CBnQURoSgHvXfEQ9xwcNA5stjPczOw7\nJUFctZIHkAiBj9/4uwJu0vKB0V0nU7XkUuylAQwJp1+AhdLq4Dr96xTHN98e\n14ZoLv7B2SYk8TDOTiDg4+QjqT94XIlQ8jOJkOcVhS5cqwrtXqgIsLKPVeW1\nR5eq23DdqtHvnTUPg9VoB9BilHmP2LOt4eQY9e07znm5ncyXrmLslhY4opN1\nE4GLc96bXJ72bGbMxN8opq54faGq1AwOJJhGSirZmNve9JFm3mTBO81lSmhk\nr0bCX4cRS1G3HiTMnMsRJn0iLaF1tUVzAG99Mtvw03DziPIbt0ktNaiVsSVe\nuI8NQsFvxkNzyGb3BZmkXzyUuGzxzcrd22sBMsoWTKNq572Gwf7KuoIXRPjj\na82o\r\n=1OHL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.16", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.8", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a698f4c949d961ab7198f0b0fe9fedd2d424ea90", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-UIZJw2ZCdasCTbrPcKSixCMRZgcxGNHhDJi1GPOTXVAGHYhYhp8DCis/qfuy4PTx18oyXw41Pq6B899N7Nmyxg==", "signatures": [{"sig": "MEUCIQCdovgnLfY1o9WSPnoLAtCXBS/cl7A3gmsWFB/S4JKzVQIgBHPkvEYqM+tNhGvIOKtIT5+XSTCAYbR23XDKwRMvKXI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBIcCRA9TVsSAnZWagAAWGAQAJ31p0Jdf8lxu/sFSYaZ\nyxh9CvMCWSAHP+yGZGTiMIgCEdQAArBMUqUg4hDVgL1kFs+zCnXR6admDevf\nRa0lZ4EenNUgEPl2YK+/0erNbIA5omn6hfqMf5Qms/9mAJAXzmprxnGmIDki\nu4EcNFxif6AsRCG+pzn/WLOuwDo0M9FGBlprlwr7IgAg7GnB9ZVBUqXTrB6D\nMlVxhitdj3JoUWBEdt6XAoGef/80LKsC+7q+TciSiZ+vkkQ7GHLE7SWi0EXx\nUZ3cvyk848KhM+aFPwYvt31M280AWgPD+Rr6HZDMd2nU4OkDznegeoUOpQ07\nekdnxr38P738I1q3GN/qTWUYXuzLFhmFLSO5P9RudUjrI6POEFNTzP/MEvCa\nMvvwhQehtmoowH7ULA13EoUm/sMp4fjCQM/tjFyXA/2OihOHeLw+VkwERAsg\nuD0vzkfXqLeIkbdYhbVEZZGkxLCUbRZgomBpz4mKeBwRbp8ySS5XZPIY+FKU\n4vqQJ04set7EJXOVLkHIabgr4DO0kyQ4qLsxNqLwkdF5Z4XvywgwPLJdZF5i\n/wxhgaOYn71uI1GsvzPaNiNT8u7BJv0HuCZ2tnP0kr0yF+d9rTaX4rNgvhrT\nZ79CuxZx7ZhRzQkOT+k2S/bYhU1j895r4JNWdjXMgh7fhRORyDI1q7arO3Hw\n+9ND\r\n=E/0u\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.17", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.9", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3bb6c3bcab44d79dab9d7e2a07c645d3aa7cceb9", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-JzNtW/nvrPeQg+n1Om1833MArp1xMUoDNKyIgcYUgq5lNuQV27LDBPemLdySJ25HO+KK8YXRilo7Y3megKsgbQ==", "signatures": [{"sig": "MEUCIA4fQm/QS8DPNlnUCzgvPwBQ33zve0J/aOugfbcmp24GAiEAzOGkCPOVEp5ib0V3ITh9JZVptsgYd8Onpg7LZfMauEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30447, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYqCRA9TVsSAnZWagAAaiAP/jATSUnU8dPtNLQrEYLH\na2AUEadtfujOr9OP4FO2Zxal2xKQt5N7M/IUzFmpkEB6+mNcLADkBSyjcwjD\nYI2oxP8klmaU9T/MIYAwXTOokCU99vVYnllBs46SvvKhkNk//YA7xUhzd9i+\n35fCsb59XXF9gYYjERtIS3tAOLwG79oEVj6fkJGiKvzMD+/HIvziTlL0POHa\nkfC0hxO382lj6vWqGeu+gQoM/LRYmBQAXhV/NYYvWkXQ2yBzrCikRK2i5Q6i\n4q75wS5QNAxIG+eBmuw7jkaBTGD1A+mzG1uJ7x7ggUT1cvoNrI0nUAE5SEIY\n8KmUrTFsxqbLo3xSjXZKTEKvSteB/0l3RobacwBo5pKsrLrntJYsEUTFdnKB\npWhaaPI5JeyQ+sxnJe2CEurRa92Z48eTsFmuhds6R9kL+gUnj8Xq7GQYaQSd\nkg2Q97Pn8YV5d5NnBhb1Y4S6coHrUkmQhWqOW5G5EquxI+J836TSWC/nAWzG\nJgd1c7F/k4xDVhzUT9amEYM7KhUz4VEVHwFiL4V/o6NMmGGOl6Z6NsUzUnfw\n6alntjXDNnJCMzO1z/VscCv6S7lpa3Tsn4B9f88fu8OokkEkvicDRUHTIDnY\nUTS0FqWBrgKU297Satmux9rvsNYboQjVUcz+FXxIbGi6/MOHYhosg5e6Z7MO\nQ3CX\r\n=xcoq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.18", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.10", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "38f3de56839bc559a288071ec55ada15f1d2f524", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-wb5zVorMKqdEXagf2Esd6NcMfV8AdvnOqIpzMj6UCnbvVsRPR7GZN0wQaceT+sHzr6gFsyTQ7eIueSTYU8DMFA==", "signatures": [{"sig": "MEYCIQCiwRGBT33n7es+0BiJaKB+3nrQiGY2DJBYHt08+ww04AIhAMB5FcPAHvftGb+QMunDSfQxwZZ+MqsuoN75hGsGF3zW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvPw//R+XqFMx2CTAxzpaeJWwItu7hJRNahy9QkJCJmIZdvOATcpPs\r\njE8n48uvE0oZZyglO0Lgiee8NLl/C8jB3hq1LgMzH4XXlZHYxz6WJwbp1NfV\r\nlveGABzSYTwWT2OR+zg+NB32MZ3PCDtoHCm+E6FcD/o7LLyiatGFTuXVltS0\r\nu2/p2OsQC+LNojimpHghE8ehxxa5FtTehDAw90iuXfv0ziVxBv3NRYzlr+HQ\r\nDQz6WOkuJvdbmGexp31Hz4/FlbRTATAOJqePkHH0VElLhrP693JMPkhzUZ+8\r\nSRz2dDQbP7ge1sr04yQtFsJoWChwV8NyxPZZ8uN+RRBJ6RP4td9S2pLy8IIo\r\n4A37aYXhIj+j2WQ9xB4n8AK3Cz0bf/B//AGyYO0H26G6fnXUJdeAXOkjcd+t\r\nC7UT/0cCIY37Omq0GMUAPvy16lIDvd1LKAfWRDk3kiNbcK/hPx3xGRwC+aUQ\r\nfmlyTS5Q3HeQJvGjtnGSOsjVt4ZIfMaW0u+GGVRpou+BdLlf6WxVQ9vLdP/M\r\nDWFfSLRaJnXtx7V2+WxsNlTywqppVz13rangbQxfGWR3KpqxPSqX/ML79jD7\r\njHHe0IC5tRO9z01SoFeaIYyD0exQuR020L1cUgARLkqC5G8wf92v/LEEr13s\r\ntdOkfb+SCgCA9XCJxjnodA6EIqLPCfdimxY=\r\n=EPog\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.19", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.11", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6b5033a535b6f65838cac4c173074860f8245b17", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-IY7yMYPeXTxYhFdaOXOahkFzfXzvu3uMC3Y5OIV7ao62b+zFqj6eUTbBXLxVhwiOYMoxoGMc3EWTWB0YwTu4OA==", "signatures": [{"sig": "MEUCIQCIPV/cglOw8v0gqk9PBhCgwq6zuN5Pr7tGg/JldQ03ZwIgPwXqEarPB213ZFmVNTh7Yye7bSjycVBJEUPpbhHEPzI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkVSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4eQ/+OXuEczui8nYXYLujKPoOb7zHj64Q9x0g6944Kfn4l2twZsDr\r\nvJ3TpsaIi0+sN6C6mmYfY1pMOI4KeKMsht228LeGYmNHSn/+BIoloml4Dd81\r\nGS+KgHQDjUys5/XHcW7dASmdvU7VBFH2ESMR6BLjT4/jLoQUsalDNzO/CLB9\r\nU+yK9F3+jPjxKcGJ601E0XDCIUjuboOq54e4hS3m+URFTBYiGfq7xUYmSzC1\r\ns3qzqfFZvCxegbnSxhzHXWGWp3zWFN+dJ+kQTXDVdzdzl7jhBogeMgWREyfK\r\npDWO2PxyP1rfJ4klESy9tBR+FizVOrMmHimxHSWWutaz5Lg/CAUx+RSwarb3\r\n3cA9pPM5V1tb9B1TmT1hDrtBG1gi1fKUcb79IcVLgdwkXCZE5W9W04XJEClc\r\nFMnPcwMcSIwp1gFw/PWuqXRjufDbIWFHJITuw3u8dRY4DLqzq8BRI99V//q0\r\nSCEgfo9vaiXain8f6HYWeVUieQm3tj2P9Y0M6NcHGPFFhYpmmcmivGAfgrsr\r\nr2qsIMyzqatxhzWKXaynvHRR9UNwHBPxMjRcO+u5O7guW25H+Syfp9AWl1f5\r\nbsXJexO/+HKcGiHb9JNYbHCT9EElOTS4TadH5KP6y0a8VOfn1A5PZ9yfsxhX\r\nxEWR5CoSYsKPbQZ/BWo2MwomQR/wRoSX1bU=\r\n=Ts1h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.20", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.12", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.20", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "34c156e0655f86171207f6af8d7c007f53bd1747", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-xqamgUk6HBnXRqFIRsrS42MxeahZqcss8cWKbaTUxgnVXs1CRWSIx9cBghMtPoTErLMm0dH/LpNTtHtr76PMMQ==", "signatures": [{"sig": "MEUCIQDZtM5qlbRfRpn9mcQXuvBH83fBhDRAsgPsc0AfU9eBAgIgakiiZvaru/XOs2bqDqUakQvafPik2XWpbSoV/FmekuA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrY/w/+PH663SFhZ9w7VwfKS7yRhAHVToPgWGtZnKqctmobWelh6bUr\r\noRD8S+YdHpClecO/zzRHE/TINS5ng3dZHilMp8ZPhcw5MjQ/WnXeHchCYnx9\r\nALOcaRZPBOkHpOm1yFeaiYATt4qR9/atU0/4Yp1dgROVUx4BGOe6XNO4VTO2\r\n0Gs3x/kHmnHLAszDltFPximMVoRdiMKwwj5hQA3zV1g38GXXwu0/d9rMoLTo\r\n41QQ1G97IOAjHs1nTZAYJoZWLEQA9Mb9tMMYAU2dsMCWromxeHVASDA93VGm\r\nZN3UcQVc3Tpqk3FeUBabJPXgKNJmSKdT+rCBlvppVsSjFCmCzfKeVJBbN9To\r\n6iCQ28tOn5waROPVTRKfqiXJwt4gAGpHbEuEFbarMfMylumxzKvxJnMphsN2\r\nZtg5WOe+XnOnxcr8GYCs2zKj+k9/PxAtBWmxa8rE26oIQS66FCf7amc7qikv\r\n1wvB+CgoS1iAZWZsXGb2xuGweoleUUQXkHj2aqxug0JWAiZ2mQ4jOL4QytZz\r\nquhm7vbOwyH3x4scCfettYGtqUQg9d/wJYvE96HO3ZNj14KVF3fsxtuCUKK4\r\n2Rk+fFo+jyOu1uI7hxI/tTS2vHlzlHxjqY3vrN6r9QuiV0y6Xhx2rNuUUYG2\r\ni6KvzLSdh9KPukMNfprNvgjst3LkW8lvF0s=\r\n=2xnE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.21", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.13", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.21", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "90b160b4e73bca71ac58c424ca630ace7aee661f", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-VMLbzma3toh/4bGIXruUFIfM+yA8Q4wk/0DjGUJ0LONQu2lxBn8IqgPsrWb6AGFJ1J6q3IbEoyZpnnxv7BRPVg==", "signatures": [{"sig": "MEYCIQDB11aj6G1C67Fp6SqVKPqjR43tSCGsR4UHsJZVAMKgVwIhAJosVRsGN8CdV2cHtDhoay0/2TO40DC/e0AxIqonhZKy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFky3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7sw/+Pa71NtrcWS5G8jzbJXoz9Ac5AwqMKZ3cX4cyMCGsXPpgN0lh\r\nkPPIV9f4P/azL6F/ORiJB+HiZgSKTK/u7cBq6hUjcOtgKIkuH37LxrMrR/KD\r\nhXcKJwMreVm3Sp8ufjqxOp/A/2cJ5FOXuoA1Dm3PXVGI/eJAi2uomEeWvqCB\r\n9mDOU+L+pJ+N6IQ5ijZIg7HErZA+Mh5L3ZFnoNo53FecndW3QMH2sS1sG2J2\r\nDldSTZKWrn2GvV3M3Z5WYeOz15PafaslzkH8kdS/dVk9PgNUVv8BzkmNzr2J\r\n2dSh1eoD36KnrRNZg76usCIEWhJv49jnYWGhUcCsLcL6MNVKD6X0TLR7wRfS\r\nY7J6UFa6N2Hkwdq6qFVvDRQSAeStjflsgTP89iTkqyTEJpHJg8n3iq6q7yyY\r\nJjHdU8ml4r+zOgrGHxiTWUmVRrbJD5jYDPKYCyI4QACvRv5R1oqvN63tBb9/\r\n+q+l0wIsbvv0z9jvf/rlVPJPqVuMvmt5L2EhGaHr8QR2aHD0TbDhi8+T24kp\r\nZEEZsWrrnDMg6DBdnqI2RgE2vS5qrbRW2DE5E4mKKS2svhD1FOas44Q3Wglp\r\nB6942KNSan9x4KMAZrNUQwFtiIz6GjbCwJWnMjFQuuOijr5lKtQwHml/uRfP\r\n2OkQ44c+2k3RkRRA47ByvLyIVnV81t6MkCE=\r\n=AyWU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.22", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.14", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.22", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d75a1d4b85e48efd3507bfee53ae564898fe086c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-EbLEX55QLrqf8TBxKPJDglj98kfl5QXUvs/dLYgBlQxt1+raYTRFonxkrtphnqc7ZMnoV+m7fd541jzTrnHH9A==", "signatures": [{"sig": "MEQCIFKxK2hxeXVGg9LtprUcnkV+oyDsfS+oCAVFzbe/nLgMAiAtD+OeqJnh42n/ZxIxF5s/UxdmZ50lvwoz+E6lxsRDKg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlOPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo2SRAAnXJ9eSZ4gdaIIDPmOWynhZAV+fEZoLC8zzEfIV/NRbxE+HAX\r\nUM5tUGgr2EPBWENZ/dmZ7mfTSVTOcgupg7HB3OQjFkdCOhETgnyk7i5QUbw8\r\n7YZspLnGjZSsj58QIFytIhxpf6KYV6/HCjKq2KKOSk7VF2JeOZiBsjgGG5l0\r\n37cuTKqud8yWAV851r4dglTvry8HT0XakXf4uUl1kF7fzDQkxSyNPhQ6kuwJ\r\nVfdhGOorhql5dCXsOtXXlQ2av537LlEtqAsy6vMY1BbEF3uoL+zuDC7laI2P\r\nuOixYhShUChCXR9rC1P2ywvV+uixZiGYXEzrRsymPd1aSP7/LN5oE3XAJu/5\r\npuakLnwgmw7Td1cnf0mFBGJqk6hCU25KwcLY/qoh3j6fKDQRyhLH3ScPY2VI\r\ndNEaNgc0SDOAv9c89hhnL3vNBy4P5mKhZ++Ysjg1AGVihaiLaEAnV5lUn1ph\r\n0CecDIJ/2lTcOR/xyWBXNFaCmCd8/wclBnvjez5Tj9mhS8hbox7qNY1MZz1v\r\nX0KqDjCQlA0ONWfv9otlnXpGMzKjj3ff8FY1Sw/Nx31/+KL6rrOuJVBFwbu7\r\nYnhZTqwdQqHx1atdzVWdCE4IRfLMHvlyEz/HiZ4bvT4HD/AB1OD1JRkx7L+g\r\nlmhOVHwytSuC9a1XvgH9mL9pWzlwe6tfFgA=\r\n=htwy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.23", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.15", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.23", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e0e1d7fb3c9522dccd497eba9852e6149359ebcc", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-clan5HyzjpJ5CpGg1hny6xs1picV+mDu1oWQD0CdcP2D70IX+6UPKDBSKmgA8FGPfpWgshvgbnlHaR45koId7w==", "signatures": [{"sig": "MEUCICsd/y+Abl5aBdbx7OZcx1XMacQoH7+seJKlAcHqlksqAiEA42JZZrg5h148zczirr6BkQZmDd+OA6V4vPGC0VMvMiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpEEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4RA//cfp/xWlcKTerkzSWSk6eEhsZV9ZD9BVDE4/cuxHlwiAvRG46\r\nFzF3SeoXSmEHVT+thulSN8yfncxd6Lrqqey2RlEtCYTPtcB38VzTpAUNgCV9\r\n+FSuTQEJ5zVXL+nTB0FV5g+4qTpcjK8mx+A8st1ydHs4Fn8rYLKQo/fPTTb5\r\nKq+LnsIFrc8CLAKkbkNJFTmadB/831jlVRjurW8Dlt4cqE/z75zg8xjgkVx8\r\nWuheyum05nI0fUw1Z59i31hIQAlxDMJcBuEePnLihOV1l5XHbJ7fvziLBk5j\r\n7md/ktSwKOCHT0yS6uwGV9vIt4tvD6wsRYznYpwWtWSvVEf0n2GE0imyHxVW\r\n0m7Lm1BJWpiFd1CTrvKDekW2MJ9sTrpBC+aueDlCUOlmn0uA/TxYejJ2mS2l\r\nEZAJRmX0KSr6PqobWoE28pT/TgB31uBiyId0uFNUSPLZ9eQ2/TSXH7fFPWY0\r\n9vulMXg4ZkWjeKEIW0yka0or9awjMhRuuhbZOR2IpFt/yz3Igq32hcmh7U4B\r\nEafJS1mkJj6AI8wWBNrj6H89unpdh9YONE3wVKddnflvAwhieVbo6VDPIxdi\r\nYUOfjU2K1LZw3uENml2u3IfKzrbGm6IZ5pia9tBXN6MrrAx+r/d3pjyNhCvK\r\n6S3YCMqMv3oqR+6iPsarqjR7J8sf2IBEnDs=\r\n=w2F1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.24", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.16", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.24", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7460e4ce4a6847d8051ddbea21dfda4c2d026b69", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-fwSbRdOZ3bjyuEmQXcSLAyTJ+QedlMDi8fZD3+KJ/aCuYC3dK1ZcJolTwRkCpajoDSzJOlhRbSkwGd3FG/zxvg==", "signatures": [{"sig": "MEUCIQCiqgNTgGFS0Gai2D3dY4wHZl9RJZs0pPiasgCQy0wNYgIgaKxH6yYpoKkXPOgbJ+5wKGMCgT6dJb5bRpog0+BvIkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr8BA//Y1oaUdfIZS7jieMj3PSI2pSKfkBqkpPzt5y6xJw2gZk7BoA+\r\nkZUYObkBbYzciDmYvQS7PdA5+nXCy9SKbrB+9V9RIUILJYV/+CE33LknIsdr\r\nKw/6SEWxxnQabkcKZ/ee4h83cW7HCSnGs7G1Zx3PJR4wwvB/bZPTy1pRI+RS\r\ntqFbtXO9WNP7Nc434iZ+XHbOd0sv+cAmygpkeTxK5vnvYGURIzSHxLUyijBw\r\nEN+YYGDpHW2DdqFM/hPZrbqC7CMYOUQxhJUtKkyIVMJQMFE9SQDXlG6PjfH4\r\nlrfygSYDxAHf/m/VsEFT/vZIrFW0/Z/mln1xQeRSWOQr1+0pKWrkr33PuX8A\r\nhIVVdg6eJPBA07X7szScLYIJvogRHAJIogr4AwTKD9qz7dK4U2j3WpRU8+NG\r\n+WLv88lttYbwrrkVO3+CmlXH5I3ZBbd0zWrE+pX00p64aACbZCJFyPDTyBlY\r\nOyWtXu6tneo8sET7oA5aPlOX+3hLNGulJw1c4WCOFA8RhdIDYbe9ihMA7PCB\r\n5uvOreE7j6JtK00xvkruqMkF+uG0yUag6JDfx9DBQ4/GsxJOU+85Ca8x5RSn\r\nNgVZoPQ4JEpBZVdPAW4YRbMXGdZwXy6/U5TjK1mUJnRNpnsac0h1t7uklNG2\r\nlSYeXy7oOUtMkjcv4H8C9m55//SM2l/jo0A=\r\n=UsEK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.25", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.17", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.25", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f56cb6f7d520a1e33bf5265cade48a13a06c48be", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-YJ46A1pxTudiu7Puo/h5OgwMz3pv0iOX1fnjmtpUoWRLHa5Klq0GeFsdQWBGfmV1YDOw9qddAskeQZ9SzQPO1g==", "signatures": [{"sig": "MEYCIQDyoEMEbj4+ACUTYlB6rjde60gZfuudNe2p59Y8acLE6QIhANhHCw7lgfXAwdfvjcvmi+401qWoBqnWtu7Tt2rQ5KPs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4YOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp81w/6Ak/ZCthIcdlKNa7Q7tnO4BX3tEeblztQuoQ6gj0ztNIdyxK3\r\n1tjbMZ2rofWqzRqf2OVB5eLmh02Dj0XsJMhBfIm8ziFdfEGOPB8Se/fvydrx\r\nP1qIsLXaZJqt8//paDeQsyGzoT/Mpd7p9NohAGCwJ2C7UhWP1CDbqtI71RPV\r\noHnFVjdLyEji8fYbz846O/nnG+MkyOPCgaWm1VOTlxEYiuCemViDQ4jc7vT3\r\ny9K+Bd9yYQLH74eWfYsETdA9jYjArWV+waeV7avVwLWaJZ8Gus7vcTU2XsuZ\r\n9cLmLm0WeRPkvxW3rYFUL2phYJvYbC1X8l2JAstbEQPF5d7hZf2+5RF/hj1I\r\nglIVgGEbsA3Pg6Du/BYpE+UXZ+7wbp385+3aGflRK1H6XhYniUymiMYGio1r\r\nyx6chgwpTwqB9yjIEJxE8/fNJ44sZi9xDpTll7VKUQObEsR5BZNMiNQ5IOxc\r\nGtQ+J4Ol1wsseQqv5+ybeUXrHts3O8MtSFylWeTjFjCzZx29W6aQndO2CdUg\r\n3ylfEXAy/J0tP+9gfq7Il25wyFK6R7ZARps3rC/wHEOXxygJ12ie9LkRbL5E\r\nhZJw0V/Few0C7DMX72QKQr8u5oZqsm4ZWE410/E4/G1SG8pPnTjfnrJ2tUb3\r\nEZL2qevVEKwTRmnunVo5jZF3cbexeFvpL78=\r\n=e5Kp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-switch", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5-rc.26", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1-rc.18", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1-rc.26", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "71efd1954ccf0781515f826465f87f923127d146", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-YIDJ3F4XHGnsCXY/PgyVkjDgvS/2ajALct2sApfKjFHxOUo9gmf2RE2Dh9lSsJm3pn3EZUE+k7JwzOXDmvDW1Q==", "signatures": [{"sig": "MEUCICJO/lgvFPf7EiezxtIJEH5UJYvuc5sKH8n9YvvfBfeSAiEAp/WfAjgeagdtrxPuEgEuGGWSlLdL2iiVQU/uDKo7nBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8Z5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpYXxAAmvEmDkDUW7bvjusTdnIiE1/e23EOOadjRMRdl79bcVGsHXq4\r\nXBLMvbBBlXtCi1xQHXkKQ4my3wqBaJZLLt9wPFLUJ9X6pl443TsWaWLOCw7s\r\nbFlNWbrbjPmxzZViQLQCTV+Dfb4U5jrxO86v7rqahNiR5g6mkB0CHzIlF7/8\r\noyhEH6hCoPukUwYuWKi5VcDQQfY15WEdkv/aBxva3ehkGXWf/xO204n3DFF2\r\n7jvQfMqc28ABTeAopt96GrvfzDrGPOiaw4c5ZlS/oSVV3fdYK7f3/a3x6ySr\r\nVGMdGW5kIwNctmwHEGHUDiL/70wa9HYdLI6DLDkEG9/c01Vgt/IrPQ6JMi4W\r\n7ZBp6TJZDFbNO3m5Vr3wYsUq34JhAkcZk3uRi10nnT/QbKw+py+uumtrFKbZ\r\n/JeLrreSzDI3Wk2lRjz9X/KlzxPoYjyfHG5Xl86AsSu1yyZh5SgLY5n+YQIU\r\nam47sslJR3QxJxDQnObAOlomF/srdfyEqGicGYoS8MP7EelpmCeC0AVkBEpl\r\nk4rXmm6GjHDO/hkPNMu4+z6OFV5yXAiJ7GeAPebFzqKP9WcqNtAj5TNRfoCU\r\n3MY5bhI5vubmSIn6kiyILFY8oOg+Sxiv4MjxJgsFFpLhqKNuIIpqL4iEF0kt\r\nygxYo6nrYROvoFvz9EUjDeptMIJriz/cAlo=\r\n=rBlU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-switch", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.5", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-use-size": "0.1.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-previous": "0.1.1", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "071ffa19a17a47fdc5c5e6f371bd5901c9fef2f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-ITtslJPK+Yi34iNf7K9LtsPaLD76oRIVzn0E8JpEO5HW8gpRBGb2NNI9mxKtEB30TVqIcdjdL10AmuIfOMwjtg==", "signatures": [{"sig": "MEYCIQC6jC678OSqWHwmxmaLAWefxHWgEVX0pLhjxt/WqCOIjgIhAJHU9hcnKNXjrE2LLJgShDgRY9KFQl6PYysjW4tXIM1/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30390, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpH1A/+P8okjs1t5se3Og5SWmS+U+mQN6/ckLrs54NBPHXTdIOXp4Le\r\nlK3Lubq8QqEYWM0hiFlwz3i/AmCEZ7SiASCBxkdSGRF/td8Vj4HwHDVJRgz3\r\nUPLhNPmj7sfc2XTWDHu9csE5CGdS+dXzBMwNA9GlmWIM3N3VFiRFtyidnWCl\r\nwk1vBGz8A+yNOHxKD8MRKwaHOZGhYibgok0ec4ybibLUNNXt9Ne5jaQ/JIKJ\r\n5X4E/LJPn6qhhh7QpKmMeKbFh5tVmfO/wwBSuiaF2GRE4YueEhZKBuszRVC7\r\nBE9dBOImCOA50lwn7D9rXWUB3IhqvU/9R7HsSNPubnTmxSI0Ph7DOSUMULcF\r\nPtE4AnygA0BcjVhBJnc7E94HHdKIesedBl6Sn53FKyiTLDH/I2EfMxdqi2Jo\r\ngn4uDAfyvz6NnjJ67IZZQp1MwvXAfSSIGCu6flEhrrmPTbJu1xckze3go2mA\r\nK6oCMZsg1VuHywvyrZwR1FbIk5v1qnrm/+HOPXti81z4KC6/y/kJ16eMPDfN\r\nsWrlPR6mr+Zc5M3dCRMGEZh/pOFcI0A3DW/sHRAZQwo87nR91VJLFwOI059R\r\ntkqaku/eoIQhlZqFEvOFlx9bQQ49dCxmfRE09t/Kmevymb9VOdW4Hy52PWSS\r\nkLc31jmIOkLyqKT0NnEidqDiLv6FuUTZb4E=\r\n=eSgi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.1", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-use-size": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-previous": "0.1.2-rc.1", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "992b8464b1a521d10cbce0e835287d41dc69ee52", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Gf/e65va2Ba3oPPlzWJTaWP/BUTkUrNfrTSUME8dDChQ25Y88Ku/BdxTyW7hmtwZc+JV1t55YAI7WPLre22mtA==", "signatures": [{"sig": "MEYCIQC1euF2ZNw/8UUQjoTMntEdx7yyM4fV2uLWBXJJtg3YLgIhALTH5bj+vlJ0HPiC7gX0oO5R1m5kHmZ4OWtZDhlLC08w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAR5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpeuw//RyoxiKVzIkZHMoIlXQRokcu/chn0JBKug82LViCxvHXP7KoU\r\nnhHa11rlz+XLYr7fHvaQfjdtnoHSZYRohtegEcvoFjoziYGbDCoP6Y41uMq7\r\nilmPHHyMzTuhZ++QZxnij8DPvYEmPICLmaiyzLnQFtUa6CdopslQXJymYi+d\r\n5zQvRXx70DfOlqlLVFvSIANYJRJto3emtHYuqozlBpING3Io9+APNoWQAGJ+\r\nDszorYaLknaYbmkVEYqFt0ROkMygNWHvqJA2nMsn7nNWCcu+Nb+RE1xQkWok\r\nCkjXAVcBBDXJjR+cPh45GMJneeAClR3UVc9By3rK0MMF6wAV3IGcbB30TcC8\r\nD1iU5sg3oMExyepYyvULFSMVPffunuhFlpYY+NNPAKwO0yzMrCrhBtJX+hG2\r\nncZrKbYWqQgVXXFyhFHSrY6+zJxanDpX5BKFoANsTghaIhwShqP3xkrJ7x5g\r\nYnRIouSi0JNnDijkJ3vLiocvdnnQkVTuQ2L/d/3rR3045wbvsk9+iP4gfOGe\r\nXc4rBVNoWl4rb5wyiTZ/xjEn8zQViUmPqc5ZGzjt3J1NJ+FQVZobPK7Y3DZ5\r\nkuYG3Cvzp80AaqIPfjybUGyUcD9/QNO2yQBqEjUN4o7bO7X77tz/dDz3reRz\r\n1I4R2bNfUvVZp5Kscp8YMNtlcptQZ8HwXqw=\r\n=5bM/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.2", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-use-size": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-previous": "0.1.2-rc.2", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "64741adc3bcdad93ee5faa375e7cb3f1bda917f0", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-CZrKI2clllfyDLfrtNuHwqaQxOBJG3OdnVVcjgMn54LmkshGDFNNuG6xJhtX7evjlYjxKGlFq6GIEl8dbBN9Vw==", "signatures": [{"sig": "MEYCIQC7ICPvK3MjdTjt9vpMxVTfrBd3zmMziYEiFKmRFajShwIhAIvWo3XBti3vcS8EOldw6TYHr/5VY5pL2I6RzhORdBYW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 30467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrQ5xAAmZzVnD33HA4jMYM0yHfvtaUPJO5dCy0/oXiCXWdH8dOIAxmc\r\n7T8/yECoBugOs194WWQBr8hbDCaosXRnOGUPXe0ji9PfR/LQaqD6zipv3x2Q\r\n4TUENf8FQrgbT+RNZ8d3bw+dwlhkqhCwB9ST0CrLVPK7iwWfIQt1xKGHuVlC\r\nk9M2JWDgIQ0yIXuFpMCEp8gBslHULZbn3AAR40/uAyVY/BRwCfzcQsmcDAhH\r\n/lrHvCMYc3+ynADRzo7ePR/+78wdFJVA+Qc1d00K+k0JJKH1QYuQOYZ603w2\r\n8KcRtZDDfMX5GdgbHA0R4kQzPRhS7JgmLRE9gsAvsGuzcEDqFupIsxEmWYuP\r\nYyKAYQvcgN2L4u7tCX9TnPvgL3l+vGU0Bdo1158UXnL0lPQk87371oXDqd/5\r\nMKBhkLTbKofLASDhY/0etb6RgrbzhPNAvIzDRrJBLhBvR9CUOpI78olSm5lb\r\nBYa/rLNPG9Oktb8+/9MjLpZgDqMutACd/NVIYWKiROAitBPetyLfvKvZG6qi\r\ntfxSZXVg10luQkg/08O+MKBq2mKEMRqbNEfPvRRSWvUpx+si+IAu0gyo23q8\r\nqjZg8+SJyVOButOmQztG/bnYWoGKBen0OzMYipjPAnfyjZz6eVIqGP1Ylc89\r\nNHyBgOz4bmtXwq39AKZlNbrMDTJGfoLJrHY=\r\n=2w/B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.3", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-use-size": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-previous": "0.1.2-rc.3", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bb225fe382ad96994fd5f5a798a6e67fa3438840", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-NMaeb+eX+a9RNW2EAceFSvkIkgbtQViVRVIZ9I6wMapnxHD6Hdsl2bN7naDZ2Z7hGgUzkSry0cvDaNWRD4RAlA==", "signatures": [{"sig": "MEQCIC0czVoKPN74dDyI5Csk/mBc9ROC5HKJ99vp4Pgaz954AiBqMKhB8jVqKIQ/x7l6arRYs0GHUIIVwPLq9PPxhJEKjA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXJA/+OZmfrGmpTBERx2Mrd22NpCu68XBTXFPv+zezR++0q+T+VP9j\r\nNQUZBfH5ZerR7xsGYr1hGTNRRZeampx370F2Z4AORxMG+0xR0M6pQJVVwRfO\r\nDLllNCQ/Ue6p+C1NGUSttmx7n0Urv6xYSulqYQ/J4+QTqreSfo+qmRcfnVIS\r\nxjiChZsyC7rUirztF+u/aLKcxMFWu3UQN5CbpMfDRfssr+aRxgKnrcPP9NdO\r\nHRT0ruEWUnJ05e6l+SdNqHFsAn04CcN45pd1gA1OQcVeHHCO95qhNw5+YCQu\r\nIJBB7J5PgeHy8oxhqSzWB41eWRET1eU30J5QaDKyKTfJbxO7MZ9QVcziE5C8\r\n2M7WLG/J7yeXh1kognMPtZ28eyXKPoBlaWHAooQPIlj+t2fz8UKC8dIMSC0k\r\nEyNFlXTOm2PktMwhMZ9hYRg5Bh4/gnCgOCj2dZvCvGce57C2IpWNgl+8MpGu\r\nIr/sqC+Maqs2mJU4BAlEZ3L7X7jqUr8Hgf05x4e8XbF+icE5e9Rgqflm0AT5\r\nyPVNTL6gEduel2eBagPWgHXdWaUsetVowXzF439Oh8D15ZKAbZPsi4+QB+55\r\nO0xC4fANJFoa7gXixiQhhe5p8lOa1DGIq0Gm568qvm/T/t1BBpiGNR3yoII5\r\nAIZzfCgEggIjuJNvFzZz4ZHJ+1gJ28d5S7Q=\r\n=SXZj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.4", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-use-size": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-previous": "0.1.2-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1f7a40d03491a867f334b90ba9152d3834b2d2e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-WZQ6ZbP1a/e39B9nMraTaZfyVGpyVfNAcEsF4Zx4V2EYBl+ZIeKhC6AbhZ6OVfETPmrM+4zae4o0XTpgT/LG8w==", "signatures": [{"sig": "MEQCIAukWqd48qZlPVNa27DETOLPebieLBoT9fks2XoMdK/xAiBKiQ11GbGbrXCXkoaVfVySHP+SkJozk849c18FxsDBAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRsCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm+Q/5AZZY+eVSqao0kCaQI35okWhFPHKsu9QsDe8RmIPKpFoYSCcC\r\n/p9c3/qaQ+byL/PaxN+pc326z3WCut+x+tNwWOJeiLlUlbyZlcIXpN4xOj7y\r\nuQ1DF0kvWIq0RPH/ZYa9hD4kzzChaZD611MPTenYNY/k47HCHOC3KYtVhWZA\r\nXPTAwv+UxExl8u8bh+2HrecPh4KNRA4O6AbkE0nM5cTcoP728N074qnjnf7+\r\nDjjEzX3nUKm2TjQ5iCjxcsyEzPVVHXs4LNGM+XaZaH2qfH1b2/Cw6vOF//MD\r\nMVDCN8W+2+BNI2Md1o1mb8p3lEH+4K8txFcUd6eaHChppe8TvDw68bFnns/d\r\nG5jiVCxUKhyKSygoz9QBeSNTfEla/eDjWEmBY0jp1F2fIjtJgCoMyTactgXE\r\nM6uuRiUHx5dOgiZUxS9v8VVcEGJ+IQ1RKEGcCQ6kRvWE5v0Yx0Qr1Yjzx3w1\r\neAgPnQxZr4lFi70rhfoy1GM9nPYwglW/RPvaYdk0cgGnWd+8jnCblOFloPer\r\nRV5Z/j8ypxB81I/iAVu1r62yB62SMcSVpp1Ix9oUYpPNrYgwyu5T9OjeNu0F\r\n5T4t1snekBSiyOpUxVf0WdId4jIPDEFsmf4Qnz2GztW4RmBnEb/9BO/ddq2E\r\nmj1XcCouD6Zk2TNCU5y0IN6V3mmWFnkyB0I=\r\n=XAeU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.5", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-use-size": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-previous": "0.1.2-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "23949459d223c9be48099e0b454010cc8128877a", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-nL5+DX0xa6yqmXb+4vBi8hS67T0tJMQu+YtA+SOFLL1KqWEPmitAyxJ48IoXOQmQCVFPFF7aJtMWT0LZNhDQ9w==", "signatures": [{"sig": "MEUCIDw+FQ4RYiy3fGV9YrmGA4e2MlVK5Hq7lLql6zBS0J5IAiEAo7GU6FXdUJkL4Aqmze5Xp4DLyYtkUFYLht1c0FLS6hU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapg9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqujBAAgeShcE4TxkWJzBb0TC1nh+ccJVZWj5tD2yF6uyZZcNGeoSYB\r\nK6xrRWtzA3YNTXOM98Zh4kozZ6CplNH0MYHW3G5xs97pDM2O1JpARO1eYDd5\r\nIVjNRCgMJK+6yGOu5NM3UBlTfF2cXtUsxSo75Rsy/478RCdvGLXJuLQ/sckd\r\nXPIfeWl4V4r4BX1biIJGb4e5sclxIbi7QCuYsLE4cPyjpr68aAY/rstny4/m\r\n4pxXLtdKGlMHskvbDlvZMtMspGDiYEDEjAfbkMyVPpvZcX3q6w6dMfikVLfo\r\na0vPoCzyZX9lOSBYNNI01o60dgJuU4H5wATS4uiwj4okZTTSJOGE6D2xT7/9\r\nJZL8a4V8cg6DnEaqGrobEP57FWEGlbXom4CsLm7jIRbeX8RnuhbgJ+TbDZd/\r\nrjAL4IaXZJZkp4OZZseFIJe4OnOehNBDQdNXBBp5s872RUZtp2CTzE2K1VZj\r\nusCts+Sqnw+zKv+j17cqBqQDvSG4rgkPrhMyY2AhjDdsA4cNO/aTrsJFlHgO\r\nE1Ba4O5vzk6AY+v6NjXq4exhVPYrtR59Rk4B+9C276Em9z4tIM9OUElBMmmo\r\nO0Zoq1kJUsyqkpD2uIi9plSgwuMNY87TJnh8VUbzfB3jGr/crYi1IYPiSjiK\r\n4EJXPUfLqxcZsGWVLDkgLY0SZlJMLqA5jog=\r\n=gibY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.6", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-use-size": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-previous": "0.1.2-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fcc14ab398d6f44360163c04236b53cab117fecf", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-x77Xk+l7/h8P7ph2lrwLZEiGjYmo2sdG0Uog4yEnoC3VyKtnPmT0uGkPEVRDPKdRF8B/SalJeeodh2X38p2zsA==", "signatures": [{"sig": "MEQCIFWnEMXRPw4Pj5bb2tdjn6fJASjNRuumXKL6Fc+tR1COAiAZYvu4DhJi8ONFvN/yyqhQn+bkDsZw9vpKiz0v1jQX2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8yQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1/A/+MoCiq2l0BhQr7VTid+XmHbFb1wx91xzHUUF0SViM4eEX6Gvj\r\nGr+pvL+sli9pAzox0IxsEhX4wWSZAOju/9UfuSIka8hQpBvNPYw151SI5oGh\r\nx/amhxjOtbgywCpmu/a3W5UCvudnkyn0NVQBGuMxDf4knXTFUXEbJU3fX/dH\r\nhEeJ/XtjvAluZgNKUxtVWvJb5EwQANysbMdPLCoRx1fhhtUIwzu4DJYCSUcc\r\nuF/wg4ey0C7gxxCK9jcYW/0hVQaLMZoG/yOqkr2a33Vj3YU7eyPssgqVlvtL\r\n9GqS0RtEpMuBIgE9h6WLWs1vjiM9QAEof99+3z7FI54xaYfkoYzmfBYPydMO\r\nCGmTshreMOp5KhjBIujBJwd6izHdK+eg5T507JWn/p6gGykSU9lxQ/CfoE1m\r\nsAPAF9FS3QnVF8/utF76GAkLcz4DSDxZCtRopfuZfQlIqlxmPpAxLsIdKrVG\r\nX3HeLwHiAvWc9j/b5qYYcBalM/wUi3LKGBJzwdsKPn73OS8WBWRsQVaRTrnE\r\n66Q/BjTUTTlN952EDammiRw5WNiYUyLjnPzgYiD0GkHLPp6+sIDhl+eIG561\r\nA/ki8YkIMFTQwHnx6K+XMvxfAnIiSOQPnieFyuNXTWScdOsNTbk5FxTpsuf1\r\nQ5UNBGnzU5AScaSDOupt4CqsvIqgYs1/bus=\r\n=2G16\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.7", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-use-size": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-previous": "0.1.2-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "791089ce9a897bc68fc74e56d26d7e8970738dcf", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-QqJ8ZxANmBtPrFwMBLEgBjRB2yejYcPpjUEF6kjM3XLU/J2ljW9n6vMAID9qrJFAeVjfkeT1RLzh8EQE9xjLDQ==", "signatures": [{"sig": "MEYCIQCwY7WRnkOoAT+r9nYtxjz9o0AuW/aj849sP2LDkWfAfAIhAOF2nlsKZb3Yet8Yh9JEw5sKvecBxV/FVsp8059P8iJK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia92IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmot8w//S010XzEB2JzDTK9a76C7YsND2snUmeJq29L7KUQnBrD3rTpM\r\n010rXp5t2iHAFQGH7gO7h6Kn0NEkxL4KVQNXSf8Bhn2v+k0tlITE8HPS80hb\r\nOnypdZ4pfh1VKsubM37n8LfREPIcMIbpjAuJPMjwNheFEkFLfj/bisR/15zx\r\nAmKR0i1rsoSPXTV+OxEmU6E6Q8D/paArD4YDY7CRs5YTD4TlNuMCGQ5wdq9+\r\niMBMW0ZzJEFwU4IcChxyU6/RcGEXzn+wIZMn/S8kiGRP3s39Fyue66g+0CuE\r\nv8OvHCwaNrWYOr3ITgngslszE7tH6ZOw2tzUBh/azmORr2DmT8OzOmRDc91E\r\nYrAZhbPerNgwuJVj91HPGUK0ttEjg/EOzEmGKNeyPZC5zd627k4Bi3qg7YuB\r\nQSruRCCvZpkI1ROrE/6swbsEeu3uEJtZNjQgvcgFeM+IFKN24IMhljqupumy\r\nSxRc8KPph0VC5R1sr23dEr2TdhxtEJm9/bG7p6og7r8KTney3S/dv1yVtIMO\r\nXvi3pDrkxUZ5vsZIquR50UQFRk2rtHfP7mb3V+SrybK66NBwI47mvnOa/i6H\r\nsAGVkWqyWWuGT2W/JFdSfPT4nXRJQwQVRFlgV556nI/qctBsKssYN7hG60su\r\nfKkvuTFwLtaOCu4e9RZWOMRjYI0o29/mXOY=\r\n=l0UD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.8", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-use-size": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-previous": "0.1.2-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "77404de034c1b2402fea0810256a9e282124a254", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-DHACo81uTv/nTQYjDAbERjwTnMVjY+LdevyQRzfpGMV0L8YTTbu3lV1Kgw/djIdr8ePmjOcbGYZVath+fMN7XA==", "signatures": [{"sig": "MEUCIQCSo3VjszEidzRUrotym46+Z7q63Gc3j6vvlbWHF8rM0gIgQ6eOkoDSrlQiRpInPvlNzBRj/ryyXJ/KShlEmXfley4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVieACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpLnA//SqAwpSRHOyQTKAdXG+71KUrS2t3vS2RhjbTKREgGljC80QVL\r\nOBFpsFGz2emoc60OIs5Ucytr8x+WV3mq2g01uR+dL6KdXMRUU18Unt0olQZ5\r\nxKv72JkC7bS6OijOjx67KNBXgvtbc1ZSPgsMzmaVk2aBeDpTD+m8wKlxkuLH\r\nRzjsImomyGw3pxPWHqvzO4FdHX9PFQy6nxRf24AUh19Je6vr5fDq9ND9cQeJ\r\nI8Hr6ZLr9ivmImJHogjGqSAzwO/Ss67ZW0Hx1Co2cZRmDk++5BV40ZBPfS5B\r\nC5IKsYkYcvjpT0r1y9ZcxNrm0MoSoFavP3f6Yhh8rms+U5+zgWvzrTIau4aR\r\nBM8BNLihq+hq2HBK8ZkSP+cru4utSqxmKUuAttgF8T1kjPNb35CAiBrIkIob\r\nEUnkA491VAOlTmbmi2igAuGnOX3p9DKtLMA2OEox3Blx2blh84vNFFsPr55c\r\nYhBv8GhS4EqkWfNar2ShUFLYmGcn3mH3sPTpK+eoQQP146vh3CX5DI0UUDlv\r\nf+FfGFkM7Qvk5y2vkHYSNwVZZgRbhqIMT6iEcAnZmTiJIg4Z5mIlvSQf/3vW\r\nm5aPD3rJwgvk2nZCDaTtY+Gju3iCV3NnEZuM/8UXxSLitr9qyt30pbF18Q10\r\nJJsERHfh0UX2zwxAkRaW9/WivhdC43mRi3o=\r\n=zoHZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.9", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-use-size": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-previous": "0.1.2-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cfe6c340e9954a5ce2e21ca307f9c3f27b3ea861", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Bh0u2sTHaQnTrtIvcqPHVzhUTXREf9WPdoyGtayR8Y7qNEQKfISuXFMTeUnIwJJmxDJ72HLzAgomkIwGIMtcEw==", "signatures": [{"sig": "MEUCIQDjMXT0ReaTCNUj1i2TXqTWPP8QcNhyil4bH3KmZ4i/zgIgIpUFytouJMIPSmRPYL/x/gNQer3b3qPiyMBCTmv/Tx4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNiPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9Cg//fQxvLCjNXmiMOOq6aCo9fhgZ+ovHeIHsFQ35mNMec3tdGVk5\r\noMrAHjNEz6sD+SD4pF7uGrNAhtE5+pgSOA1c2ea5EBVRfNHOhmNTnaKRywaC\r\nzY0wIdLkU9YbOuwGzcmz/eZyXJl8zXL0LQxL/fnfE8p3UJHod57p+bBMd7Oh\r\nPik+76y56vzaZBk4VkVgO8F4A+a7fBXaAhl1caE00q7CVd0+8a9Klhb52vwi\r\nPMmgVtHTFXgX0QdKPn6/6VrD/QE92VMpRZvBBSUmlMx04JNzqP3sMzca9ILK\r\nl2mfaxxZ3+VfPut2D4ampu0NkdCMLtKvPMVxJVhW/rNHFTNf97agIMlrvDQA\r\nf1oMJUcHqcNJwy/3sDlu7jFXI+JuupKEsP4Qb+e1IM7byXiZ7Lgx6e5vNV0C\r\nQum0OcoC+ekEdPtbbFCzql2Jqc/kP6ADr4DYHUZBMf2/OrpQnStHchkqD3wk\r\nDqrmZOyYgXkv451bFVOuwaY1+elZFcquxFRx+rm3sKU4IHQH9KFpewF7adsA\r\nXGnXJow9baS9sQLm+JXGnVC06EL9fYQKUQOgT03pI6Xy/iUEQZw8RE/JWBtx\r\ndpQcCEJqyddq7tpDyJRO33UYbUwJqzO3qk3BFwkptfvVYSkhjEHMoLacj8pY\r\n2LSmHq/THEXMhQwcckruXkE5BkXg47+YMhk=\r\n=Miph\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.10", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-use-size": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-previous": "0.1.2-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e9280e5665422e5819dbd768fa049b57cfc7e9f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-ukz5EPlaGRBCS2uAO8uoO1cYN7TG3jX2x/jm2vdgXQZiEPHwsPMpIweueTp7JicVxzJPsHY+1zc9aZXlATztJg==", "signatures": [{"sig": "MEYCIQDKLfp8+KNp1EGmmQgxD/lnQTbesqZiudCRYzfX/a6nCgIhAPzIXGdCCfiLPfzatgcFZcO73caKzEuhd1G+xidmDltK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrzPQ//XCJcAjuK1MDAQgTsjCB2FhywbRdvKyHNgLHKckwYw4p3oW6r\r\nvdg+1l0mVB1aLEPYqb57OrTclIfE+nsTlIjcErS77zIyJLqn1d2kbNAmXEQu\r\nA5DciaV850uVORPxYnDTB4rQaMWVVz/GUHQRUlu2B7E7ZlZVoGHI0h47kcVH\r\nqM5LCf7BPb/zgaWulSvWIJtTPjKM7eXEdom8P3VAQQ/PfO7oJGtaHn8okQev\r\nhMa7niF4cpRIB5I67CO0eFrClgFVJuXSViz+11klNgBIBMpskaMRl2TE/BAD\r\niscfk5Fix1N1UO4nuReulzXjwr+cajDk53htnqFLJCnRr13QBNPMC1yCuEwW\r\nWMk8Uh22k6Z71fG/ukT73RUZZoE5lvjwQ/hAbXGFAOlpfTaLA9dbII03kmTO\r\n9GhTvI7x/hUWiAiTyYjEdsULdfy6B1XwLKW7n3KPnVy/EoX24XnGEyEX024N\r\nzEaTShIbJbbSiZl3HJPLHI17DlI7ODDdGDEbe+fqbBT7fousaBQdoBJFS3lM\r\nGbF1MslJmupp2zOBEjRD6ORPg8aX5VhiaYmkOufz10TtZXnluYGwPhXnkW9K\r\n7P9+cSY0K+qMDT+woJLT2xAfyJCG4ARqfitqk2I4KiUVwlKjnRSJKeioPt2b\r\n5shQiLHMWeQb04ZMP1ReYWR5UxsHY7T9LMM=\r\n=tkU3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.11", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-use-size": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-previous": "0.1.2-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "34e8b549a8c8f422732b25d238ef89e715dba632", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.11.tgz", "fileCount": 8, "integrity": "sha512-xgWWtZXB+Z4bgbDynuAeRqP01+H2YJAEjm0nq1HeQaxn9R6SnXa6ccFbcTCzlQT6bfn4IJRBcRIRIwqeyrEtcg==", "signatures": [{"sig": "MEYCIQDwhcMSK8PzYdaw5tK1jDAv+FnIqfcCy6CaCkNlimNKJwIhAI3vlCBxXmXD0X7Opt66LFlNmIZpzfGBlJegJyZOjiss", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNhA//Wy1jRDbdT09/QwQWY8+rLqQDeW/eCjg22ivx1YhX80WhWbo5\r\nsQ331GDY2qYiyWTfnZY9OUmF35fCPURt//5+zzN7PiXT+O8pgF2Hp3QSUqWe\r\n4X4ReqOcr+l78YsiY8qdBchxjSPU8/56bd8wq2WmsGEZlYtRbvYNt8JRx4XI\r\nX0PdiI5EuW3EBuW5C/fB+ohSBu1oQGnWal6yfDFAqIKQi3ojZ0gVI8wdOIXc\r\nW4+PeUpKttkmt3kJHShMPmutGfi4w/I64mdfKl/w96AwXXWy6CotQrZneDgb\r\nk1Hazl5bRyWRvl7kGgeOmwTOrzFisKYqkPllpJQsrOS3m8nhNS1oX8p2QYX6\r\nKxz/dwz9NfGunMW1CibyoEKfJ+8mO0IvO1hd36awnc5MZsHBWQEqFr7cQY9t\r\nSNXzNQBKjKFsNWYDQBye64XR5/+rPjjKeXfaB7oI6r9smdubqN8+s4bZ/QRm\r\nbnGUPCPtRikics2oqwiTFcNUv8q1XgO3gKO4amEjO6K7Lna5yWCRjZS/bdBQ\r\nMsqMbwFFiblU1dcdxcAJhfTcd8nkR+XL3uKZPw7IL5EKdfuvipPb0wbYmey7\r\nBOMd6B4QzvMmH375GlvV/MbvTTjl6KOYyyFr21l17w1jtAXGY+0uLg9HWw59\r\ns2thlr0+jR0wkWXoOi0S2CrbVrGjdsBKIGY=\r\n=HpwD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.12", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-use-size": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-previous": "0.1.2-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5fd664dffdefaa5040cf5a0f969763cf605ed037", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.12.tgz", "fileCount": 8, "integrity": "sha512-VFtlrjBhe66OY0n1SzZbMfEs/T6Xz8f79GxbJJmkFU94NwOpVWwAgTQ/yCvu8dfimu/wPAXLGxD5eevolTSBYg==", "signatures": [{"sig": "MEUCIEiqhE/dUXWryuRf8U8T31Ivo0GvzyDHkohlz9EXBC0rAiEA+wVRL+KzVVPma+wUtmPgG0o2BRjazloA8GFfZnD+R1g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45607, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqswQ/8CCB3wYwBdBNpX/01Zc12ei8cPIOTVfKiFfLdnf9H1RowPflI\r\nPKV0dY6e6sHjL89wZ8xXcmQ07ZDWGVMygNMcCHFblmveseslQ3LGtxfwaHWs\r\nqwTneEJoTGrIHfz5kTqXa07nAvlaQnZPmCt4tESVkB1lz036YXOeWLxD0Z2m\r\nL0mT4gw24IazF7zEHPGRW1bQuIBcDq+RSeOqZUlcFiC2MM8IuOjnVz4g9xDu\r\n0bkd3IqwInIJpo8JKyJRNa5nU9Xg9jJFgXIecy1Ed3Id62ObiBsq9ym+TORs\r\nIgJJT3jz04Sci3gHlyKnY8e85kgvxNiMWpq9Mfh6bTYTO8Sbg87fHawCyPNM\r\nxfWPJonFjbLnyv22ol0j6Ykf7at6FSENuD7EA9qqpRMeO/2EirXOG/zHO8lf\r\npIlhRlClQsTDRnct45hFT9Dy/SvBjingowq5xEJLG7nA1nFvkm7YQslnFhWU\r\nC4cjv+TLupHkQk4+u8XNGr1V/aoX7WyfdsDMXxyiOCvY6AXlzd8JL70awblX\r\n1bQyIzRPbGYND5P5xl9GVKbVHbWPGtq2QXEKXcCk65KFGtwjXEv91BnaGqhy\r\n0XWTxDbDkB2PkIlsIhv9LNrg7dr6jPWnU2gTF0nYh6mq/UVzJ+WKbg1iIV1B\r\ny1VMKTgr0wpnGSNyiqhRQOyzLZY3v7xVX4k=\r\n=7i+Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.13", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-use-size": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-previous": "0.1.2-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aecf1d668cd92929226fd045886dde7d4480494d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.13.tgz", "fileCount": 8, "integrity": "sha512-wtleoCslKHXCfe3HsCfbZNXDEIa56trXJSs0SBTOah8NKPluO12QZdxni0DUAO33O6dnU7D2CfHSX+zw/oHrLQ==", "signatures": [{"sig": "MEYCIQDR/Ah1Qvz1qLtRhoiAG77Csome3HN863STRGyxq9kwRwIhAOe3rtHqC9FdhETpE7nqURtrDnDDNTIKZYAT9SXSRQ1y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSEw/9FQaa50OuVgze4FZ3cktdrTH4ysHxeskU/KwRM0Q2GhcZLZKv\r\ncG48tI3ihjOiXJ8cHA7tzf184hVXHg8A+MPY8ifpGnKeJppjnYU25jzKCI60\r\nszBUAr9u3JV4Wxb+KYKfuG0WvIGICogfqFi3OzyyRrgxlHRaZGVnzSc9qkLW\r\nAv+dChn/IrH0Y6iO70Rw8NUYK9XK9DSomnYXirAXyCmZcggYnLNEZy9y0VWz\r\nd5bC4D3EvpPzHBIByJgj+zASbs4CZl+YfykBAM52tkIeFwDn3UUPhUIu//z6\r\nR+uT37ySSGSmS0Ffs5Xuj5do+NcO35zFwNiPGfEDFB8t13RA6atU/kei7KdH\r\nha56q51DHyrFx5VtizTwtM6Z2uwwfpFP3xqFAk+0v9xy2UbWh0nemz/uzY6r\r\nTV5h4ihcXEoKCPyd12ekNfpnIpZR25MaQEVDu7wGcuiiy4CVuqiWhqV3BXVg\r\nhloxSVkhAz3aUFKN0EEMv49jjxv5Je3Oac6J/ich4R4uXAd4+YM/Q+m7fZPD\r\nWgdgkMCMKD2dLj+4GTQffFvtkIgrbZP0wQn9x+M68RKfLj+9DLkNt1CUap80\r\nui/Z75o3/elbsn4CaMPAuq7SoNoyTYPs9lDS+TWLAvY70KQI/Kt3wUN64WvY\r\nn+s1urJlGNBxRSe+CWxmoJNiXd0ivAKcCUY=\r\n=Lm8K\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.14", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-use-size": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-previous": "0.1.2-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9f1e26618143613bc30d3eeaa119115c2b489a57", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.14.tgz", "fileCount": 8, "integrity": "sha512-WIK5ikOCl/H8NIsUs3o1ULxVYq1ZkVei3AItaIj2dyqNmPotO0Qz7kFK0lhVrKvOE4UzXY+8vlyRgPBrfFvveQ==", "signatures": [{"sig": "MEUCIH9RQSORpgJ1LprjO7S9j2HZPuG/lm28R8oc3QubKWFTAiEAptPK7TC9/2uG4NdEbgeMmOBmgw/iuYIBaDO7r1S7E4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8qAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4ZhAApJV10gS5ULNXU/78twmL92wyP5AD0SPJ02q8jEtWwDwlGXPo\r\ngLyiMT4a9WEuPOfMUUve6+XPT8jq5ROB8a/HWb41GhcBxc7ThRCX670YWCkV\r\n3Hd+E9j9EjFcKfySw+4e7+5uf8nMK8A0EQ6qLm8uyhIPq0VYUPokEIM5U7up\r\nTRUPyHZjPUy38PMy2fFViBmvD7wJ5s4xSWZ0gGTAl70O3XO118qPO3loooDN\r\no6SXcnSatOj4xR2yZZ5rllm2Bzt8fvhbNKR8lEoK/DYi6POzDNKWCh2FLzgM\r\n0pNRaRLgxC+91dfe/nOg51zzZgPfN19Ixii+IjgxJSR37ohhlfmx7WUQc10Y\r\nTUtZYXyaJJ30N0o3D4iIZoYL71go8V1EKJsfYT2ex1QOngnvQPKaSVkDa9Us\r\nR+fgyISp5NBgyDji7YyqtoC2qvp1+Zz074UONh0NIUiNQbPDYAfSRwb/eviT\r\nDqPiCTZZG5W2c4Ykg377U+q8a5uKOQrVmmjyrzdmAmkVp0cElwSTv2wtNzQt\r\nFMthHD2yR2uv1gYcMwoNocJYtb/jodHnVzGx0tEbQ70BRq+mMuwZxHPYpP2v\r\ncARsQB0hTxozW4PbDowNoRvHzvOJGu45AveMmnRsWkIIambLvNSc+6vV73WB\r\n8aIDtogajWjtLOi3YRGFUhSMmeBBu+n9vUo=\r\n=BTD4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.15", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-use-size": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-previous": "0.1.2-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "665598bee550edf380e89cf5385f5a9e36154a11", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.15.tgz", "fileCount": 8, "integrity": "sha512-M0T6dMCLfK3HZzf1mTObqTTdtuAWxZeUT0T3HG620zQV645PKaioVEit1OPAJyG9hok8WHanrQVRO8nIkUKT9A==", "signatures": [{"sig": "MEQCIChkASlr5w+ls9rIkUZcD/7UGX2dJRYjliP2//HPY9ERAiAQzt5z+ILOf/WxXGbc85rp8LpSVldvxyQyRs4xaFS8eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA01ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKqA//WvzL6x0Y6tc3a0aVEOtjjLRLIYFFeYjUvp2/iDrCazpnLzyq\r\nirFCi0DGwa1DdilFKTfNRKArZ1nrlafNIPCUeQ1uY4a+vmxrIOxcIY6gx2tU\r\nfrVeVGLpQyAl32BfNrbL5nnqLaoSJL+oqRYKETT8K8Kw2OgZslVkEWMCd7hd\r\n4R1amv7h+n1S9sjOadeibdW8pcmUM5mHiAB86MgdLRD24ToFZVDZs1sI5Uul\r\n0BRAld7IkVPb6xu79XsgKzR8/24uYC0N1xdMvC0RwW31pDe3mx2MGIzGWAYa\r\nrBQZcN8ULmTmLkNo5BUTD497TCzpHP8TRcyU+Xr2We/BLiIeE4X9eFBuBVPv\r\n6oBKbXByu7In/JILwZn29Qv/UL38iOrwn4efRYwBCIaBzIwFpwJ4p/XMzq4y\r\nPAe27uG3zohGVCLV2l2dcgslDYJctIfOFuwVNwxPXLPPHayKV3nSq3fN+Zrd\r\nobfF/333wzEI2hxylqm+luBlOzJkGrBKb55F4LcXeWWpG9NwrqPxOOxMTXNA\r\npRTOqg/Lu8zgJ3FyCnH/6H1FEt55eqx0VJOj+G3QFfK7WjRj7cj+9MFONNAP\r\nphpOo90hS6c3zn2MmIUwMRdL7qjvoxlA1j2Hpw+vevbvUeaVXL7NfkzZToWo\r\nRQZq7rQs7+TMB/7sva6oyZUWYOgFRBy9udo=\r\n=7dOc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.16", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-use-size": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-previous": "0.1.2-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "74784f70b3e678dc47f2e5ad557c02cf1f5ef379", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.16.tgz", "fileCount": 8, "integrity": "sha512-BfWV/QctDB8TDhW80Xauz6p4xja0WdmOLMmQNVVgvd3YCYfVJEb1ozSJ6hT51kBMUHnSoK4HJbUqBVKWZkzLxQ==", "signatures": [{"sig": "MEUCIGGtdVNkHjAzbZmdi7iqfqhrLWfc29VAHAaKWoQM34paAiEAxmkZWI2rTmAlsatRdVj1rLHqFqDqQIUMSW05m0bQFes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmphTA//ZpnfW+at81UajAeAbgx/3Pqu0fHMIbNVXsF6D2yIPGQN7W9h\r\nIzYD2w9b5hPEdIAKypxPEg0O16nQH9WN88Aai1PM+0J16eZEpGlLs4fkcHLX\r\nP2CvxTVTeg56I5ovKylOGYMcxwmw9xYoS1bX96waphnSBzv8gxOLaE7Rtcu3\r\nzCCcJ6z+49tgAfgmlot5eLzmqZK+O/qO/W+9D86wdVqYwaHlmOHmh2Arl6li\r\nBLeMfcISd9tQZ7vM9Zf6PYfH0YHejfvYqK6gd1U9Zk/s+sf8mDF39XM645b1\r\nIFgZyXSr3OjHhlni3pqbbk3W25C+EmxVi5EXoR6qZdZmxKuhO8+OKlQHBhWf\r\n2l76NSaU/KqGzLMdYbJ+FgPyZ+QIDhts1t6zeKHL8wOa8HjkVQlKf/G1yK+A\r\nK0E5LsLgut9d3y28BFTzPB2db2OE2V8aP+KQHYILb79LXyGhSyT+QhE/5oiG\r\nkFk7fzSUczbt9s8i9xbRpRuMu3nyNr+oynsdI+wjs1WR+E1Rrqf24HVqU9XB\r\nTlMBbdAiXQ2ZK48BiXhRVFOHr/EKYg4/7IvL8w2Ns9IaaXJYJAvT+m2BaUkQ\r\nsS1Ucv59iu37RD8h3kqnZx3S4mmhaWiUSRlU2t8FSGBNLYfyYRdp6+5hjNeg\r\nRwKA67A3SbVLwxJ8SmwKZ4oqJPFXyLVI8Rk=\r\n=ROi2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.17", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-use-size": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-previous": "0.1.2-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1647c789877d46630f2dfaefed175b2ad864e110", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.17.tgz", "fileCount": 8, "integrity": "sha512-xpou6GmRsKTnoEhbl8uLLHu3S+Up/8ZfU8toZTarKMtf9NvR3TlOdQBgXB/67kTVDQFpS5LaIaIf3s/KQ5iSlA==", "signatures": [{"sig": "MEQCICn2bnHmuaBhhNeXLpg+/lWg+XPE9nkTY6BX/1vGhPiPAiAy9wti4fa0KuoNV+wJRHvm/6RXqH+RrMLGTlxh4tiExw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh09ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmzRAAkVdm6lqK6QTAPxB9T4O5lAXZN6FDZc1+yP4+d8IPFsXgE/de\r\nbL0kSNxKtCSejKreQy9R1/nWONhpCJWCqCt26Oovma3z5xYpHsGx2lJcdKOo\r\n88DjhqPNrLLZNB/PgzY+kiadRk0pKnOassVjMMLDjRf1DsiTaU/EM1ZLYm3m\r\n4QiPLmpOup1quiHNGwGRRVr8T9ifH7ce7FLsFIJdNMHDNw9hTMZkTHu66o4u\r\nSbvo2ZwC/UMPNDOnUg8b6wn0Z23PK5uFanuRhuh2aqTvGGdlDr8kVQHu4ucl\r\n1IalHaPiNBcYTfaVvSmOI0dYN/YxeqPCvyM7TRK9IHXrqXphhN1j6g4A/q6Q\r\nVzZXb7daSo1Lw8auInhNcu6W1rEWsVPrWGnqbaXG+wAY8KOQSTLuJ61sisv8\r\nEA7EOMW/KWUzaGUcVzEFUTmt4XOB5XPO1gTNLiImq2M93tkGp6RKIR2NzsTs\r\nzikahz8C/e+4Fpnms/lm3gjXlnfZ0dWMe77+G3v5FkZt3K3oTE4v8UUDkhf2\r\nJVIxeMU5ek/I0yXpgO9X2mJtzKs9+0y6pO+IjwVFttsynJJMuXUFgKMhqEa9\r\nPpd3TDDaZkWbezx9KQZBsX6EUWBn029krf9xve/+Jp5i/SVgFMLeBueJACmV\r\n5KJqwwvrOpB2otzXj/1G5IBQrvJiArLDURQ=\r\n=Su+v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.18", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-use-size": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-previous": "0.1.2-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e3f4aec7da444075a240a81c7f284bac63762859", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.18.tgz", "fileCount": 8, "integrity": "sha512-PLpaeCDLfzUyzXpSIeAr/zpm0LaoJSB00AAFMOh0Z31Z9CT/fODljbBJidZ1Ab4gXxhmaI8qe4gKNQ+4DpvsmQ==", "signatures": [{"sig": "MEYCIQDriLbwc9z/LJqwfALJn6g3n3UoJccZJZUQBhQtXDBsdgIhAIayXJH+j5uzkXYBtY9zC4zewie28FXjM/3pd0kyb7cx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOdRAAj9OlAQ6S0hL49S6FMwpYRvtmn/HDwWt1G1uAinqAK6JGeAVL\r\nYxN1tdTg5JRYyV32VJWYhPMWCoNGNJWYXIXpYI0rug/IeQER7EjPBZggxf81\r\nLP/F83K27Y4vVgv6QckYCrCs0b3nIa6kTx2oM4KIsiC6VZkgNohYtfNL+8eS\r\nm2t5tNx9sB2PmZizjMKIagVt8xiCi7RNag4G4TiSip6Kve8geohxHDZjTVQ7\r\n4/AN3+HaqHs+HeGbNMOnynqU6tua0HA4DkAqtK3DG2ww7IZ2wGw8+1xvJoch\r\nr7kANX+dL7wJfRq2oJfAtgVrDYKTR/KWat8WIq2Qoxfh/uxJhhXbBeRgwJDo\r\nnrEG5n+tQJR7nNO0UDMwZG5lQG2XqwQudIT1pxgOJbMnbcFn0RzGBk1CUHVJ\r\np230e7fX9O6At816OdbOth2RD2b30xZpgWPFRnRtqUEO/2MsCQARfVwQQFcx\r\nnttrGxW426r3ESjCQ+rkyFwodTifwQQx3Q8ChSzkWpkPpFxayjl0CUps8OBS\r\na7SaZq0cOvOE5K2x7BnZz60Snfj6lEeWmcyoWLoSAH/4AJBAeH1hMcVYRV3Z\r\n/Zff90wL0/vZfHmixX61d1o3wqxvIUHZQ2YHJb2Acb2TEfidVYz+QAWBIzlK\r\n+laXwIvxNVYz4v3jFvXEmQ9HweWVgkWDpiQ=\r\n=SsYR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.19", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-use-size": "0.1.2-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-previous": "0.1.2-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ca127c88a63bd85e3cf17fbd47e827c13f2688f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.19.tgz", "fileCount": 8, "integrity": "sha512-AAnDac9SmVkhIjzJznkbqD/SC8zKilvXhu3DDdxen2C+ZmbrFvY9y7/Wl0hr6rfVEOLro0+xzpQP9QbxtHhTwQ==", "signatures": [{"sig": "MEUCIECbOiQdmspVm7+k7IQMOigZ8r/yZ6FZILj9ChRil/iRAiEAyWXsZ9Kh/nbCdBzMrVJS9MRmZb4b4nKJpQgAGXM4K9A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2XCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLDA/+IwSdJkxxO9uhYktrnKD1SX3pyB/0JzxXgNmmNAztort6wBiM\r\n8DTKe7A0fqFdB5RXx8Vi5wwDH9gw2wpU6b2wjfLiiYZ08hZnHq+2F07b63oW\r\n+wF3035EL+7kaF1c4+mQLuSYIR1WjfIjAahbKpFxCYhRDuTiZr/erDOYv/oQ\r\nCdjt7D0QaGqEZon/lH9MhRqz0fn6hskmJTOpXdmaxDFnCTLJljc6CX1/YzBR\r\n6xzGMWQnrqZRXn4UmJhWW8TP4QRhzJRtAd4TYDB5/KZo0oBKJVmnqTuzYE4r\r\nsgCRl14iH6ya+6G+VMVIPQvwI59anvx7RKwOSw8UsUXKurGp372mC7qm0iH6\r\ncQN8BUIu7E29UJ1mIeh/WrXnVTm/10UumkGgQSkh85ghuv39fflySMiEbRFz\r\nj/CUVkaGvqLezD2IJE7lk/423BWKNWVp5eaFla6cmLF5L32+S78F0Xdeqtxz\r\nPafgXEcvhogVaBfYO21m1SpQrDbDPGPqZp+KQiD8YOsEBzUpVAF3v98GaMZn\r\nAeb2d2Vkwixedjltbo30f4ILlOEm071GYhAHe8y6c3yILkWLcKoKtjB+kUIS\r\nZSHfFpQxedCKRwsYd+cVtBCEoaU5L0YNy/0UAiBKfz2RxDzQYp0ZRN4E6men\r\nZtQc4JX3o6uo7RT5GUyAgjIbCyrA8VuFuig=\r\n=JzMO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.20", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-use-size": "0.1.2-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-previous": "0.1.2-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a3613eb6c138cddfcf183e1fb8096aca77bd5392", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.20.tgz", "fileCount": 8, "integrity": "sha512-zV0qq7oy/5Imfxw/LgQSeYiCTL+sDUpW2shp+sQ/uai7UPHGJmTWUCub1nh8zqkiI59oBRG9AZdsISiDXPHLwA==", "signatures": [{"sig": "MEQCIG8d4yDmhYv3j5FHCxywIUD9yopnY2+PH/RvMcaPnlABAiAyvqiTg9CWTPTSpNBkDRNY43jbaFTTMI6KXDb0kiNx7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3b2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqLw/8Cuaco65bmxz26jzgPJvkU8BVB8Z/uj/b/5dTEL2CVxIHI+g7\r\nuJJSOby4KGQUNl9qZc769VoBGnhDo3YbIdbKMPy5r6bdLCuhzoFM5sa4AfYR\r\n3dog4+4YlF3ydrTxawkbQ0E4I14ABDQObPyoDP1oQqxu5kFQhjeGPZp8eiCa\r\n82GmJASe0cMXL7mvZl3QBTnysm8A13cxRCbQfnQ8erRuvwC4ds8D4T1yKTzz\r\nFhFCp+tcSnQwwKSKcLIUkcIW3AsG1qhtadoukKhD1A7OuVUb4aqF1a5axpuD\r\n0t9B+St0/0d29+lSv0/9I4KQNtUn4UV5Wy149Qy3hkIM2ONJFdV+jX6XALkP\r\nJOynu5elmcw3bow6QDvcOQSiffI98Ezj8GK4i0UwHQuylobii/NmTaCqHSVn\r\nWLAilv/0hU3guLW7uwEVkwMKAKr/EnU4qH8bll3xlG5Bip6GqnjAtNaXsL58\r\nA57HMqBa7NI6UisobgPmyzgfsWwRn+daDanhrn2+LDvc9U+wm1/uboLgKG/3\r\nZw+DSOGIHNeWF3eolLRwMyuUQpxfsFhjfot4IyoGTEqkAo9X18q1R2iEC+CU\r\nSdA8nHCWWAa/XPuTZ/UrehPGcoqwUBIo5t9LzGlBxgFA5FOrjwY3H5dzv7cm\r\n+9iSf7p5jdjdj0+NrpdNbHArIfxA7cdSLMs=\r\n=MR52\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.21", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-use-size": "0.1.2-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-previous": "0.1.2-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6a984958c8c55964177d84eb2755b5969dbc5f90", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.21.tgz", "fileCount": 8, "integrity": "sha512-ptMeSocLPC2CqO2kQmcnrsH9FgcXxhRPYzmNjkiuvg3kPcub6uh3H2XcbM2vnJLe0pafpsVT8da7i6o+6xRx9g==", "signatures": [{"sig": "MEQCIH11Ch/atJXTlE6aVHXLI79Qfi4GJAXdG4Oz/nrZdam7AiBDsI9KwxE2vzgQuW3DNYRa2DNzWRTAgR92Sl66e4nSFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4HxAAgzoNBV/9mYQJRKyLanJLpk/y1xOQRfJ6yf9U75nu3ZDha5IB\r\nFZuMt9NRGUe7qApA/pS6MnxM+uO7IzUjS+AxDPPRRhgsJMP1LTIVbrQSyTAY\r\n6UKHV/yN+V6Td0st7mz8+qP4gsxthuWfymNWYI9SLi6sCfJyZ+55VXBR++Ma\r\n2aPwuPusdFDrc1t4ELjDGs4NGAx5BEY9tEgWFPt9ktjQMLi7fYW2WP7jQcO5\r\nMfdIn6TfYoXPQMZN2OFECEEkATqxnLBHVje70k5nFS8bO8/CzmOlZqH0MQO/\r\n4VzrcfuiHzYPVdl690p0YvO8GH+HmbXcVXScfHtW/2uYGW7k7J4HqPvEURV0\r\nuhFMp0P78wbxDEoA1YZT4A+/Zm3XyhfBinmH1BXfIRX6UNsCH8JIldcxS01l\r\nypbvlvx1ZavdGIZnaUuZECoIVsKMD9iQxJaF3qgIcKxXcWExeH41tXaqwR+a\r\noutlnki8r3a1cDEwZ+NNYZDgjSEztmLOGNkt757ZsiOdFiE92KWEHX6y+3M5\r\nzOcRnQWHA2tnu9rXRlS5MzwOGcSl8UR1HDcEnY8lfQWozZwxeBVMcL2T46HU\r\nj3L+Dq7Tr3PrMggIVxCoso479kdk6fX5v9K1mH+UOgYcVj/YO8AyfsX32D5v\r\nNODLkP6SI5JQjtZV78AV3rhyKUx4hfEUyRo=\r\n=sdHU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.22", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-use-size": "0.1.2-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-previous": "0.1.2-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5bfc2ade78ee97b982bd1b125939e21865cc7ad1", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.22.tgz", "fileCount": 8, "integrity": "sha512-iHSRrwI2DIuEaYZMfqEM65uctPVPl91Y5WrwckhRUrJjvMBneEy0YB4uTuQXx2/BjwEHj9XZea2WT009rh7Izg==", "signatures": [{"sig": "MEYCIQChQ/143HWIc6O+SUNHT9b3Cj0CCluONoKerBjbM9DbLwIhANUoPPsxWptcmlFXebQhyB2DD7l/raJg+Uf1dPMFM4DD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+aACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcJw/+PjreFrTSLOjYfk0Jn5bS/ZmtDUiDEJMLiK4mv26LSOA8JurC\r\nVPBam2pwsvPheeQ/e7iswpA7EketialQRtyAGdv/fkEIg2O5Qp329lU3n4NB\r\nix9r7fRJ6A5A7njlUP4Urqf/oGh7nJjQP8bb35pD6TZqWeKQlIS6+XVgyCgc\r\nOpwzvo/JzoHj+GDC/P7BVm1mOaXjyeUdoCNorLnuq/yl0T0Y3LQ2en0acSF8\r\nhmCJ2sYJns5XsaB0jrMiUu6ewZ8YPPfi7i2JurfK8b0m8eS+6MCjeENvBLyT\r\nBe+WaSPPemeljtgMniEiqxtigz58TcO9QK/umBQL2Xev6YKMn/MAxK+z5COD\r\nYwPE0QsQcc2tgnxLdovmeFCSj1Gd4wM/e0+dEIbe0gi6ENYT2ayL12N7J2RE\r\nLkgW/4lnAz60JlD3NJl0lLxlcXF8HFo+UTdB4chDWQ4cfFCj2bO6+6XDq7nh\r\nW5PWc59VaPfSJEbo7BbcvH8A7lDAyF//qJJekPcbBPRvlJwABp2Z65p68633\r\nriSv1vYyxcNN3+oY8JCyvZkoLfmTPDdpnXN9FcpSN8mi5Jn6G2mGKpx25tU7\r\nY02/1Ne9Hi3wZOPLX1dAI+ok5AYsdV4/u7hMibT/PNFfkz11uM4YFF41wtbv\r\nCWyvhxVWWSXicbF/LqyRzfji1nXmClSdffo=\r\n=45DX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.23", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-use-size": "0.1.2-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-previous": "0.1.2-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3d5f8cedc9a042fdea162e55ce52ed0fb9f0f06b", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.23.tgz", "fileCount": 8, "integrity": "sha512-vEgr6AJgohyZK/IaSpn3KUHbgzhi6PckzZI7VsoMybrBKFOlkRqUgPT0GapNFKRy83Gp0gDwf0QzggU/fh3QUQ==", "signatures": [{"sig": "MEYCIQCJSFsk45nZEATDg6pTOMMgckpqEI+1oNuhBLDLOKvwAQIhALUo3rDrBWoJaqOb5q98ZIDqp8V7Tk16HSa2+yUvoBMt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfihAAn84zgtnb1ocyQWAP8iKTC0+IVtQzVJwWYoSZA0ai+4LwK6ej\r\nyq0TqgVIhnF1L7wFRdC1P3p4DS5axWvwjYYi+joUfwlqbFFw9MHwpTRIgtRi\r\nkkFMDyJpL5jwp3se7Cq02MLFiUmoGqB/dhf2LwDRdk7BUnn+FgizIJ5jbuUP\r\nLHAHu3wZHNwSHzlbHRZoiXgRZWFsWnay9CdlqF6l9ooezrCtljrR1B2FUnTC\r\n0UQTRgMv7jBqPF29hiSMMXSwciVFlv3BSUuh9I9YyU3ViYP9p9EiPSlPmmFw\r\no8UsOE/FbieaVKq5vHA5HyVTs0snhUJsrYKs5ujgNmdbjP75WrOVQQyvWr42\r\nFn+U847JcvernZNIImVigkm8XuGkqCbOu55bHsYuKsQFi6bAZeGp8x9bEo/x\r\n+e0djxDTrqTZuExddbRBhuuUl3kMlM4LmruwY88cdbyTV8rCt+UHwoiXuqPn\r\n77w0cY9Topcl5Gdh7kVP0C5A9BqIlvw+XsnfREpDpvmYrMmTbeJ3pgUftqpo\r\nnCjRUlL+4PRomnI2WDYqOzckwS0K9c8+RadJKya5ppe1aOfSyKURUc9xBFJp\r\n2A6ZRY23e4A7/4WxTmCM+446tGe4m56uOLf26DaB2ofOjOZn8RcbOcbN29bS\r\nPrbkuTFjn96UGtzWED2x4YdYX2pHhZnWABo=\r\n=ZKR4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.24": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.24", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-use-size": "0.1.2-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-previous": "0.1.2-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e0ce99c9ac852586a96c82383ebe66bf79946cf7", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.24.tgz", "fileCount": 8, "integrity": "sha512-duYD8N5qBUsn/psUfZ9EAPamiQ2ZiXJcK6hLb219dBysJ90U4vW7C8saaQkXKEIk0hA6SST1EdGH11BvCW0kcw==", "signatures": [{"sig": "MEQCICWNE++SPI5uC3jnpm8+Nczu6L8rDrpocczIEYOyDwWRAiBO0sq3CDMdMiZ8rLs4koE8K8IiRBPkZGn403CO2pDcRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLh4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqmpQ//fHxLtYzUO0HZjgdPv3F5lFONf0fXolOI8YzwJmKluAcfWRlX\r\ne5d7mjFSuO8mJahjiTlc4J1ge5sHgABfo2cBUmxHsjEd+86XH0jzUc9tlJhE\r\na1x62sN8re1Ss1K2XlvaOngB4QiC5Q9l9IOhgUXFFrw1n6ZkwV/e2AZuyrzk\r\n1VW0Z7EsBTByVr6XyAb8pMEglEB17amEFl6FGYZ2FxN5MPcgKj2gvvNfL6mn\r\nEbvKCpfGnuIYXFT7O+6ohRt1lnEIh2ECWv3vzbHyanNkN5SQLx5ieoq2rEqS\r\n85gI4b/2GEW5OuJ+ZaxdSZ1e+W5U9c5d6wdmwFZLVV4Opd6a9UE+1VhZMujL\r\n8qftzwG/DEQNDO3EWBXnRQvXMdEKbFUhy0ickKoYZKdpibE/SA6kpv1NGGfc\r\nQuPzACI0d5kBPjF9c2iqeVa/7m9x2fiDjJy2fZC53igdBJt9lx6WaL7egIfX\r\nuEHdy+oMw8agxfRJOwhxPoDJgdoXN5w+GqNU9ETzGXXHkYrFBibTLVIKVh5e\r\ndjwcGpWyDeHrdXwBwHA+hodqL1kyDfvgSWXV9Ep8UutLpffIyaEDJ6tFQzOd\r\n9dA1d3tNXszY1oA9tYSShScfwG25F+i9O5iVD7ZrGaLdzis4C6dA6e1x+lUD\r\nTg3tutkfirLEzwg80lSBQ9XQi0ou+22BGjQ=\r\n=YDik\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.25": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.25", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-use-size": "0.1.2-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-previous": "0.1.2-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dc0e3dc6331413279cdbce65e7a53e9016a5f59b", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.25.tgz", "fileCount": 8, "integrity": "sha512-YrvFdnAqRyiEtWRDI82nPDOycndUo+xjYngsnZKRxwTugcX8F7uVUR4y43siSIJFDwz/F0g9WwDBaRCOPYleug==", "signatures": [{"sig": "MEQCIERNsu57cvMe2qGPP80q5z0uLOdPv1Wjfr3n1xUBoXG4AiBa7rQXAwBSEJChHEaw27ppMyZfXwcJq+Alma5pkDSbDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj4pACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoU/hAAm+wHiczn+28Y+dITcnr+u7gIHP9Sp7d87JSEsPhI847jnIMh\r\nqMQBITmsrpR9MIg+q5FMrJMvlHq245t8D9NpawX8QxtyvCyLqo3ymsD91kDO\r\ngbpZEDBcXyM8AhJrGsd3rgtc7rwnMGy5gRXCKrD5+mCfBu4VtElUnON3LWcv\r\n1YfEsAnvFpYysxKj4wcXD2dsLWWdNHvnTKaByydgNnwvD+GHok2QXI9FuRa6\r\nA3LtgGhvuhg95paU/mkxQIvHJ8BRbfPtNfYHc/KJeMeynvYW5PnEToX8Raym\r\nmaZXogDBAwnBYtpovHe0dm/s3ftMtF43vC+VMJBJ0MJd+YS/pGEX6HrIOEB5\r\nlap38XjafFPgn4Boz6S1pMUz4di+QfkCpKTvDIzom3esBjfxx969BV1IGy7x\r\n4kg1UXCde/0PxDtoIm1GwoVwaqVeq8G8Q6UVzV9vURMGpe0Xal/NVawALqMo\r\nP+ph1y4xhJC/Pf0aW0pDQY6vyZV8cnzP64ceBecB63GHNVmVRKjVXq036g3n\r\nF/4YD4pb6Cs+LBV1m36ZPBYrCELXOjJnNzLugOaX4nhCnwnQFC9gnpEUn4SW\r\nFiA0gv+cKvZ6sofi5gNVoHmnFJ/t4f0CYdgHp9A4GzXusmpIbqxbPBJsjkem\r\nZDYZBYL2cKTVj3q4tBCodp5/jfc8zt1ZCsY=\r\n=FR0V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.26": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.26", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-use-size": "0.1.2-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-previous": "0.1.2-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "310c3b4a32b1b7a7e9977299704c24fd9ff40f82", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.26.tgz", "fileCount": 8, "integrity": "sha512-IHEZVzRKH/TE+Yh1CThR3W8uIm4S+58Vi6OZog1xcZ8UL8/MOGK1Cj62S0k9HS/VzbbooyyoYRKOVZGxTWZBTA==", "signatures": [{"sig": "MEQCICp1QVjGp3uFrGAIm9H7KnCl01d4zxSVv1vBykjFH2ZyAiAC1n8AqTZdk8peklARp8799cI66Thz0+/+9bY7D/Ccsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrO2hAAnm5dpMrHIwgjq97naOejwkJbccBcDiZhZ/zq1trVD02Nn4Zp\r\ngKAuuX/uznIMUJA/QZbacFF8k77q2BpMlGcmofoHqD2juwyW9hIy9OKX//xS\r\nAkbkoWHndPW6oVgXiElf3lInIjrBCn0pcaWc5tjr7ujQIzW5wLrKscNWbsfL\r\nw6pgxB9dMJn2Vm04QTzk31l6xTkDnXVMAbxdDX3khxjdfAdxrGgi5Sh7g1gu\r\n3kyByAl2HaxAnnqCyQNW15KeNBjfAE1y1cYiLhpGGhstnNbJdxPWD47HBq0z\r\n4ViW/jMXPIVt1A0PR0WBWR/1PW6MIZLTUBMPDFvWXkVpugRR433V/Sg4Xc9L\r\nX3JVkKiz6TOD33CVQl4zzjHVHr99Lodmiei+9OQTGWAemulsvWVYKt/psHQQ\r\nEYwU1OCR4i9OWKEBHipTPS+hjkPAyZisqf87Rb8Z3lIpjCXj21LIMTiI12XI\r\nlGM31tSYAkVV9ct9lQI6EAbl+YXNaJE0Fl+jPl4xtMMPHDvXDMWtn+uOpYB0\r\nwSAj+fmtQcZnVe0Hu1GJEMxBwTKCyCe43myPlgrqLRRbJ//lFZ+n5GJR+CQ/\r\nP9KXIT47iqoINvaaLJ5275M9+0t+AwHQIR5OqSavfdi4OOvZGy5+qW/o4Tne\r\n7MifT4lDSmU0PiSfklaxaBieq8PZ0iM5rYo=\r\n=w7gz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.27": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.27", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-use-size": "0.1.2-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-previous": "0.1.2-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bb5de58e7b16ff13e4104cb89e793c5dc7701855", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.27.tgz", "fileCount": 8, "integrity": "sha512-yWBWM+VEBXjqfVYF4gqTsNusflLYbRoMCSz9dw/mkQeIFYWpavZKPTXD+ZGT08/+uXGRjXu1ZuMrKQMO7u4Mjg==", "signatures": [{"sig": "MEQCIAJBrWOr56vjjcZqcViTuIdx+0JWVckMzwQjO0Q4KsiYAiA9cscPkJaq4XZmiUL4F5mCcx3BOH87v23WkueRDNA6BQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ2bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr4Ww/+IGH3BiL2VDpdB469sI6akXEY+kBBWK3Vi8rFyTQ6ZfFotmxq\r\nm9hkA69wqonNcPmczLLJtzUn5BWPYym4szqz+UOvIVWmUjJ6KMZ3x18GB4kR\r\n8+0vWk16cYFZAukuXOWZRqwzXdIyPRLLtQAh/IwGwGevWAkxxWR3sYtzidga\r\nm5EvDStcsHnzYkpAnTcj/ABIW6JogLxrr2knuFpth8fF5SblVF9736Gx7KBF\r\nj+n2JY6gYZ7YstWVSPtIdp6iUjG6Zn00h4ExK1gu4ZPwsbT3mkJJTb56Jw2D\r\noTIu7iyzGdjqIufpC47bFDn98btKGPi5xH7Fpo7jR9tHjF7k+KeFPM+LXxJl\r\ngqTVbNbFdybD6nBC3/m/K5o0Me2wW8bv0NY8resri1VVILP8mNHz02y4Hrht\r\nNxXnzVW5lBsoE5wrYGbxfOkYbeT3zKVmJrA9trfU2rKknVzTH+bKh8Fh8Hdz\r\nnz/rqso+uUeoKS12oZCKS5/y0PqQzbz/HALO/+0fnQDuVFkr5roWqJ7AkFyQ\r\noBtDNyYQvPB40ybWhPWzYhuPTmRZMy4tepWTLvDS6FaZSwqDgWJGMJjF0o8a\r\niE27yXVwKxT73JMT2FwsaJiEvtkIKxUCGuvQqdo4T19gpW/h/x4dMrdvnHu/\r\nkz1gw16Llc7KQrWAId4alpSRF3grXtyrs7I=\r\n=wHAE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.28": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.28", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-use-size": "0.1.2-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-previous": "0.1.2-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "356b74dabc91936e1a191bc0b3904b049473aa11", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.28.tgz", "fileCount": 8, "integrity": "sha512-knUJHYKeLhbGLZ44W/K0zq2YwN/hwYiF31viN6ngC1Rcz5YJCj6eGUbPwy7q7Ppu0eosL6pC+tZXGuuwS9/40A==", "signatures": [{"sig": "MEUCIQDL0CWNkKzWAFpkJK05rnP+rTKc0EytV1NXeBGob8kJbwIgDAUCK5t7fAFJ0eN5alwCnlq/yX63B9+MHZGXP97wEGo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildN3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopeA//YGCFtXzQrvM4LVmC/bz716UoG89qE/RAQIKt3Cxao+pUP9G4\r\nyX+9zhIuyJRIwWwSxUJv7xk7Qmz4eN66K63sjw0B0qBR0F3XmG66Jxf5dr0G\r\ndNtCBPhvL0WpMKGADD+MwR0KSayhoEBptVnTz1oRl6n5mraC4Ly8WXOIWoyb\r\ncq8AUI5yjU6z7DtpEatgerRlQU4GIYm07TfzCdQdwNfgedBhExqHlgi8AKZf\r\nlzPYI4L4VQSHM1GYCHx9iEhpsP0hNUkeoln+cuihbktnkjWsAkWUQdHYBpB1\r\n99por5p0N895ldOI70vyMLqJ9LuRyzH170kJdJWGGTUj86BOpSd3hfrJFDyN\r\n42hCxrxaCeKOHMnGP+9tRp2b7mUZh1rQkkQmv8k7y4q4XEuKK9d7rrJLFY/0\r\nPUQpum7WoyzGC+m1xTfRh/yB/noZO41Dq6Ko5ur/EvhPbDV6MrmHutJmWdwT\r\nePIbJIEVsUcMouyt3+Wp8mTf0RCHlJo/1Z4OjgKz19yVO5fjr2NML4tKbtTm\r\nqPpNMDwB6nGKf31aXcX5NWEXKO/izzeEOgAqL+yTqaicxmDzN/ihxVV8Ys/4\r\n44RFJ15l4ObuICaJXNWqVaG6PgYLsa1kQ/CYa7Y+ONPNK1Q99PLNjLgxJ90R\r\n8XTR0Ixi4ZNjZVIrbbdmMPCJMSafeoKRBYc=\r\n=6HYR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.29": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.29", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-use-size": "0.1.2-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-previous": "0.1.2-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2adab015abbc55d869db8d1385622f224bfeaddf", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.29.tgz", "fileCount": 8, "integrity": "sha512-wVbpJc9ONH4smIz1RpL3NaaXpJSmQJX0nfsPl+/P+OnpPVLabtfjhTrxOL2278gtzfrIMzOL7sD5CbGzZhN87w==", "signatures": [{"sig": "MEQCIGvxztCOlANY2hWrU/ML+8KsCbxQ2rgOJG9hd9vs8cZpAiBdSqJTzoSjIae0NKEk0+wWPpyVEtPc0vdJawOV9bh1jQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlzQ/8CZ2kPIsD6qCFRJ5S3wA2oOsJbXH5hHcl66skRfX4gc4s3koh\r\nVJOgBz8ucOUeJ99nDTrAjywducgIuFs1LjyLGqr3omD31gdkLs9JV7ptB3nv\r\nVjabounWQA/ikYFX/J5KoQzDQ6ui4m+udaDX7nz1mFVQ9bltcQ+rBGk0pG/9\r\npLk4UAlB8aW+SJiyBp8/QgtCINPLD651M/EHMXIMnirpm1kiTZUFxY57m+Bs\r\nPzPOMJySbTn5tosPLUuKUNfs+mvCOsEvVVs8sU/QLv/BBORhbEe75Lz0MxJ+\r\n6rYuIMWS/0VLXu3QJ2HzzO+bLVPctvpaDQKHVXnmTwErChfeBciWjct2+EsI\r\nPfhoIbW9RN0yGCyGAgNed1v6tD+Y5fV2IFLvwD/wrcYkVRLSxh+rsSmMJ7jE\r\nbia1tgobeNyR10f/dhOgSx+u331v5iTHfU+Z1IKww4AvyElK0K2Ive0/kHZw\r\nXuGj/DUzDZGHMdSzy760NYyvAXa2wanZD8vhZfSJ8oUpAFbuVwHgqetAGDlV\r\nLwAdLPZf8TiyRIBUtnRqgL6zEZM2zhTVXYQsOwJMmlXRzu6F+rNTZRHJdGwN\r\npdh2jUvmpTiOuSOO8BIREASrZ0FQjQ/U6thqhw94OHXpQJGbeuaxU5UIOdmq\r\n1s1sHXWXl1d74aeIDNtgPlc+LwxaSYcpGHM=\r\n=8zyS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.30": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.30", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-use-size": "0.1.2-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-previous": "0.1.2-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6ad942690b8378a025ebbc0d05b08d3afa8f6e84", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.30.tgz", "fileCount": 8, "integrity": "sha512-LOGT8NtJ7r1BUcIvtFB1JH+NJ1fF5nB/dHWgnHlnh3bkBFjvz1fd3e2m2GDRCGZgI7+8voyJ49Nz4SembQv6Gg==", "signatures": [{"sig": "MEUCIQCNEbzQs/+xWzhGAQSdqrE3rz0GcuUip0SVFHoJJjdXlgIgWVTZDNzyVNP1x5oDvwI7wTrnhVL8SMQ1WCyoOrRovF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcvRAAkBZdeuDcAOl6ok65jCXsMTnK75SZbtLz7t+wSOsTOjb3AVP8\r\n0JbrBV5WFja7poNypGu2u1oVKZYDAeYVfCUZUblg61aNwMgebsSuHc1Dw4y/\r\n4gN7jaMCtttIuUgR2TFE7hmx/0vviYgoXm+Mi2k33z3sI2yts+eRG0mQOOJU\r\nsiqtzZjJg9nx6OElcPFl5HHrB90sFXYK+D+HErIs1QCrvmHpuonrLDMKcVMo\r\ndGKn3CmM/cyLbIxRGWerIfKMjaUWg3Y8U/eTaxNzwXDGASYCP6HsGf/crHNg\r\nB+17LlbhJSqd0pRead00MXngIy/y/4HBwf7Gj84tcpdhSVStjCdljLKC/Nct\r\nZP1APWn1lVqdwcu6HEv5LUp6I7mXjAHTxf3mKn2VPSxVyHqbY/TWUt+UIbk7\r\nuCSdwxSPxbOSk2wERT2bfr0/hDAuyEBBI+K8FNTUA1OFo8oL12A7DsOLORTO\r\nuCJa1ROyEOg31WpzUccYLo9/6GdEZ6nGwF1VQt8UMILmPleKjPMK+JTdEpxK\r\n9IrFD1ZOBMTrBBOqNwkc8sp950cKaP3Adq2DJ3VFDplWd73P2YEV+1uRXDl2\r\nGU9r4vXcIixBPZFrzM4c4HW+QAA4zUOq6eNjeKIuQbxzpy6NnGM+w5udvHVH\r\nfisQaxHEalAyPFVh13Z9VdqhE0lQCoBijbg=\r\n=E4n6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.31": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.31", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-use-size": "0.1.2-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-previous": "0.1.2-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3cf32a7012348c8084b13346089c2c964c1b8505", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.31.tgz", "fileCount": 8, "integrity": "sha512-hIM+j0dGFdkMFNRLRhNhDJygF6/XOWnf7cdWMVTjwNa9GxdK3TIEMzNw3r2yfvaQKzHe0HBcWHPwn6ZnDvxdzw==", "signatures": [{"sig": "MEQCIA2q42I4ZaPAIi1mpHiUH3vwUsNFgzpyKVU1pTx6lc5VAiAwUj9SB4+47W3ssNR/ULVOemjHdWs2sDc8uTGENKJpLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3YEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMwxAAkCTqnpYczs2HwY03gWY8FxZEr67N4WJXLyo4hdjQfe+bLOCn\r\nbu3o2wI6BArGoUf7nvEfH8EL6UntA9iZyLzLeEThdQ1DCik968Kv9ONOyBwY\r\nSUE9z3jGdC3qCMonsNExfokuArsDFM4ETZsZHTxNp660o1Y1ooTtaNtv86yM\r\nYGJxqNOo1u0t9uxDNO3l4SP3E+SQtLEGZHlEmdIQkBNP8BRgfZShLSjgB3HJ\r\nCrOJ//gxLYYg7c5JxTA972VMGl0sw1EnAgvd/Q1H2HMMYTFiKerzGBF9T9Fi\r\nj1c8JX0HVMBG4Ku5zHO3wY6KBDEL8zaVvpydLDp1R6UBGbXX4NBB9f9wrO6q\r\nSsICVnpnwzEzMyC8TRZZFe0U6rtaj8Lpqwk/5HU/xq7HTYvwHF/zyFkQXfBz\r\nFJIv+/2A8ptHwFFDtZ7jkHsUZc6C1G9OST7PahKicaubn5gGYljhRZBkYed1\r\nJwKBwEXYJDzwfzGW+Aw1s/+y02YmbtI4VG78qAHWlrfO0AsPrLHwcCRDkXGi\r\nJOoP0KWHYPY+8Mw9AJ8vXoUR8KFKsNCKcvXwQNKFs/ORkRDd9DgSYDzt4UYF\r\nZLKqL8bGv97Nl42xba0MrekMMbURTESNe+7taUg19aURW4G819QPfr18I+Uk\r\nAe437rzzfUuRtoH6Qydm2nchI5EesR28jiM=\r\n=DJB8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.32": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.32", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-use-size": "0.1.2-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-previous": "0.1.2-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2ed0f59d9599232eeff30b67c952f0f33b3162cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.32.tgz", "fileCount": 8, "integrity": "sha512-l/UXeW2dwaDumEkOXVGzvBvCC1rH6jdyU83hP6+6ZgCuzgt57RnWTgOXy542TrAqtMrOmOESPrvTTnXtTObW7A==", "signatures": [{"sig": "MEUCICvYNYL+e/DTi2mPOSKfyD3Tm25FYUibruxuJNr1v+ePAiEAlaDkR1M8QxHI+XG/9EAXuquE68gNUXCGrTV4nFuBpD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniSGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsVQ//cyHef9oQ6bK4iKNgejH9shFj1wRF0LTOpLzX5J3JN9kYdOHV\r\ntiYD6ENK+JzL7rmZEunqdoA0kLi2muSJ8z6Ue27bagqHgLlBNzJLL49o7Rjc\r\nDq6C0OA4GH5JA7uSn8o+8ZHREw2oV8Ple8Tf14Qyyz/AugPI8udhLzsE8nOH\r\nF8wPydf7oJ4+Z62/cu6GJY8IOjYxIGo+HmRgJtVTVneXFsnDm2m2PQECceiC\r\nQlG4qOHWlUikwrZ86cK0qOdgO8S896OiUFSoB5cPxWSqsZtyMKv5FXtP0hWL\r\nFJWCAVTuBmcECMYTmdJVwOXFhzsZdxWG5ZM9k0lPHpPS6loATyQ9C+vlGKwi\r\nlVy2A90b+ICGuRcryxMArMQWp8r1f3PykVwzYiaPgFEn9HPOaWgxGTjLqHpC\r\nQf/oXpLDGdtBGlKGP6yjZ+4jb9tTPCxkQvbSrTdKCmdF1dhQrvCDFUg4bSOq\r\n/XkrK2gXT/2T4AvOlRrRm1GvRNvZ/OChF00Uj9oGOZ1EzfPHeMYUkszxkyFa\r\n+E8fpJ3VAweeRN7gSrsrGmSfdwxtun1Tvw9WNwbk3js03oIoQdOPxTb3BRN3\r\nX8LIzr6cp/tfMGbtLQNY6E5hyNvzlB7kiZoJg7KhyEmZiv35ZrfafgESGbTb\r\npwGj3iJ+RX/4v3r6qJwkcNF5fj491SdRg/A=\r\n=wQV8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.33": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.33", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-use-size": "0.1.2-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-previous": "0.1.2-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c1addeb682b22d04ba9eb9d8670c6f6375370420", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.33.tgz", "fileCount": 8, "integrity": "sha512-CzNv7npaD9A8SQe7oM0IiJy5hRK2G+Wk4l/QtmAw4jpPn8np3yWk5hnmH4TOTIlxu8ifxyCt6oQwx0zOcznXCA==", "signatures": [{"sig": "MEUCIQC77IWcluXJ/dPbbxAlSWTaHEU/lDkhu5sLEZj/dexmMQIgZF2fk0wXcqZM+WgYfGF9dgxXXHDbrxG8zeJYMxpSyuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1sg/+JFcbG6iUgWWcDOghNNe3YEOVDjnt6JOBNBQxXrxcvPLsWuXd\r\nF1OcCuLZmfSabQiyEZLCcAleENYnKX9Ddj2w9LMDRmxWnlwH9MLgyfLaVDMD\r\ncTDpltEp4AU9OJvKJ59/7euIYEMHrJWejHVpEPAgY4GaZcHA2moCmomdkiJi\r\nyKklVe4akD4/Kt4hYb1Lc5N4tQwhW0PeD8E54eDEcTlvx13aTQ6U6r1UHp/8\r\nHt3le8Ooi+9p52KHKOLQ5bpo2fJ7DO6qzlf01RfkLdpb8OVAvcFu34ENaq1/\r\nDdRbPBALy0pXqYGw7iYFs7pUdJ/oRR2+yuNG4c+h2LiwBn2l+494x2U/AbPN\r\nc2IWzJZxt4hPXqwhcGKkrgOJ24JvRnGwxZYca+VWwsoAnUzirTMHuzD0AqZS\r\nFZbVTQjPLn0djw8VwIt6p/q8/imQl63j4vc9MXBpQdUCFn+anerVLPpBnsyD\r\nB8JcAYU0CUJ6/TbNpGNuplomivsNrdUXFWSbwKLDvLTFA1RA6BG1EC3gCU3B\r\n/n4KM3JUOQzCC3YV19h7clcu5ycHbE0otxurL82fQ1wXFPpBCnlNr56wMt7f\r\n7q/5ZQ0H630ozystJmEWyNxFdN5R+1VfXj3wf4o/ua1le6bcY0NtdU5rmdfA\r\nUkUsa6fAQCvvAfNHJCcPtfVWpTsfhEps+bI=\r\n=0CV+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.34": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.34", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-use-size": "0.1.2-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-previous": "0.1.2-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "407c59597e7ba085dfa32348538ffb5f72a2185c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.34.tgz", "fileCount": 8, "integrity": "sha512-azR48yJNrgcdHfrxuB0I8ldFvge4WhMQJNFIM3qeDVPxzNILrLo3Yl1EzP6rONRTmQh5lTztA48BnxQF2tiz+A==", "signatures": [{"sig": "MEUCIQDJrJb+Hu7bg+5T7RAckASaAHlOsGnQxfHbGOilU0YNHAIgE6sHlNoMouNmfXOa1L3ZgiE4Y2eCd9UsHQ+jnutqlSk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSTQ/9GSAoQ0tRxDoeQXY7gX2yIbxSeVPXNixxOnF2bHjwk0GpGMDq\r\n0yGpqCx5kdFeIcUUt8eBHPafZPOdQpB97rk/R3VQMzjMJ7ua40dnQwniz9If\r\nPPPTgpeQbxSpZuULWek9Eb0eSiGs/pj7SR6MmiqVaUI0TDLng/YuMX5us6Wt\r\nNr1EepmB6grfU+/EgKmWCn9cDQfM/0O+yWuxf9MgQe3JuqZ+VAswbh2vFuxQ\r\nhVkrbXu84lf+JbzBPLFScHV7OF21WkajDmMxpgxhKkCUmiEGisJIiFbI/SQt\r\ns1473KH1w2dZ3nt/DjlcjsuxRB9ljZlzV7JTj/FXmgKx1Pgp3oUh3TxuIdC7\r\n7sXLQFVLXCthX8PHXD+I8E47ofqxp/BIVJACclbbZ8lI1A/ilebJNdKGLvKP\r\nDuP/1DdyOtZl67nJJhbOPBb8gSNRBz+Kkv8ejxMPsqtbZ0jm4FSaI/s/5s3i\r\nfh2mkZfY3ciSHb5oY/yu/bLVo0PX1R8+7iA55+vAK44b0KlM6VTTEACn3cvJ\r\n0tJz1taFOsAnCGRwp/5T5IGdEawPV7SohypJcO9v+dRgop/g6b3/Mbj7ImPx\r\nLHvpWkCjfE6FgVJOy4AtQj653e5coJjtIw6zjy8N56lMclcUjaf7tCaEPlkQ\r\nHbiLo6NZTzgPiBLF8As5W3naK2+eACywREg=\r\n=mvPq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.35": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.35", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-use-size": "0.1.2-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-previous": "0.1.2-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0414d5cdc8f62fee4bc302cdad754bfef8cd95c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.35.tgz", "fileCount": 8, "integrity": "sha512-aeGOfHyl4uId83aKVCut5W5ziIRTxVcxwO8mw12tMrGTpDv5ud4EJ4V4A4gg4EGMrM9BQQfRxfdtji7PfDI9Zg==", "signatures": [{"sig": "MEUCIAsfsY6IKy4kkZbaNvm3z2gdWLGnQ0Mh69IXyGD8yat/AiEAgGRvi/so14adUraqphg8zDRIEu6RUXxzWna98A4YQco=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdexAAkstehE1IxRVYjJtFEI9FQYNBB/fVgwKSYqjHbuF8kgNoD57G\r\n3VBSxXEXTIxvB+VtRncrHYX4rS9u10ply9prq9XardUyrWFO7BrK7QznSfdj\r\nWG45vB5aC6K6CVymaIlzHDzL3Jzbmqd9seSxfoMkrLABEwvkOAXmMBb3Yu5A\r\n8i53+9wleogVq0Wka5nMNjghcH6y9wQ25nJZx1chzZUtIQ8k0gfUqmWuNan1\r\ncj1HNsxWiv558CLhXeGQ2gWdsYeNk1PKiDnK0ml+jzj91K9NQZEIGmjE8dSq\r\nEmwrTyu3V/AufOy0lFgj2fGwDwAPDo6Mi1kY+jhS/OYGLM0lqD/PF0qaoJY7\r\naywrs/V3kiigAhqpbrE6CnEWr8whRvpZKVmLzuXM+yAOptkHL6tys/fn4tFU\r\na70DRUfzpPQ7G2Z0eLrVWj89fKpzAdALkniZkDGvlUfjbJEz27GoSeTH8OLG\r\nfnXC9o4qJYuR3fgOyowM+bN+wu8jR9xCIArISUPmQVGWU3aKg7W0I0g+14gA\r\nwLshPhBUp1ivBHB7JYLo8GNUEmQgsY9SKrFvlzUH4wZtFKcICs9nPNuBJ+9t\r\ngV+tk4rUhhCFccVxQODITBYUWR7K3mN27qCnC/eecPorU7as5SC880h9U8mM\r\nQUBN4TZN18EgK+r/BPqyY87eUGQl0ckdrGE=\r\n=JTUC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.36": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.36", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-use-size": "0.1.2-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-previous": "0.1.2-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e2daf0ccf65f106e2c5502b46e4287637df77d26", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.36.tgz", "fileCount": 8, "integrity": "sha512-2HX90/LP55RAtEqa3w9Xjd1n/qy860bajtUbp7UOkAceXVFluLn1vrM1J5heyFqSmaxq5kk8bxm0+T39Zno8Ag==", "signatures": [{"sig": "MEYCIQCXzvoHFP5DAZ2twWZzixL7xz2NngAA+aunV2PY0ymItQIhAMCKpx6oqpmIS8bz0nTj22YB+443WPvPLO5AYlASDius", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0I8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKcw//dVyE9stSLytLdMEISn7qrTnOhsiY5VIBo9hOuL0PWtvsIgVV\r\niWqC5BPXQt3uI0wPZVDW734YJaRHtbtdio6yJIRyl24zNTFvo+Fg0uSkoqKr\r\nV6zvTFyvWBeYKeIdwhQ8X7gX0jxwDpxPq3wAlLmMaQtNLOzSwhWj29P7HEzX\r\nEJ8F23p8p+1vFpGSxYNzQVxA34C2SVS5Id2hWrF/oxden/ZtO3XRYjcOJpVQ\r\n5bopQZvcATm/flYN+aVAz6cR08BmfIOShjTQFEiEuXOzHIhO98fdoobOOoy8\r\nuXFihkHJk+5DXkwF/7WwOTwoVbcVFrAlgxUC31JNBDmQzzNXvrKXsqd6YzNg\r\nVr/M6dSbMqi123v2HNSRiWEe/rpIM76NONRZrkBuAUj6NvdffXApi2y44GG1\r\nsGlDNJdq4W4LeM8ry7KUIPNQc+6l0fSGwARsN30PlyBY178xnTHSj3w6bfEi\r\nf3DKVmkU4A17WBg/zCGJp2gAQJo0xcOYjfpiSOqrqjsvUqlnrxRrby1Kf6ns\r\ncWwsFP5WBB0dIwulPMUrX0hln2vgq7BakJGvods5uC2ZT8J1t43DO+75mHu+\r\nd7z6gmXMJBsgTJtZr3hNkqmuDeXGuVIMXLDht3Cfpl6lsj3Vjx+q9MsSzcNs\r\nkhwECqtIp3jBlAiGn8NqqoVNAgOY5DUPy/o=\r\n=xDJP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.37": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.37", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-use-size": "0.1.2-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-previous": "0.1.2-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dbce556bd10e9d65c5d79d3a939da5427db16412", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.37.tgz", "fileCount": 8, "integrity": "sha512-sKEnMM9iPFV2Li25EEitE42JQdSW337k1jppJr4CQ1ZgLESifhAhQaTPSgNL7qKBH9bQ5dW3zBgVZyh8/nOprQ==", "signatures": [{"sig": "MEUCIQD6g1HmdBrL4EGcI6I43024oiyxAXSAEzRTHMwPnYTq5gIgGlIEJ6lQihsmLVKLlZNjE8IUeIx/r2Z/LcHuh+jVraU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0oRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpaig/8D+LmkIGkyLhn7MPp20wknacEB/+qaTuUJvoAEzmZe5nLg3Fe\r\nc0PoonCRAHO1lK8aPgBKMbCoSloVlrVQaiwg7+B+TeJ4QUQtz2o83m90SYGw\r\nCiPVP4C7zE29iorhjBqdkLVIeF60BB7B5ADieCJ84nKNJ8PAO7bVYG0coRGg\r\nB2tEmeYD8A42KJG6EJnc75WpXk+ntLup3aioUqvPY79IUZCOXr+1su5YlGAY\r\nOUHybSeCvkDJbcBcdB0XS9EJ/D2vswNw5qO8AbrRQwcepGvhq015jGjUWpir\r\nssEOakPF5e/zjqErieMJ2rQvCv9biUYD/fuJBkxQig7ZKc4H6Vet0tmUKp2R\r\nWhJrK5dDhZfqHaAOK4yf4oIF4TVzqFuqzUZtcy6JDzbYaNh6/Xz7TwxX5rXe\r\ni/OkZpYr5lPYZRM/V/lX6bdRz7BAtf2/PQTcK0rGQsIexy3fSU+wkaZ1Zy9J\r\nFelE/uVy5++7mbC0WwCvzT+tjckWv2e8SGU6xlrpH+TYSog+QekO/tnEZGTK\r\nfJPU0V7ynTN6csADzMlyvMWf4CxVa2+UsCH4QC4FZb3XsexBRWb86dIET7+X\r\n+AA71L/xhNcb/FQluQDgLpQSd8D0mipfgdPllkDvVlIy76uE5497rv2FHLEd\r\npNN9FP2oX6/s35YHuLQm/w98STHYt4PIdPk=\r\n=xkof\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.38": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.38", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-use-size": "0.1.2-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-previous": "0.1.2-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "60ce9f1d320b35e1676bbcdf6779ce0755c9f099", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.38.tgz", "fileCount": 8, "integrity": "sha512-O7wYA9umeuz2N8NAlbsE61vi6DqUPpqeSowDD8zQQascsSP1wYpU/XmtuJgJ1Rml7ZDXvmu59EAdkFpW8WbiVw==", "signatures": [{"sig": "MEUCIGQPHt77vdLr3y9+5vvXQR3G2ty9gU0C2vsO1uTOv2olAiEAzusKpfca4e83BGAHYGtGJWq9p6pHJYp/OxOTDALM0XA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmryiw/+Kk1bL66p87iiZmx29C7zUwIkOwoGuCCNKO0iTMaeSvYA+2gw\r\nknX37OjocoCnKzaygbSb3XasKqS4KxrRm1TeZcY/Ahpoxyu8IdcYFu7OvIja\r\nGUjWTysi8kxJyDTvfj1jHZxJ//8LGtxLfRmGhyuJRSdlr7uPj6lyzdRFvdaa\r\nBt4q2TktSAxkPb2dk8AIlJBRF893cMsmVuP/2pwEPMszmU1r6fTYTbk9v2pO\r\nscNJ6/r0Zm4P3SJAKUgV7joYMy7dEwazfB5ay0mbE541Q4VXwLgR5tFa8Dq3\r\nBuF2vJ/7YNfbB0DRMD7DEv9FgZed65EITubQ8+9BYxdwYTTGafizzZ7tlX2t\r\n1OK2JjxHIpafN9bGdnb1NC+z/I86rWuVnY4JcvxjrQFUJ1SV/O/m5tbKYLTZ\r\n4DhLRCcqUNs8f/cu5DSo7FO+6yIl35pW+R1Zwalwo9T+Xqp+kfznp7N583B9\r\n6RZVRk4aLKjrP4uY0BVb1B8dpO7UMI/G3vn6FlVsf1cAf92JfrLI+9K832J8\r\nSO0LU7RB1TAr/ek5R49Byfd9leqrv/IK9CRBOc++udtmoM8440B2O/282MzQ\r\nAtinoXIV33p6PM9mpOZ+/+oDG3tcdf+9162uy8LpCBTV+FUOpuQ6+zTceM/N\r\nJ26WQzhl9M8VjJc4P+BfBJAGa62ab3cW8YA=\r\n=vF/t\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.39": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.39", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-use-size": "0.1.2-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-previous": "0.1.2-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1c52065738f6db3e9c78748d2e7d00be9f74eb2a", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.39.tgz", "fileCount": 8, "integrity": "sha512-gkyMcnhOtEPYSfCMw4zcm5weh87H0HvlzK8BgCtIEgRJGIR5RLBC/YWAGOhkvApXbKfbUXr1vCE8X2FeoRkKpQ==", "signatures": [{"sig": "MEQCIGhi/7hk4IUhyMzF3yMRlfh1/ov/ccbxwXYjGtkPQlwFAiABsiIziHLl5e29mqzoJZCpe8u4zVc3N2axanRqWeCeZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+KACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/4A//SpNnN2Fj5E/sz7HP82Ly3xQrE3IFTbH5YZZbnA4LSk7EdEUq\r\n7lLgMxlt88K1szUoMfCzkzxZid0X2HNX285bepfyP8ov5exbkzbeB/oMtPeS\r\nXMRInMApGxRqja+mMu7hzEV8a4DbyW4fgXMPEqbXq+rVS440AOxI1nbztDuO\r\n6SSNDTf01TqHz/sKtzkmnfqNe5cQmAJgU7GDYs271TkTCBA68MFcYsPo8nxE\r\nD51XBebiw701mw/la/vghecUc5LsNY9hZdfg8FnyEySxrZZSFx9sB3rbdvjY\r\n5B+7N7IwksTI0BdtchCdafFkrNgNbLZIyYSN04dcHWhfEzECy3z2So5E5yDB\r\nGFvvEoec51HFPl3RHTHwgaz/0f0Q8CwuZTljx4v6qWKMvAYKBgNcg/rnIU96\r\nmkTbG/3ZDgqQ+VOAU08k/QPMXz9BuQsy6F4159QjOLZ2Pn+0lhoiu+qprosR\r\n/W/X6Kh6d3Nh/BRm9UwNARkcbn+xTEc3n99pmtriV+Up4UzvBe/grW0A1SGE\r\npp7W7toYNuqf37CG5Bf89qz5iUAMr40EVpDY0PJp6js8dLFXCpHpPvXBdUGs\r\nh143RqwlGW3O12SGe6zB4/39NfE9AiYRaY47SVEmicsxYoRXakSmcg9tWx8W\r\ngrwLJrxo4+WMlvFxSgn5VXQ53NAJrnyB2FY=\r\n=VivV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.40": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-label": "0.1.6-rc.40", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-use-size": "0.1.2-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-previous": "0.1.2-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9975eace45030be84c54349b1f66dd0e80404e68", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.40.tgz", "fileCount": 8, "integrity": "sha512-NTVH1z7iqqyoQHpH9ez4ErAyPY2OsO6m13G7cpRGFrr7TF8r/1k4kFMTUNp/6Bds2HEa+HBIcDfCFkEb2QIm8g==", "signatures": [{"sig": "MEYCIQDUkH2UcvTKeP/+xQdzL6tg4ONwIjXmRtoQu6Jc4KLGtAIhAKfskslolm/GjlsO6Fbe54sBA4QKIwU/zh+um2WDyBv6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 45651, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq87w/8D+xOVZvqY79JGptvpJV4HN4QtgfcQjPABe9kyJKQrvzW3Prk\r\nNGQ6f5eXROPX/O7dy0a37IBPKFNhO/ISIo78FhlEgjFyfqwI4yf53qEOMESj\r\nr0OUjtDkxH631SQ5EeyLqW6FG5d7G6j5DRWrcnfT2v+Qi/+v+t4mziZskpf0\r\nIDNfph3rUYDK796roJeBcqQyM+KVijXlBJ37KD8y9JAc7uIotV/foL1cwwYG\r\n6bimr7MqK8QD19KTQDzFB+6BhYI2RWxc+m8mKukAQmIM9Yjqao7y5R3tKnTY\r\n9qq3r3aT19u1q2yipbzAbQF8Hfgr0gE0vPqrg9aFelq7alg8Fj5WUnWyihFs\r\nANrt19TiJ+baTq3JOjHePNx4JWal7vN1Cfz+0oEoO3NB7AnwBnBVLTFdUDf2\r\n+Cw7xfsaX2Gvp05D7FyQliBcNoV9ojxRiW2Jx+gcfrIWsL8bflNPOPteQXSb\r\nSirCQ4Q4yStVsMGSXvDFei+vg0VkLv1bLUmM5hiqBkix7b+6svp4RQximpkQ\r\nwcJ/yzk7ueUCUqNhI0tDjqZUp58cZpi9mn4HjYlfyLKYdiBP6qmTh7R0nTQz\r\nDXtrXZK/eGQ0ufc0zci37DE9dyzG1T1ib1vDEpuCBRki0d1DGPkOmhedSoSV\r\nWFajwETrMvnbPA0tUkqUoUwmntJqbRCgypQ=\r\n=G6Zd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.41": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-label": "0.1.6-rc.41", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-use-size": "0.1.2-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-previous": "0.1.2-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "518fd41c5b4f68427dea37b29e86ebb5b72eaa98", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.41.tgz", "fileCount": 8, "integrity": "sha512-54cNyq6zCWQ/km10d0mTXLkRTabIz3tp86YzgQ0Ep2R965asf4x6anXQCCktUFr406Jm5gR2wfzZ2DBIfsjLww==", "signatures": [{"sig": "MEUCIQCAtjRRLCUlmFy3jBk4SrPwoQGuRquwqi2MnQYr3BBY8AIgPLTxt1Xn3x+8Tlj5YiiHjhbT1NVJf+sVQ58TYV9gCik=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSPQ/+MxXkawPDuB6Luxn+o7bodXp/38APfuVf6eRfUimGWja2T889\r\nbUpqMcseZhGYKtcgu5CUHweRhWP+RRWvCS4ZkvoO1ycI2lHbsiV4ba0AdY7D\r\nNXDQ79V4uIkROVrpJAMR9yP2xuFsjQiUGSNBUB0yI0k3oH1Vdy6L9XZ5HQpX\r\nQ4Q583fDbjbbbnRVDu+xfHPboVG5TPbo/0EAoaWoaTmSqFm9u7twYirrmSRE\r\nrErMU05cEa339ra3la3kWJ13HmirAcU0FoXM/FD5gABefUjlG5OZCKw5qU4C\r\nwwXNjx03Hg6wdcgBcq3lp29ltAxxg3P10hEqE+xNGGS1sQt9MXKoe1MZm2Lh\r\nKnHDl6HysiB587Lgz3UhdrGEgXEoJx+URQJQj9w3xGK/pKhZLxllxg6OLS6L\r\nVmHXzSbZQtjlV/6NUN2earbz8SNG+fxTeIK4wo+yJp7LSK5CsvA7TJteg5y1\r\nkqmqIJOdlm9S/Jbdwqems0SxyOYIPd+cLtAWAyD1uwdqbXzNzlBN2TtWMwZ9\r\nLTqPZSDyJbuR18460ns27/2KKJrBavj29fcav0YG5mN6EsKWVH/n/PXHuFmo\r\n1uVApfHbhWrJv34ygM9/qYuYs2jsPGzYvCZuNQhyoQVS1YXkC2/qOE5EK6B5\r\n9TiDS1Sl+z9uyeXnpo9BKeYnjYtXmZ8tpSU=\r\n=Kh93\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.42": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-label": "0.1.6-rc.42", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-use-size": "0.1.2-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-previous": "0.1.2-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2927fade012e2b3637e29c2875627ef47b7f3e3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.42.tgz", "fileCount": 8, "integrity": "sha512-QNx0URXevvy1z88jGrIUdv8Zc4YL0lpappq8CGnYtK84ZYx8JxiukHgaMJa6ORJCwacNFSqZJIWqDveCfRDujg==", "signatures": [{"sig": "MEYCIQDuhnS6/daX+y1Rr9CCNf2Jh3jM+b6MrO+CIerRm3iF1AIhALs7/7VCCaVW4tNgs8wZuWyBeOKDIa0widzP1QDMHkNr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvJA/+Iyds4Q8+VaRWvEApN0KnZWBqRAEDYgCiueZ71JtbXrZQ4pTb\r\n38etrW+kUBaOP7WgmTdbMDiRLKlh77Wh7E7Z85ZqAVDJsngl6+qwNVJv5ay4\r\nojWqbI9SIxZp97W+f+thKyYr1Ml0wBAoaN3Nh/1JKnN5z7z3lm/a5m+BH7T1\r\npqckIRFCFqwvkTMZyuvHt1lgq+pYg3JKh40V8wr47e4c9RHWLurF3BlTm/q4\r\nS+7zWoSggEZBokB41glj7NoP0zkPcuAv6QtkOopSaXphVbdhO45ZIpGv9mZ4\r\nNlumtLv/DTOQaqK9CoMYpmw5qPwQaLLEc+dCpup3qu8g9JS4EpUYLqikWWur\r\nh6vVRpJIyEUaqgxxgzvv2xRIQN5jN/VFcTeTHqRO0RXDB3ehAgL+78sCwYj4\r\n60SDaqZYpMxPYSqt7DUw3rHNb7VLfCW9NemzQX5NWIpasPAf/XhY6n0B74hh\r\neqvEeGI2nrgk/DFC0U1KK7cXfPZdZMnlo2C1YMdZ92LhmfR1529u5V+C8RtO\r\n1RGB32EMAtJ4lvTyvpue1yxmdUJ0ZloGAiJeiOcA1BzlrgklVo1ACBNDMm6W\r\nmHWSTJKTEbAu1Nehxl606ESfEzhacdKw9XxWCUILM4TZEZiHAdnIiHSkmgy5\r\n4tkXf5HNCo49FgnTOz7NXtCyA5a4mpcBODE=\r\n=oNnj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.43": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-label": "0.1.6-rc.43", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-use-size": "0.1.2-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-previous": "0.1.2-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "620e105e2333780f5cbc132815bc3ac3ec01d618", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.43.tgz", "fileCount": 8, "integrity": "sha512-N5YvunDMJY13nKhDMPTpquQSYVDhLmRjf6eiQGr/TE6M40seNggWUVYcbRpghEKqtemlEHs3Y0r5aaqfJ6GNtg==", "signatures": [{"sig": "MEYCIQC3kxvY4I/5YdE0NZokTxqCkQ5JtkSVLp32fmzOb+7HmAIhAP8U8yUG2jhw5x+8tGZHdJO3ODeaCGBTPvBC/kl7Wq0R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvspACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp/2xAAi7MUJn8O8F28qJYWmkF0zpCTG3xT0beEJkToktyPqEA7aj46\r\n3Lw8f09UvmwCDvcdYEE5W11gd5WN6bnpSRpThszan62152/RqxWAqkbZwKmD\r\nNjMWm1Od7a4AWi5onUmLZhjJ98FzKEtp8zi+6uh1zV0AesbPWWSWoH2BKkDA\r\na6jPxt8ESbic7gww4L5MRv3otP0ff9Se/RtMxOqRukf+C3PELtHYPXsyhg8V\r\njePVXe/iEwLpAO5hz1F+crj2xVbP9mItcwTMx+yhKSD4oybmyYbor5tHjwvZ\r\n3rxoQCMQmuK2IQS/enr01rhsMtrIrawrcRAsCyoO9fUVgZUNdHaEjk9q9RBg\r\nC/I28fSBZIb2tunmZS35fhy+w39BuFYCq/Cq32fWpDRGCXygrBrJtkvS6PcC\r\n8ScEqe1qqKCxCQgIub6ZG+t/VQRL2NKQUw2/ygvVPlR64QX+rTw31lhIWax6\r\n1EUQxu2XHyhcpBAtvMK5scLkIxYNgZfIVfjnPMCMpF6/UjLvEzCpE+SAkuhL\r\nbIiAfMO5noG5iKABnL6yl1oGsV2nwafUmJLf15wKMk5YJjQNhlVIwBjYBa4e\r\n9dYIpfF2mn9lNxgDIy2QF8bl2m55GB22mn3EBW8Y2Vvkou3syaGQGdXMWPc7\r\n3AjVGA1qN/JfgsKtA958fz/xXVEUwx5mjXk=\r\n=6c+6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.44": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-label": "0.1.6-rc.44", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-use-size": "0.1.2-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-previous": "0.1.2-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa2addc3dce2f4a2b3f3fbd88c7e57c46d7ab489", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.44.tgz", "fileCount": 8, "integrity": "sha512-1WH0/CgAi4+wLKFQ/YbvciO0wWBWLgPfJ3etIecJBY4Y3t1T4L/kblvRKYD6VTgJ3FyjHygvb00G9JCaXkHRBQ==", "signatures": [{"sig": "MEUCIQCnGBRWYSLwDdYyLt2xgSIkhE/TKJ1CoYvpskpexJVfDwIgQV+lDpUuUHC3pzKItV1fw3bppvxn4j9bhLcfS5Dmn78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XG+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqcyg/+K7upoZhth4wk/4Aru40yWKeF2g5JmtDPV9eYcI58QlS3YfZf\r\n2KCd/NKr7weSnCZsFJRAGPTZbUT4v3nAeApPqpTjo9LQOuAXmSp/kty4TLMy\r\nW9EpiT62MoZ2R3asXygElcUoBBXlXyPw+sfQZ3r+2RGAWC99vG/M1d2MDZLd\r\nX8m0mmHydTNmkC4VukIvk6FV4rgPGTBplPdcQ7U46qvT3FVtCT8sCKXokQeZ\r\nWSAxu8QpMyoKUwJtJUln2eWJE/ZK/PibSwE7AL3PNtqV1VEA1BB5FmWOvQsz\r\nY/sXPS86jOCJY6RklQWKubViTcX4OwEVNukZIMO4fGsmXLBAB3g5UhZPuDnA\r\ndpzIVgCa1vf+L693udcX7RtJsosBbvJ8YgYAITyLUcEhf1sDuk3GprZPCWx8\r\nNt18ZGo548ufRzd3wokslaT+7I2GhUdFEPpC0XfdOpAp91+aRUb7ZdTJPfUS\r\ntzpe4JAN+nkvpDZMAMvI8XafhktF4xioM9bOZokQkYBmonwMRRmwXRvOGl6N\r\nhLqtjCWT33ldQgkbNVgwkVcCsCmrnHcBSoIao8xBWXy284mc18sphjAeWprg\r\nxnS/5RJVjqjGe3iyXSAqd+w7gtrD251fnpSt6UwIIDouLBlNC7dbiDVFTu5G\r\nVeUK0u0V190eT4JZ6GXT6XlpYRVEPWhQT3M=\r\n=/4Ih\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.45": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-label": "0.1.6-rc.45", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-use-size": "0.1.2-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-previous": "0.1.2-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4d092e13343ab4b877a3b2420ed9019f5ca54c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.45.tgz", "fileCount": 8, "integrity": "sha512-1Zda88wTdKW09M1BTUy/WYPRrosCnIF5kUppYJvaVXEvww4ipGbTJuL4Avfi9PFdmj2OgAD/odHAXQQoiV2l2A==", "signatures": [{"sig": "MEUCIQDbFLh8mgaay3qvRlXEZsL5IClshWpPTbuRHsODGQqyIQIgavwmuOjDTJBsojtc/9+0GEYNdthjgaL+sANH9njEcZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2dhAAoSP1WchF0fBUGwYLDAcpBdOMV3yd847tySs2wbCT95t8UBB6\r\nV4wgu7YM+TVtU//F1jcgkakWeD14M3OxTa6YDTso902WqOZPj5UjFHLiSpK1\r\nXhLtNloN4g8YH3TZzHMJKZLcx4vss9zRfQV7f4cGOeTi6JZQ3S4i96hzApMD\r\nRgYZP94g/7C/q3qRiMQgeVsIyENhSKG1fvfJUzDt86KNNi/HCPqpIxql7Vjj\r\n3+OGCjE7+0HWDFU3RvN7IrclUKpGEqDnclhyR5U3eUXzpKjGugqjnT5wI08s\r\nAKSkRYmb6szTY9x0d1+FuLtNOaKHlvhLCN6keuMh5GhsTlGYrJT/6fsqKIbp\r\nfmsWu35veTousScXbG7t1dX1VQtP7oODHUgJOJCgVgCd5qvMgBEa0k/xF1f3\r\nH2OtQx0xm8Den2PbOacx988f1qZ9Pqpeq+g6W10GZ/WT+QWMgwEjvzMk8R1F\r\nNPmstOzsv/Jd80JGMlcqXbLZa9RpGFOV4aUKV4C5qyIjPKAJbJw0wJfW1WFb\r\nvPSBxY0saaUDZ2h7dTYBDxvPi6ZAtU1klPwgHPdOWQ8fePwdCsc3WEl8z6My\r\nRslS6N2azpgL+7jvvCTyx9pBf2XkfQoyV5zSfTUqZVi2Sne+UlAXNzQups3a\r\n0/Bs5X2vljb9M4RZAfiLmYhDT6I4mb/g1AQ=\r\n=lkIV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.46": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-label": "0.1.6-rc.46", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-use-size": "0.1.2-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-previous": "0.1.2-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "afd2929204cff22e65d77ffb5179ae105fc2de7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.46.tgz", "fileCount": 8, "integrity": "sha512-qXm0pfz5EJVe4KN+H47TzM7E6Rn5aw15ySwgPEjaqcfmVtUOubcd9nLlHegM4yqFwXuHgEEuNqYFzcdY1e6fjw==", "signatures": [{"sig": "MEQCIHV3smFHDQEH8ZazRaA9fRSsLWuG9YAdn9ctxl1fQij+AiBQkusytwTBoWlByXwgj290jB6IDc0qyegkS+CA/syFRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi198DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrdHxAAl0/4qkNU+5g1HxD1tIcmDGBdudCyDcM3lPt4O084yj5Y9xv+\r\nQx68DgcLnuviCur8W7aYAv9M1A40Em6BwSt39q+wN82zqq/58ON5QvwOSB6e\r\n1bZO5cqF2MRXtQupCxljPgxwJ4KOyB5eUCP5sQnD2mrC19jgy0OYUsCHQvH8\r\n6bnJndFreL0gnUidSF8XUebpKhGsykOIdE+er24BP+N/p5iMpHUdy0n2bdb0\r\nUR/rnoTsLtDru9eQH4Xs1IA0HllmQlwdCQANkn+rqJ5CubAo+Lps7RsWAIRg\r\n+Re9de3/BSMpF0/hpi1KrlVfUhPSvYQN6/hu4SULXJJC0I3pXz12ZGRAKqt8\r\nw72CdhAOxCv5JtZh6OHvEyMS6JlYqu/+01+7gbTsrbFrQbYM2JNbWGNJh3kL\r\nVseFAZ8n8G9cYIh27EfrxJ7hxP24tve6Axh7sgHeHvzzGSPjqdU4Kb6OlQZX\r\nlH0SiMU0HHMVDYRGMWjLt9POIdiEVPWe7oiNBj2ybp+6M93Ou8m2uv0ZxYHq\r\nrtAlcscXj7IbbYwOsylcdIFm6R0EAY+J68dtZoDDjpvOC8axZNTWtr8epb8g\r\nDcmY28I5n3Weec3/1n8VNR2V0O1ZNxNxha9LQUoP9bWNVeU++lDuYHZ1SDVr\r\nnuk2iPmdYe5WR1QDBG+gG/8Rs67OqQrcXnc=\r\n=gYsw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.47": {"name": "@radix-ui/react-switch", "version": "0.1.6-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-label": "0.1.6-rc.47", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-use-size": "0.1.2-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-previous": "0.1.2-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5483ab3425e55c62e21d8ebd1079bf6bd9c50c10", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.1.6-rc.47.tgz", "fileCount": 8, "integrity": "sha512-JnRHhaqUufkblt8PK1korwP9RgVPwOdwbS2SWjhxkaAb/LfbP+9hNW87KZVgNM9jwVwlxneCCdC75jFOGhPPCA==", "signatures": [{"sig": "MEQCIHXevBr3mzWiv/DrTVMDiHftpIHps+oxsHwRzqXGni3gAiAEjcf9aSt/LKg4kyIt4hDCEDoVlJSEfhsOhAlP0OHMyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44854, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CE7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpeTg/+OQ8TG2dEY9WAMi/ReaR6LTV4KRTlvyLW9fr6Fuzq/4++Yq46\r\nBq2B7e5X7to/Tj/bsM/ZxUeEWqoSNaMR6dAkUw/X6N/ziY8AcS1hp4eK2d/T\r\n611zyeCNo6Msy3OeYuIr6lbcLy1XL63C3vQi+ggPp0pphsSJEEVW0w1rQKJe\r\n3oB9ZxM44d3rE+iOEzBqLtczkTJfolW5fD5Nf78LabCSSRHRQKYxwlPNMpzo\r\nCCn35q6m/HdNMIEqFDzsBf6z4L3mgBAtukTW2S8QP9o/X5MTbCfE6wCJURAf\r\nSjiSh+mLsF+PgCoMKU8GR0QZnpTzuWm1OpXhC7YETtrzNjIOkFj0PyWR70Ce\r\nHak02fDWJ7t/u2Di7opUex4CTjcxYYVFcmi05t8TwY9/8CmOLfnfXL6bXOCg\r\nrACABp/frN3zE8dxuEi25EnWvzxgpzlnE1rBC2ywgjWlwQXeLQC1Ar4BDOhH\r\nwaTeSXJEU2YwD4wP5GrXSyrNiswt7ZE+zKBngA7n7WUMpBEi1sNAN1wrdqyD\r\nrDy+7EIV3BLxc7B3htHgb83DrY0h+4KIgH4PCW4jKNieEBRHFZiOF5K8MChR\r\n4868l8eh2eMCJ+8nJJG4CvFxCSii7fHJveH1JZd5CTQLqBx+ffLAJVAb9YJM\r\nMolgAL+BqF0CuiinPB7Wrk21wtDOGluisWI=\r\n=Bnpe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-switch", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-label": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-use-size": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-previous": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c536ce377595da42e995db45a40d2c7ca5ea5c4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Uylm8Gh4+mHs7rVcjk06zKDY0tkg/ASUp5P57meGNL7iAqFJMxRu4R8EIBoCE87m6tehjY2qNKksQf17cR7UVA==", "signatures": [{"sig": "MEQCIBKK1jbSTbK25PeBrchlXqEY7uzDs4FNlmueKz9TSnOSAiB4BYoeYZMsjjKF5BP17c4zouMj3btzo5u5DFBycMUXSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44846, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpcJw/+IqBL4//e/VFkIdQpfURV6W9YwXPzkiuJE6suw+v02SnUhT7t\r\n1VH186wyaosFupBXWUagBC4dkcqvN0uTC6rJNB62hq34O3x6t9evn5YGctPZ\r\nwDFzHwFJmK0TUwtlFijXb5WzymVmGshJhVrwA5P4j0mOGn9pMFhvG+R+CxPT\r\nLqClpErdxVko5Dz62NywCP0QUlOAfE+ppar8YUqL62Nf9tTjBJwJbM/gNr1+\r\nVLQtC8TR3iAtCSVXAGnNG+Gz0r+bEOvy076RDy6g6RdlJbm65z7kNfmLfT5N\r\nnScxSRIOhnM56NDtotcGpsww+vff8+03zXo6VLF8L8YBHsvWA00n69+48/mQ\r\nxBHPsICekfNS2mBYPOc7NhbUMg7ujYgjd9O7BKFI55gBY/b7+CtczqbkLZi5\r\nGI6g8iNjIm1L6lnwz8BVUFNwinsoOl0xSY1XFguTSERPQqU9/NK+xOMu65Ks\r\na7pA5ueQIvzJU5EjDFdCr10F/UwX4imLyxuzU2BwN+fgNb8iSzXHkuNwI3T4\r\nuCjZZ57C0sd7orQ0ux2rfboyqabKpGgD0FIXNzKMR4nBVGMNX5Hg1Zb9AZzw\r\nJ54Ytl44kPToZ/fC/oGJlnz7sy6JfYBX8JxeAJg6ZHRbj8hjhXzNiu+tqoU6\r\nH8OmrqhOl3+x5m8bzaAuzDV1YuYOkeCiDEQ=\r\n=D117\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-switch", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-label": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f714eb81da7b16dae7f5ab6e774e3194d8f009b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-SydkCiuirxJfRG8259J0RIpOw1p6y4TgJdNYvDNXgkEjCjFzLCvBbgDNQn9SeYXk3mjO/ezMgMFsacnrp4eihw==", "signatures": [{"sig": "MEUCIEawaXxlKYoBI9jBcaBhnogFT7C+Ej/RVln8y5fY7p8vAiEAixRPBMj2JREzA2Hy3PupugOD7eIqln+gXeGjHS8LYpU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSEQ//ff+o3yeDxnAMKPCmOVRpmBHbyhxCFcMz/11q76Ou3fePMxPt\r\nIrFAqOYKvtiz/ulBW2y4GrrOQ27ZJx2B910j/rqp7meXorSid7wsGfiIWfS7\r\nmcNoo1eFXuvgUHn5fOq0goqwargplGj4yvBiSe7+mV2gzRkJUcZrGE3sNt4b\r\n45SEX+/NSg069a59x05OY079EY9rKtAfzPXU3P3uPruC5Sxb6P9nUSaRrbyw\r\nEOv8T+O9QYSotmdkaEr68s/RuGSub84xcwEpQCk0ldDilSSsLgDkRsoO6p3L\r\nKm8CgcfCIJqB1FDK03Tp5XoSF948I/NK59N/798EyDqs3yrS1KfwXEkFbnuU\r\njaOqFj4/3SzqJtmfNkS28wHOxUsfFxzVFzJTw1YGnXovMSdyJqN8sZ3CGB+C\r\ni9lPiexTh485RvC0rsI6vZAVzbEgVnmf/+0nIU7wmHIlCS5tfSPlPcqD4pb3\r\n+z1LDK8EsR3Ihf12Kk4XUPwEXH85jZpPRAQ96MLfGxLBmfhoYJyRu4M1IPKL\r\n31uFUdTdW4s09uZAIgLzlXfaMfAXUQoB2jb8kOGvr7SYk7siQ+reR8/ZR2kB\r\n7VVCFUxUGuI1cuVkVGVUMCZdic6QmknhX84Yby4g99QIFCUZgHKp+IPtMMPp\r\nTZ9RYiPSN0DvtsI6IjN/QqMP0FM0gWJbQkU=\r\n=JzgD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "144627b04bd312866bc1ede24575c4b2a6a32b66", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-F+zWNDgRv83W7vIzk982kJutgq22+dgIkmSvIi0pVEOdX9ONHJgLIvtI4eG2t7Ja+arV/3HSN0n5pCa8pOsm9w==", "signatures": [{"sig": "MEQCIDl4feg+U9E/e6Ly14bZdiQLw5zPGZ4oypYF3/Ai2UjlAiBJjN2q79cYtscEATyNlbLcnnEftxUgkUF57i3Jck5XtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbteACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpvyg/+LbXGrWp4LV1p6dvnbTmGgCM3Ylt/XBNdLSxMrIoUSOiVy72T\r\nrxIg1OZ70GTnw2hpCRYhXighYehEIVzhiCuTUD7SQNO+fdrzm+scwza/WmlV\r\nbbnLMDI+0WoQN0CrqEITWrkDckwooNdhsibdxqaRMoKn+8Z0WiW5VShyTMAa\r\n5lKh7GKXJpQeLiXAQtCCBvHfBzJTYpcSXbvMauVeGq//hM/FUw43r2XqhR8W\r\nLVDPWT5dlD/fbj4/tfF6GnwHjiXRnD8/mD5ttesP1G8tKGEn/oBRK1x2ub+C\r\nAZLY3oheOz9hGz9/iy0bfgzWavxAVNiylhsoXU4SnA4tqSzgkTsnf4sxrr4A\r\nUrxa9F7lDvudBUAagTUOPZIjkBGXKXfEX269DadpIh5BuT425/5QTnSj5sja\r\nOp7LI3YX3IHsvZCqbxVvaifJt8ZO8zfVyVppBhyqMo33TIN9nux3NCaUU/nt\r\n4tN4hI1tz0XtE8LBsPiHtHcbqnHWEC25ez2cmhIdklsK9VahLyC/3GlzBx0n\r\nDWb2k0gPcKwLldGgCfuqLNkJ8uPbONTYDyVtH6LeBV+F9VUZHwWioS5gBnjt\r\nydkGKGTxG+l8p7Tbj6Cac/BRFunI9qubUgH0vaOaPSpuqpf0N27Ipt3ljqNb\r\nXOjs15vtKjkXtZkAjXsuxWy+UP60/UuQGQY=\r\n=Iuaz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "678e808a5ab96f44a6996aad858831f927c201b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-XkeRhTlOtdCuMz5oJ74jF95vrwdG5eGrmbaGtcg6W4MGARxbcaTuUgWiDj/2l5hjO2VanrksumqVGt0kpXC5Eg==", "signatures": [{"sig": "MEUCIQDm2vYyisTumZfVrowkdIqppv5MFAywi/lmmtGhaw1jWQIgaaKUdPXw9XJPFLTdLjK9VLJfe7+xNR8Guw3pBoLVzOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAQhAAohqT3gZDX0GkI2uxeD0Te8voZEsjcGVP4OP7zfAI4QFYgrQs\r\nwzpdR7yeqK6G3ZdQYISt0CzW1HaPp3d6PiwJwy1d8Wm2U5dzenTgFglU7PTd\r\nj0kWUe86cw1BnvqkDPknaVmI4SELUKF5YGoTS2SOlzSQDRkyHh6Nkgs7Jci3\r\nxvo2tT4IJw0hI15/6/9vHRKPSNqcaLPShEoiLR1pXJosg3K0qnC9np/6Dg7U\r\nlRxOMtspWaTTBZeDtF894DcSRGfpElf7F2bmVPggVTmLK3r0eqv5ck9D9hLR\r\ne3UrW4o+1NBUsfQJhKLt+qwgKx8egsc6bsseRMZwrScfhkDZspnmEqSxKkhZ\r\nlnP/17o2RS89NqF84TqSapl71/2Y9nXMyT69aggeHQHUM4Xo/A6gERFXf9/E\r\nFy7zChFFOINNhtTEBIDjbGSmQggBYqrZHxRjKvNeYrYdQ2oGp6L3yzZPsGFt\r\niG8kJhi2QYBYsBa/7nbLQDoj7/bD5peE3C856g1f/HZzTC42e5k13csfVC3j\r\n/6p3Gp7o6ChpDrE+FaeG5vTOIX4a5ko8tioG215m0zqUYyLqUGXvB3emGR4X\r\npjT5tpx845uotRUFnLK44ASnnzJSfCITV8TvNkJ3QX5wRiYpyFnwUyAZwZb1\r\n9Lw/BKU9sMyz43d/Luh/GzCNyRWQI7BkYFg=\r\n=vY8s\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7a59bbed7c1d78030b9b26acf08bccdc63ebeb6d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-xPFMdpnAMABZP4UC0/uBHS+JpPas+l4AOe+dqd72WB3xw4YzpvzWCHIDpUnlCpmNMOB6A9TMP3JV8H5cLumG8g==", "signatures": [{"sig": "MEQCIBwKvV1o6v41Uvf5oeieJLAI1EFfAc+mv7UXtqek7sNhAiBfJntVWKF1VU7UGlzbgKjVaLY2K2FkUKI/t6rERa3ieA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkaBAAkPGW7dBHKfpMIefWNzc/jDvH0rzLnf4k1lYLXo/RPVTqUVXi\r\n9nV7w2jn91vMpDaIlfGbOCne7+edAKfN/zWtGmGmwFb/lt1m8Fleu13CF3a+\r\n+OdbE9x291yClwKkeQEI8DXUf0c1vYdv3XpeVzTVEoMLH5yQjX7O6r4hINGg\r\nhXzV6veSGfhkPhddI+eYWVjRPbEPY970kJTZ/+BAN2bj+JKX7DBoAUGwp8bS\r\nMh66bEHFxY9ALLkHZisvy45a6dkoPmFGJdRfIOojR5yzErv4IcZyJkRE4GS6\r\nEdhB3EQ+OCyG04Ey/oBXrsuI/pIIPOoAEfprvnr07jyJeB2bao52VJZpgibc\r\njOOzN+8ifz6K91kgPS5vBUV0Wx4OoSsgmrHjkDqNeWOK7pjMAcj5DwD2kvuK\r\nu7mAzTVoZX+Sw/ESxf+eo1DvNJtCeX8BtVRmpOCsLdDjC/o8zuMH6ec3738J\r\nuvCTRJi1xea9cvwz3b6TTSrsNtC+ezyTwf9NTn+YX+5yGtr2Y7qUE2aFRTu1\r\nC6fdo9UQHN2t6twISvuP7hzjD67207zDRHtXoHB2K8lAPOKMJUj6GHG36CI5\r\nzofb22XEtEBkJX1NN/GKP7TyFdq1UjquWR2l9NjIOzdhAvH7KnAuRsTG78nN\r\nwkIbkORnv3TghgplclWDI5lehqQqeNs5QHg=\r\n=VG5Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "55d4f97395aa906bf66540af3fa24852c0390ff3", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-2wuqRKF4+a6MVXnnFOIVoS6b5cwXVA0ad1kmTvcQPMJEcjNHd1LQdqMHADmk/muN8zFy8bdG2cs/IuAP54cjWw==", "signatures": [{"sig": "MEQCIAJFkF1f+OeiEWfkNiSwMCgnFrdRQM2bEPEOY/j9ktBpAiBlo7Eyw9kGWHAIaWIuvwXE6/wc9YhY2HfFfAveEPZo+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvTw//aSAl9heuNlF2nlcczUSvl6xWwlkTk4phc+XR9OV7/ObUhgvd\r\nzfyHNM7eMtWeuRY7XrEO/8TBYvTSQLOgLaamY3US4eln/6KndcLV3pCE0ztE\r\nVjrS+ox/7okT3axoV8cXa3GSSUr44Etj+OEkUAG4xKc88wdtHIxaLFcETQVN\r\nHli3FOVN/PUTKO1KX3jxM6+Tk5dtg0LizKI0f9JYL2YoqlIQfL16/Kc+73nC\r\nnOQJWYkBIRmWMV5Lx2vsd8gOiYjHN2TPWO1/JvzJXRxw/UM6Sik/Rlk+lOAX\r\nlIEECeXSXBcmCY1LZH5etjHYvh+rQs2gPWiy4/INpWtZfJv+7N0wFzWrFK6D\r\nrK675j+SSbAJEE4KPhi5rFo6DacaMpEf6Ob8UpwHHeXOImxjM0YkmCDKi4cx\r\n0XAcUoIocs9CPAnTTy/utDu+b2BJOJl8BA1H6ZwlxwPQjkt2EQNfK7xlvKZu\r\nCxm6EkrKgmrW2bGVLs2WC4idG4UllrUyevQqCkEjGNToUtfFz5TeLEFAcFLn\r\nJVpw/GNOqRIz4B6DS3wDm5cKo/2dX+OPaw8Ez7lqUo+/BTcDinD3pGGGOSK3\r\nh6gQUTghVOd8fAHdl5sa9UdFyud+uD2/1lGyKuwYJEteI8c6aORCQEeIHP2i\r\nNJoLVURaR+pH8RrYqW+YTZLOPhpefMGwpRk=\r\n=WjEh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "96c92064e98c8e56b6cc9876fe1e77674bae8658", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-5rzr<PERSON><PERSON>urlLjJQ4bPSJOgp6hfLNqpAVgX8SU0aG0Mn/jh9q0kNFXD0xsQnsPJlgrOt4lU8BHTN9YmrPWXIncPWg==", "signatures": [{"sig": "MEUCIE4diTl0jUvCSIDhIHlNORV9pMNgPBwoRUzM95rufJk2AiEAhKIWTEvxpP+cgyKLmmEOU8IvlGV+wfpvMarz2JiHhDg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3bA/7BPHfDU/gqQZJyzlJffkdn1IpS9ddbhbv3j17O8obvXJcaA13\r\n4OvqmLrYy3lZk5x3+kBl0ULl1x7OIabaCQJuvSNFCHT323dJJOtkYbZBTs7x\r\nhd9YCeU/AGbvcmG/oeceuph5NEX1ZVuaPq4eXg+FkjDY9j2dNzbiCUDl2dDW\r\nDYQrAzgSjeOoTnxCZteKs06ljKo3hAVuEryCvLP3DQTFSHJnCLSCh3l/Fbtk\r\nZ8ourVcvj2sUGbZI7mb/9ElFTxMXCWMDqZ3mdc/2jeQnRx2LLXr+ccrs2qUe\r\nle0ThBfHiiW58ko69mynu5GndlHhP0bGBLRFrFuzPRiTwWzsZCyqj3LbODGx\r\nif/ZF+4femcKhH5JsEzs2quXjxGIdzkZpivQpo0tE5IeA/PVyPlRpgUnAl+O\r\n1fwn5dnAhrkZ4XIDHQO2Gn/GbV6R4GPq+Tqf0Jwycg4IDmrNlD2TvZGKsGjq\r\nYsjaJoyNnDN5wI2p51miFDRliWxFhX9OwhnhJLP55LYwFIVpbqH5azo/h5qv\r\nES55MZQfHpPoAUOdyWIme+6PvJ/Lm/eSR1Ezkk7LpE5tV9BHyA3GCKnZTBnl\r\npRIjgnzbcUj+gSCHfzaabEiEMaqJXYr/naSsDSCY01mELee5p2IL7nCky+kv\r\n9dd/3oMoYV9EoDYsB4DNg3qS+7wqlwkr3Uo=\r\n=8KmT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "998d9f2bc1e68fd63b36ecb6be52d039fcdcd538", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-DaYfehxr81iK5xmuaZbqwSBDZWibCC7sVEbzUPAQmt58RQ9/6a55YvJE2oYyjj9fasrYssStEy8St3srjhWjaQ==", "signatures": [{"sig": "MEQCIAYYEtpjmqzxtjiw8gAVzA6sspIrJvzOE1NzJoybkumNAiBw3RhZAGtXR6UI3oIJfJU0DHbJ19w7UoxfK6N++Rzn7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwP0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpOGQ//S9xn8ZmBiPUZey+A7RRHAOfOjP/IUsPFjuU3aStNWo8Te0+n\r\n6cZOlZF4YcFnxHgnvnD2ibqKG9qaEYdtgjp/zTPrKC3EMojBjK2OuVXvfeiN\r\noBuc+BEpfEplVTctJFHVf9Y6iGybBRdPk6i3RWFl4MOAzuN2vhPP6zZxSol7\r\nk8vyIxCTeIpXZu61n3E5IgWVYCMwgAoRr9VAcBfFodzORR+dl55MwZQe89SH\r\nwpm7zFJ3AmwGZEZg7bII6zNObEYFax0YWFldY87SBlAiWrikx2XKV2U+Xox4\r\nlKgms1zRBLbLjgH4bicig1XAWMVP8HM8Kf2ixO1g/U68iTjqRFV4Ttw0H86z\r\n/gwMTTN/wY6r5Ql4x4lsUU+a0xmlDSrYSBF61y9JHXyPRVVtmQNXjqikeIBd\r\niVty4l5VwkPMRilkNStkhiwYdpEl5Qh7wTfeQDXnBl2k7DV5TZHsKISVnMZC\r\norgYiWH+TlPanbnlZfkJB3U64XCu+IpZG9C2ftEIzioKGOKANnG3DzY5ycc/\r\nNAsmfuCwE4NZ9TBFbZnuVxK4CK3Bm1u1gB0tPT0qSljWHs1I+UlDuSZiqVxT\r\n4h+7KvCpmo95YTZAe6LdC1Wg3MAYaoPL+lu6lwGJgBvW5P2a/UUf/php+Ho/\r\nY4V3ifR7xneUTH6M62Pay5DjgjMnwE991Es=\r\n=O0o4\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c94dfd98d4e104ab94d383b0694be796d69f4e1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-jASVMwlofk3yusdP6eE850zk85Fls2iLc+N+UN4uRcyF5GQY/fqAlOgYcEf4oU/0miX6GhnWItfU5Lj4RYK/iA==", "signatures": [{"sig": "MEUCIQDKN/mdiff/DTvDXzOwQHbMBY3sZcmXk64/QoA2IIy3LwIgayqao1TgIYgDKZrVP6pQevfHttkFvobnd5J7EdRMVl8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrCRAAmptlXRi2q8hR9rF8XJpRC3NVfxBOB6KY8ZWueV/e06RFZbU1\r\nno5E94glpTU7OD8qdCOAAuqT8zSo9chhUeGNQFOvKlIBW4e4bDeLeL4wBSQN\r\nW9V+SwPpjBQjL9vcaFLKzJtJS93tnNqJMeJjfmABRtwXxmMGWVygrdIoXnwY\r\ndyi5qQQzp0Hed9cFhw9CDgvL3k8PpJRf9uIDU56hAoiEuqvQ29q9h8rh4QLt\r\nfoJE7VVO6LeBRG7pzQydxsoP0prMlPJiRcFj9n64wI1M2XXidNISJ5G4J6Tx\r\nbdYbW3BhzEgMDGyfBmXNQPj+KUmQ+7KvJn0AJ9KY9/FQha5q86wxqm1lJEGl\r\nhFFG6sbZzw2qOVlzxn3w0JKYCouIDFWdi02V8pjfpVyoaI8JRVO76IUnAOZ7\r\nRM3vIZZgZAheSczBlcmwj2xJIc3I8SfjrIDOmK7c+FjXDWKH2Y9cCMBn8thh\r\nV7CohsrVbT723l+StWMapjBPxYOd0F4USm+eVlGDSULvZPF5SKlUkpwEbdMB\r\n+VYtlJsmdF7u5jx7lbCXHyuQwsU0ZEcIy+lOD/WAypdrD3tjb/WAH7xw4IzB\r\nCTTiWuZq0bczFKpK1PG9y0Duz12TSWPAU00rCV7/GUIBz4kVUo1cQeqxUoNM\r\nhMTqZ7YhL7MrddaYmaMubWVIx3LLfMLC8iA=\r\n=GfCe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c0987b933b3db374b7d31fdf54446dd94d48b382", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-EhDF5HuWGvgSV7JJfAbj9hcUVLZtGBEcN1Rr+7xFBRb4zbisNp7acpQodbiRvB17dB+aotHFwN6t+sjt9ZooKg==", "signatures": [{"sig": "MEUCIQCUxoiVyDP5Ip/EmikVw6s0GdIveyqI7QSI3ipJlzwp8wIgIzyJW7i2EGS7OhUtk73q0GaehR4+t7P/lY36oWOCTTQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+g4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmox8Q/9FQJni+96nFzX6AEMVBVmvXnIfELB+P0YdWUpJSdPgWyoCkbA\r\nqa1Zwh5EtocVmVoAOkZwiG83McptRGcONN2DiQ+L14hvYfXU5uB5Rj4r4UIE\r\nwrm21QNeRZm76bVOcqLfcOsoHdbGJV/QSw796ws9+BNGPRuE8AK8FhhHWzpq\r\naenDtRyh7rJzeAEJHPAgGeGnVv9EZVUOY1pNJkfQeJWRDvFArwmUywoSn4g/\r\n2AqMiE1VlhLr+GVRbjO17QIL/oT0B1sfOTbmQlq7keyDaefw+U6fwXhqoOA9\r\nhM4rjrq5v5suf89LbqFfmJVc1DTNsFQ9A1bIpaNe7Ao72zJMR2GD9nB5kdYc\r\nlrRmNVsBL3vDDqb147BopSESZM5Sewr8/jeU4t77zMcZgpnIg5dxP2lgAcI+\r\nM6XZvlOvazUeukk5IvPuE+Hrs22gyL7EDnv2ZOnLVTaGE/HLj+6BjqACZfe0\r\nYAaJeoLkn+4FgKyFKq//sHm43gF1/DHs8yUgxvmdjGe5sqXkAn3hkUQodGNV\r\nNVD0FZ9fueDP8fGE8wcrj2Eqg+s3VxKbPT2hK0sWUPKWrk/P4serTix5o/3j\r\nA0lPY0zvzhTro0pPeO1LhIf/Ygn35L/hSylHBVK56de38+BXJgyTiIQ6Jun4\r\nFyEQj88VB6K0FOcCVrzlvHkTKAbDhONxfNU=\r\n=uupV\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9c3ea98d30316f6bc42c815dd174e13005cd4b92", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-B2WDdJjdtmxYbNSCZN/6UPEz2LXlAzly3x1rCau5ATY843MTecHLMJ+qBfIml0gUUPJNujfJeGuOF0TVWU+Mvw==", "signatures": [{"sig": "MEQCIG+q0z2XGovlHcD6BX3BhXq5DXXkKgDVomXe0KPA5ydgAiACDf2ZmRsBX3CWMtWwvpEaHeBpEwHXrndtjH6Hl1gFFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/biACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMMRAAjrQ2XG5Fm1SgffqkxM6e3tBwhfXUhFDNNgOhmaULQUL/SaP7\r\nmdX27Bn/0NLSGuovC7sTNWhiuY3jw90yIUVX7HZfH4wGEG+zOenvdfWN4hS3\r\nPnBdDIxjVkYD5M9e4d8p1KJo8AEDreN+YvSJqEH3p7v02FIpK/1dK1l9J0PI\r\nQny+BgNaO0y//QJ/g6YlYOmWtd0FElTxQdwrWZcSVnlQlMxk2l7dzzlmZigN\r\nY2YtNUUICTUONChwQvuMRBn78l3w+JxxHK+/yY2yuoW03q0eJS7BKrqt7cX+\r\nXnYAcb+u67S+xvTTDx0kPi308buMAkzC52upTJW1H+TC6xHjYHBfPnz9uqep\r\n6dut6W24NkYbVNENsX9FPdZW1o0GdeUEaQLzSSkJ1RiHFXUdmR3OCR8mHmsY\r\ndL+fuWZfRHaUQWcHyosO256xtVc3jDDMrWjulVMMbp0ScoXwhr7w7J07cbM5\r\n6GJf0YON36XNUhfZphpvsRshcXiNE/sAiE8CYZ+cxeVsmzQR1GtijDDNDOpS\r\nAJFR9afgqHVACYe845D3bG8RHtwadhC0TNqq6e4tRkv0LfmclMjkfjbymRAc\r\nmhiQ0I44PcJDzAVdpocDHtSTw5lGcdmR27R6dSnVbDSri95wdis1+Vweuazr\r\noAsi6FbRxY7tNBHhb04+K3V6vg3Rx8HG2uo=\r\n=JR5p\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d000278765fe8b6a6b30425b277051753517299c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-C5gQrLxA702YmIWx/QOAQR9syt/Dw8UveTfLhXsGFx5Dnr/3a28sFQFSxZNydtYaG5rOPwLFWiDlzCTR4ny1TQ==", "signatures": [{"sig": "MEUCIGUAtCXEeLZK0yaG8AAOsY8N5Cu5zMpgltyDSsCvE5iUAiEA4m7SD8SvJaKPK7GF7+vC4WCcEVXFdyDWm8d/uhNUr8I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRACQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG9RAAgcq1wIaMF62SpYO/yI9Je8s+i9iVtmmhkJB37d7j7pLX8YMx\r\n8dwqYnppL8ZxYll9n1EU+y9vtU79zd3JhqrbUpCIVYKx5w1ma1659Rg3M/7W\r\n4hQlrWlZCbUSuxRhUjdN1PQMB22v04RiCBUG0EuFlIw+aGHU05Lf7BNXjb8n\r\nBfam9JJ2mbZsT3r6MGJqkZbZk6YgGNay3BJH1tABPp0PMN4CXdvGI/wXG540\r\nGi0wEPe9tyo8j8pdJYF1nVrKOLwONs5XkQFb7go4cw88eVVN+p/FAfJxxYSh\r\nTejzJ90F1GbZBuT7kMBCfgP1L8oRB+5YueCTOIkVA0nIoxjGbLBYq3NIS9DT\r\nx2DZ4Yg844y888DKJ6WLnmjfZWh5ya46EYlz4aWFu7tLPryHmNSEqfJH9bRQ\r\n2/YDNWysSZdmqoz/EtgaaBmkoTbrJKeIwh5Y0zABWxT/VD218yB3YriCLSL0\r\nOqJStBBBUdeGt1ffw2BJlbDWcbCF6D5D4yo8L0n7S3oYFuJJPLj12nqAM2eh\r\n+e6Xh+SQawQ3yPNm9X00YjP/OhJC+qavJzC6RYGelh/ZhgoPUQodF6X/63EG\r\nMrrCelub0JbVZUl3oRswsN/rMJwyORoGwS6AffVP39cZY45vs5pXdTg6TIze\r\n/s0RwKLLkkKPdzm6GVDAoXttaZij8PqSIug=\r\n=kzxu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "682ea9033931fea0d465f57be29296cb93fcec40", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-dXDF13tyIBx+TkqJ7/TDr+4KH+TqkVimx11WyaW7EzDcDKspGkVawJLG2fMOMgRHkM7M35HDpfksT0XyOsz5iA==", "signatures": [{"sig": "MEUCIQCNVsHs0/yqvEpUIoJE6FSxN+uy7shHjbSFKv2cdDlFkQIgXgYamcmJ1iH/gSfwmDiCIeyRlUviEwNh1ZDLMPgy0ZI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRx1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNJA//TzgCjZYgo3T3vWlA7vRzwTZu/dpLqQCFPezI46r+7i8Hh8+g\r\n2753gMJ0ZUiTz+Ja68bkPA/vKjqAbLP4HGKxiPz5nDArqDb0joHtjQ9jugB4\r\ngFHhH1oCpiJ5UyU03Lu/TArXXtV/nLPucO2EU2RP0xgJ5adVR/B9e3Gxv2Bu\r\nRS65NLPjPS8AzaV7FnuK0yV/fb/Lnl6e5iLileUCsnY8ITDvX1oYuZLO6KUL\r\n/Zyti04AItxif+jXvQ0HcjtXt65Oy3pZq68pmpaqdalBVpweFuXASBQeM/bE\r\n1wg7ani69A4LlzFtpAguLWoz3YYeC2uX2T29unaF9H3wlL0USKK4QcB3b03C\r\nGfGO+581EHH5bMh7djZ/nCzX3ysRCab85cyTiI+XzM2VBDdnYHD1i4cCJalK\r\n/yO26TwfrLhtdLFlh5CLvzL6B4iCenY/Eq/rusPilQIWoR/FSyizRB96f9tq\r\nKuq7HBof4TYGufGcCdgSyfUMpe49wR/bZFelWsLmYkeIjeas4XUiTMscIdVl\r\nUHZy+idhNECBZ1DgkKxgboeePQ5zhhRRSuyuzkvdQieFewhYHhYq/cWzLGNO\r\n2ey3X6hRKdzyeG8Db6sMHAw71wQt+b9YydTXAjWR+3AP4rFj6UnS4XOh80KG\r\nprSiK6dEBXzzwCHYjvOEunc3ycxv84AV68M=\r\n=w07D\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e9b520d015df246d6a2fa3ba73e62306e379ca70", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-LN9KdCZINBrEGKIZ6/bO19elR9RiKvIYbNx4sB6eVe+yXC46R82uC5tvVZ2BdPf26lDrQIudpwv93CpX5dcFcA==", "signatures": [{"sig": "MEYCIQDtgPFTaJY7fW9u2hT6JUjhMNYbN9iO7F9XeqLCUOG20gIhALPETKbvRRhvKvjT+Z0hOViRPKIBUydgmi9fd5K9me+s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJkA/7BYqANO2vT3JOtNrlS3N0zeDiAU2U08+FZKENCoPrMWAM5cko\r\n65ZS/0QpjZquRmTAB73SNxWHdc2O6N2+iy/0FyGVXaT5tjuqJKt8tlJdsDTL\r\nYWDyU7TUezopAifPRT8qKrmGi9KuBxqV6rr5QmZ/e/9p8fdvTPTm1r4XDN82\r\n4M+VARh837XezfCZwxp9nMAi8NikDpkA+6zrduOtfrZ1LzcnoPKv9NGbL2HM\r\nKw1qdhw3NkkG4oT0ft4POTc1nN+2o+EgmebVeGDRPg/5CQrIcNPZP6XoqJkr\r\nBjWX+9FGKf5oCDk0S9NJypPgDv8VC6gtzRgFHIPQ9Y06ouroXgyCQfBPRTLZ\r\nREwXmJ7ql3loY2j9gMDn/UqZ5CCbjJ0tXZmtz3L1stpQql7WO5oM8aQX7Nxd\r\nb0j5CMC3Ix0KniNXjV1SUQKhHNQtizpQuLsU+lojhu6qDjCLF8vBRlcjhX4X\r\npVBe8+5JflQVw8XcBSYsdrRJfvID3Xby+SAt9MCnLmi2r78kLOPLjS/6+MUY\r\nwLMv0MZ/pw36GdHBd1HLCF2tKkPwWafkjmoaLSPaD2Sux2qBDdfppfm1eYeG\r\noyYF5ExC7DQd3wchg37saLueKykGflAObEYorEBgNBN3LKhzpWyeaRhvJlHi\r\nP4feJjsoMJ7kJq0W4lUbxwAFhaXpGIeI260=\r\n=ASvn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "051149e7e9a828787e38eadcc167d2ffd7144b22", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-oZ4d4CXOht9olUblZ96T9VCfi5K+3jXj+jCWt3nkir28kQzSuEY3259N0vnv9+I3E9OVErWz5lrj7IiVcwpWyg==", "signatures": [{"sig": "MEUCIQDIivioGVsydkvtgo5atnTa5u3nBgouipAKUQU2N4lqGAIge4KnN3WXCHzwItIAMJYkxmOTybc8CqM6geAGRQEC7TQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnK8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVSw//UCepycuVE7NcDVeYRGcnVrfCRz/8mPYfgeew7kTLMJkfLuRJ\r\nXyhMcmcZYkYYIyoXzCaQZaafAw0KHfzhkhjn+VHrDoCgtKTXvK6jyxQCXJHN\r\ndkLnkXoG23jXNOoBZW/PT8JUwMQ0BugbcfeScckvgFx5vk9W5EfOgYQvg9X2\r\nR/rhU9dTh07RMDnfy3uW3uZuN9Oask/yR26Tb27E0+XGnA+nVe7Eiz1r5JW1\r\nbZnaS7UYgk8R3Lczfh7oGXqJ/tG9KgCVNds3OhjB4ll6kIo9YKdneVoUmIK1\r\nb36PRcSXc7GTrKxUROVA1s/xt4o48d4A9B/Y87ORwSYRXXJLSKWMv5A40nlF\r\nqolCQIk0hZLqobjzyg4WOXi45xHP/8CAWweRFtCb0Rzm6PGCFwkdAbGrLWRy\r\ndjpu/rngE7JOeGFJvXvEFEeq8uBwryVSAK4Wi2xUoyDH8RAiPg3SvldoYgs1\r\noACKKb0lqZxKlmr398NiCNSHWO3zUOf0WyZ+/eEr0H+rWWVu4PVWmTi6VHpa\r\nMye9pnRGZxa/41BCQO+G0//S+UXr+qqsJ++4myF74WcbskRDFN1Ua0rN84DV\r\nX405kFh/Z5LmP4sW4cvb5By19vygBWXpfqZcmqr3ZYZsCKbhuNzbb3GjDTLz\r\nVehxKQu+mxC7HClqcWQu+pZcTTINduVZnDg=\r\n=BK+B\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cc91ca8643c6a70b11c2783a5dba0418ffa501a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-pqs8ZAVjx7YdRjDdFKECsS/yGzkIbiWl8zY1z1i7Zrm4zdV9bCc+kLGHxSdKbkLnoMsszXNguMxg/RKgAs0l5A==", "signatures": [{"sig": "MEYCIQDe57GIumNSfPXYRtw3yFP3xOpP0nsMqJDAM1y2zXyjUQIhAPhzMlkm4/WsbgIf3v25jwn4zJnYs9loS3/B6mqRTl/K", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqqNxAAhTZFli6RnQYwMrKOaxUAL7+nK7ETGjbB51IsMc9aFAUQT9KJ\r\nwPUbAkGWZwjXsVk9+0GRd6Qm5qDDAk3d5T1v33GsfYz9bzPkbtWo1Ee5xKKy\r\nbJZDD0MSalhMbqQ6hkhodUVAb1M3V7bGhVy0F/T77SL8l2mKz3puVzjyNQPJ\r\nhMmq5U7fUb1gmaXwiMJELxcwtu46J4NC7Zs+C301U6SW4UFJoJRB9U5GJ+r9\r\ntNVBATIkA6XmeZJCwXHg41l70Va9NdxGRjXcmu6ftT8sMh/oqpwofWWBUpJV\r\n/8l2pCkip32h/bYqriXR22X2vsKxMYz1C36dB1PGwspHUTJo2fWR/fJxRnPo\r\nSC9ONP1X4x4KDMLpiQMLC/3OuUpBksIteZf3wDs8RUX0d+/sp7lmOBLn57G5\r\n5b+RTskGx5/BvZlx8XsZj8xUt9+XHexOlP0H04k210BDgubeMFAlep52SYcp\r\nyaua8/SSavJ8B2rY9X7tNqA9J+gyTn4dGAQIGumxFfTtGHdbbG07u/vxLfZo\r\nxeZYUMxrWB24mA7UymzA5ak5TSGnKmxIN/PX5YHJYuyJLYfsXJJYMU4v+4gS\r\n2tx3XhqZl2GOYPrFKsvXwfEOI75MEF14LS8OsW+IJr1jxwF1z3la+XVta0m/\r\nihmSKfoo474gYj6OaFawrqyU2wCYXdKX6Fg=\r\n=vxIo\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ac1cbf94ea3c8257a08403a7610c16820d16020d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-JOsrq2mfbxi3pkSaRJ4xn0vjGoSg0TeShDCxz+iY8QuK9m3jBaGNHOXLk0WECbSPZCdra+RdtsOK0GG/ilG2lw==", "signatures": [{"sig": "MEUCIQCftFy+9IAvUaE7A3hijDTVs9QJAHevvh4DMTChQO0bZQIgcWFaIw6GyCk3duE35EHZrYqgtgJMMRHP4+w5t6zIDaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq0Jw//VF9UnGH8F0I9kDBJeAi0zN0KAeSdDFcYlgZSS+3KWLPYj6j4\r\ndDIY6I18kyLNe3sIUZ1YvqLACSz7tdccOcdoq4cdX/zJuvo1xeEoWPnhWBnn\r\nLMDkGyAXtpP0/LlQZ1R4J2pN8/6Cu0ku0owYpFzfI5qMmVJgMSGLyAZRGIib\r\nn3IoCaGqsudBbeQ4/U3+Gc6qbvAjS6UE5HXt6B6BlEJ3JpRKY9qEgGsKp/zr\r\nw7VilC8s4wZyLVMIMk+7iVgBSDmg400HKJa+IVDGgGmQFN0EUw93Bb4JFaJN\r\nSe5jz+Wi3VhxBo3YWxx4JHzMVcHhFcPVSkSK14pW1MOxKAjEG0jcXyjqVoSU\r\nlPxmGo6kr3+/9gvBFs4rdoZmVLcXrDKlAl2nOH09sSHQfAs8YqzyXHVacVGO\r\nN13jCwcWl4numSpNomfFQ44Bjl/N+nSqkYfyHtQEq2+hC5UGOOuQf4225DlO\r\nH9GpyWVwzOeNPQ9S70nc4CdYmYisFe7Lltjb2pTVF6UhuqE28sQlq3hxZ/Ds\r\nc1FxlE4IvtMZN9SJVgWr/uKRN0ZkAwYDLue2/TXdMA1vB42SQUqsC/e6OZds\r\n+KKDBYlvUGj71SlFSB7czdVfNSfWkQc/ZaZshZc+h2ubBhkJU4UdO0X2xhsJ\r\nztxMS6SNqTDY5iA+Mofu3qNO+sY45zMu9Us=\r\n=dBA7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-switch", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c864da2f82bf40d9a8ab4eaf1853ebe346b4d9a3", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-mfsK4nX6/Y6vm4rqFbqa0auskylO6V5RwgkM/AQacybpTgi5bXBtzY1bIJGiXMOnFhsEXQNhFN2mMXWTkJAwKQ==", "signatures": [{"sig": "MEUCIBucP5h2tmFc/K8oVdFXv7PQ0Z6Svd/zwYzovuEZh502AiEAv5D8fE1IxH2jgb1R1NH0kWTeBZjJgj+ufBA3ol/NhUI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43353, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRfSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqfnQ/+LWzOo0TDODemD3nMgkM7cLcJ74UREblq0t0G8EZELrMiEraw\r\nqSIK18W9y6eleYkYIYWxcUCrVauevzqbHuJ5pCNafOWs4/oOHvjSGb9IN3z2\r\nXQ2oteoGPFS1JL0/dU3dtlmfixGWcoH50SNLbirYIAcYtA5ZLoZyED27IhB3\r\nD5PDyyAEfyikHTD4PwsH1GkpORLGBPvgWqw11a2OuHQf1BCOLk2dgpaXDkWU\r\nSKmvlkmFNXGUVeGUJ2R/uSZaWH3JjgTwBzpH0fcEmATckER4mdLa9bPWXb1k\r\nKpTHopfjd35Ky4cnak812PkC29JJbiTCsKFVNYsdORQY4PwfOcz/PdSzr+h8\r\njLbnhXUDcrLowLalHu8U+beew5DbKa8l3PqeLzIwmuvAh4sPAfqWVlMz7fLQ\r\nwYJX7oe9jP9Je+AhKXJ9hc21T7WWzOTkLVlG3h2KIqEedG9X6+Rehx4SVrcv\r\nUllKfRUTkMPfH+y2Xsl3kh3HTAe+hnZMCHyVWZFHi3IeD4hJUpolLxpBZGrR\r\n/eBStELP7oeVF+rR6tNuLJ1RQ8egNdzXLeN0J9osWQFXOiESJZmTVSAiJFqv\r\ns6UalzZ4+hGRyj8887YbLZvmRUVv04SIVUfKJj7Pkh1+b2mbJrUp0e9FnixP\r\nWnNYqYV4EF2gqo20ayUs1zpBQ8rKhQfoJIs=\r\n=wDnD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-switch", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "56665fa951a4190313be21fab70a90041bdbd191", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-tTxGluMtwrc5ffgAiOSMrYIx0r3vSTcgM4Vl8rqfpXcHt6ryB9B0OlFKUOiDpKASXlhvzfHf4Y0AYKJdpzjL8w==", "signatures": [{"sig": "MEYCIQDQ/hU1Oq4vWX6x7IZKSaFV/Iybp10BEgVsADjG5umQ6QIhAPntCz6+PBhv/cQbi60Vx0F51pLApShnncV8j2VEtN1B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc8w//e9ZveiZLaRvmtZ7BBEAvbQDIcBtPrPCpK9VZp4j8C8DpVdR+\r\nZIiMSX4TA8vYclet8Av46/QBA7CqFCyx6pA7g/5CvHA50POcO5smNVz/eKVl\r\nGuFCxgdrnsdONlqeOl6H24p7VOkaQB5a0fuzXNaWnNMTr+/HQIn96QAWcFZx\r\nhTfSgQOwUlQHW16Fl45U0/nTezD+vr2GVm/QzDOddwAYVRFeCvTOaFlpqq24\r\nzjG6ELwVYh2Kq2a0e3IYoZo9WxsxhlrBe9l1cDzc2g02x+nMzudWttcPA3cK\r\n5Q9+ZhgYZ380giPKC2mcvDQrwO3H0uJKdicNn+IEB+OYp+Ethh+ZfNx4jluj\r\nEgdq/2dha+Q7efhZTVFlDqo7zaLcmJXWgBbrFJBEd3Fzcddz+eLNWZ1jpYlq\r\nBow7jW2aSToO2fI3dRNYBFJyv96QcBv/htA9C68SkjL9uoptPmRkYoaeDLPf\r\nidTrSkHMTETz/ASn60Qf1bdX60Ylmf0r34gBqWbIM2X7sW6d9DiUYS1c+GsF\r\nrCL4Jk0zR8wgf1ny11AZwaFfBxQYQoGH2YUwDdbcvQ24Pt4lT19C3Zn1uKS7\r\nj2ooKue/btshQ8y2TO1pRrM/odkrxRqhNeUBDANNdk85omUav7UBwr6+tftV\r\n95cjISpRKMrCNuvFyQeu43UO5/hPaY+yIgg=\r\n=ES94\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-switch", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a5106768d3b2075a4a70b0c8e33ed29d4b62b1c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nOUBKYpEZy7q6zxaQBsabTnpq7iVAHWBosQgO5tYch3tebxOqgxsaH6e6dBPXpw3W7iYEEc9FQ+Bz63gtHBgSQ==", "signatures": [{"sig": "MEYCIQD148rKmLtcS4gh6d3SLYZkNPYsC8stTTW10oigeCeB/gIhALUOxPI/OkQXrQM0ojnPjITBP9o5R1egrw7h5Cq2xsuo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzf6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpl5BAAm3ip/hv81esphUmSedrhSC2VRyiANUnpK+TyBmF7J1P/goOI\r\n6gKZghmrTeeXnxs+xNIDBeAz0F5/zArqwYK/Zg+ERNbWzOeD7L7nn+3JA/yq\r\npe9Tc1poz8+x27KAJzIv/+T8VOAG9OQypHRiKGu2zmyoBZTfscFIeL0iUwwb\r\nJCOAfEfRJWhysRSYRDjcEYq/fQ50XvtxAOyOwwoar8qz8mlm23V0p1uKXf+u\r\n4Mn89HPmi/0zIDbn2suwIudM5uDEnlnnt2kT/jTqEsk3c/7iqh0A2yAdTake\r\niXe1N22bkKFRBEnbxNrULUpFvnuE3kOjuG0/Ik4yuKxh8PMLJzq9U3ul7ryt\r\nFBPod0QF+E9hxwvXbNJKRUPLxfy1qhI4Na1Hgh5izT/pqGXe9D5qfJL9Gjnq\r\nNNGljkYqa9cTjq8sLUJzPhijrz2QWr/EUjLMNX1V+Ol6OH0/BVEEnxONnATm\r\nQMq/VoeP1jZr5Ysy6mi0L8rFCgDgHI0ihfdKi1FNR3km+6R6XHf7J4eGxC0y\r\np4JKjb6uN6FVGdikO6XuPBQ+FRlNF9OY2RFj9YNhLaBVy9wOF7s+nkerwc6G\r\nISacsrR3/w7/jfczwucQwMHy6cFPZspDpUbrGBv0YTz8jZ/asLg44qo5H+hp\r\nyLnQ74Q8MNq7uM8y1rNTbLuIvceX3Uindnc=\r\n=zBFO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-switch", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e3d1b9fe18b6b1173aadc8b8e6efdc96a28a70f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-BcG/LKehxt36NXG0wPnoCitIfSMtU9Xo7BmythYA1PAMLtsMvW7kALfBzmduQoHTWcKr0AVcFyh0gChBUp9TiQ==", "signatures": [{"sig": "MEQCIHaEhYhflnG0nrFgJgUMBs4s2D0tywJEGh/eCkrFMnQiAiBokJGNgsPeP3YZw4wiBkvIJbIKhrVG4l+b5FhA7uikMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43313, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJa4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4kRAAjO5gzJYHAG3V0+3o9C3iibV8HbRL4EidTKv8F0xkztkPjlHZ\r\n1ngEJS1OFdVSVjJnhWQL98PIxpyJPQlfZvEDXcFQxtTKGJDmm1LZLIykpchv\r\ndiwAlJ715mzaKKOCN9oUvJ0oWWxp2RLghDFub9ThlnXLXxuyD2fC9SQwbY8e\r\n7VWiw32oX+bDkvkjwoOrG3BayQoFnffokorDIvVQEMQ2ibVlIb7jtiEDAvXQ\r\nj2lttQHpF/+IKAXlWI9k+uGCryYlCvaVzhQoZXu1kfb7DjS1NRmUUEp0pQoy\r\nUO/Ao57PWqkE3tzAfp5RzB4rWW1y4Fbca7xUJW+1YQ4QKQtgFUaSrSgX2TrF\r\npgSpR68jYTLlTTkJ4vm8FQSEa35JMQfjeXe2lgOY6SnMQomqGs3oxsLu75tf\r\njy0qOC/pzjf6xnKYUfq/qNMd00E9RQkaxd3bq8A9lKFppNRW5g2sQj9D28QE\r\ngveB/13SITrbRpVX9KEEnSfXh46fkcz/IpjxiAspjSZl5YxC7rNpA86smkw1\r\nU0BKn0nKkaNfLu5wC/fbtIs8RyDkGZc0UyWoBuCqf0hwkIQBw46NIYJfP+GE\r\nqpNx2fYSaR/evlyvh1kksoV8MMY1l/TAZROu4umI1HJ8leCzPxMlpe9CdSQK\r\nJXz0o8OFBTHiBWuWCijOmIdwjyxHwrdn+jg=\r\n=RP2b\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0bfcd01fe313a5e57933c6e7fd125cbc90dda336", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KwiAqDOKePJJxOLmNNslElMJC6qEQwjkwbQzyovTTUUNJuUPxt4l0Cxt7nvqnp1cBM2rdweWkLZQF/eXNBGNEg==", "signatures": [{"sig": "MEUCIQDhuJKmk55lqz792BzKwjN7eehbHPAGHpSrLf3kEmaAhQIgCTXMhJ+jiyahhZFVTkyNTYO05MfLs87VchU1LV9GYjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrK0hAAnXn3ObWcAZ7l6KBKZW0kmVV+o9Ap2l3zabiQOtmAdr60h1s4\r\nKHKiCrcqDlq3et+IfhvCMEFrixmaB8bcqw2GbnJQ1VLLo+3p9yqAlp3sTTGs\r\nA8jVS2heU/Hd9o+3flSGClX4wYgsyZKtBZLA78fL8Oc7eUsh7Nj1jLQ8F//i\r\nR8/YqT3jkKL+9+HqMWImXIvaO+a1KlifpMrbTvMHqL5GlfrEpBIQDFtZSTNW\r\nniFrrRqq+yo8SWXgHfp2KeS4hrXChdrant2EoesWhtwIqrZwhqjNE+Ld7+RI\r\nQzNIiwRWIczuZEltchiQ5B4VJYO1eyatNxAc54PWBbpGyceFvyKPpl46ZPth\r\nWRU0IwGgZcUd3WMgn7r2KU6AB4UzZmFPuhjAnf0JiJU4cg2tdE+r4gJFsStG\r\nMxzXdcTx6cHxVvClmIzQSzSju1eXcrca4VkqDGO9E+6tytzRxzcTulmmxgo1\r\nE/PQj6wjZLagV9aTXGyb2OARw7IM4N0r0CXTsEBPDT1qDls4RNS5Te0yOiVX\r\n3rqIyqCEfy1qIy+ewCZAMnp8Nj5W2NDOko1dQCLJkf11e3aTReDN2CVo73ak\r\nnk+IgTPREBbePSrWTyhicLaFEnMqKg5kP5145AwiAOl0qiGeFYOopD5kcEob\r\n2hmC/HDR1yz0ET0qtfYNOtiSl3dLu3xGmkE=\r\n=lOh3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cd6698cef33530ebb3e667241668c21e2ea2da54", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Cl7UznwaeQlNXTq37Suz53fs6VM2eutp2wAYka4iCefV2U+Xg1tCy0cenDzPz0H6ArUAa+BCrmQhjQINB3/HRA==", "signatures": [{"sig": "MEUCIQCk+LV8XwS0f6KSS5ym+MGrmEurS6up/AMomXLhC8O+mQIgD28lvZFSc7MefV9UJLTKzuicTmeUYdO0boIhqbRbWuI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351}}, "1.0.3-rc.3": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "89adaafeb890878f96af69961daa91630bdbd331", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-snWzv1mtNlyS/ki0hk1D7h299GMDUgWzgS+l89xT/ZIJZRHz+nNnwk5MgqCAcOKEj2f8VxZueZu7n6DRh/95zA==", "signatures": [{"sig": "MEUCIQDZtoaSyzBFEuqLdReZ2N0N0WC8k+VUIREr0CYzsLYDMwIgaxf0cHVfBt5H1SVHWCkpgqLjtajZ1+tNpHmSgEqCjjQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351}}, "1.0.3-rc.4": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "315dc2b60dcfb6ba6d698cceab4ecf0806f2d49e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-0yEbDfX0RHi9MP2sjVi/Enf6GTzUQJ8pMV2mvAkxwAmi516QqaS8ubvS8JqZhA8KnqQ6/3heg4XKHDsWED1iXQ==", "signatures": [{"sig": "MEYCIQCll2G4wOIpSoP2JpwKudTCeId98zR/boNRxrw8vI5fVwIhAN+Z9o/K0N96Hm9RqioUOmWBCYYOVYHyTrtSXF5iXkvV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351}}, "1.0.3-rc.5": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-use-size": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-previous": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5dd122669e95cdd605ecf1047aee5182758461cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-N/wixJFXeOcJ17eQUwyFZ4upC0lucCVf2YerckLfgxMDtjGyludYvQAiVeX7uYCRUsDiFQvBxZCiRLzRDEJHPw==", "signatures": [{"sig": "MEQCIHI7sldmsfHDth6gQjOjuiNMwwTTRBJWK6+o1xJKw4gGAiA/jEGLLv0Zq+oTz9vFbC1lM1A3MGwbNZn8dStNPaIwNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43351}}, "1.0.3-rc.6": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-use-size": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-previous": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "39cbea224ae40897e6a9460aa29e90d7fdf72bcf", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-U6dwJlVqsucOh8RNNLIDfR5+7NwOCyyWc+O5/4C7hqtHrDkqFdt2erSTl+6gJLUDUNlhgt/oAJgOMNSmvM36RQ==", "signatures": [{"sig": "MEUCIQCEABaLDrzZNliVB0j6Gbf3sWz7MR5Agmrf71zRvfvchQIgD5U2sgeiBUkTLqmr715rZlT/xcamuHdyv+OePsJCv8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44725}}, "1.0.3-rc.7": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-use-size": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-previous": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d9187f29823b0be4ff2217f85bb1f9908390b59c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-mIEdjkhwwneNAA/BlANjMJy97y7ELwTrjb0/1eZjLH6/ng7wmpeOFZ/DfgrxLRIPXgFpheqxorSt9Z6MFHBBIQ==", "signatures": [{"sig": "MEQCIGwEA0Y72n2v3WUoH4Glt/6h5iUZCdV5seJT+OhHfPgZAiAhEQHXwRAr9mmPB+3Rgm9rGu99AQ0U5OhtRngAVNxI2g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44725}}, "1.0.3-rc.8": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-use-size": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-previous": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "817340177b4b5cc96963d689d7cb2b9d2fbee98f", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-/glLrqrMCbLRmnLbAt0W+w64f9+P0H5/qH9fJe0PMAiMlgw8XLXPx8V5o5W2uM+lNvZqcTNi8jADVwx/XbVmSg==", "signatures": [{"sig": "MEYCIQCWBY+rzneWm1sHG6epIzsgjnAUe4SPaui74QK8q6FsegIhAOr4jESO7H4gT2D0ovv3WpQV8TjWlD0i/CmE8merUYjv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44919}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-use-size": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-previous": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dfa06d4399feed9b2402a72af366af5f75d03643", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-tc9VIWTa0roNsirzCiP24M8zp7tXP1BZsjiWAGLlTIMEEcYAJvrXmIfdw8reSc5KVEkvhjcIawNvvKBDC7PhuA==", "signatures": [{"sig": "MEQCIGy5wzIzhlZnzyVT3ANJe43ZHX4Zw2wCSzDVdV0MCCYTAiAVxZ0fc7OugwKmyTNkdRabdcNlKm6MsfAaO6flxPouaQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44919}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-use-size": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-previous": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0df08752399aa9c9d894787cee6d095638587fba", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-ReI9LYMC12Ta850+7SvFxllJVUWQchhqsDzfK9JXqEe5FH8hVKChdee6ojKJ5ybNsR2we2Bft58OcOwXs5f6Qw==", "signatures": [{"sig": "MEYCIQCpv2PbbaEnljuWH4NuWRWM+bQKnSrn48hNtytLlQfTMQIhAO0tQPCFWX+JAhDqXvhWjY+HYTEHBKoQPlXpzoufFtFX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44921}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-switch", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-use-size": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-previous": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1338c95baa75851ac2f5bb68f532604bb3b40cb7", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-Sw5EzobSol4Fm28ZK5/TTg6ioGcLATr2Ly1mJJ8ZKd3D9btsiT+NYso4wO8nc6Xe8Ff9Fl4fks2/afGvvNdW9Q==", "signatures": [{"sig": "MEQCIFpeWIDTb5TwDCFVVKO3coo/TEtiLFL8pakqYWBx7bTRAiBlqGhtYeHvI+IDO+DfuWsLwuIq7JhW3rbWKdKed/YDfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44921}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-switch", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-use-size": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-previous": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6119f16656a9eafb4424c600fdb36efa5ec5837e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-mxm87F88HyHztsI7N+ZUmEoARGkC22YVW5CaC+Byc+HRpuvCrOBPTAnXgf+tZ/7i0Sg/eOePGdMhUKhPaQEqow==", "signatures": [{"sig": "MEUCIG8r4+ykJ1BVH9pis1SyypS0rhpvlKnwYyF9diF7gHrxAiEA7AwtmsNVQJLPpWkJctfQsfF/95VzjbuBm7vYHIph5Ng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 44851}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-switch", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-use-size": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-previous": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dce9ca5d600925c0d022954c70a60d1542e8011b", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8+yJqGyL7SFjOMfNu5eRXwKfH/CuPEPVP20Ohwy90Rj+FzCFDveIjO4D7MC4lPxuNhXwYPMxBKGf9pZ+yOeWPA==", "signatures": [{"sig": "MEYCIQDBpk8R8jmfSY0bq7OVzKkbETwUOJ8sswn+OylEytCqeAIhAModBGqvqvdQlSxMHvVW6CJ8jJW1jl+vE84XRiYqAGaq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34634}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-switch", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-use-size": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-previous": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7ed833730b3070239dbdc43e25efe4ebccfcdab2", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-HQnIdobuVUGHldxp38yYXOAK4G+hwc141cNyUNK8HF2Dm2hQnt1RTuMiRCYPFFKcIyE5GMasOWf2TWb7JGP0fA==", "signatures": [{"sig": "MEUCIDAjMaz0AaguDIKAQTXw/efOq/Z7x2TQJjQAeRNRTucIAiEA91Npn44cmw9vIq4VOlCaYD5u1guafjjPrUI9V0RpqnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34666}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-switch", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-use-size": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-previous": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0d034f075f357ce6494fd21a6ffd70a8807a9940", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-PHHPaYJvSyo91shgWpb+Tndmnw62G0Ml0I82x0b+A/8PCuNJDj2nN8T0knXBCAPVS42oM6Lh7pDC08o/mIj6KQ==", "signatures": [{"sig": "MEQCIAqldhWfJFH3l9QcwteY1NRffxesSwHGZxJnCH+sk9ffAiBmvR7sYte+/jFZexhb2NyTDBlNgAZ4+w6px0ZrKTpsiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-switch", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-use-size": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-previous": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "392f25b285fd115b4b634178895d2cf87b84032e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-J5U<PERSON>s4oHyipIYdV8ql3YxMKxjCroOac14BZVileaV6NYaH0+UP46B4gVvX6AZ6/5HRf9H6AFLEB1aInig8u5NA==", "signatures": [{"sig": "MEUCIQCTs7OQbQq2fbcrc7bxxv210hMYl0HYyWOLSpO/2goDuwIgYRloK+W7ecIeT6diPo0QvA6cSIr+bc3FDch0ga+Nn0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34415}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-switch", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-use-size": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-previous": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ff544caba7281e98db9d626988fc1d32d7e21282", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-1kpbeJloLRvua26HfpWzzWzBC2GhLcjlRgjhlXxaH0/lx8wQhbyOnLtsur7BY3tXiZI6J//LFMQ6Ot8Z71OA0A==", "signatures": [{"sig": "MEQCIGJ1G64cnHziYSDHy6tQgm47Q4cxkVDRDTzexHQXjzs9AiBvM54wDuqcgZbFNGHT3UpD9pKeRdpSiQbTLXCtvz6Tmw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34415}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-switch", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-use-size": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-previous": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "16debff4a59f9dd16c61c4faebcafefcd6d21190", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-vCSm3CNiKet/l9+K5RbREhPE64J6ffu/IIHAUW1sjJQbwwONmF+Jb3DobCpTsy5+cWUXk63sUyy6/V4E4mXyFQ==", "signatures": [{"sig": "MEUCIQCenj0np7PgKYUozG6vQnyoUlKBSBMP2zoJ3lginv7YzwIgfyKcfnS18tsoy8IVSDcWOba2OkXocg6hA6fxnsFiqaw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34415}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-switch", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-use-size": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-previous": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "050e29f575027d42762d83e8578cbe952826c569", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-IzevRGoN/TnCmVGUhvTU7XKjv/EFORgQFKsL4nIVmoh/kfbUw0rJb15iVZgvImblceVDFCPmYujETzQxVjlCZg==", "signatures": [{"sig": "MEUCIQC7lZxQAvr15aM0SIs3mSN2UdIomwufwm6q1C4R3BtrjAIgJ4wbmV5Dq++P+NK66qX7YPg2PEB9WrdG1e2leDGLj9s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34443}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-switch", "version": "1.1.0", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fcf8e778500f1d60d4b2bec2fc3fad77a7c118e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-OBzy5WAj641k0AOSpKQtreDMe+isX0MQJ1IVyF03ucdF3DunOnROVrjWs8zsXUxC3zfZ6JL9HFVCUlMghz9dJw==", "signatures": [{"sig": "MEQCICvLLoDF9+Un2xSOBLo7q0jANcAn83shhjN3689N/wnWAiAYAMpuQJsSmSW4yl+YjLGSvJanjqO8wb6+7QowYzvZHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34375}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-switch", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6590ce442437e01f39b8865b3012ef8f2bb69faa", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-vqciJbWwyVYuXxF5E3WeA+eObRrv479AsmvqMZaUS5Nbg8Cc/FNvM3UDhgcqG0NNEBCxvq+TFE1+TH5Wy3NH4Q==", "signatures": [{"sig": "MEYCIQDWORTSzlaYSqjPvSFV+/StH8JpkvuSSyM8SmRCDCdZagIhAL6YgU97z1dpGOERHQm8jPiZks4sDN82qM3L43M6awlO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34599}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-switch", "version": "1.1.1", "dependencies": {"@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1401658c24d66a18610f18793afbaa7fedf5429a", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-diPqDDoBcZPSicYoMWdWx+bCPuTRH4QSp9J+65IvtdS0Kuzt67bI6n32vCj8q6NZmYW/ah+2orOtMwcX5eQwIg==", "signatures": [{"sig": "MEYCIQD+PdzbKwW98V3nndJFruuRf1EV/knUnb3SkBB0ijaSVAIhAO6/1CkE3Mkcncr6YPp5mizWLDsl7dct6q/mo3IjEaRf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34561}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-switch", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c3ff26aba7b73fd95b47f903b866d6dcf1716ead", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-3dV3Vs9vWQtgw/KzSYF88XV0Nt9mVyDtpBgRR/lGICPeu2sL+EjV8d9V5B+TTyQsW2AuJm149sxAjisOYqcNsQ==", "signatures": [{"sig": "MEUCIQCDQzC1XosnYPxKlrEHyBmTUnwbphO0NsHcxLA7K967gwIgb5H6QzOF10tOrF1anbTZCmA09DnCj+/jrz0yQfKy7Ko=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-switch", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "66c039c653a8279400af5c2e0dacb327ad21af41", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-UK2/JfSisvePiIaQsUr0MNOxd+Ro8sesw/EwtfeE2sMIKgb/nOzuOl3diKoSISj7/zPbEATivOHxu1eVzem8wA==", "signatures": [{"sig": "MEQCID8yi7524BvlapmMP5X1MWw19GYecBbvgH4o4C1N440oAiAvuNUtvONuj/b+cM5IUngvU/kizWXIPK5zAsR04Ssnsw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-switch", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7f73fb4a73ba279386fa60df932d0ca623e5626b", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-vHwEdRZ02faoVmH/pQGt4MgOPJB27S7p5F3PbdQAZiK/XbTbensFDp7jmsK/eJKOj+g+kCE1mF50HeQKpN43tw==", "signatures": [{"sig": "MEQCIBUoGAu0KPXcFR4E1S7smOp/CQG130W1LovPbFE9yNmpAiAf8iantQ8MO0OO8jJQX2peKMSSeQwxGkIN+CAdJPaGAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-switch", "version": "1.1.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "61323f4cccf25bf56c95fceb3b56ce1407bc9aec", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-zGukiWHjEdBCRyXvKR6iXAQG6qXm2esuAD6kDOi9Cn+1X6ev3ASo4+CsYaD6Fov9r/AQFekqnD/7+V0Cs6/98g==", "signatures": [{"sig": "MEUCIEC2IaCRWZ7U9DjQhqKSBrIX4nSGHv+CoNn7w+afQHiSAiEAtwAeT/eeoL6UDFt2i4/wYAGtGXba/ffPp2NkkEmfovs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-switch", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-use-size": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d8360c6907888fb1fe50cc6b330c92ff14df0f16", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-XjQ4OD784dYEu16ZzGHW/NWZS10erMKUcPdBcr2kse2I0JKfwSHfgojnQd6wXWWhr7HnlimK0FfTvIpV33virw==", "signatures": [{"sig": "MEUCIHoGJ30lCeO3ixVbcwlrPDmLa5eCwchb4Bxzw8Tu5IlbAiEAxIGoZyyBSzeTf2wYdz/GasonjiOwH7Yk6q4IsX1WarQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34318}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-switch", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-use-size": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-previous": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1e25a42d8293b0ba99b76c3032c65ec179c809d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-ee9RhvHXRL02b23Ui60G2+jC6/dQkXvPW9sirR31zod/S/suafgE4TOfa/y3Fg6x5Wb5aJw7qw0N37ZFAEj2vQ==", "signatures": [{"sig": "MEQCIHyXQTqrofKMkZSG/ccwF9x9g1vEuRFG95vuw+AicUyJAiBOIA2KElYZXs2/RO4YugQhc37UBCHx8GNplg2Q0aMioA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34318}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-switch", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "34bdbad35d61649fc913b1dde503bd9c33f6619c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-fyvrZfsfc2Oj9tkYJ59C7YggsP60bisgrJ+lAPm6amC78vPyZeh4/8DMbvH9Ita64ld4Zuqvxa+YnbPBi73+HA==", "signatures": [{"sig": "MEUCIQCiRBDjOVjub0+5l3XTz3815InIhsk++mKdg+1Mm7KgYQIgCSX1D0QTXTvwmK8PdWYKQJlDLZP7wn5QWrwP6WlAX8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34438}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-switch", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "96ddbcfd2f9c93ae068fc41a9e3442ee11663be6", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-jMJNIJDdyLXf9VRq7xvIUPjuepPba2mTOyhgI3Io3U2o1booHyCb14MYLf+p5mUOegspR5zbfUvciXkszojaCQ==", "signatures": [{"sig": "MEUCIQDRJz64J1tP1qVbCwsmpzyZEzCA7I8P4nrp8MzdPBPjxgIgF3BeVe6/QDnM+thO2QZk8V1EyUT/U8H3HsNO7GvcsdE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34438}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-switch", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9772c3392bb871794625404e7642ccf27c4d55d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-U52JLjykoxivESyrGpSMuvCTJxRb3NCw7SrT2pgLRa1D6/QAdAxoVvwUkqLTUeMQRKP1ZPkfPBr3M0V3KIiyRQ==", "signatures": [{"sig": "MEQCIDz1g2SUR7ysl+qIs21vWqdQuPjcgePlogmuJcjXEii+AiBRGq94Q/m54UR/ajnzTpCy1fkRttocBx8JRDCCH47bKg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34542}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-switch", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2fb6f4c5b2e3f4e08076a889671e27e594c157f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-FUhmkki/VmvV8fuiCvgnkrmqxkEIyJmKA6zfnPbhWF2rSV9UgdSip7m5/s1mJzmrSwdB3P5+YHC6+EQe+PvT/g==", "signatures": [{"sig": "MEUCIQC87/PCClEP+V6Br+UXomKyp5jGBanRA2BZmJ7ChOUJ3gIgDjU6M2vluahoa2BAj2qzYSbB6xokjSrRIJLWS+aDAI8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34542}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-switch", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4786004e7e22c44efc0bf9ff18d0c424c46c1d4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-mQr7kmeqEeRzoIP2xcNfwblwuazRstt4/0j6MLjrrTrzkXecYEy+0codkcLbkfj+Dr3wvWXlhMXlnVBBZqEvCQ==", "signatures": [{"sig": "MEQCIAU+yFPjzDlQk3ORPesofGRPeo1fLzlELVI6BTNaMjY/AiAQQXEfSxjtKFw3a7WXXWlPI8ql+exAMjW9IbkdgYNgTQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34646}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-switch", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "049084071b1936e434ffb629fb59b283cb3f7bc0", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ln86RM4yli4MTcQXBvynsZk/neMFUMnFwQsYjPITwAjpyRHjZjWJGFoO/OV0CkwkdNduNqfamJTfTZ1dKU2uuw==", "signatures": [{"sig": "MEYCIQDDUtSlRemLv/dYXyHbmuP9PlAfTT6EbhMIV3dLGCqqdAIhANghKDELFQAaGUicj2YaIqA3o+qwrUS7840gAWZA/ceO", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34646}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-switch", "version": "1.1.3", "dependencies": {"@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-use-size": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-previous": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb6386909d1d3f65a2b81a3b15da8c91d18f49b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-1nc+vjEOQkJVsJtWPSiISGT6OKm4SiOdjMo+/icLxo2G4vxz1GntC5MzfL4v8ey9OEfw787QCD1y3mUv0NiFEQ==", "signatures": [{"sig": "MEUCIBtzvDlFqL4QP3G6EfZCUuTcVkLKPD8SRP2gtNEy7Q3vAiEAxWOQa3T7AG1GVxPstsgq7HLiIoCsTxuanaGzuJqc36c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34608}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.1", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-use-size": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-previous": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "294e44cf099f78bfc661b3ce9c19af62d1c09b82", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-7Lgw/AbyiyrrOo46BblCCgoglMh4DTCKPz3TOQvniBEuoi63yzI/OwTxBKpobITz7Pfn1nCZ7t9ANHrcLi71dg==", "signatures": [{"sig": "MEQCIFSP0egxglo3LBbDi78m1qmsKfHCKrlCab/Wo/GtJ/sxAiAmGJKJaQKC1KBjEXdHwAMisMuJqz4jFM1nuXfl6o6YUw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34682}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.2", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-use-size": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-previous": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "94a7199164773418abfe7e13c9cf33685bdf11eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-IE8et/KFU1mDbrQ/B/K8amyKcCAuueqCqQGF1zRs2YOLnbZaNhlNz8aaVH3r5nX8X1k7Eg34TPYW9Z/GhZBN3A==", "signatures": [{"sig": "MEYCIQD1APUrjmAMmRjHa3p1RYEMXPWqk+Co/cDSqyWzMSQMbAIhAMFSYz8eyfvF2LBsdZ1IoSqE40S8aE019DqvaGt/oljZ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34682}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.3", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-use-size": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-previous": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "015c281c25716d59d6c6e5ecb6afa0d6257c45b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-kyMmcPbVjolCHMv1TnPyvf30O+AE43R132Nl9ZfFQogX4IpokNhZKnhh6XLZpZoo4XawkAxDZzOoyEadika2Yw==", "signatures": [{"sig": "MEUCIFtz49RVs8qnzlLGpqg0IuvnsZI517vl5IDvIHnwzxvAAiEAq/ysddg2jF3bQjPwkFsuheUh/XPYzlaaZVsO8X1bfO8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34682}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.4", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-use-size": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-previous": "1.1.1-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "95a69deaf541998661c1544be2a9a2ebd7789f3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-rwzfOXUO/RIQwbB/o31/St7ie25BJ5itu6a1Ibh9nHDIuVUqockZ5wMBf3wYI8xNoCwBcybsqJ4gnlywpWR/4g==", "signatures": [{"sig": "MEUCIQCFja1u+c3YOEQhVYcGTJ3l9VniOVHxXC6cFMyKhMF5TAIgGctq5pH7RDqDePPRAAJQc7QiO6PmX3X5/qaKaj4ys6U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34682}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.5": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.5", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-use-size": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-previous": "1.1.1-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81cb489e6e115d829114bc3e725b9e5669b02ac9", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-us5JaghOE992XMm7Z2Ny30dO0qD/sJADKQoc/CZcooS/NvTPq230LUjMKmMVS1pY4uae7BoL67bl/0UI/Z8RDQ==", "signatures": [{"sig": "MEUCIGPtJzOAb0XeFkYC8UuK2eu5y/w/xsjbgg1k7SqP21DJAiEAhDmED0wYqEJv+iMzwSqPjKjLtHmeyW1vukhwKpPnFmA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34682}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.6": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.6", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-use-size": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-previous": "1.1.1-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dee1a65bbc37cd69bbe3a723e6de2c25c27feffa", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-DusC7FjG3R/aN0HLhXd5KcnEOthxa1kBMYYDvOUoWvNxwMDusXFj0ZxAJ1dCAj2oBylIbpSJ0qXAnU7Q0098eA==", "signatures": [{"sig": "MEQCIBtMsr9iYpXZ0qLO3eYSLOCYwVELXVLPyDok1iEtsCWYAiARPMkQYH7pbJ7i1shUNwMAe+4Bl4Sdu8tgb3lZAZ4HQQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34682}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.7": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.7", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-use-size": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-previous": "1.1.1-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "94acc4943b022500a600d3a3d7fa7a6a9b2527fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-DGalMbVBLYnNwx+pZuw/n1Gsx41LqUE/IIk+gbiDmgJ3NOrkQly2/0PXwFDN/76INqDdS5vTgyJINtbRKR6png==", "signatures": [{"sig": "MEQCIBPT2Y5FQn48bjCWNFF+7buKs6+aWV5yy6ort+8Y4kruAiBhKbNooFf6rjUbmm7ZafCjLs+0iqMN5q6P61ovK6nQPw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 34682}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.8": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.8", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-use-size": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-previous": "1.1.1-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f0588fa9053d252f83d0fdd83794495fcaadd1cb", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-ms+1c3TTIgxsxqTFg6OGi8E6TqHVH4rVo9pgWbEpga1d9cOOADZVYcKUSBfuLWsz/sBZOQ9S3fJG816hyXn4IA==", "signatures": [{"sig": "MEUCIQDtFXrBcoUW6jG+kll14b6TLo2cVR839y8+qxMTu/J40QIgKVwmQXqNC0HBGbXGZ2yT0x/wGt2e/oQqNZRtM+m8Qk0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35073}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.9": {"name": "@radix-ui/react-switch", "version": "1.1.4-rc.9", "dependencies": {"@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-use-size": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-previous": "1.1.1-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9a07dd2383e1d8ff99e174e74bdb1d12945bf12c", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-YxQfTu4XeJ21lKbANQhTs1/MS5lO3ETm4TPqexdeYw/mIeLXJCd8hwREklkYqNNq/nYDfdoHkVfrBBGFcXD9kg==", "signatures": [{"sig": "MEYCIQDEPvd3WekEIsrnnoVJQcH2XP7iKhdNpSSkUtYsPjTbLQIhAPbrEWR4mgbrGUftQJpzLbTFgMhWRMff4RXzxbktTP9p", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35073}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-switch", "version": "1.1.4", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a0672f67974ad11cd8e2304b04aad8ce684eb037", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-zGP6W8plLeogoeGMiTHJ/uvf+TE1C2chVsEwfP8YlvpQKJHktG+iCkUtCLGPAuDV8/qDSmIRPm4NggaTxFMVBQ==", "signatures": [{"sig": "MEYCIQCvLcrC1jp5auJoaSzwbRyLv+r95oyXR7ubAtFQmYQwFwIhAOkuU/kh8TX2NDWiqRDqEXOkVxTGe7+eUvFzvN0mgtnC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35005}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744259191780": {"name": "@radix-ui/react-switch", "version": "1.1.5-rc.1744259191780", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ec01150ab1f6fcd27aa740356c9b8e9a86c48ae4", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.5-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-AdBBa72O8in8FcBYXpcfULCauG3yL8jSj3s4c0bJN0R90UrPjZ3fk91ht4B2SAhq+dH1Y7a5lOjnsXd4qk9jsQ==", "signatures": [{"sig": "MEYCIQDVMQtBlxWr2OGRd1d1o/QgPCfa9GudVNdVYAChj/O4dgIhAPrnWwnJHcyDfCSWlIzwvxVGugJCm7GwnKKNEQtXJVhY", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35741}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744259481941": {"name": "@radix-ui/react-switch", "version": "1.1.5-rc.1744259481941", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f26334983de71077c15c20140fe5a37169d8a95f", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.1.5-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-rUh2zM4mVfZaO1jkeOwDnEfThgsZkyKIOK73t444mEE5lg4be0c7zCJh3NaDG9z/LNXyGMVZ71rGmaTv1JGeJQ==", "signatures": [{"sig": "MEUCIQC8jNIt0doSKkcHJP5H9lWeqOMPuixcd4wfOhm2kmUs8QIgBhxLgw5l3E3C243vxCcqBIhvreFzM1VC/Zm24Y5lqjM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35741}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744311029001": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744311029001", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "adda5471aa661437965f5c5f6395f62d5054c559", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-7SGD51Py58aRXCAj7lP+qSP7ICalblF3C4wh8oiWcpmbfWOFBSa11wyOuY2v6Q17giLj8lzogSHfbX8W2TMXpQ==", "signatures": [{"sig": "MEUCIE3r1DiOVSzLSpuwBpeLRDVCnmjkH39VI5XEStgLTx4UAiEAsDmhFFLNq9ilgYwc2oR2+oAITHn6slYsUC9huNcyXI8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744416976900": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744416976900", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c7c73604563c3a7cc23edc77844a39d0ac4a444f", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-puqbnpoecVmLED+FYiudQmmz7f0Kukg0VXLI/bXZwk3VpJP/0kKlt6cC6U+k+z/mVvSwKF6po6hZkGchTIUbnw==", "signatures": [{"sig": "MEUCIQDaqewi2uL5Zu98LCI8/HLsFk4j/MuKFxP2ZsyfvjwMowIgafCK5i750/8QwyTFjAbmf6zf7Imr01KF/aMslJR5jJo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744502104733": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744502104733", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "95e5ccfdad4c99c8c508e1b3810dce595f89f8f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-UKG/yyXX/qbtXPzYUGT0s2QaNRPtRJrmfLPVZd8OsvGtvGktsaOvJ87NP/YHsy63M5tLy/AxQkm/ECwxJkvksw==", "signatures": [{"sig": "MEQCIEZeWh5xlItVgAhADojDzJUrbKSMlm4KTZ/CHSzO/P6MAiBChMSO+OlbjC3h/Dy0BmHqusvXcz2iHqD06sX3GbCTcg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744518250005": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744518250005", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98ac275aa4d035ddd4edca06d292c81dc2410fc7", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-SSXD3SRec9TNYERJIqtrQbWRzyAnbGUIDr+dtsT94H6Me8MzAxOyXXgjiEXHUJ2Yu7U1aL9kGolmpLr1z829xg==", "signatures": [{"sig": "MEUCIQCId7JTWus/txpjvDhc7WLbdrsmlyU4+OwBikWMsL9tcgIgFKqgJq53e2hW74tKoGgiw/f8suR+w13/xqBSApMrbFk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744519235198": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744519235198", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9e4ed26ee44bda0858d48762ef5008463b9e5af1", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-Sces/EVmN99cc7SdvtB7wIZCjvvUXfT2EHU7//fP/nV0O56HVXUguELgs7RDiLNZGf/nB/dDAizE2+Ju4vQRnQ==", "signatures": [{"sig": "MEUCIA71qERU6SjGbWDVCESYHec0BPE2AdorfQDI7gTjw8LFAiEAk8x7o/Zgtx/6ftCg7LDPjWq/svypxedz0heb0eKRJyA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744574857111": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744574857111", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f3fee2dd5b6255916c4ef2063a44df716326bdb", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-SatUNtCglz0GW33fiL3xqRCo3WGASjngVBIxnULhhhvY4fh2+oSto4k1vlVAR8q+QT4VvWSgqzN1/G+k0rW2qw==", "signatures": [{"sig": "MEUCIH8PFwZ8QkqupmvZPptS/KVb6kTJcYlCkWo90PZE2IItAiEAzhTB1ONvI42+yPJLySsFQLPzdVjapCqy4+x+cjEj8Wk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744660991666": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744660991666", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f12a5763449534224e4c5002e9f5431a1cb16c55", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-jPH+3sEikL0US7UsVFLSG0oPoQMUia2QtwuqCP2iKGSwMnMsOltKR/WYCPly0ZYiM42OX9N6TyIRzFPGW12oIg==", "signatures": [{"sig": "MEUCIQDKq1ytX08uyRCCrnn4TITMK5qKauPLPClWiaqwRj9CdwIgTiqO6Rh4aDtMXJRnfBI1FNQHnJfSQZS1DshQuIvjVDw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 35608}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744661316162": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744661316162", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dfe07d21a37f6c1f038a866b29346166beeb6c00", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-4Fxw8WSdC8zzCnnX2FQ9UX3rq4YTG7hc1d2Iyc8yEuIpVx0IWcs5i/U+9T2+WXgaIejWfIeP9VW0M4PYAIZXZg==", "signatures": [{"sig": "MEUCIQC28bdNvR9Fpvdjun1+lmuECUKb4Sj312b5jZmbP3TzwAIgSkptcbCj5nBb0MJK02s2gnIp5MGbjDSiT+INArw2Pxs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744830756566": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744830756566", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a1c9a72941b016fe620273ae14fc35ef6382c3ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-MqYXk2AUOv2RugV3PeTi4AHGQUiNzrtTBGuSV2tih8T0gOF4TFm/k/64IWRjISepT7JTQIL65/PmHJAV4ktQHA==", "signatures": [{"sig": "MEYCIQD/JFBikTh+zQmegkFt9pTvR0eLsRnHbvbEohxsuNE0ygIhAJLPqzitFnHts+blSm6+tkyNLRA2+Wu3SEbkm4mnnmIM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744831331200": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744831331200", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ee709897870815af054f1bdb8e79fe253318d681", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-QPawOv0VR05MGRgqEjpWnZhyN5o1AxSh0Y7U3rOZ8WqlN52tqUE2BeTXJSWNKYfqSGsITGvnUL2DA2/YW+5vgg==", "signatures": [{"sig": "MEQCIAiOymqIftmsZySm5XnBeQADkKLSt0JYsJfXSvlq84xtAiAs8MBT9knpPEnoN9mMai7TVHoP1Y6z+XKgPkJ3efyPTQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744836032308": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744836032308", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9dbb53a6151533e0deb835c5254b7049783a7638", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-0GTldjle2VtM9ndJCo0HHsmKOWNjYlKQshsUVA8qzBLWSbTJNmTFdANZ/uoxjXdYoPIIp2q43DqxReXoySpI1A==", "signatures": [{"sig": "MEUCIFbf+uEtOmdNMDuyl4PYPmYTuwf60437+kesY1CGowg7AiEA3oAxV2UAGBLq6xzXJtWLqqy3eX86Eq0s5moB1lnBWpY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744897529216": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744897529216", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1e6c95aca4ee715ced7021d385f5074315ba6222", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-F2wtvSrOIUi8Xpx4hvJ0yJI4xZAfARXgvOKSDEvBmofLmF2d9QZbBxEJD1sJ/zxbeqT99vo5QORnUQVQZClArg==", "signatures": [{"sig": "MEQCIDoiJntbBEuyIqCaJfCKEpO/wkJWRaiBzPf9suKNcD9dAiBU+5Ks75GLBIYxuCPzLKxo0djvHuli0X2VIcfY1nhpww==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744898528774": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744898528774", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5ba2e56a2840af18622b8fb4235c7c25add96a0d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-JxHiyze9ninYzY2h3eiEOzIFLRsrwA0qfEVAaShEUATpz3L16p3gux7zxIkTIkZoPVJ1ML+3Mp+rpTUgtKToFw==", "signatures": [{"sig": "MEUCIQDRT5ZkrJckJex/ge0D7VWwvXutJ335KolS69uGxyxbaQIgVxGBKbpXvpBQeeKcfO26ZvtbsbDHrf26BmXz8cuhI4U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744905634543": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744905634543", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2bb95cfc7446eb98326d1a72c769bb41bde5937a", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-4CduydpmT8Qm0enQOjDNO0EuHbg8ktTaKq0fbKoyaRPiIFsrlC/8nEL/MJcubL8ZAu1hOWk+JuWdaV/LAd0/mw==", "signatures": [{"sig": "MEQCIFkUqU6TOQHKfC3JGwWVZNqssbqHTYjnYrhxyphtYts2AiBmnXhI6aE0LUSoLVlB1P1TAo/Bnn7ULvlw+eIPbyzvUg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1744910682821": {"name": "@radix-ui/react-switch", "version": "1.2.0-rc.1744910682821", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cbb36b38c1bdda495da65d0cd0dc7aa2c80eb059", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-zB5dNct6rI8pLuhrP8/j7lVIbYkgwShhU2DiX2HZz7wKtUgLQzqgPND4jSi4L7wyeTCa6qVLl/7x5gX8usO28A==", "signatures": [{"sig": "MEQCIBVyqI9EW9VcZiJ/W6mSN6S04annvd3g5e/Uq4acqaq7AiBMS42hTZTg031dicYHpFkjP5M9onkYd45YMrb9fPrsrw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38166}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0": {"name": "@radix-ui/react-switch", "version": "1.2.0", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ca6892046444aa4ce5d6b7aabfe2a76f2a0db989", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.0.tgz", "fileCount": 9, "integrity": "sha512-gbuQn/7CjA0uiLfZ+9WoSCfVeBkXXEhySJcL4EbhZZfDT9MztZ/DN/t9ciHflj0fxJlX5JU9UaYmm4/xnvtsMw==", "signatures": [{"sig": "MEUCIQCVRfELNSi7zE6jqcBcvECwcMueUz8W5dkvm2i3YxNuZQIgN0ut8poecSV4r21IlLrgSqbQvq58dSfXOS7NzBkJV7s=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38115}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1744998730501": {"name": "@radix-ui/react-switch", "version": "1.2.1-rc.1744998730501", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "349fe97dae5e0133803c57a861360c0d08a478b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.1-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-rBNOvNuLVoBGpya+YlWBH5ci/gpfwBTw9QQZ/kmH3EycLoRuE3Hf9zXemoDvtOCEhZC9h8j1dwDqiOwm3L9FgQ==", "signatures": [{"sig": "MEUCIA84IYUSwiAcG4Q9lIUhPt02FGirD/ubbo65/zgTo5UYAiEAuh+ZtsBNz1rW8ftfyVqo40jpuyDaDe5qF9Xolm+jHCs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1744998943107": {"name": "@radix-ui/react-switch", "version": "1.2.1-rc.1744998943107", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "501fcff64019bc2ba55599ad1fef340e765e9b10", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.1-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-d49P54Dq1kxG3bYRx3AExWCMUIUiuZRsAQMHZaCirnfGT4ln85VFVWJsvJVWeBhZw2UlYdZwEZQ65Zabd4PXSw==", "signatures": [{"sig": "MEUCIQCvK03mPqCY/9nPY3xZQsKHNQDDN+1+0w71/K1jfD+mdAIgZnghCVLT2amdM3v2nVimpsqH93zXd8N0R96ZkLYWL40=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1744999865452": {"name": "@radix-ui/react-switch", "version": "1.2.1-rc.1744999865452", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0003ded247595c83eb93355680dc20603c9cd7e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.1-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-oBwBTf1CnixpthN09FKV4DnVqAm7jv1IcP4YZxw1giHc10uOGdQmJskauj5m91jnitGkWtCyM9QfZuOaptzeSg==", "signatures": [{"sig": "MEUCIDqUid2GgMqgdaX09TYN7KfaQKqYJS1eYyv4HcNaqVgEAiEA8zaitAJFDU6dSpLUkiAi6mBLSI99majkgv+7NHD81v8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1": {"name": "@radix-ui/react-switch", "version": "1.2.1", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "82ffa1037531d0be01b9b20bfce20fbb4abd729d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.1.tgz", "fileCount": 9, "integrity": "sha512-AYGSGuEWLh4tylZ+7QexBI4XlkWvRjFiWRybkkSzOS7yvadaNp7vvI+oUXBPO9pspi/24CYEUml8HnKxMoGbuw==", "signatures": [{"sig": "MEUCIQDnK2AkBlpVh4khC/6pA5iHMue94b143hFKyYHV3J1MJwIgCeOw8AYVWQsMAbtAHro1M8V6RB1+g8MZANVCE1djAWo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38115}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1745001912396": {"name": "@radix-ui/react-switch", "version": "1.2.2-rc.1745001912396", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dd67220852f726d2225c01892f0425d38bc8007a", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.2-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-bstdta9FY4ENpvi1AU2EPI9Dm+z91ossuVq/UaQHND7I+WKI3DrATqJHhOHZFojnk+z6z3pvKyPxEYp/HHFlvA==", "signatures": [{"sig": "MEUCIQD2IG3at3q/zoR/OqvOkhDSzABigZ2btzG+R+ZG8Qda2gIga4hQ2Keig3zRQvpL6SDUPVqlQSIbYAPLkWZXSBcDSE8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1745002236885": {"name": "@radix-ui/react-switch", "version": "1.2.2-rc.1745002236885", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4b96fb41c81d27740d01f3ed86949affbffc0912", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.2-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-ZSuwv6PATATTsdBlbUSzGc6upfQc23+ctRbUgE5uKmkYWQksBf9pOBnRHR8hGMBe4R1/PqSqOVglpQorsU2BVg==", "signatures": [{"sig": "MEQCIHyLAULLW5hkvIXMXejYsutWbTtZx0Xy/EsemxR+93JSAiApd5JqBno9PWtFp9d/G+lYZvT4bNW3RBZJgzl7Yi7FTA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2": {"name": "@radix-ui/react-switch", "version": "1.2.2", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aee51a72b93b49d625e201e32c43deb7957e4641", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.2.tgz", "fileCount": 9, "integrity": "sha512-7Z8n6L+ifMIIYZ83f28qWSceUpkXuslI2FJ34+kDMTiyj91ENdpdQ7VCidrzj5JfwfZTeano/BnGBbu/jqa5rQ==", "signatures": [{"sig": "MEUCIQCSye8kN5K5vZOQEUOviAMasVrabREYlUHOZhnLn0UHOQIgdXqTVCrfY3l0sU+jzLuJL2I8Md6rUKKpxN6twxUZ1CY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38115}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1745345395380": {"name": "@radix-ui/react-switch", "version": "1.2.3-rc.1745345395380", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d52450b0dc66aa95481787b090bb49cd93188dfe", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-/gxXkOQtph7G0TIkB1azmYFE8gI+mjfiDzx1g8BryTygbtPOSVYzih1Houl8JZnUhmi0HdXaZo8I1TRWgI1q+A==", "signatures": [{"sig": "MEYCIQCJqtiaxDWGMM1TmBTcSOXS1fAJfTTt4iDvqnDtyUqtBwIhALJc3ekQKUHArfIFIeW0Ot6+C5rX7NiKwU2ZqMQbILUu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1745439717073": {"name": "@radix-ui/react-switch", "version": "1.2.3-rc.1745439717073", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e022f6d24468f73d820fd149c0070a219d26e57d", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-CBuuEyLvT+uiWOxIRHujKH5FgWJaQprLEIDXJwh271lCcDAC4cixxKsgzgzehtjklhTpgECZ37fOWV0M9+2bYg==", "signatures": [{"sig": "MEQCIEiQ3RppLM9x/GrNTUMx0V/Zss5OkavI226llMDaMIrDAiAGQ+8Es1KXAOgH3PdRr2B7PnTcslfv9NAJGS8LUS1gUA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1745972185559": {"name": "@radix-ui/react-switch", "version": "1.2.3-rc.1745972185559", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1a5a7f2759b21b4d9147619c94d20d5387dd8ed4", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-QL6FNsu6VCPwJY6DSATIxTZKw4B2tPN7RIVBuAEU6ZkSLWcOUYcELc1aqs0grcDSkEVNrMnrDR4WyGRSxwasGg==", "signatures": [{"sig": "MEUCIDunG/FD0HlfFpPp/Z77jlIe1zKDaOWrtDjenUa3vsn2AiEAh8j9rZXaldeD54TkPbqvK4VntL37W7qbU6yecJvV+mA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1746044551800": {"name": "@radix-ui/react-switch", "version": "1.2.3-rc.1746044551800", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f454b662cceb339633cecd00507ed854f7a251f", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-siJ36iDUKLOCgbCSmWePkGynRZxxh1rnemfVU9neDPctzvkxnmnPjJE79bar9F7uIrI93QBv7DDQkTw1t4Qu9g==", "signatures": [{"sig": "MEQCIHamsQFTbUTobM4vPxxzY+iMIQrbZ/cHR5p13UO5y87xAiAZduhhAwAjxyz6izeVTAOEz2wiq/X4RyjNl99geLxvbA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1746053194630": {"name": "@radix-ui/react-switch", "version": "1.2.3-rc.1746053194630", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ee93fb4357261f54887ab84d6562cc81abe2c0e", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-dFhLdDnTgUU+BBu5RnjIxhRrl+EfUABj8blwCPbqTq5dyg6QultABTGzrm1tYsxtIVRTxBrFeSBgw9/HjDaZSA==", "signatures": [{"sig": "MEUCIHbDfVHr6qWh8AHZPL03vL9Slxgm3TExg3xbHNmF/9jWAiEAmFv2E+LGBUwC2bVeQGUUUAeeGjmmvNbur5GnsO5jtxI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1746075822931": {"name": "@radix-ui/react-switch", "version": "1.2.3-rc.1746075822931", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d40d7bd48eaf6c50052b06fd9a80fbb2bebc063", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-ZeMUv7/86Cp7KmX8Xy0OF5o0BHRPDbbS9pbjY0nfWZMFaAx7O6K0EZleLjXg421nBttufysTh3XgCQ1oktRFEQ==", "signatures": [{"sig": "MEUCIGkpsuScg5dmurp6xI9rr8SCSJDTLa7cB2HdEPWkaXM9AiEA6tcEFOM2TQsh9GFfX6kDR14wNweC/DDf95Z9kCFbV2w=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1746466567086": {"name": "@radix-ui/react-switch", "version": "1.2.3-rc.1746466567086", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fca51da1c27968d41e3976d4cb8956ae291ab901", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-lCJpVB52USqE/1CZFR4I5vmrXMW2c2lcJ0vTjM/xloef01rChpIqX9NyYxwfObCoUJN5+JoxogmaCeQPf3LcBQ==", "signatures": [{"sig": "MEUCIArW8wD9eEukd38kbh4gEtPSx95Rn6mjPFms0jxjJRtwAiEAlYLBrbz7DGeinSRKdSlwjt9MB23QOLHEsiVZFJtX/AU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38149}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3": {"name": "@radix-ui/react-switch", "version": "1.2.3", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f449f61e2f8f43a380f103ad47d52af504f92712", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.3.tgz", "fileCount": 9, "integrity": "sha512-COMwmWGzcWK8St7ByBfcMfETXCuM0x4Uf6tLuOF6K65ivZl1wwtQnOBFqLnAFPXotclZD0rPChal5LzA8X9gqA==", "signatures": [{"sig": "MEYCIQDOIpY/iVjX2hDtQg3P6iIqgk9LT188FkB60e8AurmfkgIhAM+6B9Rj69miFVfaCNSLAggZ025CyZ6S13fyYEau3vP5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38115}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4": {"name": "@radix-ui/react-switch", "version": "1.2.4", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-use-size": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3318007c4147df69c04141aa7a77c034baea7169", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.4.tgz", "fileCount": 9, "integrity": "sha512-yZCky6XZFnR7pcGonJkr9VyNRu46KcYAbyg1v/gVVCZUr8UJ4x+RpncC27hHtiZ15jC+3WS8Yg/JSgyIHnYYsQ==", "signatures": [{"sig": "MEUCIQC/+FAQ9tAh+tImIjPJV+mjaz2KjtAIP7ai2jqz+sEzvAIgRnwc8zJo02WimnMk9mWc6hHy+kXtBqIGDKSy8WOfJJI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 38115}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1746560904918": {"name": "@radix-ui/react-switch", "version": "1.2.5-rc.1746560904918", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-use-previous": "1.1.1", "@radix-ui/react-use-size": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/eslint-config": "0.0.0", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-m+Xis2NsNDzjT2QOw1StjtLtVgBLijw1P5EnB66W7gH/O+2gpvkZt9x/XgSlfD6CjXxwbDK6sN2//LxTl5fzaw==", "shasum": "e5799e61a07ddf1a66eb3b1c72da3102c1744f91", "tarball": "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.5-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 38157, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIGAkfUR3Z/jGQdEttrSuvgdyOc2/BpNdv2zJ0AXY5BXkAiA3ayO0offghozRIQjf1JqwltilpYb+mRwg3lpxLbMitQ=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:10.128Z", "cachedAt": 1747660587974}