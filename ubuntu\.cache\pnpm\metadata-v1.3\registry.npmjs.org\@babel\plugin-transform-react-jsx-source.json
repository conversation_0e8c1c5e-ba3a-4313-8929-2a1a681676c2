{"name": "@babel/plugin-transform-react-jsx-source", "dist-tags": {"esm": "7.21.4-esm.4", "latest": "7.27.1", "next": "8.0.0-alpha.17"}, "versions": {"7.0.0-beta.4": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.4", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.4"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.4"}, "peerDependencies": {"@babel/core": "7.0.0-beta.3"}, "dist": {"shasum": "4757c6c94abeff547b6d0658c436e4933a854be6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.4.tgz", "integrity": "sha512-oL7sncQ72H9nGHxqKw7WngLeB80oK98j1006e4Dmr5Q1hGtb3kr+2UzVVQW2jrvONvZ21BgxzfbgvZTZQRDjog==", "signatures": [{"sig": "MEYCIQDszXr8V0K74cdph5hVHxV9jlCAGYtc1xxbOluTxs9v2gIhAOCAbZw/WDo9viyb8Y51yAgiIyjkgrtb0krCdena8lAK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.5": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.5", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.5"}, "devDependencies": {"@babel/helper-plugin-test-runner": "7.0.0-beta.5"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.4 <7.0.0-rc.0"}, "dist": {"shasum": "ccc8b6f407feca1acdff826bc7b9a7682676ee68", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.5.tgz", "integrity": "sha512-ae8q9g+Fp5ryRctpuJEA9JCBqLwRbyi2qw6L4tem3ZFdSztgyxB/nK47yLU/dZ3QSoTm0GguQQMUcaDFeZOuKA==", "signatures": [{"sig": "MEUCIG4anf6NWmMCSVd8TE5cxN0ooiHPkg/80GN6sPY3viwXAiEAltwM/7wTXtyaFsAFDt8eT3sBHtWEYqa4ZsJ1O3pxofI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.31": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.31", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.31"}, "devDependencies": {"@babel/core": "7.0.0-beta.31", "@babel/helper-plugin-test-runner": "7.0.0-beta.31"}, "peerDependencies": {"@babel/core": "7.0.0-beta.31"}, "dist": {"shasum": "aa5d971ee19d6699307bf204b1fb035df719640c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.31.tgz", "integrity": "sha512-DU8OkZXdTZK7BOoSV/TyCc5b4HZOpSEme9egCOrv31qyXMjHgAyyrGu4VNLDXuM5rXb1mCZLfI6tnG9uVFdDwQ==", "signatures": [{"sig": "MEUCIQDKwJXpf2NowibkAKxjgNhNWFB8Fh/+CDCZW0lq2rQRiQIgYR8a4jjGfdWmE/OCzaEYPHjYd8RKGLf5fFS8M72yD+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.32": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.32", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.32"}, "devDependencies": {"@babel/core": "7.0.0-beta.32", "@babel/helper-plugin-test-runner": "7.0.0-beta.32"}, "peerDependencies": {"@babel/core": "7.0.0-beta.32"}, "dist": {"shasum": "95f3e1199cd128d4bed0cb6d8bc3b85d130ec3a3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.32.tgz", "integrity": "sha512-+sfU3h2ffvGCw6AiQVIpMdWIpoMe1AwWkN59w/DYKJ+imPi6dNPVbyizUlNMhQ/FN1Jsdr8NJm6Mf5FRrvDjtg==", "signatures": [{"sig": "MEYCIQDSwHfqdw7edQjUktkbe2L3w4s4azjJm0qTQ9Pu79VzpgIhAPfDbgit4T2HRcx0iIw5+8ra6PJjVnOsv96WJo01zqvD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.33": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.33", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.33"}, "devDependencies": {"@babel/core": "7.0.0-beta.33", "@babel/helper-plugin-test-runner": "7.0.0-beta.33"}, "peerDependencies": {"@babel/core": "7.0.0-beta.33"}, "dist": {"shasum": "9131169d3c863df9d4ae52ea87510e6d005cd575", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.33.tgz", "integrity": "sha512-cD9HtcjHGkVlS+f2UBhxxX6gCxu/SzMK7+2QMsXSsigub/DkheSTYl7TEV+MB8nIh+Hdz10l3ndIRrRTfXrQnA==", "signatures": [{"sig": "MEQCIHDr26TSpmIC5GvcAlsIhEO8FW/fpAVPmbfa6KPtmis6AiAbZssgVwSl61fxYjukHydKYpVmO8gkC+o5h08xosLasg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.34": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.34", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.34"}, "devDependencies": {"@babel/core": "7.0.0-beta.34", "@babel/helper-plugin-test-runner": "7.0.0-beta.34"}, "peerDependencies": {"@babel/core": "7.0.0-beta.34"}, "dist": {"shasum": "53081f5e4ec9fa76ce3f882297b6af895b7893b3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.34.tgz", "integrity": "sha512-2AQcMrZCACdAHhXIwopIyrs/UrkkL/NAJbKWuX2otiiw0f6+VWY6Y/FbPgYqMEGIkkXXgI1ctC2DT9SqwXdR3g==", "signatures": [{"sig": "MEUCIQCE43N92xSU9DuPIAEBjYue+Lv4bXc2vcnr9m7L8lSU+AIgCD2jtofp86lRdATCc//fg1TXWe2Ui6hom7NwmB8pxnU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.35": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.35", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.35"}, "devDependencies": {"@babel/core": "7.0.0-beta.35", "@babel/helper-plugin-test-runner": "7.0.0-beta.35"}, "peerDependencies": {"@babel/core": "7.0.0-beta.35"}, "dist": {"shasum": "6b12fb5db5e57c8ae0448869c7aa9f89243f00ce", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.35.tgz", "integrity": "sha512-LpC3bCraUue41rxJcyNQyVb8asDS9T6PWngE5WKXUB6qt166ssnW2i5xFgO1dMDQAfS9fvH7+6ZKIN2oSwAWVw==", "signatures": [{"sig": "MEUCIBsNQsJyiBQuzjAVeit6x29SBt2SjNQTAMBlFGM2WZmzAiEAxfJ5kVZKdjL9akrlqqXGCbV23BWFFWFe8jQJx+eOR/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.36": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.36", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.36"}, "devDependencies": {"@babel/core": "7.0.0-beta.36", "@babel/helper-plugin-test-runner": "7.0.0-beta.36"}, "peerDependencies": {"@babel/core": "7.0.0-beta.36"}, "dist": {"shasum": "ee186e8de63a052f2e4da79a543523949f9e89ae", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.36.tgz", "integrity": "sha512-18xSKVZr4Oxm8Hzw8Fc2G5bX3qUZ8QhYml2t1938br9FjomzsgvpOscaiXGXVBpyUFIM8PHT34D2w4Uab9AUhQ==", "signatures": [{"sig": "MEUCIHA5bcJyttm2/9S46r5ZYdsjc2ouCles89j6EPVprxoHAiEA+p/XHYQK8hxMF1Aoz9EHUKBbaYUt+dkBJW3FlDsS9tM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.37": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.37", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.37"}, "devDependencies": {"@babel/core": "7.0.0-beta.37", "@babel/helper-plugin-test-runner": "7.0.0-beta.37"}, "peerDependencies": {"@babel/core": "7.0.0-beta.37"}, "dist": {"shasum": "a6421701be3f910498cddda137a60baf3503a8fd", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.37.tgz", "integrity": "sha512-AGvYWvZ5yKz0ma169momLAs/zEUt1X4ib1IDYTvPzbV5PBgOII/f5hiHf6BqRW+EFBzgK2hKq8e+asviwDVjoQ==", "signatures": [{"sig": "MEUCIH/7yVTzlrKPM5paRrtj9xSXztKp4AFZNd7ISO+u3CxsAiEA9/IU54cSkaSOysJIab4SQKSPLrc+/rvcCJ989KAxnnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.38": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.38", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.38"}, "devDependencies": {"@babel/core": "7.0.0-beta.38", "@babel/helper-plugin-test-runner": "7.0.0-beta.38"}, "peerDependencies": {"@babel/core": "7.0.0-beta.38"}, "dist": {"shasum": "05e4420dc2b6368f22ce83b29fb86a08ad22fadf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.38.tgz", "integrity": "sha512-7bO3+etqQQblT8xZea6T5MqbqlyienNcWpFYq5ZenqUIBW0zjlYraBE1LHATI6IVR77pbNb8YY9bZ71V48yUdg==", "signatures": [{"sig": "MEUCIEYm0UaJo9SL0NTOeOljnkWwidTRZJZDisT73u3PBFKvAiEAt5vLV+pAaFR79V/Q/5401pFjDnv+Rly5rohu5XmwuBI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.39": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.39", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.39"}, "devDependencies": {"@babel/core": "7.0.0-beta.39", "@babel/helper-plugin-test-runner": "7.0.0-beta.39"}, "peerDependencies": {"@babel/core": "7.0.0-beta.39"}, "dist": {"shasum": "632decbb1ad66592a3fae6cac3103b820bc89877", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.39.tgz", "integrity": "sha512-yYXrja2peiPWWnIzH7CmdsZkDuPtPF0Wv1HOOKIOE1fXwceMtef4eXOgD1mUo/eY3vsgJO2nTc1W+k5/1XrppQ==", "signatures": [{"sig": "MEUCIQCW2dx6WU5LTuh1NyDyItQJ36ahPkwVFG4cmCz4c9rV/gIgHik0N7k+7l/jVHEYS0FxkSG2iLydj+0MB0jnFM79Id4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0-beta.40": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.40", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.40"}, "devDependencies": {"@babel/core": "7.0.0-beta.40", "@babel/helper-plugin-test-runner": "7.0.0-beta.40"}, "peerDependencies": {"@babel/core": "7.0.0-beta.40"}, "dist": {"shasum": "7e62fe33f3e46c7f0d81d187d9c9aa348daa6488", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.40.tgz", "fileCount": 3, "integrity": "sha512-5mlAlw183Yh9EpoCAHJBFGavvN8MfxXz2pHx5koIg0xPznt7+p6JaH5wCT4QwlVFbDoHzfKv5TMZ4vgbsp6Hxw==", "signatures": [{"sig": "MEUCICNtJjYTqypmibSCDWBkROfcXjgf+2l/HpK4E77cmwkTAiEArU1yAez7g6uhXnEvdLf+2i9rttY3CoHlxrWYtPg1IBo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3024}}, "7.0.0-beta.41": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.41", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.41", "@babel/helper-plugin-utils": "7.0.0-beta.41"}, "devDependencies": {"@babel/core": "7.0.0-beta.41", "@babel/helper-plugin-test-runner": "7.0.0-beta.41"}, "peerDependencies": {"@babel/core": "7.0.0-beta.41"}, "dist": {"shasum": "65586d944c9d25ae5f033b11daddb816e75062e2", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.41.tgz", "fileCount": 3, "integrity": "sha512-wcEhynTjTTZZ9o+/vVkPV3CLQFmBJuI5I9YUXjfvyBx3yM2z3ZlnQyVa1yubFojXf/P4ZmhtA8/SAuSkv4Fm9Q==", "signatures": [{"sig": "MEUCIGDgy4uXa3ITieX6NgVO4xlWBv2noLMzNdo1BR/oMgKiAiEArEQ+2rgi6H+DqH7YcZMQZdtSifrOM2xZhz/BWs8WHio=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3236}}, "7.0.0-beta.42": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.42", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.42", "@babel/helper-plugin-utils": "7.0.0-beta.42"}, "devDependencies": {"@babel/core": "7.0.0-beta.42", "@babel/helper-plugin-test-runner": "7.0.0-beta.42"}, "peerDependencies": {"@babel/core": "7.0.0-beta.42"}, "dist": {"shasum": "2c41adf060e76b9f0652591cfcdaddd192a21898", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.42.tgz", "fileCount": 3, "integrity": "sha512-vLKqNyFDx4bESdy7dTWLmAhCTk8zszsxhYGKL/0FaFacksSQye5conP68j8jMEMn9M/JBGgxfFqL6tPbOn2uVw==", "signatures": [{"sig": "MEUCIQDwYnLsv1Y+ZbSCQ3LzoEerDp7kfU3YsLqvuhR3dfbb2AIgOgOzozcUb7U3HRknD7Bk1Pvj9HUkNBz45DaIzW/oxmM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3236}}, "7.0.0-beta.43": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.43", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.43", "@babel/helper-plugin-utils": "7.0.0-beta.43"}, "devDependencies": {"@babel/core": "7.0.0-beta.43", "@babel/helper-plugin-test-runner": "7.0.0-beta.43"}, "peerDependencies": {"@babel/core": "7.0.0-beta.43"}, "dist": {"shasum": "9ce66ede33efeaa045ae680f880751ef521c4795", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.43.tgz", "fileCount": 3, "integrity": "sha512-w3ofGWd4NLJPNi+WMKpEG1R6tKVFzQCTrlm8bdw0Vwg79UhlKdCnAC918+vT0FM6LYp5n/kap8uvEviODYdNnw==", "signatures": [{"sig": "MEQCIEv1plGoIz4plH7vaNsqDDoas+zb6I//4dLo3qLb9f+LAiB8XL0q8w1eW7FU/VoKoxttDILeCyoJrcDfi5//gH/5kA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3467}}, "7.0.0-beta.44": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.44", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.44", "@babel/helper-plugin-utils": "7.0.0-beta.44"}, "devDependencies": {"@babel/core": "7.0.0-beta.44", "@babel/helper-plugin-test-runner": "7.0.0-beta.44"}, "peerDependencies": {"@babel/core": "7.0.0-beta.44"}, "dist": {"shasum": "629101210cf86fe3cfb89a4278fb8d0966bdfc81", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.44.tgz", "fileCount": 3, "integrity": "sha512-t4CUDgbhxMCAfpAVeTPmSa5m9D24sC1A1DdyWwRLwleJkmuYcMwhwfjkpguOW9zEhaAhM5SUXjEc40WcttnO4g==", "signatures": [{"sig": "MEUCICEOD8yZKDqFT4uP1w6zY2EUpEOmIbrqo2aAkaXEZcduAiEA4NcNT3TlRKNlnmJ8loyeur6++yJbXmc6NdKINt9GJX0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3504}}, "7.0.0-beta.45": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.45", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.45", "@babel/helper-plugin-utils": "7.0.0-beta.45"}, "devDependencies": {"@babel/core": "7.0.0-beta.45", "@babel/helper-plugin-test-runner": "7.0.0-beta.45"}, "peerDependencies": {"@babel/core": "7.0.0-beta.45"}, "dist": {"shasum": "71e76a58c67ad8ba5634636bda973a32363b5d33", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.45.tgz", "fileCount": 3, "integrity": "sha512-i9UzUgPlneDoGHrG3RsMTG4eodWVO5cg+b4yF8NvfB/FTbnDxRuZPct3Z5VlYwZyTLx4GlDstFHBazVJkcWnmg==", "signatures": [{"sig": "MEUCIFE2dPdfduqne2r0Y1fhU95KZsd57EnLz08LyDzm/diXAiEAjtKOq8qbYinzmeETCHyw9GFDH4OsgdK69q8vjA+xp9M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3T2eCRA9TVsSAnZWagAA8w0QAJDRO+8jZzPkU+bD3iYh\nJnN3+A1T5qJsiET+az1aaCSEPsnCl52w+tQUTqovX0uFtQyGnXXAnMkF+1JT\n2AI34uBVy9lzLoxmH/hfL8vWvfN008f4ZJnB23VGyBFtqpa+6QYUZITxG+on\n6GfE2Zo+LOeJHx6yUqqQZuLEuaHe1pnpOR6jwujPy9rGzbjF5oUIUz8C5+FP\nyGLb3y48PLNItdEUMHLa9ioFMjMrOkqETlxFN7y3JL+Yz4fAMbn+ENVVBzQA\nWmaSbUaHcp7f+0m3a+Qsy/aixTRViUk1gmRwyPVCO2WYXmol5SDyQ/fogNSj\nhnELuge92K9K2vBAgYmWftZ6iKxkBOBbW9BsCtxYqJp/lpqM6/Wk9L5b/f8+\nyzUea3hbx6/EgqlEO5liKBH43HaZZV0DPU3k6I7EVw6gVJjCNvAble2e5OXs\nLHElA9khVHj7JSO8krmWYsoBBqn+9PDLBcxAfkr1ndAqOt9VXCFj3rgMhI5Z\nEwLobjbtG/vgw2u9ninG9VFutCOYxy7pF/xXTNlOdefrUa7wULRuUZ/tbQnm\nDmZkUnimysniXwyGYlnYENw4pI2stiKQsIGefbwAUFw/G2cFmzCxb+JD7Egf\njLDj5a1DtBC1Ce6RcHMB/AYY4ehqm7AOcXomuxUITqXD77y8yOnRGRk5sAAc\nXFYF\r\n=+Evz\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.46": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.46", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.46", "@babel/helper-plugin-utils": "7.0.0-beta.46"}, "devDependencies": {"@babel/core": "7.0.0-beta.46", "@babel/helper-plugin-test-runner": "7.0.0-beta.46"}, "peerDependencies": {"@babel/core": "7.0.0-beta.46"}, "dist": {"shasum": "5777f7bbfb6a13417896c5294d64aa5fc593f586", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.46.tgz", "fileCount": 3, "integrity": "sha512-vSSghGn+ER6d5gBtNnTZAxPxBSs1ngyyVlHse/geHSv7YnzmrCOUrtVl+t4M2/EO3CW2m8nkMfpnMW5FCmg+Zw==", "signatures": [{"sig": "MEQCIDexTZwIZy41oRdQZQ6DHR8F3cnn89/hvOotnRfdxacwAiA5GL6QWIiihyM59qGy2B8+Q2Xnied5TqoInhaG0utXwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3504, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3WHHCRA9TVsSAnZWagAAldMQAKHzxIWSq0oHdhSCVIjk\nekEtYXgtMUFMQi678E1IIrvm07TpFVmYMd8kQD+gzbZwITBMyqVuAtM8puuM\nXchENY8A7ZuEVzzRIV6itof47/9cegHXi9gUITgFP0O066uUKrs57jf/kjRW\nu6oN0kav8gxLtgl8dDYXjpFZKZfofny0VDdvftw5g4vVWlZknQdV8z2vthsq\nxkGD2zHnbTsyo6ccO2Z/wKu6hCaOuQy11GnyJqUX1mzOYbhHYIN/eeGes0E6\nH58/OJXu+eS/QGb7OwDTxtJVJNmHKb9JBrz/WOsIF3T0XUtXaBT+QitzkZ+b\nSqd2fjoPMIsKO0i/bglLzASyY5pwqJPCdmwHZivky1q7jYLBrkUrpeXgTaZD\nck5AeorAvgRs2qhHqAPR5smprfpk4ewRpXdk2MRbVkqfRHJgFbEap3c+4zzp\nn6dAiGDaCQIS05cBb0fbcFEtnHGyhUd7de6qg6+jD9NUllM090WCWXYBJDCc\nXxbzTBteygiesUlTP1zrG+sOPPJJYP0tINUTY1lsskcUarRfeOB2qgC+W56f\nwyRjMyjXyNUKv/n7DG4ie+9b3Fva1PP2YfJkiDNAhKLHahw0wuSAQ6WzX3vc\n+kHhSQlb0k93S8kStvO/kGyb567Nh+1LMmziCZTDfOyXypWvnRJWJYxOFuD5\n8TGs\r\n=VEEJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.47": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.47", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.47", "@babel/helper-plugin-utils": "7.0.0-beta.47"}, "devDependencies": {"@babel/core": "7.0.0-beta.47", "@babel/helper-plugin-test-runner": "7.0.0-beta.47"}, "peerDependencies": {"@babel/core": "7.0.0-beta.47"}, "dist": {"shasum": "da8c01704b896409eae168a15045216e72d278dc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.47.tgz", "fileCount": 3, "integrity": "sha512-oZ6D9z+qql+tz7PjGp1CaxepxqDQQTusyjeKsWr7NdEa0v2j3sWLkfK4Aa7kU9BT0+j+r/LN4u33UBkBNVoVvw==", "signatures": [{"sig": "MEUCIFDLDomknkRgpQf4Rw4xiQ4xAfT9ePOY6G9PBvmP/+8NAiEA5SOlOJz688scxovWnh69hsXwiEtqAVHToAweTcuY184=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa+iVcCRA9TVsSAnZWagAAq9cP/3u5sa56CdiHD42Qyphc\niHvB+q55uca5CjHPpgkrfUqabrqc6wt5kcDSWCD9r2K6msfUOH6FBQ5b12OK\nzLCR8hvDBtlpqaqC2q1sS6ZuWeGOSBHXU4vNIbDJG3QYdIDCONuWrN19sFCj\nAUB8OMc4YvnzUMKeDl1WuKAoI4cTB9/D2c5omD9VgcKJkyOBwATaHfxeOagB\nemrQ1Hr6tojjIR2gJnOw1980FUNujSFTKjnN7JzkrQhhvIo6WUfukSK2TYcG\n5Mg3GDjHOVv/Qde+AIAaOe5fVrKWLW7Ku+RfXaqeR484PDoVlYvE1iIyIeGK\nNO+HCUJka5CIqKxRJY2MNYnjDi1NbS6Q4fBq0u8/zeR67Wvzkp8TdUnM5DOq\nflDDDza33lR+hFS8jmzmOpkHvtMwgXSTY2L9H4Yv/ZEusvm2+/XIzXZoqxU0\nOp0XJYOGXLJ/Ujb8CfA0CFchwLeJRM6qLw4K7LI6KmCxCfcNfO6MDaFWn9T6\nikgPhR7ILRjcvs45DQ+XAHlVREB8IQiZPViecS6U6qBvTgaGtC1VFxHYBsGw\nKmCYBWprWG5v44BW67sGBdhpqOAlLzEo196iAI2+yzZM2vLKKSzZWc1RjW1f\nretdU+XlzsmYGGUxjKi72P6JdEZGEj0sZMqoLwJymdxTyOq7NDFfFtIqB9fv\nDImq\r\n=iJuo\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.48": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.48", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.48", "@babel/helper-plugin-utils": "7.0.0-beta.48"}, "devDependencies": {"@babel/core": "7.0.0-beta.48", "@babel/helper-plugin-test-runner": "7.0.0-beta.48"}, "peerDependencies": {"@babel/core": "7.0.0-beta.48"}, "dist": {"shasum": "8af9a9c9ae6b8515dcaa84d8f50a88e176ea5138", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.48.tgz", "fileCount": 3, "integrity": "sha512-wYH3U6fkaHaMFtI8rNIopv4h5MSTth3QKnZRwHw6nCaxnjbX6MkOfKWoxE24AH/7BfVlfNvKtidWKlPXr+0FgQ==", "signatures": [{"sig": "MEQCIESCHAJARX4SuxfvoBMGk82U51RLjxS2hyktk5awrF1yAiBrOIuGsspISPki0JNu9e3vK+7Bm2hhXFz9i/kMC4npRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbBxFSCRA9TVsSAnZWagAA06sP/2ix9XE9DtmNDyir8Da8\nbUWwLAyMYz9HorU7oUydMbDorTb2/FP9jyQ8hGxjh58Amzqbi6aPvegi7O07\neCJovls0ZWtOY2aoUyW2+VdAs4x+7UotAVEr07TBUyTpWkL5WKMQALX5miSj\nu4SQVUK+DiYwGxNCKL6nOJ9VRLt+PbCpnLPEjz75MuEHuKvlCOQSCqSj8AA4\nV1KdPU5b8sQYms01tWvUn0Jn0X91vMfmH9Q741xnracDYFxm38LRdN9mQv/Q\niyag9SeE2YGYLOTDYvfjsGuu1ZzQTv2UIdTtVvKZWGUsgFZKTvRVIzqL6Lb9\n4XSldg/sUaQyCVQeQQTU6uFSmWcqlsZSbH6HQS6fW20DAWvlrrCAPMoIxHew\nCNp8AX6HUZs1VuNGDA8hpW8xSVn8vr6dRsJVIEvessp+s1ljt/ulxX2WwPHE\nDfxKaVLTVIccd1h6EC8jHx6UMnlIhFzZVYi+LgNlZqndcndfZbdGAmBqN4zG\nLCg1Bj3Z8P9Yzq+laXpEMRtUDw7L8eSZCL5q8HMdKUt4tPS9bwBIP+qn8v43\nH0IyFsLVH9yxjhMy05WaVmlnHtThmZoAZFUvuI4a4O5KmEafEMFB1AK4hI2w\nW+Rn+nrfY1jShIcxO1wJ9jXzFLu2eVZxOYQ/iVl9nY6HNlrHyiE0Vcu5FbCK\nUEVG\r\n=5TQV\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.49": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.49", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.49", "@babel/helper-plugin-utils": "7.0.0-beta.49"}, "devDependencies": {"@babel/core": "7.0.0-beta.49", "@babel/helper-plugin-test-runner": "7.0.0-beta.49"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "dist": {"shasum": "05bb7429b6dd44cbdca69585481347a809caa8ca", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.49.tgz", "fileCount": 4, "integrity": "sha512-5F1Hip3HULxdo+bVrTCGMMXu9z9UQmmDmrtMFFBBf2kRqqzGpXUeyB9JNYUk8pCBhMay6w/92eXPkjoRTOhbnw==", "signatures": [{"sig": "MEYCIQDEWFk4N5BJfzspt/rKe2iOjbVvFrIzE97CWwvK07/qhgIhAIoRwSl79qq3CmSiWBsIWX8zMwcignjidil3qr+cpZBo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3482, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbCDPPCRA9TVsSAnZWagAAeikP/0tZ2wUXG55Csw4qR9bl\n8BZUE2gZXlIbxI2c9F9A/oLY93tQRO4fiGKRpwvYkbcNwkPAl+7DPZsuB/ZJ\n6b9VjVtx2y5ueRw41UPhaKG5A9uAU3pMbHzICiJWXTeAsMGsFr0R9AqK1mCg\nlitNxVcDyiN6MjHsLsKMbfAH0T4oEeryWxTZNfTIZ3XymbzgjyNBI7Cog+63\nF5ivwtq4DLY5l3qAUQbd50xz+x3jXM8ZAPYPBhAqZMAjGRYxx40mrIMJgTwW\nWgA9uKTIvDD7GeX3DIa07bMnrTZAvQ+y+JQjyHqaOyVBuq1mbHr4iYC22WWE\nJRGRuxi7g1tcq1c7GKM2r1lVrLgnHwyT10+Y24lI5pmbseas++ckBPkeVbek\nBhCBke2HTR1scA54XzgYCsgCGsvFf/dRlYFjsyTaN12WFmvgVsBkLJfBRzWA\ny3ufng86lliAWCfvEgCQDK23B4+Ftz/wwHHl+7iUzImPA98O5X2Lvons4M5D\nqBF81wo69NnXVPjB9ZunKcGdmqUiBfXX3OqY+RjrySHN4Kv9NKBJut5d5qAt\n/Gsp5DpfPI42R81YM6cp6kpb0ReDuGFyWmawaQSzDvHGVsMawz5mTp+Por81\nGwGW3jUC3jZ1e8u1iSHh7SoQwliD6EADHanwVDdgBXwySyGPyoOxx2ADsrsp\nqtNt\r\n=oB85\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-beta.50": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.50", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.50", "@babel/helper-plugin-utils": "7.0.0-beta.50"}, "devDependencies": {"@babel/core": "7.0.0-beta.50", "@babel/helper-plugin-test-runner": "7.0.0-beta.50"}, "peerDependencies": {"@babel/core": "7.0.0-beta.49"}, "dist": {"shasum": "c9be2c33441d19f196cb4ff75047c264d5173320", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.50.tgz", "fileCount": 5, "integrity": "sha512-3VrZ8DmLarXwuq2kbTqa9CGeH++iNUAL/VlPN4W+pzOiCFh9UjY1XoKHLE//AI0MRtfnXXA7DNgn7A+f8iRMTA==", "signatures": [{"sig": "MEQCIEndRa63VsuIKtM6AES+S3rCSsYwQuQiWiADgh3yQTybAiAbOKk98hPD4Cy2MIupw5ju8A43EPYEC4VZ+QAaou++Tw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3215}}, "7.0.0-beta.51": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.51", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.51", "@babel/helper-plugin-utils": "7.0.0-beta.51"}, "devDependencies": {"@babel/core": "7.0.0-beta.51", "@babel/helper-plugin-test-runner": "7.0.0-beta.51"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "6999dc491c8b4602efb4d0bd1bafc936ad696ecf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.51.tgz", "fileCount": 5, "integrity": "sha512-BYm/7aMyI5IHtCaEz7HgOxmDYylBKceU/EJEOo3XG4tBpCFd/Omg4WFIykBbnIZnyMrF1RZNsa/2KliWjQgXbA==", "signatures": [{"sig": "MEUCIFytzKh6uhUr4FqzqsXn+CSp/rvaj+WuJYsyPTvwDkHNAiEAmKvBrsKYnmBkqKY6+r1/fZFNaJ3anmv9eMY3gZoRwo8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3229}}, "7.0.0-beta.52": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.52", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.52", "@babel/helper-plugin-utils": "7.0.0-beta.52"}, "devDependencies": {"@babel/core": "7.0.0-beta.52", "@babel/helper-plugin-test-runner": "7.0.0-beta.52"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "625c5c007062ced46c46c24ce8aaff447a2e8939", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.52.tgz", "fileCount": 5, "integrity": "sha512-11HyQIWuiv5wgd1Vaag2LlMM07hUv82iEwAH+rqB2TDjRRu35A6j68kPySn9oEe+R7plXYiaAkSQuMqRgNUpXg==", "signatures": [{"sig": "MEUCIC2fLXrmhpisrMOWMx/x9EC53oMyKB6sTk+nz86Y8VvnAiEAl8rAR35UsHzb1XytWsD9iBJz8f9/Y6l43RBzf6lrX+s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3228}}, "7.0.0-beta.53": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.53", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.53", "@babel/helper-plugin-utils": "7.0.0-beta.53"}, "devDependencies": {"@babel/core": "7.0.0-beta.53", "@babel/helper-plugin-test-runner": "7.0.0-beta.53"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "d57605503dc07154e7ba3e693d8affaf2d4ff036", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.53.tgz", "fileCount": 5, "integrity": "sha512-eRaawqqhoIcj4ajo9RSxCFS8pegaAU0P+j19fBcz8obJSnjkedRg71fV+OKrDoH60Eld38pDUk1D4+tCJ1hNxg==", "signatures": [{"sig": "MEQCIBbyi3sEg4bbls0Wzhh9ftlipnUhptJ7Mr8QQLh6UKqMAiBtjAWat2Akn/nHDF6FJQ0+j6kFiaxwR+6DiM/lHKkvFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3228}}, "7.0.0-beta.54": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.54", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.54", "@babel/helper-plugin-utils": "7.0.0-beta.54"}, "devDependencies": {"@babel/core": "7.0.0-beta.54", "@babel/helper-plugin-test-runner": "7.0.0-beta.54"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "1d6eee65ff772fb002b995e538c0c9615ed6a3f7", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.54.tgz", "fileCount": 5, "integrity": "sha512-iFI7eifpu9+Adzvp9QS7tE6pkIo7eGjEjjTMw3DoMHaaSOI6ptitUsQhaFvBh7T1FBkegJEK8IsiZIeGbAn+OA==", "signatures": [{"sig": "MEUCIAEpm1zCINyfgBjohmamg2wJvM2rd0+q8hfeuqf8o/m/AiEA9eP0BtmPOY4yvhgCEK7Jn3m0iNapQpPLqqiaW/mEIHA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3228}}, "7.0.0-beta.55": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.55", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.55", "@babel/helper-plugin-utils": "7.0.0-beta.55"}, "devDependencies": {"@babel/core": "7.0.0-beta.55", "@babel/helper-plugin-test-runner": "7.0.0-beta.55"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "49156fbe8a8846b51888b8b8bffa486ad8486b70", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.55.tgz", "fileCount": 5, "integrity": "sha512-2buAdMA58ZJJ+6WL3tUAzfsgsODZQ/9FQU9MdusYWlWTgXK7ZxlHtBGU8aD28nEwJyvAS43PZILFAZkys5fhrw==", "signatures": [{"sig": "MEUCIQCkznZFlmVwhRZN9+mf5IYcDdrK1qFVBEHmAKa772YWzAIgWyucTPRU4NsGt4Az4j3oFA/p7lYmElrpsa4XKC2CC8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3228}}, "7.0.0-beta.56": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-beta.56", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-beta.56", "@babel/helper-plugin-utils": "7.0.0-beta.56"}, "devDependencies": {"@babel/core": "7.0.0-beta.56", "@babel/helper-plugin-test-runner": "7.0.0-beta.56"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "fca2cbf49dad9d44188d804905223ab3fddfa9d5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-beta.56.tgz", "fileCount": 5, "integrity": "sha512-HQs16FPkc7B+XjvXLHVX95ouz55AiNPStUgAngeHOjNVq7veZ7CtYP2uzdXWUrl5JsglFeZj8wa3+zUALhhgJA==", "signatures": [{"sig": "MEUCIC2zc/eZ12M+DS8YCrSsLPlIWEXA3Ke7WBOfv0ci4U6CAiEA8Bo9o+IgZ2VjrBLANt/J4uC/r4cHccyd3OqNJMpiQ5g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbZPxKCRA9TVsSAnZWagAAtS4QAJ2uOwycs8Vrjt/d+oVr\nx5edZPk5khwI3CBnT7FnkObsaUhsCPk+dyd2WO+v19WLa8aSO6/BUYHP3M12\nMmkGCC+XPtEuauujJocxidholEesDlr57MYw6u+Szc6i8Ffwr3mcLV6pIhFA\nQuL1YQcTxtxISbTpt66aYLSe5pzOUyyIA5Z1lI7Z8C5XGumnd/MG27jwb0w4\nq6gMTXMEZJUJ07mKIFcA9yJ3ubm+o7vuE9HO5oBVmpg49hXRpCx15sIWy2ss\nXMuIp2i9QpkxVxnEUmCIVuUP4VA+oZivuO/ASlvAnlAqRF1kZPBoA4DP2rdo\nF/fxOldc8np4FCJ0Q1VJ63Af4F7vAl/ZF3FlSTMpC4LIXh3j8HfFApM3SJlH\n/MQNbq4lH2AyYx//P7Uk+PHHG7DhPMzrN6b/0iCe5YtWaJessmJlYa5OQQGF\n+I0UXgi8PDrZ+Nqr+Pa/rUYx0heHCjbw3CAqJLP1bz5zjS68ioXEOBWjGUih\n5oE/9a/FInHzJhi7GSgv+mY+g4JZcruo4S05Lm4dun8czCJZQPtFfYoJbEWj\nR/AoA8JJnuKvtEzKOue2b6+rEW8DiHmT4sWDYCkqX2e95WECB77rGmiXd2/k\n3VrkArKQ1ft6iwI722PsY39wIFeN+H/XytQ9OrGH3p3Kc5hF1ZJDRzPPcIbZ\nkXgq\r\n=JoHq\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-rc.0", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.0", "@babel/helper-plugin-utils": "7.0.0-rc.0"}, "devDependencies": {"@babel/core": "7.0.0-rc.0", "@babel/helper-plugin-test-runner": "7.0.0-rc.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.50 <7.0.0-rc.0"}, "dist": {"shasum": "e952a3816f658773707c88a6aa7fab4dcea6a2ed", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-rc.0.tgz", "fileCount": 5, "integrity": "sha512-iCrJRn8J3pQfchRnQ56nqbSosFCma/ALurTSE62bBdlrE9rqYt1U8dRil5pcDKxWTVF36jIgn04hOVCMVrDcNA==", "signatures": [{"sig": "MEUCIQC87RsfrbeAm1QiMenyjDGLnqM5CECeXD2CPBrqveTkHgIgc0YIx8KFp4y8Iwvroy/KnOS/GjnzCHARqLP0AHDwBjI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3213, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbGTVCRA9TVsSAnZWagAARp8P/iXpX0l2qWa5ve3OdcQo\nCR1vMMWVhLIVsHEb9305u+qE7UJga6QsfnNjCtXB36IzPnnkScRj+OifFavt\n2ZFjfyLSDJyFCk3LBnMtt18a/f3xa5+7lsMhTeFutlczmngafcJ8lNeECDw/\n7buP5egsyJLfSizbOM5qk8ihEv6qRWiDg9UslfYmpxVkcRF/ZfBCJwFddt34\nFsSBNRvFcmORBncBqynRN4mCStlO2PKW8zIZ+uLmAFYSFgqkWE7XkaJB25gu\nwQX1SRKmLlBoVVsKncRw0kaZoKUNr8K70NVjH1Nrw6lxCdStpb1Vdgm3lAWv\n48fgw9DL6Ka8KRck6fvXCQwyBFOkgCci+6lvChlcEi+ocHscUOcQU/swsdP7\nvzQT4ex5jpoLd7QJIrIdMtGWOxEDmNzyUDtjdX4ao55EzaTXmnj3lp2ZJtKV\nHqtomrT8FNd0fbjFyLPt3Cid9GsQ4S9lt6VNAbuDZzpNJvBcBdzze/+nFsX/\nFXEE0Y/cudPMK7LI19oIbRoY/D6NmBxCnBS73EwVW8g3KUE3fI2gkHsPAXKU\npzH50c98hMYgtr0SuWNjtaIPYG8DSdMeUaovq89xzpJ4WDzGsdPLAGL3UQAr\nINPc6AkbQDdcvdgurihjmCFIglCagv+VofkdoJ4smdnOSA32QyFGrdCI7gT4\nOgde\r\n=lXNS\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.1": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-rc.1", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.1", "@babel/helper-plugin-utils": "7.0.0-rc.1"}, "devDependencies": {"@babel/core": "7.0.0-rc.1", "@babel/helper-plugin-test-runner": "7.0.0-rc.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "48cc2e0a09f1db49c8d9a960ce2dc3a988ae7013", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-rc.1.tgz", "fileCount": 5, "integrity": "sha512-M6cdiYTNWzqlmaa4YYpHTAp2N6tnROMCkvdy2eD9STHA9LpRz26fRQtbEc/kYL3MXroK2DEZpb8Zva6kczgbNg==", "signatures": [{"sig": "MEQCIEixPfOGl5MUsIYz6LTnVOqY0lg6BZMNBl5gZSJCLCMsAiBipthj5C8p9XFnF2LL3gN0UOQmrwR/nxov5QY5iaRkew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3194, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbbJ9gCRA9TVsSAnZWagAAzNIP/0aGob6+gAbjwIkhsTsK\nlYcyP1gJKP3tvP2KOr04uNjN7QVf5le9u+m73xa8XHoIXr4bMVlKwY+poi4q\nLEvDPm7wkkMJdhVtX06kKwthwhEVHzM06qUgfwNdW/FNJQMC3ns6XvmkMRuw\npEJ2cPFdnnzFzTlfXG4erwtivtvyVDWQuXQjP6HaJ62e1Q4KORwV0JHBom9V\nGc6rkBf5/wAr59bE4bSFUcE4mVpTvDn04gK6OVrXBDvJssLZ8RYp35vHmQBl\nIICG7WFXvkZG4EG28h5P+lVLKg6YzZGK3EN+6aoj4pbRCvsdy4EtJXVUZf3B\n/CP+oaiQgYFG35OkpzaTck3PMHaczKj5HRa/E1ZqVP4hwGbqQj7zG2g2U5pJ\npxz335A/zPHIWdGJu5ZNs1wdgtlZUc1OjR4mAexAMzslwQR6toXtvT2W6Fez\n84wsNCcMowJ0S/akP4QOl8U+qX7pVZJ+BoKyVFDNcguAQfaQ/jdmpi4xpp4b\niJ+bvSnYw0ZGV6kIDQ0Kn6lBwldFedXkJZA50IPOj19HqmnmlPxb3T8x62Hu\nUbqzrs4odKWKiIoxgwWyGU60vGym8P1OAH4efYbzEk87N1EP2Pm6j0N06Rcj\nLvWrI3FRHge5/2RXlVFCLpZwxz1oI2sCfw3vTMI43k99On9ZuD1SK0AgZiXi\nH75P\r\n=xUBS\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.2": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-rc.2", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.2", "@babel/helper-plugin-utils": "7.0.0-rc.2"}, "devDependencies": {"@babel/core": "7.0.0-rc.2", "@babel/helper-plugin-test-runner": "7.0.0-rc.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "b67ab723b83eb58cbb58041897c7081392355430", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-rc.2.tgz", "fileCount": 5, "integrity": "sha512-N<PERSON>ey<PERSON>dYer44AOf69xS9VK5PpOLXTZrKppFuixmITzDUaQioELFHGwXpGLV6GuPnoS8R//jL8cK7awpo6vYjuug==", "signatures": [{"sig": "MEQCIC7mY+kjnbuPPUWArU0HCumu0CImVLCMyfE/NMzKRa9gAiAZnTi17awH+WRMZil4BDzA9Fu3DQy/v12lGP04ELRZBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3264, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfGcWCRA9TVsSAnZWagAA4HcQAILVZoGDblktwAD/GqHF\nh6H4ZCR/eZbCtNRMjNoBIeJR++bXEWpyTkHQcgaX4QYB9RowqpQiGTFy02CN\nqLfRp73IVwbRQ620sIv8UO3uguzD1AMU6msvrg3F1pJp3dYKJR28QnGWRHw0\nC0P0hE533Z+ViCQkr9EkMkm2+VLdxDAPCo+fTzinVtWMIILx6vR0RhzoN/QS\nPYdolCAvwgIGmeWTlAp2SPmz+8bw4PaG/vUfRn1xlk46mK+fHXRFctPJwJB0\nEaVFn8WKP1x6b1r7cAdPjldqgEziLoqCi/maRaNkekjjamTjJ3hVHIUzbkmm\nws5pv7XlhKXkcDFuSwhqznY8C22S8pQO+0fCgZbApunOr2DGYjdgXtSUNida\niZ9hZc+lhhHTQopUwoyAGbQNM/n+6yf+I/IqMg9sGLdfcyLc2c2Rkx/IB+sx\nPBDdBwKfqVeSjG84rJuKBzafWMyvjXKKghxtDMnHg6gKyGGBHPtv8PkqpJA8\np1bfkBW69VgHuf6FTP4rbFWhySnOv/joqR2jr5Kg0Hc6j2InrbAnKn/RZ7hQ\nXkR2uAHFSbhDG7Vg5VvwqkwfumzLGk7e64dzhxDdFPNu/9fkFWlug/4ihykG\nqiy1QI9aB7BLd3lCda+Rh79ojzqT+uH8/bDY7biX4KghojGYRja3R3gk1mZE\nnGoh\r\n=hzGY\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.3": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-rc.3", "dependencies": {"@babel/plugin-syntax-jsx": "7.0.0-rc.3", "@babel/helper-plugin-utils": "7.0.0-rc.3"}, "devDependencies": {"@babel/core": "7.0.0-rc.3", "@babel/helper-plugin-test-runner": "7.0.0-rc.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "6c452914279c7361964ccbca32465d9cf02a9380", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-rc.3.tgz", "fileCount": 6, "integrity": "sha512-0H/UKuz/z3Bkk+Uy2GaeUALfLTRiCjL38YyD0UX1X9TJYC2HmIxvyaTqGfplcu0zR4Pgf73wze/ky1cmVLydoQ==", "signatures": [{"sig": "MEYCIQCW8E5sYWegP8AjAkADk1aiJdEk9UFRO4rlS6eBfqfySAIhAOcFvANJvjLJtMSSd9xSoE4g7yryrEuGGJK2EIoDsTMg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbgEm1CRA9TVsSAnZWagAAVMkP/jiIu/wb2HJxurjCkFdT\n9iClLza3g+2n1G5RQonZy9hw9kgoZZdXPmoBNpdeKKTyevpiI2HEeZUkqSHC\npNpD4LxsE9ss914kTNGNrV8h8uACUsygKlIa6G4dFXvX5fBruIUl1VWH4Q+K\n+ozZxPvTjo5v+5s3jxU76p32VRoWWGkBfi5I200PSV4R2qlSr/YM90mFCTnZ\nSowaOMx4QrJlQBR6t8wTF0CuQdLeLw7BK80tRGNlE0t+snbky1n21Z0tQeCQ\nyTYBwqVeyCGG2ktjxqPo8NEpg1uCJPHiQOtgBZQdvgBRpHC4CmIS94wUxjLk\nGIHLHyznDnIsv61F/VNblIQnJKPY7AVHk3FU7W2fXVwk+GxY6ASPgR6tmFgG\n5g7dCaEoaVUAKMGZY9uwhYufv0Roj85Ne4sxmhF3B8AfnFeCl9U7MtZVRKMe\nwwyKqNOa2Rr4ioryLuF5wj1Y0uS+oZ/uFArg+BxkzmhaCBqP6ALaO3uWqIMY\nxfn3H0yG60FgxNleYbfeHI1b/z6dZdFraRGOCL+7hik06T64Z7iIDWNPUmsx\nu2OhfKKAVygtGVOxl+FA9mQzEOb5uCj3JCFH5Nm4mPHkD6+nRRANI0DmTpJQ\nuuYyVkdgBKk9oU/TW1HW0LDMOKOaIQOuriAZfkrtfvD2JbJj8N+2bNomHhor\nzU3/\r\n=6YmY\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0-rc.4": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0-rc.4", "dependencies": {"@babel/plugin-syntax-jsx": "^7.0.0-rc.4", "@babel/helper-plugin-utils": "^7.0.0-rc.4"}, "devDependencies": {"@babel/core": "^7.0.0-rc.4", "@babel/helper-plugin-test-runner": "^7.0.0-rc.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "8db576ca07f3f8ffaf64a3d11754efeaab7e0f45", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0-rc.4.tgz", "fileCount": 6, "integrity": "sha512-CFQN+B3K5HPID57F95wd4Wu5j1WUBFIL/Upnbf6RJBIfzYUnAgfbn7gzNvi0s98eDBuVZggkkGzBkY10CtUhXQ==", "signatures": [{"sig": "MEUCIA4E6alrlO1vnyrLWXnLB44LLeu9flRd8WtxtDuBlpoLAiEA+QS7SP0XJN0xUqn+5F247Lkks8nLTurNC5feThW4xyM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4367, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhCq9CRA9TVsSAnZWagAAE54P/jagq2zv1SonC+2rmFpl\nGUgw3eUB4p5oCRnigLHlu0d5ip94dNdRSGOgLmumhGzgZM2ZP41Uh2hFb1Hs\nEAJTcW80BGf5nB9gGwOUbLNkwy/9IGZDgZZ6OJx0xBLS6uhtTdp2izXZWQ/Y\ntn9RDT8N0JgORbckY2dLAc02BGokjL6NfgOCOEMLmHLxV1rX1+B+tXCOuh8z\nj76AqF0H80uHe5pPWfPsECZk/g7LnzEp0CR1nGHD2MuEaGKCaU/Ipex9O8p2\nFCikCFLAwLDYhvH5XnBbIDxtgwNzx1VHVO5tfKwUjgDWGphwRxUd8QPcLC+n\nMGOtXY10v3Ss9VBfPyqYMXslABiuYzGGfjUsqDf5wRMfbqszrTJ7Bql31WlR\nzQzZdyEKBD2OetcL1ZsYxtwrjXFS7cFP2FoqVTyh1+OhX8+gA1oWxmhSDc41\nj+zc0YeEHmpo0cgAPLbQCWViFhEoQoEMpM5a7u5Go9Hk0bO2nr8HKbhe3Zhd\nUokzScI+m0UXEu3J3zb7i2enDIeVACyuQXQK9srxaL2b3YOTeaOxKBkKFwgt\nMlt7yv8IXcxKz/WaUto/DX9mzW5uKjLXYLAF6VaDn6KpoGKqt/n92+WVYjHs\nSSNc+74uek0zSa3kfPQOCnPbdAJytcLImgleuOjL38PEM4Pz4ySQP8LwzPma\nz3XT\r\n=aM4l\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.0.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.0.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.0.0", "@babel/helper-plugin-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.0.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "28e00584f9598c0dd279f6280eee213fa0121c3c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.0.0.tgz", "fileCount": 6, "integrity": "sha512-OSeEpFJEH5dw/TtxTg4nijl4nHBbhqbKL94Xo/Y17WKIf2qJWeIk/QeXACF19lG1vMezkxqruwnTjVizaW7u7w==", "signatures": [{"sig": "MEQCIEI4qncKF0fMaLJsxTTrhMtnbfm6vv7DlyQlPSrwN4M5AiAlz3j1VtKtFvHTEzf44G9xgeKQQeGhY3Y3Q8y56wLohA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4342, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhHCqCRA9TVsSAnZWagAAV5EP/RlwdAReBglibdp6aiN+\n6Ai9auOuGl3suPvHwtlS7aK7jANTgkeROPZCMRx6igseqZZ5IBoKfLFpcx9M\nS3mNq5cHlynXIQDo+Au5gx5vzZ9J7jx5J9OHR6CZCIobi3TcRlk14xeALBOx\nvRIfTJYsqJdFDCaEyK+n41o5HsuZk+uwV+XZHboEtP+TnQbOEt/wVkXxPdKT\nqZLV6mi82BLMJuplaSf46ZUYomNYU2mRHJ3htGZDwLq/+nZLgyGjjLGi6STe\nJF1O3xbzF0M0sw9Pu/tgQXFwGouwHiUBBam+oQCMjjBrWASCSPDpKZql+Cc0\nkAWfaB1SDgDdYR8+rbE6rTlvcAXABpkLhr7chmT02/8cldY7nbIh9P0AAY4V\nS1uITJObt0s54VoWcTJ4Mx4GtdGja2ZEJ5dol2jRT7ZQNKcKQ3W99KHpZiyy\nM8K6NjK/7+IDHEl9F4qWZmVX/qwrJF8eemnqujJZgWv/1rYXF1IQVgca4OGC\ntzwN0owMFyvEV3N6DaI9/jKuEN7CRrUjAy5EeGkKHptXBtxfgJGToc9BEETv\nBLSQDEf93eU93+Ahghc+snEEuPryXyZsSXIncBNFRVLSxsHl6HnAgBSZR+ai\nxPCzpSaC/kQA07MJTB5vXbbpqFOFtPZhW579gvEnFYu2ly/bwKRWLHJQ/UwT\nNuHM\r\n=QCdD\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.2.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.2.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@babel/helper-plugin-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.2.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "20c8c60f0140f5dd3cd63418d452801cf3f7180f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.2.0.tgz", "fileCount": 6, "integrity": "sha512-A32OkKTp4i5U6aE88GwwcuV4HAprUgHcTq0sSafLxjr6AW0QahrCRCjxogkbbcdtpbXkuTOlgpjophCxb6sh5g==", "signatures": [{"sig": "MEUCIDbiZYqxJtKn9eM6DCOvViWZap5Dg9nymL3BURdbDPbnAiEA77ANLSpbdk4JQIspJos5gdfsI2z3BbuMct/ySX/Y3xA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBX3XCRA9TVsSAnZWagAAyLoP/j9nACuAZE7gu1zMB/cC\nktPwab6dMfk79NIlca3JGPMBKqowhJce0QOxZNTMxq8XKAQeYQkGUDuj8EhS\n63cVtipM/t9RWicxRXLHtFvbdnp7Nboi2YyyMt96YsBszcEHK/hG/FB5jXNC\nh214gtWB2ivoKeukAkuvDPZrWQcqKQR4eOeAc9DShblC7mOL/WUNXjG/0Cgz\nGXj3fwTf4cnhyjI0Bj6apxt1BK6ucy/i+Gf4JordRgQqgrCI8euaxfTWak3G\nUEuRs5nUSOA1CWklMOxX7070Ay80jZfaQ5kQ5jFzRx+d+fKuC9cYbXgug/x6\nmRg/EuTzvrPW40uekbLYNICGr4Q/JUgoqKq1gdNoBwstxAik2wISgR/WQSMj\nahj1cA16OXsV8T3ZmwCIIGjrHuDswo9ndTAisR0FAanlY9OPmX95CSb2DqpM\nRvr8IJFKXTB5aHRWNr1PpKU+no3SO1iOFp3EXVakfJ6qu9lur/eCvK69QpYV\ns6VGKFQMd84gDbU5JmN0M4g7pvBeSSztja3eOCymmRcOfhvibgOoW39TVS8v\nh5cY3P3Sr5Ia6zgEEIMldl0Za7DVwZzPQ0mLDWZdWO4B8mQD+S65Zwz4MiUi\nYqfXPV5/H0Ear9x+xBetTtS0U2R5gIba3U53jck37jQNPtincMiKw4L3V9Oh\nG+2D\r\n=etWQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.5.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.5.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.2.0", "@babel/helper-plugin-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.5.0", "@babel/helper-plugin-test-runner": "^7.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "583b10c49cf057e237085bcbd8cc960bd83bd96b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.5.0.tgz", "fileCount": 4, "integrity": "sha512-58Q+Jsy4IDCZx7kqEZuSDdam/1oW8OdDX8f+Loo6xyxdfg1yF0GE2XNJQSTZCaMol93+FBzpWiPEwtbMloAcPg==", "signatures": [{"sig": "MEQCIFC4A73w7RJE+JrFfrBuq8YMTLV95cJBVCJYvCuQiUPoAiAJW1wj4nNyEbzSEu8xJ5oRD3xcZbYyAz6+4VWjcLwAoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4495, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdHffZCRA9TVsSAnZWagAAjfwP/jXILiCXpBQujVd0DvvP\ncMafjDt3XmFV6jKlWs6ZwmUjlr5XWHsgqGfXPgFKiMYs2pwncH8NGnJIHAlT\npBYTXcbW7+FFmOV80gR7zUxwrswXUyu+mnY3w24S8t+8dJLBmzwjaipFsvj4\nel7hlZtE5iDYu5dm6wBbwUmCTdtiLI/72u5VWVRhiSuCw1WdmXzHcyPQ21Mv\n5kZH0ojIziHbAKYxkGcXwUrtY+HRWEADrsxuGJXjy9+wQf4wwdKlfEY4Wugi\nKn9/VffQLFl/RQv3guE/pTQGTjLsoPp1ArTPv7lx95ZTXlHQ3j+SGn4x1dIT\nChI5Jwg6cvKOpgPY4rApZlbtd7OpQa1CLZHBxvzoRmfDjsookXOkPr/QxFqL\n9k/YmZPKRd3gcgjzE//rjmagdLm8wRS3Vk87yVIwCMTMJxKVrLj4Dd2LWC8p\nslv1iVxLkWBa+ccUuGLMDoA1gfb8eqdHF8P8Ib/MZ2kPTwmloylH06nsPmrt\nctiklx5NWEOiwDVZTYocHDK/0x+hOF0NmEypZSXqWk4ArxceEetCWHSpTAOX\nEgxsXEpr4sRvt+/iqmDtv87Em6Pz7e7J4J4Vsdl23bT/krZTl4iBuCsPoZGQ\nxiEIBoWBUhe/9UBGNCNi1JPDw5dk1Ta3EBy7QZNn0DqZfk2FqfyX24mfxRBI\nOpyA\r\n=h9qS\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.7.4": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.7.4", "dependencies": {"@babel/plugin-syntax-jsx": "^7.7.4", "@babel/helper-plugin-utils": "^7.0.0"}, "devDependencies": {"@babel/core": "^7.7.4", "@babel/helper-plugin-test-runner": "^7.7.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "8994b1bf6014b133f5a46d3b7d1ee5f5e3e72c10", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.7.4.tgz", "fileCount": 4, "integrity": "sha512-5ZU9FnPhqtHsOXxutRtXZAzoEJwDaP32QcobbMP1/qt7NYcsCNK8XgzJcJfoEr/ZnzVvUNInNjIW22Z6I8p9mg==", "signatures": [{"sig": "MEYCIQDe7JTZQGTYvNViDNGN/O9sVDCTBmn0uGuctsVOOTYnlgIhAM1dJfVyBSOh7wbmPIpt3GW78x6fyyAqnUj1ONyntLwN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2HA2CRA9TVsSAnZWagAAu8cP/0m6lgAEWyvgvyk/wHw+\n/Kd/hRsHMAPvwiC8XEY9mCo07/91UgHB/bqoy9tUTiml/I6GD3NwSAXAfbRV\nob/rbvRdcNzU02ztmKl3WbsgPUismbfgAGf8pSET1na/Yebt7s22zEO/CYFz\n5MPdlt7w3fAxOjRhHPtCay86cS2OjYiUYNFW3inFPN1OBg2fvHLvwyh9cOZg\nZ5b685LpPbf5w5ec4pf//Z79MnjCv1FvCzqSPTsCn2YMm6SDtD5e6JBnkxDL\nyhMdSyx4Q1V3h6+EEAK98bCNcyECsgMiJRpPlXw1FedSfIMA08rk8rtF72KQ\nLCw7DpoAyYM5RqIDlI7YDa6OC1mtO1wWJKE+S1AfJPMN80fKQV7POl0iXi3m\nduD2fMhC+j2hMjWXOfJ8Cl3hUQN2F8UOjwVAFhjA+AJ3CHczv+cdfdGbaDr7\nTlFTlk6rTNN7p+SPrQWDDzGLnnrYB6qEyc3Dr7WxzMxlyhpRS1Wjl8RitqRf\nOTq2nllg0YSMkuKCc2vN/eHJONK9eUgQdB+Fc2haz2JmWGPQL/6tHemZM+YI\nwoQXxEu4j5zqMygKPE6V7GfB1gjgnQe6NiZ2O+bq/cIcs4lVzY9DukCD/m3v\n7Axl9WNyidx7DeJooHIGw/1uosQ+HKm3Uxj6awKoWMkR3w+ewYanQ8vrzGGL\ngndH\r\n=Mi2M\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.8.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.8.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.0", "@babel/helper-plugin-utils": "^7.8.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@babel/helper-plugin-test-runner": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "97681728c12d770449d1c3657621afe974645e59", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.8.0.tgz", "fileCount": 4, "integrity": "sha512-W+0VXOhMRdUTL7brjKXND+BiXbsxczfMdZongQ/Jtti0JVMtcTxWo66NMxNNtbPYvbc4aUXmgjl3eMms41sYtg==", "signatures": [{"sig": "MEYCIQDNHa16Dxxo4FeIF+TYHk9cs6y2yxAlClpy+U0OX0ydbAIhALUGfKT9u57QQcXyeM2wKvDalq9iOphuPttwgKRuS6eP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4306, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeGmWDCRA9TVsSAnZWagAA714P/iHUAkm86WazG86w4txd\nP9+3rhcZYbA1S8cAeIrhJWHY0g0tAc9/5sriu8s/kr9w+YdNP6y1A7pL7RH4\no6eF6tu1xFotk0ZVtkjoAt7bMsP3mQx1mUb5VYMcbiDupyakbeBf25njq0aW\nW4wSsr2MZweeXQtoIgRXcfSFs0lY0knNJythKLRuRqnbZtWBW1r0r0gksjyZ\neUeyzN5g0dPE2rLB3au0H4OM/lrfvReAC6l5I44FVExk6LZPlKnxPPWeMGrv\nlOm/opZk3CG+dfnHsY7nCwGTtgtjicwV7n5atKwqHfmnNRKTbDLyItGQwdXM\nZ/+ZB6tahZ+/ypul969DweTlyJj78GwnTow90jJPyZpwJ1pcHLuu6Tx2CwSN\nh/4H3vWSFqXjnPtczhZf2Huo3KtSduHuivFrwWRL9fizWvR7knA1PpEz9tuC\nqenrKnR2wZh2iuYmCQcm0TDPKgwEpSGVnH70gyfAm97CXM3uvdhlE/zUdYpl\nm6ZbnrWk0Yco/DqWfHRjpYHGX9h24JM/JNk0a+IZc395hC8KjNisJbO5CAm6\nWeVw30T3kFpfHw26QQYId7cScRL2TYs18VkLTR0ARGqE8U3Ua8TEgpIAfaOw\nB2bNXzZPBSdWGYe8lmsxPnRL5A8JOEGzO/cN+8cUnOhyjsJXRoxejs5ms3Py\nnHhI\r\n=ASqx\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.8.3": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.8.3", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "devDependencies": {"@babel/core": "^7.8.3", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "951e75a8af47f9f120db731be095d2b2c34920e0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.8.3.tgz", "fileCount": 4, "integrity": "sha512-PLMgdMGuVDtRS/SzjNEQYUT8f4z1xb2BAT54vM1X5efkVuYBf5WyGUMbpmARcfq3NaglIwz08UVQK4HHHbC6ag==", "signatures": [{"sig": "MEUCIFzBVzRO5pTPQk3Ke6JCUPq6WUVivwgXgi1Pg2s+N3JCAiEAopf00DBrDXqRXSo/wb6z1GdnSPlogTnJ1UHjY6+QtaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4284, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHOQuCRA9TVsSAnZWagAAKD4QAIPJxnleifeZn24ZoLOI\nERhQfJqB6+UPzg/1naqSRBHIrpImYu7kwgv4Scyt18gwSPcHn03V8g2AIdnB\nPKxdKEc6+FFhhd8d1FLmHZKzbl/h1SVLpU3aTRvgSrf/z5IOdOec9fsXYWcr\naiENTegbSOyxoxY9afbDZ29PIJDIixpyf+l82SpbUJACTZIb1eq+/uZE56U9\nj0c2CkUZLbVbGY5k2fdVJ/VDK/UgDRGXM1Aj0+NrvIiB6JBOEDq0wbqPtfHO\nKV62YlHm423HDta1o6PJGwVyP+XzxzBSss9zY0jRuvXsdEx/E1zM/9b1lUG0\nUi4jT0ckrQuGOZeuvDHnpXEBfDx9/CXqD7bmmhEHTDLBIrwqcwVOv94THbZx\ndY5uJk6X+F+F5MWSZfd16K8gYyNc/Q8A6bAlfBzVSLToiXiD1n5kRgzMHW5c\nEbYNAtsRIIIcGMBNY/Plsfw87hhExXF0AZgyFG+s4gx+JczYBmQj94WkZXf5\nDvV7D2CCUr+BEtt6gWLic8O8YtOUau1exlIAcMJwA34Pr9uf9LM1euI+YiIW\nIlSn9YBEiLmzRWFuD8vOjeMDqAIh7bYwvx6GqgN6zXVwNrSXmNY7IPIIHBZe\nhuvRvTAY0lvZ7stLlCAxnRI+SpaMDj1Hw582vmpm3zhNINOQUUqYDhzFf9Qf\nxD+O\r\n=59Zk\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.9.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.9.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "89ef93025240dd5d17d3122294a093e5e0183de0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.9.0.tgz", "fileCount": 4, "integrity": "sha512-K6m3LlSnTSfRkM6FcRk8saNEeaeyG5k7AVkBU2bZK3+1zdkSED3qNdsWrUgQBeTVD2Tp3VMmerxVO2yM5iITmw==", "signatures": [{"sig": "MEQCIEi/Ictp+ul/Z0YTTUk8hhLmsCfQGh8QdMwDo9eBJ+hxAiBwLiHmlva+lKApPJsyDm6KdRTmz39j2GVkavv8pJ0AlQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4591, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJedOOgCRA9TVsSAnZWagAAZwsQAJqWVQHfOmR8jKq/TQmZ\nwfUO3++12KpJYpqB9yBgQf+A3qm3wjNkfOYV7n14ZBAnK9g4Nx/TqZQGOgO/\ndZSjH2QFerSDP2wW+xQzWXc4uZiIp1A6MBJPKZNoAMOBlEX23DUrLGKkcNer\nCUtpgqhKSRAredRVmxSC6w5xxjOK8FnrAjZt6LUswuJNhsOjm9XRou3zmeP3\nnD/9hMMbTel1p4DKfG86q2iaANOhEEu++rrvUSwSI6FmDzkfYYbeA0D4H16C\nspDUniDUZ64AO<PERSON>+zRlD64DIBwqwOQFSmvHYDzUc206F2qByah9BOGqC/2ed\nh8H1vWGLs3Xi1ZxXIn4+2kRsyyQj/oVXHrY+AB4Dzdi94qIQt0k5kaXpYBJk\nVsSzN/Ci98x0aLA5Scj3EO0v5QDccGnO7OV3n5/kDkOHCFvW+P0qxKfltEfN\nAAkugoRfabBpYR1FAn/9OIpjfokrijDcP9DM9vEAjY7X31h4YSbKx2zT2sVV\n75GPIHmvYFa2ZdGJ5r9FRf6R44RtBwDs8PdT2hn8svSbaLfVjEwt6ssZOe5n\n352r1Se1MUtzq8ZFpVcXEn+9l/A7ueYKUoQ8vxmUCB4F3FEV0lgfmiM8MwTB\nqG6+/LH8190AZcFNZ2M4wBJ+WfCkQH2796ES3qv22daxWUTdpUmXIheHaG0u\nqrme\r\n=2iS5\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.10.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.10.0", "dependencies": {"@babel/plugin-syntax-jsx": "^7.8.3", "@babel/helper-plugin-utils": "^7.8.3"}, "devDependencies": {"@babel/core": "^7.10.0", "@babel/helper-plugin-test-runner": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "0e24978505130a79bb8ee1af15a1a7d8e783347d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.10.0.tgz", "fileCount": 4, "integrity": "sha512-EmUZ2YYXK6YFIdSxUJ1thg7gIBMHSEp8nGS6GwkXGpGdplpmOhj6azYjszT8YcFt6HyPElycDOd2lXckzN+OEw==", "signatures": [{"sig": "MEUCIDdxM/p/Ag1Tx6Ahm7oX2xalZORZu8jEtnfIfbdK6Mt5AiEAzWchNYZYeNKhTG8tHc8DEvC90XvavlHc+ZZ9HUaL3XY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4611, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezY1yCRA9TVsSAnZWagAA5nYP/3NnvUUM+73W0MrWVaUZ\nx8TsreIvkwKtO5UKR4nQRkwp24jRmEIqghQZruMLgWRIhbIOgIO7UJ29d0Da\nI7VreduTaWOIswlOffQdh7TVGNg2b6DnZuMNu0WrYX2o7Ah9Tap1lZb4Vv8Z\nTEe9kb3LVFPIxktflETzzwarnKHMQMsmAt9aIUSVwbPqnc9PF6LoRSzr4mk9\nK121/prFPOfxzClujkZNWF0uEHkjT0yfH9r6GcnK0YPjJi8hbqA2d4iuNEMT\nb56/AeIWzHG7ANP/vSaKpHtMVg7ZonalUrgikMyJWLZIEx4EAN5SLqiqVuJL\ntw7aBHyi5d9KpvQkmUTFTTIRkniF2QT5Ap7+LZC9ikapXUQavk+OlrQluD5E\nluJMZBzo4z6rD+vn3DGkXudTEq6eddYKWCp/fKxZbqrImJMMIp8YC4ZT7CB9\n0dbLmqbtWmr2WWIe8DLKOQhMrzIHcyut/F6iyK/+Jj+vlIeR9jmjoL+ABpox\nVtIhDZCTqymg8MQUtq1eWqLD+cgaSLU7KCoGeI0W0D+Sts9RdjTht+Z44MRn\npBMurd+6i1zU9/aWGpaLrlQRiRFxLS5lkPaY3ZMjbxtAuVSeUr4X1cyuXW0L\n/HZ+OEZo/jluPFOC2Yg9gJzvEfzv5eLNoyq0HmB18PY5xLXHgNsAQwASzebx\nOCYP\r\n=fcV2\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.10.1": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.10.1", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.1", "@babel/helper-plugin-utils": "^7.10.1"}, "devDependencies": {"@babel/core": "^7.10.1", "@babel/helper-plugin-test-runner": "^7.10.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "30db3d4ee3cdebbb26a82a9703673714777a4273", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.10.1.tgz", "fileCount": 4, "integrity": "sha512-neAbaKkoiL+LXYbGDvh6PjPG+YeA67OsZlE78u50xbWh2L1/C81uHiNP5d1fw+uqUIoiNdCC8ZB+G4Zh3hShJA==", "signatures": [{"sig": "MEUCIQDMeO/e/1jo2TRFh2TU0AQKK9a3VhUXvCJX6R55m+pxdwIgFoDG5th1v6JjACSMWDaWt+HcwTdTzzRPX6VBOuhhTgE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJezuSzCRA9TVsSAnZWagAAf5wP/1IEkkTO6KGLXMOtI/+t\nqT7QDJCOs8cQYdgBAtIBckSJlgS4/5oNQ0/BjaG7h5fmJkH7f51Qh4E/Q/jA\nSdcAoim1gyqnAIZn5bV11BDE59IPZwe0AgFVm22BxbDjp83/uWUi5xPV1eKw\naJUyA2dN0FDtixGU/hdJ2ryxEzeezl1dXxmc2W8Y0QEfCUJhv6VFKM7MgkhM\nXrZpDKpzu3IuReYM1kr226p7i6X4ioAX+85gmGUasw94k2eOQMgRtl7AZfXH\nYmFbbu3TKl2X66NnuBhJXaI6NGKP/kZ6O3swMcDTv5e8g+Fec5eRECfoMC58\n8p8j1pQDzZOBcwaOjMWYs8dhOC5n8CZVAUSdsR5+iNwqdn2H5Y6U87vG4Y+R\nPXAmHATs1aIm5Z4NhjwQ1JnOXyKj3MP4aK9T8dX3LqzRKIpJwF5hg65ofcpc\nFbxpLTdg+GOaUXGjuI9XHzXdJjUJXskaLDBGrwpJjwd4PRPQuXfNpNmTw/zN\nN5GHZPAiR9/u5tk7g2dvdZ5+KGYaZB8eg/lOwwCS2ZSiOKV7XDEQP9Q8C2rS\ndfZpzcraFiECCf3nZ5ZBwn7IZhnnoKHhLjLL9JFWipUn1HUUHXoucXMONqFD\ncUZoSZnD6KUQ+oBGOl+BuWraqOcJT1EwlUUBPmABKPCDd6EmA8v0tX4/W5e6\nFbsJ\r\n=djlq\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.10.4": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.10.4", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "devDependencies": {"@babel/core": "^7.10.4", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "86baf0fcccfe58084e06446a80858e1deae8f291", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.10.4.tgz", "fileCount": 4, "integrity": "sha512-FTK3eQFrPv2aveerUSazFmGygqIdTtvskG50SnGnbEUnRPcGx2ylBhdFIzoVS1ty44hEgcPoCAyw5r3VDEq+Ug==", "signatures": [{"sig": "MEYCIQCoHmyNAX0xeWR6zcFYxv1UYhfDHZSKQto0hG7g2WW7SwIhAP9KODDiTUVg6kEU7GUZgD2zXJXnayclRlGrhJpGiJTf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4662, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe+zo7CRA9TVsSAnZWagAALiAQAIIOE9ZpAN68PQYbi/6w\nzkFfwbXR6zKIIqsZp5igLVipcf4FZTUIRSPPVy9OznCVglZFuvLnzwyW0w3H\nxvy8urO7lFpkJz4N7eiJa7Mn+mfyZhBKN4dHiWd/SxokDoqiAuwk0QdtYCan\nhSPw/es9+paoLKQkZNvR6E2AG0nXQEdm+Al1l8TFsR1qC9upIjwqXQB9wrjh\nNNmo28n/rlMZjIza3MpO+9zajx8HzQTkHkHmMdL2o0R1u6VbnyNpNGQmkg+E\nmkEZHuZ8qp4i+Kzye8KB/7eb3nWCwn67uJzajEAbAA84bNUCScbJHhK8lN9E\njbQzSqeqmjZE1UMK3H9D1VHR0z8eGTm5jjFU2cO/bfXYXvw9JCS+CS2alRoM\n6I/dofMieSsiVZq4isIXx4mYH0ehWzg5yco7BMhoHXPXKtiOrz3DpxvuhXc6\nWdJmNwE+MhmxhkN9xT5miN9sAczaNN2revJZX/KGSxjFN1dCuj9081pfC1Uf\ntn7YhIVKuMYIz1oo6Dqao+se9xbI/aDosNW7CE5j9N+kr0ddZ6LTIBsqd1RH\nUuFs9613aXU7YeJ8464ox4g968n3SlNX5qgpmkLax5er9JA4wciUgLymzLjc\n5MGjYIQ4mt2JydLKFJZRJefkP93e5Z+WYau+rkpRvev1F+43ZSi0N11JiLkn\nRVNy\r\n=74j2\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.10.5": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.10.5", "dependencies": {"@babel/plugin-syntax-jsx": "^7.10.4", "@babel/helper-plugin-utils": "^7.10.4"}, "devDependencies": {"@babel/core": "^7.10.5", "@babel/helper-plugin-test-runner": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "34f1779117520a779c054f2cdd9680435b9222b4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.10.5.tgz", "fileCount": 4, "integrity": "sha512-wTeqHVkN1lfPLubRiZH3o73f4rfon42HpgxUSs86Nc+8QIcm/B9s8NNVXu/gwGcOyd7yDib9ikxoDLxJP0UiDA==", "signatures": [{"sig": "MEYCIQDjZvnHYFlYiwnQekKWd6rmUVRymMI1vtU78AvcMG8e5wIhALzXPztGPTdppdlVk2cFKfE9zXaeL/SxZx1GYU+79p5l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfDfbUCRA9TVsSAnZWagAA9moP/j/N+nfK6v0ei9lsTSwP\nwc3zEbcAC+MK0lrw0+pcJIXe41PYheANWRSr6I12eyyd0k6x3uAOTbm7mldi\njWZCg2a170jkI1s4A59neV6J5WgFuFRSxLaqjZRu8mcRAm8kKtNtfQHPtC/j\n6jif+wipdDKCcVSknVnY4NzkWmopjWvOHY3fhMxBFwat4gr7/7S7orICLvNM\nuo/lfpuFLk4hHFxvktNcHJkpleb0QPEbDSgKJLrDEGuckqFlRhrIi4AVkePO\navZiSHfwIHLd9B7hvs/mghA5bhCNG22+77ahw6QjwJpCxaCuMX4EBccz4k5r\n6g/A0Xb9e+voo+j3Kvr/pOVu/wn7Or4KoE5co6LDpaEy62AFNk6eH0ekh5dH\n+u2XAoGkjMTBzkYEAManekX8jQXs9JwCDY7uJVXilmRHjo2/XgMz3uTJfgBt\nN7U14zCaAAgxJcO1ZxGC1+tbC5O4o0mUB9J3Luq+rIZ8dSdKvKZDOlODMc1w\nvs2Ns+sNIvRv500PJNzcbooQGm67uiKn56ltiHPDU0eGMloDhnlCZ7OLkFZJ\ncVjKh47hHEZm+tm7Lsd9rdcckdnsyJXrzEyHAAT9xHQAbBmo3p2+5JRkP1oB\nOVSP8DNrA+dtD/dc0IGu2yhbktlcoeOisi7IRvDgV1xrGikefY/loKncYaek\ngFWa\r\n=cL+k\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.1": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.12.1", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "devDependencies": {"@babel/core": "^7.12.1", "@babel/plugin-syntax-jsx": "^7.12.1", "@babel/helper-plugin-test-runner": "7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "d07de6863f468da0809edcf79a1aa8ce2a82a26b", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.12.1.tgz", "fileCount": 4, "integrity": "sha512-keQ5kBfjJNRc6zZN1/nVHCd6LLIHq4aUKcVnvE/2l+ZZROSbqoiGFRtT5t3Is89XJxBQaP7NLZX2jgGHdZvvFQ==", "signatures": [{"sig": "MEUCICrM0+k61/454f2Xxlz6lvuWxWmvC2+6AQBr3Sl//QVXAiEA2XEudWsHf0uJZnlP5N8hS5inLcZBmC1Kz/wcAyueowM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4626, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfiM/ZCRA9TVsSAnZWagAAeiYP/0v02oXgI4wduOoKjTrP\nuXO3U/w6+4OQr+qVnWJ0k+u2V2qHM8QcGCVrGNO1j79HGtXKnywIkNhJL+IC\n7yzGZ6PQ4a12FuD3/p8scYlwFNVshWiH5v6/pqAF8acpT7y8Fm+KrGXifKbp\ntAjNf5q7yOWmKbPPUkbOWZAanGvJndqaWBigoK/zBOv6OszLRMZzW4F4yq8q\nxeAsa7JXaOjCAc/S5EHIBRfM3GJp94tORe2w18lilQYRFBvIqZDn8ev+D2Zr\nmYwggNpd4vL5qKRwRYaPITFZ5NNYdVAo/6xnx052kFk3XGOncAu7EJAA7KJs\nJ8igckaS1yRun1hXHw1UP4X5BhZkvG5fBoXDyJl5oidgm3ntkXBFpE5GhF1W\nuPncCRe4a80wghZnUJ4OZIOvOWzc9roaE9T2M9JSxDkEna+Bp7ts1Ur+oaeF\nh7Q1CPX2B407u2rZh2bzwPfRQ+nOR8YFUqC9CcZHP0Fce9fuXurSfbgrbRMz\nEcmAp0locOcVmOYiBDIPvwEpZazsBuPtt64d6b9UstaGxIDdZ1ZXJh5/zpmw\nqhdjlEQcR2S0KZm6uPAbGs6S5GOQeAwSEOBVg1xBRAlTbdBjUKR4bSM2/4h9\nG5mDhmzuAuYuhYr1e6odUAcNnTPBdbKgJf4l8fjybAHwRTyzpD4PFDi1eVTX\nzr+Y\r\n=SSEk\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.12.13": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.12.13", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "devDependencies": {"@babel/core": "7.12.13", "@babel/plugin-syntax-jsx": "7.12.13", "@babel/helper-plugin-test-runner": "7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "051d76126bee5c9a6aa3ba37be2f6c1698856bcb", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.12.13.tgz", "fileCount": 4, "integrity": "sha512-O5JJi6fyfih0WfDgIJXksSPhGP/G0fQpfxYy87sDc+1sFmsCS6wr3aAn+whbzkhbjtq4VMqLRaSzR6IsshIC0Q==", "signatures": [{"sig": "MEUCICpa+cNlbcnP7hyn7cP9TQ5zxC7K9TDuYCb+AAVgjBiXAiEAhkD+tkYvBIOTrnklqENHxaYgMIeUy/vNck05tXNRE3Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4707, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgGfgoCRA9TVsSAnZWagAAUUsP/jcDXavvtdCo7/rJaGZF\nmxQZ8ePiAtaO9Ns7MUIZ1sD0HnnFO5DIyAtIzwxNQ/ZLHrRVvl+Xv2DU5MjD\nJBAcdr4KlEFs0RCXZnRBy/DHgr/jON6opgASjk+pOh0NfxAq2Vjfmep1V2XK\naHbSLffKTKxUeTp+DMuhQQ5IdOpAkKHhQaKZecyWD+tbVjcoBZ2JRYlE5i0z\nVEIrm6UxdDxEIk8pX/izALEsK6OA1/dmBrsedvUovEZ8zmVuRgwNfTXkLSr2\nvwmwwfbbUQ+kgwpeUwfAae4B+B4W/fTDe+b99isEyLRzV9KLo3srD9TjXVDe\nD7zlyd2/i/ulzAp53TIbI5oG1Oilkw79b5YEq2h1rfJ3SycEQ/11k1eqZIC9\nUcI6r3TH7BIdRmDyhYvtagLqYURgSXBVC2m7Jg/zYmLuyLBRlfF2CWnj/y8V\nVXx+uQfmNBuMgdGz0H+OEDXdtFfnpJXC1sIXNVD7nr493el8LuBrTvF+904x\n3kwg5wJQcdWIs7YgJRJMHye3ODZ0AsS9v2CilXGi6XUA3LNO/4H6vQNr8W+2\nxN5e549RUDh6MiSxLh6F/cYgsqE9/SDQsS0vnvL54HHekvEDv7+BfsymT5ra\nGVzG4UF41NkWxu6S/sL/kuNthRDRt3XMPwEL7DESRKrNZY9In6TjvmUVweAO\n6THw\r\n=nRm5\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.14.2": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.14.2", "dependencies": {"@babel/helper-plugin-utils": "^7.13.0"}, "devDependencies": {"@babel/core": "7.14.2", "@babel/plugin-syntax-jsx": "7.12.13", "@babel/helper-plugin-test-runner": "7.13.10"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "2620b57e7de775c0687f65d464026d15812941da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.14.2.tgz", "fileCount": 4, "integrity": "sha512-OMorspVyjxghAjzgeAWc6O7W7vHbJhV69NeTGdl9Mxgz6PaweAuo7ffB9T5A1OQ9dGcw0As4SYMUhyNC4u7mVg==", "signatures": [{"sig": "MEQCIEpE+cD523QgM8Q/I7fSyk9xkFOfuLQNbO0AEICAZPCwAiBUl7om4u7RkiG2SefaHcMoE0956V2QjFz0oKtqauay/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4704, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgnAvFCRA9TVsSAnZWagAAibEQAJC9iPF+ExVUbAD0grJ7\n/jLyCCzPWza0z/+V+zBtKr3WAiB+u6sL81SALgtp0yQFyp5blkYUNmtxXk2n\nB44DDLr8+BK3SGs5TGO+HidZ0WX95Bp/Wiq+BiKFwQcDPxBsiBtEsFwN31W4\nt92DwxylQ8K04CpLCvjQr936S8+wOelYTX7FBCekg2z1hLbhk3wc84Tao5a+\nL15UGLUTPVxt4/2+39aIMd8zR1szD+nifWCAmq8MefewDxhtxWSuBMt58ARB\neGFDg68vRnowHgzaG33EnhZBCvdV0w9OUyP4BlWzKGIZoruaR7zduKEsGHLD\nMuvTkrOkm1MbqXJ98DigeCsTkJo9rN0eyatsP3do/lHgfysRdqk1o5KE4FTL\nv3O446fga7BK8Csbbq5K6Xq3LcPmdnMLRGoXH051oBRFPzz7ypeOLxNezKc1\nqYgav1VnGS2bOnmCROamdbSnRA0bXHU547Nc4giSpihcdy5J2wi8/Ha2TYrk\nYwGfEf47tRS1lnryrJwGHkjiiyGUR9bgYiv0+QILwlz6s71A+o02f8ZQyVJk\nA8zfgR0nSTujr+XcjJSodW9dytiCGShLBvj73O7GXTqeW+9qKhj4Psv3l9WJ\nluJxOCUUq76ZRSveLOZF4kyU2AlaOrTAQPI7IF2/FtOh8UXIGi6eQOXuqRPP\n/CO3\r\n=Pm8W\r\n-----END PGP SIGNATURE-----\r\n"}}, "7.14.5": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.14.5", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "devDependencies": {"@babel/core": "7.14.5", "@babel/plugin-syntax-jsx": "7.14.5", "@babel/helper-plugin-test-runner": "7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "79f728e60e6dbd31a2b860b0bf6c9765918acf1d", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.14.5.tgz", "fileCount": 4, "integrity": "sha512-1TpSDnD9XR/rQ2tzunBVPThF5poaYT9GqP+of8fAtguYuI/dm2RkrMBDemsxtY0XBzvW7nXjYM0hRyKX9QYj7Q==", "signatures": [{"sig": "MEQCIAlE9h7hN9DN2Vien4nxCqS6wC4EjFNfRY/ykEwJp+2aAiBadD7zcN9qkinXnMF88fnaZ5P1zcBSuxBonMCK+RN8RA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgwUrMCRA9TVsSAnZWagAAyWkP+wSMvdFbOS0xQebhcTsO\nzxpCgDqb3TmlqTxNMfHAK9V/WlwaCcp3/9qL//RuTZnXQUCaXfwmbqntNjr3\nGgFOnd1dcRxby3S4FRfra3hj7nYHgw8BxI4vbvwM+LSHTda9JNnxpuIZwjqh\nr2o2xPOjkakjMA9SCUVwWwHEArhzbau45SMLqbnpWT+1CrSyrho8E9549uxZ\n4PdJu3NwVUuL0isqgkNO9GyhkZvNPmUObvDaDN3HU+WQo7+dhejis5XBDJCs\nlIMDE3v1rSJEn45AHSYE6g0uWIk4BXeM7gp1Atae1qFevKCPEopq1Twrx3tE\nAGi6pNYmiT1hER6dPdXsO0e6E92kSBaiCKEEu3iIJ8YTv1S/B/YL4oFdb9r4\n0QWgc3rz1PA14jRg50yROevQvTVth5gTQmcJWkwqzedh8cHLXPauwiy1KAAH\n+u3tJr61/ZtdN5OGiqMaCWv0ZsH3FaDYgFFfgWkkjjyOacxS/JjDfC3+Qi9q\nwLdKp+eYle0DzhE9J+mZUA9WS+CpbM0EXeADk/n1ScW93S4BaWJC9yCoNlEJ\ngXjomYh3zPNkHs/8tOkFeUnNIcumiqNLRUAxEnCvl2l6MIXfmMuLDq3AhqhM\nnnEG3F0q18lYZnJ0JMxZ2FqAi+lQsdtKCeT/JiHLG4GD8KTmtIrw6T1BuFtv\nk7hf\r\n=sP25\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.16.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.16.0", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "devDependencies": {"@babel/core": "^7.16.0", "@babel/plugin-syntax-jsx": "^7.16.0", "@babel/helper-plugin-test-runner": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "d40c959d7803aae38224594585748693e84c0a22", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.16.0.tgz", "fileCount": 4, "integrity": "sha512-8yvbGGrHOeb/oyPc9tzNoe9/lmIjz3HLa9Nc5dMGDyNpGjfFrk8D2KdEq9NRkftZzeoQEW6yPQ29TMZtrLiUUA==", "signatures": [{"sig": "MEYCIQCtoN9U6a46VvAf0aukyP/dP/lxYGu1yKMTc/0DDkRjIwIhAMwh9kRHSMKLlz8m2Xgls3L/4T9F2Hmlmaq/e8blYtaD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4804}, "engines": {"node": ">=6.9.0"}}, "7.16.5": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.16.5", "dependencies": {"@babel/helper-plugin-utils": "^7.16.5"}, "devDependencies": {"@babel/core": "^7.16.5", "@babel/plugin-syntax-jsx": "^7.16.5", "@babel/helper-plugin-test-runner": "^7.16.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "7c2aeb6539780f3312266de3348bbb74ce9d3ce1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.16.5.tgz", "fileCount": 4, "integrity": "sha512-/eP+nZywJntGLjSPjksAnM9/ELIs3RbiEuTu2/zAOzwwBcfiu+m/iptEq1lERUUtSXubYSHVnVHMr13GR+TwPw==", "signatures": [{"sig": "MEUCIARczfAaoSmzY6cIwnJPSHjKmkX+t2xxzy9mHWSGJ0n6AiEA9hU8N4G5CuYUmyUvWg6zAUcMcmNnjYFrqqP2mblGr28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJht8kLCRA9TVsSAnZWagAAnDsQAJUjpMbt65s0Sb/GFuF5\nM4rgQQa3eviUd8Am8ezaA+9z1onYwCfZog7p+bSXcwX2u+SeB6frZUyJnWgd\nnP3q6ITMmAzrhBep6ZhaR/hLdsmSF9XRxlXHIRZBLZ5/iJJIZ5vPsVq3vg/w\ns+PvgkD+YHF9b64ploVn/rzLzlqwoJqIBYROLOCyXSDHd+0M97dHNTF6/l5x\nxRvrXd6ojNPEa5S/07jOrh7e5SI05WT0PhCmTYgGLSluRRrHrZEyO8HyHpJt\n+9mfjU/SVYnEHpJ+EWLhHTmbsdv7neSmF4ikgYBWN0+ubxFNzyAOFObfIqfF\nU9b4VSDmVRtJPQ1MQV2EtoazG/BaqI9hTIHdyPHXjuVqfxq+yanzBRD5DyBH\nyMmv6kRoGk+ckZ6JowbtMyRM3HFs/VF+SmKOF6wr0xAKemPgorTV3S9v9EHu\n6mSYN1VCg0okQZ9vlS9tG0ndQSZ1CEPOt7rTF4VtL4ZkzMJgJSHpqy7q94Bw\nXzDl7MwbvAlGAeBMwHRLhK2DX2VlLbrJWaFmgY3CJiFT7QHdU6h6v2dhhYZs\n4uq5IiXWKXfrBMIv3kg9nFzi+Gq1lzuQbx1j6k9PnWDmpbbU6HzoMKMhFi4p\nwEJN/den++Kw/jM8ZYNOSIIgvhSpcdTOpN7d1mRNnAxb3wwA9FNnnYxI4E7r\nhKw6\r\n=QeCs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.16.7": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.16.7", "dependencies": {"@babel/helper-plugin-utils": "^7.16.7"}, "devDependencies": {"@babel/core": "^7.16.7", "@babel/plugin-syntax-jsx": "^7.16.7", "@babel/helper-plugin-test-runner": "^7.16.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "1879c3f23629d287cc6186a6c683154509ec70c0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.16.7.tgz", "fileCount": 4, "integrity": "sha512-rONFiQz9vgbsnaMtQlZCjIRwhJvlrPET8TabIUK2hzlXw9B9s2Ieaxte1SCOOXMbWRHodbKixNf3BLcWVOQ8Bw==", "signatures": [{"sig": "MEYCIQCgTtkCCyy+uaHMBhAoBlpEHTbilXMVVvyrL9pSKgHNHgIhAJ9VCjQT5xfoeNvQ0lIv4Na0rfGtCT7uv6wfX/eBFYWN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4804, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzk0wCRA9TVsSAnZWagAADtkP/ixI36wLq9+KQBW8oOva\nbgcxI+xJP2RIgfvoD0zVizxduEgSOS3UMPr9XULFZJHjl2iW9HkrkUooW20i\nyZFRlQcmvSkVJWrqcOvWTXdeoGbj4RzltBUwOa6V+S4+EEtxXdz513cm4zaX\nRHKq5JcFthOgEB47ga7uI5o0TAWvD2Ls+qo/UtLkO3ZSjz8ciU3FPND5b/qW\nwacydBhGeC7FN1O0YNtNUkcEI5l+MU0DIxGuegTZdtkSQuqSob37fpQNFkaf\neNQDLcVSEvKu2Kd1tnCu4CUzXLu772fEz+9Ttnbk1LFNOBH3oo3ebT+O3UwL\nM5l68YMDHPWvgajubSu0NIB9KKvvUlbfXh5FTQegfUp7nyq7efmp1G88sqcQ\n5l/SOXlU4yTpZH2UKbhisI1tO+0VOli8RC7W7fSSjAqAN5cPVdG/pT4+LpGa\nvUAXBqdhMLY20EJi1dER2cKkJhKVlJv9YCiT3l8DrVJ+rKqYSB1I40nSRSxG\nS99/NXPeZmwzKqgsLhUTiPQPkowYaRXSdR/h6FNHsYQMKhezYkPgMOiy+HxW\n+HAKDZjgIfb76UvRn3s46zCKKzhhFnuEjvHjUCRLG/m16fYsBh3K925s3lZT\nvzo84L3IehiM+hCmIB6IhPIJZcbjvXjBGk7Va1oM8KI9aKD2alNNf0s1FJmg\naeaS\r\n=H1Bg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.18.6": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.18.6", "dependencies": {"@babel/helper-plugin-utils": "^7.18.6"}, "devDependencies": {"@babel/core": "^7.18.6", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "06e9ae8a14d2bc19ce6e3c447d842032a50598fc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.18.6.tgz", "fileCount": 4, "integrity": "sha512-utZmlASneDfdaMh0m/WausbjUjEdGrQJz0vFK93d7wD3xf5wBtX219+q6IlCNZeguIcxS2f/CvLZrlLSvSHQXw==", "signatures": [{"sig": "MEYCIQDalEiezRNEhVbr8KIl46ApF+TydYmn1nZB8Utv0HVkUAIhAJpB8ZTahRuI6gdzsd7/gIAe4F9idF0lQWjzuyioP8i7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 4826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiugnrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpSJw//dKMNmf3ILd1993GHnoweN32s8t4hINdIKYMZUNuiXRISIl/m\r\nh+KGRuOM84MYgjLPGuIeNuCbSJIpdvtqzj+WIfGlB+6sfHwKXlGMC86M23jr\r\npukuGXMb3s4s5GtmG8bObOhBmFOKNwZR+WnwdrOLnQ4r2z0r980AxqvZVCkn\r\nacS4Q9gPP92Q7q52gz/C1iZVzwPfc1rryNfIw0+gnd8n81hAVxfCxi0dUgET\r\nuyVqR2x2hf3vmnNMVjVHNhoui9VdlEtmSF02j8mnfJqqgNj104QxsTIxzR7/\r\nYFuzMZDH4eeJB4DASqkm7/5NSLn7WHliJU1gZk4bIzH/wwXbUCkQbeY4R3F1\r\ncedrgVKs35asSQwHgYYGtZExqsiqngdPu6XBne9Z6FGh5UvBCyTNA3TLZBv8\r\nIS3NOL15Lu/lAq6tn65THS93HGkLgfdWL4qfyWpxr+8/zhtGCFnXaYboOGBA\r\n2elbmM8j8+qTqGd1KeHL4k1I4gTCgmpmIQAQsBLel0hmw7pUSoukNDNzh8EI\r\ntjPGq0sFTWsSK6j47qvS9jLzFpkxPngL0+0U8BJVNzg6PrXaoKAdpolx0Wm3\r\nIVs7U1WMGQXNlE6hEWJzVYWUYE2AC9s3wyaDCianSyFPnTn6DZRHGBbT71qx\r\n/v2DmVZzJTMfKja9zo0tCnJDDRCu+zz8+tw=\r\n=49xg\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.19.6": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.19.6", "dependencies": {"@babel/helper-plugin-utils": "^7.19.0"}, "devDependencies": {"@babel/core": "^7.19.6", "@babel/plugin-syntax-jsx": "^7.18.6", "@babel/helper-plugin-test-runner": "^7.18.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "88578ae8331e5887e8ce28e4c9dc83fb29da0b86", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.19.6.tgz", "fileCount": 5, "integrity": "sha512-RpAi004QyMNisst/pvSanoRdJ4q+jMCWyk9zdw/CyLB9j8RXEahodR6l2GyttDRyEVWZtbN+TpLiHJ3t34LbsQ==", "signatures": [{"sig": "MEQCIBkxif3YCaXKUVtm2F9vH7mtVzFwAqr91oIw8aorFxosAiAhIMhpnQhPDckEw/c5+gDbJWV5IVUih9T4uyTUvVqBZw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9011, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjUQ7hACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp5uA//Y784Il27he5EJOGezw1PZ9FS71Ks0pKWq7uoLqngp+F/AGjw\r\nsvdDEIXrUbg2UiRxHLiGwr4XxnpXQM1dLQKvWqphXJkjM2m4LRIURVEEPQID\r\nK1rQM/bMfCqn7MIusbcxri5fpoWJOD0sCigT0y3uCmEc6OdAe9oBNg7beNws\r\nzAUpOXF3TyXVJH+Z3v1ynk1cIUSeweY9VuJ/mbtHzVFNIRvojITTkxMZ55lT\r\nWCnVDKT3N/EY74mDu2O0Wp8I66uO5XmF2tpIPgYh6Cd6cMddXdH8IuiOcHh6\r\n9usQ6/zYYc3t11WsU0miBvPTf3buaIOZtN97HMA/mHu0WxRYP4Ef+CYKH8Di\r\n0g4wemZnZNkukQFDSWz/PGWeo0JgHX7oUTeqmBXkC9r9O+UXQIRHPCtpr7zq\r\nfO9CS2WciH5DClmOtJEoRHp63pHRXL6ZMal7BbB9DtIm0Rr3QkOSPCwiyztm\r\nSaQOTuWxzCPpvMsPQtgpu/NuQuTY5Dj6JT+/ZfSa65fHVzyfZRCFOPefmudz\r\nOBRrVg4tDcN5mpIOT7aIB1YafSJlD53B5raTcpalYfaujpOSbzUzqVCMi+Fb\r\nhSM50fNwzITwxmUGutM5vG+l3LR8k+QHcyyOC9FTz9aucPhNzR7MwtouJmfV\r\nbGL293pd8awoqAU+89hxbBq2Xqo8OrBLOUU=\r\n=oZDo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.21.4-esm", "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm"}, "devDependencies": {"@babel/core": "^7.21.4-esm", "@babel/plugin-syntax-jsx": "^7.21.4-esm", "@babel/helper-plugin-test-runner": "^7.21.4-esm"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm"}, "dist": {"shasum": "e4556bdb6fdd1b483bdee28d86a598beba8122e6", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.21.4-esm.tgz", "fileCount": 6, "integrity": "sha512-YiL//cEeg7z1j79SL7/XXmkDfQVUqDyqg2kC4NlOTXH+Wv9QhS/HLntOWXE1w1XgKngfJ8WrXcVG3x3dKTUAYQ==", "signatures": [{"sig": "MEYCIQD7F7blV0AdLmoLYSJwxuMUHnANr9xQYpvhw4Koa7xAcwIhAL4f8yWc+QMiE2tx3Y6NeuPAJTRWORmupCjEXREHcj0s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9220, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLC+YACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3WQ/5AGYD7Jq7JGfVKE/RxbV+ZzkyfusCfOU/edSYfIwxc3e3mO1A\r\nukRIOp2RfaRxSHsBmVN4UvJZHEx+TfW+2Wi/JwwrTDGuzuq3LDo7YVxOh2+0\r\nd/4SPMFc9CG2uxgfsJ2rSIbtaxkJdjMsDmEqk3vClo6PPriNYYkCFQRk32Mu\r\nHZElbfS1ifua+5TitvNl51uxFxWAKLB4i2l/Xi6g3BqGG6GA3e1NmH+kUq8H\r\nSweFjI5ED1pkvQeDrB6aiUavMugbdttwCBMKNrcNjjRxUmmfrNEfpdOJruEM\r\nq+ApErXQ+msfAYFZI3o/g4BOSZ+Pqcq/QCR15anmxrsvklR65a07vX+ypOO/\r\n34xtzly7r95np/hAsoCE7eSPl3Q4SAbWm1c3WFMcVGNOV1duAx4QNRMuuXPl\r\nO/9eJk0gV1F/2JIiXoG4MYGgj4mi96Zhme493L/COk8GnN7pzYgcYcT/NIZp\r\nXM3I4nl8GRrfe1tst0x6UMRkccTl9tliX386xU8XEnSZeXNx0q5mtoQ446FM\r\nlGAXBvf7XkpMCE7VB7hiddBkJQPHTrwu4gn8YezF1ZLwRAKdx8IIqGp/V2if\r\nPFm/FR4T6jgkRSmf8pnMO7xjg8mEVuqMJ1sfQyZ62MrTQXTbWYZa+kUXieqY\r\ngZ4JfqnFojAcNnPUPnOGL03Hg0OVWd+J5wg=\r\n=Ea/H\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.1": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.21.4-esm.1", "dependencies": {"@babel/helper-plugin-utils": "^7.21.4-esm.1"}, "devDependencies": {"@babel/core": "^7.21.4-esm.1", "@babel/plugin-syntax-jsx": "^7.21.4-esm.1", "@babel/helper-plugin-test-runner": "^7.21.4-esm.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || 7.21.4-esm.1"}, "dist": {"shasum": "f840ce5edc70a6263a4e1010862949bace82cdb8", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.21.4-esm.1.tgz", "fileCount": 6, "integrity": "sha512-DsfbgXxVYEgbnE1DvMWy6i9ZSYKX43ikR0ZXSsTrg1BDZybPHrDQLNl8QR4Eudo7yXKC03fpIIztdmsPmx3c9A==", "signatures": [{"sig": "MEYCIQDg5Mw1Y4TVzlOPkFyAMapxg0DcuPCEdvtiefcwE8U92QIhAPheXTB/5AdCRoy8DDVWy5OtcAqCp9AfWd4bGNV/NDzV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDJhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMIg//dA/3m/pfOmLL79JtPc0zdGLhzQASXsXGdkjVG7JpAzfmqr9g\r\nAaJngXe/6KyXPuAgNwt8hd91HYKTTXJYMwS4XKGM45iAJQFJRhEuPsuU04KM\r\nA6zzv9fL3J7IF9wynyWVk8+m5f54v0TMh6D4F00G4P5isaWfAy6jK86/THI8\r\nxjTrUu81c1JROfIoN3I61pFYCPkdo+2GyXSwnCEcJGAiFUnsiJtMx49+x3lN\r\nE6ebW7cKgkb2Iggf0Qhw2m2uSrlMhYev3KQN62l5g0IjiGm8VhREwIbMfJr4\r\nE7LR7BcBIjnANfKMnLFflHzrgniSfsj1uA9VB+FzCd/XqPaYxHO21mG2VgrO\r\n0emuhp3QfC+48tTRtRThpqTxYMJbj1skiDmsJpXGon9gYv3hRFgeYtWk8/lQ\r\ngPhyONpnjFFKp0N/+F6bdNHbfIk8u0nbSGIvCct2DdZ/mRNZiztvbqrUDFzy\r\ncBZfIEJW03+bpBWxlw72EM3d0Dp1bn7mk+Z5CX2CodB0TASCWJJ8HQHemr2F\r\nKYqhVXimuulqyDbJ5n09v2fOVSbXjp3NJ96uRZfMm69aX7RSqmzjcjiBh9Ez\r\nwgMuRSCMg59qquGO0B1G0oYyBdaMqxv6u5HJDiTFHZg+pWFBT81OvSgUQbsc\r\nICF0LFA1OQmUhQL4ExRIGckqnBneJlEl3T0=\r\n=CoIM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.2": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.21.4-esm.2", "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.2"}, "devDependencies": {"@babel/core": "7.21.4-esm.2", "@babel/plugin-syntax-jsx": "7.21.4-esm.2", "@babel/helper-plugin-test-runner": "7.21.4-esm.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "dist": {"shasum": "93798b28b7c9a0b957802b60499226e733529726", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.21.4-esm.2.tgz", "fileCount": 5, "integrity": "sha512-HDYDRbpEQGLBGG4AE4UWA4319Bti+P/b8UEXJRvDdQyO2JWcZB/DgAR5cMNHvTclUkqQE69FB6mi/3pftPxsNg==", "signatures": [{"sig": "MEYCIQDvRvjZ2p8Ih/AaMRPoZjqK1GBPXQaWiLZsLh2L/Ol8bAIhANpsRFWLmklfDjEnKih36zJdegcJ6lo+cq2DvhkxP7m4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8866, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDaZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMyw/+KI8PmHvsnEAuCEbM4uN4wOu/9VI2F6Or0ujqZHWQJKMYqWhL\r\nrLys8DQNNIxJUwssu0iRqi3k7gfrlLQ9j8p0pFd6iNCmw2Szx3M/JP8s+NHT\r\nwKsz8tvD3BB/5BVqhZVPnWSED4TRflKuwTLYSSyIHAAD4PQgdKxFjzFZ71r5\r\nyfQpEyvARxAZIVxK/lPS8hHmHWmjYzNFClNwynlHvlRGI1odrD82mGIO2hZY\r\n2zQbPwRECyRGihlLZL4Dsf48E9dWQfDNblE9Id5cSDl66apf98iFPOaAMftH\r\n+D9NJSZT03xZ7eludH1bGZAR6TnotkMRYa8OT5b0mLS8AHNJNSBNlqV3WzPL\r\nyBMcQlh/JBx5ZMxnLAyhBzHYAch84j6eHGpFFmnHCR1Hpafr0QmAQX4rNxiM\r\nPTV3U92Kqneyq7QrckhXALAnoh6jOixklLg47xefI/SdFltb6Sx8uLQBu5SS\r\nKpvOUISPWg+qf635zCA8SxxySF+yqevToaKlbGqmLd+GhwBbDVgGK3GH2wtE\r\nFhB4hAJuOymTZov/TLwUtdkb5JTnox7QlNyq/sskMvj+NHH63DWKYpj32g6z\r\nm3zUAv36uLg8qF4QvzE7lWiUSE71NFMJKOSPR1LIoj8ZYMsPIQlMYYoMr1Oe\r\ntsHgIWllVeLGG/wO84uG3zhQk//mvhA1CaM=\r\n=IC9T\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.3": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.21.4-esm.3", "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.3"}, "devDependencies": {"@babel/core": "7.21.4-esm.3", "@babel/plugin-syntax-jsx": "7.21.4-esm.3", "@babel/helper-plugin-test-runner": "7.21.4-esm.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "dist": {"shasum": "3ab936ebf66d884a8b8e12443abd7add908ec94f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.21.4-esm.3.tgz", "fileCount": 5, "integrity": "sha512-GVO1+x1v1pAISrAZPKbht+7Ax1hEwV8CX+36eKsmYRoo7MG8PputfJABQlvjP8QgSDUexe5OXojLQ0gS/9gEKw==", "signatures": [{"sig": "MEUCIQDyEw+wug00fuLIl2ET4GibvNGoPE1w1HpmzRS/NsDJ+QIgMK8yXVOUXlIx4o1kapNs4NJZACaj4nZ1IGjF44MJ6RQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9207, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLDqLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJgg/+JRu6C4twsRqnreZa2JqAMCRtnLSAYRdWhxyixM4sYduknoZO\r\nNmzsIjWTD+2s9VjWz5UOSJaz6h/mMAdMWEqWU90DxKrUBQu+1KxsubusPIDu\r\ngUeXBocNSitWivaze5v9qZvFL4OFKWsuxfg/SW5AVQgXWqbs4HZiVJv5wgKm\r\neAv1pRVvaWEUd/C5xo2Ew6VPfjry3jNfYkolfPaqpONMkPlnH5e3QV/UXfN8\r\nq32eTJejaftV7tm28GX0Cs+i40uwo92a1d4IK/PUGG5/v9L/MpO0InpXajvS\r\n21y1tqoJcdSHA0Lhp+t8xhGXWTUT9Ny89QcMg6/yJkP94UvyFfZG5Cb7RoRC\r\nxEC3aB5y8Q1z99zQbhuIrk4Lac/wW3xNjUcv9jdteRp2iHls6Q3Ik+ylKzfR\r\n9wHTWX6RNspDDCrpJGrDasBC3fdWLQ8kxBfibHMc3WvV4P6Vxz0J3vZt2o3c\r\niC6RnEZIZjZZB8nGnzZtHMnZpLS1gGvG2b/9jxN+Zoct31vLZfRRRaja8n9t\r\nFWjHAVXsKgfnxFU3IU4wBsJub0lJp6fkYZZ9qV2RtgOa/H6txijXxL1TlgNY\r\noqzlDgA8jnqZuVM2ueiRlLtz52P6l4ANjh6BpmYqITgxmVr1ChnUeQgd63Yb\r\nK4fZOkS2Z8LhIcLovXEhYWYFbb/Le8bRjYE=\r\n=FZpo\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.21.4-esm.4": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.21.4-esm.4", "dependencies": {"@babel/helper-plugin-utils": "7.21.4-esm.4"}, "devDependencies": {"@babel/core": "7.21.4-esm.4", "@babel/plugin-syntax-jsx": "7.21.4-esm.4", "@babel/helper-plugin-test-runner": "7.21.4-esm.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^7.21.4-esm.2"}, "dist": {"shasum": "9a62a1b3b71825293ccd32a2ce26ecd66115b385", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.21.4-esm.4.tgz", "fileCount": 6, "integrity": "sha512-af/DEj/d6h3+wwoQkIqU2TQTk4j40wKkD8LKkc0rIW1VNHqsL9SEdlEuKluQmmJcFgj2s3+iUy9wFpOGYmYsvg==", "signatures": [{"sig": "MEUCIHDCD7Qmc710AYSlUFuX//JXjEBwOCfjVzcYuUVTxhCOAiEA+DWSeZAl88mep7pUy4n2vFnAUpdpnCrzmYE8RXqOPfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8886, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkLD6TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoNCQ//d0RhJjxY6JP4k9yPGiLqwWRu8K6KIckzoc94fGrX6uMwOaaI\r\nhkK0EyWv1IKHwzKxEmcd8WVshI8uEgLaInH8tcfdOMlld/2Ms4H2GU8icKZg\r\neV0ezZdPkw2i3q5Tj3eq2ODlOD9RMKw9T7IgINC01hLYVcjfcm2UEedlVAUk\r\nnW+4iQdTOMoDSsQJWg735QRre3K0ItdPCF83GxqtV1ioYNFtYeHZ4e3N0Np+\r\n12STUoXlF5qXr5KOJDbJ7XYbdpOIVRjdJt9SBZXfHmBgnMtbQC5/lSSNIV95\r\nFBFLy8Gt5OjZf0AqR55cSmyYB6H1Wna094oiX1idGSvEbCrBB0XVZQ4rdkt4\r\nxVAg0dxmbUoAD+9BIwXz8pVDkhPMIfYsZqgFruD5NfqMAfgPG0j/aDhteBfb\r\nq6PmX+j0GwOt0TAE2dxWthNkrmULPooRPz3IpgA02ZUGEhQjT8oOJbGyqTF4\r\n2Hd+g6UQk86Na8H/hdTA/UshZC7SP66ddhLsTy3/SeMVn2GJmAI5ympRG4lp\r\nVJWNezZjW73DahBwJnH5WZO4jJwUJRT8oysFta3Y923t18q2KLbsPksHdWGR\r\npxmzUOUhT295sgiLdRrehOZhuQ4Ewsl7YNGuCSUDQhkU4Y+ceLlKFTQ9qicy\r\nmyOfR7awEXzYjw9Al2vs/A6ZziMVz57itNM=\r\n=nt0d\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.9.0"}}, "7.22.5": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.22.5", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "devDependencies": {"@babel/core": "^7.22.5", "@babel/plugin-syntax-jsx": "^7.22.5", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "49af1615bfdf6ed9d3e9e43e425e0b2b65d15b6c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.22.5.tgz", "fileCount": 5, "integrity": "sha512-yIiRO6yobeEIaI0RTbIr8iAK9FcBHLtZq0S89ZPjDLQXBA4xvghaKqI0etp/tF3htTM0sazJKKLz9oEiGRtu7w==", "signatures": [{"sig": "MEUCIQDUpkt1lawT77C4BDj4uW0oCAZn0KpcoggAR5kvFt0zEwIgR7DvOHeSS17S2eLqqxCT9Z1jxHPCjuoxpDYSjSOLtCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9176}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.0": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.0", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.0"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.0", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.0", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.0"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.0"}, "dist": {"shasum": "e4230de5ef652db13c809d87581e50ec627ecf88", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.0.tgz", "fileCount": 5, "integrity": "sha512-CG79szbl2Ln8o7+p8gCQBHkQH2i02HXhNAJ3OnmpctmJ1nSixZP1Bl/enJYBM2jo0nbEvkTl5855wCsLA6LtcA==", "signatures": [{"sig": "MEUCIQCrZTK8W7+t0+z9jE5+Lg+FfrILSAVNZIw3H6MYTUQF0wIgSICijpFdqwpNfnK4j18rZX1UDZ5FyETHQbTzajRMHT0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9197}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.1": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.1", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.1"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.1", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.1", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.1"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.1"}, "dist": {"shasum": "3eea7d10e856bfdd9b42fcfc1a0b58ceeed3f017", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.1.tgz", "fileCount": 5, "integrity": "sha512-HYdQgS6CxPSCMduPw5oLszMxWIHLdniwF8Mk310JaXhwf3jCIpW0s3Z1QOImGDgHdCTIaw8MNZfEI6JmAkCMfg==", "signatures": [{"sig": "MEUCIQCh7gHFpR43q1ZLzXKFvBgFs7/aVOcxmwYvEQNU4CQiFgIgC+jTU+jB0BOAfXYjp8LGk3Z3BpJT91RHPhLrpy49cas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9197}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.2": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.2", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.2"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.2", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.2", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.2"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.2"}, "dist": {"shasum": "e207bf0bfcbcc9945ab21599c943a152e671c6da", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.2.tgz", "fileCount": 5, "integrity": "sha512-YMdWZk45IffUmsA2GyIk/Wp1wab22ClblGKeo2200yXk6jMF7w7xUNjz18cYQP+/r2Xklnp6U17NoloYSHkVlw==", "signatures": [{"sig": "MEUCIDwFNkBQR8YtX/SiDxnl9Hl8MfGtggZKOnHnj0Z0pcHJAiEAyJnV8+NYoWWM45DU05r+Hs6N7djUcU88rTSsVlANxxQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9197}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.3": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.3", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.3"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.3", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.3", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.3"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.3"}, "dist": {"shasum": "251965d0cc0d2cd5263ba340cb4d9e088e02f7cc", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.3.tgz", "fileCount": 5, "integrity": "sha512-tJ5S48R5BEQBWgx4dMJ3ZACYK3aaquY4bR27MZj5Zen2vyREVODhJWKFdEkP2gvwmcHWsyzGxD3y7f+1kWpLUw==", "signatures": [{"sig": "MEUCIQCoS7upBJOmXWoPlxQoeeonyc81d3IxnfZTmAmSlMst4QIgZTeyLf9K6tUHgi9n2xccSDafm7QjtxNrt6UsQjJv+do=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9197}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.4": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.4", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.4"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.4", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.4", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.4"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.4"}, "dist": {"shasum": "acd939210e5c64b470ba2343fce65f6e29f58d45", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.4.tgz", "fileCount": 5, "integrity": "sha512-xLelJDgzYExrk/L7NbBK+MMOxhb6TFtvVCUjnjM9cuxwABF8dWU8+saEvuvan6AHGvaEGdRekjC7CVtlFekfww==", "signatures": [{"sig": "MEUCIQCJwjM/hHoAf3XyFKBgdLILJvRl+o8HXOGHjB1d3NkncwIgU2jCU4LRXQkGaE/CUvXkThkZpW7l7OdXVVxlbe9T7gQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9197}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.23.3": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.23.3", "dependencies": {"@babel/helper-plugin-utils": "^7.22.5"}, "devDependencies": {"@babel/core": "^7.23.3", "@babel/plugin-syntax-jsx": "^7.23.3", "@babel/helper-plugin-test-runner": "^7.22.5"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "03527006bdc8775247a78643c51d4e715fe39a3e", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.23.3.tgz", "fileCount": 5, "integrity": "sha512-91RS0MDnAWDNvGC6Wio5XYkyWI39FMFO+JK9+4AlgaTH+yWwVTsw7/sn6LK0lH7c5F+TFkpv/3LfCJ1Ydwof/g==", "signatures": [{"sig": "MEQCIA9PTC/Jr0EkMliQhN61IMSTaUIq0RfcfmOboR5CU5ofAiAkM3JtccFnZDwmVZt08sptZ16EqRJSDGXVflCj76dJ+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9256}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.5": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.5", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.5"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.5", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.5", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.5"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.5"}, "dist": {"shasum": "0f7ea3fc6f25d435bdd89fc3b8eaf0cb356e059c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.5.tgz", "fileCount": 5, "integrity": "sha512-zCOQExHWkm2bH1xw/F1rja9X4BSeQ1HmNSXBVKf2LV0j8wZKUP6eZKM1/dEv969M2s7zX9bGI1areVrCAPWG6w==", "signatures": [{"sig": "MEUCID2oIN4f5VvG+ZB2t6f6tppZqtNaC81q6o/RLgo76yz9AiEA2m7sbQLSiO+ENFZJWNwO38axnv+pnofvO3gQXwy2qBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9310}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.6": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.6", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.6"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.6", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.6", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.6"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.6"}, "dist": {"shasum": "99564aa7bbe1c43df9f63e1625b2816474226074", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.6.tgz", "fileCount": 5, "integrity": "sha512-HRuatvxJWQeaJjKEHhvwiuDM/gFnWyqOQhx5ExWfLyaQFzVLJ2H9M8ZJ8EjNA6aBGV7YKa6M1wmVLFeiJgbr4w==", "signatures": [{"sig": "MEQCIBbIryxrcNdroVjhFQ/i0fbF45fk01O/NrCh7S7OE1XbAiBz70tpgdEfJ4FfffGQsgJ4gumgwyD6+662xpmdZpkT6g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9310}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "8.0.0-alpha.7": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.7", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.7"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.7", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.7", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.7"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.7"}, "dist": {"shasum": "505c92cc2b52b115de397a8097ae1d07b9e6213c", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.7.tgz", "fileCount": 5, "integrity": "sha512-vuKtLzENlpRqQIzbqKskU60EmZRUzsio86aiXFzcNEmkwjD/kdGYzJ3axKiNUoi8kBl+IC57eLmJrB9qJk5dpA==", "signatures": [{"sig": "MEYCIQCY5KtgMlCI/PNxWZT504ftqo+GSkWOxXzEU59TOxjfvgIhAKW2n4HobkWE39oMLzmhRzc4uwL+OLWJvY+3IAgQr4FO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9310}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.1": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.24.1", "dependencies": {"@babel/helper-plugin-utils": "^7.24.0"}, "devDependencies": {"@babel/core": "^7.24.1", "@babel/plugin-syntax-jsx": "^7.24.1", "@babel/helper-plugin-test-runner": "^7.24.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "a2dedb12b09532846721b5df99e52ef8dc3351d0", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.24.1.tgz", "fileCount": 5, "integrity": "sha512-1v202n7aUq4uXAieRTKcwPzNyphlCuqHHDcdSNc+vdhoTEZcFMh+L5yZuCmGaIO7bs1nJUNfHB89TZyoL48xNA==", "signatures": [{"sig": "MEYCIQDkO4ECx4sZZFGtqibkeMKK2v2X/A8XAoRH4F8JQQTGFAIhAN8G65lBm6900noFE0iGAJJXPfuuuemdChHCxyN1BIPs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9187}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.8": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.8", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.8"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.8", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.8", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.8"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.8"}, "dist": {"shasum": "657d6ef8f7740cd8c2e4aef9199ccf3561a7dfd5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.8.tgz", "fileCount": 5, "integrity": "sha512-7aHjbDbzQl7dfQ8b2TnX3Cs/m+Yuu6IspXGqWDyTWak4OJWolOWxYl0OmQQ5i8TF7cHPUodzSc/hy+5chsoDug==", "signatures": [{"sig": "MEYCIQDeFWbXrQnXEkWk4+35d7vfCiBiD9ei2g6gY9zw1TTdTgIhAISSB6JKLVN6dcI9vTd8xuPoy1X2Uh8DqC3eHGenGjCB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9224}, "engines": {"node": "^16.20.0 || ^18.16.0 || >=20.0.0"}}, "7.24.6": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.24.6", "dependencies": {"@babel/helper-plugin-utils": "^7.24.6"}, "devDependencies": {"@babel/core": "^7.24.6", "@babel/plugin-syntax-jsx": "^7.24.6", "@babel/helper-plugin-test-runner": "^7.24.6"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "4e1503f24ca5fccb1fc7f20c57426899d5ce5c1f", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.24.6.tgz", "fileCount": 7, "integrity": "sha512-BQTBCXmFRreU3oTUXcGKuPOfXAGb1liNY4AvvFKsOBAJ89RKcTsIrSsnMYkj59fNa66OFKnSa4AJZfy5Y4B9WA==", "signatures": [{"sig": "MEYCIQCgB16kjYhYpS+EYlHWl+puLawjr3Fzaa/EWEkmbfT1sAIhAPyYyYE7dCUVyRt0x9mmdjlSTsrhwPtljvk0ueooY276", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75116}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.9": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.9", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.9"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.9", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.9", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.9"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.9"}, "dist": {"shasum": "a43bd481b0a5b55048dd40bc26390b25e57fedcf", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.9.tgz", "fileCount": 8, "integrity": "sha512-XpZI4dJXH79PO0cot7WSwM9CJaOvhx4ALDdR4ypN+oMKLVQ8eBFjFDy28738aUynKyuTuerTYZdBX2TShqq65A==", "signatures": [{"sig": "MEYCIQCYdtVoQO3MQYbGwb1sUlLYop5f3UdHNOYToneEDduBcgIhAPs2+367uYG4RvLsqT/TzAwHg7ivAlO5azx/fIcHJ9kg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75568}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "8.0.0-alpha.10": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.10", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.10"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.10", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.10", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.10"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.10"}, "dist": {"shasum": "4e3305911b84c103cbf548672fc356c65cb4e5e5", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.10.tgz", "fileCount": 8, "integrity": "sha512-nrIBX6H1F8RYg8u1hHTBpGrQk16jL1HcqNQlxVbzNBLNkLtg9lKEjkez7ryWJ9G5Ja5wyUBSclWsBmAr//rqWQ==", "signatures": [{"sig": "MEQCIAXm8E2sTXj5vxUQhFApQqcpUjdx16vwjxrlmKJtljs9AiAMU4B57+STdiuNjM3yYhzG/lQpPmWj8Ydpt3o1EgJL7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75576}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.24.7": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.24.7", "dependencies": {"@babel/helper-plugin-utils": "^7.24.7"}, "devDependencies": {"@babel/core": "^7.24.7", "@babel/plugin-syntax-jsx": "^7.24.7", "@babel/helper-plugin-test-runner": "^7.24.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "1198aab2548ad19582013815c938d3ebd8291ee3", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.24.7.tgz", "fileCount": 7, "integrity": "sha512-J2z+MWzZHVOemyLweMqngXrgGC42jQ//R0KdxqkIz/OrbVIIlhFI3WigZ5fO+nwFvBlncr4MGapd8vTyc7RPNQ==", "signatures": [{"sig": "MEYCIQDrOL9BQRglsIa5G5EtYggRJQ9oP3vDje+IXHxXWBDo/wIhAPzj55qEr4G3EvpSp8BpClUQfq1NmCc9ng28Xun9P2Bl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75112}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.11": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.11", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.11"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.11", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.11", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.11"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.11"}, "dist": {"shasum": "6c10244994e80bb0d1e1a96037695c96f06d154a", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.11.tgz", "fileCount": 8, "integrity": "sha512-5XUP674JPfrI16qzSH19lc1WHIgxrbO7mzCI3qP/4DWR44BZ76cK1xaWSzF5QAOi2lBJsPS49AWpzpr46tWi4g==", "signatures": [{"sig": "MEUCIQCe7QM+nBQOVCXdbFrYQ+eeGYPFudkDC0nNVpqEf/VglAIgZt95TdLHtpCvhbW2mcOrdb/pqYLn3HszAdNjVGWuV1U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 75465}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "8.0.0-alpha.12": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.12", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.12"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.12", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.12", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.12"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.12"}, "dist": {"shasum": "e0a61f04caf168ef283dd080f2c1b1d519cb2ba4", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.12.tgz", "fileCount": 8, "integrity": "sha512-hufBN3HsVdOCccVBrm+cH7BVQgrA9rQVdnDEZdnoK5tlJuHtJ1kMQ9XC6QGztqs3tO0u8yNouqnEodS+pjkhwQ==", "signatures": [{"sig": "MEYCIQDXMNgXSYvz345+H2X76d3tZjYyHVrVmaX+CAMuLDVpJQIhAMLQ3UGhAbwM6Vw/KKAucwXjnGo1SI5LdaWATzuvViDY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 72251}, "engines": {"node": "^18.20.0 || ^20.10.0 || >=21.0.0"}}, "7.25.7": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.25.7", "dependencies": {"@babel/helper-plugin-utils": "^7.25.7"}, "devDependencies": {"@babel/core": "^7.25.7", "@babel/plugin-syntax-jsx": "^7.25.7", "@babel/helper-plugin-test-runner": "^7.25.7"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "a0d8372310d5ea5b0447dfa03a8485f960eff7be", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.7.tgz", "fileCount": 7, "integrity": "sha512-S/JXG/KrbIY06iyJPKfxr0qRxnhNOdkNXYBl/rmwgDd72cQLH9tEGkDm/yJPGvcSIUoikzfjMios9i+xT/uv9w==", "signatures": [{"sig": "MEYCIQCxll885XxE5u6N7vkEQTQYlBg/JHThG1QxG6CkSQaQfgIhALenG2b8YxQAesBF5VPFr1XsbkH7DPohijn1vIDlARcj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 79649}, "engines": {"node": ">=6.9.0"}}, "7.25.9": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.25.9", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "devDependencies": {"@babel/core": "^7.25.9", "@babel/plugin-syntax-jsx": "^7.25.9", "@babel/helper-plugin-test-runner": "^7.25.9"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "4c6b8daa520b5f155b5fb55547d7c9fa91417503", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.25.9.tgz", "fileCount": 5, "integrity": "sha512-+iqjT8xmXhhYv4/uiYd8FNQsraMFZIfxVSqxxVSZP0WbbSAWvBXAul0m/zu+7Vv4O/3WtApy9pmaTMiumEZgfg==", "signatures": [{"sig": "MEQCIDD8OPbHw9qU5Y2z0Ib0DbXV3YKeMatVLDj5mzysazLAAiAsFQ/VFOolu44SFoXBaShgcjKJ5/OvK/ajxZyum+1RDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9186}, "engines": {"node": ">=6.9.0"}}, "8.0.0-alpha.13": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.13", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.13"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.13", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.13", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.13"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.13"}, "dist": {"shasum": "4c1e95fe9f93ca34f1f37e7564fa598ce79b7508", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.13.tgz", "fileCount": 6, "integrity": "sha512-fuzfdxalk5beos44k771uPMQYbXefVLH3rUZ+5wW0cXCcL6fthA5RPOctIfUyriXCmTcZMziuCf3XTL+enpJDg==", "signatures": [{"sig": "MEUCIQD/77KqYe4J46ld4BWRY9exDtIya+spNciPL/WkJ6N93wIgaTfYHNpld3dKA65MYOYDSmjP31glqD8NpKz/Jjpao3k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9658}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.14": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.14", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.14"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.14", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.14", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.14"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.14"}, "dist": {"shasum": "7324d823165779f114f39cb5fdea929904ed3753", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.14.tgz", "fileCount": 6, "integrity": "sha512-bRMeTv69C8Y3KUnVbcxuSDRl9YDSrPhznczY83tHVItXtdJrXruFcOyalHQ7GDQpo2yew0eXaz5YhQdMC8ANbw==", "signatures": [{"sig": "MEUCIQD2A3KYELrR+4vUKwkHOhL74wlycDXM7gvtF+sZ7BQjawIgEKqw07FRMvExqTAqKJfM5bHQbGiZsDgJTNRlEhR41Bw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9658}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.15": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.15", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.15"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.15", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.15", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.15"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.15"}, "dist": {"shasum": "92d3a73b0a4529c74317619906bebbd8264602b1", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.15.tgz", "fileCount": 6, "integrity": "sha512-BnDqjQ5QK6jsn7LpN+V0rn7Ws6aUIEevR0jWsnWcbLV/cVuPnTzn0wUOcsgVmmGesQbCPg/7zy/W/emBMJ24Qw==", "signatures": [{"sig": "MEUCIQDcKl05jEkL6cExCDTOLkMYgEGyCr1ph93Z8WwWCgKOQwIgKDLRXnsTjuxTcQgECuG+324hzGhVzsSD4VpnSTc6dXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9658}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.16": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.16", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.16"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.16", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.16", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.16"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.16"}, "dist": {"shasum": "95f8808416714c1c474161b6c56e3b150e15db08", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.16.tgz", "fileCount": 6, "integrity": "sha512-1wPhfdw69XFz0UCtInK6BJsWaD2EdwRtk8owNHwVPHbAI3rNIMFeTC1P2E1SjTpN/jQNu3IEoK0b97vVwAtVew==", "signatures": [{"sig": "MEYCIQCQeruUI0OJDwN0z+ue3elZjilwCtucrkhzy4hUoBirOQIhAMxxtZ7ANces3JORV8RyTTQbDPuhqlqdcTsG7CMPWmZX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9658}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "8.0.0-alpha.17": {"name": "@babel/plugin-transform-react-jsx-source", "version": "8.0.0-alpha.17", "dependencies": {"@babel/helper-plugin-utils": "^8.0.0-alpha.17"}, "devDependencies": {"@babel/core": "^8.0.0-alpha.17", "@babel/plugin-syntax-jsx": "^8.0.0-alpha.17", "@babel/helper-plugin-test-runner": "^8.0.0-alpha.17"}, "peerDependencies": {"@babel/core": "^8.0.0-alpha.17"}, "dist": {"shasum": "2b9302169c4dfdc35fd71156c1a0669109fd25ab", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-8.0.0-alpha.17.tgz", "fileCount": 6, "integrity": "sha512-wm3boNFThKdGO8ceVGD+XW+pgh/2b7co3s/WdhJJ/YJOFCJ6WrsUXPba3kHcoF0PZqcnTgqPmKUjsLH6zOJTLw==", "signatures": [{"sig": "MEYCIQCWJeb2A1U0FRYx/MCnk2AS54WRKvO3DsnVf7jFpxcMhAIhAI4wSg43PglVI4vkaGsvjkREPmmoHi4TYs9W4u5PxG0H", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9658}, "engines": {"node": "^18.20.0 || ^20.17.0 || >=22.8.0"}}, "7.27.1": {"name": "@babel/plugin-transform-react-jsx-source", "version": "7.27.1", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "dist": {"shasum": "dcfe2c24094bb757bf73960374e7c55e434f19f0", "integrity": "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw==", "tarball": "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz", "fileCount": 5, "unpackedSize": 9186, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCbm/FQGwWoGa26t3MTRsY0LKdBuS/hrXPZ5+g2AS8XMwIhAL2diX08Gs4npFcMHM3j0L1csIkrVrTPolHBu/S86IDR"}]}, "engines": {"node": ">=6.9.0"}}}, "modified": "2025-04-30T15:08:56.087Z", "cachedAt": 1747660588772}