{"name": "@radix-ui/react-portal", "dist-tags": {"next": "1.1.9-rc.1746560904918", "latest": "1.1.8"}, "versions": {"0.0.1": {"name": "@radix-ui/react-portal", "version": "0.0.1", "dependencies": {"@radix-ui/react-utils": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "08e91c651de12ddde4450056a5bb74c5453d8d20", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-rfoz58QZqXtJum+6CbVqNZogAUv6OQbRnXyEwpZBSUjtdpiiEcqdwgzAaPBCBY42RBcu3cY1Afh7piIqj0EuUA==", "signatures": [{"sig": "MEYCIQDsCVd/nWcvhjRufRVtwYAlNoZFP8M8aZ0jDv0fQWDXtAIhAMH3uRpuld4rE03kGJyIZ8ltKKgjQOlAH3BxXLKntMBY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8638, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbTCRA9TVsSAnZWagAAsHYP/38mYrtpm3ik9Z81boCf\nXHfVnCfQvm3NzRYVF9lcjfFNu1NuqM76aMZhJKAHbxIa2JPvKV1q5voyS9zN\nwcchyWyul6x5/AaecUU8zXCTg9o8rjVCbVSKbDKd0BeH1gJSHRaAMS1WMq0m\nJ1ZbwJjHaNse6rsjVr4kZGDzxuAWo9KTIAMgjk+voGD+zDHZvHoIEWyZsBdx\nA61f9EZJCrwALlzNBa+ZaLI0dGXEv94rCv8ypxlvsF5z9CYeWKzKxnVJ5gJe\nkm98cFerq3Bwv1aY5o1CDglzgrUczbGjiiHahtzQ9KFYAfI0numls20bN0rH\nT2YKIdt5wiALnMnna+BSAJTtwYIndCZgtsqBxXW5jz/mv7sx3V02ECaITU0v\nT6dZIDUYk/wIcb4zEnnfgY1r63TiL1ftYpQ1Fk36XNudi8WoYivLBIYeVOLK\n4i+u24VVnPfkHx6+0OglsIwY/lCHuZRgbwUDPZRKAC6b1dq7eBspZf6a+HRl\nMTNBdTauz8VbXolhoxvJKnrt3durUlyuk/ZkOC3KEOZ1ROez4VTPVoorkDnH\nGVdJy1jS5pvUbtyPjG0CCfCicnKY+sFWqAI6x4oVBsB62LTjyvUQ0fL5tIeM\nFTERzNe50Gzc3t9Tbney0sy2CUlnRUleiWDcFKWu1PnR5E0w7t1qC5ooWwwX\n2B/r\r\n=vAP5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-portal", "version": "0.0.2", "dependencies": {"@radix-ui/react-utils": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "10ca44a3d78c980ccdb8d7e1c6653eb06b3fa573", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-6AR8RSDRFMSpwyncyFMfw1+XdjzcNcMq2zfJgJgZumk/pwY5OBQhyL8/MrsPbo1bh9Zu3c0itcWNF0j6eCliPw==", "signatures": [{"sig": "MEQCH3FkwhZwKhspN+dJ89NSL4k7F8Kf7snIKxgkaKBCXUcCIQDt7q4+0KnqzsCsCHNXUqGx274+p/ZqOa1PljDNZzISLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8580, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvNCRA9TVsSAnZWagAA85wQAJQst08623S4C79/22UK\nSpnUD4P2S9dBkS6aY6ckedTm+LPo4prWezRwd8GvU3mxK+rTlpVqKv2ny77N\nDTLEIhU8Dy5S61zD74/NIH5xSjWljQjLXPN9SYuZjMVmkIYxzc452YvhB3Wq\n0xOp8KV+n2qbcHAea8h3cvHPXsnFyKBgtO4H+Qtrh21uaoDbsHAGH1DtbQYF\nma2QGDVWM/ISr6Hv53TcAJWuZCVqz9JI7ME8D5oo97/8PhM4R4TNpQl1isyQ\nKpA8AGoHiCJLHGlrV4RfC7+5xAAE0XdcsheHzJXgOp186P3W5rP6hZJf5gjx\nLvwyqJWSPt0g7oAra1MsSlCWxv0IT7+Ro5n901TVNIT9C2omhpQftvil/lLF\nHIYK7zvP9FRhwolTQCP8vlDBK4g+jT/dddwTuZ+o8sUoXGBNRF+P3spscoQf\n+GZKGGnUwNZnlnoAV29LTDWSOEfCslvAUohAwbiWhJqJKRiUnMAtORrjU6mE\n3TaHBwCeuG8wdtIQgVW1hPBBrMs+IToabnPKl9hCFM75htG2H6NJ4HMYgfjc\nJxtK3mkELyPF/w27lixQYUdWo4BmCV7X68p55XZO8Hc/HaTowR8wUXecGStB\nbQnHZeaRiSY5KUBMTzKg3o8LtPY7DUOi6kkmuzQbHTbq2QJX7KbetCuFRX0s\nt0RR\r\n=DYBa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-portal", "version": "0.0.3", "dependencies": {"@radix-ui/react-utils": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e806a96962a57d4933a6e66c1e7245bb23326e0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-0Y2Z5jfhYAEP4rdwv2AU5qWMQrl2cy/81q2oUUGmSNpkD2HPC90DpWhZErH/QxcKxTzQDXbrs7/w7qaNhWWpnQ==", "signatures": [{"sig": "MEYCIQCvPm4ggscqepwYBIPM0iv3u0mbnZ16hRima+8nDa9VrQIhALY4ixtOmBdRxHHHeScl8KFj7GlaDQrE0q62fL5a89H1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8526, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtSCRA9TVsSAnZWagAAeR4QAJut8NJGSx2SWQLHJB7d\nVuEZl72oDSr5pwniebhPKCY9u9/u28NyvwtadaavPoMGyZ0nt74RP5pA1T5W\nyGGqOQGaziuLITlHMFREApZsYVaMV5U38YCLyoTMr8cJ7HE7Wq+vV/96K4rK\nVtDN+tB5XFaw9mRJdLq2hQXOkWWjm6pm+fu7z34OXAHH0NirhK0bO5927NGa\n95tT8HHLf4mQijFk7/cuzRdlMf5uTgOOFRX/4JR1Db35JDHCBkntfrvFyHCk\nXDbarH8Z93KeTdRjo0OH5HyjFKC7MJBbnBioUrF2wnFrGWUgVbp3c47OzYeF\nnlgZKJSy86FBYsq3H4IkbzND2/QnDGj4ex3u2SiNRL/q5t9rVv1nKXB11nSx\njwZfucbhGh79ltC2KNfz5NGZ75D09IOAF50fiJS081ggqNZ2Xh9k65MmcDVx\nvr3Z/XGUCjTl86vzW72T4QdpNWIIm31fAQ2DVTUBuFBmZ4Bc8e7/LgI844fj\noAGaXB4UNzDYpJ4PXKFofSdjfCFVV1Q7y5XGFZBeZ00QMfLp/u4ILTqsrnRZ\nlal+/DWaBewPBwz9cekdrZsMfmB7PNiJhBA/wVG5gxHnJ5UnNozvVMOr5g9t\n5EvXzbKliSUuAObV4khcVtxhl0iV9HMUcx0JVaO6QK7lD98XT4oj64jN1Sjh\noaTs\r\n=eUNz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-portal", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "9f18e668fbac920eb0747eb324e9b8ee4e57a985", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-lX3v4uxl6WzzdPBU06qVAuH8mDB9xTGbqLtb2HArM/CjnJLHk5yip4S0osZhNB27FVw5+I0wjll6NALpjG9lSQ==", "signatures": [{"sig": "MEUCIQC+QO8IzSMXymYj+4Xo2Rq+6yyd6HrHbymM0t8tXYqL1wIgPecLERF7ntyOcioNSqqmSMkxE/qTS7exL4pGDOKKhak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDANCRA9TVsSAnZWagAAWNEP/2TcaOXENA3Z3+G8ko8J\nEclMZ+fML8k7QuI7Fyv9AHaGY3iepluLu1jMWW3QXB7ZTuMLexIS6Hdw8Q7J\nJiJVU2HX8CW9l7NIqBOTeUHGTrdVqpxs2XSx3C6G09UF4cyb5valbPyZga5b\nr8LkYE3BvB6Vc1+GfScK62zPqSKK0fGl+z5SjriorTUPIJot1q3L4tiSvSk8\nZ+qtarLd3ikdWcmn/Zh2bmKXuciqBhZbjGjfX/nrDAWsQs6ij3jPXx+8k5we\n8yS6l8yDukdHLndcXIetRFu/NbwgkwZh8hMvpC8bBr2ziAHrOraaFQfy73Jf\nHQdoO+q+eyKry2AS/EKPaoeyfJUL7OAUd9J1FmBDpXYl8kYaG+N1lv0Fc0PT\n4iINcjlSZjmJPZ4sE3izKBq0p5fyHJ8rdvjDAuAJPwiXxjAcm/AdoXsXTU52\nvOJDYErLmJr32PjHHwUE0fP536GA/m/cvG3f8vgj6Iemtk8/PHyu5go4jIq2\nLdxaAq3cKNVuyUATdnyvjZSVwOMm72znRyZhQ2bXXzA1k2qjNqUIK6VkIoSp\n/32qA1pv2QotoydmfYeMc7sJHITPbInw3FiB7iS5EhyjVc25BNSaTfowchur\nchzmO3Fq4ZttXVpzyHq15ilQd/a2D9bT/HxXQrRdCpfnycXZI4vWEGQLqpmZ\nkKXm\r\n=A8nF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-portal", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "3fe61a8a92dfd8d117b7a5ff12d69152647f930e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-LLo9hPSkDcz/By0Axh2iRZ0gBAzpyCPH1pVXJzjJCsi5ItAtvGGK6yenn1eQU/HOIZJhne4wi5+FSte24NpLWw==", "signatures": [{"sig": "MEYCIQC9SOrZyP3hjnl/aeNbkWOaS9E3FXWVqgwN1DJknGiTmQIhALLplA7eNGjVqLRJQ9py+VbOqYpT2ExJxfuZbKLIYDOH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 16120, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/V2CRA9TVsSAnZWagAA9EsP/iJokLgqKtcRVomsAoLt\n0w46BqYGaxTzRW7eeuE6EAGAK1pRHDx6yIRbbkqSMrdPbex1QasGrsrw/YCG\nnpxDCQ1Kn2L5GyIlJXfRPbR4QQtpOLhxMtdmjwgifXHSTy26aXBdgyhxJ9ay\nnmGpTQt7OjuP5/ZlIDFCre2Zx7nvztGiIPtqitYz4DOTEguINia6icGg04Sa\nRIitezmRKOmX1NbQq4gpg+0J6GyWAPeX/Qvy06Ultbiz34cbRlIYbGvWjkJu\n+lsTSi2YMz8QWEom/HtzGZwusONOJ19HomhpAvqyynEoxLJ4X2UZyMQuEDPU\nXFLB3RyonrqCFdi8eq8e5Ae1bommxPZn2cGenNC2CAR0w5Zk6D8yoqVE7eVP\n0u2swGb07Rb7MYI5FBYqLJBL2Ke41JbAxWJgbTqt+ZUlo9jvQfUG4FQk3iXd\ngKMpyNznaozth5mLS6hrnHWhvjM7yZje4YI9+fUECT3m6CTheP+b3kGK/5eJ\n/mGM2+VSGjyqX83QLRgq9Uy6mUP1TFCA+6HNED6wwCV17uqES/i8S1lo90c2\no0AWPyH4hQWMQYf0j7Myoekwp2CDEpM9cEEk3w7Qjhux3dDRfTvE2RmDZYL4\ncgDFNuEWaAHyuiheFD+7xRDSKoWi9uBO4xMeJOG8RR3kcX7GzXMIeZBZ3Hld\nfsey\r\n=vQA9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-portal", "version": "0.0.6", "dependencies": {"@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-use-layout-effect": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "fc69aa73446af17fb4fb3353a611dcc13805933e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-+OthyHBAr5WCXmXgiZX2cFFTs1+4iy81NiEwz5Te9ABEs/fNYU0fDVOIFtUYlhTPxpbHDmgq3YDXoWCEDCyTCQ==", "signatures": [{"sig": "MEYCIQDvF6/RgAPXxnLUW9V78qa9JZsdVlX5ixYHokcbO3torAIhAJHWq9TeQDVJz6BKDPDS6LLK7x+ZXeiUWXELpNcfvqA6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14738, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VPCRA9TVsSAnZWagAAqQUP/1/8dBKlFOi6xpKn6qhz\nMuBbsl2Cqb7fg/jRr1Q49ljVwnhkt48BbIeMDeT3YHzd7Culsa7Tjv6d2/2q\nkmgD+gUWvzMeW4KK+pmi90fMkDaXuGvm4qEyFxmlcW/KUiHw+zX6u0+lZXtq\nqi2E1BiTlhRl8WPCCp0Z64fmzQNKfHd3cixp2D26YsLGhv1dnfgv9kRVEt0U\nmogGYSdbdht4VIXilKzxmS43Shy917yPRKryOwyums76Rs2ML3Pk0YaJGm6x\n8sRXb8MneadvG0erOsHBAYK23jX50mCoCahYTHGOXCwoxpYYEtj5O/6p1ALp\nCXi7vp8yJ4AVClQml5tKtjyPjGaCuTOYFxTo+1o+No9iZx61ioAGj3kapPTy\nvxHc3GApHwte4eSLSZ/dzgdy2y6Ytgq//MU3FCQ1jtOghN+CtDGItsAyjQ5m\n7oGFlyAZlvpjpcsQb55uZig44wZypgA9yTF/6gk5rwfUJkuYKx8UBpgxYc2n\n9S6AYBkv88eXjLwWLmKmLEcKMMN9eCZXzplfQxMpgwisa4jLR4DK5LTAbImO\nKNnmsoEEY1QnU+9X/8fXHd1hQL17BDFSRG+DFxo4TLYHWmAz7ATErUum4EM9\nG7CVvvgNxOm0ojqVoqymdIyyeygmdD0e13ICy8Ut8hMJGfAKmWAiPAkj1ahv\nXjXC\r\n=zsEg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-portal", "version": "0.0.7", "dependencies": {"@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "cdc8518bf8f46d00968d3891721a9bd2763e9088", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-QK3c7LxJCcY5RXRMskUTeexHkHzavMN0f+gfZzLLnf/+l4lYHSkb5kzYrTfeItd/yJvvc02rE1MHtSHsrnS19A==", "signatures": [{"sig": "MEYCIQCKH3BG/dvX8clAewH3vs8xr/XYmK/OYKwrCs9RHWxziQIhAI6PoZt0IR4IVqu+iuPA4SpPWRl/RMzcNmeBNnRiYfCW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmO7CRA9TVsSAnZWagAAkbsQAKQBDOuFRujXfVrDMw/h\nS+M4hEXrdQ6mOOPHciM35cq3SCjFSZkR7wzxCAwg8MmtysNroknIO0X1oz0x\njE4yPdLWh1rXBYWY4NAHP8mk+jXe7pQp/n/9+xXjjLot9B5lkzvwXL3HfcX1\nHIIJM8ygVt7wUmkZGTPnFjncb6+5IbWvvFIvXbnyDJdo/+Iep4ojNjCNUX0c\nsltzI93PU+7ca/RXtf1EE7pJf2NxEw/t8Cw8sFfXN41pZEgn/13Tt8LEJXtO\ngsOi/YrLT5Wu9PGz1HNgYpCQIuYmNMYJhHlaflQQ4YhCXQaFk1gou4eRzoJs\nVCv8t1FjhMdwUoc8whcLgGPanDBXbZfh1aqIgnJPmHTrhcnPE2gXlg10jxxX\nFC/SJk25oMBNAWmUQzpVm7qO8h2q7khUhNGjk1SfGWOvyFVleOmMsjjE1VUD\nkfrT6gFHRsvr7CsUlJ2dMwYILW4aF3QEPLDLHwG47CrBxkV0YnJtOjPlJyst\nSTpb0RNe54aOFOFS7Vgr9Ku3+xSXLHPI+ioLdLWitaN95dP4+jgYnaKZIAsD\n6gpuu8/48cWqZLFQO0LHoite2qeKHWBcgIUL16C+dtd2THaQ3PqFYoXEv9ge\nDCn9HsdKnTZ/XWhFeNH80TYFKz347k901KdAVoCE/3fsx9l7OAtI5LwACF+V\n49er\r\n=pB24\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-portal", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-use-layout-effect": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0ebeab6757c44b4799c6da486faf77441460c581", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-nbW40PIGEPt3htwM1nHVG3b4sVxJ6bTHuB09mXc2XWXmAwkaYX5DYf4+51QY0BiT+6ohmAfSA+TlJojy4DblOQ==", "signatures": [{"sig": "MEQCIBXUfHd4fDORvCQw/wlJpu3kyiHKfQZwNAzrAnjfqXJ9AiATSx/rxMGJDu19MycRRRjnxnjvLy76WrapmQ4zBmrQGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0gsCRA9TVsSAnZWagAA2zUP/jTKvKBMtTLAXG5+Letn\nhAKLDkq/r5BbmlWdf2UMPKSxHlSOAaAFW0VAda+NQQpBX0obly3G1bEpSQf6\nap9d+wpMijpEv8iHPL2+befql5C2M1h9QVGlvHtlWc2RJs9qXHT/2Z8uvbXv\ndnjQwFd5cnunJnZMq9yNy2gLjKPzNMH//lyQl/0odVcxhoga3rf2bLsJtd39\nhE90qdnWstx/fb7Yfj9T8NZ3futqRtdjPlqPCRfn1IOYhm/aY4NmGDlF8if1\ntJHhu/bZh6CBj7mAUUzlU75E0yaPMhdvMFhy8H9bO4DKvKnW9l3l3VWpEr4q\nCBhO0ap5wco1I9AHOJzSMNI1V4XFAvLUsZqryLix98Jq0pU/qa7cKxrDjrUq\nmFjoYH/mpovY13SW3uh9oMItIu6I26JhBLcHj6OHm23RvsjKAPr+nrRmOzVP\n5yKfjK52956aRg+NrPwI0BbACSpC9xhvrWyhSty0fqJOnMch35PqZmZOgFu2\n++RFpKCrepXauT2I3P+tgm8lPXljk3XL8Vf0Tf6xu13Q2ieplLzgSV4EK86i\nVCt/geHZZPoD3a7n9+wao6qjTACvPe9t2FHGVnjQwwqEDPv5HPVVWhXzY/qc\n1iiHbnaJVpNzNezAQ0Y3JjyoDuYgPrePMpcxzA4m1B+r/h7dXZC2KvvLiYGH\nzqo5\r\n=AVBV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-portal", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-use-layout-effect": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "9eae8cc22f63b40f4f80d3ea674d2ad2fe69dcde", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-hU7wvEbevV+N6Oy/WTh5wy0smuXHYXV2gRkmJPZdf8axtd4yrCZwVtiAz78rEjl7LGkRKQuLuplvs5ueAk8kwg==", "signatures": [{"sig": "MEQCIH9UsRFv9G1Mnm72ZbFIy4yLNkdRP9nCDJFXEvMAxLDUAiBiWpyC3sToM/sOCA9bCJEfJBgFujMlh2Mvy7qavRuCWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 13090, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1H1CRA9TVsSAnZWagAASToP/10e+NZEjy161cP9pht/\nYNjy/hCYHzrEm8maQ3oWvBYOiF2uR3jivtJ/SIWnQ5OYv1l2NaSLoErZeMOM\n0xGLeRfccXo6UVnfohDbVwi4DWRjj04IP2U4nj7rJ/3n+SJsz5dvAgxB5nbd\nvQWwKzQDa+7czopUCQb1iDWQj8qm+iAVyhprq4vrSOQRpxlVJddheS7PrNRP\nScqgZl2+e51oWiFUlQ1mY7eEZ2uwz1mY2+kDa3KeNzrpvxGU63FQUNmtbYxq\npobFYZsC98jFzAFbznpJ3tdeuCJnPdqGEO5/R0NzVhTHX9QAtRl3Hm8nEVaP\nRIKmfDE1pm8XmjXTxUj+Jm0Y0kIvT0Bz8H70K03Rjj5KkILRdR34O69UegiK\nDYJ12l+kjJ+UznxV6v97wYKBoKgGOW6twxo0xEZG0497XOs3KBU9pqxkIleS\nwye1+qfCs+AIjU8Djjf6/z401S+NBImIcd40VWowrqqyBIYXPK8H5hTRQPCU\nhsf/zq5wtxo+/74y5hw4in/LTG5IONKPAwHsxgsEFYw3qadL/pK/PO6fHIn3\nw9YYEvA34CJd3pfCBCP6fXiBxPd7ovk6WTRMrh22JPoXzRwJl1/VXxPvIVuT\nRIEKgOobJNKE+VcTC01xsaXy4gUSv30pqhKq8V/64Qi4M/VAoAnO5G0XBvyI\nWWVr\r\n=VcvH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-portal", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-use-layout-effect": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "cc317d493bcaefea651203010f4781ca40f55b31", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-I03XOZox8IRljgLXEP8/C/ciMlIAEUIy6XhBezb+Sh+yrL4mwpCnw/5cwuxfWzb+uaRSmMkuLCSJ2HoZttTaBA==", "signatures": [{"sig": "MEUCIHXJ53lKr+mfNnCdmhzCH/kvTN9QgiDJJjzTBl0O7GQoAiEAy7PYAakeya7QRqUChhYmWA5NLLLC+l8f/kVnSDZ0npI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12235, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vzCRA9TVsSAnZWagAAhz8P/1y3ImxsSMRuyDKpbkEs\ntvQRazNM4nwUKuHFZZN7vD9xlCRfzoH9PwOEhxdkte5Z7pUXZe72z3C7FwwW\n0Y+VTu2dbMaYZ6pZKUa/Au3qe0nbM8KNTO50z6BvTcKuOgrZklevzUJT5/Gh\nNSNY4KzGC/G5dvmzKGFS6u0sCZgYm8xYy7ApqUmC7cDGb0b5Os5KkOgD94WH\nB5Xfxe5yajiCLcxwzVBWWrRE8ZBXarmMiCBpA2XB9s0TcdrYLSyY1HboLKTB\n0X9enpQnjDiYWNRMXCuJNSPCkax/4M74j2Q3AWKQ0QvjDD3iK0uboPPDFg6b\nhd49Pst/KJX1BoyWx3Ggg1ZuI8mn4raxmcolv+PyqUNTWyWCEiV57q5ZnPkg\nze0rdnsWcddnU2CoWdgqjC3sRsOlZkSZHBv3Zyy7jpOuc39nqxhM4ikNnYep\nq6Bi5PS9yxhQXHreU4zfbFmxLKeItO+uDpHmh9ns4JNu3xuE7QvNP4Gwaklw\nx+ElQu89KCO8lS2uAzzwzoKWOri3PguSvvb7X+DKlPuzBEje7GHDCnAC2xgL\nMWk1gx3HZer7YzzOwU3y/oKbBFEL9NRoVAUwEZoH25GhQs9Ch+iuowJDK3B3\nNlaBUOhGsKWAw3Fqe9iS2EJKxI+iohYGO6sibVXkxPTrxWa/18de5vorU7rH\nsy1T\r\n=QYXk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-portal", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "961561e17e4945df1a4e24fbe6e20d5491b486f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-FblIl0ZzfvAcwZn73/vwGGxsaOMEPbwx3uTkLTEcaqmzMnw1cR7eqx+VaX4ObxdY3P8ZhT9DTLnuyiQff9WGuA==", "signatures": [{"sig": "MEYCIQD6Lluocpea3c5aPAXRAJPweD93VM1usUZ+LpENjeEvrgIhAOJWyUTWClOdqpblRr2hk63P2GlOMzOwvacQT/wZl8bY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmoCRA9TVsSAnZWagAArycP/17usyhGxi/gsmer4gPj\n6QXPJ3nXO2BBMa89sazFs+HsPHzJvtAmHrclgkYkTR9gjult0j2Va6VY4cDv\nN3X0DT1pBn16uCT7zMOib0BfiAlgOIZWq3mQ46Hbqc2iiCvFQASrUGlBnCbE\nvNTrdRtgvOF5ZG/Bhk9wHR59AsKJyZ2mAcbOteDnCiBiiQ/reO8L5S4b39gJ\nbnuPuvMCtB9HX4dhi7SxPbiS2B0vUysZsKQukwl0mZDHh3r+AbE+D7mHtpvH\n6qlHFIpOXBslp8++kLNJvQWqUv+3xjy6HIyze0oSQF7XUNMK2r4IenlHWnDA\nruDmT/Gr9S4gHX5trmRoGFwaf8grLh6VeFHXo38vEjDfGDk/7IE7LJZ3ifpx\n1cj6uiTBcLDJCBSIUJ2vMhZD5hkJ2OJN9cdQnUOBgzqc3yCyz2f+FCpQITTH\nYE8K315YjQX7olBFF41m0bsITdIXdI2EvKB/bHGdipnCWhQ59O5GLxHLKl32\nsM2pjIwld5Mi6TgOQniFPyKGmIAFf7Penz4ahY0e0EesAUY9DTgpY3mfzIRF\nlUEaLcfUwcLBDaDNObgUY/yhBaFvtxtM4VSADzfqa+0voO767mSnjIYMivtB\nZDT7yLkKM+qaXxdKs3v03kCxi0+N+HCBJEocTTBA0mTQiJxoTWDgdTTyVcOR\neLsu\r\n=a9QJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-portal", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "cfedf5535b9beaaf0b228274fbe50be559363a75", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-FhYH8Ysr0Uygyb6OMI8h2xWNGwAy+tGEjxXP0y4UscPq1nQvf+h/ni47W/iakKuqvVmcm+mf1oO3A6mGjyCqyQ==", "signatures": [{"sig": "MEYCIQCX7FrYmwGdMq6bKaA6729eDvSmTxrJ4uZMTHZCI67nPAIhAMFMXjTTueaRm0DsEpnL8pO4WnyADsGnWwwGOvi9lsho", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12211, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7UCRA9TVsSAnZWagAAEv8P+wYzPAcSc5CnV7Fd/sO7\nqIvezxFsNkMCataZK1Q2+HBb0+InbOhkymIrQciTfWBcXZSCbH/ua4CTnHia\nWBxT5DsF/DSsiJwB5tpDvkKlpG4gGOSXaLubpVCZLJukPFNME/jJuxAaBO0t\nTFT4uRmaKUoOEbVGwCR2+OeCjaNC/9VepZk4Yl2jMXfiTxhVPYBS70Hh+ISe\nZKIf1bLnOCGeBIJS6+IBjJyiAolHx0rLbsaDaipEnFbTLmeqLojbuhW3aeIW\n1Q25NDtYBGdcOu+o2wljB+v+yTrfeJGy/nPXJXJJX4E5e8ms9Rdw3e+KVbx/\nojgtOvVHL/pZ9qB+NGN8/v8YNjdBcbPU4xco4ZDyRBe87tlQPSE/yzspd4tX\nnewQkYwrHw5p7gCN81mqFFuB42dBgVC4n/U+0OgqO1kCF0IyyMjOC4bjwvR8\n/QgMYiGit+dPD01I5NQmcxe33H0kz+BkyxNQX+s9H0ZJfvVqSSiyn9CeEddh\nbRKDTKkYnfV8cpZSTIDGoguJlcLV+ax3ZVUJ7cenrv7aXaD6oDZGSnKVfOSq\ni30/P3hujqtcX26i+WU3Ym0xFctX+7NX1UeEs+DcWKEXJAwdltgmrxaAYGJx\nj9VWmPhNZP4zkVwpDngalMOmfxeoQS1Zm7l54E2QpgJcBj71o7fPUxAtPee+\nHnGh\r\n=7QYX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-portal", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "8e60e9ee9b1594f98ee4a8f24a9851ef7ef2ad31", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-Mw5hrxds2T9HTGwdDbvlKGvTUfcuKhPtqxLnizOrM685e80j+pfjAAfMSXSyfmra9KFQvk3XxmUP0d4U6+kzMg==", "signatures": [{"sig": "MEUCIQCuK66xqu1pdp84kDXFT1z+nxRsiqSjBYtWjQgXk2vbeAIgWWW4gfJh7VvSooVFLAWzhxCM31ruZb4aInSFJoaICY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlX/CRA9TVsSAnZWagAA2BQP+QGg4mEp5s2RvAt/7LvJ\ndCR4II9Y1c7CHf11qtwcE2qs2ngqNuz+vlvUehXJ7bzMwVj7pxmMIeJhQ1EA\n1LyIwzVGVkbzCNUlv6oC2edZDd4Xt7RI4FYRLiCfEG6xdnQgUDs2rJaCQ1eX\n4WSv+H4kdXjYQjPIfeZs3sFFkxU/jN/jLi66lNOURocxUM7EUOc4p5A+X7Vx\nsESXi4qI5sse9P/NZ9cQnhMyKyQTEg8e/UfbuK1/b3w9ZhN522nTbadZrJP+\n+fBFyaryEe4rlxiBpzRDOxNonNtM0Q+9uyyma8ihYy8wnGu3aw6Q6T1cbUoS\noZaI2VmANfJtcqfXcU8f0aVNl+YZ9fwCv8a3BmvMbrhcqsVegZyBxj3/WZxR\nCQPWQId4FelTa+WZ8iopkgeWz3nSR49ZFj380WbcIU3xmzzyeaSeE+WXpY35\nps8L3118JNfRT+pK2wHk5qto+sb1tZWzjdF1s91e02BgrnXEUQGSP+mpaqHz\n+cP/LJGx7750yCfREGcgUkuxH7ynFpMdVto+DlLjqhzgAoMg9Eqzn0Ad7k38\nmEZNGghNEgEZuZGr/V5nRdHxG18nRTZHqwcX1lw2K4Cm2vKu/Iz+vK93xzv0\n/101FObceU33+jmoPcp9+J/ZqWsHymc2+voQ8+WWZkW1pXqjj8YTOByZ6yfp\n3BtN\r\n=qDJM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-portal", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "31513d8777cf5e50a3a30ebc9deb34821e890e9e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-Wi9arVwVenonjZIX6znCBYaasua03Tb+UtrBZShepJkLGtbGxDlzExijiGIaIRNetl46Oc2pw0F6Y6HffDnUww==", "signatures": [{"sig": "MEUCIG954lpzlxK5KynKEf/u/XCqh8WBkTyP0ipzElxYB04iAiEAq1gQLACm53V+0vxNB9/GmSIrz5GtftQOCiQzM5MTiWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9vCRA9TVsSAnZWagAA01QQAKLtTq4EnQZ45wNXVAW3\nUS0TfQmR7cyQE8WLg4fcX8Axyho1s9KoEY0nvb4b/OavbumjwVl6+dJnc+3G\nQRJUl5tTcxb/iajNpzguL32sCdRwsTrBCfP/HDBBByKfKOPqJCfpU4C27Jt7\nLpdIs5sU7ycB1d4jQiqc+F9e0BQU0u9uaqAD7/PHkHU5AIYHM362gQNSeZMh\nEj6ft5l6Ar4k2iwvY+AM/6t7O0DKcI8Y57mWxwGWKu8rE631/Vs4P3TdZYMP\nfBY/Rh6gH1k49PQdIMZE33ejVssRJnenXzaOBCkNCRfaTnX9rb0b0x2tIwgx\nAXcOr3Aj7WeTGi9yTqBPWebKBnZmsCD73JGBvDXJ/ZLN47qdCbhcklVujRn8\nEcwz+FUs76L2OVGkA1y5CCAr/cDmKlZm5+asUxu2ca3VYc6vRpEkateEsHgS\npz4G6sJTnwCqJr0jTFn1z1hHFfjDVUyPMnVr2dbmnyn88TaompWkaBZjakns\nJ2lPxjH6YkeVKMp/JeJ3qNKyx5wO93snil00dQ5r2MuvCPEtowyr0u7lHZsW\n04h6OboGGCsONI58CN0Fv+JtY6Asx/K1KqUheAxwZjlCw/FwIifqpFkxYxxm\nDRKhA3XckjmtbFH5eSWgTKjZ+AWt55eRz3I4DQO9LqhcxCy+wS7S6rnCqpUy\nNtVb\r\n=7GiR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-portal", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-use-layout-effect": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "833bccb192aafb9420bd037d5827e88caf429dc4", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-qMESsdqph1gbRGzy9oSzUoeZYXnR2egXVcEZDqmesfn8w/o1rC1wadKkyBf7qo/YyjUX4mvXknAA+ftp1aQp+w==", "signatures": [{"sig": "MEQCIAyY7Gu+DdomH3OxduXPFjAGviLqiRLVHRGGkeozfwtKAiBQrsnKlxDChiytBn/uoyCKhprX8YypDrupPfbsa1ldLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTuCRA9TVsSAnZWagAAbeYP+wQZ5SLtlLvgLhWsSw/+\n2oE4XOnIdgYyZ+mCgiZaCpGvtl8zcEsIjP41ke8EIJ4z+893GGAnaNjhoe3U\n8g7S9gtwpMHkxys/aPT8ShBSz5d75kZ0+EZaHwUm/PpnNopxroISq1aeErNK\nDNNRPbcEIRNphgG+7FppX1C6fD/itaJKPesfc7HwOhDzDIyZNHQAxtj05xmx\n4YMsyu/8HU4hrCEfNpobemjCAI3PMjjQ3cjzQBZF1LkrDw+wYT3BfiTBeM2r\nk0kR7kQ4c4+Z1wVzezyWOVIQyly1316RHb0oDCVBMmaNu/Q3YJ9ZusEzebBv\nscZoj/bdGAV1ai1yAm9kmAGkyKtnoQCe/Elk0rxdwDq1AbUT76ywOmKv+g+h\nj+1OBn4HRu/mZ5c8H9G4Zh0G78yoPptwTKFIaBksnzZv/ULPQFO3gwCFgRWA\n/vr8DOBlRHWS4BDQew4y15sWOh/mEKOBms/E+9XPzQaAcyC/GsagkYImUFzg\ny0n8jI0zyGYV6E5kKfgHN/nA4whlQ2yVrE6BqhqW+auq4PNF+TgTTfi7gCHm\neqwcIjZB6UuqIjjjA/A5GV5bbQ0jHl69PhIcdIusn3u03SlsC6xCgr1hbr0B\ndlRv8GloGTB9fsa2sDGiWSBqM9IgUWk9Le5Di36j4A1ktICNPo8UkbAKLMBG\nY+Ot\r\n=Yx/4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-portal", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "79f0518e5f939cfb693b24800cc30b7db5c4b71e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-F0a1ygOUTBaE9a7XQM2hjZn8ihsk8nh11uwQ4W3mqprou3wNGgvReoL7VaqPpc910yy5OZjRXYz5yJHghZguIQ==", "signatures": [{"sig": "MEUCIQCIvKF1a3Bis4AghGfW+GWjoIKMQ0ECASgnyujKSiqz4wIgL/WmogL3KJL7zBXMm/dKUXEonjmKiFxBQRnOgQ/Ou1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpjCRA9TVsSAnZWagAABi4P+gImV/SoVmxvZ5z/mhPv\nDSNsLzeyDJ6yFsr+zpngqTL7okLJnQA9xk1dnJDwwK4Uw7/GwPXQY5r/a6GU\ng0/SxSKbRLOx1KbGER29Jh3Y+FS/Qlh20t77T6yyPgYzCKcGC59yPTG87XNK\nHg+TA3nEBQuOVqaCGvoucpgBMsBbhkmcOS4gX5CzaI688Nurj28UgVs278T4\nNFxVxBOqgPcWcIDcGEwZPCRx/3Rh3RhosIUm5LPcNHRae3b4C7pMrivH/wfq\nWzvqE8mzEMGZ6QzR+MfQYeLd1lrmHH2Yepr2OmsKVcALHDJz5VfGAwhBK5Qt\nUlGG5SshEnWDTHNljqxoFeskv44y6j7aTcGDdLbnlIpAOZ682+YyzUefou4I\nm2lwm5T42OVarmp23R2Y+s4c9K/tReVHr1FCs9ca5UPSyFovAUmgcrX8mwaM\nTE6NQpr/0wxkNA/JHuP7GU71D5wl4os4dVJTIsfmdvuzhBC0REB4I1sEPcNR\ngPRFC2QIsmMS0w6O3bnoHK5ahIQ7/0ofyy/wftjVxI51AvBYvSo4dj34k7nH\nDh/W9oc7pE35JPrDTAkynyyfLt8cxYdc/H2GU7noiu7PFo62e4tiUDZRq8e1\nbyXuwfcpEOMWf0KWBQpZWQtQWtqsIN7AHIf3dNt4Vv/QozWJBDZsqHxUbrnf\nJvHH\r\n=fZXb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-portal", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d711015e50ea6e7a29258091833c90694a7306ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-0teYfH/hSX7O2Wdv40OIO2B4YkO3tH2QjH2PgmyFlSE+gR27tgP5wy0OrxoI1PiyDMtDf0E+ap0y8GZGdWUONw==", "signatures": [{"sig": "MEUCIA++zMfh3NcYlvq/j666JGx809jipqBlQEjop3ipuuEnAiEAiwW7WGwaDcEVjTJWNgSLczLDdNHqye92jqoUO5jkDPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12358, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyXCRA9TVsSAnZWagAA9BQP/jx09Y3WT9yPgRLzg0Ic\nNcEduX7NMAlqX9hiQaDLfl06Y6G2m/q7KjgyWQznPSKpATA/OLyyKhPs8rbY\nJ+KfsceDrbBZhO4EqAGpMnJkbdSXH/CtCdKs0/mwtymkhj4lysKSwiaA7yyB\nxAdDiPt12T4HNX7X/OQWletQNyMK4xbrIGx/K7zFNU+nVzy/DDViSgFLXjGq\nlNrk2RJjo4ISYnKbZSGEudeR6thZlThx9KFa6ijb/emBhDRKIneRmqqvOhkx\nLkVy4/QHJTcxFwxcB0STqLWXpdwZ4lA7OSg40zDNUwSW5dI8ShQ6rx6ueazR\nv1Xdn6EXPE4A6grszCIxYLo958v5dJ1QnEqqb6t1DGF8SHmVaRRhAIwbvQv9\nrdMh6uChsNzvTI+7NuC/tVcSnrHAveFrRvc0lRUdyb8rnwgROMYkLF58x2l+\n9kiuC0dqxnQUbgbPB1iQj/i+mxlxXxy7ehAH51qmvcDzG1HyxInVygtEz8E4\nKw6+ewRjFD/2V3l4zgcq9EOAFQnmNSt1SYcLGpkH5ICDjDs8/UyNu2OaRR4I\nJHLOzq+rVq4TRtTFdoG1scp/4V6hOR2V4BVqCuvr3hqRNhn1rJwjoyUefhgK\nBTY+a5yDlrWAxu2CXBGjx0IhutSpMTnu5wT0faKOhSJCy3pj8TTrrHDQTh9P\nyM+0\r\n=xGpj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-portal", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5f72fa2f9837df9a5e27ca9ff7a63393ff8e1f0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-HiSDaQVlhoZWvp5Wy0JPPojqo31Z3efs890oyYkpKgRDWDdMYHzEWYZxC7pB60a6c6yM5JzjJc0bP7o6bye+/Q==", "signatures": [{"sig": "MEUCIAHvpO4Zk2RHvoS/JuWJLnpQishmZUqtXvEd+IzqoVHbAiEA1l3L0BqFaBgasTs95t8lxqH98DUH0QLq615a9lfTsgc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12314, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmjCRA9TVsSAnZWagAAhGcQAJXHPZRxR8yDuPNZgGT6\nTDrnuaSlF/SU72187BJ9PCIik4GpVgj/dDjEuzLQPSGsyCaksGB8yh2Dgdu6\n+rebHWgNpULG6Khhkm/9ZUBZb/2z3nUDJTf+i2jezpU3EkOJLnoXjAop3/HP\nlkSQah+qcxbb0jTCr8mnVnHsV6tur/p0oCmHgU1iwE0X9pCeA1MtPE1Xd2lg\nLoCzPtiVu7vJm8VK6a7OFeuok0pRALL6gwaX4OkSsXqZU6QiKVJlN0735m36\nrSnsmseP4THdR3W2y+633Jbm5NflDQ+qjkMToOYq/CXjFRSTWLNc24qz+AwM\nVucuqcG3e7PP6U1/hadFCiRMZrn0Zm46WsC6DZ12xUFVhFMPDlbH4qnFOl4j\nGsguQkKDdUV7l/GJqbTjM39NwSgvkzsM0g/Ts3szpdOToixKVw/A0kPC4xY0\n2yoRFis4CDDDMmrtsUYDBPHBQ7atHN6tVAl+Qrkzephgq8QCJw9vGKuWLVT9\nfsZoTP5yrI8qcXgExvZtjWmHE/Sf2p3oVYgsmeGogChbyfgwrJgMNjgO8u3J\nSbQ909FlvckLrME53I1qqxgruP29ai25PaVjnafREVJ2WUtNV+NAY2Q9LZht\nvI9RPgLuyjBY5UxMfCvU8NCwTvgVHFdaFJYRMyMy7hd1OrYuFonawhINesHk\nuteQ\r\n=10MA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e79865bf275ccc12742c6d876e8c04f8e4e9c4db", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-zvtDDxckFsBy84mTx2uJ7vOOakKMByiiaz/aRlQk2lW20UP50S6ak6fHQA0i1sNvbDN3S42ooi2oXNbxbX5LXQ==", "signatures": [{"sig": "MEUCIQCV697+PEuzHC0UzntSeb/3HBneKKfH7tbSVturBI3VGwIgZQYAW6PBy9/ZvSusriPbGczOOhfZjVR+i5I1E/bChDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQInSCRA9TVsSAnZWagAA6VMQAJX85fJcmKTFdswyn7Le\nYZz5gNHJ4sAPmpOPQF+uJzMwDZnYR2NaA+VGmBJ1V2K2NaHgw9fmjCN+rGsG\n/0gYl5Hg7CrJw2fZ3T5sGtscHRCFDL61UNbz5ZMIiZDXc9SUvKJZSvaNGeVt\nbbBShHH7BNgiA3f30npkuGwGHsR2VvXY0Jau5V1K2EZGzFCjeK9+9dN9GjI5\nJZS/MNGO42/x1jS7a6iM9QSLpx6mOcHRbex4vxHH4CzGujSNF5bKXV4KXa5U\n3RgWY6Gbcl1xmErgG25uNCfZetCsAPe6D2Sia9d7KWOQjNVqzrF/Yc2mQcwO\nUMD1msnrWwlpkJzQ8GPWqw3tcW2V85RITi8zImcP+gxigA+4Y2XQtiZSfbYf\n8WqlrOvNY/3or/+/9gvh9iO1zwxAf7rBiybO2LmyNvx2sd32cCo8HKD9aIuE\nloeVhl7Ch4vUIi1Xlg6BhXXKQpnIRczvcnIBh7c+3DrjtILZA9j8BdAvAbwc\nSJujaSOivwhPdprr7TPJX7REJ+oJxwHIyWk+GiTc56XQvov1ghz+hbYWw4Hg\nP5eSSqD1DcnyZx2EnJH2q+ttdmVHF8rlB9ZOU6b+KsGdq6lX51I56hWpXbnV\nm5oua0f8UDBMW1OvddTMoUJiPOvf3+YuYSlDagy8mcn6VhuqsKj7s/nE+Xng\nqg8Y\r\n=nW14\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "471d0e836450a5e5086486d903f4fdc903d705e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-HRSy4psu/xrK39as+HwXmrpm19sw0aS5+kRWevO3ADguqOhYD4GYT79C/wNC0LDIQ0TZpjhIp9GrlqgL0nDasQ==", "signatures": [{"sig": "MEUCIQD6qmWDgsC9S+0cG6Mnctji9JcnfpZD1PhAsV+zyaZCSwIgKtd48r7gbgfUFJgOL7mC+OMcZ6ku8IudFHRr3zqmwjk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwECRA9TVsSAnZWagAAVpMP/jLIX2iNIxro1GfA7B1P\nBnBcJgz8UiFmi04xQH2Had/0kyUvSEg5oid1QQM3yyQFTo+5jv0AnR+LA0MK\nwAquUqDDnqFSwmrMudHFlInu0abkHdOfEHJQb2M0pf90NUppsvSPr5D/cdn4\nEINV00IbEswaFgFH4UGW0QYemyIkSinQpEXfGPcPgAmA0ldbysD6J1va5KNu\npCTquFUIKfVqd0D9ZQU7W2gDfr1ftzBhgH41Fa8Wqh4pWv5AAPJ4QOufXcDM\nEj+buyZKGDardp0Mou8uL+Syp0AO6nRqgm+3Oy2/QQXEma89uPG5pImkVf8Y\n8PC3skXURL2CdKvq7qeAQVi9DqHnDbGCsOp4/LRpJPLmpVLsgSFGckWXHsnH\nyPtCi13FdYNtDsVguO6ptO8MxpU3QS3Xzu+Lm11Z/KUudONR+SPDuNd+Kyxe\nTNS0hKn+YUk/vzCFfyQOsPwtT6AskqvW3wmGt/Uo8At0bvigCxngrWygmXUz\nPoqU/5GUMsoxvPso8PlfvxZKvnntjksPhPO3t2B7QIV1lfy3MujVkeTSK3m+\nitg1dK2Dwq9PbkGmqeZvC1gdCzFP9btcjZjSepCqeCTqlCXbnMAyA5B4ESb1\n2DfDafccF8vB90xMiZF5O8qHK2ngnbbUKAu5JKbSqZIQWbpywS5DViJk0yIT\nvML/\r\n=5SfT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "c5778ebada3c3e7b23a1b51cbb2092bc7be2608f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-FMS631m0+mHINJx+zrvjslLUx7MSMiDb+NEGFHU1AePCRfKPZuO9TyZ82WL52Qlms5h6cQHu8FIztGQqvKYwbQ==", "signatures": [{"sig": "MEUCIQCTut8F5ijmjVH9Ngqtbw7PcoBQ60I6QxdbvU4/UQvRXwIgQ1/mzuCdgTfxxaOmvEoH5viGaQ07D3zWf4/1w7HU784=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UDCRA9TVsSAnZWagAAimcP/086ZxeTTB+3adLfCeEA\nHKO77nbCXNmTaXo3A+CXFL+lPw4VjHAMOGJaODGs8ia9VHG6XychZv2Opt0y\nthEaQGMF4hpCO84Oe3DXr8/Rm5sUA+2oSIl0A9lESc/ebjdZ+FfzInIkvsLS\nraTNtq6dUje0iJuZd4akk5MgNPZffqui4by33Bx4k23aACTmefT0XQazrOen\nj0e7UQTIF9kW0Jto1R7mFGdJ9EJo8rKrEec/4IsnfN17bS3uyJ13rHzlsw3U\nSPJtDjiYtgsIeLLobmoOvzva1xAq2k7UNhpQa2mkX2DJt6CU11Zvj0GGQHCS\nigg6CDtiY0w4P4MoM4QQin0m7rGpddP7FsNkvmmmCBSVHXEkhr7If6eyg1xi\nw3YWPDqVUvJIQZTYkykXxtFmvWz0DlObwdcufforirlUE4ooj+vJN7TudsRP\nrreA+Z+907QnvFaVNg/jwBeHYhYJwXvWu+87E6Hb2QthvBiBJfAuvS5f+qVg\nrd18bfSqPDQxylqswxNKahElkNtCKOFguMAxsUuUmA4fODCcJaZ3bAw3ATJp\nr1QIPsU5OnNwJd7Q9X67Fsm7jxuGZt9svOwvU4ecw9oucONMq/KReooRpcuK\nCztOTfuq2kXiUiTTWrki7bOoTUgESP7IvXwvJDngwAFPjwd0GHQvgm42HK/d\nlIAq\r\n=3Qmv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "06002ded4debaca2230bd21c009192ffc7ec4ecc", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-nrLyKXDeAHwmbS3JiGn2rWl408tjbYwVID0rJiqxBidQher76he3coef3hx2CdZ4Is3zD44VGitOyQwjukfTNQ==", "signatures": [{"sig": "MEYCIQDq9evwC/TyWGX4Tt2dIZQCo765XwbKMjEcLPOxvsEwdQIhALscwVFujCB9mwN1QP/wxcOGwLM6+lIqnE4qXCnAh76j", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1zzCRA9TVsSAnZWagAAJ0QP/RN8T4yTxDekv/obV3fn\nP3koZism9TqevYR+NnReHnCsm2GFjhmx8vC7IAXdbaW/Uy8DQrHpYk7x6NH0\nDEZDFtvuUuMsl4wjV9x/R8nJgfQfj1L44cxK/UtLbe+8eMNUiF9COMdgFwgj\nm+E9Y9EUzRtHZe+q5cE45ClSHQ/UsAnX3eoGPrWvuxmJea4Qcy7tAzVwQ3U1\nfl+kkhIbsaal346JWpoYNuKzyFhiQfoh/YTTfSUobvf4FwcTKYbX3gIkY6Yl\nDYXon6PGJI63W+nPkY2vlPL8B2FDo9xTtML4hBIve2JdfVf1nVDI6bIHaJym\nBoYqAZ+6VkXzuzCI4nNHxTa7UVUS75PoHpu24qS714Zaxf5ldIqTFTOQvdI2\nzqHQn5eBvv1xFqBN1SAz7HFwqZJQhFSvNrvOqeWgTWhQXsn03gbDD2LJBCtt\n/TGf+fldde9N7gCFsqjdxzSnWpfq+cvFViylsx11z7P6O4eB9R/piEQc31F5\n50IkZgZ68qs0jgRHkb67fWmuXyllWBSEPFvUr/iTwnVyTWPsnrfacx3nHLpo\nVfuNB5WNMQCJtLLgsAdRyrkGPplPITM+6w2R8SOTBfM6tWH+GDFffIFi6LLN\nK7K7wMCGTXKfKPlazUIzPmyA3q52QHxyJA8sda7oWkvDdRm/tZf35Y0nrivF\nsEF1\r\n=jr/n\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "4036145958784aa3e94b0429160e350789cff6c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-HJkui1hHSeTgTYVRG9ShzrSZWjxe/kAWs7Les2lELTYh8PdXNmXYzU3b9/Dea+zxscJTS1tdPohVsPre1/uPqg==", "signatures": [{"sig": "MEQCICL/7lXO4xidkgUtOU3arHVe1rZJ5v9HUKPbGbPzgNgQAiBCjSjYIBAG3ANA7R+AiNjJGDCv7ePXDmtv78xza9AC4w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFkFCRA9TVsSAnZWagAAZ8kP/AtLBnLvTntMi2wRuWFM\nv1EoAt5fXCLzxB3a4/3IkCKwOxLgX3ie5NT5Q1dlqEPjBn4W64O5NIEsC/Va\nWpqRwyCxMsjM9BWU2K3deh5cUK/U/Oawp58eiGwCDlKX4h9eQ3LseIDhoIEP\nB5vLIDsnLXdpwFL8zJDFuOkc/wudmp7A76PgGPjuIgSwQI2uCcSvFeelnQlh\n07VmXMaAlozz3WDyzAWnXeOPwHb416AbleT0JJ1yIhmtTnSNuOWvV5Jk/y0f\nMYgmuJq2ltXUbeCIdeieBhQUX9tmhM6uKVaPJp31y1bnrhCQ0QqIIsDuqlD2\nYiRMssTAWsqR5FJbZkTjjWdR+tIICRzAFyTw4bzfoLKgmAoCfCg43Cc+lX4s\n4X4k/hbTmyI5/odlApS9d2vYlEh1HEZLBi5HMOTCJhEvQkwWOTkIZXHFRAHK\nfhBemzwtGCDxyEGvHoemMvwjjbpaHLOFqHRG9YD0XtnMro/OU0EiCxFrQ54K\nF3Nai+LuDRkTm7fciw2TE69F1HAiejq32U5x98/pPd2t6oPolp+dszZ2399I\n0BdaSzSLc5HeDw9FKuB8CVJhe8U9TGXWBeTkg/vIsuWzfoi65mxeGYyXEzQy\nlDYJ61jqgDE5MY9fiw4ukRYuUo00heyz4AS3qdJ1cQkrVcfaCIb6MtU5Xcst\n7WRH\r\n=w+Gh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e5c10eddea5df119b1c169094f53cf9e9d03d8d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-kJahWZuLz4SP3PC6vsntATVQKhrzlIp3Uu4zLm892vWIUEfaxGUxVC+OIzfa1y00IedVaqn+I4jl90nIT26qTg==", "signatures": [{"sig": "MEUCIQDCQyvstIpFmzLkqnbbcun+dlbX5ecFOFIbYuhSB/87qAIgLAJaJIg1xgqjwT5yfln8RUL7I5y27DQBpX35VzZkGt0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352}}, "0.1.1-rc.7": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "91c2e4194e45391493452603a5189cf7fd878f4c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-z4Y1MfyGXq85wUmq0E2yiQvbuYSsNSpDZjcfXDvpMjm2RlrUBUnzAtSYbalbqghtp0vV0BwViZpgR67UaI5iHQ==", "signatures": [{"sig": "MEUCIQDwTrBTGqFrQTgMYv7fgPDgLl/pJ+svjkQCvBpGrbFZzwIgF63EljoBAv66LEJ27shQSHlLUt41PFkeZ7jPSYYtmPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352}}, "0.1.1-rc.8": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "4edad568fd4d4aceec147d0066ef4ea3669659be", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-vBrkQIX3RpfDL836O8bT7Xv20fmFX/++fZQLxYSPX73ckmXYDjyO/60XT0GOt95D35uCfsgwQaN+m30lgQEdmQ==", "signatures": [{"sig": "MEQCIB6fxlDO6dLtIT+3FH54Bfyxqfy1cd+ktW9bVsN/lyqOAiAMLg549OVCJZqc0il8kId5QqMUtlBFqwdCZm4UOMwPGA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352}}, "0.1.1-rc.9": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "2f2ce4fb75f2a5fdc9fcc46c247bf3d59e956aac", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-ZIyOwt/+F/n14CxgtSXJFz0mMce51L9zPXPjiNfoewql8lvF/XUYuoNBlwp45GE11oWvTn+D/Gn8kuJK6gwbeQ==", "signatures": [{"sig": "MEUCIDaDkWBEkE+veHUt0lMpe72ieUM5ii9/bbljd+YsqfT4AiEA2EN51yAhETo7oP/RmvDdQliS6WyWnUaimiM/i23rz2E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12352}}, "0.1.1-rc.10": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "46eded7bc94ac0c626fc158bfb320b0c4b3509bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-DCArxeyeDlU/4kKSnjLEtMWVYr7lmdCGtQ0r1dsm3w7KScZ3TPmxyvzUmxWI8DbIaFYYhd+nVqwZKFjIXVg2GQ==", "signatures": [{"sig": "MEYCIQDoNQkO+fTQsoZvgU2k5VXYdVDbg9nMo6PKRcDNbystqwIhAPUoa0mPLUUoA0EckXiAvaJNBgPIeMJwVIukb5oip4Qf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.11": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "8728e0d2720610ff6b5ae0755fb8d73e768aa194", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-VfiOu66P1MrAxi3ntZST2/7i97hamob4Jpl7NiBZnATa+44Xy7CfkOy/F5xG9EoAiC/9PuUrdo1uyvpiEiVhSQ==", "signatures": [{"sig": "MEQCIGs2trYFgefE8FNyfFBRAGIlY7RxvqrVYMreOjgDE6KGAiAmLJUzHhNU/PKxNXOV4I8ulZAQI0FAPDWtvIYVmxF59A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.12": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "7916cb53df5a12cf5fb28eedb47f175297338819", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-98KomvBz5UKAW+V2wj/ivJFYmUmarsdCRVmciTB+FUDVxzeHRuBnP6CdVh2YOmgw6kUuHp2wxPjjyMXJFrYIBQ==", "signatures": [{"sig": "MEUCIDk3KR7/7TqTZ2SvYQCJMbqTBtHOGS9nF3SPJe+WZjPAAiEAyUzvg6+hsWSw32UmgQwtcTb+iD51wnhu62YJ0B4Mrkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.13": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d91524542ce45dcaef9d8cc16a94df70429aca69", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-vi4pY0D1FLQSPBocGqT2dRR7E4k8F031fPy+GLUuUp+UEKXmJOAFU9ZJTU/F9H5CSIRMKcn0t+eDOc0fWvV3Hw==", "signatures": [{"sig": "MEYCIQCTaYX87jKOglHdpJuoxf62KRDF5KmUtl+SL25OC9CuhgIhAIYbcBQCdSc4F2oGxhkmFFufDYrXC5c6J0kd4e/NTn+x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.14": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "a7dd7de4d881765983e230f7c0d2407787466dd4", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-OHNTD3zQHb6vq8dHTlIXDY0m5qpDFqhCaT/hiUL+yH+fosnay0uBZpC2C41Ghz1DXoyN3tKoLC/58+F8pNcjuA==", "signatures": [{"sig": "MEQCIGVVRQbqmKdXbTTDkVjHuplzg7hvl4ggP32+e0gygLDrAiAobbs9nHzwl5WFPDYbpIUt9x9tih/1n5ZX8FSQWuiUjg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.15": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "bad9e1729c760b0d482e33a8b5304c3bef8fd3b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-hobU+KOiT20JX6FPtITfieOfX9jum+uS0AsUDr0IyKQmbylPDYSZoTBwPvDDf22L1BiL53I/mL0TaoNjmgkJMw==", "signatures": [{"sig": "MEQCIApS0kqUqzdJ7cBJvDOYLzyAYC7pbGNrFxqpwhMnqxfEAiBcmikeGHC37A5+U0p1BpbPLPZyIKhZuCZVLJTHmy82lA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.16": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0403614ae82e38ab8a67d458b634921debf103e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-E3pzOcFj9L4NlVBSmLe1QVHGd2Hm+tDSEjRziboi+23h8miNdVMVEu8KyieydZ3PWfy3zhwus8AZRxn7q+nyzQ==", "signatures": [{"sig": "MEYCIQDfX6c2ZdoOdxgzbASKLeTAF8MxuVpgBzL63lVL+4i81gIhALo2pD4i2dfqj9Wir1lTQVgAwbVwaiRu8CD2VQkiBEik", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.17": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "90c1b3c003d4b337385638d3e994fd82a274825f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-jL8z8xWe9SxxzE7HqbgGvIvxlKNFks5elDvV+7eGWtzKSLlntX/ncYZybR5iELrWrf/oRN9NX4SjTD3FXLrwKg==", "signatures": [{"sig": "MEYCIQDfIm6iivNTaMuvUt/UjqlgUuKvwz1FkgawIhsK40g2gwIhAMSDvSue5qLyuNEAN4r4kW56pnMpggeUhkJ52LxVCotw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.18": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0f6c581da96578adb1f9ff68210903d1b6fdf500", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-7+7rT4qQDNr+9rzZacYvA95VU0o4RvFfN8w1o70UBhRtVtqHcu21ZqAiPQcopm5+W/QGfgNje23tHbi+n1bjSQ==", "signatures": [{"sig": "MEUCIDU4ROEFwotZQxwbD5a4yveXPAKwJggUVfwBlpImfh9aAiEAxA0lgkQz+K+VVed91KeGOEJv4y9rYA9Cpjin6r39LOU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1-rc.19": {"name": "@radix-ui/react-portal", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "a1b76d8d36488ff478c3fb2ff97b693352130d23", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-CnqQfv830HxEYJPOAJtTnMixJo2+CCHdVg4qZl6Mf0zvPRYw2P9yQosfCJX1xEsdyAB5MCxHQ/UW2u3liYP0fg==", "signatures": [{"sig": "MEQCIHEGrER2USrPMhhXNmmRoDMw7y3NyPLKLt0ppVdpTGeMAiBV0LkEy7VJJ7o7nl+jHso0zfWQw/HF6z4gf7coOUW+xg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12354}}, "0.1.1": {"name": "@radix-ui/react-portal", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "c373f1fe7ea3b83a817240689e6885b9c115cee8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-ZJFgUBsaFS4cryONfRZXuYxtv87ziRGqFu+wP91rVKF8TpkeQgvPP2QBLIfIGzotr3G1n8t7gHaNJkZtKVeXvw==", "signatures": [{"sig": "MEQCID0MnL6DyToUVNSxrUqN9pfgdGkI+4USmBIN5r6YavVoAiBWGwN5jmDz3uXBq+C2IvZpo/5XHGuUQgM+oO7Rcfs1/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 12314}}, "0.1.2-rc.1": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "091a1e5cd611eb45455f6922b710e4ac04f7059a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-oCbG5zxaqjJ2dkOHBeYO7gSzgc06AOLrN/T5DlKlNBpwjxw3TMI7+pp7uMzcLo1KbbdrlngxNcHP2m1rDPklAg==", "signatures": [{"sig": "MEUCIQC9oyrtw+bJtfU8M3V3Pa0AuplN9cHlaRdXnF3rsTI7CAIgY9vSvntmMGvcsMavmUWIvL94mXDvrWLCLlVFNuhBE7I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997}}, "0.1.2-rc.2": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "7ebe160368c5165a68b5b9a84e3e2a4a137a2fb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-KSWNsaQJoM2oc9vcuwAz32sy7qTB80apa/i6c0HTIOwXyc5thObBbu6bRf5O3Km32fF9DqgB8sf8OxmaTl+I3A==", "signatures": [{"sig": "MEUCIA7gS0CJbvesBzNlijrLXaKqlPhHyZYnYyr5x8AHU8bmAiEA6e3jG03vzn5fPY3QaJhl+t79lEkpEZYZIZjudD40IPo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997}}, "0.1.2-rc.3": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "6ea7022b1af591834c9c85bb926f3acf80628457", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-7+WlVMZiT1wLvRDrAEx9GYei+6pO2yIN33aFKm4sDH1la5O8ZA5szcuwkDC28wz8Sw/qnNZy90occTyNtBQXmg==", "signatures": [{"sig": "MEYCIQD6troKYytWvfgq2PAiJhlLCAdz0RRoiJOPbWkME819CAIhAMSr2rTocFC/NwtSPXiHrx+ISH2NHyjBdR1BRDl+EBpL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997}}, "0.1.2-rc.4": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "3db5511e51e2f036133b23386fdfc3b65ec50698", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-1G14TeEzkWBRpsz71SgGbgALHIhtQx0R2RBo2qHtsKhNdxZFBqh5R9TdKMFc1cJqANDzeStUvZVnOWzWTpo8Kw==", "signatures": [{"sig": "MEUCIQDbYwneKXjMe7TzY+cQiPiTwMAkFr1+/d0xqF4XfRQ1IwIgKsYnhsplT7ye09t1jgVCKVyNPlRTSGHS1L8hwRIIS+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997}}, "0.1.2-rc.5": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "96adbe3fa85a7447403453a927cbaeae3551305a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-qRNoWJ49VFijg3T43n5zw3YFBCS/WidGwjMbR90+ChXY7lMEO3PdE3WSZSioNqnJ2NQaRxrGlA7QSZ/6sVFl1A==", "signatures": [{"sig": "MEYCIQDlp6ligs21a/ugJfW0ivZclUdpStI7odoNIeHLIa3JugIhAJGBZyBbaOabpS8NVExFotEUryN8daJrcV7yRxWN1yop", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997}}, "0.1.2-rc.6": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "2f234449e3796b8d481732ff13d33a321baac9bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-WcwLZNK6Z1siWDPhglgpfr7cBY9gdcpctEmePq6yLm6JMym1x3Bhm93hR8cZs+UAshuVA0q+51WQEcVqsi4LRg==", "signatures": [{"sig": "MEUCIQDIVM0Uc4eLGbIUAuvKqrMBfONhpnme3J72jJwmR8OgogIgf2POD8WDX5PgC2oJ1T0KeFEXjLxx9Iqiq895HkiOghg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997}}, "0.1.2-rc.7": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "3f90bcc16b9bf76bbde85f3d88a602339d1b8e64", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-UpbAt/z/OTh3pMcsEpTP0TekeyubHdbxBd+NqH9omGsfC4OBiGqdV/2wHvotdQV4kLxQ8ivr9+EOPTr0sYJgig==", "signatures": [{"sig": "MEQCIGbFMNGnN+DdeUGWRjbgSRnBoHidNh05C3YNsWKWn+enAiAI081+sF4/fzam1FJp1q1PcwmKwz23K3Jhu6bJr7N8Mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQMdCRA9TVsSAnZWagAAmxAP/077ej7IsBbzMOclo3NH\nGshtlpeVs4pokGcscxyMUfq3/fvHhN3z3zdhzg1CKz8msm3GYaHxS2MHJ/sb\nH1UX/dTHitUNx8Gy6vJoElBfnYiRsmvpcmZw8ZWAsee+cGpnS04FDWN8qYWS\nImrt7Xdfsd/xXihJVwsNNmx1CaR0mKv0RAR8dlIqp/fZnqVZNuQEVYwo2zlP\nkOeE08rTkwwOdajIowZjHnOsrnX043IOBPnXOpwme2K2B35xHcwtCl3CDgQ6\n5sMip7a6bohF/pQW7Sv7Z725mGTpYzWY0flPwqnz2NP1YajQzFiFJzhnKn0a\nFuRAezKtivjLcDMQlZYQAIuEuqrvCNvkMrV5fG3o/PfYNh2gq/CLO0+GhCNo\nWEmE3ipDtMly3kjeNCtfP18sbLEZ9p3RV6TgRKphre16dQfvfj2BSeYMJ1XW\nk/u2Yg5rtxNxVga39dprzHkMNOh6adltdbE6If8oug4xPFwdP7zIceXTEU/a\nhpdVFi2Y9nQPrddNKzqn0uFp8J630kaRZiNG4L6xYab/pbjsHhd43X9tNyQI\nrtUyzomQYQqiEYQgR0YQD4StlUfkrLjhbmOUGS4nrKNaPPYCAJ3UxqlZJduJ\nS9FF/7VJHX7GW0XFDtMQ0Pll60fA6OurduAw74zPyJ1BwZHYXdvvGjiZnwEX\nSuLV\r\n=/+rG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d72d3b9424c39b3c439c3d4f4dd43e2ac3e43367", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-dvUhdO/zOn1sjWUlxfMLKAgz1wARggwUW4pHMC0mJcaiURgc2Xno6MUTuquai1YdPYeS3kkq3NIAs9eWlF8Y0A==", "signatures": [{"sig": "MEUCIQCH45dh3usgJ55JQG8N5a2l9XX7St51Z7vuJ8ij+B7tKgIgLBKWkzeJB+04kBhL/ccoSOVDsyAZ7wnmNf93QYEQFAU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlQknCRA9TVsSAnZWagAAfoEQAI6xpZ9rg6CGJZXFyBsh\nDVFKl4Usd/C7gvRxHVjbars/mJnr7Oe1VTacatcWNEuJb8tQ7jI+D1HpSP7W\nO+SnvcEX+Z+yEqVvymU2l7Gk7xo0RYXO8Cah7YM2CrNXrLQdaVTgAH251/8B\nev2EUSzJAtfRInmI1p1TPdii6+XqmSzGJEMQ3cg2zI3H7rDb/HbK7siOH0NY\nkUpnGhZtekNSnUIjccd4YKvCIrGcNNGSIdUZ9+CL0AiUcg/XvqrrIba0DRJp\nkYEnMnoqZzhMWZ9hcSCOLPAXpiDDPIyXmQ3NVhS8le1bTBcuLWRZp1mNJi8n\nGL4bKKa9Hu6Ty7mFKnNsXJBcxy2lkZEIFue8trWtst8rvCXTE5GUUfvNa0+N\nLKBLzi6Kx3SJDwOAtfaQSWL8dlFdjNhB90MN9RnHAXfKXM7+xMI99uDL2fcu\nscbqIczInI5J8J39Ds7T9d+RJhttNC0IyRsKEGgX4sTRWfSmBDB+bjcg8O+J\nFj1iR+fLnxTE6oJafUqFD0A96kBmggxM4IMNAMaxPlx7/UCfGQeNJLfQzCF7\nLBtnLeBWtor60yD5G5XCG1UDelpaJyPgeJcN3R4WLOMsOTzVBD/mw6VfouiV\nRkmrIazRvnCvBxJipNKWDkMFFeWUhfXD74XKMuoIperRtW3/fH9D50RXM0d4\nI5lF\r\n=+w68\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e15134cf5559a141fc082d64e85efdf12f0d8c1a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-1rSRojjM7qSL2MKNSvo3y7YOSoUaSQsuYdCVGnXygWqZVrYmKxVvdmLHgU9fPHMCrxRMeplXRVP9/tWJUq6/1g==", "signatures": [{"sig": "MEUCIQDDPP0UVC5GsgAE5qttnr9WKeuAgCO5dLWXVDyNQ663TgIgGH4pqtpytGM/wcJeEchBZyXyZQYMrroUttwxN7B5B1c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14997, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhljHeCRA9TVsSAnZWagAALZ8P/0FTO92jfbXvYt44S4wL\nhFTkQ3HeGL1fTMsspVljan0W2AzzFzbaw2V4GEnN2v7zivJ/TrdnZS9FD3Xb\nZm+wuBLTnWxm9MKY5aaaZvwFj9klOebXlgbVD4/4rq7GMxODoOmr06nlJV4y\njRm/KEl1x7fcC6ouplvBuCrw6lNRS/+bP3zNgDIJMjZVfsLstf2iiMM7hGo8\nzs1LmJ1RuGo4VUG3VFWiqsXbXKLbgcSxG4AuJqIQbWD1sYZKbrNWmA1iQtb6\nFthMBSrYCMR5SxEiMSlqKSG353qkqXB2QnTASd8YmYiKUR4YMcqnm41492vg\nzHfZ76lOixjUTl9cjdsUaKKCxXaW55qO1cLEi7uJ2ISazRfz+mXPfGGNV18d\n/eHpYB1Lqel3xTpg9IodCPUoOQ2X6heuWdY3zK0fm4wGS5YE0B9vnOd8HUQO\nptftuO6mFRwcPhVf4a+9aUYRuYIIafdB+zE19UTPai+Vvxbrlt6fDcrrrYm1\nZwqq0Dk/z14FRlVeNKogLLaUL9CurkPj/gMgegWwfeQsnK6MRnqmqT4Wrzii\n8w9ys6yYBuYzxxB9Y/ASN6M7m7PMhEoWt4dVhV1dEDBVTthfVV53XkIe4lBg\nPdhlb2tBuB3ipGwaboBaeVuCKp4SRA9+19gdrQK2YdSajShpJg8dP825lFpD\nVGpv\r\n=0kCj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b22d20f542ad8ef6cb1a378ff516a2b6fc5bfdc1", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-TrqYjhu/uec43tpjUPYqjYuWEym5NWE84vVb/+o2IWE9prY5rvobZPlHjXfCMIbS5YVdXwdi1hY2KofkIMjmpw==", "signatures": [{"sig": "MEUCIHQcKiVkOXHsp86tZHjBruDW6ugJPEelin7Z1bRd4wxNAiEA36Cp9Z6G/pn/6uvyN26IQvR3/75SKRzCACtVW7YLeRE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlmb9CRA9TVsSAnZWagAAZVgP/0BDmdeLWOfrTwYUhRBZ\nFi2IvuQCB/zr3maos3yDPeDK4/uw2flmofbFUxzkJLYMLLC42+MG3BZDvi5f\nvv6u7SXtv0VAqQFCLuwS+koUgbltJZPSuiE/oC1c+5zdlwlJp0Htw81zljmY\nnlQ41o1gHOhhwuv6vBTOjMa+UEQWLn/C7K9QZeGoNdxQ5WKc3aZjA3T4H5A/\n3B5Rr/rbpfLaAIQAJkpPlg2bEHkf/DqDKwboFQAI2x/yyn/XR0Mc2Hg1zlsy\nP8/oCXIpvfItNHi4VhRC4yBEQ0tMIMtZDUBJ86EbshU2vTIq8hkQbpXoc7qW\ngBERh9X4LX+bPnZZiMVesux3N+NG6DmhVHXk4cU1XJlDO0Jqrg3VRr0Z4+oZ\nxyafIiuiRC8ntE8PdzJf5wcB5VpX29OOCay6K5WMNs0gMSymmUDOaUShWgsV\nFbCs3AHa1vldW4iU5Dviq821sV/z/6WekjdmCcZEt+lS9hD6X/Wslj30coVm\nOGp7YpAZFluawSPQR5wn6s89MwtdLJdvRsnes7lPtsS2UlyrFws8ZzvYkuoU\nI8Z8q1uP9Wd5X9DKV4bZIfiKqXanqM7xCnL3lk2PLOf+BFiiSj70tjl564/a\nSGcoksAb9xbSWfSPyKIEiYhQImn5kfwf2bf53QRf7xnj3A3vHnpzcmPR6FyA\n2V5l\r\n=2x1w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "391351f345c84e12faa7e30de66eaee679366ccd", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-XB/ducpko8tD8qQMQ68NHsJ0KKKXmz9OJYivzCXe/J8dW6n6HM8DvNw5DmEC87e57hv5c18bZtGYjRZYsvPccw==", "signatures": [{"sig": "MEQCIHMYyNLwU0nfiL6z/91LlG7aaeuqvZ87kfgZG7x9MRHKAiBJRNdbswkr4QHaUKUtoSUZS9Jjjp+POXDT9Jdf8p4lrQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 14998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5njCRA9TVsSAnZWagAA4igQAIKKrgjrsSYpzVsy2dGR\nNnA01xtCMG2cY2Dn1QCVDlDqKNJ9lHuFlTWzxZbM/xH1iYM5ddeGPSgXoTGn\npQ3QsTjudSqa19kNWxuCQ/sKbpdwFFNattfGDagRFSJvra77Q8YUYkOLklQF\n62En1SDWKISH2ygqRFTbqtfuu6fS6WPu3k3Qlo0ZXfaximNWtkdsMwLNk6W1\nbQ95tg2OGLGXquUgCtLxDkuRIA1Kwdjh1x2L+DWFEdrlzyoNBmdF3DdqRV0N\nR9qVPROTqXCPUiam+8X7swOhq2qvABEdNNQbRGWtmFunatfuJJsMri0GoVrp\n/EdRoZCA5vl+aoeOjdUOihggZytK6VzfORzojBTbI7kk/RquEEVgREBBSVTg\nQvg4MO1vf5qWm14LZw3VIwn3/R/aN3OlLUUNBzf6dRCeqtYR//Q3FqPiLmEB\nL41kZgADSJ8JEJbKcpt6Ynq8TOslRlnWIV9FYOs6TEGmV/cvtcxDMEd5foOZ\nO0Jpci8t+ESmHk9ifyK3aBEeo3WFrmLBv4RlJakIwbBs9HhpRy1vYA/VInwt\neoM2Eo+nrysnsTNc6n5Ddu/LDc0Tt5tTWC8aI21kdF1kENQybJHi19MMRIx9\nseFmCnZojzYYdaHydKFAdisJIptvfkuPBRdBCjiKbF7+CvLH5cCbm9N1Z4Dh\nEuqj\r\n=37GR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "6e694db0617e4d55922183bfd71104b8866433fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-Gnv0gT8VjZ4A24GuqrVYhy+/swhC65RJDvuO38t6RKgz99OTuIHvJ2YfE+J3CmVSCCg0NMYzbbPM/htfhYeqAg==", "signatures": [{"sig": "MEMCIDQtdLNQCe01rllw+hJhvlwYiUV04pUA5Bk00Y8xMI7TAh8c8NPNvH6TihxFDGcNfxJU7WkuZdanvuybQ3HOTlL+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN5GCRA9TVsSAnZWagAAjlwP/1zoG0fyuVcRK/WCDKfm\nnu0nqa8V66ILwIDYuHP5n+uQUKO5+HcN7LLYoEULIPYElbGVwVY+4uSPt+ND\nXmbo67uPiNneB/NMPBP8Ixj7lNFIi7avJIrNcqZ+n19i3WDywgxCi+uEYKvk\nmr1JBCGGfaMriafT8uK8i3Cv+zMzrH1hEFoWN28x7BSla050DRnC0FIJLW1c\nAJh3vsIkXk8DG0T9YmJkms2l9PWbgKB+2zp9IwVy809MOMtUS/NW7sS0J2vt\ne5lH5MjvQ6G91MJGVm1eusHXVjSBj8IsVhobz5yHFkxRy+6St/Qite3J4bOO\nLgZHF/VCoI2SRzslUvcvoZp2O08yhv0CvlY/EAS9vYgx9oeiTJDSeUJQMKpY\nfT0PCOAiKc+kyLi7N+HDMcqGSNwxbTYLGpjtIqJKhtS/D4EGyGUr8MsiAae+\ntUxb16DAq+TUPNJfmFWvhLdv/fmCsY+GZEZXp1mXtU9unFo5D95d5WqAsykQ\nC7X4C9cFN8D3SVUG+g17d0fmmfo0R6ewvlNZ0kXPn7n7j94GJG37dLHpXJX7\nmlYSGu4EmIaZPvAvmYlJQkPc1OUgNDMKsPRUBtlgftcVvqDL9232FvLP5353\nGZNbodlz8I40xsuKs2WvWmJgyzeuljpetDuhz98F0FKaWFmLSOQ8CIiQoC/m\nZ/1H\r\n=/FX5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "3a4d35cca31dacc709e2e624dbdcde714ed06f42", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-eliJjq2UZ0kwaX3K7kyKheM9k7RIFfjtG97aB031Go2eAQaDC7TNDcwdSmjmy4khLhGfbq4B1NbFeV8RfHod7Q==", "signatures": [{"sig": "MEQCIGLocp5Lrxu+29bvBPXmWlU8fPApRnAdKBUFSpy6Y8dkAiAz7F7LQ0zQLeVv/mhkSj8B025anQmkZjAo2tvk5DczOA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15055, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoUKCRA9TVsSAnZWagAAeXQP/2hLGMkoy6OSSO4JBh+g\nLZLJJrXqzq8a7nOpytWh8EMdyTShdSCdFyw7+onfPVOHI48pAEbYnqQnoOvk\nnz7ZM3mTICCgkX8SZYO7QxlLkSi4zrs1TJEk5tAmFYZWBRmQBTtNCYGlFK30\nP7n45+GdlOnDe8ienMYWXTYiS9OlZ39TZGlEVW+iA6HPoPTk14ZR6yWAO8ie\nb/z+HvH3flGouUblQPjIIZ/nHnabRxEw3Cu1HWtBuVa9ISkeJ5ubNOFR2qSt\nRM8Rf8KAyWvSAs4jwajCmUq4gVTOwnMIeUvV7vnHf+nns5nS8d9wOWt2t+5s\nzMCk/Oh7QJUqbJSg0NurzTrRZKiRm3VNfWJE9zUn157XDBZx0JkkIfad6Dn0\nqSN1BhCAZR7Af9M6nabzSEG9Vwv7iuZgEggBFCcTGqX8MwEUXC7VfSalCY6x\neOlF+SjCe2z8avmcXas3ba+qyTktHNFkoiQ5aPXBRX0l17ZhAm/QuV38Rlz7\nQehMgOt22CU01tqs+06wZ3zaQZs38nNc81NcaGMtrE0oMF1CygHnynpTlBnv\nnEntPOiXjOIvo2OVGWwNq/X3fXv5qhcX3bZl5mHH//06YEuyuo7iisJFT/HQ\nokXVcmwW2bpYEXBTRwH56j5BKTWhYZ4mzcg7SEPd8BmWxoZOXz1Sxf37DZSc\nzZq2\r\n=Zoam\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "83a54fc593527d1cfecf7207d7e4b5b1f7cc7094", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-2BdMHbrf9zZ+H4oXXNQ4wp6OFEgsust3rvhLdOloicanJ5t1FoLYH7Ef2jW6fF3CA4nj/w7BTj5f0+gHgLeL8g==", "signatures": [{"sig": "MEUCICZxECjQEANjSEyZRgsies2khPIt/qBIIANJIcQsJqvtAiEA2ZtEdMQ3aXMQl9K7cKfUcxGAYv6bFCJbr76XQjp6X3I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiAdCRA9TVsSAnZWagAAXo4P/j+1X4NK+3RotDNt27D0\nV9a49dG6hY6FKIoPhsnVElU3mwelLzXHzGoO/0jx+8L/b7P8z95NYmqMHSL7\ndE6Mul/S71J61rgh182zk3kTS32kH6SYOiAJHxmojy4vFl3EgJyHEL31LEBo\nJNgSLm3fgcoc7TLOdrKkCf6710CcrdV76TUMZwLIAD5/pcEema0zzsTagK5A\nqPWXOZBtlg+1iVimaOydaKkk3Tfaj7R765z8xwq8O5M0vAxgUoTEWSI1FzQ6\n0fIB5MAa7dQqheTor/LZgXblMNJi5hsgPCuVgNQjHrw6pHves5wY/xCrU51J\nxyNh9tGMnjVYJFlxnNS8Rwymf2ifjj+fPhF5h+Kp7FKOu26jLe/ap52DmueW\n2kx2hdWZneYBhIJAN5yggnCmWSglr00ViJzskC8+kkl5nfxihzFieGexSRLR\nhucjO03MSApK8pGOAQu2x82uaybvyIYpfpUNMuwrwnZ3AV5WwMHtdH2CDyve\ngYb9901iwNdU5h7Q9yKez9e9DuzL5ntttRhPq+ksP+uHlCkx+G7y2sjQoFGl\nAlfWGYlfYP3KyHayziPBzdnyHtTgofiUp9QIJ3UqGf8X6v1UK4M0qfFhLAmm\n+SkzvNEJgC8H51+EqZK9sGraLHVrelv43ffph4eDA7mkPv0vZ4CEaoghEMP+\n6rKX\r\n=qWVA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "f46f8e81a64393ab2755d6a7f604cb7682d58fbd", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-+ZdcMNtstJVDcJFaK3Aov48HRYe0rDmico5R07tBdd1UNSXT+rkwXsXzZyG7Ul3nbtHVc22IbOJ3laiMKjr/bQ==", "signatures": [{"sig": "MEUCIQCrJS1lBBFtofoCsogSn/neoZUADPK/8J6bSi3fyHs74AIgNPI06RzfdRtfNt5roJmSamN1XwBRveAVsuulQSqYD2U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15060, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiOLCRA9TVsSAnZWagAAPucQAJlTT5WEYGPSbeOytNoN\nGS/MBdVDtzbYQ+vk269W/QY+2dJ1J+l8J8tCrzQGuyamdPb8CufeiDU9aghn\n+nX/tHU5xO5yZLfTHlwojNcPx9T2VMaui7jiGdf2hQGVczmSMuwiJzs2YarK\n3lvCfh9RRFnuhu6hbtC+WrhJCYgIE+Mib5G+IjUpC9Y+/RV/zp1uS8fSGlok\nYNvGyKhHxVelSTcNgOZLBPXp8VN0CcMN5kuqITqXpSLRIeugiKSwDAkHjytQ\nru+JkIH/rAyNW9cxBY16aiqBH9BDboM3y/dNCmzNvM6GcY8woRuwTefxF7UA\n7q/InuZ1XlW4hghEJpCSqy+wVc578CrQz8mExPih6ZFhbMclMA7Co12EKN3T\n5a4wYHEKYAJsRL3k19zDEvsyp90tfFkwkeFiEsHT0iv29RpG3FRJgXgU2nmd\nDuAc+QKpHAAd8ioikSej0JdNfhNJLCgnpAiP1P+JV6rpcC2ZaqTOxoAx1A/9\nyvzBBFdDD2xTRlBSXajjytwk2pUQAwt8thOPpJgVOWT9s7C5AT+98SQHuEOX\nrncaQ62MQf+D0ARb+5qniLd5PELtU7mTXMMhNjMYBDbWeceIiKJX95iAh7hw\noFgoGf1b/yrQCFUKZX/3HBLbBNSaJP4NsHycdinCpq03oOoSGEPyCrj/7XyH\nGmCk\r\n=d9qC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.16": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ca8fdb1ce8fc9b581bf5ab77c02c876f4348eb71", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.16.tgz", "fileCount": 8, "integrity": "sha512-YEdWkM4+9fRePWz1gAyltGe6Hhkx+yIIY7Hzno+3wwFI6ca0h8JAfGkFEOBMobduplZ6VsfF/D8iakpzGq481Q==", "signatures": [{"sig": "MEYCIQC5rNNdAU1eyphxMNwJTJVtOUiN9u5x0aHwChuOAUXTqgIhALK1eDh9YXh4arOdDJNA8MYPHu1xdS/CVm9uAAX+tuKA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryjOCRA9TVsSAnZWagAAom4P/Agem8o/AmnJmdU2EFBd\nZhB2sa77Q8qpXCkPy4Y0QUsNhGFhJByYGNwnRqrF22h1IkAaJ4rZxGrYERj0\n8bo18Q9dIZqUFVjHY0OjRyK4AhCxp1AzBu+1CY1gLqw1igCDZAP1JcLBccVL\n4OO925zhZ6J9wutMlc46idDWjZkas+w1G7TLob78ASelK5w4rLOr7NbOdTPX\nJ2/jJ2RlO65WgNqpOiI2H/TQovHOqsiVjZswzd+4nRkyu/Okm78rC67pSwYw\nbPKKdxsu1aV4P6xspAbAseIq3OegdPJ623JOWHN0x3H9FSdT9JDhAJxn/SZD\n4zu6M/HUJtpUtIAnvUgzhjQvEXTB/PQFXW74/3mVZt8y9AvrCBzY2W/atY5e\npi3YTSwWsdFu3MIMjbN922o3KFk2+a+5LcZlxU/m8UCK0Xus9+gzuR0ZkypY\nP5cUGfVoBGDG39G9dml3YrovvXKtcY/iNpn2hUB5n0kMxvIcWDWIP3zo4yPh\nZsld4UpSQ1XoDMUBppthAsiUPDRdnh/M8nqogDG0KfX8wacqy2KMCWQUsoNE\nbKKVn6XWweWc4s7HtSXGOuORvPdRjkwZHCoVY+vPeS4KKPRbXJw4NprW1She\n5gmzqvVe3uN0Rzv48xa+7M0e7PMCSAzmHWDJaoxWrZU3819Od6EFkH5NAWuo\nT7NM\r\n=Undi\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.17": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "3e5dc962d43ef3b205ebb52be576636739d08bff", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.17.tgz", "fileCount": 8, "integrity": "sha512-Hmr2F2qoLUOQ6dMk3UxNPXlVr8pA68MOlHDgCqVKVfZ1GF3vTo9sl2uFeqYvD07Gj6LyJBqgCGUao4Rz/26xFw==", "signatures": [{"sig": "MEYCIQCTcPDnYDfjPh1I6je+1df3wnrvIZuriOiwMx0repQ2xQIhAM6RB/fKKptiY8k1FmQlmgwudLxR4T2+gBIwXbdOo1ug", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzRWCRA9TVsSAnZWagAAyigP/jofnVgL4aYkarh7OCk9\nQkoqxY6YjpigKm6zMShLgj9s21u0Otm2UukaqwN/ibIj40akvyF03O1yNxG0\nF9v2YJv188P4lkXHJWlOne8xHDUn70PSG4Cc8lOMgtbGJVNUqemQpl2LUks+\nAchu/SJMXNikl0FVuxPume9muzIk5DjS1sVePlOMuch4+LQNcj/0PoMV2yps\nfpguEi9k8Max2Ly8vQDcl/yC3BDOPw9hAJqzHdWkh/ucLSlfip8PmPmIy8ij\nm62ODfBwlMIcxtEFRIVkGWj1BDuhk+0NR2aG9jLhwp8bEFYCO2VEyazoaTgJ\nOjglHsHdtQiS63AfS+Non9yMhuAESP7jv7uyFlmWbvgv6dO2Go2sC3PuKTR4\nm58/7CCNjYSvWiKsqz9YTLO1I08erJ5x9xj7ognup0zvhu7pIw5Y5KkPcb1e\nbCThZua9umUmvx7olud8026z0hB4WPWHwCParcq/sJBVllSmxxmshgvg6JV+\n3Rfcif3MEh2ypNy2BXp05FgFAoMQC7tRLoes5Dg/Q0eJB0DLQ72/1fk4XCOt\npks++koUdM8L/L+8HOl7LMPLAj5qTGFJ9a4vOmQequsecxi1tFPMvDfq5rF0\nFUZZ4QxjqNdlywDox2EuVyIAfs5IPct9ZPfgqoJoGq3qLg4JCgIjWTG44uWF\npepi\r\n=A5+z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.18": {"name": "@radix-ui/react-portal", "version": "0.1.2-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "341ad8157e14ae96ba86ffb247323f593ba59f12", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2-rc.18.tgz", "fileCount": 8, "integrity": "sha512-1D5v2ZgWRAeCdKLCA2g5aN7n51d5kg3U0dEMSurf43zzLwcYFYqpm/x9g7QaE++4PZphQbCXE2VVfI2qQ9TXvA==", "signatures": [{"sig": "MEYCIQC0Q/0XBSxLT0ZcE1P8rkUeMuKVmQq/d25Jcqox7ObbRgIhAKx6kvdQNoRQBKl7aymAf7m9ZRz08QwWmkmlmn0c34Pd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15672, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr42pCRA9TVsSAnZWagAAGM4P/i0IoXXgSjZ/OeuQqAOj\nfeOcQ474t4ouv+pGz/QJZh4heBmg7KttkzClQCe+Q0Je2nnFdLVVvbKjV5rV\nGEhGbk6REb4YeRgh4Z1VZHhlBiEzcw+Q3qN8LW3cQTaH9Gsb0yIzOEAupEsd\n0Ol3zuHYnGzGfVo8LtoVaiZ2wbP+UoXYvcN4vIyYbyLvgag1Um4GxP5510X4\nzuNoocM8JtPUEXxx1Uh8IuQVSmrLOs8uRS5T9BR7iW5Nx7WugRSl1TgiWH+p\nARBfaT1Os2iFWTv6qhtYwf8EqV1y6VDOlOFQAOlQ2LCjUx12upYVH3EVfoHO\nY+Eab/nZHm0kgLx9OZXZA3SZ4/4Dhy7+MJNMkFmLB94NFgS25E9Cq2m9KaPk\nyMsK7JJz9TWo6kBrGRRyMvxhxgTo8Ru7xfB/Z8ktI3/TU/9xBOAkEnHKS+L8\n5ACUvePnhhYMka2C3JWY3pgPyFyXiXGUdzcMHxBwWW6Z+ib7Bqr+cdhhmexV\n9nXHDUBAtC+6+LYTnfpC5m3WZVJExPBsY7fIToV5mbE1ox/AfJKfKwD4P5Zl\nG0U9FWQlzIr2YkVbECn99Jp5u//2m0bo0xNUVrx6xd/NV+ZrMvQkbWTsqWAT\nc5ug6CxzL5Y5RDha5epgMKsNuVz92FAIrEppdvjSrH5uzIH4Hp0H7hhW3wds\nMiSI\r\n=5chI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-portal", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "a47059fe04ead0749d879818a7cbe9e7c751c4d5", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-rLSe5aeJ7yWD6CuUyg+U9wCoMLleRyxQS67eqALzLW7zk0glB5q5x2ihAEjocZH2Tng9v5QkYaLyh2+sO3TMRA==", "signatures": [{"sig": "MEYCIQC/Q1Hg+WsqtbvmTfcAjCY0FfNSj0Ad2xR7xIeMYB3BbgIhAJ6o4DdLODX02h7zAP4/YvPiUimVQzN45nE3/LKN4MLm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDyCRA9TVsSAnZWagAAQdQP/0RH9YQfKyeziEYbl3t4\nslojgciNRz23JEVovIZ+vqrWSfl1X4sJ36NmQUV8Wz2jof62i70UEGswJYvC\nRkDRlgHWwoUj5VLynGDABVA6csLdl2hLvVo/MIVLRMVJU4Uzx0b99mIwj2vv\nypjn+JVfmUIcYapYLbWbm5iuSsd07P0ttfQvZOcWWOYz6PXYLuWkK/1wHcS9\nNFQhmIJROP50BKtG9IKpLXI6Sty+ZW5SYCDiuqatdUEy6DZ7GFZlpvMZTQKl\nOhbK8maurkzclitXqqRZxTXOgZFaaOlqeiJgvC6D3Sl0hhbqYaxqDQpcMpG2\nDj1zIwKx7RSdcdYX+mn4FKnKNurNANO5NG0wG6wK9LiGnyIWObPhPC4JqPkQ\negJcEttbDRA8fe3AkgvE9e+uJPyNxWvxW1ZHtYQgf/RczkV2c+Pb+psc/U4D\n8Ifqycx5ZwOPZ0V3IpXRRsZu0EZMy046XbhHi26Ee+3k1FffPpZPVU2WdvUw\nDeT74jRHDxg+yvMHdS5Y2U0XwBgKv31HZjwOw+afrwSvycQu6gsWkW2w1MUv\nMybArBWMAD+GkzHo8OPKH3ibv5cg+RSphYpcOYw0By2L4M2Rcmt+qpjrBpmh\nYNL1rRxEeqrSVx5DtmMFNf4aOi9suuMtYLmhEbU4WN86F5iWwOI1Sth8c4ko\nz/q8\r\n=xRKd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-portal", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "56826e789b3d4e37983f6d23666e3f1b1b9ee358", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON>+sPYLs0HhmX5/b7yRT6nLM9Nl6FtQe2KUG+46kiCOKQ+0XzNMO5hmeQtyq0mRf/qlC02rFu6OMsWpIqVsJg==", "signatures": [{"sig": "MEUCIAnJ90JV9SoOdFcXGW178zzjDprIp2oGn+57rBXsLnjGAiEAnoYvF5FqvCai1FelIJoScN8hp5JPkhAotnj26k+xHKE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLilCRA9TVsSAnZWagAALJkP/3sPNbR+VCumHygOjs/N\nxQNLX6uswHzB3EwgeHV1rPf/td8srtbn52RnKZi2a8Y05GgkpEByR++Exlnr\nRntkaw6ejhda7qa9EFCFNDnhVHxAMO4qSLJ5OHc84U1Wc+ZZ5OudMduEtYCm\nRIBOJ99q2VnU+rQ/PgaTFZFWlDKHws2TuuSyyngrK4fd5UUMCpOVmvadMTG6\nmOrXSlCJOIc75ZEkv4gWItCUr6q2X/8UpiS8/Ugq48LCH7blUbsRKZ8EL0l1\nKLYfMv3hrbfoo25lSm849OExMqsLaBH3n8S72+P0qw2ZVsCsxjAeBrZdxVXt\ntDpUdI8Buoavy9Cb4QD7lPbHpKP9xlaKA5QtmXW8uwB7SExpymZD7YBW7+tr\ny3gwJNg8b1dfbMWy/hT/q88veOE8D0rLnj+B1hvoD2NwLS/TsSdk6cK1LpUl\nMsI6qoPy3cKBoMS/7cPNKsjfFadItHf1/UVo1bhKsCNvlE49rNDfSQsyDwnu\nMUz1+LNLceRJuBQ/SXqMJWjInN5iSQrtyfqk4RS54SQ3fWQfP9+REfblFS57\n6ShrYV29X35gJZVqjwwRbuRuZknZ00rR3UNygPP/bOxG/oTNWBhDltxEZR0b\nNmlkrZ8dnOoDQ5fVullz/XstIC9LsRDUh+Gwg4mpzC2dZCPDBuV/3563tLx8\n5Fq+\r\n=xQKF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-portal", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "cd766cdb49e8da7f330fac3b99408264457cfa59", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-LUMOY6O2/2Fh9VVFtarmZXzdhTZkBVYL2l+qZYlndc+/by3ar+/RrLqaY8bjV6uTBhdyvPvsK6kGYXmCRPAinA==", "signatures": [{"sig": "MEQCIFHrkU1BUotc/ifkdo8EN6OnD0gTP3Jw9Z0CFNEZbnX/AiALCMO2+UR7zAqfqljLqM9mAOZF0zY9h45JdwD6a+5+Qg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLjgCRA9TVsSAnZWagAAPUUP/3NN7mczj2S27CUuVxSY\n8js3PZe0nvFM8Qi8vgu7HiKkShDVUF3q8iVY3Ujrn8ZF6wLX/2fTtQVjHI86\n5LvlLmtu/sekjy1iiMiY2U2K+bu0uuOvVKlIwoQzMB4xRA/xLV88nJYhaCKg\nGcDBK+lE75mzXZ/KWPlu9RF+b6AACgim2WMyo0BU2XYIqqctSz/hL5axgR6n\nPfEQuOFhHCO6Fv+SOvpJP1plhRpyM85xmai7XqnwaPxAdiDtLmBot0vR71sr\nOjNWky8p9EQLxYGHmnO6bUzW+b8NCb1goiu01jGAq0MQizXCblJHhnNuylsV\nB046R6K1yBkqgDYyGJQ7xChMAYNPA3W4RZOYGGBROb+Jvnn8fB1etnU9zNfl\ng9KC9U5EYgckGQDPzXZ26EtLg6vchBDhBp9UC7vbJQpekzDg0JYrINaa5Gt0\njJmKL2d5mpvsBh2lv4LPyDYlFvKoojHV90mm58F6e9kA7ik563Y7kbB9UucP\na5nywv0NTmp0Unp9/01I8YW55rkz+c0D2WZODtmQjwkke8XXvJF2m04VP1tB\nd1/qHTDWmFv7iYZ0f29Qz4kLmyQv2wafOkx2Muji6xe66E5PzsG7DUFyvRJJ\nx0t1nOdnrLMzdgWceCCu4ona1iAO6V5wa5N0hWpqQ5JMLaItAc2Mn9VxA9Kj\n/mjA\r\n=LmsZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "39c73033f41a610670a194ba9501682aa55d2113", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-vBYyvitDXkb/QAERRZ24nxjXh+7zfF993YvfvvKN4jzFi7d0Eg6x81Yg2MB/lqLKbDpWLItm1ql7HhmMiL4Gqg==", "signatures": [{"sig": "MEYCIQDLxbMiY/7SWYTD8Jw7mlTtUIRNR+42bWY1DuHT5ngpewIhAIp6XL/SxaqDLmIsmp6DEdNF23wb4bCGU7xl4c8Js2Fd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31rFCRA9TVsSAnZWagAAMhYP/20stMvEEAHHBbWeq0kl\ne0Ez/Pf8MJlkAmm7bxPrpZ5c+tad3/cWJ84rVAeYWrPeqLnB03CaE9KsILhE\nGAqiO1WcaXyxuJEkJjELJAsizNJi4aDXXCqsnTZywaFWVmj47nIlUiom1aND\nDgsqGjgPx+gOt/cH7QJYnQgMtnY0hITEdIxrF0mjpZAbTMOCyX4tuVp6VONH\ncAtmR5MD4kSC5BktguGhUw+gLdTRDxpFQtgR2N8vN4ZIXLgQkhsPWzYk6TgH\nt2UMBnLbiw3UGHRtUIhgFbVXvSsNdJxYf2iFjbwarJl+4Qtf94vaP+mlRUAX\nxveVmFqHx1k+vmD9K1dRjIEcp+IHE9+G1lUVEREyoFMhGNmUPrT7tCQ60YST\nwZyACtYvC2WxbzC49AjxcdCuYq92JYPC7o6GJ85FySOYMbQJLqD0ekqQZJKg\n8yWwUqM7l80aU/+dAqluF3gIF9b0ClZug2TPsYPfOJ8W8jHc7YwEDKHASARC\nvcdG9uDaz7wHctKtd1bgcwRguGwmffo7HIu01tO1gHVqbqGveCN8qXeHLaxw\nJ1dtwYEi6EFa4wbSMtpVreCU+rNiC45ScKfyFjtb5CHIbGhJX6mfNWOTO8vW\nR3XUriy3JKT77yvpkHgl582stSJweAdf2zbzQnxEFq0GGm75rF7A7Q09BWJ4\nfL0F\r\n=0kXe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "23751c54fbf6557a309c4fc57708d7e71ed103fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-bW1wt2239w+hp4ftK8RA0/p6IRMxY6EJGlbW1yFGvMmZ+3cwsLqf+XQFqGUq+9uAyYIw6hUvJxXktl4zBuOFhA==", "signatures": [{"sig": "MEYCIQCsN7GTG1bQRo1wCIh8mXzNJhPesinBefCoTr2kXHKzPAIhAO+1ZwHo0MZ/Wd5yoAfcARQTQdy+BKopukTQGpYz2aVS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BD9CRA9TVsSAnZWagAAwREP/20lOpQLhRtLb/dp9ZLv\n2uHOWuDHE7Zmv7MxFKbG12bXf3nHQYeuujueRYVWMW2PYowzve0bgk+lxLgS\nd12UNKF4spGDvhQvaGIA5wf0Lg4mfUljFC3CW7TN5aRqwW6J18E4/TzH03oP\ngRmcF9t0GC5gvKeaD0uURMN+hpwem5jsqXjKWor0cjggYGWCzz8YpMR/XICT\n1pxE01L6JhSK/kVpI6q5BmE8robkYPCkccaQgav7EQK5sjcmeTESzgw/LniI\ntXo5RuGFeqcWW3xg1dUiRf2eNmny1GIL/Zp0MSynrarPko/PtHRMW59XNoHt\nj6Qnn7iFZiO1PLUqLFO59f8nZV8i9LW+CrWYJWc+edBlCC97u+ANQEjxlct9\nynMmA2J51Be3/7NgPl1Cta/7hoqYULV8eoqc9dafi7VtWDa8cbhSPlSt5gM8\n6mrxDKQYNfU8Uzk9NpvS3UlNmMiRc0zXNTiPeB+8GVaopQsFkTqo3Caf6d/8\n1loBgk2s7FGPPmkb1Kk21Hxa4rKhdy//yI9Gqe1RWAn4KTGBhPxe88l5hJv7\ndqUUJo9CqqKz3P3ogGrIgK+bOFnsRPS6rYCO3Q91b+GQt2S1zGOyrTQ/zuWf\nUutY12BlNzndnr/b2FDYgSaPxz4GmiY3sVoh6N/sgODJBV6fPhBCKipPgDw4\n+p2v\r\n=aKMy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "4d77d360c91cd7bf6023fce981fb8dfb489bc982", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-J1qpJvqkvUPfT6BBd3pyrjNEfAkMyYgQdbnSe7MYhbwzsSB4vc4poSWHDUoA3jE6vwuR+OkTMZDZVzsOVPWI1w==", "signatures": [{"sig": "MEUCIQCOXjsmdYHqiVo0GgUdkYRYyF8uG4esmnlykgRNEJniowIgT3mgp9R9rARC9DdhdgZQp4h+O/rUvHDQHUfnwmdySh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CmhCRA9TVsSAnZWagAAMTEP/jLJt/sD5uQCDwPr2pal\n5JGkppMxznFrkkOSRnt8z89ZDHxls5Sg7BB7Eq/iGjNpTmqqbY2RNW5GWcCG\nDePH16haJqG68xIBEV1NYbCbbWSAMu/gip2xZRQzVHXCpgrwxrHzS+w5nOdh\nhFkwn+4MD7GrJ9S+W5/iIdORxYon15uvZJ4/LbtnU/3idwDVxgZlqZooqeTG\nKcrGGoQo/qpb5R9XID8eer4RsA8BRZLkDM8EC8Huv7kvGmyv+16SSGmwqhiV\nVpkWPG+0P6hnBeknQUclZvTCYWZSGtaJh7PS5dgWNgGrx24VJGeNe7KU4JPp\nj+Niug0AS17pIaau6TmdPwbC3kGItMqvw5K+4rkZHsWFEsGnQrqwVX4as6fO\nycNYCear+hIoLmTB2nfsTie86nk8/26BVxzFaYKCOyj7lL7rKE8oTaLVv1Lt\nNK8l73dEKhqus4IctuZ1DqC6lQ24iCYmp+jeeZdbgy0nbjaq8b1/gMtNjlHl\njwLke8bTePnv8VQNYUWDkWc0uaCrTux+aaJ8l+yYC8iJP5Iiqd6L3JTEJKo2\nYF0k1OWUN3b2IQMP45dMCxPLXgOkTeVQvhFC+zuTxJuX9QtvBj2YMULSGz1A\nHn9Svxs5dn7QI/+NgwkBnoH1/Tsz5gVVbg/ZXNEsWNb1IvaDCYuZITcqK2Sn\nable\r\n=dXMg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d0d0488b6a4f6ac9b62a4e15897b326801e57f55", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-gMkZZ6HbgAg/3XV9dmhXxPE/4RvbuSvAqDothzvBtMQ/klG4SoRn485osrpPvwOced5Lr6ab+QzYrtCIk58kZg==", "signatures": [{"sig": "MEUCIEvbM9x2fh4BiLu6gedXwjDAWbkVqAFzubrMUEx5c2iQAiEArnUadb5puCtA88c22tS+ipRXqnpjyX7ha38YlJZUGBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GqYCRA9TVsSAnZWagAAwtEQAI50yDPW9Sq86DpOPV3b\nLvEPc3K2QQ5ME45D4lKIliKOMWRivo4WWp2bOB6KuyRfTTZJLtcuIwL86SbA\nN6/N5Tq2Mq++JHwHL3evdVm8v2LKWpkc8g63kNFCq/tySJsAcRs62tfbm126\n3jrviWoEh3susipffRDV5sKS7XDwNnyE2QuF9OY6LDmM+OismsU57wEE038l\nWU9m+tR0YlywDlj6WzLTWdv9qNGqtYieq0tmopGvDlfTj4hckT7iiDAx/bvk\naBH9vz/veDQVZIxBfOex0lpnf+7cdfe5hTdwOTUjlafzIFAw7342IEOZjn3G\nFhK0Y9pzahzMTU/8zzI3OfDtag9yVDnHfQO85tqD/tqhhuZhmZ+wH6BWzIIG\nOhjohL+JAG0gs7rb26jp0lS/6Sisc5vHr8z8EAY3ZfphuLrMVCMH7QyZv4SO\nXvto6+uQ7QlLLfiYfbYvNkRR0/MpKnyheuNaqR/uhbrLVcIlKp+sCbeBdba2\ngiDDXCszzKUecCd7onj7mKpVOnoCruD9Tvux1JATa3uGP4mknbMFXfpLyS9e\nfsqeEaqEzqEJkS9JvU14AMuh5V7ubETJtU3wPX3YCD0+2dYSAVVV7Em+8/gD\nwUB3VDZ1aDIRxJVmwDyHajOi3MA2uFrCQJnc0X8CRR69J8qBZRvfptJKT+1q\nVgv1\r\n=nDXo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e6fb9718bf2a1b79bdf65d67d7be1c3292d84f93", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ynzoVlVhiHmWkJ8PeAdAq1L1eT9NSY1PL2/OjxuVrubOSPA91RYkCx3zXduIqviRSOjhrw0lP8Q3tHkRkvxKyw==", "signatures": [{"sig": "MEYCIQCMKyu6DIEYvI1F/vvJ72MpZ7NPvjmy3ekcLP3KeKSADAIhAMJoCJ6BVcqVRuDykzXLn0wwLPtc9g07ntWwR6hKGCNS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZcVCRA9TVsSAnZWagAA6+sP/jgSMuiDZliBtqt5ovUw\n3RgoErtwkoket2HTo0OberltqvyUEjYF3EeBk69GEbtlRmQxf+7P+zMoBZng\nSpvrBL4DqjgwfiPByWgDBMVLoI6TrtQ7E/eolqt+aYDAosq/hzDUExjCZ4Jg\nxRKYI+EPOw5T7bfy3DgI+Jo38sIvmT2aOhbhD3laAsSlG2xM01fntYYTs98K\nsJcMfkngJOz5ZZDVKOtgelVlHLP529wlbi3ZYAr5uu7QbAfZ3bVPbAkgdOkk\nA+nuyZUJVDNLfEu7klXh72eeOCNpZeu4h57+VBDCvmoSOLjTp8b1ovXJrTbL\nFtDcyIy6SSWCV2wNLpmnZEHimP8IZAfWwcADWko5bx9EgS074RSOOcHBnq5s\ntNz3DoDNZlTLxIcgQACuyESd24PuwetLrZt/HG2dsZQpy06Fg7siMdwxnOJB\norBo9Zo+zdMwPh7zuBj2n7OU9dqd4GUrNBBDd9wPw/oQiRP2VRihmouK+lO3\nQOUqgGzrq+GBMx+mq+ACfPPI3O+LAMjuCZgpuXToNdx0khhBoqdMlf3JONM2\nERbIxqA1CaRaQqsZsIdEtuSCgfrREnLv93lJAWupS2xEWv4Y07M7YZ/x8FTN\nAjbCNDxQW8PXZNcTrEZEAcTUrWihRu3PY7c7tWX+O70tptkaD9Ry99u3pXq8\no6Bs\r\n=Cq7Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "e199712327db0d91972d1b9041b001bc747d69ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-f2FAeCn4E+iO+7ftb1YmfleRnju8HzGcKWKxqY1YWhnCJKfY5a3JfRQ8/XbbBNWBOi2SdLxfJBot/4FYvGzoEQ==", "signatures": [{"sig": "MEUCIGIZZehPphOcgeg23fdiXjwZBIuod102BbHa9jlBeoquAiEAz5kU81zDicvq9ZYWHFiy6KWTKowHFoY3j9y9ehVllbU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YszCRA9TVsSAnZWagAAdWkP/1g51XkLfsW6/lOQvm6+\nNBcdhs5pKbE9mUHlGf//4LEWT1DJAN0Fey5doY2efxfpT96x6eoYriM2dLA+\navywcKMQRspQASWSWReORaiaZEjudBB9xWlB4r/eJ5Z8IU4hr8zEGq+LmCwh\nGbqKhAulCuc97is57D7jXhcmBEEIFTh64fNTLeQJMKj5N7aJP4Fb2n21gEhc\npoJKIh/ugjR0+6t0VQgCZwWOgjXtQKdOPQyI/1rxi5gKA3YajlAlLGlTnbK/\nwbJrIUL4fvBxVbY1FLPVyqcvPZZfeZYDa4NSyjOL8qC87e40Mjb1kpbAC483\npnrodDnlVsyFkscnzjJwmw7OGAdYlW7F4v6d8A9xuMi4kBtJEDBSGcCoyoBI\n/K/cEMcA3pw6fncl077m93f0/9lzEwZ3fbdpCvPP38QHYMZmzM9ffeXVDnUz\nugoyCT7Ctqji0naHKdDr8L9FtbnABtXa/hXWzixATmS3hxTdKv35HNMRmEFY\naSq0s8HxyF43chOnypEexJcebnIfbMeQiifrRD+fGRiYosHJRFw+QZmNxsee\nyO7/UWD5TqJOURvODT1P03tQYAYdfeXGGKdWUSiC2wHZJpruR9VoW/qh0ykk\nlqtg8RDYxzivRF+cFmO5pmO0RCPmctEWK90MyXUjlRcs1R+pPUlCWxvFiHhH\nW3zh\r\n=6ZYI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "65a6ebfc567b77beeb831f55bfd0dfbb1af37bb0", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-WJRwdPehB3FQ+i5ZjHcZ59/BNRBuipaWZ/xcxSQ7cq8n+4i8P2L7yYsLrQL0QiaxdpCn4b8aSZlkpCP64EsBkg==", "signatures": [{"sig": "MEYCIQCiKceXHvQUMcvop3dSA+O3rUGDeIjx7HYRTbWtcSBQTQIhAJXnFLCMsCYuknPMxA1gnZAUXGiRHsxmeb5JgoCW4Df/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sdPCRA9TVsSAnZWagAADrwP/ijCri9Jk1CHTMmyGvQw\nvxwuV1/c1RtPv/Q4ut7ifXhsroBMGimIy1YxXLWILSGiNLp3p+AaxElVG8u9\n19OsFxO2QT2ZtU1+mJPeRsozBTAS5Gla9mBM8j4fQa63lwmuzC/ytCFgLZ3s\nSQl/3UFTSuoiiPFIiVVUFP3G9lM6PTiFjUbxGoKYvDR65tIHRjFljy7MnThW\nQ0F9C+re+OioJFY8STAYIFWAzRWx+R84umT5Sd6irw01RKiJRdldyrqugI3k\nhlPRxjMld4aAW48JqTyEvN/WFhKuZT9A5iOWR/w3X6LUcq4T1DQ0F/3VelSO\nK+ll5HU0D1XGYvLBgFhWi2QkZTvy8JCctW6hyrr4j7ILx2/e7sR9c/VLED91\npfH0I4OrRutDZHGBAAf47l7UU3mt4evs1mo2pbmvUOZPR/c86Vm1MbDkEcCb\nb3urtOUtR/5H1TcZIO0yIrxQbRJM9ZQHHCqBXUAc6TV9ia2wReKoTdSpq+pO\nVP/azsPec+yDJYAJqYJWZdXzIPxEBKhwiQIb4lLPi/Sh5cWz7kdScvZzILgq\n90lNHwncSTBMvUt42rSFTPhqX9e2n7Myu2PPID1NMNa7EzQDyBtyywhkRaj7\n7oTmLa5oCY0s2l89H8ManjMScEwbhVETS83NBx+b600m41NnUqv3WP84iEDR\nvHky\r\n=gzuX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "d3d600ef9ec1e18c10bf398c694567c4831463b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-AnR7ScGvGUMqWCXpdWFH9PMFK5tgzO4UYuIR1Odcx7w1AFUf8ts277koW2PJdeX1MZ6lFph7XZZn4ponaX06aA==", "signatures": [{"sig": "MEQCIEgr0WO279JqfmwCFxBK+15+85RsWsoJv8sbCthP0iKWAiBmZ2rtH4/gr2c4bZ6GOFYNgVNt0KDP68CvkmjOLOtKmQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xDPCRA9TVsSAnZWagAAMlwP/jFfPWvyj3dvwAt69s6m\nzw+dytvlAhhsNInl1kWB2f32Loc8ZeFXcGPNOxitsQxGjCiFS7fL61VLtHMT\nJE8evMC1E7QypQ/9oUvFrQ3Z5xObTJVUO+zn2Kd0P86AIDdmsaBhMf4M+FWd\nEbF3/D9qS5MxMvYdipVR5NAKXUxk9TzYtBCRnW9Fc490pSL/65YqJJ3lvioX\n2I3xC0m/neXm40/fwGQLK1XDSEfIZ6BfKIILVBFYyrxD0PX/L8R9mmW3Dg8B\n1UY7+6Lc5BKJnSsWYrf4YT3ffeltY1joO+zBCL1g05T3euNP9uUMCpNHIg+V\nwWtf158osygfib79I7NuwZOH2iSRPPg07rNlPPuN4lC7lIOiZufniLwMKZ1U\n+uv199rl7pdoUCWO6wBjJaQZq0fuB1nJCeWSVDTzF3KBb+/pRlOP8701JABX\n2lMI6huWcMyPeGIRi15h9QPXWEYytWrem41pDvRsUGH1SbYBWWYWGSYt/uo9\nuFTRqnbha5knhUfdyAcFFR+pQFFEvJfzHdCNZEGBq8XMXV94c7vMpyx4cw4f\nTEJBUQWduAjEEA1AqDagvkXvGfG1RHg2+12xO3JokXvPVVqLRnjwXkOkDC8Y\nK9zUubURFFbGX2YObM1TCl59ZhReOa2JESMVVizRajdhmdYL3HjJXVgdK8cx\nvGHr\r\n=uwDm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "5088893ffb0187117708f1f95f1e2fdc3e198ce3", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-Kzl/avaSXbAB1Ss4QyyQETSECsuJswTXFjpqMM6hBnM9nVsL8zokXtsGDPnjxpPm5AR7YIwQG54ihRM06JOB8g==", "signatures": [{"sig": "MEUCIQCOQIj85nifglcoJJYY5/BI20g95qHNETpy7c570gR8ogIgcsaDlnn+kYs4dRE7HcsZ9cHJ/UrJE0Jb5kuhbfdzrfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15671, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLKCRA9TVsSAnZWagAA6hsP/2anGr9UMovlz2BRTuCY\nUhRLjyFZ9x/ZYRKKZa5oJFxyVLtYiJECj+FY+EO30SbgBb5kzHKvfRUY6us/\nPgGs19GNIF2EJfRlD6COoQhYkr85eSBNzzz56XK6dqGw+a+Cq9VYXeP3WcLh\n3f8GndIoJB3g5k4rGWYnaD5bu6KpMhUwi5aOz0KhzKYfkX3pPFNJ9lIAQTAg\nLuzbf5WeU5yEmBRocdEhZNXrY3C9MpPr8v+k8p3vBolHayPwyHhAnUG4qBOA\naYL2E1ZOwQWZDZiwZfglekpwVaeW8sq+rEtvYUkqfO660fhJ/QXGuGCgy388\nFcxRWOAVBPtXshmMGMxEJSQ7wxzKbOh1ro1AJh10HkkRKkFrTw6+Glg6s6ib\nbtXZ0NVCtY0LDG2nLUIxkLuBtS9+tiMGm8Z3a2D461oHiIMX3s35wyxkDjts\nnctcHfpCX7tENIRpyD2xghl0y4x5PoDtx3IMTIsnhIkOC5S93q5N493P/52y\nShKqOf/yJoI1gcsJ8EKT+fXG+HJMC+wyYXEsA6LKTbGkAm3MMZWGMyNlwB8A\nvC4RDcNfOl8fx3HM171zKL5jjwUsrVObAVYhelMMvP7J4t30WK1vrtH5hyOB\nntYlN+O85wn38K0x8HPuv0Zo9IWTrduKmGEvoGIDGNvXjVe7L7lVmQQitezz\ndDxB\r\n=Lpxz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0d210d8c3529fdbfcfa0df3a3b61f79b61129860", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-DmaPLaaTsQvRnEkER0StcicVifQkIAkweDOVUyowMbj3iFKYfjenlKdiZgTEnjNJMGt3aOer5xXrsNt/zCNymw==", "signatures": [{"sig": "MEYCIQCzFLZcO2nVpqgNPboqLX30c3soe6dmnSgcwiogjj4OvQIhAIvvzwzhEI65X7k5xuY6ulT9zACql5QdnI0a+ArBayGm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D0JCRA9TVsSAnZWagAAhAEQAJHq/yXcIVzSWPiQCv6p\nAQ7XEaoRrOGULLbPeXqS1d2j577PtKj8CVQaKr/tGUOVDLkRpFXLZWUjvvej\n6Yip9zZvUDEIpRfOD5hFh0mTQ6R1N1Q+W7q+2H+/5vJD5Vgrc7ChsxsQtGfs\nCc9hTHsAj6Odvz5fjdhj6MTU7ck9FiIZlZc5f/k+yhyL3nuxyIhebdKV/XVX\ne5Cw/kVqiUrL5uFP30W98hZVFfc9Z65mhdAj634xGZDUHKbXVTW9KtFfUbAt\n75ubMrl7SdJVqShGzU9kKsmsys+BrgSsxlEk9iDo0h+OtEFAU2ZYaIOOSrld\n5PRnMR8KrT/iEeSmp9crHBpvFoE54s2ZKUPCSiFoLepNmLC/xFyau6oPG5Nb\nB/h268wWHezFooH1hkH7UV+qG976jKXPBs2Bhy31ojOVSu4cZLuendgWqcfN\nM2Ta8P1yo6B1c55wDB2BPh4zL2S2TyDWeNo7l5f80F29o6tFxsCRLKwGijyS\nZ7XPhQwZOgCzMpahekguLYcFI1cOLnbOB/IoIMJniYmTE346qcfk90CVtT2G\nLv8GQP/gm1ghIZ8w/gKVxoS7dG8YSThPHaf9W7FYhEWMKdgXzEwG2pcGYzBU\nKA87xfLKGs90UsQIrF2AvWUPdaynrpE2FKtswqGNBD0L6SpZG3bA2i2OIR9w\n5nZX\r\n=wni+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "9fc143980760f752f22b49869daad59f28d42f50", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ST6ncYzYsVcen3UCmVS7TcMB5W9ElLxI2/+6n6qFJ6EpxAp3wkW1i/JlIVlxgmK3rSKy2ixL1Zkoyql6u0DJwA==", "signatures": [{"sig": "MEYCIQDhDsQfRsO4+YXSjKF9Uf5rO1CcVIbehgm6rDRNw/UeRQIhAOJzyynBrYzgzWO6XHwbtFUHLjxPHICipkWzdYluUjOe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SS4CRA9TVsSAnZWagAA7O0QAIawb3PrDY85Lvx5CJTe\n8l/epMyNRfwGQlecQugxJhZJvc1pQcy8kk1iXtKpGWOnNAptpnTWaXJ1XyFj\n/VRnIAqCAe3uwcj5OhlFCn9nNlecqgJhUITOi3wgBVJjsDFoi+6+esi6fysb\nCGmHVJ44t2ksK9oiEzp/36vWCIKspk80r5QzJeSoJVzp0qPXjl+SBnkV6/3S\nTs8U2IT3RBfJjZUm50Bg5GeMD67oNAHudXKo8HO0A9vq6FZYZdfDKG5IzWYf\ncHlMSoeD4mRHRr2lF9r0LbyLpyuqckHVhmdRTNpLj8CcjMTTz5Gpeh1Xtwgk\nWPGo01cPWlIwNV5WCwYwA2ERX6TRXwWynKd1ABWxYKgK41o9MDMyWTyJZrSa\n5784vIMfs/gDllwNnvbcIczIjbHx026T2GjhcV08I+bNvFJAXmh/YQVCG9J/\nOSCVPfr/Cf0ODONykXENJJWlc9fbygyGQwQ1lYLXxHLyYsCLAH00ZbJoGeM3\ni9T0BSY+TCJVOilpXUcTA0BB2age1+E1Vph/RV8lXiSUALq2OQkPE11TcSOO\nFplwp8lxU1v2HbXCgAzBV+9xSo2PmJu2jeCdIWR0qxv30I2+jGa+9XLnGLlW\ne568dQa6yI6U/LTmYR8Whnu5a6n8lQtdu4DYWuzv3smup8dybIF5J8e3gF9G\nW3Hn\r\n=Kkbj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "0add704cd53fbd96436db77cf7707d46621a42ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-vAv+gR3a6QG8iePXJBPvO60D7+sMSBM9RCYG85LMhpbtHnplkYP3hlNiiMH1jxuGNCJRFlyWU6ZNfqQd+dyEOw==", "signatures": [{"sig": "MEQCIAo060C9Hwu1vwRxdolLjhYrjflRhT1qHBnllIcTgedRAiBVGh1JafFsjU+utkm0sDalNYo3LsOfKyxLXilada/6BA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DZyCRA9TVsSAnZWagAAX5sP/0mbM/DW2BLaA6RJnmLH\nXmrglOl21nr1OS1SSDLYtQLBtn+YQyqFXO+RcLyUgWWlss6eo5ZeG663Uiaw\n5W3j0c7Q0IsAOjSlh+ndVDC4wxEnQDosOkjNTz/yUGEuhZzxGW21DzNPj9iu\now1RBNLq9V7z2W2dL60KqyZYfbWLzZOXtEVmWMx4NEn2cJFswDDE4k77lMv9\nOaZF5oOXBdjdEnv6kqlKD+YsdZ/e7iQv+y7reITjLoRg/WYYS5KuOwRZ/n8f\n7Qp/dYFYgFdO3tu0+VTJRPhjse+yyep2a1GzeUPZjCXbisG7hKeBKKrUmffm\npjQUentptxbrJCrIh43XY47SEq7gG3/+ffTXdCpi/Nh5E1IkQf//Cj/wogRQ\nKdByBZmzRmmjNA3yzoCGG7Ud29nErWwZ4QvNYL0tY/IPdXYTqnEqoHt05Pe4\n41iIfvGHjhoUTDTmZCOVzSOsx8MseGQ+pX0ObbO7qd6QzVejOWlgSHjGTlb0\n/7xBYNhgzjkr8eXgU3nnSKyi7GqBScXLATLNnERkFvsm88g30kUALecO6nHD\nKdPUPL20LUNnbqS6cTrvKEjC6BXjlWSWWXwHLTxS3rNDdI81IVxE6UdIIrBC\nyaArJnkpUzP5cNLaWf9YfyTVWKbjdy0tiJtCcJd4NsQB0xMwYnNaVUpIDgik\niOc0\r\n=YeJj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ec1bd85c5afdc31993ce07188ad8d104127297f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-khYlG86pXedeuQXkwaCp1TR3yXG1OUWfXOWv9Tz4Ua2B+BVAUM0cCWSjGKPLX/iARtkf1YG6qTd85DYa9m78zg==", "signatures": [{"sig": "MEYCIQDKBJ9loRrQqNTiPNKM+aF6L7b5VscCuCjQnjM9hjffqgIhAPElKflm64PxQme8Aa/28GFdThVhdgjkjbLcqN8d59aT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+Wn7CRA9TVsSAnZWagAAh1QP/0uExj6jtqetwKojJGzT\ny4DkRk9jeLWVvf6CibA5B4L7Hy+k9m+kvmf4BeNpRpaWe3Oda2CzV/lJqVtY\nuXlUyXSfYbS6joeBOhC3TyOQ9N7tOktGT6HFMU5apP5fBiCXxzwKGhkOl/qv\n8XFB2d1UCQ0dPw8qv+7NG4q/k4u+qAM6ywtnqARvWlGE84Q76CKl873F6ZO2\n8yo8ZfwOsrzsxnr6rNu9U0z0C8/a+vC14LmXhuCVjVTMTevyWOPFvXUcZFsf\n3EbZq4pqF4Qshybyi/ggJlWMW9CR61rdveZO+ifQM+YQLYy+RwuFCR7eYcqd\nE4yUF4LnbSVndYD+o3ml2rNmsbD/JvTyVkeArE7QraE4OKLNwmzOpYCSxJLK\noPQnCdOQmwe9E5ZXOxCiHDZor/CRaei9umIrMjEvb5qEiXnB8Z/uI/OAyjRN\nsoS0THHGkjnd353CJ+0K714kii6gYudCbSoCSpxW6nZLTQBOkzq2rvQkJMTF\nshLkZsduKXTbujnosLdtxFAI6SjEkaonvM7lTWFrh4L2rplGMb73xHRdpU5G\nT7SsT7cewK4eLR2M2zzSDwL8lJG7hM/qIC2tc0XInwQjsKkZsXeURpINhII5\nkNe3vU53Ox5gz/wVvmB5KS79jUZ5s5SAsM2YjfPar+nBd/sPWKR69TMv4CfG\n8Qti\r\n=mgST\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "ca22563f4489c388720d4168b8464ba1e5a7b83d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-r0Op5HGlDT/ha1ACSb29GtKjvbNUDOmKX6LeJPUC2BxvMzQQCdsY5YH6KWnZcvEiKop0Dc4AutoncDzxP7HJhQ==", "signatures": [{"sig": "MEYCIQC+zqgWS2TYv4BI4e3fKrqYMfMu4wlJJ+saSjC6/NwHvgIhAJiOd+6nietUJIlBk5m9wSdTVeuQ7j9/5ctBp+L8dcNO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rUWCRA9TVsSAnZWagAA6p8P/0V5cQM3eQgxPMIaeLX2\nGwtEJuzRpAp36e9/iRxJJjGLF7OO5AHBB7c4wbjqOEbcg6w2ovwuyBvOs8cS\ncHE4nawVMXjSM7ZOcIvr6zmB2Gv3kJCLPv07DrIuJnzIcoEawNgw/eu0jAUO\nnBwn66iRjcbzAxlCAppHn4gkiLGbBQbJZdIhkuU8WJohKOKA/0LnK3w/qxe0\nGPhGiA6J0DxCSMSuWEqAPnFlb1C60IPesTWBzyKa+DEgAhfEbFgf0eIxyXga\nyfUJyFLkTKIskpRwVJDpxIJuvR/hccuLwnbgfenYvDurw63O/OIbOFKgJbET\nUK1yB+w8MfpNqTBlFtFKRTCd0d7OlC8C88xtoEPoBZNnzpBBGKqGOOOmL3TV\njnsKttzR4UGn+mGrhw+5EP8tRtK6oFofzKyUYN2MqsvcAf14fmtg4+m4kJv9\n1E6ULbkFOnz/URpOnZkrzc+poEFniWxfMH0le4Di3mtZJ7S8atnqD4dqyaFq\nDMNYyMB7wMbPUMYX542MK6yH+Iu8malU7bZGYQZyJK/h2GQ6WEzhV5IX6qIt\n+OR9O2Db22W0QQejdmpzzmB1l6bek+K3JcuAIF/UrsN8UYA78S9xMZd8/32S\n8ybQGxi8y2PEsYctoUmfs6+2WIZqvphjCwNszl0v2jYaVMRfPxTBKIrS+MRr\nYwCq\r\n=qADg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b9ffe5e63642aacdb310a9c3ca725df58d3bba5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-8r0Vus5y9GSwEqc8Nr1ZgHdFWtudfd15NYS6dKmdebZ/P+BuNbfc7YCOmUtfT917C7+ofpRTLg1q/1gXhGqu0Q==", "signatures": [{"sig": "MEQCIGfAlzOh9uT/So9EZPtUqpNFzHFjZ+lu2AKnw3t+s+xnAiAXx7nWPCNWpfHj5ge+4WUcu2nxY4P1mvqRqkDpONrKtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/nYCRA9TVsSAnZWagAArO8P/iYnUfNXLnFtQPx+7Sz3\nHjCBilDbQlvRSJUEur8cgaAFKOuflqEWV4zoMC6x8GJJcACTU2vjGg6l6T+R\n0NbxfQwxNF46YtgSSjNqaTZ/CEXsPOuLBk5CA1iQbFPyOBQyT50jpRw2XQIN\nLVJcPDJ2PaRlHaeVPcu9OIb5qWkLilP320NU3n3wJnNtIb25ujLB/pjyftgp\nUyes762/slMcqPBMCF0joW64DvEvG+m030iG+7bBs084ioaiE5JPK6rizN5G\nNGHWaAIl2Xyviejqg6EScdr9rbo2X2aI+XY1KYYyNBXJKAnuU+yYv7yoM+r/\nFlkXqHfFLk2VfzLvc93aTX0ZwQRUk+SnSsnSX0YYBozYwHJg73GDATX2d/2Q\ni92GAkWhw/jC5RxnrHL0sMeqI5mKKqbCJqP5aychWtqmKQpxyxZ28dX4KFy4\nsn0LM9OAeRftymqP1AioTI0cESOlLUyujiq4+zjXLIg5m4PwutVnzqMs3qCl\nHJpGb3H7LBt5GM+vEAP27uvcsI15cNiNHThu/hS7OjCZ9zB2gZVnSwh+eEHL\ngYn2wrayy/be9vlBNANWBxRmH/U2eBl6rXcfzULy9zHoTcRZmHDkwzD5ua5A\nvK+lSo5290gecLg4Es6kXSJVldO5lfCQVcic1GjhCD06mEQ/o4Y3nYRSNYzo\nTe7U\r\n=RQ+1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "c84f406c6a7934b7d21cd879373c99bdd3d85260", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-q5dXS570G1VIuPJER9DvVhwSOvjkbJtiRRTWEbY+5EufQEOTGLf1jdbim72QnL5kAqcU4osCoaNkSZZM8ojlcg==", "signatures": [{"sig": "MEYCIQChR4uYnkDQNUdkekinoEB7/EUlSwG9YczYxN3j+xTbmQIhAOjwYpsDOblWy05DhsdipRNFnTSyMKlBrmlGNC6SUtTo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBH3CRA9TVsSAnZWagAA834P/0T7K6qw0G8FrvFIvSyN\n4JAqUK0CvDpyhCx0uNL1G3fG3PLEcNXdSL7J5yrUdNOivGFikA5z7X6K/C2Y\nkfGrg7RYBSEYlVQIXb5kAO3nZNB69uiScY8ombu7PAhlX5gLgwHYu8X6SyHQ\n5Lkapv9HCYOSEyf+AhFzx1L/LNLQLm4FhmjvRkjZx4XvENfToqLW0D54Haj3\nzwGIU8eQTRFMHfH96SztT12266E3dU/4l+787Ux/xrJhjJEAhlyBPHCNYniZ\nuj/ISnBZsV9mXiyct6/6DT0GPOXW/MbLct64hsu6tZf8bQ412IlRoOMz217z\ny09GOoyC/17EW1ibMmismdxJWHrg1ncduALPehWcql8S+V7jSTQZJbxVbsLa\nwru6+dLDjNNPJjYyit1rOYx1zQbAHjIpvXzMZDjNgYzaaZ6OH7bGbapzzqof\nA423Jvns8CueSugEKfdU/7ubx7Vr78Pq2SrW34woh/LlySqyip7EigOo1mrO\nMsrs3obRQ45eqj9GOdXDT4EMa1/NwT7yS/HVYBu+g30WGQ8O/LA2fCJ3+fhE\nBkjmhGDshngH/jWrSyJO0/3mcHGC6B1011/oKuVX/pJFoSHcFGAu9I43XX6o\nOGlZmZjaFagRxFbmXEyBMIgbfeGbu8ocSxO+IWqx5ZXwRa97nFMGUMHUL0mL\nyg3y\r\n=VPOu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "dd1693a13077c5a1a983c8ed4fc83c2e3896acc3", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-2QWhZhyVKFtNH2+cHpHAZWrAHpSDZlbWTmac8cupL2sCq+Aye3WWC5+nGkNHVBpgXjeoVACOCBTgAM/6G186jw==", "signatures": [{"sig": "MEYCIQC5D0eLR1GM06fslc24MWrrRdXuSLJbhQmE1Pji1Tg55AIhAIIlOpLrQ/txDzIFKuINdvNjJgVKI2JItDWtIkqbzR29", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYDCRA9TVsSAnZWagAAGgYQAIJbrJSXkjWy5Dw9mxQg\nIpaX0eBy5ocR3AVIj/eWtel99ITU6ALSSzKPLHmzcDM4KRyyeg3soZugwKJh\nPGXOXClxDr+btRq9WYdaxfNCKdwqItVq9ZLo6zPYurMoKo5E82JyEhDm1eb7\nq1jCYXthQaKE14O3GRgy0rvdkE9VZnjsER/agEGphHSJYePlnB+o+D5fpci4\n/4vFoDiUijZAVEsUrWaSchzxyRTGtDm7tMYEp/+Yxy5cMupOuriDnZRQN2Fs\nAbONk4P43i4oZ+KbmyDmGqZxlrJ84e2GhzXYmdpVnGojZGhV741cyOTya06n\nL56Gg19E6wn3Ms2So1rf578PNen9vTdufooK5ld3MYpkuSlxsEL/EHBmcO0t\nVFioorKOXzXH4B6a4r/CVizas0/LvZB4sVOYz8tKpmJ7xMp5a4Wp0v6Fvpvs\nOb7rWD4P442MefUheGeKbQOH4bcPr/2I9R0/sFfWz8YQ0y+snwU7vzOYrk/8\nxvfOr+q5yyGFwoiyD6c5OPOJAmrxYaesD2DBHAkNMRSl9zJtwgYatilOI6U+\nkrsg2cxeLdn7EjvmQNtJnDMSQeH1b7TaX23wEzDgQ14USPKIujLfWLyTMhCZ\nbREOQ0fdVZBBsNoesdnrLe61PZTE/UHrNJ9zuTfmcmfXGfeMxyr2+kNr25e7\nhtXe\r\n=Mb50\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "b2260548c33c4e76a10055623888ead4c118dfe0", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-1v9Qtyofd1vLAmu/L1+GHNVXS8yB1TQHYkBVT1Zm3H4o9j+z3tjfi+ERBaiPx0gqedLHkATD6SOSDhXCG0qsqA==", "signatures": [{"sig": "MEUCIBJZhES4isCk0Ke3dfGnec1fCzq1vdDjm6PDnIAktXzYAiEAt4PZ4Bhbx6UvC35mugOQz2IJzGJbRF5AvDe22kvi0zI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDllWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqC+hAAjiPpbiJeJK7iLDa7DrfrtoFjzYcq5m3oFYYDfJWmoQyzj2mK\r\nPMOx0zo5Y0mmkkbNLVrnbMeaNYRry/xF65k6D+Okm5+i2bZeRP1TLkXs5KVB\r\nYLeyfFeHubsCtILcC2u1s89jfAicMsu/Fkh2eyLSB97gJiu343+3CuJ753/P\r\nPfuV7+tGoYLwyVFOQsj5ZtckgQWKqzVaEwUTG3fhbYmhqPQAPJucX8fENmI+\r\n8IWPkzYe+oTMnDJ/JdZ4H7jtCQgXBboXJsG7+ukOFLFh7ItJw2B4uQo+UPPB\r\nDVpOtEc7NvQbiFZcueOWIgExO3A4EV7qTYxvDtElbtCKSxEhFHK1ecelQaub\r\nW5UUUwAEku8Usn+aSB6wieN/nh39YsUL4QQjnK0goYd/PFO3s0imIFNdWufX\r\nIYjA+drpoTbb8wNc+2qkyMRpODw+SCGMBfS6p54N35EhqiUTRut9QSxFQA3z\r\noHwTDvd6MN7TLyEMEmyyU/ycelpVH8qiRGOdeNNCBqhTknvS3xXHRdLRvZel\r\nGns7Kw7RT6AJtTi6TeBwQ52AymmPpNWH0W7m3yOSqM64ZcGPLfQow1h7zY6p\r\nz1gF7E/5kmkU2gpUy5HsxSfPGRLJMi7BtCSxbQKaL/5YY8hnfYifMuSEneNf\r\nO8m0b8H7pZckbyxRPCYKjH9JTq5XQqg25ww=\r\n=8DRb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "25b2ed5230d64dd1898a6bdb4789a19ed1fddaa5", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-tMlxuKrRkONhz/SQR/G/7oPH8MK5sPnYR9mVXjGMA2Z84NWZ9XuU+HcT7guRz4/Uozk3S1Yb7qW8YnhXbXUDsA==", "signatures": [{"sig": "MEYCIQCPR0QX87iPnojR9B6XlBkURqJ2MKeTVMomdorn1V4yhAIhALfWu13nPu7YgTibMowNpd7CaCGwER5XKP4edgJJJfLT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkU3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+0g//cS4zHKGjNryKn2+47fxBL83ueuYkthENPKEyOcq5wOvIHVpj\r\nwm5uYm5pPCTjWx6EyvJSDFfRLy6cBzaiR/8YyxxW1opTo5Qi3yZPTeDUdRBm\r\nLVwDzxLv53rfYzy1Sf8u0n/IFp1CEaatVS3HM+0QYB9ZHcoKnHOtplYSo2d7\r\nnVuu1mV/nPkedh/eo+GG5/pTLpquPnH6unJ3GeqQKZOImRse+T6RYgmlJmQW\r\n95I4ciUwVWgn9nFP8aun3bwqjjE+Fx5IvOYjMxi2e4dYHq3oIWIknGWMevie\r\ncVYf6olS3mMOBqT8JLM1YPemzjOk6p7i6q270+IgyU24lN5H1fRXvyECs7xN\r\nAeISL7WInIR1LRpz7BYf5zerail9Wjv1/e2sQkCX8YE6ejQiQSTbX7mUAwc4\r\nDhSexWL8Sy7eBKkAI0ysnHz3nljWajC9UxxeFxseLt7xETUGctlMkYXoouyy\r\n7TUHOTOdXJxhxBIFV5LNjX7lvTvco4eYsRwM/hd4aVIg90PPpQ/DNuWQ73ZM\r\nXThhCl6LPRV9M7WhdDPRVn+IjNCffMKDuMOOFupnnVWbrjHsh6yyY0pfxpDb\r\nlMWrevsdbdG71P/8mL/9k7oWiEZseT+GbgTt2IetpQX7jxXbYBb1sIfLy0o9\r\nQ9tQTZDvJZKksuYfy2wEdf3xk9rsQwhT//s=\r\n=XzIN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "9f359f8de40d8d924554633558252f7da163f511", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-DpGk794V/dvZ++77zzkIVUEldgNXILBmP4Oozn9EU/+zjyE0M/GLkdezS5147Cn1O/9bpHLZ7Ho2vLeSFvt6dw==", "signatures": [{"sig": "MEUCIQDBF6Wn/9ThhqnSq8P1p6WPZmUeByDRTW/E37KnMgWvigIgcYO3uxyJ9oO/NC1ul5Ch3CRB+5gMjJMVq9cXw2z2jiI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRhQ/9EzlYyc4zQb+xEpwtEWBz7plZk8NlsRzv/Uskg96q3gBOv4Mu\r\nffU5/CF8v1EbCg3TPG4coZOqlioqVTAw2gzOywM/s5tRukbeevPOwJveyKe8\r\nHkTtQFxPCOIpw12x5drLoVF0ELT0fiDpqieYb+71iZhn82CO1W1D/Nc9Wvjk\r\nfZzoqRjlMf88eV5f2w0ayU8Z8FrxcysOY1gsAZLh+QIOZrr1e0MIcc2xKPCP\r\nFdmXWwZ6SEZPPF59DTj98J11g+0ETyXCxpOZmUBR7sJNnkTaV92TEZZugoak\r\n5UtWG4AIz2+F0G/PcapQTZdPmg/ChLE4t19ONtYSbBSxJTzexrol8P7BBBAJ\r\n+8Nv5OgF6SsHJFpXKRWGpAfdoCFfv0Yo7eKqSBnEQQZoapWsrepbBq3azLTW\r\nJ7JOxkC5wf4ZQJKJtYZVYl3ww3Wk8AZ6HnexaUBgRmSewGpzah1QxFIZJUqa\r\nPeRow1DN/OSb4r6pAJ7vuZgCzmupuci6fwRFbgxQDHkRt95T4lSyiW4OmRAi\r\ngZhrQb1vV1jbvpJpObMa14RTw89zGzL64q14XHnBy861W4pA/mv3ajKiiI9b\r\nQqGFrQ++QwLr1tka1G2ygArHjh0+vAVosdu6H5uhYGVuQ2VxK/q7t726PIKr\r\nkATo9vncvjhZnDIvaeabBCl6Mk2PEYYbFpE=\r\n=rWPY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "291a46748310c266494dfafef3efab9acf71db96", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-uXn7vEhwbsKkjB0givmWMWa7EO9QST9egJ9+XUgv1Ej1ltjywYRMvZ5tPhtSpayHGUkzo63cVHU9b8FZMGxKbQ==", "signatures": [{"sig": "MEUCIFZ+VWT8uKzQjAFD84USw//eSlrTD3EAzNSbRfn0lkcVAiEAo1fSxf7M5ltCXBQzh7jHKRzHWY0xuvjDP26r3njbvUA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkyyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcmQ/8D2uaSCZyn8acdz0DNHez4vSO5eGev8iYmi6j2bTK6L7GGNI1\r\nox3S2Hn0UUYTsOEBt1I195BxnlB4dKjKP3llQ0+mvdYJLDNTRnk9r6z2BUfd\r\nxvCHrmVaZqkf3ree/ksYZSX3lbFc3iIWpA9XrZ0PqXLmW+kWahxoF1/8Wh95\r\npxBk0vfQkKl//SoNHlkPjeFqhp/xfRb2PBqOqcBycDpjaUjiqB5IXFLVm8yf\r\nJpPkjgmmPr2RSARp99f+87R+pNzwoR/3QvMUzamXU0TAvULfO/wAXh/QWYHL\r\ny3mQTxRVQ91nJTaobJ5/KaWg8D82rO3wiO2YhJSbcQL+KkCIhsB/1zGuHkra\r\n7On/uhH/wDWlL3Kzt2gBJnqlPXzzHBiNERxUICbBf5JToPQUfKC+rZJjEFlR\r\nTkXHbgTBlIKtvnzrDJScVL+YdSoNbbEsmw+kpJL67qW7bf8+RY1Kx/eaGNAN\r\n/M9GmSg/+9rOKJpzxLNaFvf4+ILlOx2zKoDU5Gn+2xnKjg0++WGn8Zwio6O0\r\nidQ8tMcy0AoHrAkrmfx7hLRL5SaGGUzso8WKgCZOgrbeqh41dYd28olTtz9v\r\nIHId/aNESTN5DJGT4PGTax1kbae2M93fDu/X6oZrz+fVgQ2o0/AIk9PLP4OM\r\nDJMh5yK6gB16CakdguctU8ff3BIGCoW/meI=\r\n=65hp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "9fc9e449afc52ab7e30acfbb137deb08a20b0aee", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-+pTCV5WtqR6t+P28lCEqL5ODm16HYuWU/bggF4nM0BrKLAJ+6WsUdvDn12HyGOY+cAm6Sy4jvVTfs3hJP85GUw==", "signatures": [{"sig": "MEQCIAtNmZIT9Lymq/VCyECN3Ls3uUndPIMqB26hvVbTy7mhAiBNqwU621fdTPq+QE0NILOUPL7QWbT7S5IE7tJ5xxlmbw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrgvg/+K5+4evWXhoYwqU8rnrRh24SGzH+WWfje2AM8aO9I0X6o9IvP\r\nZ45J4gy37QKpSYJklag3PNCezd/zw6QLnqsLj4kNricDmKe97D74B7TiFsgj\r\nF0M762nWWQXCv5BSzVaDVjZQJ3W4s2Eh1LC7C+d5tx4FF6Zjk0XKF6S5t62o\r\nbwBBBZ2OaRAE5JmVrJPcYBs+2ZLkBcxLGH3noG0qBhiwVLQVlhqQW/JEF+F/\r\nZJvAXJ+cdNyc8Tr36Ng/wOYzWmfydmNtZMWPz7nC+ynklWN4fpgHhxJ4LLZY\r\nvBGUjPgWLwZK7fqR0M/VB7ZC5v4VgpxU/zhir2yTuzPunwyfjfI8YyPIV4Lq\r\nNjSwWRHruDNS4IVx4lN0TmNLI75e7O2ak9Fp0uUshPt1hsifuSRTYnsnO3pz\r\nE2/UrwiaOKVK2oBZwS+ux14qSvnxsMr6FpQj9RHxP7PA3DlAQUsfkNVJ4WSu\r\naYJSkDl4vbTPR4D08HKj5pLpGYnLKfsKncACuW+PaG606TZIIWGbue1MKMfe\r\n5EC7pVVsDIsypiJ/YuHg3trz3dhN2kZ8J0qwDO7jAgN3NKZHxDppVWJOqHsQ\r\nhArGBy99ut5seqJaCV5eYJM0xgpD8yniH41sVrDGtR2yxqZ8OS730ySKeKNh\r\n7HD0GVxJynzBhPrKvQCILasQ/WS294XJomw=\r\n=uo/Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "6dffec3bd309aa3e5545c95cd746ecee51553b84", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-Y81gQq9/okX9SkWMDYqcqmzOxtLK+2+T1NzKyO4C0mgpGlbBIyTD6TYmsNpARzY4gVwZEHAeXChcQ+PePmrmbw==", "signatures": [{"sig": "MEQCIFhd1W7ZL4ZVHEuSfFECxz8VvRF+un8oR4yiEQU9F8RTAiAmY2PswHHrbpsnhmjhkFyu/UDGt+FN3gvfF0/vKdGF7Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFoQ/+P2Jz6063ygWVFSTUFnD3nCD7gsFakivXgCA8DSJb8YTQZgpU\r\nGlIuUH49RwtD6Kgq1ncQTb+/fDxb37DF3kluVr/nTUgDD2kX9d28w+CihCfC\r\nCCRSW6zbguhPI4P36fTLlW7DPTGIkahHnkU5CcTpajY6GzFU/SmiAh180DjP\r\njpAyFrbMEUHGqASsBlqxO8Uzu9yn6pVfqVlaTcJnyWUaUIPy1lffiJTxi7Kt\r\nfarU/J/oTRYc/2j/Ka+E1y3kxKP9WaLUjJ6/xRXM9s7ylVgAqLcpDJSsOBvP\r\nWEQXZnsz0fIDisxus7H/jpjiINmuxfjsXMgFpf076zHqro4I+/AZ0wipXk9c\r\n9gljddeLBSnZ3HwrEiwYT35IHTK99duSft3QViYypgz9UZo9qaIN1xVK8DO8\r\nIrcFaOzZ11P5aSSd1ohKq951TaGYn37k1OjyGn9wSYFg9OuLzDqcmnPICIb8\r\nYexj3mxdkT5yZyk+RKnFwEewbTK7D7sFsM0WZ6AYfy5kgb4O+hxmPQe6p8IS\r\nf6TulrAXb6LRV5vWjhwgQRku/hWudpGDOye+AGX0ib8o4tznA+Sp5gnZXYNz\r\n5iGPSx+t0475XTfNcWNxNy0Cd3C1QEKm2W9vkBP/1rzWV4cUo7r1wjBiczy3\r\nu3yFZRMfb7XPI9NyOnrLxH5FdGDmaKREQsA=\r\n=mrUO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "1e192641986b4368e30ff0cbee88ba09083fa9cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-DqAeaH9iTMNjy6ftDpKzUxytptsZp31UBEU97daHpXQxHEpi+SIO9+/TPWSk92+Rr7ObriyfNScwu9JnO4hOsw==", "signatures": [{"sig": "MEUCID87sw6oNDF13aGAVgHyoSJZC9fWBeHkOgaoORYnOAvFAiEA5grvKJfvfR+YqWrsdIRRLxfcKX6wDYoHCnsRXq2tUGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrJxA/7BxLDeY1Ff+rh7jc3n0NZdi2ybjtkVT5Rpf8eHf60XBEzkIVA\r\nSIMs8gT+OPjCw3EuO66dqW5fp/rOkvBQttmmSkDzAtozM4BhCBXCBC/OKQrt\r\neAvy4aV8x0Y8Ef2eInDshITnT04Pp9xiUkb9qMwMaBxWPlEyBIwwBUpnaioM\r\nRVIxZUDPdkkwspg+NotBU4m+ccs3oS4nl3bgN4hh6NIlsszdaJgp9uJSDs8s\r\nWuCMUVDNxF36UlsVTzi9XyIeqjYj8BfqhVvIporImAweT+ib8YONJYerK6+F\r\nMPE8GuEOByRqN+osf4pftTZOVuEQZ/RJGDzcSFnYRYbpV7eJ2GF9kBeWCUo7\r\nTJsAwT9mVMuEMjfhT+u1xT1CMqMtWYgFC24T8NTBpdJhjvpD01YrjxOZPybN\r\nbYiO0OdX13sRxkQm25ZspmQOHnuwQTpjs9S85qIDe2iARFVvwSI4M+x9aN1v\r\nUDHt6OpIgvf41QBpK93WuQ67t+MsoKFFo+EFL4cXi2HkVxbA/MVsUcs7AMYZ\r\nBvgLvwIr+xB9Sgt4DAi4SNZstomd817vfQcdjphjYntqZsUNZQVzddemzYF2\r\nETD75hJX3D91f6Mul4O4Plb2GpQjktDYvqZo67A5oUc99IebOYZqFiZG5eQG\r\n9VArndKmdbzq0IjwW6rdBxQk55oaAaUyXlU=\r\n=Wk2y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "c4f5b22d31e7eba15608684a465ffb6c83becfb3", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-4aMD658v8uSdx2lfXEgQsvvN3frrlutiOvatSO4CwreTklyyXyIkefHQdwDloCCGxU/52v+eQf5JrP4smFb8mQ==", "signatures": [{"sig": "MEUCIFKxbtmys/gUmvS8ANSMq9Vz2zoRpgOL1FhSyMUxEzP8AiEA5Ewp3s5icB4HAS1I1WQnnRVL2cqIqfWzUHc+UmLvnPQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4X0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqhhA//e9FxiW4UPfC8mNNqZIComwiQC+IPovL7QxY3t4E/DZdAs0aT\r\ngQ4qs0fpaFnyAxO1mlbZtoNktlTIMOC95LhELSzHgupvgr0Skp92CO/Ocf+n\r\ng1BuP0L5fiulcipnfmyVNTlsCLf5FaTR95hAPz8lRR59v6E9a9sC+Kzv3Jxo\r\npHA4e9hOlQ1M1J9eOcfVcQnAhXcjAKu7hHVploxqPZXMJt/RvCgs8AS/RFXv\r\ncV1zAZcOCgBZXQrRB8WRK1ygoi3Jqf0eB9h8JK8j3SqcPfOk2D4aoDmSVweg\r\n4vMfy0PbbAjpJSd1YxuSjl5aOUFAodYIdRS5PsgWK8qUA1K+n8/MaroYBB1A\r\nbJlJPZ5pBlhzepxCAoyiyZ4MDp6NIMu8yjt6qveOvRfSc9Qr5oR+dnwI38tO\r\nZEhB7coAFaTnRaPvp+ia5F9/+Lr+v7t3G7mg8AIS+VOPHYlppAMngVLczwEN\r\niorrkYKksnUi2mV1SdWtjXbWdGIbKqGX1SJZuYekApMt/6Kzk4Jh+IlHb0JM\r\nrBP/jEQHkiINfIrMbnSsAcQOTdC/tRV0SK4bI14EIfABUlE2+GOietFjQsZX\r\nB7ndP3jRP6lt2G3lsbYpziCx2J89tYfNYC1QkdlyZHln5Cvcy3CEGk7ZXBEC\r\naLMHbYSzwvIozm54lX9ZsEkzOGyGtZy1BNI=\r\n=N8V1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-portal", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "1a087d85921a4d77d818ec1a93528a2143733243", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-3Q59VR70HvWJt2FkbkW+M3ZCLxSvaoyB/JKsQrqiaaBmeeURU6lvP3P5x5ubEWmLdwxWk32X70n+h1BxixspHg==", "signatures": [{"sig": "MEUCIHBScwhgJg9EDF66OW6nae7ZThgmZk9nAKqA7IyTVPmvAiEAqs9ogku4kBzjDd8pUfdHPZWwjm5Hz3XMU1RrKkLGUqs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15673, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCfQ/+OTcP9x+b1tDQAsmzgOd3dbG5VeNvhvnv+yA2EHVBHft7BRfV\r\nShVDZNoJl09jrXzUrmbsFstTsQFenwPNdiodjEnOGQGjceQEwaJlyUA0zh6V\r\nQYtQDWOHN3dMCNqv1/wQ+64YAB8Sj0M9wWDbv6rz8JsxUcv6DLnBoUEPJIz1\r\na9Ryc2xRZiejlo96qMgk0gRuygDrP8Kyhmk3HPsSQdb+cD4XUjYvOjtq3voq\r\nGDkX+G/BrxiLkbjtGZ+j820nTap7+fA785mGhEtk3LHArrlg0Dy3GHgyquHl\r\nZCJGcRi38OEEE2kdlkeL64baD6KRyMwyAQsyTlCgKls9TcSsu7fuXkZEq5kW\r\nkWmXAex8emO3PbPe2qrpzjIE8z6ZJHdjVU6xQcq1spnFYKYmOz9xL1Z3jCgh\r\nZeqK1es1GGmxAkl9qBJJkncAvZWQnfWPmwc/Eq50drNNAA5/dCPrNdCEwfYD\r\n1SaXtTsKXRkSG5eN6yCqa4lBONqJkT08uGVBHgUGjUVT+j0LccHgOtwRfE4Z\r\n+/xeSAesgpWYBsOTtsuGPw6Yef5dPSkz0Yp+VklfyOzJITDn/SyFgNatjJKZ\r\nyPOYPssri1pC9QF8ZHGr7FPB+sTid92Fm9GKzcSeV9Hp+OxB08QdHZ8lxxl+\r\nuVgRylgzTL+wXzDdcdfljhizK9dw3I4mEXw=\r\n=UTaU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-portal", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-use-layout-effect": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0", "react-dom": "^16.8 || ^17.0"}, "dist": {"shasum": "17bdce3d7f1a9a0b35cb5e935ab8bc562441a7d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-MO0wRy2eYRTZ/CyOri9NANCAtAtq89DEtg90gicaTlkCfdqCLEBsLb+/q66BZQTr3xX/Vq01nnVfc/TkCqoqvw==", "signatures": [{"sig": "MEUCIG9aj6cVmHh1smXXQ/jY+EcHWSkmGYux972h3Ol+sY9gAiEA5yOgYrKyGZd/+qrDLsxMgZzobZsJok+1HwO5/ISSHWU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEBg/8C6XfIpwMHXTlstFZz7zM8DHkB01a3UhzgribQko+HBcq68SR\r\npQYd/233ez7rFwtiJBt+IxiFrt52sarJE6jfLcQ07wKJ0Ik3buS4S1OhSoa+\r\nQcroVXElpJKzq0A7zNrEKFrbqt0IjeElrMQWWVkILpLtbNLEpe3Akg7s98aA\r\nfWiUJsiTCQLJ+g1qHuA2+xFqVRtscqrPv93gZ9pS5rU7wvbpvGsjnh6RJe6f\r\nwur49PcZiFo4IyWt4n9B0CWuvv/0gzLhywVBb0UV7zHUZI4giYNy4NW8V+U9\r\ngp8C9yu62zfQrLpJ5951TAVAgCUT54Bs4JQh2MBjwX8JHn4elnumsAZSECEU\r\nnh7YORKTDim9Mk68K4rEfyc3utPd9fsStCTaDsXwteLBUbObJr7FMsx4I7i0\r\nBHGEMyIj7aJkEK0Gh8vqhQnuOi6pUR9Ec5NpQMtVBFnpkGwsxDkOKz/tMZba\r\n5gcwNApa6vWcQ3PLUWuK5ZE44JMJLTo7d9sB8msMqkp7enppgwHtFgSxnZ98\r\ndeRpgu/7ogBwRqWBtYm+XFvJ0ni6KYv4kr7BO+X0scIUp4VHwoqHpBlafYul\r\nbzpXgpQqrHUksIZNBvpsBXu3zBXk+xWt3qXhJZkrK0RDWwTuch36EOQCNBjS\r\noLfQhJYl4SE3lsBucfdQjs/FB8KhwTeJWSI=\r\n=NSHc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-use-layout-effect": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f0875876950f22e008c58513adb99b656cb52f19", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nYMXQMsRcBbeiuK44Rdx7O+jojhKjZwnvkXDFdu5a6PE6Ob/WzVR5gwj/RYV19P25jSXdERlysmxhuShvzpbpw==", "signatures": [{"sig": "MEQCIHelv3nmu8u9WCH6MjsrXo7H2myDWvlnLMNJNNgmmgEGAiAiyU1yVNXuKAvXijVtEVAQIg6o4ZMtSwLn0MIq5b+NEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAQ7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxdA/+MSJGAgEgEFVxw0FPv+OPF/oV2iW+2Cpq2vnErvAnAhPcGV/f\r\nttmSEOpTWxyASsGvJqRHWVh0WDemZOPNKYczIKoQsiLprm9PJnf6BsHOvAdX\r\nVk64KNYRClYMb6gPsPl3B7q7sl4bkXcWZliJEnvPW7c/3u3xqTHcjPURjM7Z\r\nFKAJD4+f4vze2H2MG0h0/E29sd8TayNb6fIxlJnQKYaGT150iN6BixDyVypM\r\nLjL7u9V01BhmYV/3dRrOSlifqB8Fv2ZyqIY/LK4dMvf1dxbu9G9C/UApDJOZ\r\nqsOaf96OdhKGFDJDnFrt5+qka5h9Q+YFWdsuE+0Mw5Z6A0llkKdpPxtLtJSv\r\nP/WWust4YLm5u3ViGJBAO5ZWsZmS/8qFmGd6K1/J1Vh+MbPnYvvh6/wDKJ+b\r\n4MUgTs5vHUB7D3WU5eX6A3lQnYjSghBS6XSmm2Kz18kX/mkhCR1xDWEPJD0n\r\nFKqKT2vslN9594gd0J3PVo/fVMEFzfny26bfLRy+K2Z1uvmRWvMdhPHFd+X6\r\n0ihVzBSycSYUtmD8nUOzLmtMGY4p4xG+aqpx1t3SuBjGSRF8NXNxHCMlTcKu\r\ncOGt/2ac9ErtFIIs5rIt4x9jfLVkSJx/kZIaHMVu9zNyYcYZgx48I0vulMhZ\r\n5SyJIlkJpnoaL2jgGJZw+OA/+pq66Mo+JKQ=\r\n=kO2H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-use-layout-effect": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "920ba687cb10d47713e2533a02cae966f7e07209", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-MEJys3fMfuZGUz529RvYX7AV3XX8JIRnhJh/OFNxLAeoRTekw6+G2CBv3IN6RO28+930aYQyUkeO5MOl6f+xWA==", "signatures": [{"sig": "MEQCIHiXlhgGVjCBJwHJikzYnR1KAjKfoYz9aTTo6trhzV2CAiBR7SKpZmbNQwQUvNIHebS6Clt+lH+obhhoveNe6JpEig==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 15694, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjzA/+On3dXHNbT6RI283UoyErWybYsIwGJkCGnW9rB+MzE3Qq3yNA\r\nq6tnU6pTNLIfYx90e2e16I8TVO2xtf42Wc5WhogAC/ojd58R5SWq1IS0QhmG\r\nsa8MTvJpXxzdEFMmdDhcn8eXJeVqK0ICsSgEsOocedffDjrX+1aNs3SsNtVY\r\nSgifjDUK/QrEJEEXoJEejEE7kmNU8jvQqZ5tkAmQbJuZTPnsB5iWJGzG7vXt\r\nK/f78gjc/OO/m6GFomPHhQYQVYEwnSGWy12MuSZ/9wvltVWP3Ldg63++tSdN\r\n6TWfu9DJHiKxK3EFVH3tsqYJXiCNsAxFBreGEO5LSAo/miiVFvwPnXP0kguY\r\nb1dv+BT+cDrN7LlkKBV/Ve50bXUsKDOoKA6Fr8inPXpE2Xf7b5vbZfv89oj2\r\n3vObacZp2aguQDzO6l0BxX7dnH9SA/HezmPsr1xDESzNVFr/YzkAFY8uMjZT\r\nZJ8xhy36SKuFR5Mjn++IZfvEOUb9OPjt77LFah7sr+tU9VQSgdokjpP5EM2b\r\nL7rXJLRUFT3EOJT7d6rRgDgcnNaYiMMU4roYD6cGSlWKJxYWQNrq5He6TUw2\r\nRcAhM1rS7O7mkiWEiVxru6kpTaMyIdlf1ZjbWx/nB17T7zt8U32JRjI5Thjj\r\nEh8ZZZl7QlAECTWVm90wEcOpMUiC/gWQIo0=\r\n=43Ef\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-use-layout-effect": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4e14cc9739867ce630f69e8e4992b5f420bd346f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-x1YYKsAV6EPwzo27AMPKG9OO6knu5MZojgnQ5gpHEjAm8ZK1T81noBxxd+BTgfrY93Apkl6EaZ9LR9nQyXtsDw==", "signatures": [{"sig": "MEQCIC+WS1pifKGAzOTErVvEybveV1vMoH0uDnsF+kegAFEGAiB7bQgyJ/8qPqnfELjAGB4+OhCX+2QME9Vq9QbEH/O21w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKpxAAmq1JUAC+OhGuOyMflsO7LBRiGj8C96K4guwswZdqfoQ0y3Qi\r\nc8y2jZl5O5JCihoVKzerrgGTBraCz68gNv1rVxCti0Vr9bBdzH8ljJfH5Tns\r\nPxP1Dmyrp9vdkNtBoVClD+xbiIplRAy8+yO8HNPzzDi0cTE+sdz+WzBQy4+/\r\nnzLOpdvctV4jiBu4q08aEv8zpOcwGo4VZXTqL7aUFzDrHpLAmLBSe88w7hKV\r\nb0+7iAD8n11ud8MmAb0ctNCXlIniinIcxvMBuD6o8muDuEah86hGS1UEhsNb\r\nwxH2Fsh7FfH6l4kjgh/BHd8XKjg3kzQlNAonzcQTKXlzReWoEWjRUtm1uDG9\r\n2GeJs1GVbB0+zS/LuUHaas24UXpOdqB8qqxsVHXz8G+TV5Zodph5pIEimnc7\r\nMhkIJft/WIuqR0kIQrgbPtN24fOlFrmW+IDEfvZ9deR2dJ38NOfIkyV0iq/O\r\n+y06mXCY0MLyXC78IzmQsxMuT35lBgKMqOIKhWqhavogj2uxUweONWp8l8tv\r\nf8tKjuxldzbD/bdeDr59iLVZuLbSesIBGqKmEmYcvHQQlln+3g86wzYTLR1y\r\nKZMXkztrCXWpb1kNTpF6wf9x8YHlcSAiO7FkT0Vzify8IPijBx39HlSYQn2F\r\nBHmZinPsZbZ9VXX22dlcCM8PDGXsnPeartk=\r\n=CHXL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-use-layout-effect": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13971aecd51cfd81ffad1c1fdea1a1334eb2c28b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-qW1syHqCf+XSnQYu9W2TxYc9+b8DozCxSVPAMstCgxcvdSmXMwKmOUvOM/HQXzV9ShQz9kXVho2r4eQpVmwvoA==", "signatures": [{"sig": "MEQCIBkBczFgNJoEAtqzx1mqTYoUjsMFemHxVWj+Vx7M5dkWAiB9KmtukJPpIUHqx/V1L8BFfqPqplKJM0HeQykNZRWE+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiIBAAlF23SSjmkCAA9mvNQ76mtjFi+UeISSR74r8rO8X17+zyHUAg\r\njB4A0Mu45NrLPUPqoxlqhf0ri0YUxya8/cegcLsrcewYPfpJV38yC+elZ/Rc\r\ntlZW/nJ3YSmGoQxLtn680U5zLADGlu+evMFTL968b4x+MxafaWrCMzr2HKkJ\r\ne7pbz35EjI5uQSFX5qO13AUiObxgnt0NQyoqhbM+keTwm5D7q3J0BaW3WVc9\r\nQ4Hh7n8/yddNCzvP0BZCWDLHSwtzJiDQy2gdEX/pQWWdg4CPCg5ewxtrroOu\r\nDzwZwOjnrLFMquIyn/PgVzMCE+Arf5Vk22GXfk+QCKuqb8Ye8qrimXB0VBry\r\n1rD4Bj5t48H9sC0Y52wwQv8LYejjmB/rDp0IFqGyHBDuJLuKFe+TflJmWm8M\r\nsPgeay8eEi6gmmFim5gDlVuUM7Cgsumn2GbUmIOGH1vDcI0fRmLu7DWgVb5t\r\nIl5d1tNcO50n7bTa2zVkrS1fEsUFvJvm7iAr0ioP5dx2HfZFvSoYw/k8JaLN\r\nDyex+ZDwQ2r57q6z8uv8Ujx+3AlrIzXmhhwvhAb5bUnuo2etqR7Fvn4bIlQz\r\nWIDLmryQcMZrNK0K+f0nLEZcCqY8U7Yjk/7KXurgFhTmZNpqsVcg9xJcxcEa\r\nLwVq+9KzIi1vZBT9uIjlMThpzUGeNcd24KI=\r\n=frCA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-use-layout-effect": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6110afbd795dd4413ea01f03819a627aef6a5d77", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-<PERSON><PERSON>jk2leZrGnR9j1bX8OXJnpbHMxLbs3LP6MqwkOGI5ZUzIvMY0QmbsUky4NZfR9DLtX+BTqPwoOMu+Gg6Rtxlg==", "signatures": [{"sig": "MEQCIEGsLwqxXxy0K4mZnkNw1H24vZ+eiNDTC2vQm/q0GDKvAiAsPYEDbh6fRH3NAbKYPHOzj+/SmQpV49V2/YWl3jJPDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQ5A/7BdzWjRXk+gn1zM7VSqqA6uu5vN/FedqEMX1bJwRgdkQVOXpC\r\nHE2Au++KvLDeFZdOQW5wETMP7s5lUhg3NMCII9ilKZTpyHM5BG2kQRXsEmNE\r\nlUqNJ7zWZfiH34qBqwPTs2FmC2aDHj19EGWpBxR6OWbNmFaNFWtn5sdZgE28\r\n3o8aoccF0uudSiOnCjbwdduYv2AmV8JyRL0FnoI4ZbQReRMv6SrvwPQW50f2\r\nKJFFvcSyLwFcXIQedDAaywTZej6c09LUNq2C31svI+Zo/Qag+h4Fm9+UOVir\r\ngfZTWKiHIh9Kje0hr3Q9BMTOfboYz6RGBLWqAevKr36lskSO3C+2aCA1Oq3Z\r\nErB5usJ9RHkIXlIBTvHC7mas+QQw1V+uj/tEfW8su/hSigFqTmbyQ0qbRU0q\r\npunTCoT5LlaZT85KGrVyAWi1cpCAipt4y26XOeEFfr5Wx+4DUBldirgDwzQI\r\nF+VBiosryJ9xsUe1xEwEJ+n4xlmul/XsFfhUs1AOuEZtrdhzkBPKLL40QEjh\r\nmiRz35R9URDNPAf8V834y8IOdeN5SwEDGH9dk2qzEHs5Aah/k+kNNVMgDap3\r\ndlMVtNROrTGQm67LgiwxsFK8FzcqfFCcdSGLx5QN4ToEXX6HmsFPqzaZVj+U\r\nLFZvAffD1CmW4wXPjoaufYX8I+6SSfCU6A8=\r\n=LDlM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-use-layout-effect": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e75a180b2a1520f64a5c3068ec39d21e8ad495c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-qCvvDTrZpHrfbb/qaDT9wCul2cL3sQa+KDTBGSd53YAwGxaLpebpZIBKMRRCRkE/r+1yBs9U8MVZfB6f755ATw==", "signatures": [{"sig": "MEUCIQCX57ZxlsveWcgoAKYU0oMEozrpDCKqr2nqHGrSIoJKgwIgBOdWRBzfYcYnVCPsHXr7O13Gh3ZrP46dulcFbW40A7s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8x8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomQA/+LJsNUW+3DKjZIK2+QY3VHUkkcv/1rY/Fq7ECD5iewfXuTlyc\r\n/7u+36iMOUdw7qEQ3npCFfsDdX/G3IIWLpZya1bz1UGiQDj2uU0B6seqalIH\r\njC70zt4IfnDxbok/3PzE6546ESN1Ti0YGeMoZYo+LyG8sAk4/PUNe5LwSfL0\r\nvs5znNqJvCO6zOEn+eBp3Fr9KvHafZ9X8FTTaYRd1yAKhvd7+oCe52xq2IIt\r\nDRC/xiZVMuL8j+6OQb2vwbdbHr3Zr8Z/xrwAOstUVkkDQztKndkgr2Ff8AZM\r\nxV8GNx9ZwjSQd2eUiMvNVY9VEQxV2R3D+hcGX11jdjRLLFcjrOgx80dKps3K\r\nw1LbEFSlf7rxBq4/ZLl2v2IZrpaR4kgiv6BN1H5fWNgsgz2ctWSDzyapegZW\r\nWfZK1+RBPJvhWaNR9k0CrFaU6Mxwy/0Xk7ZyfhAyCO5ytB5cx0RBFT8vWMGu\r\n+vj4V3AWw9WnxOUXdfwC674UhUidOXsJhuS59bpMffynYg9IFgWk+SdpsWW+\r\npMwC4QMQYZCWJ6G9AMKDQjUD5z03OMv+QKjBKXrLA5yriEbeeu5VlVMhq9Wl\r\n6oppnO+0npNCriOssILewRCNt+uU945N3f4VDXmNUl9K9hIRUs3aq49k/+MT\r\nxvDMcDLShLXVlEZpV6aZEHf3NQLXmVmFmME=\r\n=gGWJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-use-layout-effect": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3810d26556f010301f18cc5c6b7dd4375c6cc595", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-cMnxNPeoXC8ECNSK/+sucx/zOfS44KkMhOlFy4ghFLPC41nx5WGxAhh/OcTEZxJUZQK98aMnW9ggwooLTJhDrQ==", "signatures": [{"sig": "MEUCIHP4BgYQJgp27TXVi1qq3UToiHGgX6LcpxEXACX78qbuAiEAhwiqw3xWaKkLMATBC2Jku00So1BKNzSLhAGR+cgY6o8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo1Vg//eUYX6xnh5zmYeU2qQvwbCSsF3af8ogTo/jZZQsoEOI8WGHVA\r\nQLUE6n+9JMt3n43+4QYhqlGscGsslhEiPSxWJZVd7oDjxkdcL7mzDGkXlm0l\r\nVjMeBomx0+2T4dCVUIrL9Cxj+UIKqWgGlucz/9v3K7B8bUzWFoRfXOwbGlq5\r\ni3VxN5lagZt5jLZgZhME8XSRPLbeRXXwYyNjB/mB141gM4RpwC5qxAGCs11B\r\n2+9jkUV81bMjhtOLHdIAYEswNIPkCOOAhYidL9KZdrt8QvaF0Vf8s2Z/cTPK\r\nlns416xyrtV+rrXpVlmn9P89k511Rgfc128hbkA9v+BANfQuMzNvAJgHyt3L\r\nUgXiCbdp7BLZTOQD5l7sxRKwdsYJB7oUbrMlWuZSbUECMTDTn+9mYem/c63r\r\nNmIiuFUW81/ayElextsUTuhMi1CYXZo9d9rXj6bH8aVYAHkXsoy00WIx03hZ\r\nqHf9N5a+Mnm8yPqK76GSflvFlUTR2e4O2mhUYOkOi+VkHjfN0f/2TVt1woZV\r\n5qNOpa4pWijVX2iDKAZuLqUJHFm2IXp1sbOpCuTelV52JVf6JpZN5lWDI91E\r\nBj9pOtQXbsM4nnL2wu8i8uODs3Ivs9YK12RtZMTzWaNEcLnFaovWwSQxGkkn\r\nhDCvevPiOZL28RW15/p9SQLLfLTfvKzp5no=\r\n=0+eW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-use-layout-effect": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e7d5f71510c8acd3d44dbacfb18fe4b62edaee35", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-3WHj8WM+KVDx5PIYjWnHf1blt6FGU2zsLsfvXQqfgfJourmR8AutdsqvbzlXDSjaEh+ESVLD9RzOHZt1Oqf0RA==", "signatures": [{"sig": "MEYCIQCcoBuFUh+D5M5MBKuaAWYKjzWZprc0o81kvoTNH5NGgwIhAK6NEfXbQKWZudjUACnnRzUlvfXjM8GelgbKeX9FgWmA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoULw//cuoYQqyPs1SEEINO2PNHyV2kKsCE+C6C1BtXBuwSZCpUcBC3\r\n1s1hPN+ccGkdMlaggy/S7Bup7ZWAOdrmaY0yQ+KjlX9YukRLlWRYHhVdSTsE\r\nODrf0tFWAdN+SGVt1nwIjTAcBh06p07A2ziTg+fMmvGZhQqHu8Yr4ncbfKbP\r\n3e637iP3z4rXpEQkk1d5++u6/o4NYXuv1VAOM3ZBG4IhIcNjOrHyxsT2dSEQ\r\nIhJAlQVlcFPdGEXMjcxRBYwVhg7aB6fkWGthshh+HnSSjb2hWEQAhvkBwNpL\r\nDZS6wIQ2FwVxSpMjcVcYwuQASq8pd56idan/X/WTk3s43ifWP9uR6KJJX4W4\r\n2ZWb95byhFVqkV9HEmPZCUK8pWKVl4KA+9u99VQutQYDzX+Tv+N1CKxK0+OP\r\n7BYjWI4Bp6HLzJer51lF8Jppt/RYkNl6sV67SkmTM/dG7GHQgJ2CM/oxrLIs\r\npF10gsEjasnkgnzS4yfRnEX+ee4Wafz033ITht4bTZ1FienQB7I+O6t0GdFI\r\n+JWXmbuYd3wSZby/HEaSDSNz7A2BsbJRCeqdwNnjJ6jaDvLrxkGxR/4HNlFv\r\n8Lhz4BX5VU3D/91SY1hRla/Qrmwkvkf6sRElk39dPq8EQyn86HC56gYA9a7s\r\nx/L45aQefkGwGMYxP8rvhrabu1R8rbWnDp0=\r\n=PGt7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-use-layout-effect": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6254f49365bd511c47b4b36e8787ac0e12c454b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-xFjOyok7xRNAIBsWF4JzBeYqbh1AoPqQEYF+Feuq3gJsgGHXZqhaOfzXWK96PTKXfnKhiQy6n94o1wkjCQrjRw==", "signatures": [{"sig": "MEYCIQCnrMQMmLKv1ug8GIb5kr51nqXg9ZzhrurYazoDLwjqYgIhAPoM86a+G5s8HW+o/3QMMwTKjGsCL7NjNJXd0JeLnGki", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24150, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNh2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4BQ//fmr+0M81nFWIooIEs1nofv+7MBxNu5YSFx8PV7uDWS6Xe8AB\r\nF4VUjPJJUs4lgop+OobQlYvAetQ8pLzJp90XWR8ZCOYi8yiaUrauVKuM9mQU\r\nTOnFDiy80z/P8ZwR8MDrRB8MYSBSYO8vOag5n1io+olPncjbr//7xdjVP+At\r\nteyeUdn/Zy5f/5UJnLKhGBrKfXNyI117BzytF6ojPoubIUccJVr+/bjpHEr6\r\nsjqZQbkZIX+b5iWLKaDNLR8cjOjoUiiw0MRrDazS5o40CWNdx2gohFy16W4g\r\nwwNfyKdsfh4auLXZrnmwib4SFmBIKNkmj6G/r9am0h4mGXSX23NHm+crzfIV\r\nEjoms//gqNHVuVE5OaaP1fX9zG2cLh50o38v1bQACyFjcRgRkdGYfOHOeeoB\r\nsS1tveK5fX/vbwCDxE3j9od2bv/ln5DzoEKYGGXOLcL7vO34z0AvrL6NhOXE\r\nXMr9E6Z36If1vG0U/nA2fA2GQAIk17MAxbxloWz6Qcqu42OA8REIfVQjakfz\r\nJlFYFpX7w5uS5ificnp0bCXQbnqzD1lQetHXJfbR+TCef7GI9XZEZCw50dl0\r\nOWtufpBVPY05QWqj/SBwSTTon4XvEfSmonIiS/XORKN9O4d6q87GABphfK9t\r\nlR9Vc/MIe+kmBie2plncsqEUBI/7f7kMPbE=\r\n=w3cc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-use-layout-effect": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b8533a08a3525d849e26c69a0b78f09c309f7006", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-zk48Nm/4HC7YK5RZA8xRh1PZzPxGo5onFSbm0lhzCApcjyp7CKv/SAumXTabjqBMP9nNRZJk5l7ey31HvA7V3A==", "signatures": [{"sig": "MEQCIFBGN2P6K19aFuQRLIBZTxr0otk9yY7rxpjt7+q3vp2nAiB7sYHx3fs7snB3TQ+7gShczjZgK4zo18fMD2cR6+nsDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrH+w/5AYZN7LUiUKekQdjrToFgZejv+s5CchStfidVerj6F+aEeD5e\r\nLei/cSfBYOMkGqkE8oRcpf0UvRYyPfVif0zO8Yb4DxtAwAqfb7j3yuxcdbCh\r\nufPnGiB6pMW1JGI520AcewuKJ/hYyuhaQFSnrCOAFaRLosdzxMrbY/x+6r+G\r\nsNXcrKaS+TjigfDW2xx5gCRaZl89mKDGsI/C6r37uKdVKDPrHN9YQxcFV4wQ\r\n6XENyJSgjwk/EantyaKTpr6YRTgmdOvoRPVm15kOjd8cCtauJiRO9D6gzKnJ\r\naRoVzzV4frkK0TB7HVQmHIsJlk/WT+OvGmMxjDZIyREHWuac1OOebS1UvkvB\r\nvDwTm6m3Ddg+K8tVLKLk4FPT3k5xAdR/zQz7LKw0iha91/uVyw6r0Qn1+XYh\r\nMqWqJscU/p2cwbLdgckdLR88l58MBgDh/VAXb/eOQHmqdbXPpopHXzIaAHr8\r\ngZr7spA15FlCOXgqiq1ao3XzerTmFPPdqWGPOojv0cJSkkjNqXp344FPSeba\r\nWtbsfT/LdQDQ1bqpjiZhUYws9p4+qWix7d4MwroGdCbH7UX+LvXS4HR6SVpL\r\nx1cWMVMZbMvJeiZdStNOC25M0d4vPiCH7ycMvab4qXuwXf1NiQZguYIRCVXa\r\nQKsT+xIHTuuzjJIfpEuJv8cW0bla3Y3geOo=\r\n=hWyV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-use-layout-effect": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dee14779cee3fb5097cf7fc108186d747baa3664", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-GibPHvAtX9KkK7X4vL1lYLbB2mvcVL1iE1z6HiTRcWtMKPENthZ4/RcqH68/UtethJVgXeNrDFlyVkaTTCIr2g==", "signatures": [{"sig": "MEUCIAvOzqJKn5L2BEJmLRTez56IvXSEqVuyMO3bJL8aDNSgAiEA6CoeJIjNEUsdFnQYp5gEbEt7tcFquMJtGmRimRwrpKU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSlYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNHRAAg3EceKXdx8ZREoeQ7l8r9q86a+G1QzrV2oDMTYXw4vCW2OJF\r\nUPNkormX6oxNfz6TakH4I05bHh/1koBSeQOtqNlhQHSyuKhxjuba6uiMAT1M\r\ndAbuq1t4vFaQp91j+x1XlqWKrAmx1uXOy4CtuhQvEZSs8HUdwfu2nIh5R8JO\r\nb9yt7SzSM6DPqjitzOZVUPNIBa7V0u1691ewdpKI00gXstf99oMOBv0OJ/yz\r\nc27kQerhvYC0nGujabJZOHatQVjWXBPuU7GBg3TmDXOpFgDkyBnq/a5gAj/W\r\njP9xu0QIAn2kfjksX1FxErjCjgOc56JeE0y+xe55LVao2LYRm2lYnRg1+scN\r\nhjXaMLSSti6Wx4m9iQ2ao0baBTmkS0mUe3fCZG0zuwPrYc75wSGU8J1uuGre\r\njFmuhKRDthOL+Y4jxwaTJ69GUHIdLBs5b2XHZrG5EkDGSRSc3n5a54CwOXIF\r\nRfCRjcuEWG1ugPPpVNVAA1L3z11il1IP80c6ydqR5KUuNff+Lw8+tW/TSTdZ\r\nZtWgZ6z3bcoibU347HXUJuhHeXN7wJeTZ5xsyPTdnA4zuhCqI7+g9g0Ca3gW\r\nGxuOiV48VZgc+a/roZfAHNd42qhOF98vnPDgsHZ0QHwEQkC4R3gWiPmrSD7y\r\n8MxK6ZtbEfKImH0q/KSobSVarNhhoQgI6uY=\r\n=1U3s\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-use-layout-effect": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "887fa84a7eb35862fa16473cbecfbfd6a8c552e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-97VAUnIIASRsovQVNQ2rYUlG+GR1DkIcSio0N17Kp7OAt0kzEWV1wTcJ2M+urNz8s5i7B5PXnwuEwbilVdewsg==", "signatures": [{"sig": "MEUCIH2GXJfi/FMPipW8Yv7++OCpZUoudR0mo1b1fPGX+w6DAiEAgzEbSBt0ndarNhKK5tkD36tQihFh8RvI1lEmjRnN25I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOYg//bZtmVJGXP/5Dj5zuKcJeJMzqudKfBIhAfEBJgkBSIoNHNXc4\r\nDKulnCmA8kTFafhJ3VY5wOs/OrVKpGG3jRCAYaJrYP70dcY7LzOTOWWhbxBC\r\nx9wJxW21SIvPiy7WXM8qPkX0A6lqtnZv+Wsev9VVParnoVhA8m9KqMbzfI2v\r\nm2celdHx/bkZFV9wtM1i0WyoAZq0fl0rXvdxB4KYpWDPefDzvCthjA2N+H8a\r\nvM1BUc+6sPrHEOo0d/InfUSegfie1bAcMMjFJdxMEPUMz8Z8Syb43P+1KDOy\r\nEKkX8+LF0IdV3eWkfaYVrvCI7OkIFAgDQS5kSI+13s1U5asAk7f156vIGZfP\r\nSRSxQiwzKvSRRl0WXgvvKaYUZKobTQFctysqm/MdlFkIgesVCvLWacKNxC9f\r\npbPgHpvi7L+ibLWw5yENck5JgB+mas0QAIJ4Yb3Kux2yUgnWAl4RYSTkuCy/\r\nkeNpJndtA2k+omOVggYBR9fx5cdF5DFiDJkCQyi7oqyxQK95eJc9xYfEL59/\r\no+4CiySDOatGpUr4Kp1WulH9YlLRMJPVVicsxaJmVRTw3+QPMNSjFtzi6xjg\r\nretBw7FiJhDLAkG/M2XxOf+KY/H0oGJaQaz03tp2QBr0mwzgsHw2EBv6oVjn\r\n0gdUS3P3DuH63nDNKnrQhAsfQ5NYVjMtcDQ=\r\n=JeB9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-use-layout-effect": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cd8a0cdac370da97400ff1b7266713088b3b83b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-/CrzrrZh+vMDH05iE62NJumNcBvLrs0p4NUwUP9/R8QVho0SP2LG/bzBXHjQ1r628IsFrWz9O9pkQ5zvgBhQig==", "signatures": [{"sig": "MEUCIDMKSrqUmGzOFmPVJ+L68U6neEfglHkH8M7JZHh4RUiCAiEAjPc1wB4GZA8uXtvlaSF/aGtlJH+XUgr5x2p2VRgQHow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1Mw/7BzRtZLYZPsT/d2Ts7XLO0XpFQBOrUKeH58lPZRdCwdbe6eDv\r\nTCVka9vdRBUuATkXQ6W2yl8jF0uu48jhJWWBK04h8vOMeoIMHJ1Mk67UJh7q\r\nF6RPWHx+NdKI4rgEsPYE/KbiGPNd5Ns419+6zp3s7FL93o73p3jo7AiTstzz\r\n1YRht8j2okIgV5s43ASzs8rQh9SYvG3ZuQl4ihQa8pubttQFV+eXGh6F/dy2\r\nh9dhfEfZ5VjwogUSmG5EbtLxJdM/R+e26DL7VVZt14vjW+4nfPcsilLVW3F6\r\nkBv9XPEs9PICVhvEgRlUTZaKJopoGOMy+hFk7PQqWwgKLznMG7kIeloGgNWR\r\n4mehtKx83EBW5SoDKcKTrpY+SHqkuVL89p7q42G+WOZSLevrfCNr7Gz+VSVa\r\n9/fsaYswMDmrEsM6keWarnsR3K2iQYrCqhZT9oCx8E0bIwr4V6Xo1oFsCH49\r\n4CLQsFiOjr7FYZQtxG0yCwaYA463KflVKn8euWlkbu9hqU0QR/2Qn80oL7wu\r\nYlfYqtDncSrEg1ZF5V0mzcxxj7S1uLL3gTDmGOZ0xAoIA2D8reUabe+6iWj6\r\n3G1hxLZPrb+4QRaIg5Ap0CFdHcmZRmzQt45TF/HJwbfhu3Alj1V4qs3uGmfy\r\nK1KYb0L2qGr80UvOGOaOdish+0+62Y2rdns=\r\n=QgO5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-use-layout-effect": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d8cbf1b5503da796dde75a20914f31f92f8ca89", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-MNT6EGa9KxhJfWyrKIC/+D0fvBwaIPM/vNAOTDb53UA0N8Oax59XdrPCV6af24yCIOwqybjHtgl7jiBsGUPX1w==", "signatures": [{"sig": "MEUCICS28XnCe8+dWNXF5/uXkYVnA26W4F4X61T+dnYNRCzWAiEAv2Jhc+XbK3nxx/lFWZ/EsSDOfuPN+nhpvb3ka4HByDE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8psACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2hQ//fheT0LEG0muxRuG+O4klTs+2hAMHnrbeL9P8bF1bEx4xBW0k\r\nkUvBag+V4se/LIGmetN8F0TnTSboiaUyf/Mf1NETsOCD7GiBWHqhDA05lT6q\r\nIctOc4Pc+LJCwTg3/+20S2IXrs6nPzeuiD/LJAMf6gYaZ5toCbvi1isu14mC\r\nCdvKH/M8W26tfTWXhAYJEj3VOg6AhCom4XQNHJGD1WeD6QIeIyVI2UZN7hBw\r\nignzK55XEQIm/1P0vifOMlOphPtXBtJxTdAKRgzprGYJzAJAfveYiSe+Hf/I\r\ndaJjYP767kiMCJ2qNHk0H8TLU9m0NgH7Jd4rCPFJBTQkQO6+0GEw4g6wVlKB\r\n/GDEpe50cDQmJO2XoWN9E7HLu07piFS2sHsSVwC2USWAUioNCZME7x17pN5g\r\nokAwC8xvSTJdFjYiPvnn2/XQy4K0j95NypxIDbGBohJjSD1XU6ClD/3WLKry\r\nA1tmZWPRrVrC3OhmuYYLtOUz0qJglACFmpA3MVrPLNidxGBIaQrkfuPsOXWt\r\nQkpSru8LXJTgoNoe5COdgYvIObcpz/llu/y4jRbCaszb+/iUwU6x875niPU0\r\nXBW+LsBNLKMbQuG+CHYyG58kpgveR1nsAsGpByxHyfGs/L7ie43+NOxb88tk\r\nzywYmbXOUsgZssky0+Kvgxfmthy1jZxYPMw=\r\n=WQC6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-use-layout-effect": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "013628b25c97f5109a8b834ccab7a9e985038292", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-aZ3jVNbOYvRDOPlSO4M7NpriE3g3+wDywvcOCmHk5kfFBAu5rlCSBxnB8qmS+wPXdXCy3aHPJYm9SV1OZrJeYg==", "signatures": [{"sig": "MEQCICLOp95q573QmWbK/PEbsKIA3tUv59hrr9WYMhKuQpsjAiAA9X7c/smhuK2Bp3FTJ8N73xO4mgMdh1oQN2qHll7I9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0iACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpzUA/5AejUQiPZT9yekQ7Q+/UFcX3lNSjAs2ispGG9nWCclzNM1maW\r\n+XasSRXbjpxOJf0CNBRxovGGuDgs5RDReojc3ZmbA1H4yxTsqryFh9Nogv+5\r\niowfs8nIyreoj/WKFxwoquyB6fLNjmFXVZbgRcqTyOtA1OO8YuNoaw2XnlT6\r\n/UKpf7CSEBvYJvWLD//Stb/W0K02VM79pz6Xh3wfXEHkceeFO0AGykY1pxQn\r\n6CeHO9yw6oHTNLlwfX3wEdiROnpzTxgvQukaDR53hz4SlVj1v8/Ff0M5prVs\r\napFY4b30DYMC8ob+pcBRtzbqPokNOhhqJC5slr9p1QPfqgWyrWtC528y06zU\r\nYwC4T82jNlMYBkeYOIEBuvtGAWGenEuxpijfDYT9fJZFvAVuDQYcpeV/VzsP\r\n7jGIjf4LV0rdU7AvM1hP/MYS7wFQXixPOWvkVDfDUAZgV6KbOPPO08XYVoYk\r\neOqb/N2yXVbEIEWANjjuIywZn28gqJp4enJu0GwQKHQ27G4naorR2fJSanbp\r\nC9Ols+7yR3dRWRQ59rCxu3hqBmHp6QbiEVl9nZzfMMMTh+39VfB86/E4IGC6\r\n5Qeec+2QpQ6d9SqDRPorvgK60MXHRkWzWMb/2qEQsfL5HYcVY9gRSdws9tWX\r\nyE+gdlAOztB9nXtp4DMTDon2aoR0wuU2ZSQ=\r\n=8/sa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-use-layout-effect": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "93fde80d8f8ad3af7ef47ba88a560f2d2c5feb89", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-YdNeSwLlP8GHlu0AhNshTKIRXE2Wa7I0M8NJNgWbBBOcIsKJAXjyB4K6xXDA4b1MFrT3yikWiwJFuYtjznl8xg==", "signatures": [{"sig": "MEYCIQC1hqmHITWDU7cCbXWRH1S5h0EHEyGpFiLFNRrvmhzJmgIhAITQDJ1RX0LyI9q64oIwfxgCcVxIU8UfyV4xM+fp6rTR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmogrxAAiy6vg6TBn2rnvDzaIzvDbXx22hKZbNZc9M6SRsdIDyUIvOkX\r\nR4GuguQUByi4kZFyQboD86oLAjp3fXIRihv0HeuHhv9V8GyRPKHvTgrVENOP\r\nzDU5WvLptRpRwgP42SOXSUZt7qDHZHH8A1a2vgiyxiYXRNz6W5xQmoSEtlRu\r\nz4D2MK4Y6pd1Yd5JYJtqQrUCcXykISFeMvY/wrwKWOTfioC3/wuCuOBq97zk\r\n7pUr+BNDaZSmJwQzMMNsAGotkn+GxRlibQM4A9U6bQExPgZrd2ZCZ4kExWrf\r\nmZxkz+1txOKiV/h3iHMDXCotr3/HqhIniyYG9uRVPDkTyeQjn8uiQS7T6BD/\r\nxiW27Kwgcfo8CrKyb7h38bV6XlN5oVajzfw/UyvFlPo6f5vU8haDXIp+HwtC\r\nEaXBMIdUVvIKxdErF552kXDAITEfNe73Cye+wvvCyRrR3gLQVgULxK4da6wO\r\nD5Gq8VQl++ScPDw3jKsI+PwP7fZHXM7917NN4wNsFHAsigoU2sBclFbL/9yw\r\ns11/d/Vm/LXmgIGMxz8rN1W/rgzE8Z/jXr3S4V+7+H86RwEOXC8cIiUKuKWv\r\nFt3uhDZh3U98/hTIWF3oB8MAvgmQNNrQf7z+OiSrbdnRP8LaQv8CqCOs6qh2\r\nkVxsVrLgjwYV6Aq9f2fNbj3BsB7Hw2RAGrs=\r\n=ZFci\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-use-layout-effect": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "20bb9a5bb675bb59f5fae567cba13c0ec980eb9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-uC1iCvpp62idC+N0ors0nBMVbw55ET+JMHB+V3ZbeDSov8qp6mMLkV3cDyArs4uh3elVmJy5kwmboKGz8ni+JA==", "signatures": [{"sig": "MEUCIQDIbMKzfSyj7BDCIoiaquhsh1VCVoXg8C12A8IUqwkxsAIgSuWnKqV+Ykoa5QoxLULj1nDyqEJ0rlcAav7szTMs6zI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpU5Q/8Ds1aEvFtjY25jIv+w+CSGyW5BdohHAkkC4CtAyUHfU9gijnv\r\nnPz3ICi59O0n8Aac9XUsNj7RP+gCacgS2GCIdvpoecjSLvMz2keHRnE8n8zx\r\nYN9+nRumFEc5xbi4tUNN632SGyr7Q14W+hC/LszR9yblvcEdTBXSMmV/DrQm\r\nZvrpQurdVkipYa/OZ5jOH76zcyUEF+tnsc9U7Wu/i3epXufI8UVhBwHmqn3v\r\n4pwVUr5ZjIR0PoZYXY2PeWNDyMCvlQf2AFGjzxz+Mi8WRZQNRABsbBvDl2K9\r\nV8v9lv2et03F6nQZzOLlR1O1t9XlbBdGZIZ2MS/aXb/a10clxwSLc5w97JN7\r\nEMbnT0TjBH8pdGgxAuYwVRhzXolZnJx9qpO4+Zn/OGefRdgLe/y7+P5m77B2\r\nI9BfC5UDA4DdCyDm0vfcNM75Tsuh4rVs078Farde39rGst9hv2BGGI3HDyzf\r\nGORF2rg1ed/yWlf84IQOIuio0qBugch0cr+LCoPMh5fZc0QeSEZ1TzZtmKJy\r\ndaOo40qI3pQhrn51jCbsXTMx0EOdaKmp5XlZy+bXCKqQyCukyYMlIY6xE+nT\r\ncfja/XK54Oggx3z/u76SCwzubYIjUK4xSJDxZqd4vb3P/sHTmIccibyJi0By\r\nNwOP0aTDsA++igUnPdgnCtmvuhLmZZB2ISY=\r\n=Kjcm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-use-layout-effect": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8ff156abc2d4a4cc43f7fb95605edf677dc4983f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-DlmS8wDoI3hFrLLALD44l0O8UI10S3e220RcIADxwhhfSg4mwKOznKMdeZLUW/Hmqcmhqwez7Y3u4ktClT82QQ==", "signatures": [{"sig": "MEUCIFrBIg0Ui0wi355furxlJQy94Nqrs6Z82l13EmUqKEURAiEA/Em+E61V4rRsiyfoAwiJjq55QnYCX73cuLO/0qBbHwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqiYg//cpKIN21d/9CNNoSvaXZscHnjDVB0EeHIN0G++HtMwXgpB/qL\r\nJoX/uvXxTb7w/Qrgo8DOGPie2JUVx9aALyBb7hjI6AlZyU8WvVGh/nxXqCPu\r\n5FLC/S441LXhkwycGzJVJkQ5gEHHtpTaHSzqWWxWJWwAzUeMrRAZAvbUbO9l\r\nJZPRk5lVCZhB+PrYZl1/lcdeIHRbzmIPL3BBzPq5n2lKhKatJVUpsX5pPDym\r\nGEjUEO/Q2Wf7wKVDj7eJreDrx9FgvM0wnRZULxwr7LFfa8m7qn2WseZWWSnS\r\n/ode4QBZFHKVC+VFtP+JIvbCru3n/55KsqHfusyTYOODF+RhZNrGc0A25EfK\r\n9dMwWDgvr9AqRDaE4hWIoPX+F9o5bDb+YxEYHUdAc/dl0s0txr/7yOIf1EVL\r\nFQtNkPn9qGwj8vtmLG7qZ1jmeQ/WNalWSH2Bp5yt+cj1P1XPuaZQPZLNSVH5\r\nUrXs1epBG3uuNozsfDLjUaE7uoS1qCiyqddM6l87BfnVdCvrjvTHkttlVDLJ\r\nu12zsPCUtdh2/5nrISrOOpL1KkSWuJhsEM5cmzoTSGOHxZ3bWvUH04xTR9/m\r\nhR6Sit7dYXPraDUwrFNHcuzScnGy6LH16fYlfbM4S36aJQNUWiQV7MxkCJFa\r\nLGns2QQCD+uLzcwWUXWojrlp2sbAa7WLALQ=\r\n=JrxX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-use-layout-effect": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff7e03cde5e1f007756768cf377de8e5c1508e33", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-px6JW+lWlPO1vch0HEo6IWRy+qVEQKj/V9Dg7aJp9tdGUzYnc23LsY4OOjAG5FNKjB9VNzBd18xAqbPOK/WitA==", "signatures": [{"sig": "MEUCIQDg90POKp6Got0KMhCIfxizUYZOdJ8FuqHqlrVrCGPRIwIgMMKenXar4RAsX/FiQSOcPx2U6iHcDimDlJVkn4wqWrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqTpA//VChTFc4FXQYZVwbJT9Uu25Vxy4/JrIdqCh5QdpWGhVsIWu7O\r\n7gOBzbVNB9leeo/Weqkgogts4mBNGwZvVQDLTtWrrK+pCq2ttq4aG+WVmPKq\r\nPLNarCo86fT4aQ9U31OJdR2+SHPIZRqtSWBDCHvlZ2fjJdPX0R0bdpHwnfr/\r\n9d9ls4Bo6fj5i/D2G3FZgCwcBR9hXffvse2mt1mcVLQTb40NCg0BBlotLKZu\r\nqZf7YG2CV6ugWINYVv4O7AjSzhWnzYoWg/f/88MPodeE2DC5uX1hfTMrX1Vz\r\n9jS3WIQTZVARJW/Q/3mBkdJb3e/qot5eNqDboR3Lk06r59Hh/5Ceam6awdsw\r\n43Us5Q3CJGbkWPwIdmmLMOwgfE7Vc1DRrWDndXdLgyqPMT2s//UGLWVb4AX5\r\nr7uPS7xKQ8o8AhTAAWFxvYmbaAMDjPa4v4lBk1C26Iffq7q4gtfW4FNVE+TM\r\nL5EE/29qPl48hP32NFqdwwIrzip7Lc+QSl0ByqRoGj6Z8uO90UN6laV/olWB\r\nEupwPMSGPx/EnEGo7m4Ujc5/kv9ULNuxFInQCYwJ2kStAfH26JtvPA1g1dy+\r\nNVaDfnWaPTrOT6bIB/UhhFJrDrK42II8SooYVnVqAGNHU0YkjHPgxTjdOGIw\r\nRThTK7ceHmU8zKZ7SwCYXdPSuyEjet4ZKTg=\r\n=9Rm/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-use-layout-effect": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4c258b2760d9f89bdbde6cfa290e7e56b242681c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-8ATPgYKewtzHQivco1K3ueGrOKO5zXRX4Pqtw1t/69HOLPEzjvT+cGiu9Lx0RdWVgtzswmFBgPyENWJ4dtXdzA==", "signatures": [{"sig": "MEYCIQDux85ne1UZgzARNqm2jF2pfNlYF08Ei+UBrBjPauZ2SQIhAM19Bc6UMgTZcaRu+EZ79Av5vHKuv588Y98lfPPxxx4D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq8zA/+M/Tsqia6dQfRBkF9XsCwnsDL0sFNhJtcljssnrl0p+ZuTcwo\r\nUcj7BIihDvcNLOzUQgTUHLVVRgBnrFjyEe1a9d6cBh3DT+uCS9rfizA7iR3l\r\nOXcBmPJxJSxsZFt61LnV8zsp9YqNSjSNLKtlwgD/7OypO6jIfFwiylzEB4uW\r\nRPrGOr7p+UufW8Gok3MDsw35V+FfK6fs7ng3zX2UYqiN8NImDe7qiNRrfdui\r\nxM27bo2nVzSHRe47/qGiaaVxIyD4miBukYfGnz7fnFytQdi/vGOCUxuK+uVj\r\nsDr/U1fmpkdTrmw4kUItwzLr1apfgNkD3NOUwTam3fQmjMaGbEpzuqc6sezZ\r\nw+x+Y4QgLHi1Du++xdYFD8z3Bd54BOkX5/MDj/GvwU/0tQp796hjpVD4TEKx\r\nsnn70CdUm3yjSWcS94cUKI8/pc/4DxJjCHPnJebyaAlFtn1+g2Il80kZFgdX\r\n4tdFZKF7dU9z5qIHOAHHBqtVJGQ59MgtC9YhxIYG7xQGL6/DEhcBA0jwhjxs\r\nhOCOHxcBenEUTFeiVcQDaLbkids6IPLUw8D87sYxBSfdc5ByjYMgFjTJMNts\r\naWAwwPgG/BMDAtASN8K1mkVtBxp/vLyEJhXTkBcS+SCvp4jqOWcgr80fHQdz\r\nxUFcPQR4jqIqExb9xeqocTD2CbZmhcztlcs=\r\n=4ZOb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-use-layout-effect": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7844f6242786e2a764466f4ec9952065e5dba63", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-TE6nm38/JoLKdFgroYPOCstCWuEX8tgx2x+eR6Z+JTOHknsX+8EJljWjq16JJancO2dYE/CwwMdm4tD/pAl5+g==", "signatures": [{"sig": "MEYCIQDScoaYzZ72AIhzaQfeW/b6noyoM9Dux7oPX0BV3cuKHAIhAJC1joFIVia8jHJdXrtdF2yiW76BYWQk6rqwPXnbT/My", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSzA//duqWwWf1+XLBMAwFQ8sKQ3hUs5EvEDSj8Ys+SGUUvMumyZ7f\r\nFvGM5BuRSmhmaS7821hki7z5+SkXKIAqWtNBtSjFj7LZb9n4lDmHpsd8IPQn\r\nP1L4xqm3VtFLy+4VmlS66lYJuTOorur4cFI1aIkctbFEwRituBsTm5jdwzco\r\nrUU27AtcAwadYvmyyXDT+ZCHNCqZCW/OVZxPu3B0ohDjLh7xaj2EqFQVaKmD\r\nuO7SQiiRSPlYvhTuA0kv2qy8KOhABTgQMJ44rNSfstajiKJwgqT0s6mpJDww\r\nb3kDRXE6vjlGyWRNaNTYnX7QVKes4iOHtN7DrxoGusANPwxQ1hZ78fCO/84F\r\nQxjKbac7yVr13Cqf5sghTR+xQijZI4U7ngt/FNlUBfud74tbEVs15s5LyXDh\r\n4lkUAfc47TFumOkFXFowXB+EEWNEFv/gnZ3AtNbB+vzLPREgrHBPadaoRzQj\r\nWDfKcmJMNTeBzyllos0S6Ry6gPVPbbTJWQb4tHK9UYKRBqwppuGyvTdEr1ge\r\n4d/GZVetEdYOls/mSobr1eaFr3WoD6DdvovHTfsOXiLV0UrzIjZJS9XHKfy9\r\nue38x0CPJLdS50Z2tAkZLfur8Whmeks5CRMJ6LScOza4Zq1f7nmq0kt1yJZK\r\nY+kwsrvsEtUprLtjAhVvSJLBOaxtn5RqbRw=\r\n=M+JG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-use-layout-effect": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "28801abf7ebc4de97f3c9fd5c4755f7ff9d8a0ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-Vq6gvNpj84Y/kKrxW+h61E+Kdc9tArb782IIcDtYwrzu+yJmlmi9848GaVvGC342+pLM5k+Q6UUyf+P3oBoyxg==", "signatures": [{"sig": "MEUCIGge6d3YQRNjC1sC+UAAFvWZft4yf4JdYeXQP5vhEpwVAiEAuV+TbKGbJ+u+/maCBHeHPt2TMa1sDqto/Qi0LCrMUvA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+HACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoAoA//bekkAvd0BVrchbsimSz6UjUe6ktFV0ltXYjn0vbX5z8VYzvw\r\nEyIkVdMaRUU5gOGCepch+4BWCW1vO/qNByT08i0HlEsVYmMCxSBQlMO3HEso\r\nB86lEuDBKZYvqbwWbGbblSVQFl+IQTpronJX7Ik+VC0EntUEKo3SUHMTMiOa\r\n8SyZvKAX+milsXBj5g51ynMykji5DXv7+e23EdKtmsbxJWZPKKHil29QjGMD\r\nrTFrgZJf9GTbmPK9EiYlNBjD9dvBJ+3ohPAgf/lJKySZ51Nex5T9Mqy5L7I8\r\nJDZ7f9CeZbc1YhDLhQb7XcsApsKBANmW0kHI0y9PwsjIePuGq/21KTG8JvIG\r\n8BcSURQTknQmY8jPrTuCOr/i9CgGF4GGGDYIWWOT1zpudN6NpfsXz+yua+DC\r\n/O0SKPi60EAARSPgmdqRiuUKFM/U9vUdw7g0zKCCu4i8Xx2vpcQasySzACDv\r\n/pFdAJ1EwSwzkwYyD9wowhrvFo7OHsbQ41dy/wjEK4xfq/svMEHF+x8VBF/c\r\nTZxvK3cTdSWDoyRmd7xF0otLKM1sd4qhzkMPWTG1LopkEGihLpYJ559y0ixY\r\nT282CARhsy/lqwlMEvWTZJuXjfNP1n/7GQIL2ScjLSRtvKgur9K115ple2ki\r\n4QerfRCBOM3ThTS8QOc5jx7iTMn053fBhAU=\r\n=C5wO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-use-layout-effect": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f44558d1ae407ac4223a2ededc0d9fbb8cd5eadf", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-ZaAs7YP9Vg850B5SkeSGQSmmVwYMHZVjsP+FcfWtEpdFXXeGjg8Csyqq1UynPU6WNhR2cnQ7KG4cbSatB7/IoQ==", "signatures": [{"sig": "MEUCIQD7cdf1IE8l4W1NnD/+G8kr5/ciyxlz1iXFTwucy+ai7wIgRRLnRckLYtjxKnWOfF8bv6md6aTYrmfJDSjjGmGgF18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrflw//QXXzH5zX+kqJIzY/juWr+g/oDQKXWhEIH6wSi3gvJrjSdX/M\r\ncwTrFc7Nfdvlx829HqBt4YWxcNwJnc//hH2KZoKdqd4b082acmAVGDnFhruF\r\nSG0Qr1Zq06IzDbbl51JS5iDSHA+MzDF2eYROsp2ujrpjMRzPAFgNLF42MiJG\r\nPXkckSVMj+se3re1pYMPmupvaq7JOe6dVkDM8n2JDNXbivibsIqYKU+V6AEy\r\nOKTXt3RYnod2sy5+11Xmd73GboKReO2OoeVIPb6NV48h0wq/wUK1jhiH5sMr\r\nLAiT75KdY6O2KvHkw7WQzdSokJQpiE2gQZ5+L95SsSEN30WEII8wb4LzfrB/\r\npF21el9WZ4X09TNlFNB5FURbeaINNkyDvfTuEnchhllr1hPd8ErgfbSUYklI\r\n1MMDZx23ggtmW7vT/tkqg2UQP+6u6ZypDOmDyG/FWEfzENIqukjyklphDqxX\r\noftfYMUtrFddCUtYnGv4Y2rrBgU0GUCicy9akaKYQB8L4wYp+OUUf5QAoQvz\r\nFTIDBIjTfYzOuNiOjdCtEMPJHVIRQZG+RY1GBaybqNToZpN6pWo7q/7sqf7K\r\n3DbmxI9HCZReO2NBhGwWCC8JndDQBbxQQBM0OHHOsZi8yKlAvG87MNOpHX5P\r\nUDmysTC+OhNnOmSzhbXTeYGwRU9WVixgXAU=\r\n=HZnK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-use-layout-effect": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5977ab59ed58c25a325e5d6879ea0d0c41f04558", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-jkFtGmfFVRv8NxRo3x6gPIHusJMU7l6zT0fn8xDTbZY3TeEQRHFk4SWxywawYg9Dq8NOvkXAA2USDLwC0ElEQg==", "signatures": [{"sig": "MEUCIFttATdb0aceeyn1npSACJVmo5FjTN0rodHRIkzNv9pNAiEA+jibtfAM1L34r/bdArBWy8vY/qtoFyPs490+ikZcTLE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoPKxAAmCPpDld7e0x3zeQU8wsU02QScsXwvf4quJLpp95on4X3dmdP\r\n/HCCLR6RaMNUIGp0M7amJhZPqMP+IuN/mTQsB1aJExIlCBbuGwU8jwbczGo1\r\niSa3B9FdrevgXrTzzQ2LmQ0Kygl7w5EVyj7m1puT32ityohfhXMpMMekfjfz\r\npTn0vXyIf6AH2afpIvUQjCYlQ3ee1C6gs3jysSAKF8C7zwGSu6lzCZC9/s6/\r\nsqJ9qoEBjAKzxX7rliZAHpfq+5VkImFbvLnz6ZL44ZpRb+ApyKuA1VcpRjj8\r\nq6B7vFTvRmaGdbAIrpx0lC5J+YPKV4ceUMfaUHbFUhn+18HsKwdvqMtHTI0o\r\nB3Go/Z2zFgaXYUw2dmaaVzQ3FtHswC67mPJ09pY1mLijm/HytpJCigRkHbi5\r\nO6Thd8V0FJ7tBmluFRUW/ZKrhcqdzs3souwmcnFSpst9mesaUfC9C54+NC9C\r\ny9tn1z3kzaXD/Rc4B0yvAVj/klO4glnYMoEt1/7Z+oRnQDIKrmwjkVAr6OdX\r\nA7z8f2Z4y9AiQVK0DmOvjBJxR6bmxL6TI4XZI0ptVHBXhX+V3NXJp4HWuR7l\r\nJ9K4fSJjfjdpJ/GNp3QTujSAYCt1Wrlr0akrUh9v0z9ADIBQiRDJ33ItGzJX\r\nW9hROFma7smH2tgrc7Bd/yuKluqh7i2vNi8=\r\n=Im2r\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-use-layout-effect": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f58bea7c8475c4cadc38e5d9e9b65eeb35889848", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-xsnIuid09aFh/SO2P+5MZd6yg87ptiRB8v3PybOp4trrk8MPzc9+jrGOqYOPFt3TokKgljZILqWrxbUtZdhzPQ==", "signatures": [{"sig": "MEUCIQCkg6CoZkDe84Rj0XCNaHbH7TTDNYK9QMT0UvLXCLwTbgIgdOdI+2GHzbvnokSkPg80StTsyYg1sz5Srkp5ZewK/pA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj34ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomMxAAlQzVflYV/hF8gRrVC5LyT0CUj/+jN1UlLqA6/OeDCed7qeTx\r\n+j5ENVMgiGscTteBsWQkJqdV4V80h9tV7hgyMmiUSDO74htaDsP8vq5CNAb5\r\nnZXuyp+9cwBc5V/rJZTDvz7DS5NgY4spS/qofseSANumPFvLoe7Vy1tLBYSb\r\nLdOH2L3dMgy6xRG6KQ31/46HN6yoJgmJx4rXoPaKMVZDCOkxKWCl5oSO2Jog\r\nGdtrjVHeS0/NsicyplNfG7cdH6CiMBzd1JB92pOg5uxZ0zJRuKswOrBqsmzF\r\n2ZNazzEuhkUHFhZspnipsYTBepvYV5C2DQuFLtd0E/+ikXhYjWHnvwRE5Y/V\r\nw78OHFF2lA8IF7JtFKBErdwnQdUuu+BjSpKjcKMpbHNhfV03rkD5PLoY2m1j\r\nAMBz3N7saXIW2vsM5jPKZoxHP6j3LIvqO+ThKQrNr4ybm5Puj6HWbtbF+5+E\r\nSXyxl1gAMh1xccgqoiUTmJKW+fSldhbk+brVKaFiZEDMqU0BeIqDd7IrBRFC\r\nXwZAc4iGHraVBtgD7H0D2EHEOATCYwTNKmYKgpEKE7C/YBeQAJN01/ZNqE/0\r\nB9eBNXs94Umlwq/O53GnNhMAR1leNKaEhPbmp8mEqbfkaw1AwtzvrFfuQ26q\r\njDe2pQrpDqn5MUPQ7tlB0W5VH4OeMRyV68s=\r\n=raIY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-use-layout-effect": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "85c1fe0e822a26ab66988f983265ef9a75137663", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-6zqvLyuLEISnKOYcISjteQag/DtguqX/6dax+qCfgVcNlA7WPj67g5LryHmS29RkfSCZsvqy7K+IBDrLyih68A==", "signatures": [{"sig": "MEQCIDTC6wmXIQ5xhiuODdohtz9qB10dWOMtG81Sy0UhEGIuAiBmJaUVCErjKl+KBhzFg79RD1DiDOZtdEpTpHok+P1Slg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqFPw/+K5yNzgMOZdoGt5eyHFVunyiuCuGFgzRk/IV1ACzzzi1UOI84\r\nGDfGxF/C3uLgcpdm1tAMWqt7mkjtu6cprjiMYuD1hz7KxnGb2dn0sLXi1HgJ\r\nHbDA6/s5Mh0vIYpPMNyzP5K3N7xGes761Ib3jRRLCEKGbDLjy/C5rA+ckf2I\r\nd31uFmO8mC+2E1yWuij/8j8LvWwN2BXS8BWj8su2YW+NnH6cmcusoOjXQ4SN\r\nYInfW/I++3z2rhri+h1/6XXkQYgBfYInog5jvL/p126gr1ca61kBPFCNYoAh\r\nRi00PwlOyLwITpNuLugkxSC5fekXJa1oHZcPTdYQXTc8eCXeu2W/w0PSuXop\r\nCEG+BcH9gG3bmeztCewm7Ixdh2nEE7CGptEkUoMnqIynL/qQmJfwzp6LkK7Q\r\nA6KYrRNqkM18y7f/W9jEPnPAi1A25zA5L1Duj5+bww+cxnOpk1Pexac2XaMy\r\n1C1JkPiPIR1gJEH5ze3xZK5od7c+Xou1P3jiSp/4FoJbKTC6Arc/7NVfKQLl\r\nWYsvCdPoMciBV1owZcyrCMwSmI1eDeAkfvJjDf3j6tyYmZnWlAUaEoFKb/Nk\r\n7+LcSCgVJa0u5/UX3owPHL3pE1NGJPAtbgnVf7hAKV1m+JGJWk7jbGZuYIxy\r\noxwioYvW1n+ZKOfMfuh6F0ElTMeWh7pN77c=\r\n=2YJW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-use-layout-effect": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "122c362209669ae219b3c7965f64b02767e59702", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-IkXYyXwoBLM2GT1PPuY000azH4wgh2rrfDQOo4H42k8PyD0Py9CuFUw12We5RDNfP0bzXX8NHOrF7sA0Z+o1FA==", "signatures": [{"sig": "MEUCICECV9H8Mlq3LhOypkQHkQ0E6DufL2+NZZR2Ecay/i/OAiEA4PYMRNg6e/PZb7yoAqcJVLaOnwIqdNi/J3/ow+woHLk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1lACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrl6A//fhNZMVyK2LLvYYlZbGUj2S1D5Xr4+Mx7ucdskH0qz8ULQdvJ\r\nVKYvP8MVLYN1wYd8QNhSI4jEmOlgMa27EqDL+rl2t2BTglg7Lm0aVswQ5/l+\r\nXcIAiPiaF22Wowxk0KqmU4nYF8Js23IU4tpYLFruc03hOoNQM0BkfYn0E8u6\r\nj/jMP3b2JLgWh6ZaIhiGyBqhWPZ9H59tg/qwyzmGTARGatZHVadDsfP/PnlA\r\njQn2V9Q4HgIFRce3P6tKXwWXvqVNWu2k1AWayQcqGMLLcu6Eilr7MeGmZqbO\r\nUH7FfUijMjG7/VKOMdJTUs2+JWrcGi1u/TTVuJmYwXdz/WQLs27WyDMisK5Z\r\nTDlY+0KNk7maW8I/ehv3jKAHzDbJ+Qb53GhuyHJbB1OZvGUAgInkDPVeYTJu\r\nLpfbFZ58ndOgeZ+zEV7Wa8l05h6Vxq3yvEfiCkTBcmlJZQZrQ/J+tuQiaFY2\r\nmffTuKvvk31XIrZJ5WNfR/JaoIdN302aWH7C4kqP13RTO9z7XPSfWeRy9cit\r\n6xqOerW3P8X/ji0Fa+/7U9fzCRuHHauHiufxh47YpK9ngIen+VgawdROaMni\r\nkQt3g8VuJRvSuFKyPWTZyNUgR34eiEDnVUDbdJdbe2XwD9VCiYKcDfSAU2Gt\r\nLOfS+WSGBLYf1BcGRtvzJVBkdhjqrvLOcpA=\r\n=wCrE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "24fa278f557c757794d7eaa721da706c931bfa17", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-kU45AnQgI59UkEnBDna14ad9HUGho6mYNWj6vvrvCYLEu+XzKjRsXI3053eOc/FkTJKZ3JzJ8VP/aFOMGFYfoA==", "signatures": [{"sig": "MEYCIQCCybNEiQc6GSKD8dstLBVfsJdExXelP5RlZgQ+2f3PUgIhAIZMnXz/jCyFXrfCc6ZvK/SOD/HVwKax1rTeFNXmaeev", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqf3A/9GzYWX3KWZ7sLkoRsz0Y3VBihl0ZEOH9nlhw7JlJsQy6n79ue\r\nYRAwpVCx+nw5pGAQ5GwHC7EXrY92i1o9KICqVeZOmd1wb3EYXKtfNCSdhdfl\r\ng50U8iNLYVx0LMSSOEGIbqthp7K3fbYEuSgzyvtDqPz6aweF4cv5s5NnGs3/\r\nZQdlihZikMq2t1uerodiKBcK+LjphUwx2tC/ZLvEBlNdfODwRBm+6K/HG2g+\r\npXm/ZsJHTyk5JMdTzm2LHxdgnCF3K6MLcD8k+QAHIIFMLPS3U/5m2iThHoPJ\r\nlbPrF5TUJJ7cJ26HVsgGhC19FlGBGZeBI3DWGgpn5g4xqYqUoE7nlk/a5Oti\r\n0wPReSLc76vz5D3f+530QN72eP3PB5WLPLzUjHPvauOy/SBiLXUgriD9fzEz\r\nIP5oiHzZoTpsY9/XdxHYpXo35A6ZJWlNXyZ+b895nCkXU1Fg+jbuqlm5mSk1\r\n9jK0Xz4tFp8Hcti+eqktduXqONpgLfGdwI9a74scmn3nZ4LQEt6548dQLC3z\r\n0I2oyYY4OqB6E00WSIrCIhtjyeygn16bTIXP6f7Exc7IwmcUyD72MFe0Hf8V\r\nki1DXku+fuGmfvbJmu8WJC6xtGZkmGrjz/DFiVzeZctnNeErDiQ9i2gCNWJQ\r\nlCt6C22GfsJcTC6xNZgvHt9gYSba3MZDX/4=\r\n=YeCM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eed1d92a1cffac616eb05eb52041387a7d2ce87f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-NTYQBXOSpkVTl/LFh+XlJ7b0jEV2hN43aju8OvlC2MhYJ/KVgWZv545nFDZgjKluFr8xKKkr0al+Kh/+URQi1Q==", "signatures": [{"sig": "MEQCIEIPbSlteoK/w4soBFg9776YIVktkHsGof83K9VXWyb7AiA8DC+4zfWaG1Em2Ks83AFPhDwmJ3NZk6fkKrSh3AQATg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrVtg//QNh83zfvD32iiN005Zg06zoTiqrAAOZcgFnHd32VS+wa15rz\r\nR5+T0DraAPHHrlIRBqBM7+gEIsuPmUkRiupbWJdbFyNjoIi2HwpoaTxHMQHH\r\nYNpS3FM0gWwyHRhwaAs4rhfygAMNJmadPM2wDUOdELdZVUq8Aq8gxofpHml1\r\ndO+VOFoJKpNL3E/9Ck/waqiLEG0X8th5SkDf8QVAIAEEAdIyK2n3VU2r7YoW\r\nV2LUOK+BCdi4FLh18tm0C2diP1BJT4iq95FPl3fPXCxDIkJbi0EfT5ejONM4\r\nu/zwN7qYJr2meTC4WX1ZNEXeBi6IuijxX3MDwDhGTV15SPavAPKeNaOSNAHD\r\nGHZy5LuVLQHTvWvDh94bQyyalBFgZENXrlUuytDynWlv1UU0qtGZwHSIseup\r\nxT0PNC3+rmGoTh01vB7yKi3GUL2kxnE6Jmf9Mo89LSw8AYR6PiOQKjcmyzI/\r\nWHIRVi6pr/8NrE8PVZ5HSPdAT//Scy7WpBoOtKnBN2l4os8i2owxZpymPPjE\r\nePUD4bDzjw/TJfskmD64AZ1lq1loXxN9kvRkXQLo3kapOT+yW36mGx8M2Qnd\r\nmMG1jnsxE0OVRkur7V78kJBtv0P2+UO6Ol8w5M0deRwOarTyWDDCpxveaQgq\r\nPyeDKS44oesHGx74a80m4uChjGwMjjehn9U=\r\n=tCFJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "011ed01347c91a09c9284c08a404469fa7fb9164", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-9k7OuTpbS8PBVjnK7poed7AaA1ZT3HRdiocJRoJERlmXkW6y3osIrRJKUY0WHFpfChH9JG+O5HuK//PQp0xlGA==", "signatures": [{"sig": "MEUCIEQQthwlVweJ/li4LovXsH930f21r2SXO9HAuiEEOAN6AiEAxjVM2i7SgiZTvYzBekhJshG5Ubh2ojdXghujWh8RrtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5OQ/9F6Z+iUu3/1zX5Krc7i4x31Y+VEfyH44KUU3pYV3A3+txt95/\r\ntWA2S/pl/9bBPoSElmaiWFkBGk9Zkfk4lGvvzN9tmcK9VF4ft8Y95bPYqrEy\r\nph5qFYdbvuFMauO5VL/w+RipQNljDIFfoPEHAJ6Yn6mIa/3FqBNbvaXBhhUL\r\n4nkUCx7JbdWi6f64c0BipYYAy/loKqDonAKky4ztBsgiZb1+plq4txY+f87u\r\nKS3U8bZ2B7/DPBKvV8noho9J+tGFQx5wWHkL0wRytwkG8JSP/4WMq7kHCl12\r\nMOb1jeVd75XZstEwKl5RJXj5Dkwzq0k4VGk/ThVmBngY2wJWYdnDxUw13kyK\r\n5SW7DFqOCc2KhNFj2+GEdsvyHerAT5DO/3trAHT5kmvnZq136jXwsBZrx0w6\r\nMfT9Z9vlRDyFcuzADnSb2MFpb+qCZT30W1hqe9ZpH5nR28u1PhR5TYxLC0sb\r\n5QlaUf2LrLVKwyYN4OSTQS23oBaj8MDbKdfF/lbA8da9rQditKcptLQRTbbV\r\noacffMk7pLPTwxoM2n1zbGnbkIbGFE1iMzRg+VYUNRM7aJkMliBZS4UNgeu6\r\nW1ibZhsj3zNUPmMBsuUxTtK5KuvER+OVE41Snlw+buregfiSVZLPD2cx5IeZ\r\ni88UskHW/qUnwUxcm4F13c24G7mmRmMOrSE=\r\n=EbN6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f0ee7f253db2f46d6311aa47246ee13e5a54394", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-AKgM/az2SBh9AWJ7Z/tJLcz6ihfs9HCNHC9htkVyyvleCBJmmKG220Kr78Kx6GMR7zdmaDEcymzTxYaqi90VJg==", "signatures": [{"sig": "MEUCIH3ocloGFxVltjxpSYUpmBIawUk6PzCdusBMvXyKRtbXAiEA9bNQAD2XcKD1DI7r3cChBtW5kM1x4HIxrr+z4z017yY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3XhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr59w/6AgR+lAEl16dbO6efIMSYDu0ZKYi9hsJs+KHNHgyeXpD7RmRD\r\nEVp2+bzBsVNlbGDMj8lzwkx6D4P/FQi8GVdIjkRc6HZg7kR/l10aYchs4mG9\r\naFa3OifRUbTJMwqGZBCvoLb+eIRVR9nIN3sVRs+VI5sPd3IEXntqHZ3uYcGn\r\nYPIUzt9Zf/ZNJk5nPniCUCUcLr9s9xyPNIbS2yts09LioVI4FZm7K7SKsy9x\r\nqI1sJCP8O4bw3jByf6ZSSK5nAzNPmQCKlxLrL4NhnKJ3aT7BrBLBPUpQeCd5\r\nUozKLGttcSL6IZJykQDu3gILPhQ+hzn2rFhG8MZxD7ZfaMxuB2SK96BS71Kv\r\n+WS6kWUfObGlUl044nA5tlHWjlDNe9sQko77g8B13vWgIoyu6Lg2VF32prVN\r\n6bw8LtsiJDnW+dEb1Cp49B6FKr/XwE6U2RRsLgKH+MFjtS24LuRrsnJCfLuf\r\nkq8brBa4uQEjoRxjwtNruEpaD0J4R+yI/qukpxMUbcGa2s/J9bEDMjFfnWa4\r\n9BsHtGJSBvMybUV40BmthxykiX+BzOIVH5381ZIWCHxWpZY6PyerNrZ+xFMP\r\nssAUC7p8csxzoZO8dgJwu7RdkUz7GQ6Wpoj2gyTYYQSj7bU7F3nUQAh7FGSF\r\nTmsr+9vDT2CkmPlAcleui2BzGBi2ogyB8Ds=\r\n=CNOt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fece3b99f6bc321daaf14e8ce3eeb49830e4bd42", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-mbLZ0V7UNx+mAh837BW8Plg6ED1dFgNvJEc0YarXG6kGMf3iQcMnYQatFlqoV4hwcHq7gH21iVsx4YOBPk1LgA==", "signatures": [{"sig": "MEUCIQDs5xYPj97IdCRJU4gFsLfIoz+gZKuxAlXmYyj2vbixOwIgK9XOpAmCpqyY5kHwxvR5HwCB1T8LLRBeYXYZpdznHuk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniR0ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmTBAAkTUZE7WqPK7zmVQ1e0zblvaA0rC3+OxLpRqtjCDXneAM74aN\r\n3uq67bbV3Bgtj7MP9gmHn+/Bu4FAHO4CffL3dPUtBO1K1CbsX9yfAes0fH5G\r\nZVveTocTgVtkLUAJ1tmbvUXSbEwBwyimDCn6iGEideph0N9f5M9peyTRjpdn\r\nFBzFGwi6Q1zWor0JopqZfrDCXVbd3WqdBkL0HCfOU24kD5ZvbW3STYQ3ZWyh\r\njGFLxUMg6/x8p+YV0QzQmOsIpNqx+ARYAuYn5LEDIMcksSEWX8HbPXAA2tWh\r\nEwnc8c4TchCBOKTdEBa1A1W68KxxX7zdydvTB6RWY65P8wOwgyoRTR8JXwSc\r\nB0Ws839pkBsHUPp3Jhkc8In8S3rqDYERo7q4iIc0+ceCVXPBmdwwfHbMRFIQ\r\nzBiiulWpPA1tEkU24CQ1Em4W4i8GJz5v5bXLG4v4J+EHHQbAwXvXEiKVOFZj\r\nWyGhDL/yRd1PhERd55Kk2nnjJACM3+jZjPdsXXH+gnZCOme4XNW74t+V5gGS\r\n68ocIdjl7QqWMQV2ly6GGVKBrcRQgkyGhopVCEhoWqyJONiYyT53CNr5FPNB\r\nI8qp2jbuQvKFYe71t/OuZ6wUUJH4CFoBxVvvjZqa4Kwbe8p3GgseBNyyYmqg\r\nM2qIEcF2RXivHAmyPFMNk5jUBuhVp3wKK3M=\r\n=fyAV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1dc687ce196f9a7240bdc49a8436e2e26d1a69bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-mJFzaORHZRSLRSz1pAdP61wIevFTEWV6P1IWZCxfH9yvf4YxYx+G1WNPoUppIJcA7at3KPYNLCMVVEObcgTbuA==", "signatures": [{"sig": "MEYCIQDCQTsSXas2oCf2gUNU0nWCbNhpPw9KYVDeyUzMMtFjdQIhAITOeSL3VJibewh9n5hvABE5taIdDIDEyQ1KK2oQx65L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKwQ/8CxDy6o0Xmy7hlvoi7zDflsFPBTBgtalYKId9OYS04g6KJNUJ\r\nfgiG2gXdrQNv48iXM7rgbZNrJ8dXm8TYhuGfYDRt3Z8ZswRVhn1Q264teWn4\r\nmZuv2MmwEhMw+xqkK99NDQbtAbn/YZKrKx9nQ9cXZctL/oIfM0kY60+5VshA\r\n8+K9V8hjjbBjSUofDG3i4FQEAwB4hXVD0YwxWQoi5GiYxzaKjU8Dnk0yUIB8\r\nDazwsjB2iCRlaFbYfuD8qyP4Y1WVNEuwYhLTFHbgDtFe2ceXgUEEoBaXCs6K\r\npc7uzX1toYul/5GCes1uxyPZCPhjGWbvpbC6lRRflcg+PYyLSr2UIHiG0497\r\nqWy7Yxg1iTZY7rQXBGSWpm7bTl1KJToogTDG8qPbASre7WgfSC5CmbC5nC8D\r\nziEnwg5Ya4kaLHXvhSf0le5M4qevwlAMJA165b7sF9UF7QU3EYp8ybmD9nmt\r\n9BwQWHTXjGl9wa/7cQMer+1ZKVcWHxwKb1TaU05iesf7lIigr+Omb6+YUxHY\r\nDaujsyQ2Qyf29lFq+UQnvixFXkq2QpnLpbqV64qKC3e7gu3CImS91O0vus2X\r\nfH6/mULyuVN9IfgtCpJI8nYDO7CGpTajaHVaPOCHmRmu7PUuOcpz5MLWwhXV\r\nA39oGrcQhL8bGy7NrA86ZTrTjxia9lsCK8c=\r\n=orUY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7066da8bb06b2c548f0bd1b8b22cc9378a736237", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-Nl+3WL+oC9VosoibLyKWh/yuLLDc/S4qfQHxGAaDrY+P3ag12IBg0LvSpH8ElUG8hJLkaI0shNELcdB3MsY7Ng==", "signatures": [{"sig": "MEUCIFg3K9NrT/JTdGK+1um88FHcyGLJMe8voXbHgX3kDkFKAiEA74MPFkUyp8e08XXcphUzXlvSl327tsNehLb5qlBH8DM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+FACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9rw/5AXEv2aiRnZgv5xsEVEIGUKMmnPkJiMjWSZgzFCHlR5z9JUDC\r\nUCdPL/BrDwEkMV+n2I+bpkqQ7bAWOjQQ8ACa1hIBNJkEkcNTcmbVwWsz6e37\r\nz3YfeG03wf/boK1GIW4Mkl0StI8Ep/uGu6DEb3Acza8TJ/rLMdARD1TCChxm\r\ngKblvQfYq4ILgIxjdN9dIlQwFL9ezZtj5+KziXmBBLJFsDxL/vIb1OqJC9s4\r\nRh/YAKRBfYZru6TBbflv8DzsbLhReLaOiMd80Nif/rfz5oXyM12Zo5A645jd\r\npFhuV+4nud+jPnD5YfU6GsHnogzt+DUqaahy91hchB5O7Ec6xhC2R5vfzPeG\r\nZNHArVqs314kW8FvO9g11kt+uUmm4xIYdNFjPHlf7izjEM4SK71e5jqz4+R0\r\nairgyfegOAcuGuHes2aBn2Gw9dhXJV8SiKkWairUMJlxylJkyboer0sKhGnM\r\nfxmJndpyhTSUGAJ0NOmFLT3nkj+laPuFFipdEdUI1aZD26NhVsZb4UWGbZ6Z\r\nmIvyHRNVu0JLktXqYv/6UJGrtO/6ARAn/Nx3GLFsdVohIM3JUj1Q2KNylr/D\r\neXqtk5Cp9RVi6ybs/R+BFTYRYPkLLL5qlD3Sr/0rXQCUlvEOunhU2ghuTbyi\r\n/mOxAoCTuruhZAxZPNWpVzu7U16IqX7Y8dc=\r\n=nUJn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6561fd476596ece61b8693bf5d79207639f25051", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-BaF3PzC1bV7mUWI83W4jc4z7K66v/ehF99+FfZu4cIa3bYuoJxpNKDCKZcKae6cpW7cU2SejGg2cFGsBvJPSSg==", "signatures": [{"sig": "MEUCIEMVuH9gl7KLg0dj+lsEYw4ptWw5bcu4inDSMSbsx2JWAiEAjMvk3IY3Dd23jzTbMhyUjMPg88j0+MAnPcwMoBRXlXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOY3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqkYg//easfrelco0yhA6q1pnbmEnMCDmQTYYCfssvYlEXpgqM53KbV\r\nZTyBAQ0sgGykXmNSVIYkM2wYUwAm8KwcNaTE5yNU05B4NYlUyS/Idqz9DaWl\r\n8UJAQ2QGcdRhQrhIMgKi5iVhqOh6IdfHZdSmISY8AGEZ/wNIFDgKozXuTTVc\r\nbIRHeNowL/suXUxIJzDJpgqDKXnXflLP3jH6ZGdtOnQWDEkN69717UVRbgSS\r\nz9VfBZdkS9DlgQJ+Lsqr8ZGfjO6+86kPLac7cVNS82YfZAHd6f3+vpsALymv\r\nRDQLJEhuza/zfBusgJCFmcPnf0sg7BFzLXFsFZE35y9OqATgDnpmx+vEdTf/\r\nPTcnzu3X3ctiqn5L3LIjNtIHl/bqdwLrttzTz+8yYHExrthuuzbWFUZjnBbK\r\nbLXkrKHPRTPsqr2JFkhfHTWBu9lS7hqZNAu2PA0YlLK26IuhKEHvklo4C/l5\r\nQOyjO33cOXRmCKDpF5iZCe9Z60d3Crf6kO6Xf3YC1LBsczKnUtdEF1R4vmkV\r\nIyU0uVN3YRvUG6cVgGuMxbMcljNFFIcngLtJCgZc5kBglRI8SoczaI9fDo5V\r\nB5yxTn+QjydZwKRWXfPY9c1WcE9QBDaG9jRkg+1kISsvmH46B+yQV1UQS33D\r\n4FV9lPOtL8res5d9SOeAKoNV6x1jCLthbR0=\r\n=LmOn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7fde79aedfa3e9a4e593ec958d50a22c8c30202f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-V2ihZdxix9fGdXCCE9hY0FzD2c33eyATlMeOQ9JwqJfAnw7crpFMkindbrLwKUs5EjRDrJ6NWJs+LWEjSO3IEA==", "signatures": [{"sig": "MEQCIAvunu+wnAUtlbC5boUY2Wh4VfopYOt7mLJV3Kmk6ndPAiBJvZs3xv02I/m8JUs3Y2GbSvayJz6nWeYMonGXFqpnAw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 24153, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbxBAAowFSUBOKlaBocsfYscflhIcNicQomghnO81kG7d1oa1wQjKL\r\n8UsMuuJyabWmNwPFT3Y6m+gAPOd+2oKb4Q4BntlBfVOEazrH6LRZQ20DzAqY\r\n0nkDk7P3MVzAslQE5uA37iIlHxQgtxU0F4YRVNBdw9zuKdzzgBt1r+gXg3ki\r\n2G9EZ0ehZEBXFBzKvj4Fgz+5dAETy49rKjm3v81rJK+XI1rKVqU8AMReRhFB\r\n9qQImhy+oT4oZmMXNaU+FZQRrGmahIk7C6a1X2P/Am2N9kmRcj2XVeo2Oz6P\r\nYPVguPxevpea8v429820bcvMdnwiRNfZ04y2ksVfvb0GBGmmrTKSgVbe9ucT\r\nYW5yJUqebf7nNlZyrUjeILxATo3qzImN2y4lG3Q1Xup9rTxg5SCp5pgnjLqN\r\ne6mKFtIhh03vsoPthx0/zNJIpfRLKnZ+MV9+mxvSKpyjZ56wec2wnzbZRSr6\r\n4csRDvNHt001kjKXe3FsdzB5WfCKkE9bMiAL8FH0YE7oZOxlyd82gEEprGlc\r\nMZHND63f9tMGeF4MsCAtnXaz1Zs2wfulkuthArHA1BHEGcSD8nKe0mMsULnL\r\nugADghTjwqMOU++7POs3beILMIG4m1wFZWzMLis+OzEJu3Og4VGsBCDqGGfj\r\nEon7MNYTmLvTUiyNSvrQ4SCcWGiqvi70uew=\r\n=bDVo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ae06e5ce2b8beb8ab839e03b6f41d933bca21a95", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-A4W/6nvU1b+NjYZy4B2OiyIR1VwFRbf6e4DUjU5DXBssHukaX0BrBkbWJcwinOfKPkqQ95oBolj6gz1NHWEIOA==", "signatures": [{"sig": "MEUCIEe/sO7n++yOXc9PXzJJGMnibaUkEm6y1D+Ym4FgdGnhAiEA8XkMsg2H5TQ39ozDZsa4Zap0uJGv2yYrCSEPRpjH1yg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0n8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpl6Q/8DDEEhAl8b9iAavtReYvd+mBDxYgC2Wtyf53Ren/RXBO65a7C\r\nHih7P3LJYgoKb1ADYbc0wA1PAdu+TaomTMckCVj7TkzMfihuEUjpDW1Ms9Au\r\nNFimNof1p37mGIPV76npznnLsDSfL4El7qgNWqwjv13GzAXXPMVHeRiCU7Qv\r\n36WSn+u1YPjypNI2Da7gTvFIokFwqxcWwQ+FHlqRrv5mtEwmKSFcFH+G9UnI\r\nE73CxJKSkbRLQAKaoOqAqI9FyvfnbekUJsAAulxpffMINUM9yhknhVBJxhGD\r\nMXPb189x3/geud+LYjbufQVJeIi+vkZd4O4y+IHv4lSw+qUb/ATUBmbDmgGL\r\n2bMVdoAUWjiVv9X0V8WMAQcEQLe+iU1g/4xGL3RibfMsO/0BbJ/p6OeqKI3T\r\nHLleaK99lrlppiEUrZK+MJSY9mmXhAcgfdhgSfjvi1Al7gARzBnTYKANmzO7\r\nq2+SE38ObP087MU9E8mo0SJeJw+0nyR9vpbl9qvisnbg4wdJ3VemsczmI0FE\r\ngzpcz/Ca2yC1fkvegrsSUMvtd+9DG5mA5DIhO1QvCb8MGPM2hoMPNbitppBW\r\nqPBj+sf2SH76ZYlU8sUHcMnL7CO+fKYV/4O12uJ8xuggrVMmI5g3WedBzcni\r\npXT4zgvmLzZA8TBIQRNwk8NGp4B1lHwkhHg=\r\n=D94p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e7cb1d8f32e1982bfdb4dedabb1224a390ffa43a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-WiBTOxZgVUIg4tTXnCFHTovPlQJzsFBLMsoFxVA0e95WcAwqC/Vh8T2y8anZSKceZBKX+yrzeI4AkYPTXR9rbw==", "signatures": [{"sig": "MEUCIFwSyN8vxAndvNNmsE0CMMQFhpB9AdSnYox6/vb37U1+AiEAt1w2Swx72SCnYSRKXZtXAwCvYqf9T12F/lXKhMCKFfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzp9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq2UQ/+KFTBs9Bvh3GnjheNxIRA5+ChiuVpvBbF95bLh1jzlc+zNLHu\r\nCsAvYlzs5PnzBQBFc3ZBvHkKWwrNE/s3U9m2yc32T03TeVFiDCZpUFykqimu\r\nQaFQEyYo3xtad0+T8xjoE+MfMOqtbOgFcE0gLdqVfKvIOhPNMOh7UnSQhfwA\r\nR6LvoaGq1Wo1/9ilJ/7QDC5ACtaF5mpPO3dRqrb9elLGwlI9FO39s0leFpbL\r\n88nPn3CbSOxkRHCvFf2PkIC6jr2snnYBl4Fr7w2pumTmQ1r0wjo/63Dq++49\r\nxCqR3f9YnyL0+WcypiCvoSw9aQRN3AbJTibn+fIibnohZ85l+qpJHFLGifmi\r\nUap7t69F0h3OMx2UPHfhz8nSjG3SFonRC2ir8iJNWyqWzQEgnxKLxtGVcZfR\r\nnI6ycw+UY+NHcZ6hJy7tT9LVK3d1u2u2Bqjx9TKzPf/R6DLxEPW1V2swNLq5\r\nfCimK89b3q9t1xHzQzaRyv3c2oKqEz1LEtpKS/oma3JyEsLB5YyzUXfcvcLk\r\n0a/j7dNA6ZGl31k+ibuSpyOMtgP26akLjJDjjvc4YXBorSczza9+TlDngswT\r\n7R2OavZGwVmi6x30gpzxRVwKYxa931lnb73iOeriJ1CL/X6KHR4pioIV8a/s\r\nlz+I+ljrWBYWvMBg3sarhcPT7ygmWd1D2c0=\r\n=2X1Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "761c11203dd163de28cbc451f18013e2f165bec5", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-NNG9j7qwXxYPiy1qyKn4QUXbpETBuu1iG5aEfircQ32pEmkjAOWMwspshR1qq+OwD4AahkfikNrHmuFmXSIbDw==", "signatures": [{"sig": "MEUCIHognFmPa8SSpbrIG6HTwII6oBFl583ikeVhX1EKPBSFAiEA7lAK49+f9TwIls/NSa6Z2GvxsQzBXA7reB8fNecSTVU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz94ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJuQ//d+8wD8TMAL5qSFx4NHW/hagv23a+HioggLzP2ME65mVdrrjM\r\nDUhRquvbVIG367o0if9eVi0N/EdP8Jqv0NQu85oIjb3qmDLASL8npNdRFTjH\r\n99HOcqeHLy/T/2zFVW1sDWxyuVQfpv+fFfnrB7t8U5Ld3PK4uhmRw5PQa0kP\r\nzdHD/xFJKs62hxFZuu83NgkDLNyEW0KcdCN+BBbfd9UuH2asP2qyZ9nLpHXF\r\n0QCbXmIH54ZlXFXUfUXJf5KBpreFCbex+D75FGvmKqJ2ccq5YLEFA8XI86ag\r\nYAI55Frc4ZNM5LymIkXKmETFD74wGYTaRCPy6WrXkVpbOe1e2ZWqlUlS10Xt\r\nqxXoVnI4cgihbcMc0iuE/uMPAn1TDR4B0HPrThMzW9C4jadYnk/7Br2S1enH\r\nZs4cFPv/KI+JzHslyOn02sfOtHfs1+8AAZyxFgbfdj3BTfe6uASyx0DLHiOx\r\nrLKsKOjfPTAJCmM1dcE1ICf67LQ/obxZlOzYf14Ll1GDd3LxywSyQ1D2HBCk\r\n67TqGvDYLQ8B+Cogz4TuvanVKaSpCvmZINWnymWOKMY0aUvg2iJhedP0LZ84\r\n3VdRSeo07v8j0LdQFJeGTmzkguTBPu2QYkHY0w4aiqjg0gzRUU2NYawFAMQ1\r\npbm+j01MfhAV2JQVgKjQ3mNnal2wo7/XIwk=\r\n=Dz4C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ad5e02a63cd70da6157eb8f23af36404a9549b89", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-z2IORUcjm9hfI1I9uk2fz8htgozdfAmHd4bkEhpNtDtu8MdKMROq9usZ05SaXTGx1KN5TUS5uFRgM/xalk5Ljw==", "signatures": [{"sig": "MEUCIQD+FpZ987k7ZDPnG+UQuiCVctC1/xHz9kwRM5gnit49RAIgSbxW/RvO053uWERdr8EcITsVI419mtfdjK3xbtdl8sk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11224, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmojTw/+Mi21TWrzfq2ghjgPeDEjrwtL5o1vbq8hoWLsOCeXlM86uxdI\r\nt1CQ3MhRSOYTCqlsN6CShDVDxPxcEjluKS+rDPCb0LMDxUTHIyNqvi+b0d37\r\nz9d2+EQfumvdDOl2u+pPw/98RsBqFnni3qt2SUN9CAx3SwOansdXQyMF1nZH\r\nMdbV3pK7wH3VDz20GGjLm8KGmksL21Im+A6ytGrORF2R6OYohIlDuBAFf2U5\r\n7RvSir4yr8+Xpd0MzmDfWU74gSon03C03bpNPPogpHARDWQu3zcHLe+VnqFv\r\nTFqRkN+w2+a/DRMAlu6/f9oW4QtyTORuiLTvLyZSARfHGSMyJyQ9fTrPzCKE\r\nMeAkuqI3Hw4LilIhlL1iDh5WMlL2MOB4fVBRDBu1pwws4q1SPupuoe0s5LDm\r\naU4d3D83sOkP46I2kdi9d4ylIAHmp2Gs6JBChf8NVQ1H0wZ+3jzShxmH8pd5\r\nJ7Gtiyisu7Ys5cmg5Os+ODGWEaFWUE0gCFcvsON0NsYw2BZVf/wI+QNsRTFZ\r\nlfTPjlUYJcjrw0CGyl9vSu5R374Qq0zIIwp6wmN8FjwG9Di6QaoVDaOvtj+G\r\nBDCiGG25pebPEGbQtLuzQDaExh2hck+FpcgHeYC80KcdOFVMLeABsTnQINOH\r\n/QxA8kGr1Q6/3xQ2NiDb3Hf8DvUWbvSv8iA=\r\n=LA1H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b06b44242ef731d536bc92f3aa4ad799ede19660", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-h39R29qP416fcHn+oqZwTyGG3+RArq+tuDMNDFgUrrY24nzYKPcHckpkshqe177AK+Uir8NxucbizelnIIaxcA==", "signatures": [{"sig": "MEYCIQCRc67Q88eIcvb7MbgnKHqjIfS2qHlfkksZ1sDGNdtTWwIhALWTRd+bGJYwsnBY0Y9Lnngc0wgQfjiti4ggbS+vSCKK", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9oA//YXzXozvWssXZA/7Td1jTZ+GMTxQjCOfkVL1FBqCJWhaegx4d\r\nY94+GXxAsWmUZNBo+it4X5TU5is9VwOiEDtxOkq0O8KEe7NjTgxru1lSlBNT\r\njJASl9YHJIc+6BN+VAVxMmEyR9b1MHIR0aL4ZG8jPIA8+Nv44G8mdw2GUjMb\r\nrW22MyNWp0F9Knjevg5Krko6H0ZdEZ3yExVzIFzw6qSVs/s3Ki/tKfZhywMe\r\n4BCBTtf1r8E1Na/6eTrD5JydVwc+vbi2yS5Ma++UeLqJuyTOfp/5BHaBHADh\r\nDqATS7Z8pt3wDV6pE6B/0OyL8Lp0wlo16nFgJyi1UfIY3Z+2dMs6JG6Rv5TH\r\nAKPd0YSPL8hFqCffMwolr/reigAppeBBapo1qR/viWc6JrfyjT0unE6NR+pU\r\n1FeZWHxFZdwN4p0UcS3I/wqNdkV09mpQZUWpnOS/Gx2z+NOs46+9716JM45c\r\nHNpGrep0gdM+SBKwWG+QboCCn1V/+c8D1wyHeFTcpBc5lQ5a5xfGcH0o+y+/\r\n7NRXF88/MdbCRzAA/eMUKMnltIc+xnOVHWGtLeup9drgezh2+Rs7qm+mwK13\r\n/2QiO5L5cwcgJNM7JAosQZzn/VtvC0UJwtgQQB8mybchCgNj+13BOl6ME7X+\r\nEg4SU/EFh3hAW35BOlW+bNZuDLJXhmWUuw8=\r\n=h69m\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9c3fde2ef98e9a35e6e933b66bf9cbaee474ce07", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-RfYWVs1pBZjubXKcOfW8Tg1A+RkTyBfnOHHknVHeYFSF+NL4kt7fVIVN8qcf4fFxNRKidvF9wIJa05xMMRe5Mg==", "signatures": [{"sig": "MEUCIQDMCUSvMpibWo8+lcyrYz3s8FWtUYwJ+vvvisQoGwboWAIgLj0qPCZf1qoyehVTfE6MzqhvIHedVdJm59RI8TANalc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvd5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+PQ//bd0l3DlB6D7hhNFYyTeHBr9DkZxEVA5RsUBHHTurBCYEvSkz\r\nMfixtpTUA1n0F9hbBhrlcglu6E+BQyNaHZtFxY5HSGGadyFKbwDFhDzO4twe\r\nIkhI5yrG8sE4pWAZltcJXUIIdhRz3rn5ASPV5Phd38J/D9fPxs8zddDptNkm\r\nd58q2uNKe2KChl6luRZwYiEZZa0F2SEfVN6SDkobkDSqC6NrZUMSL1jdHmto\r\nQVvvlrWC2AcsQkifORK8Ld+48OQ0Ekdr23eTkWqjq3MluFafhX8Y0aykHqYA\r\n/e8VD+Q5295Rvr85mHCym5JRIlGziytemSNt4n9c8CKppq03cvlyPxEWzLvq\r\ny5A/ShZERrstmID3rgBPwcvdI1CMbaIKVJyRFWvpJd+Q/GsP8WUIXqGwX+kC\r\nj+p4gUj5cPc4xnexYR38NXxybUo4vqUZpYMIn2xq37GOtKQGuJ5hssVz/Kzw\r\nA51Y2NyPgv7m1blLTNVyvJhTJ3egxv9lhKz90RQXXpJrtZ0PWwNTOaj9ZWDg\r\nlCx5yXNwpwIKkCGf151nJhXVhlp5Bsfca08IuUeAOqB6Gc9BTrF2xllGLRFV\r\nUOZqJSNXbYZncILTLCxkrhLMIO4wgtjt0ThHDzANtMpSaQtLJyHZU9CVU9ky\r\nN/IaTX4zLce0SzaOtvP1tuVHtvxQinv+dhQ=\r\n=e9j3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d129d66e4c641201bf0a0b8f4c3efa8c5427ccd2", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-aQD/DRsD+Sj+q1UDoisub+qQp/S74ZiUTyn5jCShubCKbgi2tsV3TNx/DI5Ka3hV+cF7uXPuppmG5nSq0mXubQ==", "signatures": [{"sig": "MEUCIDoSD8E5Saa40r5d6lLdKNViW9MHiR5h3DdBjp/8acb/AiEAgJjhJMVS7LgsCQYjTkjFeII3oyXxwaogTlgTQAfGZz0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbzQ/9E/c40PYyHRH+JtIcH/7O63FKXlNzv64yZz9t5H0uUPRKAfiZ\r\nkMYf1Ok4/hhd3/DNsaKSaVz2sXfQOZc3pjbduquhT6h0Dod0rMEF2K66w7RW\r\nj3ZYJkNUhOEwmpfy53a2oVCKF12LJnYSAuEDrGhtykiOYOqDI+zs3gNdhjdN\r\n/Y+mrhxfvhlnlQbbt1xLUwVruucaUjOO8Nb0THr+Im2LJaB69l5UYp2vZ012\r\nZgUUSxM/xyQQL6QkkugGC+n2RoSQTK2bAlwk8XxUHTQjPjq7ot/ZxAhZt8nL\r\nLLe8b1troEFDepBaWnmuYiSuHYNsS0DpE7JDLhS021HVj/uSLf/ARqsCckZh\r\nC/3qrgVuvV/5VXfEFKgxitDwu7gTWCWmk+M1FUQTZ4LzUjUsp6UleUc4gj0y\r\nZYyY+yRD/RN08yeTB9433SKrB1WZYaMsJPJcmBtVgVt80yepSFafQIyb6rIB\r\nrGBrPLv9MYEJ0+fAe3lstCu0xqWdv+1oNDoL0Ra5fYQT9czk7FbmMVuazftT\r\nqXTFNKp4S6NK1Rk2qf56CN4AnH6xWQG9NOD7yMD+C04ktvNWoxvOWms0tr5y\r\n4wihAZ6Q1IcYPot4OnJ/U1cz5r3Jo3tqQanc2OgTGcAv0v5ru95VY2jFNH1E\r\n0bfFUhQRZR1MT2pW8F74TDg2iAWT4SD/aRU=\r\n=iMSF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2bb6d84b7262c04258846c0f37b809c31053de05", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-gAOr2ZgtugIBkbGXkYHXxFHq3tEh4WxFAPTmEeS1oTRDADcITQMkAa/zoo+U7BS3bbO7/QFryg0cu5mehaSgTQ==", "signatures": [{"sig": "MEUCIQDYSUhsdfiUDK92iNl2qiv8xmwvGTcIbnGkXYcidZe0ggIgTPjIaEGt29wZxsCNJ9ou4Gn0WCpVyflCuqmS9Opu0tc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOVw//Zuux/XjGX/r5c3ykJdAGWphqYeQF5AcHLOwsj8GGQ4nsd1CJ\r\nZzoo4dwr3tHe5wyeEsr81Uvng4q/qq6UDCNbiZNKCZifk3DILn3CZJmNM6In\r\nWIFtcHXM9taStmGaqP+EXWoVHBjBvvtIKkVt3JU0f3e7b4+rSN5Nq+bxXJ9H\r\nyEpEBdrefyL/rY2WWlgrHg6QP1flMzcqZzE8i5EGMdCyz0fptztxTCsTQkdm\r\njv4O45BDNepzh3xP42wnOwHbTcHZUGgi/zJ9aMSou+MTfCyRMr1UbrE8ZlDa\r\nNcFOvsvHXdC/Kp27cB9yewKaPHJAbdsPQ6Af8eb+s8XwRE4u1jmLl3aF+pAk\r\n7z8sgH/GmbzKsTx5G6cPa/jGBQ4DDbYJs99GMkRBBnwypddqaNLQwCx3UtrK\r\nVTh+MBeqzcWHZm8QD0ohhCz3Zoz48wQvpL2Djxbsx7wWzZip0aGFA9kDtnIZ\r\nmrytMhy74Ra1woh+WiD2JZESIdAh9mMdQjOCwKZ+zpo/m9uzD/DX0sOqWDdN\r\nAzx1Y+ynrBOdBM8Fx4VQBgxqFRq6zvqM0yK4q5TGLm0ykHf78WglyqBrt3bl\r\nrmVDjOpLxXp71Edkm4tN994cS3j5LArvgx6LSZgPWm2DvFycM8syT94ORsAA\r\n5B5bp1bsnm+zv7G4z+LzRQcjK9Et7cLEj6s=\r\n=rVFq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c990f1950f4bfcdcf91e3a14883edbdb99bb8709", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-j5fIIx4x3umlYSvmLGT4JrSGcpc5T87a87GEXYH6okZQdkzbdoy3QWFoo+gqJWw+C0t8gulse3b5m8pniTWr2w==", "signatures": [{"sig": "MEYCIQCcZH4/+CyzIf0Y+3mi6us5xTjy3a4fHgLp0ck+QZaJ4gIhAM8sIJj7TccvZ8wg+fCkr3XmVz3DJJFmGe2c6TfNeJG8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpp2A/9GFY9yXK8VdY9r0rk0hCpsRVo8ZHMcvFz+TsjToSBW7eRbHQZ\r\n+r8npaP42kAq193bn+c68ERA5BaMdZvEQDrpqjC6/qTd7NHH6bJpWOMD8BxI\r\n7MkDtxKyB/gE25XdmX7UUfwwqDavtXOBW8yaMgiygv+T+4sueFlmvAYMmkf5\r\nB6zICwNhq/ulQEMOGsjXH1OP4xDFqUo4zZCiPC++uLGXIR6vdZJ7cdN623Rx\r\nVtX9cZlLPpG8qLjkepjngXvrni4iV4S7UpA/v2NnjP6Uo4NcBX/1lodzVm2q\r\nLaeEezOspnm2w7WrOoivqyT0cgP57CC1CWyFlYh2I98JbMceiqga/31O2Qhe\r\nticNW0Ois6HNUSavgFTT/hupEV4EVbAQYn2goqggLktHB5awkmSk+O3WH/7l\r\nFtDJTo3OgozwGXW9hfw8Xq4AG40SDCrAqKTJgiCjQ5VQS32mIB6YHrZyCPfV\r\npbHIjZrMq5r+GIdiD9v96iv/JvP1UvjyKKHtQpmiNSH9kDyZ3isnCFXOHLr1\r\nUm+9lMgGbpkLFZvscfr+bwl8+KTalJ2kSRxRnMlHmwwfNOoQUHc0vr+jGcXx\r\nWTbnQErzBPE5Dofwr1T3DY0qh3lJdK7Wcaj85sJTndzogdVdISAWHjqcH5aE\r\ni69BfUtOM1NQiVbFuqaM+BbrZKd5Softfyo=\r\n=IKN6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "40a0e1b66a3e6954d2f70a5116252db9c4191de6", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-T41yQc7X1aP5Vmgv5MNSaqZ5wtUSbxtaTsTU4FdEhPWGhumIuJlm3i0B8p0nc9HJ2/ODOq3BWO16zlBYH3R4cw==", "signatures": [{"sig": "MEUCIQCG+Om81loLq+Yw0YECaLqb6eYVpwCHsnllUsRqFrHftQIgXEahsBx/ULJUAKFE39poXHLRre0dPIks3KT7BI8U8Hg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197uACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWRBAApPUhHHduyCfV/8568OeOnd0zfBYQn0H3w4WizJdBBPezPpBq\r\neAzhdwoU69t2gJVS4isyevsJ1NWgUCor6W9F3dQ3pKelvD2ZzQsstTp+OtNj\r\n+qU6QkjOe9GGvAjHZdRH/uH8ui34v5ehFK3/hY/otNA5nHVV2UEcOBKVewI+\r\ncIx9N1o9SRlruKpsZFYp5oICChtCTdA5EJE5HwFAk0Kk/tru1PS2qdv/nfah\r\nScs4p/bfdD0HGNsNHGoDNKur<PERSON>Jjb3ZAC1HMCAjdcVnFFKP7Az9p89ubd0CMq\r\nuJtAM9tY13SNYywPxtP5zXUIOetyvITGeBJTbnBxHofaPcYhcGfSb6bC1iQX\r\n4koJve4/aINkpViqJB/J60+upbvQt8B2UBljgchhV4bm15kfvkma7u1tWuvA\r\nxLoL+V21bFCRlXeyJqrIZrEZ8PFNeT0p4A0EhArinWEwRytPInjEeOSxCOiP\r\nJBiQbrWPCXz+nfD1HlkLQJwOjguzQK39UbWicGN3N9uIA/iKYtJAMaORBzXt\r\nSRGFXwE17srTbjHz2j6ulIJsultLwlaihPVVjDC868HPIwdUVTGK3iCqp4ti\r\nk5GFyMleU3RJgLhy/UgWwddkObJvZu/u7uVi+7UjrwZRi2lTOyLzJ61QcBDa\r\nFFCr2fZLIVgOpdcEcJODeiTEcIhizkSrrb4=\r\n=KIsu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-portal", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "403a5d74c510300f277019b797ca7a915edb3b75", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-vhCvW/V6xWsbA3/SQN4zzVFQtZpbZTDrq0p/KM+MhPx/jo46PLmtRLudT5h30AVzwTniLpjeMziy2Oy8aozFcQ==", "signatures": [{"sig": "MEQCIC1JuOduCinE77SNfQsyETuBPaW+Z05AQBDvG9pqudeYAiA5j/fW1TpewsuWtHn4O2Ev+R1GXvXr0g1LMcHZw8UMeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CELACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1vA/7BsRLpVw4s/OLjQoGu5RvfuQ7zB/4T5X2Q/0UfeoHbEDe42pL\r\nJyaZ/Sc9XnxQRfQjKSr/U74H4e45iiblsl/K1FVgbpMf1c0xugP7eDRMi7Q1\r\nNqgj/f/WTgp7ARPSRv8kdLd0kmbu49sUpWAaUKoCaDHhHUBcHwyN5FmmH6IS\r\nOYUIg8EfPF0NyLcbgtE5sFtF0w4KBDAZcCx1FAkG4MP4BLsW0bMKb0dpyEfD\r\nS+WsMuC0FfWQbCfJx8oUtj7kU4lnmBByG0st9peTUeMjmE29QQ7gqTQl9Sk9\r\nEvui/4p6XxDlcT7XET8oER/pqbS/G/lDBPR1UUnvRIAOCjtGePz9b8gOi+NR\r\nrA4WnS2fqGDv/uVU96ZLbF3BRzx5HxIOqFVvpbhl0PXrpqLODXYBQB4FOkEf\r\naBNNCmBbl9D3/exRKQf9IGUJpo8GB1d8BLvJOdCG93L9E6QltJQC9z/pCucK\r\nUfsEHuv4wPI7vGKUmL73p0MVwrIIyjE6B2PgkN/0ZXrskRWOey2x5Qfr6m6x\r\ncdhM9eZIqfvA52wpSnvBwylpHERE5YHhxIHfhLAwHj7NLmXJA11EfHX4nK/v\r\ncCdvqYdyXuV/Omq//oUyh+Hfk+7RO/nuAae5d8YGiPfG90Kx1dL3ILfwRksG\r\nsEChKBZjWRAfeJIlWXDpA6vbINeari3fplA=\r\n=ddr9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-portal", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "38026a446fca88bd728bbbfeed1d41e75076584b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Lypt8Xk584DjPIeXRF0WPZt32467rBi4h+5FRlqoXtK9hFRQ+7r+4qHo5mYxx1HVoXsVIJZeaBtrf2nWhl2orw==", "signatures": [{"sig": "MEYCIQDt2PTnIiDU2oHaYbHMKTr0kjUZFfTLJhor6f0avgui0AIhAJYA3rNBuLIb5PpVvigpGRqN+HUl2h2quQRbGpRX3zR2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRCg/+KVRIEurN0YyYfwal6yLY7sdefpTY/uB3p1db2EbR9hiwsyRz\r\nOzQsDJ8sp3pEoW+NPk23ocHXXLLulBkAbBasN8m4ocA9fLjdywX7ufFP5EQY\r\n/Mlm7geaN/+yFEmJY5/xH1OHTQO4YGRUFP8Cr7GnNSrLpZ/YZ7bBcps97lTZ\r\naPw9rpRG76+N+i41cdCoUqXLX4eIPraFdUFLYgMVtToD2rossj/naDxJdOrl\r\nhjzl/mILpa6q1ob9smDt2LBBfE3uOeVyBHgUXhZoSrIss/DTcSxZ8GdsDMbO\r\nfZ6ujZiHtirkANN9/GMuUILk1UHArC0FV5Lrh4vXj6LawUehuxtqUgSFgYqI\r\nWlc+8+Sw2/OJ33SpbmWEMZ/U4rGKVMAc0aItuGnxvcD4ZJ3pKvm/m53QgjWx\r\nhWVnWssJ3tWLHpD273oLx2gHSirHE3orlRqVx+OvTnJxpUvnS96SOIPe/YBE\r\n1pIt99AM9kNotne1GdXlhID6pE0b6HLM6pME3qDNbFgagzF1w6sir8jHsDGG\r\n0NT6DhXNvGvVbQaKqyBgBYfoYprCqDwxBiwF54pRZSQ1m457dfbxULLh0dz6\r\nLGOwtNSO+1ioPlpHAF7BP3bqyKM1QsEa2/yo2w1Nl8YdgHwrorh7jiM9Fel4\r\nCSnU4PoYzwxoK/3ksTUY9RRiuSUx05wOJhc=\r\n=7o4r\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-portal", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7220b66743394fabb50c55cb32381395cc4a276b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-a8qyFO/Xb99d8wQdu4o7qnigNjTPG123uADNecz0eX4usnQEj7o+cG4ZX4zkqq98NYekT7UoEQIjxBNWIFuqTA==", "signatures": [{"sig": "MEYCIQDWuPWhXZiT9U5x8y7LZOdgMwYGfUIOZ+qRAmzPAHnQpgIhAJnCha1kfYy7HvRlgLxaDf5QwMFiBWtbWjGSpRIsLse7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryyQ/7BCa9oASGYd93KTcqIOfVpTyZsU/Ei14DwlqHCX138LKexK8V\r\ncUiCInBVj5b01d3uM/Tc6dvhbmUXfak1cAwRExNX2yzqa8b1EY94maQinVb6\r\n/cy2q4drQ5MO64NADsYjZ7sjpgSePmPl3sXydQlrXtRrUmB0Jdsfq7CWzy+B\r\nKykpjoQYz/Kebc5C97JDj/nmAHPMBnrwCJUEi8aVFR8YYgc4ZByOflWsGBgR\r\nzGWOoC1EmTrl/3wOp031+7LUzLDLKpUyx48QPgAgUn7DU2VCuhbqoinjuyzE\r\nN15qgEJXWGfpcunHjuSSc9qUCZsyEwMwmAgLNhTrIZaNpO4bQqQTjdebP2wI\r\nPRtTpJrwZayHeiDW2Rc3YUoXexRFkP1NucqsmhaAAxriCB1g6HrTzzJp44fS\r\nNK7qjLxyEdl4gFI3XHZVeUghCmjpYvaR0bgaEDZdoyCLvUkTXR+wXDGSiua1\r\n5+yXWTw27FsdeKdFfBni0OmLAo2gPa6JdDlHi9qFCjyur9HrVphJ1RZffYvE\r\n8jxzPRtsezQGMJ/cPgoXtVR+SRZoMV8rEMXhHdiH6oPhdS3PnO2IbaReQfTZ\r\n5PjW3M51W09i0HIkni0qUF+eA3V6R47is8fGEFJWU7rxcC1edEr5cMwqVEqI\r\nPX7FUKktDc1TL5X1xuIYCvRyFz2PkzHFOr4=\r\n=THjP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "375ec9bf08e17b1b7d548df0f8281e755cfaf8ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-EtoZLfw/D1/oHpFIFUmBdQGXtNcAnj1BxcLEmYlzRjgQ6qxE62oqJX2B6m88ce9NEpIAkvjOFQXzmr1E/Cx7sg==", "signatures": [{"sig": "MEUCIHVlDwcGqYDRib9k1sFXsQcUHYzxhlm1mj2Nt/U+4wUHAiEAlNrMTqmrNL2gL7GCP7oent9GNo89Ej9j1zt92ADpdG4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrySA//ZJH2peD+viQGdx6ZGx76Ad04ogdcbQjkSfIl88qX86tmCGTo\r\nNHJrzUwujLU2J1UPHGTgNO/b1lh5Xyewv7bl2Ym/ipNpqUkSr+KZheeRvo9f\r\nxNJPqBa51xSeJw4oUEAMjUvAr03KSM5dI48rdRdl0rBh7MwxxKzjetQokIur\r\nT0LiStZR96/2aAqajf4/Ex72MyXZicRsqJpcpKlS9RDtAXNaOHC/4NR0E4lk\r\niK+JV3D9fAxLXwjA3I3Kpszm/DdFERmWqeW/AhGfB9IpkVQ7oAxvJxp/uw8d\r\nZNRscJinH6IHHg3WsrBkZ7rulfXEfXEtIj/02lZuQbHKWVUb4o5ztGHblmvx\r\nI0efsc3HgfqnCxj9eS8iCzIRjNfQvJbhVnYXsh6/7aPUnFAIkeKnl1slEDHL\r\nAXoAbFFg49feNVk/Cqo0rM/iCd8USSXG6WvjUhGNOnruUM3SmRtx1F48US1p\r\nPuomiIYh5hECJqYeyd+CQ3Nrf6C4GVhFfYtzahE+Ec1+FSLewg1hFrMdXy5Z\r\nBrLyFNt8Wg/9b0+dxZi6lIA+VW3WfGg0qxZSOsUoJMHhia1SI6OLsm03088H\r\nYNpe9i9M7WAjJ8WhsbPKcTxlm7688PaOSEQUy9GZRWhrGSZ+QaD/KFXuYK1/\r\njuIpLdqLxZQAdz/GFffAQ++G2Rj1ksXeDhU=\r\n=ABwv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "af82901ef430da2e0e305507105c71ad9833daba", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-xur1I6Yg5HbBv44IL60PkUrdgrSGyHgTOkxe0K7bhLNUIEmbYspbHonk41uM2MuINuINRxKQyhNe9WF9R3RtSA==", "signatures": [{"sig": "MEYCIQD36ndLgLBUip8BsOqYvoTIVzzYY+3sQ7vV54jyIQJYHQIhAMPaf61SnRjzxlQdfjAkcwx80Nd7yrDdPHC+2/rarQo8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqXQw//aun/7fOl2ckZktE60iV751vwqD/I4AhhlOeUH1nkbbjJ943S\r\nHrC79qeI4t78aj8TL8WUSZ1vOytmzrQBNGBJZus88NVv2f2v4WlW5MfZSmma\r\nXagpxwPTvc6Nw4QxghHWjAOtJh3io2ioGfiG4+3R+2FUaO33fiPM3zryf921\r\nppMk1KywDrHPMj2NBK62J9FuymJEUfiJ+1Z7WyFb9tUOKdmEXdwkqnHqMt2I\r\n+fkKtBfRyJW6YR8hSD1JS7QFdMrPhc7VadHCdmtYH+MKEgWMr/o3rtunXCRy\r\nJlru8vclZ4aVV8LoexMZbXv3zkB2aRDCDEj4uOcL1q48D9sjWuRk9UQvvJm9\r\nsKXjCf6CXEb/L3YCtREuNzAwsQKDXP8lk/JdIQrcOklhipw+Xh9CeKJ4bFdD\r\nKOwxEkIBQtHaDKWY+wZAv0MJs5L4QinDSLUQfnW8Jqa08HxRs2JLmmNKQ+Ab\r\nVtcV57o3cDs+OD3ol/3l8Q2lyv1mSBU40Y6fNuh8KCvI0QUEhCYMtntpkZ2r\r\nNW9uqQpK3QiyWkl9HJsBDTDXk7cHww9QfTwMrmz/52o58ao4MHYfvyqM4Ild\r\n6Kyk8kKhCbjEEOSttWz0nOEpwLQTeh49vRU/paRnMsbE0anxaTFFZ9QKxtII\r\nUSE0TQBgcvNR6WrE6SQiqRHU7DjKW49MHTU=\r\n=21Gf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e5d92cd933537bb4758946845467471e96fff027", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-bBhR9d5Kz29jshHUg8/Iu2VKaVOoV3qm2M9ygrLpOnpYOYFLfeFVn8TUInluIxvH5IEUDJiVVEDUnM1yNd4a1Q==", "signatures": [{"sig": "MEYCIQCNQ8ppUUfHdaMLFbiPKkbN6DHDpBswaA1/+xRc3ztJ4wIhAIlBXypwmt7Gzf0JUQ+s8zn1BCzqqHX5c3uLrVI/FP6t", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdcRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVGw//YaSmS2/cIa2V+rxEusz3qqZ5zYj6mdjjMUjbo93UR6GU+scH\r\n81IPpS7yJLttTipflqBdKQVSMco3yBQgoQquEZ2D8EcYCP3cIXlaYrfmDhU4\r\nKHS+oKuaCJ9bBp+7AtCFwhiTsBN9mMHUoZgRlfettHE8ILUMh1C9sqzIvMfg\r\nr8yuf4O8FiAlpMXPELeIlwhwhp8hX70huN3qgf/0MaCaYaxAlD9lDYhfKYnf\r\nFkcZeBjUm3xCAqAw4QZiP56Cp10cxtBukF+js7TGgsdZdxhKe+yw0u5RApHH\r\nlov89O2CH+UTn5lEspRTirn3oCe+i+ZPZFh8FbXKqRjmuppZhDkgE8Ujphhw\r\nGVXlKOmSTEOaNXn7AVfC5a8YJrKJWIjrbs0LBrrI2t04NNDVvxqdW6i5AwnV\r\nd9fWptJfNZfJ7Zk7oF8D8DvjQPPciec8JYcTkz777N56CWKfbIk423fiOq0C\r\ncQ2eozK0JhwftL8QqOmw4wVV4uExeH4r+yga7EsavPB2H/9lYxmUQRGqgjeC\r\ng12XqBs43rDFumtJ7kLYhBdzn1iSswfJIiYpP1ziaJz3hzKQ/R5AtIeT5GYg\r\nPejytwZjbkM7DY666k/MMomrg1U8FDcmu4Ouwf06tqT0bIwTA/ltgTWVfppS\r\ndTIbvjzPVxDzNB4rZ6XutO2cN0nGd8bqI/M=\r\n=8Rvj\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "21d436d43e619dec3882cd8693b502ced0bd5090", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-83ug5CcBZhrjDBQdSurfyA2a7rP+He/XbfQ1pd1stmWsJ8gYV8a8JFA5YvGFPKn38i1PeXzPrawhbiEjZXMIQw==", "signatures": [{"sig": "MEYCIQDhyvQjPjps1hR4ed/cP0AvOqROFOXkQx7PenOa0a0BEwIhAM2pjg4FPPmT/iblrZSUJperGAPQBgcnxiWMdERp5BdZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrNA/+PqArRKN/bbzcZ8uKHwt0HxZWJ9MFM7nd1Kqwl6EX3i58OEZP\r\nHLOl/G2OCJgEs1a+YKR1AbZvOC0mnunr+9ENR0TkIOTLmMhGlQHmWFLtbnCn\r\nHq2tJqpEE6j03lOw+SJsYU0U9KsbUNBhRlw7PLD8v+kzcZnOOCQWP9DxACCv\r\njOT2gO4hEMJ/eQWPculUx5TeiMfHQg6X6GPjdCzk/snUfcRYUc+cIIJbOPGi\r\nEgmVUTmI1R8/P7iKJNiXycRcCyTXE/h3k00b7GYysJ9Anjou9WAz92bj8EKt\r\nrVQ6aipWlWcjtxinAY6h8c3zbrinJtVp7roblhgkZtOQY90me+NqZUSWVp4U\r\nA1+G5DdYiAe0sPlU5ielYJCJ1pTs6uKex8BgAOZ7uDu8qpZUGga+QuPmNz1O\r\nqQSCMW8cNBXSaeaevo7CGu+aL2Yicn6nD9WG0+YIjrBZ+2Ib1WVbokIEr4BB\r\n83m4Z7EImnC+YMjlzzy3Y0MEAOEmo7NmFmppL/z1Nhozc5Zgm4dTgKGE9slc\r\nGx+Zw4+kJogiC9WwCNHQamvWs9+D+4o7jbyRjaey2ZMgDWJUEsHuK7hE7opO\r\ngX9JehbB5Av8LDyMazc8LZxjQVE5RNR0G3FxUbfjqu45UoiGFWn4zQJZXxJj\r\nofX1YiLU5+KmABSW2MLZc7zuIM10vgoZTx0=\r\n=2CAI\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dd6995290c58a81d6ff71221135dd4ec85181978", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-YuTROfW1zi9JCgI8rgEyzRDJjqtZDzEAwi/cFOKKqmuzBkgnqwZiCWanyly8Ps6W+/cztmJupRtsoz3kzpo81g==", "signatures": [{"sig": "MEUCIQCBP7M7rUyyYirtBUEn6mKGxWfTaUIznV4Whr7F5qYg/QIgV1izqOh642TO+5nOll7IQhz7bJRGBTXm8qPat87jlQI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC3RAAlPvMbHCLbPVZVNTpinTFVvCoOA1iIKCBBueAbctua5v5ovTL\r\nCZoz0oaZz/1Y9ri+7LfYHBPc6i1uSsC7VM0aTsiYLk/pZ9eAJwqNR839HV3g\r\nZuu7GmZ9DCQxfVURBHrbsT+yXXpRrAvxWEykHDFxFD5iD8q5k2z/bIhpFgoj\r\nxDQy6d1raJpTUOr/kARFy7b+Z7dM8HBNMwDSGrhWGbwwBRnaATHrlq7+GEhk\r\nU3KmX21gfsOzf+Q7IEOFqpGqq7DrSO43+25yNkR34Cnj4RgNdDjyxZeMtVYx\r\nvQ4qGbncxeMHdlFIect/zG+p+24KUCx8Oh6R/iWlcLYyO66dHyW/9zYT5ew4\r\npvsTHA8FCvsUx0eaoOkdoGXCwm5XCPa/GS7/gGWBT/NAZjS/IGrr0wa/3VF5\r\nBZtcm1N7HLsXb5JZQuSS5njowe9spQ632RPgiSB0S95sNX8BmghZOoRk7x/K\r\n0Wy6zBEfJYtj2+GYDgRGp5AY6w/jRaYJ1+/vaAp1+ivq8XZ+GJj95OlMbDos\r\nJLmoV7BKI+Z/zLeyWzjR7p89TzoMoFlOzgRnZWYy21MwxvrTEkHzG9hSBMOh\r\nrLeBMqNsjMKBRopyWYfk2iUZlSa+FMb+KnwN/hO38QrONACPfPiImLVM0O6X\r\n9KEKEFgkqHoEvUEhm1gv0u9bpzUpjA3zJ2A=\r\n=MNlW\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a56c73e92c33a069b37ca239f989e60a7ee0a291", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-rezj8KfZMlPGx+z6L3ZqEWOnbFa2Puo++y/WSNBolWbm9cqzPvsanD7yxWLxzp3OM79C/JB8zWpSyGNeEMyFmw==", "signatures": [{"sig": "MEUCIAkC2ju2Qlj/mKgODguQfxMf1y52W4VweuiqWMYrQKTwAiEA2CIx+vJhIbNFpXOPI2zt5puc/Mt2Olc45jZyzRlvm3g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwPfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwHw/+Nr3OxHpmiHOEXpGEPUHs5jH+BBeJam3kSqkozuc0cUkw8fK9\r\nS+ANR35859anyYwUSN3cf6aPlcJJVQCQAic0cSI5Q7gTFfxozzI6LHhlI0al\r\npNnQyhjQ72gVtA+RinSQP180Ti2/2JxG5riSl9RfDR5aUdlH+/KporptZavm\r\nTDLjU7lb6p6hcJHibudaWSaabmYheOGjVy6eUZJG9VVqM0JB0xXti3ClIvcY\r\n4rQY4geflnG7xrteANIUKxrx5d6Cn8NOb8nS069r8z3Hk+Sxh1qRjkZucgtl\r\nZMh1SgfLiyrmAzQSQAlQJInn0zI7vJUYrPu/64QxTaob5rq5/dbRel2tTg6m\r\neT1f+awzi2wXznHJB5a4ewm20Ls8fVAr/1BHxgSXVmidXNXQB7Nb1xy19wCA\r\nTsPQiF8U5131Xa7d86qDMmyQ3rvjFOpeU3Na5263kuSDw7uYELPRsEBid/vE\r\nE0lb7ZCxFjmWpg1apUa70OyPoeFwNucdo+uw272ywdaysM3Cu4ci9NGEBAVh\r\nEn6zS0uAxM0im+Oh1bB4YzfJ55Fb3vKbiIoHLIhyz1e4H29gdBj9w6duR8AV\r\nQ3j1GZDHlxy8A21aGIRMShNCKp8ZqlugSDUzN/zbeqGHWbipc+M9aR/m5Yhl\r\n4skFjzerOcSGwbduauvl9ea1jtMSRMwhmyU=\r\n=M6jF\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eed7b276c179323e326757ed5a271dbb961328cc", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-qYKnJlGzB3sg+KWeuzP0eF23BXUmI6iiqJ/2ajNJtpilTNENr/OjSfB4LuWqg02bOTcgLDW69zeSXsAtDwxXsg==", "signatures": [{"sig": "MEUCIHzt67bESGjsvLgKdtnJpKlexS+l4f5bNPXqCjZSP+3KAiEA9VzMaQEwgK9cbynBrWWetlSJnn9x2r+lASafBDHwIbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQPg/+PqtkIoLSqZz7AZ3DCcsxWmq94T6HrFxrYmcVve1rMPCr/HW7\r\niXbXM2RludDUS7ZZuOW/rx76ht64OUYmUIE0+L9GQbd9mK2++0gXZ3nar4d5\r\npUBBy8TbkMS3nELVTu7OILh4gfJjnsVeD3JdnS+Yh+mELLv5QTFrFp03guA6\r\nm34Mj4+uyD+RGED1cWzZ4qbsqBL7Sq0rEMquTKWZNkGubSFAYPQhYgxdhywY\r\n6+QO2ls5V4Ack8fK/lReuTgsWNwqDP1xqyK2trSUoYopLUYUlqHtseN1Tdxb\r\n9W3qJxi09WHL7aPhn/7bvx7266d9AJFmrJdPFo8CdKJ6py4tcsTYh6TdqItB\r\nAjIdrVO7dhyA201RGqR2bUzzRCQeoYr/E1mqxvvYYCvDQCKrXtJjmP6LwN8A\r\ng0jODdUmoDcm7V81mv9SpwAnc1DYzX1nq4OJw7ubwbkcUQyv+DzsA2MpFJwH\r\ngtbGosVrpkplHadwA9vGYNdJ/GYnMy91mvHHaVP+Qu3FNMJjqqrpcsU5qqz0\r\nFwo4zWiZN4UKYgnY7AEiDPFU9gx2AR9vh4qgBcAS2nnp+lWrFPxBD98WSc1F\r\nmzwNh5ommQOsL8wN9RmOvOi3MP5REXhEaQAx+BFgs5LAE0qxMv4SaaK7ssAB\r\njqtCI1awRJmBTMNpDkcJLoCmpsEVIPr80WI=\r\n=CnjM\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b559377b7e8a2c3ec623bec4d3c941043949a69c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-K/H7vesmqGnbP/ldaVkkXXqLlXPIF+A0Lr5MZCpYz/TV6do3AE4XxAJEffmFK+6rzZilmLJXPT+QT/yh0Cx88w==", "signatures": [{"sig": "MEUCIDgHCex0tG4gAKWnD1I/9gWzkQ9tB2GXzTpad7hU4YYxAiEApb5oIuKBX6rhRq5M8tuduRHaqnxjwauUh3k0ATbcB5k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gnACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmomaBAAnVfmK6JC3fVsKCb7JP2mklkjqOIOwJf+RAIQMI2bGvGiTeMj\r\n9WCk3GzO2O2LJPOeGbU5rF9Yyq7p1kK5NmL94aTrUQRHbl1pWcKMePpgM0Hx\r\ntbsqJpsEEygQICzG4m/1eR4Xy10JI9n/J/yE0Skvr7N6VdNaIJtnvUehfe75\r\nmSn3Bp8vG3NPHYJVbPDtRYuZPYL81nL1KKd8h+AHB0E8ZOLFziC++RzAyMSx\r\n3rvfkhvMAHQ+Rexx5A+x8Z+YP1i7rW6BpNI4/CIxesplX8tEQvMWKSOZsZpc\r\n005HwUAKqBgEpN9R4qBtI8M33fvaqgjzEvJSROGmyKMV7YL5iML+nY3VDsJl\r\n8PqbOpt/4MVVcYjTEKhKiUkgHX6BkE1Uo+fn4Fy7/MUxLLipdRQCBLLjilJx\r\nyztaUWZpbLHCZ88NSyPfrbWLK0hKKKfzwBa+g4+9iIOZAkEWVvUeFbvFXQBU\r\n8zzAS1hTJGu0fURK/mSrIgYmLXIPijFTNAaw+wmBwfKPBxjIxkqwa4QEJDM0\r\nBIvuybmWn4FIkaTwbxUY2SeA2Ljd1TQU+Tei6flNn7XnHHVegND+ZLKOBnoG\r\nf4WduX7ljxXJo6dDIvo3I4LrgVFl4Zu0zdpUxXUFrEOWizbmnokcU0aHgj0N\r\n+AhyFLaAb6e3kYn6jTCgyjzpACMvQEuhDho=\r\n=Zdq+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4b2d7ec0b2bfa456113c10676e1b94d9b6e1193c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-msuiO+UHFFrSBgSfgyy+RqS6dDjfNQCKktyWiQX1F2MHgjmHCX6oIdJhONEOimBPfUqkjNCIkhTUn8oTOmAjUg==", "signatures": [{"sig": "MEYCIQDJw5Oo/3U7mvg3utiUIsNKsjWX2hKi+YuXnbOeIZIQYAIhALX4nPbL6mBCoHn/D3nHqENoAq72DzJtcf7VbCmzxe3O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq7gQ//cR4LhfA6VH+4LWE1eoyDpJt5tvxZLf+w+ZjdqogzjZjrZkGr\r\nIGCG1HLQYtlHk+k0siTfaglcMcRdjQVhn21kmCXfVYKwILvuLjHrdLLLXC5t\r\nu7G432azhdlK3WfBvwllmjaB3Txe5El2jmfdubAWnKH1I8sT7gJgkqYGI3bH\r\n7WvBG8Ry94Z/IglaB6MO8vVPfREf6qZD/28ZRh9m6EA5O+DHPpO+iVbcnvHt\r\nfXHjdedQXI1fHLqSg6vzDTJnNmL0w6tPZon38BTdi1nm8XrX5bEJ2Dq/9FXs\r\nBVpjorSsRuBn+0jESprPG5WIIv3NslB9s7eX5Ze8poWGrWYqYQLrzwmEYRbB\r\n9I1BKaNa6+axhEwdZqGKLIa2Q5h+Fnl+mORkvBHgUt5sSL5eoMkOr+FsKZYA\r\na9Eu+dfjzy/17RIaoGkBv8n5P5ohQWuvRbJ5lE8PPQOrw2uYfBhVMwfTxfVD\r\nMYVyQ+grifscSKst/A4QGKjf/IoBfegzl9hMao+Vqa/qxxAAKGb3u7VrnT/4\r\nuNoZf99lpGBUd3+zWv2wmPdCaOeiojOqarcsE2IHPEh8S9qVUVlC2SDDEGRc\r\nA9vFMt89ZnX56sVLYHW2c4W6F23oT4on4eFvmGDePvZU+5+eavQSsbOlbd8G\r\nfeMVfWps/tQF3c6M32VtEYEyPI2xBIJYHbE=\r\n=eVWK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9a5996adcc8f3898e7c8e026ce7303b0525c8a63", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-9aO+OOaC4T0jN6g3DjbzRyaNJ0LvBrmhJSGJMQRYW6JigWCajURGNd2Jmsbh7bMfVoRePD8fuEsBrSsqXz96sA==", "signatures": [{"sig": "MEUCIQCzK5OhG8FygLlpgGZYFV5+qOLVe+tkREEmI7dZSl+bKAIgYoeyvjlccQpMCtx185K0XkgDf/NyqSyDd1hQEWDiUjw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRAB7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoJ5Q/+NbfIX/D7TTpSUdNgElEhbpfBILF3jcb21Ntgp8o03GjYG4Ye\r\nWhqBS078YQSFFpyawpy5pnVDpWdvN6RK7GSJgPL9iIy45uzwb2mQW6PVQLSw\r\ncIBgJefv1nQo1vF4Ic7GcifZcx2c7W/3cz2eps9zfw40HnS4yPaIvIV6J+MJ\r\n/elDc3scFdLNI94VDg5hYuawTqf6Py+vJRAaIH8uj1IDnriqYJzGlhtPinFL\r\nbQnv1hWKis02YytMVaHt5ikI4ZAKQbY51P1KSrkU4iUIC3zsb2QOvifr2X2c\r\nGOmdYHgb73vGAymBXtI1tgzg+Eew/Mv4bcJ08AfL/T93mQNXt1tSdXmPxmzP\r\nOpdW7XaNWALX1PA89Rokurrt171JfthK4TMrG+UcJLfcXrU9tjS5wTsiwQKd\r\nB1UGsxQYy8+/7FY8j5mNK97lXFvtkLJzto2+mSB9K0ezxPmvEiuJByMiTXn1\r\nqbzYmQRihe09p41xFHlJd15nOVQLDAriKqwkinE9RCapJGkueaGVyd4lv8GC\r\nRxh2U3EDNMth9M00JsclBtftP9OP6LVlNmRr20ADLma6Trya6lwyzPhrIxhM\r\nfOuR4B++SNHjYOZbAS54JxoOCh6D+EC+1hIPflMmioYI9zIM4Ahby0despM+\r\n9PQX6RiYgaHacQMh0gohxhLsMHei4HnDb4w=\r\n=uE1P\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e2cc63f6b27a38792e2e629a8245be6869d63b1c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-xI+oZGYab57fCa104OhpkbmOb35LntI8SGXgptRjqhHX4c2kf+qx1Y90dE4lXWBF5ztHhl8Kz7d98ZL5DZNBtg==", "signatures": [{"sig": "MEUCIQDjMeSl1dEzVNkkmjeX7w/Ggle/3PKnTj5oMRB7Q+Fn0wIgGeQleGKveVl1MqtOfuqVADUS5dXIQL/MDKMrSmmWRrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrhtQ//b4h4KiJKJO4Fnac6GmEfNWucYuUDgMXwmmqOpN85pjC01Ni+\r\nd0ZSCuzTS9p8maEerwHeTwZACC63YtwgZESLGd0oh8ZWoj8Bw/p/+SCqxrln\r\nlvUOJ+zX7VZQTOq3zi1eei2atxTea47kI0gQqxjPrCyGNPOdGf2u+/NhRKsU\r\n8g7g3ETziWmXOYoCr1QIWw0z15ji6HNWhKE+A0bPiuQYrqKK9JA3qb5izEot\r\nzO4jh5G0cwaN4NJWTlvSwU/2G4ThMP56fWLMQMmW4BShu7oCERcJKZlbPaGK\r\nnK//XgCJvALNO3NZ1q7VgnI1gy6fFFR5GSN2mImnp56Mkk8FhUT4xGxtmedS\r\nA+r9Hme/MLGq5FD79g+Jhshuj4zra8vi8Z4L+MrkwUHU1K+XZrRbrrNOwxxl\r\n0CKFI783AOeeApyj4EpmqJb5NK17vuUKVxP/+iu1qTvsPuszF7h/YuW1WGwC\r\nliSA0QVxU/jpmK5SrJ/rgaNKFKYLZZM7itWzesNkGwQJGCx6C4k0oC01czOR\r\nQ/TFPVkZ2B1OXCSIxai6IHwGdlvK3MHLzRvTYZI3mrK4RWTSghoEOE/t+qj6\r\n6w8jqr9saRu6uGR3mh8xb3ApJJRCS9WRA9M8Q3UOO5M0JeG8un6dshX/uO8C\r\n4Wl8rgAgsrZ/oLGYY/m2Oar7mTlhDt5Za0w=\r\n=aNcT\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "364af1e2c72f6c0a631969e79055687230ec533a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-v99ceDJOBVThfRp+9ploPpXm9MV7aYkUZv96uz59Aa+9dfUhzyEVCBrrli159FrWMoAcaPsSOzGmfKcirDYG0g==", "signatures": [{"sig": "MEUCIQDygUUD6byqc844NsBKDmfxtHgA8r09Ubuzrc6b8J/1IQIgCewF4VB+KIoFCgm0bQRNPQrLO1PRQHnyvbUQ4ylh7pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr81g//QNtKlu9CyQ23Daa03YtXJ9YAyPxZxourRLay48iXBosXSkTh\r\nter3IUET7V0zupJIUf71DHuxlZ4cvDOnXSkjqmjY8qu3Dw5iJMC4aw+RsYyG\r\nQDO7UTrSHKCKso54jbef5XQMfqIVDjIzgAimv8x5jnwzgDNGioGL+nH/vOe0\r\nkAyFV4OIRiOjoSFrmibtUzOn+hesTLnYppAez7wXDbc/W2BP21SrNmnC4yKS\r\n0L7bxAt1sb52YC5dGWIWRcrPvOghVBwJqSEWLgLo3ZVVPKive1Ddp0fW8ev1\r\nvcZGdzlj2VJ0umlCndY4q0VsvTXEh/RR3zu70tyr4gqmcx8YokxDzztFuaKG\r\nhj3ExZQGs4pZuPkzD51lr4JZFKp6LW5XEb4HcXwx3HWaVoGBCQuyB1Sh/+Kb\r\nN3IOvdZtDMNF6KxJo+wqOUThtRb/BwD3GbK4iw8j4JlJ64Yh5zUmOKigMEKq\r\npcv8IfVtG6oal1nPnalA3HW/ckcxgY+mh9XjZhRyc+RavuCQh7rflvOo6jTo\r\nNufrqRO+lxSOHdA9JIx/KGEsm4hgTIaCPa677fZSSetHU6sGX3P9fnxHUROF\r\naF2eB4YAQl3QiTVdccHThI5/fWQMWyEusKHr3mUA4q4MCBDZKcGGXrdGYCDS\r\nWRvP9cQ4BY1Nplnab4cQM+aYjFb5uZmWoao=\r\n=xcw/\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "abe0674f1110d1cfa0111092cd301251d4555605", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-c95x8zV0AnGHYrPEhoMGLFdsfrDEy6B0ogcD1hItWODwJtkNtexqtz60xGDk1CgJS73VYFLsx7HG9Qwp0lr7oA==", "signatures": [{"sig": "MEQCIFgAzRAOzy0BdWJ3dITE9Biw5iQ4UThj3Iv/eC3UjT/jAiBBrciw6ykWMKka0rgu+//t56Y9KBgUY3vux2nAQInTrw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreBg//VD87ew/09+zuDe2QM+P6dBhpHh39IKHvDnV8wI0YtJxGBrrf\r\nMtqJ5FgBFChYg/zeU5TJiOmWE+wry1gZeRmjUxQ3rPKsJ9Gu1hVsDxIxO3rd\r\nFS0y5A6Ju8wEqpQlnnRCi6yrtcxUDAZ9OECXYCr++zMuRg7D5dODfshysLhp\r\nSSg2QkNc5X+EHyZpdMhLfRJrlRNv3Aw3J5cMs7SwoieYGT6vSQKPY4H5qUzS\r\nDHsQfGTj1VSSiUjqKBf0EOSB/eNdxVndv9yfR5y6JJfEWa2rlE1ivEGDnJxx\r\nvFEyoT/OB2IHYgXbFiJA/YPj9Mu+k17qigWSSLXrHwF2N7ge1LZXqzs+Xjde\r\nAB92GR/27m7FJjG0feCZPJmbCkHGbxV7DpMohTfMzxlPjMo2Y77Zjnj2dNe7\r\n3Vls9DqmiEBbqc7tFMi3BPWigyxyejkaHHaBH+6fT1B4qQBX/dRsFmurhb6T\r\n3FRoNRNKCe3dx2Di2LqXAhJsA/vGFX4MWZTkSKnOacwGuah5XinqB5PCorNN\r\nBvN7YCknHYKmXjJL+Kv/DLQTpc7ATavFqLP7BFZzwl6eNAibYVanG2RsQT5p\r\now3Rf3Rluvs9Wk+3h2LGsLnx1YUNzl4Ksj+FzpV/e3oJD7ZXjqLZ2dJkb/jw\r\neLmLyVZhwwJuAQTeLY/ctFtWO7Us6AuEqeo=\r\n=wQgU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "99993a24b451238e7970c77ae0bf1243d8f2f065", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-lSxY0JibbbLGijE01kTsxHTOJRcG8UfnK1erxXfkpLb9BLJNqSuhefgc2lh+gjlKvDKq8M3fdne1JmelyXJ5hQ==", "signatures": [{"sig": "MEUCIQDdliu9KKzM4UaCgglWfu7Zw3yXUt16vBsjxcRIU0ucWgIgLxXD0zLx4FSWGHyqL4jNNLSIY3Rkdd19zbTDFLRFQmg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtrA/+J9+Zck+DPOHB9J+1Qr2Vyt6h6n/ljf/VEr2XSI7iUJ8Qrjqo\r\nQYhRdleGzn6VraE3xlqWldLFHLS7+AN458cgjwkUUrYy63Hdtv//XlAvPzDr\r\nKmRPLeNOWr5Kp72rlQjJ4lTLZ/mvpbTq8IqhykZTmKeLqqnBwcL12PBeK1ig\r\nCJRMqero8kJFIesss/s9Ak7okOXPeRdqLMocewL52n1NhC1FgZ3iw+7EEkY5\r\n9dhaoR6x44TYz2xjzJCZZkGCLfB3+ljnE6NEMmNkz88z5/elYX+wyyOQkzQm\r\nihzpsZZsfr3PamGfJvj0GzAqF89AcVFQvoKKyZYXcBymNKceC0fwygotFjDY\r\nBHIaDrXo54NF1MuCRwNkcbCJUZ6Tt97YJ4SxZ2+DNSa3bCL10fFrLCJCLVus\r\ntwfbW3XzpwYPAbxFTxagJLlCX27EiWtj6YYKicIbDL06TX87vnzRT+djFWzH\r\nl7IqabhyDh0HNuztydfdtey7RZFbFZ4Sj+5YYrX91p9Dpqf++JuN61Pb49yM\r\nEALf2qTw8EcH1jpyP1x3+GObKyyJ9NVzEMVlB/C78UEhmn2+XzCUuDK9MHov\r\nc5Ro+S7ztWpRD0j3h1S24MtHyC/vg8MuVTH8JRETaYmmLVJ/Gz1WV0d/yJgT\r\nKGB0Si5a6G2za+YcWje8CkwRTJVWedI/2aA=\r\n=wedB\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a0f469fc1f98d9d1510aa4b4a03301a8cb6be90f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-BnFQHc2HJEgK3R0OS8kiDB4ofqSMF+tN3OBeY9lncOUAbSdZSMyv234bonLW9h8MR5bqYi25oLVb2j4kRB9MsQ==", "signatures": [{"sig": "MEYCIQD1Yn0+pWEWG7QVHAMykmJddozgt+g1O2iOI4W3LrUMgwIhALXttc+cb6OnCvm3BXPJAPDK3VJmkAV65zhEXLAYp3G8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9UhAAhTH39uy97ecxcy8bLiSnXidEAb1BXg3n304IIF0YKDokF7Q9\r\nmPMq0UpOxQBFBDnO2RqK89XJf8ieeR5ax2eMiSYMc0o6b0sgkYiOZyC54G61\r\nLXGaHrHWl0fY8TiPhv0NZtQMM6Aupu4mPzcO0qVsmc+FkkMKOIqCbgwpGoUE\r\np4rn7ntUC8pMXTNqKCHfiKatbni9FmdkkIHBJfXvT7f7a3yKwOJAE8JhUPu1\r\nn02Yzpcwvtp9LFkROx6XrohyPOsQt8Bavm4RAAxh+phWmFmqY2WXf8RGmIf3\r\nx7cY6bz/23JhUqHbEnp2Y7RGgihh8suIpwv3H38jPFMPt++1LTkPG/e8PWsf\r\nqFwcOME6BrsAcgRwh4M3u8iOCY1mLK1q6U6qdMYWoDl0+/hbJ9cQfdALRSYB\r\n1ejxchaicPazjxAM/deMyA+iX57dH5G/bq8nb8WvSagVJfj6wf+AB5X6iM80\r\nIeVQ2Eh++aKw00i6Ch8I5eOEF/aPLJfyQ6j6k/raX1DUD50VbD9cipJTS92d\r\nmPPSjV5HrpFmag0cJfhV5s1Ny1PXXPvweqZU1c02sMLgg1BPMh5nSJqSGiXJ\r\ns3Uy2MOkQI78dF/6Map9VHIo/0wgM8mEWROgR9k7x1LnWPwvMp/16vfXScw4\r\nehkJKVEMVVgFokyvpnp6XAjMJcSnFtEMvnU=\r\n=xDhb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-portal", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13539674cb30a6feae9686a0f3f4de57edfc4397", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-rVkL5tMfX6QbXVXyx9HUTbugjBdp2yJk2HrAm2C0PevHABRnBwboH5dzL3uiGKKZxTSAMumC/mb7IJYYXI5qgw==", "signatures": [{"sig": "MEUCIF4iYmEyJ7cfYaammBPceIB+y4QoIHzITqRtgs9dToHXAiEAtpcAsz3RB36c7+fhzlkLu9zhBGDRrVWCrgqZ9/F6X2w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10579, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRe/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq+chAAn+ofEiTssNJjm9iEUqvROIeCItpRO7d9a8/S5E7Phou+wj+U\r\n6fMez5J1quNeQ6FSiCG49pKpvMx0zGVp0G1squYDcyHIcRYvAKhkZ7EdT4ka\r\nHl/iPA+G5ZtX9zjx0JsNYMx0xpabkRi9OwtF6DDXH2UnY0geyAkzD2YxC1Mk\r\nigpW/5VrLYhgsxcrQm4az427/kgkRTLTy+8GgofVyNyrBS5hGUikpwFXKO7T\r\n8iNvXCpVTGx93hJdlgQnwySRlTp7xG9g+ITlrr4hxjB6t4O3PClsW2DuULwm\r\nLaDRrrRgWN9OjpnKX1tAo/tXBvDAtzjVuOr4xOvEJbBWgu29Sj93c9EptYbe\r\njl0bq2LUWlDRScdeBGQdKFj6nlV3mCXQgvF7GEs7ueA/HpTQXFVFXLeUoESn\r\n6RdvEeLsD2ML8jn52fS5aRD0EIlwJMEwV+Ype9Rfvubx54xG8IVSxwzmbQOJ\r\npnkOT2H0Wqek37oczSDWirg1wsP+uiXMiqK4z4SF8yjfMyUkKyPh9d/fdcTh\r\nIgB4B1FufIkEoIqLekCfd2eaGwcGA6J53rKFYweyUkrB56L2PpgZLPo6an9T\r\nw6gFqFK6HNEOxD7Rw118oj5MMSjGrpY1V+7K1rjgB0QB02lRMHOl782qjCax\r\npoVsz+PR/6fnp+zbhI365y+zUrAFIYjnMqk=\r\n=gONH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-portal", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "169c5a50719c2bb0079cf4c91a27aa6d37e5dd33", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-NY2vUWI5WENgAT1nfC6JS7RU5xRYBfjZVLq0HmgEN1Ezy3rk/UruMV4+Rd0F40PEaFC5SrLS1ixYvcYIQrb4Ig==", "signatures": [{"sig": "MEYCIQC99zYvY2WLXcz0jmXONv1sAjKquLr+CK/wdXWZgDjK8QIhAOSeYloJPzlsPym6eGrXPndWUuQElyffTM9M3+CEhADU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoM9g/+NZ/bQiPvxzAlR/iRDahm2xf4soMRuRrbHly8Bhxm5QgJ6i+j\r\nMUDu2ez7ag88CfWbixc7HWxUqUnJvKZhe3sm/gMh/w0eO5BZEusxzSsTem3G\r\n4uGflp0HIKUc7HiTiXJEHa9tccZB/Sg1yktWabILgp+TdHQyol6FFo/yVm1o\r\nLUW3Cibp8cX8302etC7QCnvmNYbGS+z5C4L5yd4o+0ZrkEfN+FM5ma6/VW0W\r\nkvvkz2GOxm1KRhwLXAVX3FAoW1Fjb6prgA3QGSeWD8sAN9XM6EB4p0kfed8/\r\niSbQzjmOgtZukaAK+6BRkM92zc06mFwwxNhzhxXO42xuLT2ggzzub1V3T0c5\r\n8WS54/LXGKlYZ8eOuKxgt9VRf6TeyURJNUFKgd6Ct60NbEXX1DcKlzLxcigl\r\nH3XE1idapUKXmajIp6TP9+k+Dxm/r4G4JhRxf5iuDO1IhkmBIJ8UWl32cLej\r\nYsE34+oRpZVvYiGijm887g7hPzgviFiovl2ZmU/f3/tT4fHmWYj69WMqt/yx\r\nw6c7w6JUgLNnIHlpXfp9wWmdboG3x+BiSjoM/08ZP7qhy5B8lzRHh11BIbPe\r\nB9pQFWOCT7Y49zn3M1s3NRCnIauZdRaeBnqYePKJ5wHXYBPlz5OGGGMHnSHq\r\nXPz2yH7NJr0uiRqtjpjUiswMlf+TJ4xr5Gg=\r\n=lm3J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-portal", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.2-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ee60eda547a479fae40fdf7ad0618d24adee8c5a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ylMfq0m7nkVJrtc6h16hieP1Mz6hmOJxzSZNef+9ilQxoPFYGV2ygzgB7Iajrn4Xim6tM/tqWOpGLpB8i9oyxg==", "signatures": [{"sig": "MEQCIFuaFSI9EuVCreVetdHNhfjFPiIkOVTuK16OOT7bqedqAiA0eYsqMS1PO9BWcwMvUu5Gw8JFZt1bz+zdhkd3ibGgGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodVA/+MTZJKb8Iw2Ee2e/IQL2GATKENrT07kPaedoacEiKnLBB5jOJ\r\nMDs4mrUgL/7Is2CA6WkqdjyXQamLGK5F3vUljSzvPmMHs93bXBH9JMdZ1BG1\r\nInnuUWV0FnyAsenycXgBWzcuN2FtJ3tLGtP7vhbu6tggpAvf9irQNyPeIIEW\r\n9WH8G80+sgVqADLFKrFGvoPKoMAiTOlxLl2LWyHETA8RhFC2HTjbeV9nL+yg\r\nK8x9Il4tePIwtT1Zw906MN42mBQF6zNuBkYl/JXh0wPm0NsXZroPt4KJa3Gp\r\nYIXDUEZPlbYKrQOuMh+66mBJG5gtEqvsHQkO822NDNrNhYoiv5Uez67xVtmK\r\nVsQBTQnKZm4EfCVAFT4G7KuqDXgiKrsEE5nU2yu/ZEb0vO+dqFvChAJNmoAz\r\np6SM3nBrr17+vjkxYK7bpJKwxfjuQTrKghZpTrOb9Qx0zK9KA3uBxPo/XnG/\r\n1TEuNxQIiRwKQy27WV1NKm5mj9X38grdzFGO5P7EknzyOe4bXyPB1LwqstJy\r\nsHwsPxqxrDvgZiLL3WE1n4VCQDEvgbknuhDGKIrfm1D8V6/gvBOufP/ecC4x\r\nlA46ipAsb5ZTSKLlt+7EBzKdauNm4s6JvrI5pUTeuBL+zWHIHgCfUA0qteS9\r\nUgfpURUHwV5uGP5pYXA9dd4BH4NQHLqPO6M=\r\n=sdGe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-portal", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "102370b1027a767a371cab0243be4bc664f72330", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-swu32idoCW7KA2VEiUZGBSu9nB6qwGdV6k6HYhUoOo3M1FFpD+VgLzUqtt3mwL1ssz7r2x8MggpLSQach2Xy/Q==", "signatures": [{"sig": "MEUCIFvy4otGPfmAizGoq60Y+Z43BgbcXKWwgRcYrpSF50YiAiEApr7BuXopLV/I2PPEqxa6sMSsuQVQYI/ntA/mVZYAk/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJazACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKdw/5ATTHfQneMId+j4kOtd3iesJY37rzO2l04BD+/Bnf286lV8LJ\r\n7ODBj3gqimbYn+NFa+IMn1OdiIxd7cUoAIQHfNIm0CljryMAcEvugQg01gok\r\nDDyq44ya866MW5yro6oGH6EmiYWK8k/6Bc24ZtoRq/BxviZ+Tf9q3ZNtieOF\r\n6u67uYYyaFogoRcFrIxL7Epa3OyoWfMXPzdOSkFHXNdlIUyB6Flz8zsX4xPK\r\n6reui0nzQDEUeM56ZOhY2zQd2ZBzZFPO5RE+e0x2ArGTJa/MJhwKrw/cMo/a\r\noan4284R5U/9jLuQ6WrQgGTC/Q3T6AposHdqkC1PRnhK7upHNZbpxNmcEMF9\r\n1uEwioXAepVHV0a2X4cn0GRWIBsVJeYTsMACIpN37LIkkpclc8/HguJD+9UU\r\nakk7O8atW55Gt9TQ6OCl01DoSMzlfWuTF+Ee7qrty7nUawuhtAVcAhGYn8Gm\r\nRkgVZ97urkRlBDvsnJxcPeXVEoau2JZliBLl3aMieQmjk8ms3/7yB2oe8t4v\r\nVbs1wZZPrTPmjflrPi44GOgXWBs2tfCXsUut0J8oBhSu6vEU4L9qP5Oym3Ys\r\n61M1i5lNya52oCBSHXRVawIzidXheiyRZ+npH0wQDOpwCkREUfUSo8POnzuM\r\nZOCWG26IPDrs4NUtO71vqM3gEsF2EIC4ElY=\r\n=dyFl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d2fb285c15a44659cce6002f92ae398e8f88dc55", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KouIUoue7ELtfv/UeQ7wv0ilXfWInDXpVwpEAjWzCmp2ze7ytXCSIijxD3BN4xfJLng3KepIw/xgjIxFbuhDEg==", "signatures": [{"sig": "MEUCIGOYmmFIm0km8OpXU9q8MMH5v+s3Ic3WU4phmy6Gj3p9AiEA1M4/c+3b5HfNFpwxRQfWmdpWB8C3mYIQKfeTpTHx/SM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNkBAAmlSIEYzMFzAxdF120r9t6BwRKxS/93WEwnuZ5iuaj+I8Lw8T\r\nTMW5cI4wEA4cJ4WCOzB+lY7TenIO0Y0GrynJJWQSzmGa/+dNDPdF0ClRS7if\r\nuRbu1YTv6Zv1SZtcjipo26eNwmdIfPFEWT/KOMLKbnWehL3sFDPrNUaP/WEs\r\n6yDqqTnkxq1DqvSpLumYTNG7ogSQy4nSNApntD7CSedaxiCMVvKZt8NC0h/E\r\nqwEMDAAuvhBhXFcTCYikqGioHHY1uakYy20RoWxSFr++WFDGB5RCjD/SIkVS\r\n0tGQvCDVnktQ2YP690z4YeXP10dWF5Ycai83SUxSROeI0r5kLiJ5Af4fFDwJ\r\nymAbYVUq3clXYPDo0iNICKD4AeMG3cJWxBMv5jTYyucsP378Rh8OTYKiahCJ\r\nD0SNUzz7w/K+DZanWmEJn6FYk9Qpmc2Qq5W3Z9Ci4ftQM+9o9wO2LC0StUs6\r\nxiAoUkPUbkBQ7z0P61+4vj0HDBfghUPPO099KhAIYQ39fRUsxgrPY4copMDi\r\noD4AKEWz0aWOG1UUzCiuYpZIuRMry+ajbjHlLxN62TowYyA9SJt2sPrPx/Qh\r\n525ehFIsryD7NrqqDPN069kWURpi+tZxiCEooPYba1bGOrztAGyFTfe07RuO\r\ntxlkpG3ihI7FzhXF1t3sj8/zfbrQei68EWE=\r\n=7x70\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3f84593c1c0f38f23601ee647ae144fdd6514f3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-oKilrwsJyDEbXbdcjdRAeDHKPyqjyGxkjbn7O1p4KP8EjBtUQyvbVXQmYGNg6JJRvV8opXnMgCFJGN0n6jZRRQ==", "signatures": [{"sig": "MEQCIF/VWhlVF08DUQwX4QFJkbqejWUwZtASkkCiJaZW9N3mAiB4V1EjMQOI/atyj0mMdyOdFGz87NlEbZizlzkc4gHqCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577}}, "1.0.3-rc.3": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cc8c4ebf013c5ff40a78b0ba58eac935c33d6e9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-O0M7fK8oypZEzW+HbxSQxYnN/yHWR2vOliAlxwC83LTUbNGSur1FbvggAoSpiIpPiormhg1kfYE0O+D2ZjCS4Q==", "signatures": [{"sig": "MEQCID4oU2PjxsRckm1416Y8CDLzaXCWhZSO/kF/zht2+0WBAiBo+4Zh3vmsECEZisWQA97uv6T5W4XT5wByLjnk+gcyZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577}}, "1.0.3-rc.4": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d89e7384c1b3efc692811f8fa2fac20b990b080e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-VgXsMVqyovmSkRlWfe7U6P4q4gThCV4S3Kg9xIOJ1f5MIXJLAu7kcdb9ZZw9NyTylYBgVTkYPeeSxuQ7+PbhUw==", "signatures": [{"sig": "MEUCIQDHJOmFW68XEvtjPyAqsxcD9/yVlsDod4Dg/mZ368yIBAIgPsSGbsEARqUyim1Gz6KwKaw6jyLkfE22EWzEUDgApEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577}}, "1.0.3-rc.5": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "855a18de5e01f273d808f6f707531961e7aafd1b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-p5Hkg6xir4t0fqW8hbyyZ2ABNAZ53Zg79ei2ffLdzIBR3HqY9uxJ4l/TTzJHYMA6PLUT6WgIuaAKKkDrl7J3rw==", "signatures": [{"sig": "MEUCIQC872vAUet5ajLQXrpLuUUBnMBP2+1SGPCP0s0IxJ0oawIgA12lXDsquWAJnqLxjEsexHpbdCFXAYBTKQS7d9rImTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10577}}, "1.0.3-rc.6": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9651081a47ff1bdc3c236c7fb9f7a37a8e044ac4", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-1sFaIZudJcVA31kqTa71JV7kA4dx2o/VRw86mEivfgWnQ98Ome8adbVJ9s9HoDJqlbYrdelf18WbVmICIWxpnA==", "signatures": [{"sig": "MEUCIDdODDT8YHFbSkCNcS2w7gno9iggFq3CNSkMtZljOdyqAiEAuHl+pYW1bvbqojtvF4g7cIZQHkcUbiElNFt7dpeVuSo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11368}}, "1.0.3-rc.7": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "209e92358f68922c3f5fc112ef44861c78a9fff7", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-EQ30jnRxrgpfEtPvhIpxXsOA3Q7y9bRSO8Lc2TQspVsr+yHFa3QAdSE2hhLvYr2vS+CBKxUos2ySRs2Cms5bWA==", "signatures": [{"sig": "MEQCIAiMtMrTOHjfg2O4ueqn/nuP7YrR+DD6HSku7wi58RAOAiB/pHfLSOQ+BWEFQYkb5fVHZ4+2oHqZG59Khz2iOao8JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11368}}, "1.0.3-rc.8": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6e43fe1cd3bff3c9e513e1a91687299855acec4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-TQD0dqnQwEe1EgDdTEPLZsNgBhP/UKF1vFa/9dlyjCXaRpkvmNFhz9DPbw/ZnbubvuDO2KkTajQU5FcfQ1m6Og==", "signatures": [{"sig": "MEUCIQD90mzYUyszMAg3we+HArrj8j1jwlY+RBAJxAYOFZjniQIgfLDt5eJzZ+ZfEb0tpbRFNqATtV27EMhLkM+hvWUzUcM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11562}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3f742a8fd9956ca5ed5fc134afc8512e0242372d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-d6s8ZJgUs0gimZC39ncUiCPUuTu/0vwkXht68cCXykKrFzaMdnl85FM1vsx2SnrB7eFRgSrR+EIHUotnrk6NYg==", "signatures": [{"sig": "MEYCIQCVxr0EzlJcpOIR0Rq2O+BeuazcYed4Ico9IOw+JpK+0QIhAJ6CiNBirwbsx7Zym4OpvntCfUiithgQH3gZ5uKJx7hA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11562}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fcecc0a36dda6f0f21296753f6a6678869ba6bb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-0y553QYvOGRiAgBKOgQL69PmlDJUETNQNiQsc3DYs9/dKZcfnlfDNtNnFaOgvUs5pN6vIo2PJ2ZnK/pWaPIHMA==", "signatures": [{"sig": "MEQCIBZMHl/eBr3INBMMl4VqA6NCrP8z5yzF70a1Ozd24N8gAiBNgnHQvAHjFAsrEEOBliI1QCNX1svE4qXAu78tYxguiw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11564}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-portal", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "44bb06a5d7ac994158929808e0ad9afd30c7737e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-gL1EuzEFErbUW7S9xF8K7LpfoBJShdkWpFdKfbLfXeG+JgMlugADYOgSGm3paHGqgJTDewGERsZU724KnFTTkA==", "signatures": [{"sig": "MEQCIFXd9ibf8s3rgLqAQzZI73HJHROnSAkxQnjRbTm7p8YuAiAODlNulT+RBDDBaA8QcYYhk269PP72+gJY2bNZSt2dOg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11564}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-portal", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ffb961244c8ed1b46f039e6c215a6c4d9989bda1", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-xLYZeHrWoPmA5mEKEfZZevoVRK/Q43GfzRXkWV6qawIWWK8t6ifIiLQdd7rmQ4Vk1bmI21XhqF9BN3jWf+phpA==", "signatures": [{"sig": "MEUCIHYIgU76uZ7/LKMXHVVa9+vHmQCYx8KlUQi6aYR9f+QNAiEA9ARE9/zlkCtRgxZ18DRDIf47uDrXYnIfhgA79KisN08=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11524}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.1": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7b43d031de999dec3d8333385853e92deeea982b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.1.tgz", "fileCount": 9, "integrity": "sha512-19EjmhvXQ5cuHmilr3QV0yrASqHahv6JysHWb+a9nOcMea7yPampGa5dqXjtjXdyUzi298q/P4s3GhkhObzuWw==", "signatures": [{"sig": "MEYCIQCe97zsLVHgsKi/pVUEG88TIeQSpLoeuZX6JtJ0LmpppgIhAMcchK98lWY8OVsyDKpOXsKFosnxEiMmxLnbFk8DSqK8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.2": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5bed8c6378d482d38cab819a875051df61549e03", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.2.tgz", "fileCount": 9, "integrity": "sha512-u+m1WYPDyDWy+HQajwZw0Gj2BM5D/jEIh3QkyPVxqmcZPdpss2v4/Dho8e4gt4FRo5EzGfeHo2wFgVXfGXVukQ==", "signatures": [{"sig": "MEYCIQDTEW/xCftTwpEz3jX6c3qb281SblChkCWliYVudmTr6wIhAJ/z419R0RYieD1I7JtN17uodSS8Dx5MwXb2Vkm6JtOA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.3": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4f7a3206cdc9d876706b88e4cea5616efa016b18", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.3.tgz", "fileCount": 9, "integrity": "sha512-bRAJa5413sH5o2151norEUca/L+6mOpDcNXaDwCu2tZ7UbTORPrNCXnBMG6vYIR65I8uwSlFI6I1nk8GKbwbww==", "signatures": [{"sig": "MEQCIGTvO5ma1PGGIT8V4KvlC3zO4StLky6GPDZzho1FSYhrAiA2XURwxNGdxDf5Yws8Fenozc9Y827sscqe9ibaSmq3Rg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.4": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1722c840c82ef8e1372229a54dae8d4c201ca433", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.4.tgz", "fileCount": 9, "integrity": "sha512-VZcEDYvJWBd1TZK8jr7MJe1amhzXns8zDd55B5ARf7/TwECfZTRpPSeOHZOfDWAE6Kkrzj2ESwUMPMwNPVTFnQ==", "signatures": [{"sig": "MEQCID/TyurkqYQWTCm4FKfkIn1rX5QmgXidR9PQpyChCEGAAiAZwKVzr5aIGPP0Zn8/Ppm/ZENlPl6Y6/SqyVuaTrSY+g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.5": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "64bd7e9fa908ca3335c951c884c89c6f8f9ca866", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.5.tgz", "fileCount": 9, "integrity": "sha512-6oKhxM6fF+hx9MedEajXBYZ81jAN82FZFx5srtj+OnHRsBOzKMxkTgyetf45wwziUqg4pG4BNsrqEe0esy86Fw==", "signatures": [{"sig": "MEYCIQDncvAn+Y42/klZYkYDGyReNPwTzhTumC7hmXouzjoyDgIhAMyyfsl66L/GfdEO9mqfl3/axVoLKzNKul98mPHihi6S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.6": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8a6f010d5c3951e46ff4333ee1441d2a1a70e475", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-ze3D/HEolc3NbYJuL5Q+bGBEoQeyNPRUL/ZeKA9HafEvo6ie5Is/Ps41Q+A9echFmq3mb3NHT3RfoLF24whsPw==", "signatures": [{"sig": "MEYCIQCKllgITXeU2F+M8rKaaOaMMBtskShLUChbTS4UCQvp/gIhAJIa9MeTRfBSFoShIja4QH8n2xKM85IredyvTfFi30+2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.7": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d242999c03021b5a1123c041523665526be2a60a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-DWx8yECkaMIrLFJ3XYLxhQ4D/AbGHfFvWYJdjb9jTkyWIFLzCSb28QLpVv5jAoBxKz8FJL1T98u3/tCRNE+MwA==", "signatures": [{"sig": "MEQCICJ76+CmuCwsx2Wrn4HhBHhC1TzdKBdHfDg5K2/Und9RAiBrJZQ284GFG9m5BGF/AS0WVzcWT0fFCKPWrzX1ahfyhw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.8": {"name": "@radix-ui/react-portal", "version": "1.0.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3f5fd4b5cfd51823bc6fba8ab16f5091fa25c93c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-t0t00ZHorOJ30+3VGR8QSUlqRHb00lk3sf2L6GKFtqJzDncPKlotBvC/OwR4t3dMQ4GHLTrwzrp7TpodDxDgHA==", "signatures": [{"sig": "MEQCIDPZwb5OR3Czo9bFbzAd8OVJ2NjwSQS7mwCCB3QFmT0vAiBbREW5Z7NWtm9uI/Mb7GZk/MiMUHBddSklLtrEPtzIOw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11930}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-portal", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "df4bfd353db3b1e84e639e9c63a5f2565fb00e15", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-Qki+C/EuGUVCQTOTD5vzJzJuMUlewbzuKyUy+/iHM2uwGiru9gZeBJtHAPKAEkB5KWGi9mP/CHKcY0wt1aW45Q==", "signatures": [{"sig": "MEQCIEZQ944kdFci60tniozH4eITMjSccwgLIens35jZL2k9AiASdq5ps/75NpS+GmclGM5Yv0TWQFEJsT0qwK3jVPjZNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11897}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-portal", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1abfee33fc03c2f3da0ab042ed1db8f8e856e824", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-9NTDuPjbCC7ebu3ebTO8DjuOafOQFy6i3SqSBzUbd/dITAxCJ0mtZQ0BtkdcAKyEAVRhyMMpP7ACfW79I8phgA==", "signatures": [{"sig": "MEYCIQDFrXxz1Q4GxSlUosSdh1Jxm8Bl+KqPWHp3x2gR8afoTQIhAL0d5ApWHULHPAwozlPdWwkOjmsTB1UNttf45Wex87zX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9161}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-portal", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5ed81c67fcac7cf9830b2b27194e45d4f0dd79a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-6vm1FGgrrEtuHtRP/6Dals6OUmfxHo1e8W/c0eNvlSzjAQ+VCABuqNMcJF3cY19f2Nr9S6txTTJUVJEMRWD29g==", "signatures": [{"sig": "MEUCIFnJMDac6JUg9JJHOdi9D/lqV+iKtyjutGIA6O4gBx1kAiEA6PTphrbDIFEdURBBZEipy6l9G0Lbufz8DINPkUMb/1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10438}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-portal", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7f2b5e41de0094114ef55c23d0b08623f3cca6c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-AC8iA8HMJc6EgtNvG759aLI0UjQk+Jd7SryHzxa6y9cKdkmx9kJolZw4JC9R38T7sxDjnRcUptNWj9LHXq/gOg==", "signatures": [{"sig": "MEUCIQDsFRngNrkDEI+A96i6e02y/OerAM4dgow2WvNphdpFXQIgZm30jPno6A7g90ef5kC+MbB+mm+WzXzf+OVTvuR0OTE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10521}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-portal", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8bedc0d811a95fd4a74bfe85d6f178be228874ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-XQK<PERSON>b26oP3JbnvmL2S15n//ysn1ZGVcRxAhV3Pco26yGebeaUkhay/qe0LHbiiNZkzPGYZazoF61vRtZ8eFuCw==", "signatures": [{"sig": "MEYCIQD4SxPer8+hAQU2NfIFLkIBps07n8CGafihHhj+0/ZJ+wIhAKY3LYngbrwrWqkZ7ilZ3iNhQ1ekighyYV8Bz11qaRo+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10299}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-portal", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f9cb61288bd53d53c0584c9965644bc61153bc1", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-YToH0ddkTzygYcDiq5zX/6by+JrXu1drF/SaghlkNycJ5/pIa2POp+cz7xVQxcGxhd2r/MmERYtSCU6HXmcEWA==", "signatures": [{"sig": "MEYCIQDeqk0XV3HyvN7YaICseDxGHae7VqiTfeBWZoSm+TbBDAIhALl0zDdps/tu3DbOj5vVcQpLWYY7P45bEXkXfgPS8diX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10299}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-portal", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8699c963031a4688e7ca5907d54850258398d06a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-enDyQo8CfzHj+cjR2O3Gu/W+S4phXhBew8UHLXwCPEX4UvKERVBLg4NpA+N5tNsgVkOLu8Na7dgfBjNO0CaHuQ==", "signatures": [{"sig": "MEUCIAlbFAofCesM8VRp0+guzQovRRLFRe0BLyVcaMsB2ewFAiEAoBsuWxqTy+ElQn4o6km6Tx5sYEK8KBSPEf5X+UwaZME=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10299}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-portal", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a5ad77fbc24497bbc67793365d5a97cc93275062", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-gRo6wvFNzjkgKZ24wdCOgWLSr0NUiPSn6J7Hek4O4UQsDSkFCaGjez9Zmt1RvrE/tMAlhaaqtUZMfFCcI/qbUQ==", "signatures": [{"sig": "MEYCIQDbuNQ2Wx5JfN6AtUDBhq6df7qkakR1HznVVvgzMDyVlgIhAMJSIWRMjCfzrnV4SVOvg8f7hDlehXbnnRRdBm8Mk3Px", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10327}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-portal", "version": "1.1.0", "dependencies": {"@radix-ui/react-primitive": "2.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7d8591034d85478c172a91b1b879df8cf8b60bf0", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-0tXZ5O6qAVvuN9SWP0X+zadHf9hzHiMf/vxOU+kXO+fbtS8lS57MXa6EmikDxk9s/Bmkk80+dcxgbvisIyeqxg==", "signatures": [{"sig": "MEYCIQD0upN1ZExH8Jl1nkDgvvvosZhl7HdDwOOPhl/ZXWtyCwIhAPQXT9CXiotLRoH+5/pPYshhWTXq8WcbGaL1AKcsdpY2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10289}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-portal", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "327658acf350e6b759ce17574a0b0088c7b773f9", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-74T6b0olp7n69qppuDq8BhbiPzU6RsDHLeRVY6Jk1rL0HlD1RcuV6Z55wPJAduXtVai0TaYW9jmKVi0unplV8A==", "signatures": [{"sig": "MEUCIQDh69GOUc9n18L4ki/AdsF5SfMmvvvq9aF2aRGtto9vJQIgL7lQKrjrrIqRQVfS6iNBiSY6umPWkOSZPmDPmbLXZeM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10372}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-portal", "version": "1.1.1", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1957f1eb2e1aedfb4a5475bd6867d67b50b1d15f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-A3UtLk85UtqhzFqtoC8Q0KvR2GbXF3mtPgACSazajqq6A41mEQgo53iPzY4i6BwDxlIFqWIhiQ2G729n+2aw/g==", "signatures": [{"sig": "MEUCIDktvXXc5uG0hK/00GRVLL8Os2FQoLIe1DHOhhMfPAw1AiEAqlDtHRCHzFuwQ3PwxnJ1Hvyv+icDnQgcP/LsjWXJgSY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10339}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-portal", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "191b4a61097458c46f40c8ab8092cd39bd20f9bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-roB/ETuxb6mk9v7qbIHTG/wNoCkrWCyO58YzS+rffJNrgRLbIQEikYOQxFoo+s60kDSNAyIETbJpMss5uh822Q==", "signatures": [{"sig": "MEUCIBTh7JnYzyXfd4D3v9QPHrkVa79VjPSbpHkmtPCW0qBBAiEA9ADDSCOXvuwq9dH77sQiKtFHldAIcIzd4Oc65caw7C8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10448}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-portal", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "356dba771d558e3f30839e705fcf8a33b374f5f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-mx8pHns1E86NUf0Dc2BK7Xk3MmkuDNev/0CJMOfssHNfy5PIWdKMmgdoQliHilINKEQmjyfjfOFuLwZJczjXiQ==", "signatures": [{"sig": "MEUCIECwMKAuGA0f9AKecPtCx8FOPtjPkZbR7tOgXBZZh4eRAiEAiW4XvCjCCu/W7N11Tu3a97HL/vmaNqAZTtLi2RNIH28=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10448}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-portal", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bb5ec04a25777332f0168daf96c2c2ede4132d4d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-oGgHJbIRTiWqO5GyhBL8Q8SliLC5cnhrVxh7FEZKW/oZ8cYYeCbMnYHzO7OZ8yFceVw+cshiU6KRAlaXXsMlUA==", "signatures": [{"sig": "MEUCIEgxXh1rSUjevgFUXS9qfkDkwxVsyKfAWnnMR1/Fdq5uAiEAt+2l18kDQVTaHEa0tXTtqivrVw4xN+VrhyBxqXnxQsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10448}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-portal", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b4b3f807be7c650027d371cac798035757a9b248", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-AUNr4/1t3VgqDHafk707QPkfkef88iBC+mTXz8gKSF6C7rkWWLwuz/Q9L+qkRSGn534GJq5zXd46YjO8l8DVjQ==", "signatures": [{"sig": "MEUCIQDJsCOhndH2qcsOFxKy8PgLQK0/Xqs8BxZtGQkwd+JPwAIgSsANqJHrgt10BijautvWPcTs0AoWCIw/nqUKeOSEVI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10448}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.5": {"name": "@radix-ui/react-portal", "version": "1.1.2-rc.5", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9fdadb083c4ce5d8f566aebbe0c4ca8142dea26e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-s<PERSON><PERSON><PERSON>aZuasL1BNPNrhxYy4Tx4QfOWQa93F3p4n23zAmT4WaUNlPIQ+A1guS97RQoFNQRJcrOa4hoZ5JwM58VmA==", "signatures": [{"sig": "MEQCICgqMkCVHWLtkaf4Vc9RwVhiauV2NLc7Hf35ORN6y/3fAiBpouxOLemLX32d0TSoPGy44fc8gGCCJBNxuCPB72QoiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10448}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.6": {"name": "@radix-ui/react-portal", "version": "1.1.2-rc.6", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a15eae2d3836953855de9b9693efab72d8f9bd2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-xoI7JUNlIou8dp0cUQuvr713r/OgBm9SZsk7wzgsIDVpycmxc4viMM3DVWC9PfUA/69oMUmEPIb6a8TNsMS7hQ==", "signatures": [{"sig": "MEQCH3WZRELuzKZOHpFCWLnTjIJiJrsFT4DTGaWQstKXTV4CIQC37pj9FwafqlAxC0vu6heR67LIra7XRV1W72Kuu35CkQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10448}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.7": {"name": "@radix-ui/react-portal", "version": "1.1.2-rc.7", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1de8c6130ea848e8e855eb9bb1fc8a9a3823dae3", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-rG<PERSON>4coGWOO7GjGUW6jQ56y8tA3meCykrhgKRE07ef8B/Tzp5dWwQ8G9gABT7/FN+z3D4gHlh2cGweb5tsxb3wg==", "signatures": [{"sig": "MEQCIDD6x8qAUFMJf+9jMp7MnkCj9fgyi3RfXVyYHtLGbpY7AiBLnR2mSyagCGeuYWrgkGyiw+6K37/Cz9S6tEfPLLmj2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10448}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-portal", "version": "1.1.2", "dependencies": {"@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "51eb46dae7505074b306ebcb985bf65cc547d74e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-WeDYLGPxJb/5EGBoedyJbT0MpoULmwnIPMJMSldkuiMsBAv7N1cRdsTWZWht9vpPOiN3qyiGAtbK2is47/uMFg==", "signatures": [{"sig": "MEUCIQCQbAaMX1t4khS1LyBqDMvIeVPrxjmdC59cKG1Vj1q/0QIgIIbrWCEbMv3i2P4koV82pfy04Mzq1GB0nl8oSP8BVSQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10415}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-portal", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "df472b6da12cfbb55051d82c8fbf9a751e5c3c83", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-AOtjqDg4L9gHSrmUwbuVoPdhVuYusyZCcMpBHgr4GF/YBk6FRrrvyYmA4lMWw7cSQfeM9POBgSDg/SGvgTR+DQ==", "signatures": [{"sig": "MEUCIE9Jaceb11aWaBMdASWRaJVbyh5Ar1QtwrKe0LZuWPZAAiEA+PrTVt8XDhlAnk/1Y7bAwNBqFs7bd8m1AXXfjkFNpF8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10453}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-portal", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f967e2ab48281bbe1fdd6370e9fc0bc4a3e0201a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-08phGrNwDCrCPDRyEfT3cveSODOkJYST8nHcbgC6iRMyth2Sb5GK0eFOBcE6q1dMnjzjv7eARWOwpeRxl7Rvfw==", "signatures": [{"sig": "MEQCIGl+gm3aZsiI+ad2GIg6zPUoPC7MDNY9kuuLx2kgRnDGAiApQGyGBpFtpqQl6OKjamrLgvlgHogQ365pWb6b9dtAfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10453}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-portal", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "90e2a27110899a296683ab9f1f1224ba8d3058d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-1IZOJGtBX8sHQVVVW7KsH8qT4zbNPJNP3OMg/dMPRsOdl/ww0AFAVrquaS2Xm6+VDnZ41bwX0oQFlfmZ8eWOwg==", "signatures": [{"sig": "MEUCIQDb00Lzz0SBs+FtJtzCeiAGosJGOHcwAuOBGanBqKC3WgIgLfFdpzGUjkwjS8Brlcp33px8YmL8SjTwIKhKhVIsRiQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10453}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-portal", "version": "1.1.3", "dependencies": {"@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-use-layout-effect": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b0ea5141103a1671b715481b13440763d2ac4440", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-NciRqhXnGojhT93RPyDaMPfLH3ZSl4jjIFbZQ1b/vxvZEdHsBZ49wP9w8L3HzUQwep01LcWtkUvm0OVB5JAHTw==", "signatures": [{"sig": "MEYCIQCJXTJPv6qX+l24VP/dIJx5L2zUiKgP3yK/ae45sZhtsQIhAOBeSUEX0elQJUjuBzAZxadSbOC69n6GGu0ohjh4dAoT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10415}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-portal", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "225bbb6d9f66fdeec8eabe60dc2c355c675a90b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-FrbofLznK+b8T8sUSHlyR7LRNlEnJjWNRUr36/siM3pvOqff4mXtfeEs8g/2x9HEZAiQzUgOqWPEltxVzSGDbQ==", "signatures": [{"sig": "MEUCIFaarZ4ObPdhpOdFZZ0MTL1opbtNpVo1EP0S90pITCe1AiEA5J0sXyC1hL4sJbmBVVzO2yHJqtf8qjkkKvjNDphbMT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10412}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-portal", "version": "1.1.4-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cfec531ec705adf5aeaa5b8ac25df7e6cfbb0c65", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Cl0t3L+9lHwGFU6eK7GZsV5TYCnxy5d6yJbHSr4CnnHXEy+tAVBRzn8WzTalxPR9ihZirDeUs/w9WthHeu6TOA==", "signatures": [{"sig": "MEYCIQCa9T1xknSAV8iHNNaqsvB70I1IrmzJ7AXiVr9iJxYrtQIhAJF3VW3uasTyOacy5bATNxD/DA9T4Q17fHd2SdxYU0Vd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10666}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-portal", "version": "1.1.4-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a6352a155ee3f671b17964552f32d873c5ec649b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-2HSAI4Nwibl0Mxuj78qoNabWhIsfKs+1S8xh/NJaviHywKVRdE2yj27oeDjogVR6H1cPnY9Qe/C8AqxEAX3nZQ==", "signatures": [{"sig": "MEUCIQDcfLNBgYl5mdcom/iBGNd+O66qDf15u9aDZEwpxFj05wIgf44DGtNmNKNBEiB8b4QVYIi+2m0nkREAJeInI0v8gHY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10666}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-portal", "version": "1.1.4-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9dca9d6eee93d38a47529599034a1d54decd7c62", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-lpBXvqtXrFHdFhIF7WL7GWT72niYzufD2yGj9X3dzQD58zrLM/FtC/4u+DajakjxD3NJ76bwSnOoNyY/MFgQCw==", "signatures": [{"sig": "MEYCIQDl+yKjHh/TvQDb5AS5nRfrF564dc33e3BUercGS/RZCQIhANbb4tHgojz1K2c9EH7NlWOq1PZ7MEgOIzIizvnJTfVt", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10770}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-portal", "version": "1.1.4-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b0b0695263d7248bdb8071011ebe85256cf9107d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-2nkbT93aDahdcn9Ack6wMdqanZupZ6GpRsZVNMPJZrW+JyyoEP230uYO+/HuTUlCaNVQ5FVVfbS/AydGeidT6A==", "signatures": [{"sig": "MEYCIQCeXF6EW13m8lR50qKuc51o5AEW84LAEEb8GwruyZQqXQIhALsd1HZVChbXv7LGHDU+e4/i0r+bObxJR9+fcGpWvUCJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10770}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-portal", "version": "1.1.4", "dependencies": {"@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ff5401ff63c8a825c46eea96d3aef66074b8c0c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-sn2O9k1rPFYVyKd5LAJfo96JlSGVFpa1fS6UuBJfrZadudiw5tAmru+n1x7aMRQ84qDM71Zh1+SzK5QwU0tJfA==", "signatures": [{"sig": "MEQCIFdHVv8xrh21xb1dZuWQiWHapVisa9zrjZEbFhOGLsdRAiBYCambw6bopm/4NYAxZz20CVOWoFJiUbr+W5i8FHGebg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10732}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b9e9137faef01c0946ac7975baaab688918a967a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nsv8czDS7c+2lQoWAzE+bbg2mYSp8DuWZAxM9IeLSWgn5QPxptbY+RlNHkHB7nR/FJDq3pafrDq3MFg4RCVlFQ==", "signatures": [{"sig": "MEUCIQDKYFHbr8lfmKEUk1ZFWw/Vyil1lFZULjvcv4j53pM5qwIgBqtX9H2fE1M7rO7EAPkZnAhHN6QbtKqw6d5kBhx3Cp0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10781}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.2": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1e47a58d06dbf5993f9157627023b6dbc0302ec9", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-1huwpXH2fxR7Hvxs+9Wdmmu+7Pz6vj0HYpMkaD13fh2JLDDauSL4uLezDmmOjIoDm4RZvW6xbqIBxgWggdlK+A==", "signatures": [{"sig": "MEUCIE/93Q+nUsRA23AqiT6UGHjGbSNwYSxQ5vX5XOcXQGWbAiEAxV2YQjrLBwhF/yW65laiw+bndGluYR9r2uuJX6JV1qQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10781}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.3": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c2da2f807c4012ecce061625c5bfffec0fdfbbea", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-yAdjL1xb3I9KdL31iBGd1k5t8UBOBZAj2IJNoY6QGed9yAoLuuo41Z9Nlqj+BJ47Q0YIB8xHh2YyQNiIU98U+w==", "signatures": [{"sig": "MEQCIAJs0GcRfp4vKtHtHYNU9ybtpk5Repu9hWgmK4FIJiQmAiBDmJzwpOU0YYhs7r5RbkL/MLevhZIrO3YRpqiM9SFd0Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10781}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.4": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2c353bf5593616c32ec64cb5e27ef19a5bef3ebe", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-KvJjrHCKRj3FHG+Az7INzwBPRvmStzWxXR1COAyf9N+ea0I3CVEWaP4DSmPsX8qizJe9Xxb2K0NcR7Zlfl9Lrw==", "signatures": [{"sig": "MEUCIQCSnBqDplrzQpL7j07MxlBCNLLTU0CGEgUZ3lAARAjwyAIgBZ5nPHelFBwnKJ4oAmkg95p7TN42SvXnxSAUr6iloWc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10781}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.5": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.5", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a335a8843cf96693644ee4423cd9da696f695be5", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-7D37PS19CXsscU0p2gYbv5/Hz6RWsUCFHnZRVwPgBr9TnvoYToqCT37IxyTFpAQ+U4mnSm1m52kOHAqUtGYqzA==", "signatures": [{"sig": "MEYCIQCAwR6NssdsiSrgErCGoBCW+VVWATddEjQXS/QX9hCL1wIhAPJ8HeOmf2R5U37l5pnQbJRPr2ENwhekuVajw65YHP5B", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10781}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.6": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.6", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "50aecd09e6bf3c147f832367a473cc6adc8b9559", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-9o3tFT47EMyRN2vu0vUFjphA/C6rc2Hcn1XjQFagQrWhis6hG6AjhwggSRiF4CWTiP4lHNot914SfWD9itaApA==", "signatures": [{"sig": "MEUCIFEY7C7Sj3j75GK7pKehRwswNcVPZLn8hMxyObKXRuBJAiEA/P7+ZIgOdXj4yZoAnUl/2tA2t5FIo1eNFSSpQ2ScVYM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10781}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.7": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.7", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2d87e4338ffbd961a3fafd8d3418584397484985", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-VJZ9nazagyjlWc9hxBsi1PDhBdMDQ/m8YYfJZXbJXwHmQ8CNTMQvWFBb888NjrQ/TGlstyiPjrvyb2k7I7NL8A==", "signatures": [{"sig": "MEUCID3HIUBX+NvHLBDHrtYom8VNfBbt6YKUtpjDhNC7qo3GAiEAwD+02t4SgxNxzdoZ+K3rEG4PGNXOBzgZdQex69Wds5U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10781}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.8": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.8", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f7ae14300ae142c6948ae35caa53872a588e811f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-qHV8VfOuVYQTqvQVxxo7qFrd1EMwUChYdm3tzT5gM7r43caOlKvOM64K1uYdI9LFURCXi9cc0ld6+sRtYYrFMQ==", "signatures": [{"sig": "MEYCIQD3JfXT5uBFGlu9luVZPsMeCazb5exXU9koBJb6ePNuWwIhAMTJr7uL8P4UGfRyy6q09OE21HIFzi1ucMVdgkU8+xra", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11172}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.9": {"name": "@radix-ui/react-portal", "version": "1.1.5-rc.9", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "12a1df7fc58827de0c43df87b3707d0ee31b20c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-3lTatZMY2Y1EbP9BxPAL0Pc5aKSs+I0SWLkD3uwZ6QVttW6z/0oNuJywWEjkPuuXNBUx/eprZh51UlnAoAu6FQ==", "signatures": [{"sig": "MEUCIE6lWO1keFEEluX6tTvoPHfo7QwGRp1xBQ+lcKdCTR1qAiEAut9/DMwUMxR4/2tVjWtAXHprser+OmyqnGvzVRYIIOE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11172}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-portal", "version": "1.1.5", "dependencies": {"@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "50ed6bee2d895c9a9dfc28625f24b8483b74d431", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.5.tgz", "fileCount": 8, "integrity": "sha512-ps/67ZqsFm+Mb6lSPJpfhRLrVL2i2fntgCmGMqqth4eaGUf+knAuuRtWVJrNjUhExgmdRqftSgzpf0DF0n6yXA==", "signatures": [{"sig": "MEUCIQCZ1DJF4ZGc56tnRN812tFRIHJ7e8kyDc4inEd8JIomyAIgTV1zOU1NS5iL36T68zJ3DJgOxiRHv1EgLhc3Wj093H8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11129}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744311029001": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744311029001", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0b9a3a398fdd27682bb4de49a776b2532f80ac9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-ApYUafknynTcxT4wmonFAy4l+LiAl1JxM2YYhtij1V3zbWhm1DHDTQ8XMe8FCPCEWRmPvSzEp77ZF0Vmb9Lopw==", "signatures": [{"sig": "MEQCICCymLRHdqlpq+wMsnxdvNL4iDY5U3gRth+YLgn186anAiBToOwW5qr2Y/9Y1/nLO8W+fzJv6d6OB0mNXMkLs8/vIQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744416976900": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744416976900", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9699ad7339fda34bf6761d3090a995d72d4c47c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-to/a3qfG6sJx4wn3KMwDNOl+dFvI20Yww0wLkN+njEMou6yHkTlCZ/PgCx+XOAL4qo1f2zEpewT3MwReThPP2Q==", "signatures": [{"sig": "MEUCIHmwwFdlRy+Y9V1fyd0zYhiZAyFiTODseMPY2anBNU6KAiEAolk2fFi+M+elb7KxM/rpl7z437xqI4ion/lmRRj4jzo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744502104733": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744502104733", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fac1e7fd8bf4535353062ef7b088884f708654d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-GU7lIS2tiLMfPOvJAZ8Kbv47pAx6YVve1a/u5EatYMS5HIpjwQWiFkOL/Exxiag/wbISMRwz7BT5GDjH9zZwsg==", "signatures": [{"sig": "MEQCIET3ydWMYDLJPOiNwtxu/spMBfe2AZBIyaqWMnfCcI+RAiAHlpHjOjewOu3/nwZTR6TEo4eLsh//NwFzPa4PjciCdA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744518250005": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744518250005", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c1f8bf028b7501ac5115cf107d7e5a6eaf5baa44", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-yQJRxCyYy7JN9S1S8b605BuVj5C+nc4MFIl6Wu5tvqCU5D3HVDjUZQuCtRUvMQSIsdz63pqaqtmwBwb3b03pDA==", "signatures": [{"sig": "MEQCIEptxvegUV+JrZs787nmDdTbMnUtCZlY1ovx+620qkXdAiAGLz2Kt9GAlBzVllAqTncnM3mqGKTL5+o9YEuduHmkTw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744519235198": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744519235198", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6187ecb65132b2ef30a4a69a6439807fae99209c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-ZKSae77UNsXmFJGZVcb0lOsYsRV2GPUPWaiB+8jnSBL4sNTrL49SfzIsk4funYOxXm01mGnXgaGGJCrSAEgfeg==", "signatures": [{"sig": "MEUCIGNSqMMlzsHKG3KjyPKMvCUUetMEhjdfttrHYer4PEPSAiEA058RDoevkIVNJa+Z6MuITUNej3YVrFyWdBt42iJx3ek=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744574857111": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744574857111", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "44281cf48e902187983508954727e2867db90c5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-gwBczyQOF96KUuacDa8LIjb9Br9S88oppMUDgdZPGJWHNRA1mnxK33c+TNqUqO670KJTG9n7GsWJY3AD10lV8Q==", "signatures": [{"sig": "MEUCIQDJf/hdInClF97OjVz9bASh7aUzr0nOdNiMFZKIg4baaQIgXX5LafXxQZxA19/isYGM2Peuo4r/9x0PJ1ReSYyYsvU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744660991666": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744660991666", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f51b889fc328f610a61a55983ebaba0ede2097b", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-WjyfMHWQt+FScaHEWCOmJfAp+1LnVw5WBvZ423SRWkHokSNgqvD7wMhb2gomBP+3pepPXmEOXypfI/CZKKnFUQ==", "signatures": [{"sig": "MEUCIQC16gJwZnEPoBZraVNAzCwX40CWFAFP8EWnWcOG3a0/sQIgbzcA2WZJ/nBZAd+NAKJMknycEpLya5gx1BUBQLXXQ9M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744661316162": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744661316162", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "24a0fc61c6e3de1b6559ae27b07960c0a8d4dd6d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-0W33aFyYK/BTBQd6ITuhKHl1gB+ToAUVe1W3HgFGJUETVahWHgyvrnXygirMlmBnSfK17qtBceJwTOa61YSq9Q==", "signatures": [{"sig": "MEQCIGpdae2AQRS4UmaYNCo+gDFUVvRWq1ck8Uyt+Aj0L4tqAiBFQjjdGu5JwywJUpvwDPlRGZWmez/B+dMKc6WkIXXAPQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744830756566": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744830756566", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d2f54b1f6dd7734af0b3e2b164bbc767efe94690", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-boen3TJrpZiyMGKTLkOnlAdqEdr7bXio0hdu4+ACmuEaJ/BI3lT4eWbXQMNjlTq0H52OyedRLImb7ghPcXYyKg==", "signatures": [{"sig": "MEYCIQDgVzUSfxUIlJo0KD2uZzzOsjI1OZpsJz3EA6kT1LYY7AIhAM2N+f6IfBdys4sdmwRQeVUQ1LxIoY8eBTCjzti+tcvs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744831331200": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744831331200", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6b85e62ecea7129ff333b9b4ec3a02ee1cc8765c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-MvV9KuEZESzr5kGB5/YcvvlJYqkeNmELgEYaTEdUwGxXyJWfoEzKPLQZhrIeZieRcVaYKcO1umydWIwWVW33Kg==", "signatures": [{"sig": "MEYCIQCYd2U4Qp621Fe02PnVsPe6Rt2OdAC47HYm7xqowV7VrAIhAJhSTHAoeHMH2qeSspeWB4PhUoUVqvPAHDUOFbgE2YiL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744836032308": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744836032308", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8f9f451fb2e22bd1ac590f47693c2183174fd92e", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-R244SgiYAhGY3c8mqtwx0OmvLLdjzMwjLxhmW0xWI3Tvv5TJs3XOiC+tv+sAGl3b+U7CFxwtLIQkvnFfUdCaTw==", "signatures": [{"sig": "MEUCIDLHeMgJFBbPNXYAdIOELJIiQq1rXF3ZLnE+BwQFvrxtAiEAlyZA0p4k0zXCqucv7WOW8m8xxcHhCkOss6gAJbB6u90=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744897529216": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744897529216", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1e48c93f1f9b3c3af2909169e3247c02cb9cb76c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-6YMU9h/CFwXcIJvgQsCstDK/gfOtDtkcRKCrUvfAeWN4yiRcUpOz1cJr4X/FZIEf/+904WvIXtkVbDgHS0GDoQ==", "signatures": [{"sig": "MEUCIB0BUa8YVZIN23O+ALrI1rMm9cJmZBcQUcAvixW+JQpAAiEAmCznv4y2ywh3nOM/o6UmUBnsfRhQtfdMNLupp0OxkQA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744898528774": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744898528774", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b1df21be8ff26b64cc197780227c7f3d305dd84c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-HdJ09CkY2ZbfjWmZOwaDtFHqUjrBrYGee0W2HedNjLDZ7SDJp9uI2YEpQuqkrXm98OC+qjrbprtuRA28gz36fg==", "signatures": [{"sig": "MEQCIExsbixeyrkQoEf7yQK7wxLzNZ77VOs98tgsxDGC2ctkAiBcvBQU1AMtzvWbqcMBEFrWLt9ru/nnAoQ9thO0GP/dtQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744905634543": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744905634543", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "152e8813656f363097fd9e811c71703b907cbfa1", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-T5PZsqRY9Ntp5ju1WdIDXueUwlhHkQ1jMbHiO5Ts4LnzLKWYcfnu4GKMsXo87lfJ9oe7RlLb3nZD5wDKaZTg7w==", "signatures": [{"sig": "MEUCIQCuvivoIhSSYDWw518AGvt/IzoMm6EAYX1Ot4agAHzwowIgVZTVJ0faqpzjElHftpjwB6JBu8zvmHMofZEYM33k7L4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744910682821": {"name": "@radix-ui/react-portal", "version": "1.1.6-rc.1744910682821", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "16d4719a98b774c2d782ee97c711871820612ab5", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-ZoN8VKzSp87bQoHM1CKNHQBGiFxGubW78Qylis1+R06jnoa3kBmZHUeBn/jebDHasJV+Tu8AdkxN3K0zTAOeew==", "signatures": [{"sig": "MEYCIQD9Iwv8unDMeBxiKZajslC4FbVvSIE60xZHmqCdgmZo8wIhAPR0qjZA1oGF+QIafcdWnVwVfFvNTVz0jZ4QDxTAc9YM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-portal", "version": "1.1.6", "dependencies": {"@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4202e1bb34afdac612e4e982eca8efd36cbc611f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-XmsIl2z1n/TsYFLIdYam2rmFwf9OC/Sh2avkbmVMDuBZIe7hSpM0cYnWPAo7nHOVx8zTuwDZGByfcqLdnzp3Vw==", "signatures": [{"sig": "MEYCIQDE2VBNXkfv1hlH8uhG3vHyvhljlRfV2w3D1/Vd8hsVLQIhAP0gBezpZI7IAS16pWgnCOa+guF4y9+71sHxEQjEi6Ub", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745345395380": {"name": "@radix-ui/react-portal", "version": "1.1.7-rc.1745345395380", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b43c737aaf49cd65fd4a241c30a3dd00ea68c392", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-EV94JmSMQ4hDiIVgJu37MtJ96wDsR34lZ/heHx0aRKO22FYlKGRLLgjFDr7LpZ7uhEU/I/kN0lJyHIuSQf5Y3w==", "signatures": [{"sig": "MEUCIAuK24SsKhyGwNA/zFBP7b0ftjNaTCmVFldFBXumHkteAiEAo2/pjs2lQPfuWJvhJWHNrgk4UG97FesD7dt5gwcC6Js=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745439717073": {"name": "@radix-ui/react-portal", "version": "1.1.7-rc.1745439717073", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6909476334935c3e1379360aadc9bb3349635a83", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-3/2zllXOPeKpA+cAkWvCLmWY0DKDQONhdypnsPhKRvJSGZaiB7JIbv3xp+xD61YmHacn+IIznZJa0/1hqpTIbg==", "signatures": [{"sig": "MEUCIAZX4OLJ1YfKz9rEhhDiXduYB8GZCB5/jyO7MUOsAbd6AiEAjLkrXa3V9qM4SoLf5Lsy1ABX0r9xAbDwuHnOBRPGAuw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745972185559": {"name": "@radix-ui/react-portal", "version": "1.1.7-rc.1745972185559", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7054d7c1c9a61b186142116bf798f701f309782c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-KH1Wv4SYYTdNyqqw/iK7q7u9BfD1whsDd84NCHNXniC/BjCjejVgYGr7V6Y1/OWb896NGeDeJB+vBxoLA01s0g==", "signatures": [{"sig": "MEUCIQCQtAf+t1pZE0MQLwoLwZr6I9RSo2Ai2fygbmjGKtPjsAIgCthsS85tBh3w++izAcMVpIpgfDb6Mr6dKUNUBsobC+8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746044551800": {"name": "@radix-ui/react-portal", "version": "1.1.7-rc.1746044551800", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "76085f9503a2b7b270b9ad08194d7164b1793794", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-ckCMfkrYAvdC78Z2Vw6AbUzG6f4lfvgW73rC/hsdZeZthSKwzp5LMQrtBzrfKOE/EvZcF3AvtnCw/Z9u+CadXQ==", "signatures": [{"sig": "MEQCIBlEBCeCChNsieNQgYVyuCaxZveYl4HO4YjA5Dd0MDzaAiA0LlCETjVuOGoCMG9UFa8wcFY552UQXpyKDrP4uWol2A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746053194630": {"name": "@radix-ui/react-portal", "version": "1.1.7-rc.1746053194630", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e1ad288ed0114fb15144f0446e16262440f4dbf2", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-HPkvyMs62z7OkpXHgYI/1mxSq6ZjyY216BsG+GG7Iu6UBmswcVSHsPA2NMWwt+0Mud9JtYtfit9FFSP2HVhAQg==", "signatures": [{"sig": "MEUCIEwzj8m/HgGPpNGkXgrZT2yDgQMrgvrlFof161AF/nqoAiEAxIzJSeui9yyKbaWkrVOp8/zr0gHyMJsSahg9fL3FY7k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746075822931": {"name": "@radix-ui/react-portal", "version": "1.1.7-rc.1746075822931", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "224cc21c32875b8aebe2a932ade90dff80b7f00d", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-cAsSw+r7c7eiCfcle9RHLdFcES2OI56iT5D3nyOUxVaOb8u84beER9xZmjznnjhhS/9tXopakEpAh+8bmh0OjQ==", "signatures": [{"sig": "MEUCIQD4UQmRu66QQtpJUIuyTUMu7vqrsH3XBetNTCcCP8tUfwIgbWj2fNBaeoQaCGzb65PcijJHJ+vKaGkwkyMaV1oezSg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746466567086": {"name": "@radix-ui/react-portal", "version": "1.1.7-rc.1746466567086", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a7b917fe1842ee27870fba415daa59e7f469254f", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-17Hhgx4lA+gsx6ShIHaWyVFuuEjAV/EHJIgZtyTPiOQEDn5RImIu/DhWPHNVhS1lGNNfPGCVX5opQQqTeAr2jg==", "signatures": [{"sig": "MEYCIQC6aGPtHqwsdAu6UOP1wlgC0abWsPwtHXW7tu+x4wMuOwIhAKx6nfxCFDsdx2HSOuZgN5C7ZHg3CgVDcGtiL1BlVsFf", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11715}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-portal", "version": "1.1.7", "dependencies": {"@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0d2636ff90d53a2eadd2f16a031e06eb9741a044", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.7.tgz", "fileCount": 9, "integrity": "sha512-r2Cpk0yzycgplKYq+X9mo+2o3g12RpTeTZslQocXrAsSY9cz8HJ/QAVb6kxFOhd9U6eq9zTACw1X3JZeLQHj2A==", "signatures": [{"sig": "MEUCIQCJq7QAsgCYXhCPn5X/Jk0/7YiUINgzgymTMT8BCopQNgIgaGKaRbhPqMCZ7R0+IDz31Vu51J7kYjqq44oIDYjUnyI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-portal", "version": "1.1.8", "dependencies": {"@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0181e85bc0d8c67229dd8cf198204f5f4cc7c09c", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-hQsTUIn7p7fxCPvao/q6wpbxmCwgLrlz+nOrJgC+RwfZqWY/WN+UMqkXzrtKbPrF82P43eCTl3ekeKuyAQbFeg==", "signatures": [{"sig": "MEYCIQDIv+CEQ/4z3mswjY8KD2MJK2B2fdQt8+3tTUDzELujggIhANbKiV67o4W037mgjLrIwGAq0JunTSEgNjPpdzrcbQoC", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1746560904918": {"name": "@radix-ui/react-portal", "version": "1.1.9-rc.1746560904918", "dependencies": {"@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-5yordHwxcznMiqyAIO5LqkIgj21dP2EApFd7Y1N4yvlaSluFEEfW4dwkr/gORUnulxKBpwzlSiL3JNTRqczcIA==", "shasum": "a1f04625b669d77b914d871bd445ddc026db5f1a", "tarball": "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.9-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 11719, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIChSnAOd9A7MDEsIjHoYbxB+4oTa12tTq8KEyvCnLr2iAiEA3CLuVpwBi++O0FEMDEasExLOI0ePljoonjyq5IIYOPk="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:01.071Z", "cachedAt": 1747660589474}