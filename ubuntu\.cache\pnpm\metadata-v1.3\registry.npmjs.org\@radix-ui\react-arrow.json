{"name": "@radix-ui/react-arrow", "dist-tags": {"next": "1.1.7-rc.1746560904918", "latest": "1.1.6"}, "versions": {"0.0.1": {"name": "@radix-ui/react-arrow", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9689fe27cf88c5831d3280f2bff2cca3d3850f66", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-B48t9QYL5iI74HoBNCkmYiG2p9q4PMxa1Gw0bY74XRUAyM3WdNUGEaoHd1qmP7W+iu3sqciBsAI1vhmtbPpZ0A==", "signatures": [{"sig": "MEQCICnRkruXO9eEq6NvMOuBrCCM5bdsIInc2kSP4Iwi3ZH2AiAw0Sc1AOQAtma5PxvujM7MAaSrFlhCkBUA5jplsjOpnQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9548, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbACRA9TVsSAnZWagAA4soP/RlhALJ3ofErFgpVkZ2Q\nyrpcriGXBBl704BAHMTFPTnUgP56d3o3CX7X4iBPj67Cnqu2I7J2hm69wB0M\ns6mAUlmQ6VPDelFHxpJwiMGBzfL/QO6woSBjkJHfmwPEHaAyvFtxlJs8kL3n\nA4FLxiXSXzNp/gkw9uuPrj+/rWpsuPhlDj/aBv+V6VcDHz9Iq2VjfW3iwD4k\n56w16x/JYk7gg3Qa/swozKwN+N+5b5/sgbbzLrE2XuS5N46MTrGOGHaj622R\nOZPkZn+TJzrIfHj6pcV0JZB4Lzp0pmzz8Qiqgk4wZyEC5ypt6xSIoPyf9UyE\nC/upvo2/WJmKIhEZ7P+YWHQfvFGM1AjZUwceLleI9qX/s6ArGb378W3cF7aO\n0p3B/Dq9jIQBBZkSfF7gafQT+HfgHzGyGjq1zx5fiioMXAojhJdBJSVw2fX3\n0C65TfMXNCRxlwjuciLGHtI6ljveQaewrjVwMOqZiB84VNqNS2RGYKVEhCbP\n3pw4kOA6NwygtbGxNou8USJrGdWlFBpWW0JyxGKqWkm3T6ibA9nW/FFZgDS/\nSJPTcAxp1bTBYouB70lAo+EM1cbE3utCY/SdphMuHuIZtDwZyYos73OMXJ2k\nwrTxVbuggM8slPUg9bDa1GiL1qlNqGmok1ZervfVVv+Eb/KEh6BGonO8t1qH\nA2Fw\r\n=HPsh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-arrow", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e218408fe245fbc9ed5e13fc8922a3d5882b69b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-ZdQeWfse9Ns/hzJxreC22r+izto6c+BqUWigXb4m1dJA84yXjll7Byfm0GOJlyWD3Ifiv39MubzDJS60greigw==", "signatures": [{"sig": "MEYCIQDS8sp9tUVBeStWRRHLmIdq3mQvTcbsF9LHpi5ikuoXCgIhAMNtMnTtf7fAnC3f7Pt03ijrHkK5xiNRaRj8y/HTwE0b", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8324, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwu5CRA9TVsSAnZWagAAz4oP/A4qRt9FpB/+XK3VaCc/\ntofXKqkcGoKuEXoGhBGnlk6iuyNpOocDVxV2lXIDtLvxvOtJueWdKmaBcHpU\nqQ9Ti9M6HlRgjPSr2dhzk69D4fzIauulLB4FdwpnPZOgaw2SMCfnWgwLzTU5\n+SLMBfdUnxKiNh+OZl9M/8rEBW6ZuFhuMlpZABckZbTIlEhrEjfo2JvmW1dW\nutj6yGhzEtGAnqd1FkozCwNsZNJxjCQmpQoaq3qQpZNuAtxYO3x5eGaSgb79\nJVm+HyYZd8V90f3lACaku4W+zkpR6+Ku1i6yvJpEEv/2rVRIMuRevxdffuS0\nK7ChT888e27ArWDQyOsHwcgLnAmY5R1I/5FK8RsCGDVR7cdnREhYvbl5XQss\nSZyYB+hYtzIDzVnjmd24IgB3R/ZmHgcLPNCGQuV0JBsV2dcb7c6PfBDL65Z5\nrJLVkslD+XY0gYXopsfRQ5GuiemHyWvdOBkCMv/ZEe6mwXGxp0HNoYUjk2oQ\npvIO6ZoILwNT+Minq31cm37s1FxByhpwYICU1T5FAVCkVjbOdEe9P39+8k9E\naQu6dhENNXCr8YzEGbBBXx7IL2T/EWaWuzuFQDwFkJ7ViUnfADN9d6ZklYZS\nkjWvGhmlfEPYlMlajLVZdor/FTk2o1c9RG1TnT3xn7KOYCPJbNUxHQwRZ0Gt\np4FJ\r\n=n4sR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-arrow", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "df6000fd615baad873d2104fec13c5283ed56a31", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-YMflnH/WmjnRL/d4ctWQHX/Dv9qx4zh88zaMpM1XSmRJvIrjpFb9YhBs+VTrYdE1DBvSDWu2EQ81h60aD5rHaA==", "signatures": [{"sig": "MEQCIG3CP7Sd/IHN1rBnU/oJojfgn4kXpNPNAFMjyVUUHqDrAiB1Xq6pVv6lr9wlEj4PUCWtn2mLBrnzv/26dtVB3XStFw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETsyCRA9TVsSAnZWagAAOTMP/3aP08GqYnReopu7pDoV\nRe5SVZwI7UJ0sIQxn9TeYP8uEdMvPxUz2SBc60tOJaWNKnaGB5xoEceKVYEl\nC4sEveD4mcu/wuoQX7j9vgHVemkp2LRRiJgArYyZ6RxeSwsS8hobuPUF1v2V\ndagHsXRGoMCkN9ZqImZZR1YH4L3hSmeK894nyl54t32JVTl0wuASyxl9m4oo\nTQ6UgshE6dwXhw/n9JrmHGwR1ZKkWNTUnuevnRK5w+7aATFhAY4/z9hzxRFw\nsprsG4PKUaUGakV0sDif81Kt4cqa4vWvH1atmJpV+NgNHk8fejtyA297aHKc\nI4u0KHmQQJmIQAEEnTDmaUPMy8+9PLi/zcOfT1gzWsg+ISzjh/U3n2tC9NOS\n/6gXVKa39v1y6nrKVeSl7Fzjy8dlSZJsgNMobCb25QFDE0272+QwW8OOQ1yG\nBqjCk0dlej0jaUBrkek1VdWZPeGvnXsYF39nc9GYipqLPyCS+SZ1QRz8Mcmy\n0xTiijyjTN1LcGpH2kS+x4nfxJ3HhuOBoUO7kHA4+PXZkWXDQERXU9j6Kwe5\nfCQ+qZUUm84QFD42BaLEvaQAiE7u8MknMqSbzmOzFn9AnV4oMrEbVTvQMHZR\n5t00Bsotbg1326aE9ZnV5MP5WN0RjeMh1qZucUJ286YJjkLg2PLzfMQIK1XV\nRwMG\r\n=/SfG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-arrow", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c0cb8db4be50e0b9c36cbf8df6ea0a06da518202", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-MD82g/HlS/XJRY6ot4HhMNhkdkElKNIDGPYwnXIK0Pg6xejgCuWrp24lRUUwx9y7ZzRPiMQFU73iOOz35Ozn/A==", "signatures": [{"sig": "MEUCIAkFZjdQZzBnn2fP6jBYTFQSl0NaiMCR270ZrQ7ggJ1mAiEAiuPh/rmVROMQnT6qQJcIL3+C9BrNRkxa2a5yOA4wvqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFC/uCRA9TVsSAnZWagAAVRUQAITVHsPbMK/0HM8BfQCf\niq63tPs/ujcJxg9gUQFJ6kVobe/8qaJP77yxZkYimmsYLRt1cjTdxTianeBT\nQc3jyt0upbJaXKg/6WaOXaYv4FS3WEvqjbylMDUqAbDXWXWROtC9UQ0f4Vmm\n3k8y3fdz+hA0WTVMT4QhbhBoKe/NxCc6qVTmXoXv+8qlQF6ntp2uT/OdQRGC\nhY0gltcV4HF+ClC5jdENBbWzLvMEJEEGOXOwkJtkhjbjU8FfHj8y7gzxKSHr\nKtXH4vlaY+dSMrXQnS/hhrDwS28p4ksbnTLZdlHQNrw3rvLolvi/iVhD6Tdp\np5H9Bwx+oLNpLshy6LShiNV5slnMdSrBCfU2K7nE55qoJfL8wSoK0DTjdYBV\n8y0ZWCz8rsgQsIZTrR3tP7v2AnWbnJE/Su7qh3UdXpI3yZ4FZIkh2vy8qfny\nNscRxOTr6S24ZJ3dC62WRzv6oIUOBqnmLkBVAFfj3fqsEWUd88vHTB1eaRqN\nj4TaR1bfHzezokr6dD2OaDYQe5f10Qmn5Gss+s2XzMEUdw6UPvg1NiyyA+pz\n7Qd+e7bUG9dK8+BWYepDXdsV3AftXvAG09WZC2xM0jKa1IclEh3Cts1ynm2T\niNnPSTM2yOb6tdbXDVcbgwxrDPGaceGvdM70VcYEPRWWghuRKLHukRMew6SA\nWA5k\r\n=p5Vg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-arrow", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f7f4a156c20262e0623a61904c49a62ccfbef55d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-lOJoQZDHjCUIywhch0ym+TJurXaogtanGIJT4Bua6MkGNyAz9KCCUhXyoJduFHJpSc1Z1KY95I6mo30OOT1M9A==", "signatures": [{"sig": "MEQCIGD7RFLZXKrPChezojZEkf10lQv5C+F+DlcSQUi3O6rKAiA8C683S95+w5vtyTq09q9jpS2rwQgQdVqKrd/KOoQD3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10808, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VmCRA9TVsSAnZWagAAWV0P/1RCmZZJDZ1DhMNu0f7y\nVyuk2sAgeXDailwCyME6JHX444WZKlFeq7Vm7JgbHIP0wWD0sQ8F2dKHMSP5\nTFdOVjGyxPwcY7zZsZmqkzVWA4kgxjUGtXTSSpF18iKkpwhQ/sajrTyHWu77\nlVK9lk2RKQ8G8PP+UirPTps9+p6QrefQyTDeREPr340WrihiYY5t61qtayCm\npZm4LCff6bEcAbisSDC8Fc+atpcoMdo75rM3giotYOC/ixqFRNse9MJ3i51u\n082h458hSqJgTodqmTLxts2uFJDGs426pjnlae7bBL+4XY+FivQqDhUG0ZmA\nWnVJzAgHBodhVtIsDEIEnn5p0qBwVU7+FjJ4epJZqP8iJ/TUQpFzbNqOSt8l\nsk14FdfhiTWU/9+t6tyKyfAlHR3raPN3ZPetJ4xKVUNzIf7k04oGYzK5Odbm\n/HofEWz903+lz00sxwgddVAZAhcpfV/olhBKtlNI7YrXGaNsB1ApOwSdtaKS\nwwIQDoTD5KIx7tsBC8Hy8mNL3MygLNgB8T8nAOCWJvrNgpQkSl731EhAfrxZ\na+CVevbXfGoUWEMg7eoYU8HkXuPNcq/bra491Qxist8UNyKssrnn00qKCiJX\nqbo8PIhcvrFDm21Gi8hY9pFXkcd50dq9CmtIwxBb0WykxlvkptgVZ8xuIKWU\nke9a\r\n=hr1o\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-arrow", "version": "0.0.6", "dependencies": {"@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "acaddcf5fb2e3387c445ceeb76cc16d1d3ea21c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-O8RcgasVaoVN65mIhcx30yWM1MDvpKHyVdIi2/joVm3xfJ0nprjcwEAnALw2lh03nXUNnOAEbpV9P2jsiXRUgw==", "signatures": [{"sig": "MEUCIQDmPIi38EFNtBci2TD77J3DIdw8E07miCoTNvEOOsIjdgIgI29CBzJbfCLdkLrHw0bEH1nv3e9jr/zCWM7aeOG0Nkw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VCCRA9TVsSAnZWagAAl3IP/RxZcKTeEmTI3YPgoFEQ\nm39DC9J2Kltvaq/BD3ViXcTeXia6JTJO0QeDSemuejBvi9njZloNSmuEwjml\nOO9YS81gx3d84TpHUmilOg/G+u72LSNCL66JSYONsXC54NynbInRAKpEP+AL\ns2HTb9OAmfIbfwvA4iWuRDAOiZO23KyNRKHNy9nitEvIAH0LhT8bH9jOpwfP\n76i4AbljcCbL5DthZlXLSzY117k5GOGU9NhDE7Id6daQADDTOLrPa8orBTyL\n5A+twS7EtyPVmOZ36Hj1DbUSgHbsm8M7mSL6Eix5x9UP6ri5VBVt9uWaJJ2W\nMC3Pv9KJfADrZ/DSxLXcSYhaQ3u8VDmsQ4b4F5mZiNNBItvKUBEU5v3F90pw\n//UAz80lPSuRluG9ee9itv2IoRcmGIY96fkr115HE8qgFm0zaDTE+kpU8UYu\nNg6aOwMjSU7caWd/m5wmCiF/x0bagU6wSMNnrf32iT1j0CM5DB8xYBO8OGFO\nvStA+xZcUMNgghwf97kXNjjLz6HfLMH2M0qty7XgsZDfnaqJbuIUNypApQAY\nz0Jz8+qLq/8/IxX88fQARCndKsOQ0RhucNPoQUq9TDfuZlwoZRouU6yIs6jv\nwR7Q1LsNZ8y+d2vp326XwgsmXBr8RD8Hom9iv/Ce4V6vE+TDnxytBmoOdtxj\nUkF3\r\n=Ii0+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-arrow", "version": "0.0.7", "dependencies": {"@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2526d1db5689ddf5c1f584b46256a3a422b24c59", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-rySe92stOWrsGVTL58GCJF+82cJMYUhtOoSZuzRmXvPwWj7xvKRTfopm59/On3hgOK457Y/PmKfcJLeuJ6bdhw==", "signatures": [{"sig": "MEUCID2fr8eB9vlAc3zUbzKUtumfnihXzFJ6xx8eW8pF5L6FAiEA8XE3pmUOBRfcgBcPfEI3BjOyqqj1Z81q1h1K1TFv5X8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10396, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOoCRA9TVsSAnZWagAAUKQP/0+Bj954+KMOAQq3aTzE\nEkWjpVNYIHRPDa9o1a3/z87wtib+O/Q7oIgHRCjRTmFXMvyKPvo5iL6d/gME\nGbmpEeT7kw4LpH+t3E2s0Z02aKd35QuLTuxn37pqCVtcjGgyokVPMr5XMPKI\nplwFQ03hpC0XnFa/7Ykp7NsJAHopGFwjFaJAXz1RFebWNXA2PlDcdyYS61Zf\nZN4J+6xtVjVi0ZnKhoBgR0Sv0qEisSVOoy0lhLrUZipP1duZW9rW9mZ5CVpR\nabkTOYZQsOPVnWmfpOjM5w/xX28TXijhF6V/o3RltvRDk5UdcPX+yqb5IjvF\nTruQlOb4TsZoOjmC7NImoFzlyO7iECNvjo+/WbkQF85iq7IoPOzdbljpIwM1\nds5UdNkjBsKmsNjAaiysCQtu+LipnUNhkmpTTMIVpcEnMEnE02yNZ78MIaNp\nUKxgikWqKTRGH5MkF36ggk1FecIuG5ln3Ikf/qI9Iw8f0hYd6GqJRVGxblKz\nne9wv/mzVy8zRJPb3WU9mJvV/HNpLgrBmfh3UajNySUyhFc+LJVgGkwXhcnX\njDzTFhKWdgvMbd1uzvV9vDcZ96MUa2fMxf1rnOKm5ECrPwvPup/5CKrv/Qqv\nUu+Jfb8fqQ1//BQlvC9JZ4vLKN8iubaOu+160otK4NBdYmnwyVCE3WonJ8L0\nlha4\r\n=5Jmc\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-arrow", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5b96c688c9ef2c35cf59fa7788b934dfe2280298", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-UQu64NkOtyS2slbjbL0+rh6ufUHP3r4hLbZvcfS7U1CJYMDBtn4QzNhYqREJoFHSsZBFHiYuGG9jbD4jLoGFlg==", "signatures": [{"sig": "MEYCIQDUhFA4tOlPakVKFysl7J0CFNFxgKtrqJaPxj8L+5fzIgIhANbRFeQxeZbj3XmLtsmxP5NTnl2tVDzP2Op0Mv4D9P/E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0gdCRA9TVsSAnZWagAAXpEQAKOV9J0s4YofdjPxlp6m\nQFFQCj14wFYrdUesNHi9/EUpb7nZNRjbA9plnsnPJLDb5ZIOUvYqx9rSrwDS\n/DnIPWHfN+XpUBnrY48N4GdSl2YJOs04FS45HJyyBSXlYwoNTF292fiZYEjU\nuXOZnlHyWJ7QNg9RedAz1wOLe7eVtCfJ+cIHf24uLsWzGkJv8hAI5WzLQmTu\n2tzZMqQzBogmvfu+ku6mAQ0Mk3qGwVoGpIYhiN2t02+E4/1YHCGjStldO6PR\n+0/QlaHqXRnG0gnjqqi/dMsupRVd9O4yP5IrDOCPKyD3+OxgcEJuYg5eFUzl\ntwXavIs5Z9KnR63mSBMDivvFow9l341dVkpv6Jibxs+EfpFYnjvq6L58klIp\ntJviOTrOJ24687RItRY6KvtNuT7PGxrFshUSdjUQ2LFMKTQk9QcQtVMoCSIr\nrKcT8LaRN/2hvKQToGPoiJGXK72U8olCIIKDTpixOWQQP0YUDKHV5nL3PqJl\nmGgYp1cjof4R6gFmy8uw2H2AuWCp2KJXPD51dxRan6J0VfFCnmTXA/NSWLFC\nLoDwQtEm/qHqtR5xogHk5fxCdSeU+KRZYypeFH21fjAfs5inI4LMXTdZcnME\n1lLGRBQizI8tfJjO8rTlEvjBmHAtCNhWvpTxI63NZsEzVM7MqHHgKB86dMxl\nrvBy\r\n=VCxv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-arrow", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "479a07ec864c9a668e4084899770ab54da78d5d5", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-3+V89dEs9gG2CBdLmWg5OENylf28XdDhOK/T6xrfsTgfm4uaMWgAY8YChc4qyAPQDWqtVq6E+TnLbtldd/zLTQ==", "signatures": [{"sig": "MEUCIQD40G60paqZwUptVKLdUXa+Cy+OovimdpPJj9Ki/fyAVgIgUQDe02Kcld3WodiRd0tQNFBsoelJcWYyi7LDeW2BBS0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HjCRA9TVsSAnZWagAASzgP/1ujHO5CLks6451pSmAB\n5UbCcS2Q9kyKXxSytQdvBqBzsvAVFHM5QbyxX/UjSTiNJoaJDj2WqbDxmMWh\nlLplNYELWi/ieumOGddRvcHjKQp0yb9piSRkHXHyjLPxqdtgWx6i1/ebTYCI\nbIohKV1LimhZKi5UlJLx3R8UJNc5+ibG6sinAOg0WbV3vL9ihrOApENFKIor\nkfRIKzNCjA4yeVomqnqflCJ+5xzR+EofESAEksi22hBluo59f3eBMCrM1tJ5\n7Kg76ZH2s8cNmROt/CCeB6KceLAzvHXa7gpNEzKWj+Ryx35M+ay8Gnx1wVNR\npH94nd11wkHF1/f3mmVuKoIvI7+rBl5siPo+uBevsPRWYUzi8NTk/sQ85nT5\nWTugxgoTAY4t2e1i4fihl90YhHEOp1ZeQixqesPkHvwbkRPfXHzxoyn21BmD\nqg4m4ICMVPy7GhUFlLtH+Rjs2cudBkTm3d65GCo+MT+maACeXSLCYb3D3wNL\nf0ceLQ76WxSjtuv8xkg5RPKnxSwRYhB+SLmSokhEy7f2nHpwxDV0jGSHxjVC\nN8t82KHgWmHHYwq7tKl0/rPDrV+s1AZcky7mPHvJuyZ8hFHO1LTBHJVtUP4N\nk2/L+0OC+sOWjyWUMJ/Ey5RyqqdAiLVL2A/KSoRxzB7cAdjj9N7Pwvngd0AM\nGaCX\r\n=BtnU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-arrow", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "21fc8108df2309532c0e879428819d893add40b9", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-ZOwBtyimyxFTAHBhp2sH1f4llaOn3jD6trqzMqkW1osYdme3QPDgerZZijKcVW/bOALvQlHA82VbPbGIQuwyUA==", "signatures": [{"sig": "MEUCIQCHZcqCXXpWF+ZYUtCBOfoydfOnICvudaMiYpc/AIWjpwIgDXE1e7odhhSi7JkWMYq4b13OYEfDOj1+d47gUgsgGvw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8770, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vhCRA9TVsSAnZWagAAJ40P/3dvGS6DjZO9DhXOZK7E\nXTDmSJutxXTW/H2OH6GV18SSpI0LyeV4niQr7coUqsHKWMUQ1zTAg0aM1Lty\nBZgUDfvtYkqn6pNZixQY5KhpyHSepMluzzVmT4srv6k+e50W/27VWM35sx9O\nLzLM0C4QaXUprwBIo1+XdC6ZZ5xPJR0z69fupxkp+Cq0bPMgYz3tOysNWiq0\nUW77biU0nEJmCLr7B5W+/o99W7hmWSnqHzPPswaRMwY+6/dcBFa44kcnFZUh\nDc0DVJmB/EjhARLfyfE/IWpRDY6umPSL24xXRS6q5sIqSdRO3Ime1e7CnqOv\nPoISvH/9OVLvDF9KCVaPQDKtRQdphG+4VIO6X6GUuACt6Q13UhkJ5kImED3w\nEWWqFKxMdACjzaw1aHNgY0F2IjpLCNf+zhrvX0ALxytBj6n/87w/yyBX5hJb\nUDhwbm4DgG5/7SfR39JPqNoEXct+GgarMMxoev5SJZmeturbVXHNqQW+di0A\nAwZZIHP3nQWN4EKs3zw/P43eghzPllJrRbm0CeQ7Y4wd27hmtVber3h4VCmD\n4lsZlHuf/DNT4XakWIVmnYkA42uPwK+m8jdNJfzQuDSAPZyQpU7D76J5FWrp\nUPG/PaFV5Jjp2IsBA8YY2KcBEgc073ATrF/Doouu9U7kSsG+Z2k9BVkqVCLW\nLA13\r\n=cqd6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-arrow", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "941ba239c833fd83324cf7d0e4434aefcc43e958", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-U/GQhoDomKs8GiN76JqbNeXFinvwzSFk02wSJs5Xnw2n+/gi1coGv1PP2flna0VRya756u1N2qPZzyUtdbyYTQ==", "signatures": [{"sig": "MEUCIEdjhBozTHARZR2YxUU1Pr7mARJEfHNz64wGrIhPzfO7AiEAjc3Hc7b9Irt/vJuZncl8wXigt+IzzexE+icFWaNo9T0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmYCRA9TVsSAnZWagAAiZsP/j2MjgWuaqsejxzybrp6\nOlyRAv1vYLDpt+vd0uS+Ms9/2CX7X3HGJSzzB3WmbrXWudcAZTnRrXSgbO2P\nq1dYIxLSolXiyA3NcIZoxZwwHyHk6uWxRUkZdVL7I9WZoSAG647/36gOMvfg\nJlSbqfJC9CAA/pSUwGTeu+mYbBxrEpKUZfZN2Zc3VGuuyqocaLGAmYj68wlm\nrhr+AkIppZCmRjrBJFINmTG4FbvX6fhDD2IUc4topQEy/Xj+nTJUYu+igcRc\nBYIbkL/KS4/YMSkgl6JkKybs0WUNsVAwJQUyCjDp0TlmS5ej515pVs/XtlpP\n04WTJya8CQey9XcmJ1q8gyEjVt46ococHhFm+M7QtkWtYlp6OaeMETIRgLpi\nl9j/ihmk4EYJMs51WzZHOEUOvjZCaZl0y0hgBT2IMvsmbSOHJZC4kS4YUCy4\n1gTFZvYErT21Xb5cy4Qlu1+V8aCu7WO2f8YQuKPfvcEKf5g6hsxPBW21FUpj\nGo8HUCSWuC4ticvgnW3aW6Je47C2g+VvJ/SUxmgM62qE6JEhNYI6jjmKfcUi\nCOknMfEeS1MZ5aMnGq0RUpqM/Fq/VKMA+Lk5p6Yt186wYhtD9Aj5juPbpfD9\n3EL4NYlaeS4j2bEaEepXVs2eGFEZebkVLNEC0FF69oHNcXJGEOGwFxy2/3CQ\nSDml\r\n=ivCK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-arrow", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1130683e39e136f97fa4b05be1cebdb92d993ab0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-wFVGiaunk/u47P6u+6A53BBkYZcThcPyI8zQgvjAnb24HeSK0wgp2tQ2AGjbhp6jZG01XNe/uiUH4V0gH3g7Sw==", "signatures": [{"sig": "MEUCIQDjE01vBVM0NELBXnq0TxqvnzGsZ1GsAaikix32yGAS4AIgW5UsLiBZL8OUhOJ3PaOZzBfGfsUyQkl2gsbxTjidxD0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8801, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7HCRA9TVsSAnZWagAAQakQAI1zyDmrIFzU2UYfwK33\nELgcySGprBL4MTv9Gx11mkeScDsIluE5pYLXMi/sE3r74cA1XPrh5eX/tR0S\nanhffVFtKf1XFocO5wK9h3x5/DugM+FeWqYavF9TmADZRmusdFGZ/e7moXSk\n7jkdrNF2Zo+/K4chFY8b8WvOQOwaWhZPPFeHI+K8d6imNtHEfRL1VLtheVGw\ngm5ZVVkf2msRb/UOounZNmTVeIXwjEAT+//h8Gn48K4kpcuNoLLLopwGWepj\n2Zl3XUqNlJj0c7bO33JERbkDQbyQpbxytSStSsCe37SMhr1KRSj0Qd11EgzH\nDLzDMiv97p1CuX8YNXBQEn9iP9NdSEEc0d/9n9LBpavicdX4DNdIPE9e2iZI\nL6GwQw94sL/ij8hWxLqROo5p+NwSNgIM8nrsZXmDvyVdGTf/hWUu4mgpyKCo\nJ5t9Jn2Cd4loOXeH+qhFF2yhWoKBHhFDr4XCNylry0vra9ejO7scL47I/iXa\n5cYDXqUODmR6TuqKme9g+J7LBKhTRQJ4JCoI71eoyhwJfgal1NyzfS1JaeCJ\nmhjBbTVzdYjKi1h0rH6sIAjaQ/7huIoaCi0UkD9Q4zYmJe1BRWxmUPs8IPxL\nh8R6YlECZ3Vw7Y6O2f/KOqo+F8btCsfu4mxu9hz4sJU5Scu+3JrTUjgzxwxd\njr3w\r\n=zHpr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-arrow", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06b5a17f33bbc1af1c88d2827f25f37be95daa38", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-BHBAULgQYmj36BrJ+1AGhC5p4QjJaE+szJgJ1a1EYOM3G6QOeIQKYvIm8TPEdKAiJhAivK+jZFccsE4Blzqc9g==", "signatures": [{"sig": "MEYCIQDlNNBTzQiCpKi/hofav3p9caaLh001+c/j+4RBd6PymgIhAJuN7/e1SgB7V/lrYzDn9MYf2lnjfi86+ni/EPz2tWjX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlXyCRA9TVsSAnZWagAAMsIP/0wq8gnSbZPkdjXlVYJX\ncfgCAzcGVJWG/pXXDYFPj7L7CkGP89OAszCzWDy2V+EUoiH5DZt62oE5rKko\nTLAxD6CYcLmIj7pqvUBrKLhn3FjXCgXDLZmCM2gidsGzlht3ifnuvj8IeJj8\nJtyIkx7biNyVTeBmhTFib3uyWF3PXNVtALhTcCUBguOWM1B4Pr0GHTqER5vl\nUzZyoM3S/eMZwHtTGfc60XD0HjSonUpastYQ3Rzd79BJ0yz3HyAxz3nVAkkp\n2zHfiQc9hEDeYDT48DAhAHqKZLBxVhBzhkYdSgk2saTiI8kJJU8vNAJKcMOV\n4aHwtWWZckCHpxwmPBcXwwXH2+Ue/1xKhntH4MHsFlb9OSOYVRw+JVkut6VI\no6Ql+1RlScc7fE624P87jZaVl2/coHeHwzlhSXKbpiJCwGZdIw/ZUVHXmK9t\nTiveWYVN23yp+hS+qkFyKeRAksEkUxmScmS5zD1UUAmHdezWiZRBQXTrBcGo\nNscZDcD7S4yvhBK60u4gO9LOJtWIK3cTNJinVoXEmMFSP5Xe6OEj+0/MhYew\nhbZiF2ISz3uz/XxuQTSJVmTiIi5ucvWMSvDfHLCH1UAfsGgTm4Cmk6KnTBVF\nANtG4xd19l/qe+u/p1m3HeJ6OIfWT4yIEc1fwRYnENEiVA7tlktuU1kVLy/l\nktiZ\r\n=XqLE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-arrow", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "70b2c66efbf3cde0c9dd0895417e39f6cdf31805", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-ZWfQM3hTCXN6ub2dMUmMv2ttEB/1zuBlOZdeWFeCCJHwZ+yy7iPzWF6ixaNygBa6t451PImq/dC7sqOnDJCfqQ==", "signatures": [{"sig": "MEUCIQChJbSZ5Cht8ihpFBcVeqHQgSmalpfkQv2xdSvy1E0V4AIgC88UUCUGwI+3aCEh4ntJdVufIXL5VmTXqePNLedrSWk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9iCRA9TVsSAnZWagAAnvsP/1pb+/9Cg62n6Xix5C97\nn67AAl6l285QP5oyPml77UBHzW3Xl2Lm2UvTgUrEr2yA5xHdekGnIK7vuotS\nKNWU68T/K5izLEQEPqemX86yTXqfpEy2FANGeJkSVLQzMGjv7P4DeXkBMA7S\nluo2bGrqRKqSSW39vN8vLu9eio7HN7EaxnqaDvWC5Vvo9TmzgO7qKFpTHKUb\na3fRB9H26sg31G8m5Ws6wQrItC1sJO8L9vlRLifmk2MWK04/SeegqoCwDL8q\nF/8d9wniOIsXGoPn0qIRUjlnQqMTISR6JCZy/8Y37OmInuN3cXCnB5CSIjyG\nzfHjJB1LATu18s9nYvtiLhE+LBi5iTXXmO7+3O7IS9KoFoys/ocbMndbQAQR\nUMC1+akbnz0gcqT2S5XZ+29j5kklHcalNBZaNMqowZdxMxnSFWV4vyLCxPcJ\nkIYCIapve85qJhFlWSqShTCOOX8sZJ4LTmE0SmqDH995DY7fbwvHWd0OkdQP\nOJKnBbIPnsI+AYAZ3H+8ZEDmXEL3Ze9/CB5ncmEvT7xEPnXd0grnTP2zASx5\nUWBu6Ith6vQC4iuBj7eWmnlZ6ldRm5PjKM6mlNEZthhpkXTigwx5OHlEUFX6\nLXGnaEzwQdNz0fJ2/kKCd6aEqxOF8IeGPlXTVNgZhoJlKiasgX6ZIpO5fm9x\ncuAc\r\n=YpSp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-arrow", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2fb7e4cab626f87d4f7a403672c57bce74b0a7b4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-lw3/3nPmEeK67IgndT764w/65EMm5psXnr2efCeo0eWOERTnFAswNka2bKJUSKY02FHECkH4qVzhwupFyeYv0g==", "signatures": [{"sig": "MEUCIQC7LjevdU1q8bTps04T5R+6iq6OBRzycczC9HkhxFaCZgIgO+ZRNhyYWAPQabXN3gbCdB/aIhUieJquPX1aoN7hLws=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8874, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTkCRA9TVsSAnZWagAADnoP/1weXJ1Xi58WKjq76Y19\nCL5sp4R2j9IciaIW9d/ZU/u3nKtbax5JSd3ISEI3Ej5NF4gcEg7fp6VwCDFc\nG8cwhlG/oAWEeR9Y/bJo4aaBfpOYS/qqthvr8Rm5F/9koTrrcONZo+ouy9qz\nsM5Mwyrjz//atF/ES0muLs3kKU3sJrQ3oG3BDWn5NQWm2MhZdrqrvDuc084i\ntcwj+TSNrdQskIRzdwjfxb3p2PRXnbYlLgrWCY/YpW/Xe4D5ROC3iS24dvUH\nQKgUt87ILaqRJts3VXYmb32nhkhGt7KLY4r9rJCgLHQp7PmkMNrl2ogCgf/D\nVZ2p50ZCH/yocdkuOrGLQLrvt7H5LeTs9FnPdKRJD4ddz3fLqyLUzyG0ZO2p\nx488kwLUnjSLMKG+ZwjvqLHvd6jPjarKn9xxW/+0eiI3S+1Wf7lsBcm7TRwI\njkpPYakMGlZqTOHB9MDzcGjASaFjuMNI3hFTMj01oqOehRgctYBdKQigxL6w\n/xeOn8JSqLE/IS4r+q65HCUr/4LyfjpuAfISVTndh9C2uNqktiR0XtYsD98U\n7F8g6KDTHxlDF2tcFSNH/gP9yLnXN/+6QmdDTCc3iBQTNSXxSXbszM/EsNb4\nzPUyorgrxl0O79AF8CWKPEhOC/IM9Zmq8KmwlrAcsPjQ4LXm+l1GiILfIcFS\nl6LI\r\n=4T0A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-arrow", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c730dbe8aee8a22a4dc585b2359d32bf7c82ca58", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-KVNA3xLet3q9xDlubdpywIBixwQ3IxCF+F3IxN2tIvTao9vzxCJvptMiyXNtQ6IrSB4i2ZcDRvdfPfLO/ZI7kA==", "signatures": [{"sig": "MEUCIQCxZlXK9vIb/sBEz9osNScCaUndSXHqZpHfM4+0OW/ongIgfF8afdE9rKGqY/K0t16L0aX5TJ+w/MSS+l/dv/o3U/g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 994, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpSCRA9TVsSAnZWagAAcXgQAIvdZcO2cTpJPhikbcAB\ncwoX0WkZ+17UGcHD/sxK4SYK19zJzQYi4yzEwWiQwO2Z/s35XjW0IRT61Hg7\n2RggCSPXvlZEfmQz52NKHEdBJl7s0nblOHTnReeLz98L3FdDpG8CmN9dPyzz\nF0g4vLw7+ggG4XVstKllPUBwGhL1l1eHvR7xu2I7FXcSXWB/Q10kPTF6z+kK\nPHlEx3Ra3XqJDNOnF3fxIox7OlifXh0Hcwa7GeDvW7+OeNOAkqay8PRkWmP4\nIvh3maWW1nneTRZ9u6dS4uw6hPL/DdQuaTxcG+mR6NkhmiSyiIQwaWMG5+La\nd9CkvFBY0yQ3QDTBkMr/ja+5rF+uuS7ktmx7PNLUigXnRnrjCWVNXZt8sMDW\ndBAE9RDi+PpFSL4ZTX2tA6qCMXgTGOFUydxKOLW3BI7jbUlC1YjowUAq00i9\nlv1NzovfPkMmJsJUxjfy2l3z0u3lMC8l2hcW2CXir8VqsBYUoeMfMgLjZavD\nQW0dyKDz5xe4Zgrtu+AifgqALDCqK3RZc2lByNvwvC/x5ZfzNZeJovvBaixv\nfbZlpO0Ac3pEqQtuLK7kYQfMl3w3ds0NdhxcXbghYrfwanPaRgYFonPU1XtA\ntKZFuBXojHkgYAYEC0XARpsxn++UdItMyoeeabveY9Uqi1Gu9JRzgvjWSCQl\nyEiX\r\n=EQhE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-arrow", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7fbcb48f05c01415ec676e3c073c982720f8741d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Bb7HlfIrZD3COIIqK7wGthRTNBtzF6/Texbc6wBEbvIFzBDZAMbrxLpwSr/D1fo9oRcNbYM8ycuDmGQi48829A==", "signatures": [{"sig": "MEUCIQDwfH50t/oH8bOIHg05O0+ejp0liMcTmQkiz7no9C+7VAIgNX2j6mnJGQ5mlTpZH01JYIuiSlQ+7cvdDp6Pmb1UFWA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyJCRA9TVsSAnZWagAAMawP/38kmRc8HmEGeGp7VEy8\nP9Tm3HJupi+l7EX+GMQff/IUhSK0vGFvXvDHWwJftERJVnkxKXYr0hLUI4dN\n4Ut444ZicWO/Tr6PLzlo3SEa2N8EjWpnEhxPf9BpE2EzDWBUI7sNRuJuip00\n/6UgJe7LgZN/6lYS5x9zgS3l/c1b3PsYNXL9thM9Lm+aH172cvg4Brk9Yn3j\n3BV4+3sj1+cLodttAhbLpo45jNTvPMjgGWffVU3Gu6lMRq/ELrLSUB0qDABR\nlVj3k69p3HMhhXaz5tIchQLyyh27rka3GU2EJNJ0y4NcSod8jCu/DnOgZn0c\nEqKMLuXeJghBwi5q8BwlA96MmxlO0VvgcdZARSYrV4PfLWozAn5051IxLFdQ\nL0t3pA5VnRAS6QMX8/Ol/ISk6yKJGEaLCdBw7XlkMVT0COf2YCzwxCVsZAgQ\ny2s8qzGOgvlfkotIgSFZ1cpGg+yld83ct6w3gSp4GQq7v+WtkVQ+bjxX8Fo0\nwj0gjaf+K8B5VMyB2rSU2lv9FB3xYrTuXUuoxeL3Huethyu4rjRKRl09okLF\n+J+C4ore/t7r9kWi5QkRjoqsNIWFxT2UTY2M307LoNtK7qxAsK3wd+i6Ixnk\nyw3jtm76YaIvxT6HVBg85GwEwF2aUl/+QAzPd80Yfyi/b5S38Mhas7b3ml1n\nZ3Mz\r\n=FD65\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-arrow", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9491f7244c574c0a7a281de8bf8c55b85438948d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-BfrFYTOEVjG7lukoXcveEVgNl6+hvKdMlxXQhQrkZxNYBq6TuJ2VRWfcWzCPJNbVv09g2NXoh2cBGWCLMVH1nQ==", "signatures": [{"sig": "MEUCIHKHS23weHQskZI5vrv3NF50SHMOhFONSSkTWvcwKwkfAiEAxKiYXEnnYCXyOHJsurgDDU2sWFYQDSyTHDSQnC5MCuw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmWCRA9TVsSAnZWagAAnv8QAIucD/1Kp95aniuzyzvD\nseJNpkOAKeD2ODDHBfxT4670287i+DtO9f1EFdZ8WXXEuiW7QC6bZcEj/WfK\n67l6rnbZ8qY+8IFIc05PYWBjZ1ds84fkJfaUhcQlb2mbMXyR+zpVifWLzn77\nZT5d2mtMi+3XlSHSmmG6KG8r1RxGKAN9DjFl82ZdmXOAV0jcD3Ldo21R0sIy\ndjxxE/FC9jfiJWDXDYSh8hlM37jpiYZRoCDZLIOqGZqCx6f31A8/1OkiuxJK\npkAVg6ick/LXWfCfqKhnh3Ov2O1A9wtDxfys0tK/vPIBixSqtyhSxEvCoQ5D\nzVyP++NVZ1Ub3gwf5Pohe0fq98V2RWcJCEMYZXlOhexaduPL5Qc1hFfkIl4v\nqrmpiE2CnG23JrHpIH16pB4Rl34H4B/vWjLPD5sLllneKiOgoji85Rd3T9J4\nlxU1DkEpVcNXKAVf6Lcmj5A2lzbFDXwMjMWwjn4N2BW8Zk1b9q4V9WIrwFQ4\noJ+ufX2AvJE71mzPOKD4DzZq9nKJDVzLOd4FbIMrQ/ik5szfeZmp0FQzWpiV\nl74Zsp4mfss5dbDdwt+acFrIK+IG5xnQDEeYR4yEiI1kAN0j32QHyaxNS9zF\n5q2bAcmBZl/9/ZWJaMMpVvLmTcMb1ESn4s/56S12OPr0cvPzhMFtsfqQthK7\ngoho\r\n=nNv+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "12dd2077d907bfcee90bfb9e34eaa28bdb17add4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-+JyuyO/owWnVOk8SxlUx8ADenrYUxXAKMrc5fFnsnt7bU5vWs1rxUSYZ8AZcw8tSAB34MrgWwQctfRjM+8D71w==", "signatures": [{"sig": "MEQCIAKuTFNplR4FFTS7xb66/w4+Rvh9Xelrhb2k9EfzGNiXAiBpAfCUxpoPyFASBE4s3WB5HpI9CLaQ7/Nx3+ixo6fBMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQImICRA9TVsSAnZWagAA+2EQAIvhaEv7gqC/KYfSQB43\ndoODzNUCgOPsVjcPQGFXV6lhlc8SOLjcUbpaer/8sRmUA+FARPx5C819wpsH\nCL5Cw8aLrIfYFC9fPBd1vB1djK9kHNW5vLombqjHXMw+ESVQNZccxZeQncC5\nJ1l4iQ9tktlVcQCUwIJpAYQ1JlgSn1DuJD4Og/LNRUsa4CBy6QJnBusIIAVj\nYSzFQvFR4OmwZr2Q3f0XiizacJuZnSE+Q9438tFijPW8x5B8D2jS1oyOfSSA\niCSgxeBXb73eK9TECcX7h69dkuOvlwdYfZttoo5JVPdoB9G3NzE/uegLERHD\nuaHK1A6o7yBttw/6CthF14pF47xs2L1VAMb+qEBe/0NuTMMjN+UHzBTBizd4\nxqnz9YV4wiPTy6n02MzmOufveiMnaUfJDmzpWrvp35nF79u3tjm1S7raIV3g\nnHnnUhTDmoJ5TKHXtEKb2A712d+64V47eq0n4OsgUTNDJjY9bCz5l/zg5TQb\n6fp4XwGYeNFvdi9vD1N1cLEoExkFYEKeGkTUzh/SzoXJAZKvfEa6lSMUtToS\nCt8fos87+zP2oXZMPHsuK1IIArY5FeHMcBR6y6Sjvz3TV8YXr8BO9Vl/hVvZ\niZd3kMeyFF7rqg65D46Prb0uRBz1YyfYyPJt+Ie7SYBPLYQlDD5x5TZjaQXm\n27AY\r\n=6Axe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b5744ba1d96a5674ed08aebc52d2e6415558379f", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-v+m6b5Bjs0yd1OTsrO/x7rsYfhrHovRWGE4c416F6+yfH1dKRfiOpQUPTzMKRzCtkpAyK8zvjroihnx+G5JJWg==", "signatures": [{"sig": "MEUCIQDj4Crjohs2VPVBffCX4RsABCIFEv+icU5NtB+qZHJ/fwIgSHpFhqxh5l1tDG/28QbCYRdYl11J3EhNCOe8JGSLMpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdu5CRA9TVsSAnZWagAAs50P/2gRG+Ib51SlGhTAMqG4\n999H8zgdSbMX7fiYK8sXK1Kk4zGvQV1j3qotSmGAN92Eli+FzhXrowL/M+08\nW6V7WQsINw8uYw17nkVOLg7m6z/ZJ0kz4hTSVlp9HwJcDdA7Ig5eRi/vm4u1\npiBzMzbVwhXfCkiCFZeOhapHIqEoluwIFNRN2XNs0Jak3apESqb43nThH4t9\nhTw2DS6riN6m9+9mANShsv6FNEYPEGg83NLQVvue3dWu+2MdKIbnMwMbg7bE\ngUpVpjrv5jXwP2irkRx4jsnixMQj/DdJUCnXKWX8ptJjhplShuoLEusXOz3E\nejsY938btRGmneNOFksq1nIOvKVjBKzt6j+xWE0FOcHALrjc94RpZKSFlAR7\nQGOV3ajzsbg8IVXXM/IMCdOEG2X3E7lpPpJNyBK65qrYT4lZPh8siIHdyPjs\nqOP9uv4xh1pulAFUST0SupNixDnBXoJ5yySR+huOKdUuMR7zxeEb9Wi8QSxv\nfQDdW6VN+T6ipCf2Y96FVN5j5G8LSz1iRMUiydTenFIb6aWRCHtFDnMbs63B\n73bfP57miWf3QADCc8RZhnoa6UqA3OwImPgaYPMabuyJj6vtCyj4tih6dgI1\nK+kt6D4Do18+lEhdk4+O5ttNJXuZ2Zc8/rM5LI5Iqk7MrKHyw0r+VRlsjKXl\nYMCH\r\n=mh77\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9986ae0199428d17e74ea06a5cf709183bfa2be4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-smprf1qGxp90etBU4NwGoJ44SCunRMhI0RPlnye5IocG+e51edBdcL2MZ9/6aY5Una5FhxaJdT1C7RIPpigJ5Q==", "signatures": [{"sig": "MEUCIQC3uVJDQMCAKlGxnMzeqXkv6n+v6fJFv6xW4SPsAsw4kgIgJKF8bDIuB7amVl1BOkuSliEGOPvrRq5pZ9GK3WbpX5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0TBCRA9TVsSAnZWagAArcUP/3YQqsuMx1uDfaNyFllZ\n5F7wG/TXiKAqTCyV5T3NzU86rN5SCD9pKv/r6Jn0xsEykfX0nP5Lv/XblXuc\nX2dRUzF7gZIh08q43rq/v6/oUys+LZpmrWoIXPLU/6MVPiulfRtjU9ts+ejZ\nWRTt9sdeyLgdtO/Vx6ztkkc8VCriBTg2bnDX4WFoAWaK8MI/0D5NoQXwCHaY\nbjxQLHl1VhPUqWeW5pIScurMDueBRZ0EK01RQfxSrbSxIvYASAyPVmmXDnAY\nIkkS9xooCSVjQFbfiCGjyUuLcLXwvs8e7yJoJsT5wxAC+WvQSIgF6kG6+Hnm\nP4X0EZfPmdRj7A2sTQFjf9H77c3dwGjTOSM1hhScfax2nvjIVKkNe/qnrrOo\nzEDaGtP7YITTGIdX8J8zKA89rdyroQHOHTjB3giViTedHdXVXrZ7Z2OL4Wfo\nHHAhtPzbGi1QlnCAHZgej91gLMOeyBc2lP1sCRwbKbmb4sr0BG/4rQkPgK55\nBiFCwHQR8vtn4jlb0mVtEwXwLEHj50qcnvwkPg76r9NR1n6pLapL+8+BZNEE\nMnoan1ER5evMEqd9Ly1a8mADRiDucKzpOqneGY1GmgXgXCi8fCn71e6OLX3M\nFtiArrRbxwrnB++WchPIJ5zcYDhqYKtC/7xnd7oSeWtO+kFcXl/c2WlasccX\nayrN\r\n=32a2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9e958382df6e5760da2dd45b8baf1cbc05d86151", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-9JeSbYQg+OWr8Z2blqLlu/wZ6k/p7uibE0lfIu7drhTSbqyRh5umqD74WBUQyus0n8KM2vf2rR9AWHuEtcQmaQ==", "signatures": [{"sig": "MEUCIHY0rrzSVRkLjUwjhZDH8C/FLstd8LuzG/7F++kv+I2pAiEArdnd54MHIKj6uZ8NJQDz/MNRnLDqk7mZvmZmka4msBQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1yoCRA9TVsSAnZWagAAJMUP/00ICbB9Ur4muqORBHY2\n6PZzvrpu+YzlFKvT73iFgcmnWDLSrcPniER84yTPKvBHTJQjSu+zdvVNr1qR\n/Vq5njQTCyZWsCJWseRWEUuCyJkQ8zCYSCJkPn/3ubGIA74ckMYdlUlymYxX\n3mjBUcvuIBCFt2VZBBM4TW5rH9aBGV9ISczQFMs3R7HW38VeIOHcTTevlEAy\nrP6L6n9h53/r7dubHZ95gBMRzoz/fZSBF4y5jwPF8U4+MsG3Qn72YoxFig98\nDQGO/NTt7fNqknYAlZzBRa1Lp0GCpP1Celv+WVODses+Bt4spovtnv+q0ZbT\n8D9h9SH2O8Qa2z9c0FDauHq+0C8fLM5SL3I1hIo25lGfMGI2TEsyAZLLH+t8\nF2G3G4v1qFFqVdVXwgJvVJkf3x24NYWIjkI7aRGt/43GtjuTqo+r7JvEwFR+\nFC5NfEFBvQeKjdsCXY8QzMYx34tu/1c7TprCUZ4MgwWjktMUGiNknCACExeC\nBIKjw869ASZzsqUG095hUJ6+qqF3LApu7jPkoRNj2AZqED342tLpQpeePCCY\neXoDnEcwyCF5h4bFkZdi0nlboMhNUbyWsRawBGHvU95frVJNAfelOILdTWEY\n8L2yUVSU78/z22Aj18wqwDPes+OQ6ApAQuJDkzgzYjh5K4Qv/LzdwU5txkwQ\nQFqC\r\n=6/V/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "14e8d5e11db78ccad6217fb4abc116655a08cd7c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-j5otHyxt1N1GzXBWe2qx4mLXkbuYPvhe1XZs7aEhRaE/W7o/lD6PxLtVqHFk61zLXRthZVP4nOYZlkG1E7r4OA==", "signatures": [{"sig": "MEUCIQCuEtYzNhnAUrmV22H65+uChHSPKv9eKJoxQ5PFku9mVwIgKO+Y/Lk+TKORNtw+oWGqqglPdWOSFW0zNpfycI/dzgo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFhmCRA9TVsSAnZWagAApkQP/2MZDUT57CPCZHw4QW1X\n0kcle3qm97v5tTKb1YJdeBsVzQcqoBQ2YNnrfhUmUC8ftDKwh900tsllZwHA\nx09uKo0ESih+jPUYy2k0l8fmzF9wq+SgH/Ox3rdgHc7iLVIkbMHKgZAK8h0Q\nv4JWjaqd0Lz9/89vQ4YkF9RyEqJ7xjb56DVWNYbtBD2Bt2v6EdfX9MLaoYyb\nJe5gRgZASIbPg4mwbWhG/34s3iTtyfS+xlYHvKgCD2Cl3peQ66wOWnMpcHyu\nvUMzBHg31RravQ5RdyLGishEaFOpotUoZJycpBSrDyhHdSFxrNOEf0BKUEka\nasFMBnSKhi1+/h65SH6nUiqT57FyROUnPJ0xuvPbo2U1AJmw8kf/qbHNvg7/\nOSTVBsCaMF8jehO30OnzJmYFJQA6VAemZ5Ue4327DbKiw6nLR0G0oX8U9ZjO\n7REdnWctBjzfx52Nmm1ZdezkV1m+0RJcBXtiPLTmFnMOkufoTz+2chRQyvkJ\ngcPs09C5fREDPubBia/2GUZ3xSINDLd0CBFOi8IfN6Uv6lHrM9G+wh515t/F\nd17mPmEgIdUwfhS4Rm4RqsJYiK3KmXiVKNE+LlazGq4ucLSZ+rYkeAbwx2zG\no/5TWTR5UntMVnUThnPf7o39yJQ1n22f7c25UAxjjX65R5ySzzxeS7fl3vIt\ndjSq\r\n=jdwW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cfd64daaf831d53db1ff7e0c4068e269734c7da4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-wGMP/Coty9YVU1QcqMPguCrl+6rfF8RWqWcjKtI1T6csoPvmCYlGF0op0124tkbBI9z1U+pIH7TIgfZViW6Zkw==", "signatures": [{"sig": "MEYCIQCzuajDExmsmxiQ4OUJbpdPiOjNJzpS1X1ksoDUdVLtdwIhAPtu+X0K7XcmJtAOINo+T/SKwXt5A/7tgMdO3FSJpvmF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474}}, "0.1.1-rc.7": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5f55901a612eeddc54b89998760baae1701cba9f", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-mGVKELINWU6WwZ6vQvKUwcOMf8kmUvSFEjsPUq9u35pe11jVcKi5oqzQv7O70rAWtgQKz+UZRSt6jBsbPyQXzw==", "signatures": [{"sig": "MEUCIQCEnS3ti0wgLRuGIwNyfUbRWcimJLeohU8fEAEPnd5oOQIgJjEgGXibw8cX2iUTtqTE1nuCWz1POxO5NtmE88mxi0A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474}}, "0.1.1-rc.8": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ba3ec0dd363cc812f440b84a760380255d1c75b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Cek5+7+3awnYFmc5ulVks24n2HtLAJSahF+jTf+azC1OmgSiB549jhzbW9vFd+3p6SBAXsNTVxTIfN191WVwyA==", "signatures": [{"sig": "MEUCIQDNN1++Did509czKMhRy/FxtQrceWTmnDbFedcqL1vv/AIgAlyhtARFmJuON044/y0LjzIUcDlX18uD+ZxzGn/PI6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474}}, "0.1.1-rc.9": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "10a558c0b626574a6e21fec5fc665a6e9fdf90fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-XW2iU5odcuS5VvhTbPsvYHhNvXv/w3lqykN81xoky8faOOCIz/YrnQDJFwhZyZ3CgU1r21JUR1BzFfP6fx+veg==", "signatures": [{"sig": "MEUCIQCONf3ZGhQFblS+D3UNkkdQvRi9MlWSAKS48Jbnfv6J0wIgP8KqFMd2XLsJRqKNED4ZZ65ydXyJTuJUQNVUWlpTyPc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474}}, "0.1.1-rc.10": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a11f76316bfeedd6d0880fb2e13d20a4e86e1b2c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-9PRB5xUQ9WYd1YCdlDK1WGeNI3IPIlvXIP8ifGQLwu5FUUE12E1iKJMZsVwPeFqXY7FgyhAyL3A1slYeeTbDmw==", "signatures": [{"sig": "MEUCIDb0ppSbVJpMj/ePayt6Jn/YxaKm1Obl7L6ylmkU68cOAiEAyT4o4qRS1pe9Zz2oS7SujXKFX2Z4O3vAuGXzoCGlows=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.11": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "54e8f614222d054f6b4ae77ea5cf342b86d8ee9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-txZHHFT+1qegGaR0Grbo0s+xfYFS/ELaKJ6XEx2K/DarAo4KNNH5WOyMY0IDs8f71x9+2pySb5pXSZ220eA+Qw==", "signatures": [{"sig": "MEQCIHu4l6JGP5UxUr/Ci4O6db0UwWrWX8uFVRuLFLsG/zCgAiBgsLMgX1CeKyPmy9zRPbP5GHveUNn4tRLmzEopHCvaoQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.12": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "474a58f779fb3c9aa350db2955748c0db6ba578e", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-fpL7lncuGjfhEnU+jP1OeB/irFXPm9JwgiL9AqbZtAIbJ8Lhw+ptSSqlmgwENM83vPL/Xsuq8e2rP9NMQzujNg==", "signatures": [{"sig": "MEQCIAkgcVxviXesC69llUVx55D0wWNVkmCHymnB/Ao5qWnsAiAbi0xazTVQrdmyFPa1WnImY7PNZHrr1T5PzXRv7qt0gg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.13": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8e656290f8d3913580d439001c0c90d554aefc14", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-iRf/wAm/gmLOWvZGp2/XtqDHpMj30m3/XQLGLoS/thdkH2DJLXfEZpXGumB1UvbTU54tGlRh19RMY/WKe5RkJQ==", "signatures": [{"sig": "MEQCIHLvdNdiHs5/fLpEzA4Ww5UoX4tf2bQO3GbpyN8KFU5GAiBytRO7kSe803BQ2u2uCXkUe8tQ7LiA9VTsfh6LzzIhIA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.14": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "95324a40a46d02ad862ff60c47b1e7716f55b368", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-QUJpYy7x6xQYRZmgirqr/i13ftcWJhHPLlKaZwaEvJfKrNDeEHmdm/gJEL1sY7N+Lt+GEcmTrzxPWOYwBWJ5fA==", "signatures": [{"sig": "MEUCIEQoK/wCauBNFT1KjvHcSlChPBMLvr33UPCoBA9h1lFJAiEA83isHtgWp6aPhJ9BvSdgDbmERdvZ7Fl20YugXBY/cp0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.15": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "07aeb26ec3d05149420fe033369b4cd8761652a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-pTDUHvtGbCALtzpKotghu4VGzb4NEeGXXFgWeiCZ6UA0r+9StP36sBwFh+f4m1BKpldoTm4KBPix+lETXaIxgw==", "signatures": [{"sig": "MEUCIDAgQ9/rMG8K7f+1FZVPytwTXl+8yi8pu/dINx9T4XrXAiEAzg0AO4c6ieHc+J9r8bG6ko6j+gtFaJasamJ/kcNTQyE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.16": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "13ccacc65f2532f2f24a75b1f9b939d2d5c3e28a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-DSb43aqUAkWM5EQGmD7qfKM7FhZPN72WE1IbhXBDr32kH0yX8EPgsCq+/6KQ86MfVpLp4qY/VIl6X7QQF9XI1w==", "signatures": [{"sig": "MEYCIQCs4CP1F5LVFgH3o8m787mmssuOoQwYzbJDyUnZdOuTwQIhALHSuKGbJAI3u5kovBMgbLCyPVg+EOpVgpICgDbv055J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.17": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "953da48a696522f2863a5c28e9d535d6ba615051", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-ieKSvcx/qLTtACcxfsXvzhEMrRzMDfZUoW1jmWLKaCEj9c0u+MMk5+lnKYG/Jtw5bm/JT3j8qgbQuhPReSuCsA==", "signatures": [{"sig": "MEUCIHf8baYyhMeAWVFdCL2sq0ukvVuPu458KnywazP+E+KHAiEA6g8emMcoD9O2VoApynNSjM8uSv3Slw59vZaeYnWHP6A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.18": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5f215686f06d6a0e7281d067540cc4debfb3ca50", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-2b0JzFc2vJMPGag6iWzL179m6jlEB/4Wz0uD5IwacCV42q6306WiD8eoeW32SZlWjcB+6XQ+wfyEzpk9b7NvEA==", "signatures": [{"sig": "MEUCIQCrm54FOVKQAGBmOvsAfrz5kcjnp8+ooICml3gtT8y7pwIgNu3nYDKTrb+HJaYDpD8a8c04ajAG4u0HdJYfjXLlBs4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1-rc.19": {"name": "@radix-ui/react-arrow", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f259539c447ce1bcb65bf623a4bde95eaddd8a24", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-+vzvCHYYhUapS7YekqPiZB/TbcgrEsvmP1OHEuc/2OIWeIAtvZ3gAvc56vjrhrke34jcPANnSjb6i+M5PuUjWg==", "signatures": [{"sig": "MEQCIEPPVf1MHvgSQUkNeTolQGn844l5MY8yUqbk2yRTBklnAiBl9S96xfPwhpbyTfKSFK2i6LEJz4jwO1aeqwMWjjZ4WA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476}}, "0.1.1": {"name": "@radix-ui/react-arrow", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e8e05444b37b9f71bf712a8cd4dd07dbd419e749", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-layhfVIJE/mahiHUi9YZ/k2Of41TO20y1kEynUEq3j+KLUy/pi0mjb+jrPYRqmlznEl8/jye2jwilyGs2Uyx/g==", "signatures": [{"sig": "MEQCIFQHzmCs/YnszLmwm0fO7kfz+/NYCfSFoh4UDNkyGA0ZAiBlGEHuvcJ/CliGPMiJJ5wbgPQNkodgEZ3vaTZha2UqJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8436}}, "0.1.2-rc.1": {"name": "@radix-ui/react-arrow", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "91206d7e168cb59f31ed2cd17e1bc87dcd8dcd0c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-21EHGaOu5FtkTMOb7JJnYnSlkGi3/K2E9gFTfgd/AOH1MjKqPDsbVurioWWQ4ZxxC7Pz382nNUDm1KpG0jNwhQ==", "signatures": [{"sig": "MEYCIQCwuX6U6slE3UcDVlMsX3qofkQe3KOz0ltJfkoWF/KSXgIhAKwPRQbYtiv1fyYTwSyMTILolRsa7n7ERWqZftmHOC00", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqh/YCRA9TVsSAnZWagAAEI8QAIXmJFQvOPXTJ4reEkHz\njxjv8gnnZto3WHhKOLVKokt3iJ2W1X3zAi9i2oRl5LEFyTWY5MbxtKuO6Zfl\nKjklFghdxlSffZZ1SaXIuZa9BvFPL2gT4lbCsUOzSd1irUKYtBNbOMo2gOX5\nhAGf6776pOJV/91mzFqiF5hWmdF7ElKtxwq5pcHcn5M6Gketh0Ziet5XDZYK\nfWjHmdyGWG1UccWjhri0/GrBodNZXmHwBrEGWc6FT8bROwPr9mb+4s89DciP\nBmzNX6FlwsNIulIeAbWD44xDYhiZGSmxCoHl+/cZEiQn2VFFneHz1ZZGVwMr\nif8Iv13ZamQ91wBRXv3cb+HIulLoLoFN09DpfjE4qxngpn2s6uiqu4xh4f2V\nm4V7y2v7COBpO3XqAQVLpGYg5V/DNnvMohVSmMuoic5sbKQt/9NAvQRNhZtC\n6HpZcc3A25floocYycULq6tuKuBMeqDLZrSoUVO2Rxc08EyFSTgBbkyu+hvb\nk4q2AVlPMSw/Xugf4z9Jp5aQYZiKDj/7vJykdPdL4dbDueBXMMA5OWIrJMhN\nDo5OO8J2XNsXfIY3YEmZyYLGWM3sem3WlpgmgwAk+Q9tuKsOqGULD8FS8fCh\nN5s3V0uvJ9jQ3vZdDqnMfNFx+REOgFVqqToajS3j0RJ6EDm59TIfekbncYTh\nh6G0\r\n=93bD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-arrow", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "46c744a1ae20191e4c5ffd14599cb66e74be1988", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-LQ/2iXkQmCX12ohB0ND/+zfXZOw9CpSLoHo0GuI8Ci/hp+m8hUUcj84AxJtCtu03O83dEqLukK+5Xy74Cbwltg==", "signatures": [{"sig": "MEYCIQCP5rBQcFoWfSruucaOTI8XEKnafUrmu5zhKX21FiJ+0AIhAPNlRnVNdfPHRhrZ2Ysuxec2pDPnTyvm+q3pJwxJn+KT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiM2CRA9TVsSAnZWagAA79oP/ilsVCeGSIim5iApJwYZ\nGP1xGGs8JLSxhdY3h1X3pUKyJERiuU8y4oFuFv3kSikuEircNZO318t5oauQ\nIM2ipZiBC7/9iOKMlXnC6I2bF6UBp3hQhs/WcvXKLdzXGRxscHJ5lMzcgy7i\nHqbroUuMf6h9Hi4AcGJLUPh0d1cRi1cVzQEUaP+ZgMDvu8mXkOBv3kmQCFmQ\nrXpiuNEtvrEQGQ3MNEA7odtOh6xxKJGP0wAPX8fSW87Y9L9p18idtf2YsvNc\n8apQhJiHeLV3hYuBg6STPGGMcrwmsgWq1GaxfGXkekJSZmn4FTkE7Ikuy7wp\n8ieDo0N2fw3DeWF0LQzFkwvvPHg6I+K8RQVpxCwAqdvYqOKwxMSL8UlUneJL\nte1CmocacBt3pybkzVJEbr3FEN+W0tZMzBJBGhsKXZxP8OXiMNfZmkeJfsjO\nSpwSyo+sLOYqqUbOV146i6kGa8FV9iW1hXJzYP5VUjuC3rbmAEtgI1itMkSA\nbNAwp7VyRTBHQWgl5x9kRc276BCceebSi5MZc9tCtUlnf5IhqZb/Ly5VaIIN\nazIt0pDjrRy9x4CnH+uxvKTUZOcLgC5OjhpCTQP+SoT4tN6AIaTlA9xFpVwC\nsyx51daUtH9Cz4/bFGXNS6marO5QPMyriby2fPV1Fc39Fay88csLXYkgErrQ\nAcHW\r\n=GSCf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-arrow", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b031bc25c85eaeb5c11c5120a9941f80586b4ffc", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-4EopTizaD2pKTJub82rQz2/9rKq8Pa9ZETv8KLB+tDIz9Px2dAEVgRuJCfDYK9sYQRGpG3o3ybogIqSgfmZsZw==", "signatures": [{"sig": "MEUCIDz8IFbYk+rLBucJJUBwxCbJ8VIP7ZacAyAKTnGT0FKZAiEAyy9Pc6s/jzZQK3Jy41r2hP5JxxA2SYpIAi98YA8ZstI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryiFCRA9TVsSAnZWagAAcb4P/Rt+eJIKHWmhCIWYLYAi\nFGjHhp5G6gVTMeJ/ZfKEMOXmihXzMyQDMRYGm4U4vAE8Yu8JlOvmPaaEbf8b\nMqj35+dLby9z56s11zvDXZ7xlgyKp4BGRrMNQTqrf95PyKSBjFsj+YUm0Fl6\ny8lkuCugGOOfwZoYjDJHvZV7eM9PrZ1r1rQDVLdDfnXK0f3znIN2SFCSB/37\n1IAx6tgMAN/48ssG17sDIhuW7qOMFHagypeza3QNL8On3uIJ+Dq1IkRjXg9R\n0zf6Ypk+sZKXjW2HhAXnJKKHzEtC+lh2s8rgnDG+zR2fyHMv/zC9ijVftwrD\nEn1Md1QK31kNzx9Vytu3Alp1UnIZk+zXRTaWtvHUhZ3A0o6s6o1S1tlG3OKK\nTUjGWhaZlPZB4haG02bgJWOJoYHimMKMebHA4fatkVvFooMkm6+boLG3dMgc\nKv7ynPqIbeeH8YHvPqpCWzsZogmkfeUg5yckt9bTAWfXExtgrbJ8B8Q7tQrF\n3ZTfC/0oAtntHfPf/xPzGW83xqugue9X63czHBTuEygY2RSxSxJUy9qo2WIu\nCCntNMd3+8xxgLl7axYCbn0cMRvmhfAA0REJciYfVann83b9pBRldcIxRFYL\n1b7fihSr2ltIHfSCYfYucRGfrk/ELOzLAbvpEe+59Mu6yyTujy7u24efRtVu\noRlE\r\n=hZOf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-arrow", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7e91da67cc8d332eac4024692ce0baa6d9c150e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-vLPXb7aRhc/cvNyjkx3vp5tbWfIq49OM80gtzUulBfT6kPxfyPDNlhosuWTJTFaSdKnnksN2wuAYBy6gt+iqRg==", "signatures": [{"sig": "MEYCIQC8q2esanW5RgfD9N4G/hWplbWx7jdkvqYeN7pRWvP40AIhANK12CrWWS6lFt/ED240zQUs5WJrZc7Oqin20pbNKi7Y", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzQECRA9TVsSAnZWagAA0VsP+wYM5ZN5WSsaq4NShc9z\nDzOsc1gqpSPiQOsRy8biT0pTyuGN7xCNpNlDkNWzczJ5SBqOAI1yy1CDnQO3\nOqtpU/R07esdEmlSpIO/UTkrDxOpun1gMFAfMsNuiWsdwwoSnp5DaYYBa2y5\nKrGoBnbrpfKlxcTNpQWDEusucqyX9qnYv/Bde+hvlamiEsYRc6sxv648jpTe\n4TF1JWEPed1I/qrLcp07yQ5btA110i8qbupauLZ2iWRWKTOuunxFTuxu3NHI\nAiAL0SVkTsYhRV1nj4iUzhXYvfzDOrNwXvaOHwiD9nyOttpht/6O0X30BIoY\nL2fSJIL5mPSvHb8GCTf4oDAxYIOOzmvGssVDhtk1U09/IrO7ihvA9VCB6lH8\nZXQ/X8AmYjZDlgnOAwRor80pjp2+2dUlG4syghZIMOXmZtn1l3PWXahicOPp\nTDEjtQagygg4tTYMGLtXL2hsNEyL+nRWn2S0RGQYaQ97Dl5Y/UTiZDbfL6us\nGFyYbqvTVj37fS49TNId6ypdyuAFGot2JtyUW0CXI/L3WCipoZzAMnzPRTta\nHwOfqbPLoUrAK62TwUTutpxxFVojx9lDx3GFHlIQFoWuYcojut9ui95EYW0D\nTLiGKM2IU3zJpU/9nO+1RXiuFUSlsD0oFfTY0gbdKDsPxD9JnjvdgJcK9+0o\nNphO\r\n=Vg19\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-arrow", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bd7517183ce52707a230e161ff05094113969f83", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-duO4oQGbDWlPvvfBaVZZ5wZBeGe0svXtWqv6A0QM2EtHefBv3PCBRIdXFUIa5UmxSafbyUDUkRaiXjFUdFmDiQ==", "signatures": [{"sig": "MEUCIQCfGxdjfV6qWIKgd6/KBgoKMizCxK5yd41r3LHYDWX8gAIgF/iroYbfIrhiYP38S4GPRfYl5O1GWhfuNox2iezztYQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr41fCRA9TVsSAnZWagAA6pgP/1YIbiH/ktZO7WfIpF75\nT8uYe5DIISoJbPaNjFkn807fLJxVq2WapvY3AYS/GLTGDNg3N/wnCTm8qzfc\nYkYpKnrlx4pu/Hyb4ijBCvRlovRN89XsMj/fs93Jt7k1lCHccfxkhsBVMM+A\nJGaEMiWIxILqwffWLqXDmLMhGPiYjzt77LDOFU0kXSDPDV5oCe3GmJ53KcFj\ne1MHQetHIxBDBq7khVP7Yj/kQgow6HD/D8e/wJmXB/76mekTeR+wjB+3GbVh\nJ0pMybLTa22Q+FKNKtubjDs+Wkvin12UWCmJE6PkyGUOCo7/qL5CzQCIAYZ+\nimWviUPACXRWi4cVykQ3K2nJNkZHD1ShQOhgW2DxzH83clU+U+Ak49Pdgpx2\nN9TxeymlU0gqtwbbbz9duUf7OY5vNtUxQ2JSqqM8eRdYepntcEba8uNv7r5X\nHAeivDkuHPSG5Ntwbr1x4gzwp0FRQML6PrTpMdaeXmYbuOXqCOSQYL5tVwgz\nsMDPZwlEHr6OYd4Kzw98bYMCFtydYM3DgI99nS1SVRyB4RevPtChWULdhJ1t\nP78njiuU9Zja5kDPG8GoWnNGURVqteNsVtlFDcamieqklEqIbnXXyk53AQZ9\nMvRdd0Ou/3k5XIl/t/IMy9sSceFdFiuovQvX9DzpAG2zzkbTWzIYstjqoE0S\nBM5c\r\n=oh1U\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-arrow", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "23058bb4cb86662131baad30ce21ce929393818f", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-f3aLDQLzcd96yswYoeJdIpOPSw//jbNLTjsrq9ra4c5Atu/Zi4P2icZ1f8qCmNubrG36jSvwuEkcCkTTDLmbeQ==", "signatures": [{"sig": "MEYCIQCOOv24MUKIlTi29W9KDFefpVlzBlSvi8EHXCoPrmfj2AIhAJ7ZpP0zeut87WChjcc30f/rbevu0mtZDjKrHkhpaDIo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDlCRA9TVsSAnZWagAAm20QAIZX6lW/fK+LB71yuTtv\n9dLPPBYd8mWTTXPuzmRVdMNVDZ2/b7VOzMRJUfqVrC5igbhEVAHEg6mlkf0O\n+WtZr5PMxNYO2BNhaDYP8QJHTfgUY19fBqsu7q5RZqpqlGwUyXkFmoRHPzl3\nBzIshA0m21oX6LM3gSad7iVjterNTdqFor5nm8m7RfvOiDVpm9lCnKbl/RQK\nq1CZg0aFkrDAAQzZDX0H1cAtlSXr6oftUuKiCZXrAzPd5iTc0e/3jx0JuUqJ\nSKbXj+g6B42CITYWAgl+f5rdNH8UNj1Yg5odtDy8OzBblAQYuVh8Nb9Y+Rf0\nxEmI6m47Bt3ck3MA46JA1xTfn+749hK8OKbbiVphbuQBR98EyyYxsArV0AeC\nGmhOs/VK/ZMobQsGUU2sJM1N+CV/tpTtD6ZSF15m6XdeffeM3vMGRNmSY6B3\nRPXPE0XA9HoI3ZyuAvXqBq/oJmEx98O9bfT+et8rVO46mC0eYx85XvZMD8Vr\nFW/FxuDDwZJhS5m8UrZ4DVkpVBFnZ6b/yNU7OnUzUc2PFylAl81p8D6hSlON\noFNs7zKLqmEB177zRPoyTjKphasfTailNr9NDtqmFanFqJFSAKy71gXiqKsW\nYWQ6DP9YTJYmFZqVxHphTIxzwltQuuKtMp9+e8kGrV9mGMXAu+iA67ObjXQX\nZDyJ\r\n=fwWt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-arrow", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.3-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6d6fa7168ec1091078e1da247e066d9a078882ca", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KmPRIqW/wFh7SOrvB4OC0hI3egHFkZm38djN1rz/9AERmPmQPvINsO1WedcjeDVvI8JiLVgndoEFBbSQmIh+Ag==", "signatures": [{"sig": "MEUCIH25aXN5j57GyAp3k2piR7IAXQdDouWnSImr2iv/pNpKAiEA/FqUkBV/yyl1bcsjiG8D1vmdLbGG75dl7cp+rp86vXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiVCRA9TVsSAnZWagAAPFQP/jJuc+o0iR/nxA6wz0eU\nxAScqDJHh8kHXLZGZh6gg5uhQqa+24AX/F5WtXml2r4oz9PB18tSJ5WVzK2B\nTc3SXqWAil87K02Ewx5/zm6Q0Sgyg7+vbSn8Zcp1J3ZiLGF5PfrUiPAqDmeL\nZi1f33kG2nSkdNge2YsZ18ARdqyxzwfMq3SJjlT2Xv9uCl/kfI+3rlkiMf72\ngCrjLJL+xo322kgeW/QsvBSkMTW0Ouqk0FEOnt/vsv0TpkYAIo4coJwlZrYz\nuL8/c02BPh3ZxNzr6k3vxYf2R1Lhbr1fLfLeWTGaPMHh3ejk38lc5ORJ9XkK\n7Vvjg4alAQZVCaHsUvYeblHrEWsbrgKKJFG5u1Fqcu6vexlWEyj7GX8eR+MC\nyjgYFMu/2CbZro6OWlKVUN2YSDUiJWUxd9+wYyydLMtgwGaS7fYNjUxquwaH\nwwVewdjD6ZQccmeXLQYUKJZzgk/QfU1AmX7zzHu9t7L9SZQRGrU1E1t2Egsh\nOcforMjDkd+/VVua2VoRS062Bd1V54U+t057MueKff/saf3oNsHmb4CoqpR8\nCbfTUwrjLpK6WnqXtQQTgEtfnxV3WIf19URh081eksg+OJXTx2CKkE1rB18M\nB0K3bACERUqg1m95wTh4U7cQFkEi3kCEcKVkD3WGLWLfDC5ITx52FG3+QdFL\nvQCw\r\n=937B\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-arrow", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "17f86eab216c48aff17b13b811569a9bbabaa44d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-9x1gRYdlUD5OUwY7L+M+4FY/YltDSsrNSj8QXGPbxZxL5ghWXB/4lhyIGccCwk/e8ggfmQYv9SRNmn3LavPo3A==", "signatures": [{"sig": "MEQCIBUCGMVQ0DMi1yGMl5HgHlAVdaoXTxNx0xhJ9cUE3MzCAiA68B2oN0pLbwpyIRdiBAammmsy9UIiy9Iuu+UnGECHWg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLiZCRA9TVsSAnZWagAAcnwP/2C9ApnhQxAW8vRvDlMD\nMKOU+IavYC8RdOIWhXMoqMBuEwNRCNUXrx97c038/tFcxpTN4n+ewHJi044f\nkZLzP7MWoCkJHHSO+TAIykokzLNl9NW7hJt7QOS6tLuMIfKejigJ2pxnTtMr\nAkyrOssy0zcotNF7fmOYZmMT0dofwY6nCpxT/ObEatxgLTpGcxaFYnm63Vgd\nvU097AzmtyKlbwrfIsSYqspVjxDWbToZOGjoAzeweav9VNBwjXnRksesae1z\nQWHGAPCscRlZl/yMP3oMpyuNGkSYonahEilwRCmaKJi3+c2nibjSGyFEojqs\naVR4Mr6PaSSbMcbzklD3gkYIkGKgX6XhW3NQlWdRoHfOCZc3yvUq7q69VgOw\nk8U/yu5eyW04KDW3bAKV0hboSZr8xLL4oua3W+c0bSK4jLuVGQBR1kUKH6Gm\nto1nv6SfL7i6i/g7FQxm7m+UZcLTqJlI3Plmf9ZNRpPiajD1ZVyDNLoPYGbj\n9i7nsLk6bNZ5ggLja8E7G3u7PTS2brJgZPJwAAIPVTCqxh/GT/Fb1ZAkMV/C\np+3XbOEZfhFB/ZrZTO575mGd+ulyZ+288qYSeb7xiWq/ukBsaxF9ua0kuK0k\n64kd37U/Vo5wjrS4KZWjCDhDTCCmJK6Eri5UKUZpncMFbi9FHsNYztp9GB31\nkqx9\r\n=wkup\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "60b0ecbcd3a5f9bb51fb49af643296bcde0962f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-+hQWy9BVEhr+IIg1aQLbB64lo/eR9XsuE0i4adkAA1WmrhkUttbsEU3BtFbgzWlFX/ghI1hAldlKZqEDwGRT5Q==", "signatures": [{"sig": "MEYCIQC7+unVA6qreSH7DsW1inMA3i9mCpKsuwjimTLnb/WKFwIhAMFFNWMU+CDNxy2Fs/8y7ivRlfolxlRosnPj7V+3gUHo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31p/CRA9TVsSAnZWagAAzncP/0KtwsisCJgZucnmrnJc\nuuFcGj+E2civ+5CBEchbrov2DuOMzkKHqtmbAcQ5Lgwf0lG6/ntCUDL94zMP\nyDckkeMmLaPPEsVoVroGxDUGJbN6Ed8YD9wBfBGNKM0Gsl7BIiWzpkdEhZJ+\nXWIu1/rDs1iSXlTPTqjgE4aiPueCWWlMgeCEOJWRS3cglnr1XvIZjgb0jIdY\nkiY5bjBzNQ5EXYHHwo6Uu9rU36WQQ0sRI6ui6mEA+Y7cO1QH7wgzJYG6+nAr\nVrJf6U5iNCnjXvRmEJq3lfKWRsXhWgyOTJd50c9tqRSYcik77pONg6LgHvfy\nuv+V7TvGBK+gzV534Ms2iYhQrQS537gNwOmi5EPkZQVSA5op8EAcwuvYwipU\nixq0B0BRJFWls2QlL+akOqaa9gEmUxgeddVg9rj9Kv8OV8U1xonsKcaUItNe\nCIgFtxsLP+ves6t6cyVWk9xq0kc3pO6bfmoEg67ryXY//kXSt3HQJOgcBb1R\n1llzSh0koZl4wrdtTXyQbfthlkeUKpusJgE4SOSeuTycFKK06Cqc16t54oXj\nYFgmWBI8Yci0hx9NIst71cJqvnOfmvWGzshOw2jaQ4+wgBpB+erWDMeLfl4e\nB1dTY+bRQ1izLoWz7+gEAIMZsnF1+VYw+6ArIGrS2gIUk2vs6yCGUZi6ct8H\n73XH\r\n=KR8v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ef171b3c272d56f0db8fe30f6e9c947b9baa35a0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-kV9ihfviaRSfLezzrH5aX9wr/Zp/XzTDZiY9SzNto5fq4RvMtL8IrdJ0TX8VF00ap50HY2Njl/swsTfrhRrzkA==", "signatures": [{"sig": "MEUCIQC3gC0J99FtoBMXMDJv82yG4lzaTtLm2Jr+O7lz6nQjegIgEa8B/C30cyvqMwWLjF1JHx9howtWxCaWbwBzBXoP7cU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BC2CRA9TVsSAnZWagAANYQP/1R9RYNMbbju4ycopqVU\nDz3TJKJQEs2w3amxMxgosVVLzL3l2/bexCkEl2xVlHc/I4FqJzyF7J3AW/aw\n74j5AF6wpcxsfWzvdBbE7vlkid40lauTQxes7niNtEix5LDE7KGROX/dYAyJ\n++G2bPgx3IDZFEUQJXClFyGTuQP8E3/MLchmPnWnXh9yOcfT82wBa5wN9is5\nPdDgP9mVHfFXbsfpJLyVBL28bo1EW3+h4X/BylilUBQUh47aSsGYcHsJJpIl\n/uHIx3ppLJsj5vZ/QF71MYdBKevNMMv3t38bMQucBt8WK3O+XgZpRdZJALn9\nhraCUXXnfKoIqD+5ANu5WFwXLtFDX9faXIzv4deHETgeK9yJ6XONrbMFQ1Bz\n6AzGveOCjHInMKXAUm6y48CLa1fEoHZqqAS/jWSRpuZyyO0bqIgI+kemTn1n\nTO8WAMlyVEhdWFLIRSDfU3WMQnvba8to+rHMEKNC5Q9/ACHKjv44yqwkzS+d\n74IMHIwUrvBxu/5mRSdNUh1633v+b0P3tljYA7qPSUmGaWZe1FZoxiyPQL9M\n2AvvhTYnpsilyrG7m0s0Q9r4t/AUKgGqyCQq3w6y3ENsUzkpKfARHv8PZLG3\nca3kh31AyXmB1TfCtdEpZ7raL/rZTQ78Ta8H9ATSJPOXDKIgZto4CAdd2QrX\nA3US\r\n=53KZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "672c3f3b5b5c7758e95cd4af171284f0e48be188", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Qqng6e+EjMRxATTt3KChjl610owJqckB3qJAEdrOMZ2PIPeRigUr881zC+h5rUEvedHVG+FG98CZG2ADPJrcsQ==", "signatures": [{"sig": "MEQCIEzrFBDSJKTwhqoEE3MRwMFCM1CU5Cq/szGpdyHWxa7yAiByyqyGnep/jrJhDrOeYyqhbocHC9XW3GsklDUp9xbuLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4CldCRA9TVsSAnZWagAAYwwQAIRUumGgfqfF9lpUpS3I\nKUsKScxtrYC0vsodFtanvlHZ7w3YcbE2AQzlqW6W3vqiYKhM2Anaxr1k55hU\nSI0fYLqe6FAAM80qjItyeXR3W1IW5NLGuoU82QxpsRZiZohqY3kNXSsm+q0h\ns+ul/Wj2jnlFVtgrrTzjy1rB3iO/AiHoJeVTOpYs+isIGWITHhC9ezQ1+PUD\nVtTIRSWZcgmLSI6GDnysj6OZrSMJZ029QWbqVGvTsRXUMN1izmJD7ezylk1q\nfZIf0sS94vDXoa0J88gR9qsj682kMxUxnC0VO8lUzbnHqVi0jXLapm+a4fvx\nvudeo3XvZIl6Q0JH1MDwzffF0RpRUNnHDHBGZQbcgkwk5q/De0GS0E5d61b7\nWTC0kHEeUEeW9dIh+fsH+gDNuQQo1EIHQxHC8zJa06kusnBuXjug4QFVuqeF\nINMzEV1zEdroPrJ3/zW2QQ+ztlTYa5fvqAaW7qavcdBTPzgSEPUtK5jLCaUU\nwLcItZujPVQ/gvv0kwOvCXNg73apHPWF/CNUtMKVhWijBCDDtGiXe+x64CcG\nbLx0I6PHlyyuOP/lVuMM6G6CtI7S7FjjyovVRVtKl/Jahg3PkY30Q/I4nrJ4\n98PC+3v0fSmCzLmcdNELQv7ykJhl5U5wXP5bnX8y/h0to8heilgD86lm0N6b\nTmS2\r\n=ToRM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c1d8e246165f20f3cb6204b8f96a344d738dbeb3", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-7J9PQvB3Xae8gVK41URVQE1U9ydDg9gq5VmH30AUqrbhGjl7lki7pLkeWiDUb3NqORy6K7PYA4ncDwV7robAxA==", "signatures": [{"sig": "MEUCIH8n7cS3zrWU+Ou40vrvNxkJsuuZHu5pYw/AW2EU/acLAiEAhm00FWuLGX4umWZb9SOwOyotQI+4p6n6kW/mYC33jrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GpHCRA9TVsSAnZWagAAuCkP/3ZY8ayiVsmBZf/113HI\nAK6VhOwbJiOQ9PIf0NE6UG0QAzP8Aqqb5YYOHWDHjhCr02+7XCSWLTnnfzUy\ndiLmiiraHQsYDGUKfUCcJHTkg9r+8gCvQ0gwLsXN9bbLvtxaST0uLAhoxYl1\nqIc8FtdMfuQ23DbdSPy2PpQ1bK1vsll1FZf3KyggwmTXYJdFf3KRJs57hXgk\nrORaPO1+aK82QWIUK6uKFStxPea+fpMtfpslETXFd3f8RPKKFzREXQGXWP7s\naGYgFmIcvbnBPr4aFdyfnKqIbarMZq/zV2DFBXlSTa/M15LBHUQ22t0+L8C6\nZD3Ez1ETHZ19dkW2ZEmvs4ZCd5FgEirbvZCluSuDXqAlgBOFrFbD1YmbzcVr\nZpPcpQVB63Hd5W1qXsCV5j1YfnLG+4hJfSsgmOu2iykV1hY7KZdVB1KR+Kut\n7NWOMd9mEmcbDfHS71M5MLyVTyBV/hV9DdC7XY5hJiAkOaMaLUIdhjudpoaE\n7+IzIjd3hahZws3ONseHhhnvjMMQCRfAeHJ3JziztC3dWGIAHF8dSaYhwzRO\nmaA+zTU9LxL5QkVm/iSSZSVOQSwVIybdsPk+RtPzLVFAeyHpNttQ+h38Jv7g\nlvVrxK1ChJINqFMKlE/pQstLIVpIpUBu9lyhVWdtW3ZEhKrBbm4YxoaLEEuK\nokkP\r\n=yyqY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bcc125c576f3f89fd575e16af9b04c5340ee253a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-5q/PwOnQC0/V6s/zJVkxcsUSAEZ2e9MXKRQwcWXeNbCT3yu0IbWrzNkxluYVF0EQtX90/A2fErVeKianglWukA==", "signatures": [{"sig": "MEUCIQDhlinCqU7GlZpRrOeb319IIFpfyxyFzEyvNLGNay7evwIgH3ObwLZ1PRVVVSwg9tWxe6V/ILHeCQ8epYVKyHE/MCU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZbNCRA9TVsSAnZWagAA3B8P/0YTqQe21P1RzBC6cmon\nM6FZZkGSaSf0tzepGhxafwqNOycTGs3gWeeOSJu/rLTNUUL5E2sAA33mjrYm\n8qrA/HJZ7HG1DE9ksVfqiBb61T8s+cJXgw6XWHISQ7F6EUJeIA4bI9MQSCSm\nuOy4v9gmvO/r7Ilh028oiYluoh6KVIPoEZEo3si/lgponXypbWEPiBvwrBiH\nGSK6q2IeRckCQNpF57LwVBiaMrZkjyk1D3MayoK01pdhgvYaRsqkEL4ZAg+i\nWq6TDynEmoFPYYxBXCl3CnPFN2FI/u5ARwhHV8MdwfOJnUPgvbxXq1N277VN\niLY68epTbOQ42AeP2vqgsRHW1zKsGrAOiT2OdLtXCmBzVI82UPaOuCN7ELyT\nLu0m6b/gwihQ2S6swq22slbKN7CgNIU/Hs/A0Hb+wF/sosIMJSn6Rbe2SKX5\naYAH5vS9qu9u2/SSMKU29pKUCOk7TRmmzhBlsUgUjHP8hveGliTE1l/a+JeO\nmdMfsANuIQk1kY1Dma+0fciBreGiYUXwUfyevLafwxYi+t20hbEskmhoGG+5\nIOGn6NZ6CXJ0+x+jDAHw8yjAZejcwiCK90g/NykFvn5AdB+rW5/zwn5AY/pk\n7NsEGGz+Scg8em5yHGmjJ71b7gjJWiu+OKQojJznyEXudo10JSHFRyQUhADO\nnvuh\r\n=7uH0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "97b76b85a36871a237f4b3c4fa17dea5668a643c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-xVCL3tPNrFu5rwtKj1QUzjkZcgIW1CXUhfDgHP7tQGpYuKRGVdO65KX5mDUIIiSDS5xoaBbh0ALsR9XmAX4Kmg==", "signatures": [{"sig": "MEUCIQDN1KkxJpq375s6IdQPETRKKgonVnREftfcUvHwupkq3gIgC06nwt3bAwJPgiHgnQmjicMzUF195eVF4c0qiK3bvrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YrlCRA9TVsSAnZWagAAgHMP/i10F+vJ4c/MMYdRKBk9\nqkDYO4POMQJNQiiZtBjOghWdjO6nESXfSNbwii2LABceCxkAwWjdNbnyzqyc\n+gJV52eSl7EZiDlFlWTsrjCw/HXhC2DoW1C7P640darfvbi/rGdu3gt0YODc\n4v6Bl0wU/AmUWLgifXI/DHtVdGqmG1uUY+8dxzzZ80DRgmClKlpo8bXnWFBa\nawhQbc5Tj5mAIZSiDNO7rxq/rLAbpymj2ZFlUNUc4aMFGJPZIedWM8Tr2EQc\nOnF2D7GBU1P29MJgnAuLIvrhQhAEsrrv18vTkGH0OLyq0L8rC1tXG+VtyngO\nBRTAr9CBWyfGfPl9ljTrtWhKe7vFK4WlJ/UaCGJ5zNem1K4Aqr2aRJ2iDlKS\nCNcuw3DngWVYo+SoFtqscH3+ij7SrGN3u4Xn21YXVdpCeyhPRbi8RH3zn6TC\njPt1uZVCKEvnnsLHtp7TQEPxgISruHm6BUH68jocnGBbc7V5qVwEeIqBKJFT\nR2NCn75CyGYcyDh2H3bAiIC+rabn8ucibZDp0MjHJXQEBeIy/0xnzPDQY2c9\nZFpp4t9Lekr4b+J5wW3JLiJ3NK8ED9oLfTFQd3et11H9CD0TjoSaAEQApqum\njPLKjmPzihaxD9/5YVXpmvxBEZUTAyJWFy+1ZQ0ErxTt5X6kDS8K0dEefBHr\nU84/\r\n=Qpp0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "81cc8ddc10f82ed08cacc624f492999bc936eb2e", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-SZ1cClfV8B4DGBTgVqPj2f6fc6ra3FNrbt15og/mXI0xLv6/KQq2lskWQkkGiQqnp+D5f1hNGSJYxykatjuciQ==", "signatures": [{"sig": "MEQCICsUDCnqCxtnMWxLFpKX/2XI96L+LtrW+1VDyfX77MviAiAFE+3FCNf6IS6xOjoqvrSrC+OmAKurRNUimzJ4X9k/ZQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6scKCRA9TVsSAnZWagAAddUP/jjICHRRwVprvVBUQ6ba\n/OF1BwqA6bjKgxVsPqenQLtG/3KG09PXt1pf+agcHaMtalTZhAB0agFb7JdZ\nZxd7jhZCCAXgCHUwXsgD8vQYNsB4udzc1RfToUk9lce+V/gRL3m2E9P7QKcd\nazRyqA5aN+CQKcKZXcC1sgcay0a0rYf5wWiCok3a0mo07YURd8uZD/PaoGZa\n/0mRPWchR9EmRGaJE7dbNq6yFGEYuWXPZYvgM69G6APafbddrd1b5WFqpxt2\n7D2ej3nfn7YX+dWEhVDL8PfoKFqAlmiFIxX4zLGPWSy5Gtq66j2HN2WYNYId\nFQua2TJyRrGMZV6WJ12woRR1Z5+lNP49ohMLpdYxPKtlpRMdSbaJfbkuXo4O\nR3K6AX9mt1q+JRlj9qsXX0M0/eqATH5u0Ovz7JMlt0SrtxHA4QaJLIy6kqEC\nZ42aU0u0VVbCWBrxQTDQ8D2JVD61/N6lWIOelda07KgZlKZJV1DhKd+HlFgg\nmt1ciHS4NJ7VTG6GrWfFrEKPWnwsscpQMFNE72atYxRd5TtDDAIvYM++Pl4t\nbqB8KduBDabV1p7bIE3k4R1A4E4PLXXQIExwgNzqc5oAhD0BEem4QpqThByD\npQKT9YowR56HyY3xz06fnj+jJbThVzzktjpt3lBFVkPDOtMcwnxnLG+r1tU3\nJyQf\r\n=GW++\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fde7fde6e971c0b2a952fa9975b20f07bd2c4c2d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-+5ruf8oxL92TnNcMmB08GYtVYClEy7FpkJGjZc4Q3bHcxlioG+UhmwmPAX7ZVY27xyjL7EcVUA7bR/V79ENSaw==", "signatures": [{"sig": "MEYCIQCIFDgwbwv2xmBNZt5S5FSMxHBeYdqRHWGTH58zcPctiwIhAJShEt0GLaO6K3SkZpg9VIGvACNyO3TMs9L1mEseJ87S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xCOCRA9TVsSAnZWagAA/QQQAImpr2TYFb4aLYD3s1gd\nTuO1FCRS0ZuVmOY3GKa6QWdaIc7SVuP/PhkDdXpI9Zkarb1JuYJShUKFvx/v\nGeCnvy3vATwwNts6/sgTFf1FVwGgpnW5RijfM//4SST+C6ctnLUvZqxPKTL3\n0XBa/tYP86ohXl1La58pel/d/vXQ0bWiudcV4OkQZKABnq+Tv2CROBQUxVVS\nvAcB35WqK5gvnsT+/SWxwt8d4LQntJ7tX+MtHxHqiHYuoWn8ecMRfRTyhBtO\nyYWE+OPi7eYgVCvadOyMwEKZuxVrC+5IDkpODzjW6mcRCuw7MIdaBLuWXGnr\nyheygTCX1/ZRHcbAhMExWoAedf4YcgUfNlS+OEUfvwJj92JxpilMobouaEYp\npMWYQBPfh8GrsbMz1/p0nM0JHaocOe1u46WjhZARhaafdUpYAEHSpaVcuXmJ\n1DBMbaaOoIW1utlH52TOsY/aAQhFbxI2jJ0QdqbQ3slQOgvM/So96KY1N2RZ\n+zHKhpqNfkufDblv+hzmhmU4Ghgho8LHtjfsy5Kjlo5tHBF6pDXd+Mb/FHuN\n2rMoginpPikC3GnCEaBGbJ+mcwIM0vV2T7SclYbpyzDWdNLVcQrh4l09da1E\nj6tFFU68QrKJvF/aW0NnG4+wMKhbFVI4RESF4z38PDbKFPVs61dbj2HjMF0w\nmRJx\r\n=f1yM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "45cbf81a6d6b707dac8e86cc9e4f03f5d21bdca4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-OVXdT9m3gAgjteRrrCajT1GUymFkfcR2Uk7OwYnraNmQEMqEcI3mu+yjjKTAgM2Y7NiU9jxBOPxHU9esJeFZbQ==", "signatures": [{"sig": "MEQCIHonUMfIxEGmP4giPEU8Y8CbTw7O19eVCGUTYsihP5k8AiA41WAZWCxXIgPLWM8LkryoBjDGrpaMtWovU20svvpPQg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8474, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xJuCRA9TVsSAnZWagAAzcMP/0wC5I3DbcLPddX1TGgD\n6q3nPcRfR2n6hGWN7uuT3n1dU/ywNFLpznNTY6dUm8xS6OGdthSKEgk54ok9\nycdFYUdO7GPXyMhxAj5pBFVgQe01S9MR1eMJ+520di2nYp6EhHqR4TzD5KVt\nNW6lexy3myWlMmPGVAg7Qf6RKRCJRhjspOW2x1JuBgFqxL9pBFUYi+Besx7/\nbzLB6HZ0D7ACgMYPJbDn+q35fRFGG2YJRVOuCSJUQwZAzO/JxxC8b7FYZ7M1\nkj/JP0u72wzDDhiJekdF1mdWu14i+d4whBuZnCNB9vKZ1K1ck896xGhrKyxN\nbsCPsbfi2NPedfQxkAlUrA/EMKh5UttFkujNKbaa/K5mTMJvn0YqDVir7FSV\n4Qq+U43wMNfE4h8mDH7d1IUJW/l/aMmhqgTGBuTjrKn7O4wJUojuGRbPAGBD\n2GByR7498o/X0BkxMhOgrkCpcjzGgXV/D3RWF8c0OuMvuOVpoQEalrQOeDuH\n9YDwwVrBAVDJPwymf/5R3wRuU+73dlky7vwHp0PwUeO3Dq4Gs2cCYxrN0iC2\n3CRQcgEFX12zQ3KvIpQjEQpAwraBvUvC/FvKhmtlyaKZfvU77ShIec/YCw9w\njUUpQZ4T7bBT41IX6Dl5TieazkGMzWuR8Uu/W4s6w36MEwwl2INihwWDADGl\nRqVz\r\n=IBXk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3624cf5f4a42402fe6fba37bbd134a411beeb39f", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-DlvzWMHwdNXyK/Ia04WzdV+dj7fHBL2xidR7EZADIFNVdLmz0NfYimZLs5zDvalo6duCCddj4tIxQTpfxsDq2w==", "signatures": [{"sig": "MEQCIAY3p//nQtOs/5kUOGpsNYypM6Ygu5qDhHajLCfRjQAzAiAXEQnztVc83TO0JipWSuTQzpFXnd817IOrz65/1pFjbA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8DysCRA9TVsSAnZWagAAJCgP/2ira+uJ0+pPIEdenbVM\n2Oy8FicJRVsy9adfvzNxjCnT51Muu0fIH7Qwf6z/H8NJus5lcQW7+jOfn/Kw\nz8nrEQfLQsr2zFos8MHIve3XYSMtU/7aHr6EBU7CUNzA9Us2JWQzUYYQqMKu\nd0Cbi+/9786pdS0BhrL15FWz1ZVdrF9lUGsSZxfUxFx4F2F1/r59Jv7WSBxI\nqWOn9w7vGOPQtnWVgL4MurLFT7JdSv5IhAWS5aUcA80DV9maa8bzoRABwY/l\nuko2VzqtM1AAjuepkYsaIyJ0cArO/YexJCkcPYTOewtMcio4tzPAXaDtzJ03\nISg/+K5cJKEqQsCOe488qCMK0V0LR+Ru2gxQqcHepS3ZNd5Zxlkz6IbdCpD2\nN54LUpMWn2jERHy73ihcB21lsqA8jIFUndyAjHGY+nJRnFccBvrbfHuqe6k/\nLBPMFAHqKMNh9JSKaJfonSK1X2asjwICKtPZPIMBGTCSDUmjYZEL33vgJTm1\nGUbVZAL6XjE5imiXc9NH6MBE6h58+gocvL2HV5VgiRbpq6sBxbwS5t580utv\nnXMo57KLbbRsPyQGWVYoRr9f5RsGhNuwemr6oHqX1WV1/MjjEPcPD46lyhfO\nxxqO9/SlEiUNyinVSgYNzz6jaKw4jhfQqMWQoWQe6fiYrFjGM5rJkuHWNsgq\nlfWF\r\n=8Daq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bee3df0f703e2cdaa710f89f4165e675c7776d78", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-RiNWMuw7Jd5VFegp3KEGYOyLo3qfB4l6HQr1oTgoXOFsla+c739goPWDhAFAd3yEesVQmaXP7w8KX4AMtbymDg==", "signatures": [{"sig": "MEQCIHR91G/La1zTdrZm61TXlBIPSYbBwHeZkfs7DrciOsCvAiA+LtzSsfWFFAazAEgTdAtHqZkldUKs6hD0ivkYSAK7pQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SRxCRA9TVsSAnZWagAA+UkP/jsJlIGKvfTH3CLvoEO3\nIKJa/wcYPeZiqL8JkMWbIz0ZXov2ORAU6XFqWxgltraknRPpXUKFXOXLjcWo\nmmZXJ2jtgY/ycCKHOAU3YVP+MWcTl23eCIJVeUyTu00nc1NpAvszkC70uNj3\nDkDXRhn0vutHfISt7qBmzdrH6WBZxUjFs7pu7c1QmHNpwvyXHznkbrlJHexv\nrsy6/V/hFswBvzKi2yirs0JJEFOwUkm72bSWKHSfKmqlJENEkofvigWMdzi+\nAhT2YDtVBO8h1fVa1mLwkwYXm9F1fZJoM1wYuHvFUsenhReWfFjHWCy4cIse\n7F1t4y1HjHr9TZUYfsp4UVi0q3vjCB2QrFXGGXfF+7kdUwrOV05lW0aMs/4U\nlPb1AI11EAcD7c29+N18xh6piL/ZAVt54Hv/MwavTvszGH3zq+2E9EWMb1UA\nwhh+3aCsGW4XF/CsKXeTzwOx8HI4bifMtkG0VrN9GVKiJgQI3Z9dw4To0jPh\nCpD//904QXWCuMqtlmDWZAgXj443qv0hy4FFKB5jOnvB7PtCBtF8FfZBKiFm\nHAAwbCIpGYIGFol/JsqsQyPnP77+6pLKv8pYukCKvimnJ8HkNi1fX3anldzj\nMKutexNkIYmK371qc9WTPvbPZL5QkyzyE+fj/aQihbQ44aAKg4nmjCBBLF5U\nuspj\r\n=9quL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "46a4441ecc51e74b272e5eb93ad03abd91fb9d72", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-rjpoZA33sLCfgReVU2SJwOUUzxTDn44L6I13FoZAUzCtdtHTE+jcWK1jG/YoNllZtlWVVyDIy0b4iovOYrvBPg==", "signatures": [{"sig": "MEYCIQDLqEOrj9PMXGEATWs16km5Hzgx1EJaN2Wl6awMc0AIYAIhAIfEXuiZswcywQ7xiGbP0AnwWTaTsD8RshvbfCTh5QuD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DYuCRA9TVsSAnZWagAASCoQAKODfRzLuB5uTyYWARD5\nZn4vFdH+2/oOCMCLcrhkNerz5fY8GK9LCLOucu6g7xDYI2j49xznGYNqO0Ju\noaB+LGShf6/7DXKE9RXQVFEBQBTi7TEmUUcTS/ZFW36j93NhF2aaevqRLvHT\nTtmtranm197Zr05sv5kNTm36tBagpTj2HMj0L3NBUIZTCNAlknbnGCf+aE13\nD4spQ2PbLdAbKn7oN0K4d+O5KQVTLqSKVlUJHMNG4oZLZY/ifjQ0yo2epmM0\nRZQh7iEWQXiySeziSsgaeMUd39MXdGjnF9RSAq0r5ahPoQWd2NzYEDYCZ9/C\nHRc7/IomVBZ2C3GdON5g7rhjdhMFfyjAQ9/JwksseG94x8ItJeHomSHLrJEf\nOL/hneWlv2ae4OR+cM1bKJxESfNDe0bawlngDcY2z02lF+9DU77w3K08bjTD\n9Mt7bFa4KhOwd7cCprFCnijZ7yWBM3us+lyp9sXGXMxe6zU+vS52ifpLB+C1\nI4u3hMgyGf5nRMf+QdoXPKG/YS2GQ1fc+gfiv+rHth1Lc+1Ho8EXMySn+WWC\nqYAJR/AtwUvYCnBg3tPUttg3MMKqAX23/qMlWVA3KAfCIMpnI52bqOt13CNm\nBGBd0BvmWGXLdOtHGBNv2/4KRQgfogLugozp/WNfOHsMnr/f1XDeLt+Q1zy7\nivZq\r\n=JZtT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "41661814e45d9bfe31da1201b01b852347126680", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-pyJG/cY3044ZLxweel7GUKhSPMfvYOS24mvjbdHhgi0zK9nrzcNRA0r5nhBWGNlbsiI/sawxLCv1r4htk5OUXw==", "signatures": [{"sig": "MEUCIQD5eMLCpLB+mjaG5dFo2qIXJTmrgRWhXuu3lf26GZia4gIgE13w+ihuUFyx+MmAlXTQDX052QtaGORIMsg3ZcY0s/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WmtCRA9TVsSAnZWagAAhk4P/2rMVk3cX7d3dt38ibaY\n4wSeSiAWvux9sf+WgwVWrAD/vtFjbAO4zewxTY65FyTlleDl2ZsasI3yJOYe\nqbSMoPSZ/7ins4O/WyNJaQJnSZTTP74tMHMdWbxLTQ5gsG/l2XAd2Ng945xO\nFUHfbNkghZmeaG9jOd34k1gIBAKwG/tC4byhdsvrsJROPAkhwQ8TMQxjcBoj\nYfgXq71y7YAB8s5TGOt/YUyUcHQpL5vyIwPrKWmu/356iCljVjUT5DmrB6KR\nT6cctKc6x7cJuqnjZI6B2ezmZgZ6Ki4ch1ZVhqLpgZib6YYGQw6dAjstwKrW\n1LDR5YXt6XyEPNetkv6OHA6wnHMamZed73JIq7D3i1N6sJxstGOgXjptY1UP\nqYu8MmRxmLOcQtFD8lHvDXYGD9DYO1xUw9gcmgboieLZiPjBBBoOcgKX4uli\nKzv2sTE0akgMxpw6Mf0qGZddQNl9cDlLu+kRPivFyRl3qCZCoRE48NsCMptT\nhjFAzrClrafhc5Y6DXA9bgokiDHsSbM57kXrIzhZR3ccciYWd3Q8YudEd1j+\nrH2bZU/TnW/enUyBVY3syrfY3IezX7TuG20ztX+5LtiftDLzUAZJZh1CSxBS\nDDP4fQyThRjoe3bkA/YrP7bromhc6yDHu4XhGqKC0m3yTHbCPfKWQx9ZHKg1\nw8zi\r\n=+hNa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "01fcabd531a87470c66127389b094bd0cdb3e002", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-nuEQQ+YzFiJAfiGXSs035KvKe5o7fqDhD7+TShE1L72+ksk3VCSpj20TSQFwFW9hvKQF7EbYbFJj58XeSGaEeA==", "signatures": [{"sig": "MEYCIQDKVxw/pUMC/60lY2q7hgPJahoT9sd/i0JMEfr6Zq8LjAIhAJxEDk8EPHjcvXS8WjgK0ngatgRX65GEhyf48sRSba/0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rTJCRA9TVsSAnZWagAA1AwP/j5qeUkyx2nu3yPuibuu\nqbe7sa9G//U1WRFdrgtuAMZTPZlhWp9EnZexa4ZKWzuZQak7a0MZoaOD8YRV\nyCUn1p2NKg2dTazBI0jQ1wUfkSFA2LW/89WQTH0E4bItIcD52UzuWF5sXhdT\neJ1kjDjgbTYTXlE5dryxdkO/BMlqpIljVjPqIffB7DVggxNiUcKklRTUsOsJ\nF/Eje044F1m85xps9KdUwxOwTSHL/Po/cZ33LJpjIl8FTOlEz3SgxD4cI5Sz\nR/VTrAtPrElU2yDXn/cdD+iR8YLvnW8HQy2qUGDf60ReeA6nE6dt+rJb9W93\nLavuQqInGbMZHeLTd3NynF9073drKxlmugTEHf6edfiKm8eQx0QH2eb8fmpx\n/upfkc0xUGDp/yg+zIn17bUZs5+/HZMt8al7K1yu9d/4lu1g86a53EOf0ipN\n7GNZM3ty+ZPbAFTF+nO0qtxlxQCLAp93OOGrk37jBuwbvDLzEtHc9oC3b19o\nmCwY9pVOqQo7JfryE/5L7BWcWKdequVMB7O7xhURmZt2WVKWj4ff9wV08mbw\njSZpF6j9nSTsT+CDlJsTAuRFXF0Apl2Ygq24xCnCUEycoolXbRIEaGhbfxPZ\naF3Npmb7Sg5T2FXfyPFu74pBXkNuEVLyPrTmzAOo16jzE+cfCvBftXM+xXT6\ncuyA\r\n=nZyw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9f9174f2716023f3db8bca2c144724e8c9416335", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-zPdL/SISHLtVhlIfXVhJV8g6fM1SBiq5/BophFDrOA5EJVCzonCFa2Q2T2FdjMwgNQOVtVroNN8ssRkGrNCKfw==", "signatures": [{"sig": "MEYCIQDWFINByav552tWWx3SCnLeXpNYfw5d6665CYfpPE6AdQIhAICUtZ+*************************************", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/mSCRA9TVsSAnZWagAArt8P/j/nGiQCRXE8q2CeFXBQ\nShl52m1BRt57oo1zNwvBcWgsWP6RfdCzND+6Qvx5ddnB+SahaniE1HWHs/CL\n27BNkuO+t/WurNTsDPn4JmNx6BShGSFCiWpnkHh1kO3SpmQKsOt1faAPrAWX\nFWXb+Hp/FYtniO4Cn5z0eJBdNTVGzyvnFQNxzBUQZWNsDBgoEGtsnBrfqyFY\nCjlKwvHbaEQntKUKywaLpNtcS5jOvsPZWpq1WYvWOW9xWup3S+hwCsyvkrL2\nDxDEyI1CmSOOJick4F4M4VTPmueoVWM6DIHK5utDLUzCtsHoiq1mIUcFvisQ\nxzZFrMkoEhIXlRmcZ0DV6CdX1CEhqGuEKqoXbxxl+fBK2a7KDAdVcjWV9xP4\nh0zlQYSZVfLgvv2o8901QyihrgEKDItvHRoJOzbti1+4Dflc6dX7g07zf8ub\n1wXkg8DCNnKVGISpmLnDxi1oRtz7bnNhk5VknqqWfe/3CzwuRXtZXLD2D/8/\nCbxPL2C+1buL1t9rt+FvY2cDADbZMhMyOQcnYytNUF865b+gIpa5UmmyO1iO\nWrPKc+jeHARiwWSZu7Sit725LjoOetQKjQRZ25yhyEE3DD5dKpLY4RiNuzJh\nFCrN82LFeDW8rw8+LlWRLS5AANpWrmItKaNZ+Rf0oBI8aZfZfaE1JsV/rFma\nPfNk\r\n=uSHg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2798ba1b1cf392153b52efe2a4583a6536fdab18", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-e//H1njgsWyaHk0hJk4iAC9mVyDLyhNVq8XK+3MTMGrlHWLsUksfOELFcVJ2tbmJN84QgZgIdsjk8ySxLWsCCA==", "signatures": [{"sig": "MEYCIQDHq/8P4LHj0PgzMjBmaWTcUUSpILmPoMKzL6IrmR+0lwIhAL67kbMvV5hMx92HYb0XLi0F/c+Q0nBvQfg7eVudVu2J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBGrCRA9TVsSAnZWagAA4XEP/3/s+I04Fj5rDj0IW9H5\nWZqdgLR3Jsd/CbggsO2PHGkk6Cd1hMVnEA5MF+9KyNJxrK0C6HsYs0qh0BbA\n7pQcSf2g6z0B0uEL7dCYUHV5MvPF+6c5DewsmEk0RzXktiydIJE0VRaSiqRE\nPMABVfqNjCrjbGfqC3B65UNr88mjrQFjVaALJmn3vHpX9cPI6/pJrjfsc2hJ\nhLSy3r1hKhe76L12VJbwZwcZs+6+kZv0wzLe7qGWR7fyEg08C75MHJ8dYYWD\na09casIvHqb7wZ0uPwaZFzlPBwqY/tdfLwpw7AQu+A7mk2/GTE9UQAMR7pXx\nVcbjUpAiDJFTdfE2adb/K1MI9j5gycHufDYh9ynuzyQLdMQ25x67whht/xM5\nFHynh27qt4Ec91YSNo81cmDOKs7SZmWE36KSWL+7mfJx7FrZ1PprPUcQ5A4o\nCF9rXS8dwoMgtrkSeszBqJyklZ5i3gAGVY3cxEVY5deQr7blIkjhYej17BDj\ng68HDzN7GAciym6h+Sc+SokKlepWwFI4vqxS2uTXvrQiWdXGiW9i1vK6xEif\nLoAQ3LJXfuCCNswKudwNh9ze5ITruBAONoQA30NCxv/8YyJHL0DZ3vcKJH6f\nRuseoeRrINpcanZwLRGxdScaNhsZXzrquVE6li0UUULWnqgYwWG5S+/LFGj2\nOz60\r\n=9BWA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2db97d75af7aca4fe7da36b87f67af362d0c9d5b", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-FOJ4TCvJ+cNrP/9EQfJ45T6xmRLdD90POI6KsqFdknNSkVw3taOIWSEjscImdyDJb5LPD5VmY2iIJxS1FzEn2w==", "signatures": [{"sig": "MEQCIHWIEsuTwEut7Fw2oTdpIeh0dSlcSjQRXvnAxDrz2pTyAiABu3EWHhgzSaKiCO0wupyfYvIZ3tTTUpygbgSmfGx3xw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBWyCRA9TVsSAnZWagAAPbgP/2J08to27ysgKZO2UqTq\nBPlh05e6aCrnq/g61w7li7ptCYoT3glvz/BTBqHbnZdXBwGwmnhGMTSfwxR7\nj44BQmpa0Zr8dTmknlunTV41pn41Plklr21LN1rdPsbv/MUIjJDQ3mxylufZ\n/fLlZ/yhEWTesmQAqA3DyfV5tUjeCDx5VJY3k4M8Fe4biygC700YFJKufSji\nlCff6UNYR89E5rSBMBOFJhpxGWapgFIhUEdq9Dtr6jQC1BU0rqdLvoX3zk3B\npczcWt7RkhaY19ZuffyUOowj4Q7SKHQqKncSXxR4aM+caFDhcgJtyD6nDlfp\nt1FZRuU1LamTqtXg5ihVnoz4yPum85kQr1MFNsM5vsv/pRJqiqxL6UHQt10+\nt1+ewjPAe6HijfbxSFRzX00z5/dVjfsj/kkEw6WzOhNVSoN5iP8GgfAfHi6Y\nLHGPlyqAPc/AUeo1S75HLTX8AP8AYCkjeqGb59oo6C6M54B4iOYz477+YSoo\n2itGlwtZePZUQsFfVMkrHxPC/7XWhGiFRDUdhmKy+0ESwRO+/k9gTIYrH7R7\n7+GbjbI+FJPaCHzBGvny3V9Qyp6ac3W9SXcGAPDSmSiLIDW7gKTBwXANFKm4\n9duNt80PHq3cWhmtELYBytnAhDgNsyJXKuIYMSXyz1xmzYvGSzjBJORqJf6E\ncttc\r\n=PLcN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "74b73383918d0fcea9080440882658bb9e19eeb1", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-RqfVIljR9yPfAJTmlGxZqniog7RWAcUgJcUJLfZxsCjf4odcOqpvb8OvVIeAKAn0wWXzYXUl5jDS2Lfb+5h2Nw==", "signatures": [{"sig": "MEUCIQCCSVW226OExa9PnoeekPC5OGNEOcr+eVnn4f2BBmsSlAIgWK3+k6Ig9tcgRaQSWLvIgrTxsDR2V38bHOcIueI1y5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlkpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmreqA//S0/ciaARfIoYnPv9T5NLq5Q7/IcFNwyLFCoV/cxJ3/YXlB8j\r\n6r2YWnruNAysnhBe1zC5d89BfV/E6UhK9yaVO7dJB+J5cGE7H/XqGwfX4LSL\r\nL/nwQMy4WoMGKoEYNNuymFJMFRz7oZpczeVKZDgkr5h2SgruwlbrEdnTFN8V\r\n8vY24S9A7v2MICg+AAX5/ICLGGPkk3mPeb/FZLMEe/lSjgzXLS6EEJTL4LM7\r\nIj8cmZsttBiIutsvBQ45EEZgpxg7eL903yv5o2EgnjnDMMNZ9fX0J1JJ0otV\r\n6ZLVfHzB+MSUrVn/vM7BBVp5PagA5P/a7idvt5vB8PFxa1l+53DARnBTiQJs\r\na1HiGMWg0iEPqQuAcBkMkYuh1I3SqW8x22wK/MMeOSrnTZNZkWJ3A7x6kRFa\r\nlrycH5OVr9dvwhU/E7bRraOZvIs/aYeN/FXJbjbisNyEpqnlCMeujcai9Lj0\r\naKs7kMUqVEIrmui2xJCfxf3NbzD1PBSw1GxepbMV5kDUZSxtLI8vssGKJXiP\r\nE4aI8QA5nVkjS2wb74co6xQ0BS1/XvaC50Lu/dHDsG9GLY1w2HjEKQta24vb\r\n5qzBTzoLuCx8IiH85S0mZZUmtFh2cgcP7/Z2hg2FbDZ9dbAp65XDSDYF9ZNr\r\n38sFU689IaaEFJsT5FhLuByYc+6P8SqIcHg=\r\n=zbin\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9944578905e62ba43421088ac9c8f0ef87db32f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-eDxikY0plXN5i/h8OOnBl/IIKn6zK/N9oUJL92Mlo2VujR3tlEcK6CAgDxMnkZeRax9nsyi+eKkIWlVtSeTo1w==", "signatures": [{"sig": "MEYCIQD2xPlQXku2G61mhV7Kwh0iOezVn6OEVc9fcKsuloN+SAIhAINmxQ4vhLX3THJ7W25wsuS5NZgzLsWZPxUl/j+Nl8b1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkUKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrUTw//ZC0+SJyfImBZBvFUqLNI9wP17+EZxld+m2/EhZ4YdoNafs7F\r\n99Lo1aLSh7wu0o4vuOlsAI+TVknCvB6Z2bRqxOUflRlhlNkpUi2ZvQ6XtNUn\r\nD5JeL4gYt7qmUa42rsw/SAcE8621GMBnTE2VVWnGRP4tJ72DhVwT0Ir9CX46\r\neR6lW+ssLO7u0xC6DQBzBwK8jd1tRCwyxuiMFEijHTpqjvnYaPLEbvF1Am+3\r\nJmpjL860z7XyNuVbX1Ch+/5BEtBPmpQ4OjkQmGs6xfDb5ywKkhEl24eXfOaj\r\nG6Sbo8YUkRH9iNzIUUTzW1lxf6GpfstV1lo37ItUJuUNREzA3hzWklZfuyUf\r\npWMoQV/YFioriF9RDf0+hCxH+GYfz/tWFWQn0FLWjHF5emc3AIzvbOFlI0bK\r\nVQJLoNCeem9NpAm99EDfhds03slf8tAEO4XrA56et+B/muzEuJjnnGQuEAZX\r\nCB/TOSwKh6fRNq9iraKh8Lczux6TbdyQFUR3Gka0YVLuS3qpY3vo1HQxF0kE\r\nae278nKizz7G6HeYT9dIlADKRXHsiGDYswRBgo9NznPxB4tdkC1QLhgr2fRf\r\nBCVPJ2DaFWQZZjXyFtVevSz6xE6znnLS/TkFBCvAiboQddr6ZWeP3UpISvsg\r\ndoEL1eG2EmFaHHpbM4N9IoQ9cYy3IEInRWc=\r\n=sOcm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06d91ed95038d214c05f37fe1b5ea002c46ce44b", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-+g07i6AQWer12OqT97N+C2gSoVSNi5n+q8ooa5jwaOcK6WKFKECNzqtlCNDlqQgXLXu10urX+tB8Q+BhmTU20A==", "signatures": [{"sig": "MEUCIQC+A52UOh4E1AW9LtMsw5kGUgM9r/3S4C60sTcOMLZbIwIgOJokLs033pin3rqc+OtiR3cLMUP6/zjNrhEa1gc1nvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkcYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrmA/+OopHFk8P4UV/SanEd1YwX+Af+lUwn+tNfTNzvgf+bHvr8G8M\r\nONV6A+/+9PPXohCY3lWk+7wkHoylSneRGcsU53JVh1SPdB7BxGSsj8bnQSVz\r\nc71mU0msLsbaxFe28HBzXA5mqvWHnHmAlqzz6JKVjUJYHWO4ePu9FtPGL4cP\r\ncWwSatcAuCPIjHyJcAUA4PSe+pULPZv26taGuGDgNjgwXM/KomNEBqHD2AcQ\r\n7JAdgMNVehXfdJdtyjNIN1Bc1hPSU5vkt32hB0Az0vuqwx9Ceef2Xc8eHWKv\r\nFnKG55Z/7HoZiWJIdyRo3Lrmjn5p3h169T63jpeSdDmYjPR5PASXnvE13k0Z\r\n2FyVyoh2To6jYbI/KfpgpQokhZfRr8BfGUfNpCXJ8G54oXU/BvMsb+nCzZaf\r\ntysV6KyJILtf+hPyseQqfxMCOaq+4EK2hnjDxFeN3YfSs6yPwcb6KyQ674sm\r\nQ1U4j8jp+qE4oA4A3e1epvsLJgtNVYucAy3BTXgcWzHxRbGRn7ZnwfiWGKzj\r\nXYZivPwVwsBrAAZQl1TjPr2GpD1Dg7R4Orl2AZVnKutdutJtSILgIrq0VC5R\r\nKpDXDyHknHLR2Tsr48PfRl5KfD0pBFbg3hHor7jFHFcQymL3lWhXKD+41nry\r\nD4tMKWFoKI3sdNWwwwoQpohXrbvtTUDM6lk=\r\n=4qLT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "00f55704dea1162e50e6113c88cd170a3df71d19", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-99b9Thri+/pDCk6+QSMQpKLVuVyuPHJHZjIgIoaecB21mW4SS2h/jnX6IgRcjFJfhOSJyFjFPO3nDDaQsbXkdw==", "signatures": [{"sig": "MEYCIQD0KXAZ6NLtdvn1KBh6OLzs9JyMxn4L0hvi1mz8Q3haHwIhANXx5/TGesfF29OJIfkjDnI/kbhhX12DMwnbUEI/6y8T", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkynACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQ5A/7B7htsbf8v4uK+lsjOlAPW4yZrnEjQms81NOEY/YtdYk7zZNt\r\nWBk+8DSftVirrbX0MHi1N6TVsLIQPOcH8SrRBajwGdWdcuJ/VBdAXeOMwoKK\r\noAFuDZl9GtfE5hWh2t6XHJYy9Y9uDdRE8rPRIOryoSYF72BvSWhZH+l0ABCw\r\n9/EpPPnb521cIDeZ68meYbgCXxBjmmll5icPwcPPNz0dLMCZc+2ZBQEmGN5v\r\nHWavX0iOLkfi1Y6KTIQdUzgqkJas2OOlcsjlChleKPEH4QCjnc9v9y0+rged\r\nM/h7IQXiKy1OQYczTCTTburO/SNePIv9ZTMBw3Mjo4z7JaprvdhV4cKefOJb\r\ntO4ctFssFlRUfnHgec27OJWlBZgySuBHp+JFsC4oz8yg0us7XmXy3M2arSg4\r\nxyMU0G2mcb+MfR+xOi28DnZRgFxj9VlBTPdw+yGZJPGN1VE93h/7wV/LIUTv\r\nSw/gbSWuPSMhNcoXNTD7xm8BvjOYUFfp4F50cnx9ER1VeEbFyBtak+MPZHAD\r\nZjjlaGxSvOqmNqgNkZyUo0N00PnayiuMJEc4dnZ832AXYq4iQC8NBevrC2AR\r\nJINONcnym0bmy5hX/VqNjAn0eBeQ/YSUh1Fd5C3T5bgnasCX3Q+ztzD6hFIj\r\nDIAc5ObQwJZ2g2S9HnWs2ZUMv8pU1QARpBc=\r\n=iQn8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bc8d6286f8b2612a8e1954f339fbcef83340e1e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-a87SwUoZthokaVCe+vYnE5jseG2768CClkodfKkIX4vNV1LeQlfUYewrrglkqK64PpzJ7mR2p+HpDfElxAOF0Q==", "signatures": [{"sig": "MEUCIEk+60r/CorBOyBzty5K4EzdU057jqdaelDNzh77FcFXAiEA5AoyiX9N6x9K+zs0ZWSVlx5zgNp8u4IYEMdqfutQNng=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPkg/7BGpRGZOtaoVJncs4nm5UUYRRooL7xvLINZ8ad3ph8V9FIAvr\r\nvvdjnfysNrkTUSwRu7eFLuwCmcV/wWsF/hSBjxiRIa7EBdWdEV3Dm7gTiNY3\r\n3NtHR7wghRJ1kdFpz3njZG169YblVfnvtAu+hyoP36SPc/2btX6Rf5GmEzH4\r\n/aF0UAMPyPDFXZmfTn3ZX8+vjZ0g1w4euHnHs3MCKzxgjLlMQRBXFId6sflJ\r\nuG/3e7b5rvsj25PyzcEwBlWdWDLXG6mLttQed4iRA5Rmk1CVuB1YVXL2XlSW\r\nOMuwyYQdUTqPz1s03/o401mM3ZawHjV1KOgXmXW25e0ew587N24j+7IfQG6V\r\na+RcdMQ/3J9cfYod+70xFlCQwo3feKSqLFKX+jKDmvNhfKLZNIBGhmgls0sR\r\nj+J/g0zYP70NaLJ/EwOWyrIp0w4o2ytlL+mjwDMsT9+Dx5lliAd4EyY+IehU\r\nMq0/Gaq4TmnS6k3Wv3iDiIBTJCjYsQqukQvIzOan4UeRAgJdwB2BqOU51mUD\r\nRSMVf2j/SCHCSi6ET15Sx3+5YDMkxr+qzBDvb4JHCxdIYo0LZn+efng6uNmf\r\nyz54XFpMwAuKdzTh2ugMWS1OEUN9QmMYxcOmtZcyXuR/ykt8qbjVI7Jd/ppr\r\nqiw7WSmHySBjpI1JGII6bLbkpXk++Xh4iwU=\r\n=N66E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "014ed0b6af5bc3e2f91bf4745ea74c39b755ca95", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-0CulGdLmjMEDs+xbEJvbJDdIQkPtWbyJxB7gZ49HcGlVqwX+hfS0XtlhkXLLQQ5cg0iwgO6ompSfwBJbvW/SHg==", "signatures": [{"sig": "MEYCIQCrZ307H2vtq6TxUl4kkNCXcxlwj5rngqsySwXzPSjECQIhAMCZxWL0QROnJqpuWD6U9PirHQlKAgIDB5lVdW6jiwXT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpC7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWGQ/+PkDLvp2nMW/QchKa+/vPEnosoHKQb7sMfNde3WKhDLHajdnk\r\nMu1jwH0SPtb+OLHLusUn6tcWzschvlZnSbVcav3dwsP1J2kjE9fkJmIT99vB\r\n9bwyK8MDbN44xT1hkaRrxVnQHrOR7aSt6bU5WMA3INQxCHOfJkbH5XiLP2ZK\r\nNmJeEqZ4CoKS5whKvE3WflksNYwO21r23QtKpeY52doiEOu2k6Vvhob75SZJ\r\nYEGXBejYlvA/QB+2Xl4kYa+Da4EfpEGKT2YoApWs/T9V190fx5p1V1OfXLr9\r\nhqd2eWXCbg2CzWndRIWCINZNH3CUV6na6BJ8NdkCJdDlabxVZwi59N/WpK2h\r\nPa+UocOk9DGMb1w1Lzlc4jLjxTvwSmMz9jWhPifpS3EVZWRMVbVi1PnLwj6U\r\nVVEIDiq0eLC9rNnGJRpIxlE4N8JI/mII3cHleliWbbqktDGNBI16K2Ym2+VA\r\nkkuoCu5YygsrWbI2X8rpbTdJBN5cvxShS7f7/EqUbA5CY+NbmoA6WVJNa4R6\r\nL8na74DzA+R5/iOOZrvwXY/7JKWRNIL+oZc2jVyuzHye8JPf8GcP7rynIZQ1\r\nkOKwKv8/IvbbkrrOVbFC/pWJVpkl7N2+v3DChGq5tNmX1uHxULOJ6A74U+zf\r\neuKCbVTKibOPxWiu4Bu2ZimpfXaBWgjT6lQ=\r\n=36fx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1401783570aa50392cdbd9d7b8385a4fd5b3b931", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-hO+uqRCbCm6zGwoajb4gO31K/ZP3n+KlPCEh3bm9n8W2oDZn/RIJt+vFU9gRHrARm9dArYh6eLDnV59unDeqEQ==", "signatures": [{"sig": "MEUCIHf4KSAczwLqUmBsatKgUWSJjOs1kSaVtjeJHoyZcLqQAiEArckYXwf2910vjL0/4nDYBkrWLQ7JsEnbD4DRVtMORCM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF30eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCFw//ccywij0Zh3t5FT3P2XjUSXwWaavMLQQOWR3qaakStReUjv8k\r\nP7Mi1UdtV7JubHqpISn0cf65UvhEbqpnd1r6MpiaHL0uTUDEWZ5ConPQ7zOf\r\nLRy+1FUtqRUXZ1Bm1vi3MJmyAvz4mABmmRumQ/aCsk1qEap+7wwXoS4nFerZ\r\nF0VoctL/XRvRqndIUQeQlEB9D8eq+7HRYLtp3ZgPYGlEqKYpPAFO9pKWncZ6\r\n+TdVtosYDIBv65gKs0RDRZ0JSG9XB2p2Cn2kliLlhUw1Edke3mRMwAD6I+hv\r\nxZRSHJmc4FFbmESsBLHkDhohspj2QWclS2BDhbbPnvjPDW0wiR9CHpxrgtnx\r\nR8xLl1MbmphBHMfxY9DKrZ9q022I3sS6Gwnius5T92EoNH7atrANmrQZNMnA\r\nYMx3/g2VRwySTLhSROYVY6KmATkbCceqVt1u6NkvXqEzl8sfKDCgLFq3Njap\r\nqjXgD2eKCX01x8wGhsGwQS8G/xDzg/hponwo0bKu7XYDHs0jB7pyzx1ibsqZ\r\nWpCBknrVtw83RgXshQrqoZ6G+wALNwRZBmz7zpDpoJvbadzFCewHsVK2fulv\r\nXERAMAd9gDlsoqWlyYImxaBdrJVrP7AOVIdLHbbHUOHL4XMmZMvgpMxRqp7k\r\nw7P7VhV3fj8UlGQkH/fldAbyz2KvRcbg8UI=\r\n=qmju\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "95ad3e899eb2728437f94dec84362d334a0588e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-ihBslyNUYNmbFmT71zllF23NJql35PS0ZhbtbylU2ji9IIuR6lTQV05K6jKlgeyEIINN3IVVSNl4Qa4M1jlJqw==", "signatures": [{"sig": "MEYCIQDoqlmYz5YtCt6l3nBeOBVo3HzliYtvzWjB4QfIiXckNwIhAOXYEpfwcshICJj90XbMwSTDPu4G3O4yEuG3DCSbvtre", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4W/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo4FQ//fMZHcfEaRfO0k3CzNPiafpHJhn2bMEPOfQPVCuy+kw2+RVD8\r\nNjUmIT1i1nA8Hh7EyUueTfhhNSVHwfG6bYZ1toFHPynAG/ClVQZWLuD8ooY+\r\nQNv+7LOiJXy8Oje5nauLRg4BZ6TLRQmIBqz6fBNahIkGVNGyU/uUgWBhKLvl\r\nDpu1pBQSfCy9et1X1hLzRguH8q7UEyX6FbgsXkwxQfiCcy5C0rYNqKia5<PERSON>ci\r\ncH91o9BRjnSttv1JgfLRjvzfjDb4B7P7F9+oFKvPU2a5DHJtgBufZZPiaTUf\r\nvLtKF55aRNCLi+Y2NxU8rLNwyRajd2uPGaVUpezghOVTIGH41RrmBMznQzuO\r\nraPaAGPkueSy94umd2wUOx6AbNEY7p8jeioPtXy4V/6O2iSdKyBNYS2rrOrB\r\nNWy+fmjclgBcSHnM9JNGdSZir1pcLsbeS3s8PWUtIwDLStP2HpleQs2vdSfi\r\n2lMdnMJQyEERAjy0K/YEUf0RkZJBQUAy5zSenJK3dU/dqBx6Vpkl61EQiUmi\r\n/HOn0SZSWyLN2o2QJNFKyFLskdR/0LJoDXxqtPSbU3MFmrsQxpeqtbc/ZIv9\r\nvKU2FIPmLQ5WZmiqWW+/pt+DvCC1l8Ra7gDYTAlNb4m/5x1eZureFsUqrtr5\r\nuUnGafv8tx40lnxZzOnnkFa7mDec6VN5j6E=\r\n=B+Ac\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-arrow", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a4f0820b764ea5ebe0c06110f2eee0b159413126", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-o9EktbQickugvoLC+pA8Msxtx9FlAxUg9oA0lJSrDVDM+q6MEt39AD8YIXFmeWOcNKWEq24gr3oTtsZA0oOOGQ==", "signatures": [{"sig": "MEYCIQD0zOKSfAQKVhXEeB7nICs3asa0s9uW5n1U/AV73gYYMgIhAMS0FEROIL1CeVYbi0Ov6FVU/uZMoXYByflRLNkcq6R2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8476, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8Y+ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmozVA//YJ/ARh4Z4IJJHlVhQRkrtDEpGekngOwcQvDMnBe9gxXoA5Bn\r\ne2b57JPyx462XH66p1gCZSxRRicS29Vzr3S9hcrTiLGjoyscQAWd576WiZdg\r\njc6aQEGGKo/R3km//b0fsjDGGHnuXHI+t0INfzvY6Hsq23ErbafaC1nRhzA8\r\nlfeqEnRxD2+RE+BxOdX0NYybDjWluGTuvvVmRlKHBd+hg9inkUYoBGYNBON1\r\nciI509Lg0VfZ5WoU+IVHbbt9EOJphyX67wXgGRa82lsV9TGo/agmpknRPm8p\r\n4pT47SVg4yJBn691y+MhQG9gsnfJIyycJ0U+r+sts6bPUvXsHl5WSGIok0jK\r\n9lZhhy0Ut88qg7ZU0kZNkhRu9eZZ5z/2+Hkt5mRPjZZy/KugowyR/BuD6+mS\r\nCvYm80nsSWGT4IOpk8yRxpc7xQ+H7aclPGziii98LpKKhzzJnG30SasVJKTu\r\nZX+NvpYC7dwTyZngoFJOyG0i4Uq4Iot1llgCxWhlSpd40Gn/08yO8NtfZzX+\r\n2vpGrayGWvMx85jcDiFD3mMoiNhXYeuXLkxIdzq0Hr+SkHoUM0hzZbxGvWUM\r\nibSBftdPRmtgzcAfuOHF3NqDGj5wuMN/yYd9AMveTbkWZ6s+J2q+z0uf8GCe\r\nCMFx8420UdyHI9cFlFGMSrNtPlxaFGX+KZw=\r\n=e1Hb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-arrow", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a871448a418cd3507d83840fdd47558cb961672b", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-BB6XzAb7Ml7+wwpFdYVtZpK1BlMgqyafSQNGzhIpSZ4uXvXOHPlR5GP8M449JkeQzgQjv9Mp1AsJxFC0KuOtuA==", "signatures": [{"sig": "MEYCIQDr2G2rSiPtTyQVcVUuKmdbbBDUlun1QPjwG0ZbUwDpdgIhALu3+wKsqvRs21yeK/QqUDlj1oAqmXWj8V+MKtAty9IG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8j8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8qA/9EJds7/Vmi2gTekd4NF/NOXJfYtvvuZEAdCYvhCyJu65716g/\r\nOZVr28xsIgS3uFhwrwbNckUree36zqCh/IbApFPpwvOSSLK+fkXejqTKJeYU\r\noHK6mDoJ6yxYm20GM0AVXfY81tkyXyRiRTnebmNIvRsfkZY9qBflMP+fAnqS\r\nb1d7sAzLoUfPRLecnWya+YV3FPE3Ze6bnYYbon9ijabrhvYDdmK6Dqyvj4Qt\r\nviWodEVlKsdps2jOFhhXbtorPMfslZp19SinP6/FELIsxBAL72EO+TrxTEXz\r\nLkcmsha0jr38cZIDDc+qFpHd1fDmVmQ0uMWHfPF2RUje0N+EIIdUDx5r462h\r\nCL587cv4qDdD8pYvrPRKLFRvSluWj1A9r4Q5uDFdOn5VY3rh6PpEG1U57x+/\r\nopLbk/AgQn7fcNjKEHfF00hpx9qRPiwupPq4A/u9xC/zuMKfINLsKtuUtJAZ\r\nN8lkaK//qppfpVDMDhKIZlYQIQAiCJ+zalgkFMHmDx3cy3mc883cf4YJuKIR\r\nesDzgvYA5cUXwJDMl2IgzePiSPkz4IbGlBmqY8WKMwaCKnrboifnOSX4rGEM\r\nJzbsJeEGMW0LmaFYQcm+I+jjgHZnmzXbXC6KWBQDCD0uECT11nOPR7tzM/eo\r\ndvd4Xm96GKCPIKJ/4P8+TYnPqDd5ETUb2ow=\r\n=2VIZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d95f7b233792bab04d524403da5e4ea8e8ab6b2e", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-iqj2vQt2WPQ36dpWfelDaR+U/sGF7s8o8S1Xkh5cwOdBtqMI299dgKS/F9aLC5qQXdNdf4Om5IkvmtnkbviY4Q==", "signatures": [{"sig": "MEYCIQD1YjIwOhoy1G4e6T7GuCVZbkn1jjsAs0PzonSxbXG+wgIhANU1TyHTSOICsE5fisGI1XapCRbzEZ7QoDWhAqyBjB3s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAPqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpB8xAAnamn8IYDtiC3ZJT0fCtpRfP46zhbqzSHzOCvdwotTvn+mvha\r\no8znegl3TWG3oWeRrKYedpuNe61HkasC0eHXqBmQVCxpqlJawpPCMRnqGyjy\r\n0lLMPJrl5vPvWxENNoH5ltDvRvZKKhUzTU6NVezJUJRw7lLhMhFuo+HwCl+t\r\n6HhoMeZbKU8GmRB6BzdXfRlK3oPWlZjv5fI3vBK33LvtmTCRj1MKtKK8As+k\r\n0sQgtu7avpLUNnl439MDgk+n8l88GWapanIz9Qu7FGFrHfAqEzpVzM9wNqRi\r\nqiXgrZ32VSkZHHrHiJ2sO5Cw63z4l3TzfJrlQsoD8I6zDthWE2i9GwT2pNSR\r\nF+4NCAVvJF4w5XQg7Mjt5M53eM4fATYsOJYHqUVIp6efNUoE9zfkSWm0ev5v\r\nd9Mnb+amxP4Fvz9m3cAc5aSEHknD/L1x1GYAlivnU24JbWEoV9Pc10/oGY5g\r\nKSDGtgRZpDQiNlrJ1anenn4rr6NuOVV7IAOEevnYrgeXQTwTk/buSyyrESMv\r\nc1zmB46CKZIVggIx+RZ5dd09UgBld9BiS7fzwddmPf1wY/mIqjeq4PvFj3qF\r\nraBWvQI0htm5+e1fUUSlZx8wR9/ejkBnqBpUREsABLQYupOo/SDE1kfywHyw\r\nfMzec6kPH6JBvT7icm7J9B0wy/KpikbqM5U=\r\n=v4JK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8d5506388f200bca01919977d8cc6818aaad8f8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-+UPN2sWCnEfqZEjCzm/li5057v//mZZK2RaTp6yiZCRWjF9uoxujEc+R6ufl/XeAnx5sC0+jZ20bYGpL0IDvLQ==", "signatures": [{"sig": "MEQCIEYC7d5gnO7cdRFDal5X3qepC5IAL02igc90voiuOQ7XAiAl2lXFdd2iokSzEyQ6K8LTMd6mL4e3UT5/EIxUvNpF5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8483, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZTA//TS36/ME3rHj8W3SAfjAXhd0sGhiuKN8gjlWkUd5tlsoOahL+\r\nKbwCH8445ipqoo/11jt6WDU2tmUw2dyfUoVZ4LpIBZqWpqErXqZPuoBnmg3g\r\nux/KnKARe59CjrTUcQC3FVAWlS0VaeDJWnQT0YOYGwVUd5BNMZG2pI4mHdpH\r\nIfTLxfFO06PQAH/PlXTRtW50sqUTpuN3Z7ddfMejobcyKqelwT7ReHHFE+ga\r\nwTl7ePV5m3VX2ktPi8YA1xvZ+9mXH1+wyikQ9Ohz2uC5wg1kMTSI0OfY3pNb\r\n5JzDoqsF9pWJ4ArjdO9D12CDPd0JlaAK0Gmk1FPVnZCvEecHNPvS+iPE99Qt\r\nEgtYZTATnFIZAf11uy+boqMDH/TkFu/faU1r2RCpB0tUogfnjBni+84juvqK\r\nzwywtAtCv0rM2G6BkVsAJgRwnWmFMpWNHW/XJW/J8C8+PnZGV6Gulw9Kms8s\r\nkYv5NXwkkB2wSjGbeD81Q9uFe9h/5Z+PyOR6gNj3xGWxo7vwTVxFU1ddBVW5\r\niawCXX8iLQxUdV0bIjiHj3sJ4WqAJswPW3Vri4SXTHs0vpUGPwyQ2gkrtnzQ\r\nv+btOPlPFmjyUqNS4Tn6cxL7LVYnmykzQs31oEdEWFoxaCr45YjxVYvNZ9YN\r\nkmP5a6eDBsX7JMPvxmBmH6aPg6jZlahfux4=\r\n=tqfq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5a11f63554f1f2fdc0f70c5e5a039fe10e4f41b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-GRg013Ug6WSF5A9bRw7sZpkRvEUI0GH2T0eRdZzlptmjZmppzqsHgddz41HWVvQdfEOlteCZzKdRKo7AkRHtsg==", "signatures": [{"sig": "MEUCIQDDiWBLFxRx4oX12SLh1HChAgP1CrQdyNYvcGjAdD7iqgIgChWBj5zjhoCQUwTPYao4nCXbgVfh06zeMZmRoQRmHDY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmriOg//SqMpOduj/vrnrEfo4mnRNS4lwjOnJMLEpOTV8cQhElIIU1GN\r\nSs3fXS4tkE8153N5vIToDTAqTEjI2EParUriQ4ELC8xtSKvwMgHemGd5t9C1\r\nLRZeXdJPRH1FeoDXAJ7rbtY3O6L6Blj6HYjEAWFHSXstJu3qALWNxMye1MO6\r\nVolbOqDDW995hNXUc3ySnMnKPm0mWZn1PavQ/3GkwvmLg6xAK5wrahXF0fGw\r\nPlot/WjK3qVtUcXYu2H4xG+frqPpiZ2sEVbjKS+0TewzV8z/1qhzU5LxJ7Cv\r\nLbHQoQOiXq9WnDODyCX335eKUUMw/39KB0WxE7/QJ51U0XVo8ofzDtskVEdu\r\nAbaER0fZP4slm9uM8SA7FUrgl25vzWUcPB5s5eLyddDFQ64GLCk4UdwKxqn+\r\nASFWcseJ/mzaYs9W/qlJDTE9vLBzNGBCidRgDPeQJ1aSxyYXDNgDAn423Aag\r\nVDjk97KrC3/PdrLC1XPQIdZBC0NINxzjI1TeMaMM/Wr/yw+VrNkATn/sM14o\r\nGvTvcsSqfRhyDQNkocGdDn6yIH+Z/esk8R4Xsf5Gwb74gMUIu4SkfdO6Y8ty\r\n3KL9RzjtJe6W7RxtVEaTbdX3fXTRpXF/AKYCZeSp9xPpqG0m6eL++eSXicU1\r\nkWT4OhZ1xHZ6TL9hSUz/FpDE2UNTpL0Qugs=\r\n=F9Lp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1d48402a741b0424202f8c06aea86f3fc06e9331", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-88qVv7Eod8cLckl8OYPOSlqmm8ukhlUgyOie9gac1EuWEVLJoG/ydMHaE5IEp0gn4mAR3Z/9x9Qa6KrUOuVWvg==", "signatures": [{"sig": "MEUCIQCp2rShUcX3qEWVa1R+XUOfyhCM/reUXUYZCtqWSjTuWwIgI3eyOQvO6yR9U1ZquEOohnQ0Bql0aVIkYTapM2V+3Wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog5xAAheyWQJbC2REl5kgXE+psfP9tyv+sMpfXxeEb/4UZ7hnyQpqI\r\nQsxl3FrXYysaZSYXUyGfJneeSVK0KQszHHcehdNUQJV+mdZySq6OrVoLHNWn\r\n0bJKmA4Fdlgw4fOE0syHIWoFgk1Q+JvfTRdbT7zcM5V/ogYaRPOipPeEXG0T\r\nN3O4DOmAe7fdxMvkO/tn1e6x9vR1XsIlDZA+Tupo3m4UMmOLgszChS0vpqN8\r\nGXV37wJudEmxmx2vF3dkNTO3M3zQiO8W4JLnfaGSvNXMLjgiCpXOp4FSAtA9\r\nT43f4peoX3LBDkmWP2Gj0fbF1Qu7RE8g8D2wxHv2v5P6KWmIjK5ArzoebKGg\r\nzIR5aHHNmJ3l4DEzMGrm+HDNoKdZ9xPb/7bgiCvU6fUdPpNNAOYPM2tSGQUi\r\n0klMoLI3NUuAlDdiAu/Tk/Ctt8rAHjGBw06jGCilotyBPiyRNN6Cgd3SD1Ai\r\nJSLHvkRZxiJ7FutDBdfUpWiAcjOrUJx6Rx2Iikk2agWnsbb+D8e1X/OA3lzk\r\n2ckTnJ9dK4LSSVdAXNIvdjT/OB+Jzm6PKKd/roFndnX3rFXc0jKNz/rpIwtr\r\n9sbnhHNFKNEVtRxY+gSXdiAxsVCdBZNbPSYweVn3sbE6t5IWJvB+fDZ8bi8N\r\nlMhjSaFbRIiapQoOduyVWdgFypeuBUmMvwg=\r\n=Oljs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2bde0ca2794bdce3496bbe67ac7470488dba62aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-tzaQmbo51tC7sWAf7Otn0dH6HfMSQzKzguWpLsgXwQ/gYgwZPWpUG0ws+mpvLbN4oSn00ALMSMxJMSi4P3xXqQ==", "signatures": [{"sig": "MEQCIBY/vzM5Qics9plTwrOjX//xnY5ih9Fj9y2fjuz0jmm4AiAl7+PxwG8T32WqEYRJiOQLtSuBjGPbyFYiIx0aUEilYg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOXA//cQ+v8D0oU5GuXuI6dBDKgclji10lNS3vmTLG32J8feEfGpau\r\n3z/FLPn0fFQpyaLfKkLyTm8SPdLdRjI7UsgaDUTPXkFED+MU4DW6VSkUFPEg\r\nDsCDXhIDjdmftImRlKmj5ipfY/VZ1FW69iBl2eDr1yUI4kIAaiwdgpwuizFN\r\nZBcYKbSk4XC6QpxytMc7AuSxBMWOVyZOv0atX8e4STiWZBox6jnjs8t+1kv6\r\nmbBgmYuUh3Sa4wrYy2X/TsTv5nZeW8UK/5OOUsqX6vpL6oWc4jR3NSo2y9Kc\r\not6m7z2O3K7x+pfhbMTOUFLyT45hUyr3xkMLEoMV/JqGFa9pm1NGlUrAzxzO\r\nVNfrkOFZVfJGef7uKLsPdrHlC34N93XKa0JGLHZH+doh6c6uoeXVLCxQBiu9\r\n8ipDVSPla2FELWHTzQu/2p/G9YDBZGPUVPyaOicaGbayT6HnG76h2Ej1FY8p\r\n83ulbsojSN9c+4pfz5s3+9PMeLBPDjEm4Z0XjIngP0g9ykQ4S3ruMpvVlJtV\r\n+OE0a034mS2UCqWs7CgvJrK4EQaveNPkuQFDz3vATjrSA0JXWtOuZuTQd0ZQ\r\nZgS9yBQ1MGHp35Fvd5cRDLvfpY0LWbNqK2mkchvQtJ4qhmReo+Sr2Ye+gxie\r\naDmOVAOzE/+DmFuMpv2CVoVXjkuqJTrH8wo=\r\n=PRXJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b0e5322ee2d685f2e8b73ce6410904fd60d1675a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-4tGefglAFe02via/2c7xLTV0Qu+nNMEFxaeOetkBTvXcNV+vYqsHokS3b1QAFY49jyxRLFLj7aW11JwhWIT7Cg==", "signatures": [{"sig": "MEUCIQDdPbeEhuNgUUVWmmIQc1d0jgMwp77VQqKNtsPGC+5rIwIgcSBuwMHGqar6EOAzpF73TLk77/5ddnmpVJSk1a4SNug=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqYBAAkiHQhZI/WHVysszm6Wp6b/n50yLZmSEiJFExjISpWOTPs7NO\r\nxpxuf3E9XXXsnm53pifW+ISpnl2pAGRr14zhmgdn3jp10985ERPB3S8Ujz29\r\nVIBEuSKZA6wGvFzIlhqqcn5OXFf5rshrSvWxUxgqjpjM853e7V+5lst+FM9B\r\nLGXJ4ebM1ars9PYCbpQDuxMZ6qoddJ+5bCR5H2DoKmFQFOrIF6/zF8c4M/Sc\r\nvwXzO8GfqBkXomhiAqDOtBqlLF/h3bCd/3TjIZEZ7mDO0GSWNFCM6LFH1hJN\r\nSi/uHGya7FFr15pkh1HgInBNyx5yqwKiQlytAFICmjcQSm79NImCa3G45VkM\r\noWktDSgQhKBkiRz37HdjiZXd0R5TjaGWZl/LOc8jmiGYoG74xa2VpWby6NGr\r\nBytaOVhfCzcxJLXYbkpBIwsYvQFd8fWoDkVj2tHbXTsGxxd5jbiFofurGH/8\r\nvZlTdPSlJOsAPnHEtb93x3PI7HwMtyn1XnchyJ/7ara5J83bLF4XOc+HRsbk\r\nsigZZrJjxZt8Ymc5do+tGdjjtcUy8KW/Ic0aO+U8rnQD6L/HsA2mfsPxJGVe\r\nJ8Y2HhrvdR+yQqio1HwIa0JdFfYnl/oZ+3HC5UZjbDVpZajB4CLLF/lgmYm6\r\ndrPLPiWErCO3hbhsi3aNrZeM2jvnuulITNw=\r\n=rhkb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "de9614b5f862f02ebc16181339e4eef05152e9d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-FFnTwKgOGyIbQAwHZtpqP5dHsOFdQKXDEkAZTku7p0Qbcr+wcl6eylVlwUUmvyS7JdkL8rhyMLn5+eH+HxMZSw==", "signatures": [{"sig": "MEYCIQD5yqdpLjbvMI6pJf919ocHlUgbk3jtksZ2IhW88ZDNsgIhAKQWOzFmKMasA55+WHnZ7vFHI3pd9sfNww5QrDB1u3rc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Eg//fN3+UYzM8mBNEraJTND9+kAzbYCq1LcWULmcv/8+WLcOZ1/w\r\ntNxwv6GI1SwU+XxClpHhouLpZaQcbamEQpeaOwywWaOlo76zzFPlw++quGo/\r\nIY0s8xAjOIsVCMf9RQhtlN6TKT1Jh60LlO5s1FHVDNWzI6OIgWoChsBHJTxo\r\nj4W9nOW6E8eavJa3Us9FDTtY9XfqruEQk2887hxsrDwuH3So37fFya4zIjo0\r\nOxJeEbhULTPJQVUxC8iXQIx7aAHHSWA0c5gFzpT8tJxxxqKT5qotlTBuPZWU\r\nFx3fGROS1l8jlm0ezjB9ZuFClNmZaU/acTX5eEKrGOXQUyjsoKt8U+mrVK+7\r\nyS9Nh+hEGqsFi5LDvvy6siohdUBMTIKIwlpc6vGyMGCbcXL1GGOqW6z7xukG\r\nd5PSUKgWkt66jYcT55CpegwhWASAJ8b4e8BCjtgs0j8VqoenjLTaFVS+vmMw\r\n4tXVT57/AndZaWME19jP5OQ6aZkPq9UQODLtXbvKVnV+Y+b1rbqSGkiyjbLU\r\nDKb0xu06jdrxLq5k2IXhHjZgR6KLarUcCBfyXab52NpXRzV4b8PYH3OwwFp9\r\nkQExXw5jS2xlMxe7a07azQJZIMNHaRPpVwvA4KTIgWa3ydknviMviI21Vp4V\r\n4Mlcm3ZmSHvVVsF/ClOUTre8f/6TBV53uMM=\r\n=zCKH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "16e16193396b701b9553424311c55e54a24dbe6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-xoaZxW9Vqs/LzwqDcuAD9CjWM5iXh0hQyGznrwU0X7FdTrGuicTLxBXi/qE5Zio3cVaVaCuVBVOo7a6cknbz8w==", "signatures": [{"sig": "MEUCIQCp8OVRSrTbMwFJdYdFPGB8VOTk9kZKO4PAr1fqn7gAmAIgFQWx8G7u47GgVKqsMgLY9WmBEvXWL9aI4Uu2J+HDm6k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpR4A//cd7KlNyDEQOhgVCTNFYFGkBGgKl9PlOt1JtGBMhL45BuCBD8\r\nu158ivV/+WkeNrDe0RfNTDsx4MWNhammr111vLjUnduexy9f3f0OLXRaOByg\r\n1R++IQk64w0OUoBsEY/nb8qKRTo0Bswx/oO8mn1v5kY8k6J36HV2jQj1qfgT\r\nn/BCCrVb4XDCWmfqTpL9iJD2dbadGccx1OD2ulw0/EWvfXdiv3iMZAR1D/gG\r\n0SszrHpoj7SzIl8o5sDYjY76lib7XyEnqg30fZBNoFvh8C+fgp6gEMEk6AOl\r\nmBCZBC7m4PShsO06LR+rOngvEVecQpIbRPI5OYfVxFQn7vIh2kvvuFzlCXd0\r\nJ9YE+1wei9pMwAyJerh+HSmcb7VBqK85pB79LmiqEvm0Bvlt4gGpc0BfGRT5\r\n13EYXKCK+xfHmORR9TXaNrwaP8ukm+OifStTK2XGWuIBj6oFKcEwy6XtAZnd\r\nNFsVWp0MyAXxvYelSDgZLZUSGVGlfps6THc0gnROnkjyHobeRC6DEJmSRXhP\r\ncxKRX0dgXfDo/zbFG003ILpdu/Wt/rSVbAcpRVwBZ+98pwbP5ws/NgBEi176\r\n6OrmaHxTsc1XyZXxOoK8/HclbuaNKm6cpw+16NTOjCVtYIkMtf5qAAug9U0g\r\n7qZIuwsEsMJ5lxy0hmWa/ePGo5hb/OmE/YU=\r\n=sc7Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "29ea9562fcea65dc2983cbbc38b438182a411b30", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-8b8/4NwNrboDONINzmZB5OuM2sqBL6p/e2+ke7dA2LV26dbuu86q5o51aNrn89RogQrkOPtG1wtG0MECGvW+PQ==", "signatures": [{"sig": "MEQCIA4B3O8IwB3ggQQsECjNhrKbZg4I3OzFUneQijXUW69TAiAJG77D3PabNumqA+6V2tICnj7iE75SPrce56V9VITYgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11006, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpFsQ//S8M1qLlz4aSsbKH4ZPAGKPacdUDiqtXsonf3fNgaLVSxxfUI\r\nDpn7In6flKoWFiaejU0OmEX3ZmvB8YV6sgGtGSaAiCDhu5eu/RAwnA7h1trU\r\n0Zt8bqi1DmFdf207tr1vbQ7x32OiUFnbNDRQz40mICEEVogL00ybgolfd2+j\r\ntMmzDAOH/Y2FrLBccHDQU+ZWU6qHTIxRkuFneYP9rve7syNI5jk3CVyBJCGC\r\nH+FVii5a4xr1h6H63GlI9NVaRtM9nT8Vqt2i+hV4FFH29oSrYYqOVoJgGG4C\r\nKmPCTwadiWk6KOznUtHVPQ9MOirXW8gpoEMJWKVnGrAO/duCS32yNEXs2pHr\r\nZ2D6CyJA2CLpGggZj3DQmgGoacPxQN1/h5NOEkfiL5jJGfgACwQ1SgbWsIg7\r\nm7CqeSAYoNdhiOwgBBgzHBB/iA9XRB95pr2Y4ITYqzR7c4/9PCfgzP7Tdute\r\nWIR46C3YGQolOP5B2vI2odX2sY6R6FTUPHIVxONZd8CeyGauEPcCNjB01hpt\r\nQp0lmYZunkkkOCpXw5eWOBRfMGMgryRm1/dBFdriK/SJaxmK9ZBIvqdbmaPB\r\n1XKSxU3GwYG1XXJJf84AqUg1FzgoWinoEIzcMYSEFfaxk6JrWLA6hMcpppCG\r\nZw69RnxJVVNox0QRSeXadiKXEB36bV5bX2Y=\r\n=IvZ7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "640bb33f24b72c88cf46859a0fe7466adf419d41", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-7lD00JJ0jqb6o5uTYQu/svvCxCyzb7XpAR3fvJaBr6MUuBBmFE3E3lw4Oc+NbKpcW2meggVA1W4OKurhIe3CJQ==", "signatures": [{"sig": "MEYCIQC266SdH3K8j1UX7l6g05PSz9ICx0lBCwK7j1gEHZi+zQIhAI+lr7TQyrUnmGupJR4IoRMVAjsfevhXFafarUityLsv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN9kACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo+nw//ZvUnAYhbzUGo+vt8wo6yml+OjClmizM1RkTCwcmSR/qDjv+S\r\nR3lsz6wgQvK1dX9zTT13+lsL7ZWX/oXu94BelyisOLfTAIXKl0gq2gMHpRTe\r\ntBfaXm+mzgaLeG62ZQ2If9yM7pRYeo9iOzUnpif57J7fO7Q8o0L4dkEXKRCS\r\nYqHqSLtN9fMn+cSgYvOhE7E8zrOSwL1Gdta7GEJMt9EX823VozkLM+JTFXyr\r\nKXyHwc06zX/vdcHfYr+fKpDEvm+W210u5vmba3ZoNvIYO+gpy90+lfyV+aMG\r\nBh8wo5g+0OmQZWSMaR182V6idNexs5km3JzzetyFxgpecbKARI8C7xrkPjbt\r\n3/MFEIACHN5ZGOArb6bTDprmLyYWWX1PiMC9oWpJNAZkTgfWvVAu0xFXLNgH\r\nWkYF81pHcj/udE0oG4YWJOlmLug78Ionn9b8BMirQoGmJs4x6hn0T4ojxpI5\r\ngRNKwtRhAln5hB2EcbecPPQY4tSzZp3GfSME+fAOP7+KmQwcO6V9+xVIlLWQ\r\nhfQO3yHD+kSliDZgLQGbs6G/DZ+hNUBbZHXE5BNRGZEQCUsgfN8H93joq/9O\r\n/3m9Zrqq8z+0CTyVoXbuLz5tsjBOkLyyntzlrqDyt0f7lcyVy8vwK2NDW/U8\r\nwIoZo0pgVtLxn6fuzmOp3qBNH7QaTm2e0+0=\r\n=dxRF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2de1be8cac8e9cb43ddee64bf9db4f917993d9f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ZkD1IXQFq/hhOzapwwO8wDL3rR6oR7DcyfRi+AEBtSi70Tcge0DA9AT7iJxYjovKeQ0EWXlCpXfKaiBfJ9+PwQ==", "signatures": [{"sig": "MEQCIDGTjY5T7e5m+CXC/SR2i966bHe+ikd1OIEC620pJRAhAiB7eQthtnes6kTrI6s2SBVI/oJDkE7iTV666HctPL5YNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSkpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4Dw//VHM7bcJQZP48/79zc9VdeeDcK+KymSLN3sTCsNgL2w1+POhw\r\n/+deTSbsx1CDd6g1r2VTkQx0mDyHyufcFbfU3AkL0aZXtALLgQKKMQ494x5o\r\n07PwDAs6rVtyRqDhf+vjMgQhZ+NWqcs919+8ElbosTqKDXEYcLoVAz9okH4x\r\nOm4CGEI+Oom4JYA4muaziq7NfFTn6SY7jMlv0/JGXnTbd77udzYoh0jKW6Me\r\nVisXhKyYoQU4WGzVNQ1aNXdXFUHz/UQLE94Rt300Zi4z76A0Xx/C/3zi8+sa\r\nOeepfc274/H1E5w1pJG+zfCS1+VVnBdgVXqkVcu0x37uc2quQ7an5IURDt1b\r\nye5YcYzVaOWcP8GFdsVnpMc8/RrQe+qkykFfnaYum/qFI468mFgvRj6Ne7H8\r\n0iFnyIw8T2wF9Xk6kn3YFfnxP5Nsj+O/zCxelQpSWOl/ZAPyLImXLJKZ0yE/\r\nodN5dgwEZjU3ncirFkx2C/zM+oVymM2UGH5j/9Bl/As8/h4UC2Ku1RU/YQxZ\r\nEdpzvolZ4oCpn/qeO8z1M5RkIlTInguR21DES0W8Ognw7oESOpDEKNtrAUGl\r\nVquRBBvwFRWTavAjir9ZPe2iytTzd2Yv/C26gFvAuRIZF9BvMikfi1xBRkb0\r\nGD8s4/5kxaQZ9kwVcfolh9O0nZiKyqkFtMI=\r\n=tK1x\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "64daa0b160bcf08e6910f6651e4672ab088ef145", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-XFaU2cLoB3MAcuQK3tBoFkbXjje19k7LLXEujZ5ahQqlpO+swS+1QftrkkT7EiyZbpzDg+kL336c/Oe6Ati/gQ==", "signatures": [{"sig": "MEQCIBjmrZG+QlXAgPt5Jc+3PGODotPkNVsMsPSnPhf3yP4iAiAcRT7+/Npjc6z8Vt0dm2DppKxpIF9X9N43MAyfQpLe5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11008, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieofiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoN3Q//aIdqBPkR+wJCBkQKqRzhUvsyBkiwuax07e8OwF+oeiNOLnLT\r\nvTmdRU58d1j9fDgklpMpxjlFWE8blg9YWsssPYT9nZtnJYCo91C8Khmi67Ye\r\nMcoQrDyQB0YsfMId76Zf5yHKMsxN7n2l0CRvzTXP3RhHVZiIr36Y1y6eT3ON\r\n6M9QcCqglEr4qK2C/AIyNno8XnkNAgvKlM2PCctr2y4KX1NnK7xxn3UaxHwW\r\n31DkItKBUhq+V/7QkU4Agd2yTGa5DrsJa5xMQFwfDIW/rpuA1OtzxHRT6JnE\r\nSjqEhRfSuZQOFtfZAll+ujNt9hRIlX8FM+uoCD/F2MjkaKwKIbGhZMuJphfx\r\nLDNtbcLSbnXJrWCYouFVR/OV5YN9KEDlCtfme/CkxHdwoaNad9LXYZPXZwG/\r\njaoigyVjPgu2uMeVdAJo8sYUe3o1yCte6p7fzHdsqbLkwJZ+IwIoVKwxLwiC\r\nBUQij9GT5kId8aDxesdRYd42LjjB8qexZGqWJ520BFolFRufmx93Si8DP1Jd\r\npEu6PKspFk5ueL2evl3RpYdxh351wiy5PXOcfLZ3LFcMHn6Uv7tEyI5ZC4kC\r\nMeD6gk3jI0sK0gff3KJUSGeybmc5TzKyOvfN/BKRZtOBu/29O5jCMuVphvPx\r\nSdc4Sb/3MWwRzfAAajWzGzx03WmEN8F3Zg0=\r\n=funs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aa2e81caec17a1910a64b6d75f07db27a92ef573", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-QH00LbC6msD61n3wFC96hbHroX5a+HtL8aA7cZQQkaWdbeYjuHqNCTN58QDf6daalfrIx0zFWBoymvVqo/RzWw==", "signatures": [{"sig": "MEUCIQDvZEYQY2+DGKjTGJ6V6c5Wy2A7qxsIw5aKDkADgpDYAgIgYI4APkIocbL5Mbceo2Iuk5Ie0LKefMwaYbt1IUP404g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepIoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonQBAAkaSZocaEYVZgqCE9L18QLr1uXuOk2emTh8z4gdGJdqazaR2i\r\nCfMwQ1r9lX0xmB6aR0/Qil5zA3+XIkGBa440SIfCCWOZ0RDVYPhSGGBsWAsJ\r\nFykdKi2AUcj70UcDjNuXbUGagY1MZMGOr9hPVzW5RtBAky1DCMz5PvpWB2/D\r\nRIQRCFMvRJpGS/miVH9/TEo7rjvUDqYpRTsj8U68Ch3cb64CbP3B7vFSzVN9\r\nP7wYg+XyW1Uw+BTwNqKvYGBggEbt61A2CnoZd0kQYoBuzCZq/D6PIjbgycKK\r\noFbC0JnPkuvw5jCSqf+LxczXtgBxzmyGiO7od/Kx+ZokmpGdCV+s+W6br5d2\r\nFypB8nBsAn+JTLDdndxUlfjtZP4B1okfdJ3lIqciF37rq4IH1kFrViLQW9zH\r\n0zrzzAH6VXgwfJD/potQXv5HuMN1h/esxqTLw1oSthnFgY5rQAuQL1aqGD4o\r\nVbWrqbw2FrwUSIus0l9Md2Nbsygmh6NwkuXbBUmHJ/l2c0IOLi6Vh1P9wPVk\r\nESoOn6B6aPvGgycKtwQxjWcFOE/gOKJnCa3cA7BHW8sIK2qaIfA+KoM4CL/3\r\nfL+W0UQoEKD0YVc7m3KSLZWbbFmgL5RjAcQXG+tavlT9UJLUdK41iIXv9kEm\r\nGDkmlSfFSo3ckDfPvZh9bA6ftHwdQjRQiEY=\r\n=g4Uw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "564ea2790b10df1762e688b1a3a2767d8e4158ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-4syPdmv/Ii2Y0y6zt2Vdl0SnRmgwhWxy72hDESVQ8p1GsTXs6LFIZZ6NKjxAsxYcjKLEXuIxQOxdoR1iy+0w+w==", "signatures": [{"sig": "MEUCIQC4qHa2H8WfOu7tiX16Jmyfbj939LwoDu2Z5q12m84rMwIgYRypXRKFzc2yewiG8sC9XDVL7euHZcsuS5ZbbaWXXg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoR9hAAhyATgx8ckJAtccECVIQwBq3JoxMC4z+OzU3kbGEiZZVpirwr\r\n+nZaLgyfLOioNJcITtQTngR4OjHnMLYhifCreVS5ltvhy4WnUMmheyxmzGgk\r\nMyS7F5Z6uf2MlGmo7PUcs/EhEBYf1KimGmjUOR2B2wyiXUubxGiqfldWi0nx\r\neKfm0ticFYYAOFHbpXcSV8vriONfbRSCWs3x7bUJy627ycnxXcEeoaxfQCs7\r\na01Wg01bvuHdPNfDoGYL8OGfF+K55nzG9U4YqidWPxtjGE2AdUIrKA1lomRa\r\nRyJMQHPuVqvJOyZOa3JNyRS4NYbY32jKl9XBjMJO3FTJw4f1z0gb/TpS68f7\r\nMzjoKMeEUFXnVRzEAIdsAZy7P38CryDEde/6t0dyeWjQX0wnAMWtF6OLX6kc\r\nOfLSo80YLmwrd5XPEJ3ZFidiV0/z8oxVdGcYKRHOKg5mSUZtmMDoMeeHq4tF\r\nYTwwl8KpYftFH4QmDSPuGiiPegB1UMlZqYBrCz5iPxyK3usP8M9rovRbofv0\r\ndmxhjUk0OuDEcbDs1itngi0EqFi011LrPBJLiKLukkG7eRDMYQNkLAkCfp3r\r\nPCLG/nTTW2E7M2aQuD+21BVwH0/mD3T+OsDLvmf5Nr7pg0XiopZBMYZnyjyY\r\nfz2rTL8jkR2O5AcNXYx9W8p9qDxuCqdt8jU=\r\n=kEpM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "050e722bed7c818f8a0317a40e55a70f6d36bfbd", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-GtWVyHyfdEpS8mFsvZK0ddkz6B3Hye0Q2FYea5yY8+Osn+vBsHqukRD/Q3kqR2A4rRP5pYIbx3XThuVvcgbQhQ==", "signatures": [{"sig": "MEYCIQC5p52e6KVgPG4QHAX8F7FfBg4MCQx+mQKOoRrMfooSPgIhAPvxjhcwzJM4WOOZ780UUBEBtet82/Adwi4Uw3bJP98d", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAzaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3fA//ZImn9DojTjLJDQ6JqIFEj8jCSl9Qfe3iCN97QPpA/LKxe0W2\r\nhIusCCVFJFE5oGjzkg5M6dkWQxlyKM4fxffVer6v6VNv8IwXFf9ER4MLM6m9\r\nGpQ8oMft65f5aPgtZVXqWTK8xKU6UChB7+SqNzpv/TgtljUks10hfk/Bjb/q\r\nihLmSrxNWAp8372EXHj/VoaDr/+ndBKfWdIo+uJQmixylYSWZxEm16U6NncZ\r\nDp/tlEURsctKs+tC8LatDChbCsuKdCKIB/UHsEwzXCdLGsHqFMUJadbwCOLx\r\n2yvJYubt407ZG5hmMbZRZiaOR3jJ+Z0jZzIegZyxWMA0B8AQ9myuGoZTo51d\r\nlC+Nn2JFK+kKimMF7n5Monk9nfjYY8d6UmE3XYCPWeJjRkHKeU/MHaqFpeua\r\nHAeM4pannBUjNLK/Ki/i19uHU5bbJm+KhGhE+4YYWeGbov54EzMeOnAkVULu\r\nnfQb8lMDaZ+DAQZuB5HJuF2nNX7rKgrNI5ckb7n0MyNIn18J5rrAinZHdrlc\r\nIn1C/8gz2V5bGWrNYNjc9R92lJilk1b9Pf2hdrsHeSKMvsZ0BMA1dttRIltx\r\n/GHKf+mC1trFY6WQLipLe9Dx/FtHZEunpUNv1gIw3bJ86juHcAt1xYp/tV4l\r\ni9solokz+sEJD73unIvOEMAgDldslBbarnc=\r\n=qBuj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "eb0e8b4d856d7ba24f5c3d741c3b7a9151ff2f83", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-s6fOywRgNQfY0tEng7Wg3em8UJFijah1L7o0neU20cIpLd/0qk8hJd2e1X7en9BuuSK9XXT/CMs/tmV+/qRwVg==", "signatures": [{"sig": "MEUCIBW6jvXrKlSttiAM829c1yBkBHBzFzWXYSK1Spe4b1IrAiEAukbjmJWgjIAOfZARESUGhssnbufoLapKImlZCXo/zAs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqM5w//bPLfIZ2kGD+/+MGiOmFVLXf3lNBIqUtTOqEMC5dzfq/8A0mQ\r\nsyu3+44TP6Wk08VCezl7S9wv7e9PuFZ7no0mGwZjaZKEOPyLuhitxoKgYJfD\r\n0sD/i1sLS5RYCpJLb894T0k0F+RREd92RKkgIjnr7WBcMGD90k7fTVVP2yIj\r\ndUQ8ApUs9AI3sRFMJ/pA3HCCsgd0tU61pJiQkBPiA1xWuP6zS2roUsiVqdPo\r\naYno97tzxCDhGsbfxpweKQ4L91xvqKt/puoLDOGPdLxM1bh49Tw7SEsvn6fn\r\n8eK/86wy0f5KYiaAgprSS+L36vhDVFNEu6zCha9VdVZA1BvHrowlNu2yjmYi\r\nDnD6pSrDi2D7duFLhf/MbwSl/5gaUQyI+e+INU08PxzvGI3U8eGE66+G4Sae\r\nNINk6aRiRC5/4mTQVDKSZ7ASZHca14MfTFUAmzUx2OVFySlqo+BVABTRmG5O\r\nU5sbXltJdmNVxMuiI6rxwIAT465uT1gDZ6/jVhivRrWVIXjCzIDGpyP4IVeW\r\n5YlDBGH/3TmJ3g7ZHf0Mzc9fPcaDoZrqpmeBw+uD/aGo0POkdY4T/IERX3Mc\r\ncPpswBe5tUj53QNRGCACfi7UUwXrA+/NRtoeBZ9n6cXc8/n4bzuL675SaH3A\r\nHO4uAUsC8op2Xc35x6JyPQqgCBFojn0HlqM=\r\n=4GDM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "78b27a65af4b6008d08ceabed905c0077f5f86c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-d4kGzcv7r17GWoOupFd9XXkbdPalRwVXdzJQe91dIdKGY0bN4vSNJmLrb04cov8eU4Trf5I0UquC7u9d1GYSHQ==", "signatures": [{"sig": "MEUCIGfeyir3XsSTMuwo+nWq0LtUcQTMQni90TgmrQvxoQn6AiEAr5fQCnQHGSmfIGSlp4ry+bMqYXhEg0BhrszUCm8+y/A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifhz5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKSw/9HJW4OJgX66BpN+PefFRlxBXLq53M7UnYuIHh6GtSuQzD1ioB\r\ncYPwqSKwYmQ3GrF1L4nDNS21qNsMK+HwVw46iOjNcwWigLq+hCqO8raAFgn6\r\n/ieqO0d2VcoiOaqnYaE9bUjS3nYSoBeJMzofyqDbzVOn1aZSfBVUMW7MQaXZ\r\nVOGUWyf3HXy3KeRezt/Y32X9tLskHboA8YkdZXtv60JL2VKpW28fHJhnpQV+\r\n5eUJZtAJTnJeNZPtev0z3fqrO6yfHQwOTcs/Jq0HJ3gyIRovxr3+2WJsFkzK\r\nsm5My/7ow4eZQjQyhPZYFN5K1KTzrD5v2tF/vibCedPJP03YRg3VXUp1bK4T\r\ngxHkd6UMwjKsGINP5Uec+DnLSgQQGjZeniZB5rKw/i8OPRDBFyQv6wpEW/Cq\r\nq1Z6m6TS3M3OPRGbjobaJN2Yjn0OAQYaNbAQHwXKWUOofycqZyTjNdTtidcu\r\nYqJ/VC08LBPFoM19kUbsByLNfc877cne9mKbCXm6bzEQTHaAfc0Bte2MaTQs\r\nldjC/8/newCcyJYHRzMT4qTu983mLfJSOnq6snu218qZc3yTiB54dJct2Cdd\r\njVjO/6BpZzohktj9xOJOUtNkIGkdWn4xX5eUuXosuZ3qUzuW9pTov/EzovY/\r\n5SaPZuAyUQyWswIc+BuQ+migjCWXoS4nmLM=\r\n=HpZk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "53448ed34fb1a0dac3831908ccee682d9daa9989", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-TlVWLXJZM5bjEMgdCNAE0hVS/XiOnD/EfLy5mKpUGoLMVMdumIi9H9+h5oNoXiuvkpCqvE/xvTHawA9j1yE9Kg==", "signatures": [{"sig": "MEUCIHVlX168dzmnXTWUunpLE02x4wxs9t91X7pUYVeCJj2SAiEA2qDEX6w/AQUzPrnl19KYfrCwGJ9zwYfSjiepmaJ1ibY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqcJg/9HyKwbtZYR1SyExxpswlF6gzjzL8oRyokngWBKEcYLMqe8j7N\r\na0hdalUTs1fN0j1c35KfpzgGbbvIKIcq6CM//RIYNVXo9L2phq05H2iahJBm\r\nj4eet9WTX55Y2+1xECaj+Fa1J3JgISL55oSeQmBofWV4iW5hI30iTHsEMesy\r\nakGTo3hk3NC9RGdUZX0tkgB/5zHMnk0gZtic711pG+S9N+d+HfLhAzBmCM3y\r\nz8EZe4ZhNjSE4v7/QQzVn6gTSoDhcdt2qjg8SK1xL5YkBEEIuG13QUHh0RsY\r\nEHvWtBgEroPOubSPEhyEfAcwCisUBbprcogYeYn7kBpk5oZhhRtylsAAWmNt\r\nGqHSefOTHygRIJn0NQqo0SFpvYM3DwgBgO1KshfgsjepH83EC+cBbzVUzKFW\r\nNmDlatqLGeTmy16naq1VP5VTOOuijhlZ6V6gZUBnP/Qdm4iHZHJxcq7qx4en\r\nBIbl+FNTtYeAxMUP1TIykWAkSKUpqrg33Wr9y39ktaTVJ8k5Uenp9YaYpvo3\r\nW3bXo2fm3s2TPFDt62mFgbYGeuZiBJ+9EieLBS95tkEHS7lhpL/QseUsiPqA\r\neXusD+sTzS2pLteN4KomkbTlMeBhtJOWWXdbdMpN/hFc4Zf7jdM01+olxJlr\r\nypKkpQ3FZNvSlldO6G/hifO9q2ChTwSkZck=\r\n=NbAW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "34675a35b392414be9f115180a261b42f90677a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-ALNIGpUZn+GIGUDjhuk77qK0MrhpvH179eBJc8sNGhYrwgLjkqMrJVnMvnupMAIv8Mjmj+Q2OQFydT/gObxN8w==", "signatures": [{"sig": "MEUCIQCZzfTdJ7ApQAb7QIFY7R8N0Lt/kVVC4aPEoPJol8tYlwIgVcspb7hFtnrleXJEsHokQv460KZclItXNps18zTEPdY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryUA//d34g0l+X4pQSiLZl0OB0ACgbzjzSUMwzSK7F4lMGtJxiu61r\r\nYwrC6y4Fj7O0oysVtrD/KowO4v22TRQrgma5xP7ESviexG7Uz40qGL9G6d94\r\ndPcJUAI4oDb1JBDWxdD+yOAqpPXUhwlFwq/zPR5BXfJzNBNBOkE6Rn6cP6qX\r\n9NZtYbMpi1IBa9FLII1+LWwf3Z2ZKtIobkQjHpjHAEE+OMJlR6Z7k2/c1l0s\r\n2+U2zBuIziRvP1lmaadJDs2/8Rq2saPIilk/5VxR1BZgeKnDXWEKyC83gREK\r\nFY2kH9j9d2TdvvO584AAEw0Se9dTxNX/MAPtjg/8CZ5dB2fTZNGG6tOh6vt1\r\nUsycQCcX29goZD61U30Q0Vk0Sn4fSXuGvVQL+21Fgdyr+Xq0+kN4TYWlqgXM\r\nzEr82BMK8dtsip7KPX1mxaRgPoNKqJxfEESU//mv0i/0l1H/xTYbEveS8D9A\r\ntRn0vL/vm1Jj6LgyflgcoxBApb47eBuuOALSvFLrDi6XiAH9yKTW4IJgYATF\r\nDfZ2FdtHGmKwPpPp753iLsJftG4EXAsL9HEpmgq5JmwSa351QoPYaRs1nLKa\r\nwycoKL72EPC/fuVYt4/CMrpcUB7Nc0X6HsxymOclZzhsGEPbSnMOLuFR+0pL\r\nZ70C8pXZphQG8KIkDNfq4karVX0mDNu3pRY=\r\n=8J56\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ed6bbc3372d60ae66ac2e6826568444d4f1404ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-jsVb1kkB81InUxJkZ2PDWUT6QWtHUuZQqY51L8kGU+DOiw4n0o4xB73ZQonAfBXr1sK+BizeGNwO6x0YZDPBqw==", "signatures": [{"sig": "MEYCIQDC7vGLkPEuVH7vqUcjR90+pRqiohYu3An3yrH0s5b2OgIhANmZ0MWR2F33vvofp6DwatWG+KnEMoAdqqG0HdLM2ULh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0SQ/+K9x9+XEmFuC35emtLN4s9uEaH8OtLiQPhvxzKiNbYe/JBSLZ\r\nx0GdGPuon/voKBWWEuLJ0MyXz/46P+x13KivYKTTzClM8MejsFYKWLCCUN6i\r\ngqY+H2HezjU0crnQDQ6/ti2Vo5z0mheKXhJsmwArF/XvOUKN/uKr5uY+zq9F\r\n8Jz1oc6uCydU8nGRnEVlS5n9w40Az4pJRjJULYiyd51eRoTXFj3tQZV0jWqJ\r\nzMGmIxFiwDy6Awssbd5sYoVhe+TayDSZh3SSHIYw2IBJOgGe7ph4fFVniwej\r\nSbwY4FwCymc4aOvNfU2MNUFezK7V6AVti/8Ih6L7DwSqlUAlJJBmPbrHvenB\r\n2tQ6zOlKy+Eq+9Mt7nIG8eCMZPWX4Yr6qilHu7eNMHDuWc5Hc1ADx9BTVAI5\r\nXzxbiV/PbasuZulRSgBevAdaJPN0TSZ5DdTNvZfcQSZA0bjIKfbnQzkS+QJY\r\nPUyPg5ccYb+NWzSa+oT1QHz7cUV+cxnG1OKHk57i9Fd9FbMI4WsrCmSU+1y0\r\nUuC1c9o1mxVJAXKlrmAjDDMIMGNM/NQKZGV/NRE7k1jczPYQOZKen4e2ADeK\r\n+/XSBRCcSt0ziLNdBalckuEdbhlQjK1x04zIef/qr1cFtEPh2Rm4jAmBdVbt\r\n31bfruKVmxwP72M8aD/mWUvg9LaGMN/Pr0E=\r\n=Yd1a\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "244c0f649f2ca3f6fad8a8fb95d7b60edde4c631", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-57YgvzyiCf6GFDcWzGL3zKOKHkox8MJOrZnKI2shJwLoDT8q8xy+kusNVNrKiy7NIZBa7lVJ1z/xqoe6I+6DuQ==", "signatures": [{"sig": "MEUCIELUjv6vZYriXyZXUidxIiewtIgwQM4OID2sTd2JD8omAiEA+LBhrSk52ByD42yuUscOTlS+TgBKYdYMjWaYTTt+g8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRSBAAmxFB+PyWZBI6z8ZDpoQRGa3bHmLq/RCPp49OsTZcPcyo+NiD\r\n<PERSON>4yr6aGKirOmAAyyUL3UV/qu6yhWC1wfPWz/0ak7IrMb/DPpRiE/1Z\r\nPJI0XWEJoSCIM16LEUFTaK4Nj4bvTeDbEtxTbML1E9JfCL55ls9cfDNJXxY5\r\nsrk8AKolEEBqHigbXoPJVjL/7VUUaFHahrZZnNkBRh4GfU8zzJ6GgB7llOr/\r\nNf7zl9TCYKoBGmtnq/kiYXY9Canw2HDga3BU4SQaVcjDkhHy3nRc3WN0zSpZ\r\nM9t0qa/4cnLO9rF/I2Av21MEImvTvKgPKdm9YsArQEJVUZ+M/aWkHb5ysbxE\r\n2/1Xm8bf8NfaaK2SrNnMzLjHEHBAiRaBeE9CGKxnN5hcvi9BaiVhEi0lOz3z\r\nST13ceQX4oGgdKt3AIl2uBI63fMkDGcSnTVwR/Epr4e1He0B/xzXwtBwWZVr\r\n0axiponhBKwncWQcA/xvekZpA3HVmPdfkaGu6TQQUHFc1tbDEZ4DCnC10fR0\r\nMH1Avlzb5mDAtEtj2o3pjoDsuqSlEEuC0tgvh0sl4wUjd1ljgCMhyIuFbQ+U\r\nuSDNRx+qTqXuMiA53iPTe2dUAYMxilVbQ18Mvyi0ZwQKXip7GZUsr6UxEaDv\r\nT2Ay9cq/oYrwUFAxnzlBCz9NTg6HIUstPOE=\r\n=kFtE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3ae2dd8159b499f2549ac6c65c076545cf5c11a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-ZYEQBnY7ZF5r87le9q0vnsswqWjfqtrZEUWr0yrjRrpW8vRE502GcZ86PSJOdbW3QH/R9YPoJFjV9n9gWDTjfA==", "signatures": [{"sig": "MEUCIFYrPwYsSb/kTfi+B1leQ1bhBhdPmz94vYxIYafJ0OqFAiEA0WIyA9V/kPJU33pY3//4VwyGUvYEthlh6v7b3iPrFhw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZoQ//cWOSX8J7zD9Z1bV+/VOdbvz/zqgOwBXR/7aUbvo5yYA9VS6O\r\nuK4aTvShpP73OspoVGp216VHqAXUPKZqa8Ydx11GkLmRi4uQ8mdkCAa0OLUX\r\nniiqVVWjZZkGI1qj3bToSxDae7MY5J2xIVbYJb4PmqKk9u8Q3xM+ALIu9Lq7\r\nA5/iVDeL3st/jmiIjXx8IP/ehtVEd1y3eJyjgwrzQr7rL6/gUeIfSoHGnDJc\r\n1451KFl7gMaCTKWYXiokKPalkJiKD17efMuxUqoRHCWfWLBdt7UdYsaHPN+l\r\nfA0e1c0O/pD3NmGyUR2TCGZlikCBkTkC46Z0EGCyq6tnjJeTua1gBJsxnEeF\r\nmQe5CyTPFqOHoiBBE0lAhGFvP7IX1GlQYLGtGrOzOAsRlD8MA/wPRv7Ouo4m\r\nJEj2VC17f4UgaUP1HvuAbghnJSZ2yRdYgoaqzgNvk9Nx0dUXh5lbQNLNqqr5\r\nKIasX6ZvFKJuByFs/ktWhhZO5GH/tNP/l2CMxPRQZ5PauusXK22G5tY2GYpP\r\n7eL2iQfugGiK9r+0jo4zgpIwCIJrG3e6zG80JO5Pn7CLMI4KV4EIt7kS/xXi\r\nu8sG4ymmomMNP8LJQ6n4yBWqDgEFexjdgTenTe/BHj6FCj88rB1ex99lasuH\r\nmDuTsLa6jilUDrtImqiMtvcKodkouR4mG14=\r\n=ROJ2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8325ca8325b73e78a32ad307392d1aa6a0073ed0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-BIwFelqsbhN2C57q2g+xHkFnKox5QCmQSK4cNKnv/yuMMlZ4KiUp06wLwKcW3bINUpfYUzY6HaT0LWhmx/k9kw==", "signatures": [{"sig": "MEUCIFk61zRgfZLld+xYA50sRqUjqQCiFfvtlCN6Yx7PMAAaAiEAxfmq9gXLiQniTQ3Nz7vgeaIufMyiyJUlcmzaIpuxPpM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp6oQ/+N7uazzNRqbqm3RjwItsjD4J31xvPoAr/+elFubgQgCwUpJ9z\r\nBqMwl44lW37XvUdEQUoka7sdPO7BFZSrZPM0L0bRQcoe36Qx1qnYoz0fXJk2\r\n6y7xd1K+iY2OgmRKfbEAvWO2Cy5gCKKJgjiBXEaZk58K//0DB6btSyZEzDko\r\niGq29fPhrXBokF4MskYTy2Vl2fPxbTwPHG8s9zlG+/0F1jbQkn/yrGokIFks\r\noYpcxyYKdXta77rPjrRAqlC/6oBXyPwddLl1VPziBE0EPdjWTWs4E9L1wWpc\r\nlQyvbC3+biQ96z2aYPCCcHb25wTyuWaJHucz2RsDeBDkahbQ07kJCo+5RkQE\r\nzDvkhL6QfL2HpTXVCU0Xis9ruLi6tMdV2gmlzdE41zVc+DZ/qKOe4ZbIrzqL\r\nRtFdqm0G4OsEXk6f49+Yhe1+07tLlUsgyvEcTBIX+6zHwk2llMY1op8lO1Fa\r\nt+KSctQHlgT04xM0FnMbIynmLxMlK05MLUrjMEfhmqMoGg5Jrz9+ZKzMb8Ic\r\nDX26xnBZLR5eyvyj2RTYePHN5VIqB0vr0sntUO1yLJaBUiznd5i2PihvbQjN\r\ndyqfTjITYxL50lNhrjn6Vtt2+Gb1y1wH30ea48bmjVJWDc/j2pueSxBq6zus\r\nzgrtRwMHWsa4PVwMla+L9efBtbVNS+Le2fc=\r\n=D45p\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a2fbb49e063c337eba4dfdbe2f0b1e7677ec5de6", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-KAb9heuz4Uhdi4jcmNeHyon/ZUy3ZF3DzqW+GlTqFCsLO18udzhsCAgEDyuLK6wPGE+lfY286hbnEwpAU9l8Ew==", "signatures": [{"sig": "MEYCIQCgyNtZG94KrjyPDZvoocAZh/cpXl8RJQ43gkSl6NTo5QIhAKKUi5+jLRR+3T88dACBx8nVJfJcXyRgLolDThk4gFxc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLg8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoG0A//SghCmB8l4j8ylbsNM0Iq1DvgVk1hESEnCZ0nLYbrw3naaEyh\r\n6XAmNSyrB17xjQq6oumgL/oI/VAr6Ro14R38Gg7LbfioV8TykfrmSn1HsnRf\r\nbchXQacRcWZY7DCYkdsjS+htijrOmmsC3LT95vfWPhN0BfIxDEd2tymU+reH\r\nMm8H+I/VUw8sJPe/wD+2jSEyv6yVcKj+Tr84/XImnpAzThEvXKIe6csC4m2y\r\nDKCeCAxlwmvoDvq4f7ShqRsvULfCcMKgNHgZAkg2UqMUKfp1zsnmW1orF62K\r\n7A48NoZdxXAG4zt8VtnjukuOXJEyrU4mgZxgrroToBM9tGvnVDFaB8sG4ICV\r\nmQXL4F/hvLkcUspxJNRoFhlBCFOAx1PmrA8i4u4vnWIqYWW1SkwbXLGIraRy\r\nXT8ZofbFVV7/Tm/3xpksw5c3VTGqgc0uGNXflZmPzkI+myWkEr21c7koVrkM\r\nNfnl5YkD/SYSAnjsvrOJ4r0jb777LIlq7fXDuCDNBBx5unxUcMOUk2fV++RJ\r\nOyBtY66e5e8dO9GKEVjqucF8sYkS4ELl0eCzBU5OT+ToX1TUW7MW0vGaRIzd\r\nRjKkjcv6FS3D+oum7Ub6/0m2fAKqThu9pH1BA61ZnwncD5iZ/pQAgiRS+YYE\r\nLebL6YRA3GwJjf/4sEVXrZRUpW+WNsmJcmQ=\r\n=a9zZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d553404f42822c677a5c32421b0a7a1b96206fbf", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-z/oQa6tX842zgRQxjOnHMloH6CHQCr3gjxnXLyQHDshQCd3T+wHaZcS0mc5mDlWjEyRh2ts3eT7ap+G0p75SrA==", "signatures": [{"sig": "MEQCIDogZOlAc3mQkkbimcam2+YyGnlFCiAgujk08lZ28eMyAiBjUug1UY27FQvoNjaoWS4h3en9pbXcyy+tmBJTZpZliQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpwzxAAmBTZvr6WLa9yg/+cNm5cgCXfP6nawnd3M7onIGy4lo6t3+64\r\ng9B27QfsSKydSuV/NxGm3a1aHQGHtHulZc8ey+PTcsHy7gSoCbRw4bj2aTAE\r\nbKZmtu9LSo/RL1ZOSXOj6XPSRAg4KRbg+1w+kcMFroWEIKYZHaBb6jUp9XJj\r\nOJtibhUdCWyPGO50lBsTsEf24BhfhTLW1Q+wZTTR2drj+7vm9pOKDzJlf4ll\r\nDuwR2uHWEavoGWeqPGcjLCrhpcFNSZAmHjEmACm4P8zDol5GrrFidADSgLFV\r\nDQcz/yFEJUbdqfBCOZhFt7w54/7tAL0mbZzu0Yyevw5kmF3pt7fs/eozYFRl\r\n3c/Z+G3QVSwmElPxQSmCBFr4oqLA6/YGO9j3SeQXgo8/dR5bKQNWnL6US3n3\r\nONPXSBOFGek0Nkr4846YdXeTG6ZV4Cwy7mpp+C1w8MVn12h0mbg7rbgWo4Dp\r\nrgGXp/9I+ciJ0wAEh7hUn5bHVvgCX+mn8YzihKE7mWO24YB9usOg0gskLXT3\r\nkCYcGSCz/PU7TAS3gwmAZB7AR+3Ikpfnn0cdlEKs/HbejaYq6XOigQIp4qj2\r\n0yQgewc+tj/enOqX37W2qZtEvsAlJ4ozNYByJpRCc9ovfdytXGoBYMEidv7w\r\nltonvMRW1MRD8ZV6UdaW0/OUctnACvhjm+w=\r\n=+eOL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "96dd24bb2f7fdb5e655c1deda184b65e25f5a21d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-P/SRy9ga5cW/YyhN11G7oHMleGNSCK4oRnBYngxo5NBnMyDitkg7mOq9gCmtus1e9YSUVwRyrIUHXOzdP/R9Ig==", "signatures": [{"sig": "MEYCIQDi/tYNAfKnJ5R89HT2LW7QkRTHiSHls8tvhHslgY7mngIhAIpbvN3FIMzrDaHYbXzUizIXNPZ/T5q/XQOYPBRjY64m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqlfQ/9GoqfVGCy2OfjRgd3+Sjs9cEEvR73WEZW98pj4dD4XK/SN3z4\r\nWZYO9uBvnpLlcpxwoKdG3WQ0H7ztxAfHpVMi+dX3j+LFAzC7PH0KTFyrXhRt\r\n5Qq7HpqsM6yggOrQJ0rNchcyG+Hvy4JPz4HFb17rF1Ag+4jnhz0XbwUK75Pa\r\npOhntsotT/OsfV8qOlZFkBfhDas0PT0H/BiMMVXUj5JacLsNgAkS3XQmB25L\r\nsXQ1mwOJo4A99E17zU8iHF2Bw3gCfQANAfNwZaBsBncttv4ASKqfbFxWVq5b\r\nXOx8CYtm/T/S9a0DOO0e0E0K7J+8+CRWINL5SDPQOTDLcpY/t+syGse55eX1\r\nLo6mVafz91/KPoG5VbRfUMgQfTHPD+6Ddaiy1vmDkUlp5Hgk/CHTQ2CGFW5s\r\niLVZ1Qjf+AjgrP7VUHODgx8wtYxWMjFHSSojks8+tCTKzEzxXknrXuOa5xk2\r\ncEGYzjjmkoQkQr/7aUu9VyMCc2R1YYW/dusmyJXkwLkWTckSRhkEZrcmzjKR\r\n47V+riWz2/ODqMyGfu9/j4jTpyW1Rh/28KaDPzqdD7/PgVNyinqwxB3KTtlw\r\n+ikitgaOz5v7OtG3/G2078XJ2fKASSPYVOxRla/kKSVCgJkEGJ3yFFlZRQyc\r\ninXsZRFH91mKk+gBLElHx/oByNmVdC9RqgE=\r\n=eGUV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aa46444e021c8b87eba43230d43b5c4e5fd69852", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-LgjE3YVBFa+Dkie4tLcJpRhUYLkgQpygQlHqgbD/xNEKk/mYmUlCdnPluXFK7nx4+bhbhPZW21Hc0r+XKupE3g==", "signatures": [{"sig": "MEYCIQCMy+Zu54SD63MvCFykqjwKd9aSjEXhaGS/5ZBR83y+NAIhAPC2GJb2BNmK+lS1BAZK9rSMjduEUhlfBqZJwd91SIGA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ03ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoBKQ//XGVhZZqD6ZQLqrsVrklPKm+/9z8Db/rommRAYrFXanNZSZJ7\r\n7y3UFdm5E6kxcx3GQksupqefP6N/5M8UaNd3OrlK3JfgXpdwdM+c2g2SLYxO\r\npyTwihNnTQHkxR9G6cjqFNNHesGWHn/r0FAwXnUBOQnl6oupOSo2CmfGicXR\r\nwD+vP8NbOQjiNf4KcvUPjWIRgVANqdbZ50PIOjogoW2PS1M6/uUsgXC3Z4hm\r\n/YajyUHq3xmVw1yy3vd/Pez6YbnKDFhpZ9JwJR3asTY3m/FdOOqmmlxiaffr\r\nGhtwnp9fmqcMjS6gBXpXHwXbv/zd/09ASfwH8fTutx9Y/4Ku5lM7xn4HjYrx\r\nRPamiEdjN5Hf1b21BgbulbusThOFJpnTbelCOjmzRbTR8H9BuAOss55IBNoU\r\nj//1yEKqh1n7oJvHHn4T1hoIht3g5rn4ImPjFTuARrKVCKzcWMMTagl1bL5p\r\nSp1SSFB0jqyd3ho1pDesZL8/5REFvIpzcSnT1ZJcUEiqlGNtWTQvEeCeuwFE\r\nUr05V122FiJZ79NGwHqRQUW3+VbSJR51kH3r/7fKkw0rli8gfuGkLXBHzJw/\r\nBAjEj2uk81tkUoZpbYd0b6Q9HI3HBHe4Ajd6P8C8GgvrbHtiaVx5OQ6Ie9IG\r\nMUXzdrfTsLXytTcp3mSmpACj9qHaZoTrfGs=\r\n=QIr4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e1373abd266f22ca5805b3dcef761e35d0cba2fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-/xpgQx5QdVDNAkI+aQkTevHBM+Se7laI7hI94J8xUI2nVi/GAmE+JlWovHT/bUfxNOT8EFXlf8Qp9eWlxM19Yg==", "signatures": [{"sig": "MEUCIDxi0KbGft7z5M2T+Upx1OtVdArPQGBxNnrYG43YfayVAiEA+IIqPqyZtymcyVXkX9mrPD1yoKIu95abwNBHD96waI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildMvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq5nRAAjL6UQ64v0rgKGnpMtAOHFN5XPbJgxPeo9VyU5tL1bfT+rYzH\r\nL0dl5rr2tx7fWA3CiHNHd4RY77CBDzsvy7HscIXky7M8DPe7+ZWINyOJZ0ur\r\naWvVhJ37cRNsWCAc6bvfcAlz6H7dLaNrwFO0asD+lTv7nLAU/fZDaugok/I5\r\ntdxqaXg1+iNROa6Z5NiiJ4gahNmK5K9o7OjWiNOkESJ+o1NNIofroO4soShn\r\nNefyWny/XPxFwo4du0FV+8gRTRaq3WYifKppAnPt/Jr96piBgGlhWx+1zK9g\r\nO1YwXCSrr9UJBaj16fgUi4177EQ60EodSrPfDrarDIBahE3ZR7LAohw8P3eA\r\n1brPbJvMndHCfXl7w2tyXdclsNjX1YACDehEfPmVVDM3zr8sDnMHa+ApAozT\r\nNv6LRXPvyeIvAXseVHgF5tGqJnRR65Lt7u6nL4Pc54OwtsH7zK8hZCJuGuom\r\neZ3vtkwa4zPFW6mkT9bVaKZ8Yuyg4KZaW+u/z+0GdC6c+NozsXTRxVVe4ZlJ\r\n2nPdZldX2JIFUeFsq6ZMNS87k8myixac7Xg8ou0ZD7BMyLcLJynyii2Pltp+\r\n0VZ5KZh7lUQqll4rFR0tJjQpxJufP2u+k0FYOiayArWA5httL12UTRW2A8vR\r\nqxsUF2kAcjDVdaZ/3tYH4fX8HNKPnaz69Z4=\r\n=/Iv7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7fc1b7243717e8fa422f8cd43ad6fe61abd47944", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-4BAKAWPxSYhLluB5eRIt0r/oK2h6JE4ZjrPhIjjlYC07IHAfCSEo7BZnggmcfQGBL9SI71QTQIJmQDdoUPZNeA==", "signatures": [{"sig": "MEUCIBak76b6nNelSUleT6qLw2MKD8WMk+h/vX3LhQIs683sAiEAgTxL6Hc54tBwN49nVMgviGtU5RJ33IWwDmafrNdogIU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpiSA/9Eb9x1lpILQ735820VK0znfWxN+SWwfkwG4UHE0LlZIZUnP3F\r\n7O2s1Zlpl9syENfHqJdMbviqADPNIefMbPTOnxYiJ3uC4QEHE/hOIDi61uvt\r\nazyWXHXqc6wU+/IZg5Tv2ycT1mHU08vUNgvmBd5VGLzG5/YHIxIYFjVBs8hR\r\ns0lIozttbo0Sv20k/fKF3VSYE1QjKFMmFrolkpl1P/ohX9X5IhC8dRs12tK7\r\nvnF96tNsDa5zUIo1EdRJRw1DCUK/p8/c54NK0ekgWN8dXnVAkIw169mER9fG\r\nUT2gbKs3yV0jnosfAqg6SfgN8A9OhdnNErpxYXNeBmwUjN01gWIwweC+iX1J\r\noYbZ97ijMuXMOKVdwBvLiBflWlXc3Xwz5gSJg0wKNjn2TVp9w/ETD6Dme16a\r\nyMTQDD8RKP/WGchNLIkh0tK0CI/zh5M3JlS/cbXMBfXUbTVbTfQd4bgpsCya\r\nLsdNk1G8UKMU/9j/hSHmYxQ/7t9iyqp2WqmqUe6hk5SepaI//XKYlelybP1B\r\n8gvVHHVWUoNibCuUhcrzllc4yMLPBjl6ck1pSXqrEwqYKyUnIcIaIv4BvAEm\r\nghr5TZAW+0fHaWPrGavWPMn7wQLWADXNXXJKkXhNhlYzlZb6gkRPfgZCeh+G\r\n/sxEV8/WnpBl1R7FIsxvxUfNAc7ypPYg0Fk=\r\n=+T8C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fd3a1c516363189802fc9137144e4eb1a67107b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-3RPM4ur/o1v+wLEGb5RcxTAGRHn9uUELfpAy+8EzESL151v53hB6wVJvRLgPtrBRA++phMu3qBqRuHxZ+ljo2g==", "signatures": [{"sig": "MEQCIGe3o52kS5mCEqyNQC6nZCaTCcT39qQxW0VmGlIjj2vqAiAK/ujW44lwNvr5MdrBNhEcI1IWAOqjYNh9R1omV4k2hA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile1rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNDg//f+MlXKsaWP50fZkmCTyF29yF+qUTkiMOj9Og3NVZ7GDDMGzs\r\nhzpD6WNwQf0zaKsxBnFLA/sRnWBmBU/P3/6IwUdQ6/6+uOedNWAsi/bKhlNd\r\nGDsZhl5yJjRm0FrKlZOGT1ib1TFboptaTQzSCPwXUXu9ZXPqudlZgGy+AGMp\r\nBHF7zBGjq+VAGTmuH04apQcv8f0RgEqqM9pNk2KTbRhObOektr1g4RFnX4rW\r\nAJ+PYQFrpwy2onJBLQXx4O2HDy9nS1roQxwfaS9iXBfRhEOalczRKg5GkLYl\r\n0bY3KgeZ/KXZ4ezIgzsKHefYi//TscsRV/E+BWBjrpiVfw6uAfd0XW+Qtgrm\r\nukWw/Ov33xrNRjh85xQ1xEkZERsUqzrlm4NaggE7HgFafutglvsDkZLSRwq7\r\nMAuYdbyUShukWA3h3+WJxw1+QuvrhhfP+MYLxHoc9jc8JJzAKleiG58vB//L\r\nrX5jPGbEueFUppLoM6BRLREqf3NooSVtqhXdYiysiW8rVusj52rxcGIBuq6J\r\nxH5FNeLzsH2ucJVfJkheZzktOto4eazNnXZL2N4NhlnoOtVK89qoNA0TcHFS\r\neU9JV0wPSkASN+i/XCZc6QaagAfhpEePqDxaCebQcdjTjDXq188Y8RUC2Jc1\r\nuuEimGMpADtRHVDyYXZojWak2yKo0XylfzY=\r\n=yiJh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2c0ddc65c97a202a0ed82984294132e508104510", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-jGuaepSaakSV7Br2xFPqNPPhWcmmbzTwkEUfPcX/k2LZ+jcYD8bhoBXLhN7I7hseZOkTt3B+NITaS9SgpMEK2Q==", "signatures": [{"sig": "MEUCIQDQUZ0Ozn+Uy3zGqFTb3n3Gp3lYpj/Zxg3NXp+WPnrNoQIgeLY504/W3aLdWoFee2hT7xU7uox6OsvTt4JuyeGEF5E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3WwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr08g//cvA4kBANqZafPA5V1WyS39ILepbM+ZVuaMGBpHkA3bImOlOn\r\nxSdsgRutVN5JJ5ItMNXqHUtP/Q9V4h79gDPOlf21Iq2dJOVZqdaNxgwEAZEG\r\nbNIgsmKrmUDllqaDVpTie2jYPUghaeCcVeeP7+wA3bSXONPESiw6HqVHTz/D\r\nK1W+6XeVOWSqOj7NulPU9aJRuHQrBcFXAnhypfpcE74HtWZ0xFKMktDAIA35\r\n63g1zuSP6xJKs7So7ybTeISEAh+zKmdFcA1vrbT+V/T3mp8LmohCE4bOArwI\r\nE0JsrK4n9EoHEeJy7U9mv+yO04Q/B8IutKm5usD4kHwg+DlFnMHSv5VW7x6C\r\n5jQkyzEeoBNso4AyWgUa/7GyFI2Ci7fJsPiyfdk8u6hmb8MNX4VT9L37UQ1c\r\nwomOAyLORLgAV7OLvurcR65oEZA+FhR4ZRw08w+OuDIlhXCdJtDxm64R4+ua\r\nffJVxi+pTlKA3J869+t5EsiL/lUiA5h0q1y2m3UEaIujvL9fwIOhElZg7xX0\r\njrVxb0/FfKg2zxU2jUpfZGVw7oMyjm151ct4ZYgDNBbzdLsRiu6qXTm2HjKs\r\nuFE9MX/I+35in+URd6X1qEyZLXJxWp9N3iVH8eMQXg1iOunywZhK/O0XW5pg\r\nVgfz81uKSM8p6Rj52ZEKLWaW7MpldzBs2r4=\r\n=h0WO\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "26ee145c83d8c4d4e87f00789d218453aaa7a7e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-jFYTl/cgHH+FrXrm+frch1KQy/6B4hJhmml/MjGaSw+504Sexszz8gFKrK/AO10+slW19/OCqi7614A3W+IZLg==", "signatures": [{"sig": "MEUCIQDjCGjxHKsbFEk1h9SI0qyiXpcDGEWWNYWRsZYibLidsgIgG4X0CB9Pnm1lAYWad5URfJzEv8QQkCRHT8W0pfJpmIg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqctRAAkNe9mdU/CUJ61yc6ttu9L1OetJQC4JfSLVIQ5DIuhI9F8kJf\r\n51m4C/t+6m6yPODa4JZS2Iac5H8gOJiMB146nTaBxEuMhMEJ2G5RtM3wuXFa\r\nkXjzzm8Pcbv6lqKBH8+UA7dBSk878ARsZEBa9xc1Mlcjbf+qWn6ApzAV7Q7D\r\nlqnkeNE1Rgdbzgo7wMpyyZEV408RQvSTF8fLBvXRiqEqpGZXM3q6rlHXbzCM\r\nApPMQp6C5jr09YznWTBAKNGabiBOc+uLMyhjE30KmSiimoxaYw8LNm3tGKaP\r\nK9AUcGRHU1l1xJiAfIBcMnBcA4+/IVtS0H+jEY4Z5Tb0UWBiBlc67Rcdcc8q\r\nUMF/wlDt8lQmsw2GJnX4T7iVAhdBENmAYj8uTKOxywn5AYn6xS/65hkTMbEE\r\nsDAVNOMnDOioCvXfFwoyRDGQ1h6m6jJpbwShTlt8nGfUlKSeN1HvLwU+DJmJ\r\njIdHGeuFKMOIDuvXoYJRTnoMot4cGXJpD2/RWwmTsEjUSog4NhSW6ZNkIS9Y\r\nmFHFxbiQkWcDFX1icVXX7bOwC6uhBYogTPzEZrFAK0VjL06lUOw/JyCe6SE6\r\nDOeV7XfOZiEbgEYjsrR/pWR3bIJX/eNTNjTrGWxVxDfBfVHgWwl46GiTsbSR\r\nBeTPlebb4CeCytsMo+Swasqullv5vT3Fzhw=\r\n=Tuka\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "15f88742a994961685518e8131184de39367a387", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-cUzFP7/zlYCXwrUfOEPcsWnOBA9v6BOL4hH5b+ITXRNv5LkXwZYdwcKusWrhrDXncqoZtfFvNIVcGIN4PDJaEg==", "signatures": [{"sig": "MEUCIE3gXmQqNyaZY4KCUUqwKlGvcxGProuGdrhi7l5VE5kPAiEAjjF0Ny8f5YBSEuhLzwnyj/36fjok8x9pqejG3I6UeB0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHbvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpf9w//et/nCD9gqTn2/IOw1PDQZtm9SkVx1vKi94eZaOsjHcTyfZMk\r\nHG18c36CPQz+EPURCYfsiCrCA2XkxSDZ8iVwl04MvinKH9RSbDhL8T55UvGZ\r\nMsql21CWl0WvyY/D9SkuGx22Okp5cXmsPpmycwcASWPh1vTVWVRsu1sqlfec\r\n+NHtcj/X2vmX3ElQPXen660vz6y2TaCEj9O97dM9dBvmV5m/Z+gZ1V9G4fxe\r\njjz+i8F9L7wOwuGxduIFBpw1DMyprbJ55kuUiePLncZrz3b+VzZSNSKtiMCf\r\n3moIAe1MS8OAPfhHJGaa416mckpVjG7DR7EPhkk6QgQHCyfR+bfQExF12sbx\r\nFq/BfHdU1hYBKAKE67ZZms0F0sD94M+r4Cz8nXCiyRgefxMdiveXVGTry3fQ\r\n67MM2LKWg4/d9xj/N8MXKLCM0YRp1Pl6cNVNnZwiGfUy6coPV35Bfmarl/xw\r\nYfQeltTMsXGP7NzcTUVvVdfXnulCWM+jrbp5X1KMfLDm0jxBtr+NrxucXRXI\r\n70vigX4hahdo/6YmmX9YK30bozZjwTwPLyIpc2HNjJnLGPMAZKigBQQoOnTr\r\nZy4/zYfIanODFMT0oL1Cr3V0UPYmfxLOuZRegzBWo2DqGg3Ji7DTSy857+Ex\r\nwSQChrEA3Wy5m0Nx6orpnRk2FNEFA7CqKok=\r\n=ofWn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "33deb96249b839fe0c321ce95fac7cf3ab28510c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-Yiqp9wJOgFYwsXBPg3IQjqdzGyXepkGd//AGD+sEyGAo/1wmt8BZ/h0/X4vl4LXHy5aamikNqywrAh7EeP93iw==", "signatures": [{"sig": "MEYCIQDlctaMbr9Mz+AsxB84uGrX27yFANuSnn/As5WTJiANRgIhAKO7fUAvGQZwWp1DSIwdqU7J7dpGJhI34zu3H8e3tpTT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqSCQ//ff8da9gyFsUk+ar2YP5s3aNZ9xj7OBr0A0HE/eRCLk0HS9NH\r\nQjid3rUQgo9SPVeNyCilSEBCopcN9IErxhyI1R/CWBPtXe3aCKIz3z4bsp5y\r\nMUcJTdH4Q8CnJMMfgUWNKwoexnkHRwp2EUEI73Ds6LFOu4RUg+FHJ83KQqOd\r\nTTY69zVQ0bavLnNeBi1xinMqncB9PNVSDViDPs78iX9jLIJP+5HtBFhl0pGt\r\nOq94eyN5wtJRJ3EO/txdSGB8ezEPyWzz2T54lQbvNKJR08jizme+ruMzU31E\r\noh1+0/boOy2KLC1u5RkKRh79ESuQUGRRbEFMg0CdvDiq6ighVC2dQLqnXf0F\r\nLY+tN96w2IOx4Lf6V3G4n1zOEp6rEfClw+r8ZdYW+5w7OBqJGgSfqpdwXWyv\r\nrT2/3zbB9KZg8Ut4AbV3JBQJIvDXcLpXEmzopzxhZ6DhMTn9vDUifo8bVs2W\r\nOzYbs7da9joVJGrUwbKl+OgQzrEKOzd6c0SJuNfMfU3AouHK9DaaNQkCajtq\r\nQg2scsLyJf5bW6pe0unDybYGBF4Vx4V4J9+KkDvV4nEbsVhsiZ8VfLCi8I84\r\ngFPWLSvVyrCzpohPfvCEn7WPUsDcoFpYlUNzxkSIoeOpkc1xxIWIh5Xq5tUn\r\nlH3wis9bhDiWKHuJ182pV6Q76a3oVK7eKls=\r\n=swBN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b71cdca2c8526d02165b8c47de4762625d0f9a82", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-fKN9ZbcSbsAXguK+MShOB50mT2K/p2ofOyGhcLF2RXQsZvHllTpYWleMuBT+3mXGJMRbxHyueIcSx2YfPpPjIQ==", "signatures": [{"sig": "MEUCIAnkCh2dcgKuA5AWi+8zHE4d4EdEAw1IYfslYZ9ukuMIAiEA1A4G57dUmyvvwSVBlVykvChUl5N15WpcFpGhlA4gZg0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOLg//fO0DGX3ihJ6F8uBsuVCeUXS5/I5OUyqNoRFGvJ9QY5++BOWs\r\nbXUrGBnPKLP7T7bDYh+LoQEJzxemjJx2INO+ziaoqj3XDB9MoqneKgKwvczC\r\nN9evAo2e+HXeV2zZOO4fe8+KIDb1GnUE1fBlWW2TdttpWyOq5gi8Gh6RRubC\r\nDuRpL4saRfir78vRGlMyVCSUnJuc5frr/MbHdH9QPGpAeH6k526Chg43t9Xx\r\nRwlTU01kdLezKTQNQpP7W8meyw/vBdqVzciEDTKDHF/HxYgiW0T8H/8gN4a2\r\nheQcBsC4OR6kQfMESCwdh8aOfz8FicL2gEhRGps2DN6nzb8/g/085WA6k/yg\r\n64EEJF03hns7az1RrRXx47gG47m8jhGCvYWcW1Pbqv/MZUivjoB502MDvmYv\r\nGacdUSjNB4OngqHcXjr/jn3JhE/gblh9ARUh6pjOGOy4JawBWFuvrGiVCZ8r\r\nOI/zzjo3rKF9/MvRTXYxqoRieujMFQcaGVgfBUTOYsZgD9XhknkVVGFTsmxW\r\nRy3TUSyoB8P72L0XNltnr0b0vVMtg/1zrxLVj5oJ8QJzE3APIpnQUOXSgvip\r\n+B4Fg6Dv6oMcm0qmxcg9C+rD/xJxcwcTv2SmuJkRymZEpvNaOWCAD9D8rcvz\r\nKGAH6RTc+B8UQ8Je5pGyzGpE7QSFoqDR774=\r\n=ntYx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e74d654ecea7b882fabb429ed51905351d76e7f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-jlGDJZU7tH5oAKvqGQQyY5VIC1kRTg+YgpJPyGJlP+gIljSLXvR5dUx9wLgmtBg+UX9Z/21yywZ/SnrcMs5wXQ==", "signatures": [{"sig": "MEYCIQD1zMV/txOV3La6NT5vBaK/gB2I+vw0vrlrj71GmbTZIAIhAMV94Upazry7jHvME2hoijqMPfHGxQdLAOl189sXFFG2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0H7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpBpQ//dQMSdOAl+2Je/QVjNPVY8wMHR+V+vkGSnqfB3T0KVSU7sc51\r\nFd97WU3kSFxjHIIwqK9h+AQ2AzOMetfZNGfE/yZOcubdAyMfvy8vWsUDe6b9\r\n1o/pvXQDCG4uzs1P2xoOFD8MWQbhmSAuoW7XKzlOtt9RPbIcDZyfSQJjT2jt\r\niRxZTBxBDgiON+sXynEZdEU9NwAHgxe+/YWMeLou0WFW+6e3GfqZDyMna5ye\r\nOPIdUcKnoCBpaW/mUGamrvk3IVzEuhZKTAu6wXCOu2C52jUZHYx6L+i7gGmc\r\nC0JJNC66pH78cvZlrUyq20/PGQfngtFVcg5FIuamyyHS5evaymY54KAf/lP0\r\n894JeZwJeA4aQvvNHNjMgzsuAw/4hFxf6229C6stSIS71/qWKUGFnzrq79Nm\r\nDOzOoH0d5avg/T68pF0TixzOTcfThqqf2JrWGplRtU4+AkuEHFaCu0fMmqov\r\nhmadp8b0qQObcBC0vBi28+YUAd+7W+fZ7y96erPfztY6W7xKIQ7EamUf764F\r\nYkDJYWTusB3Hk76lwwU1z5qs/F7hOpmmo/ZDSMH9+5ajCI05lX/oMrX6jV9a\r\nhEA0ps2EI7Cd4O55hkz+GuLZsxKugHknZTDw9XrL/vLCzLLYx7F/dLsnkIAM\r\n3jOalCASWKXfv2BV7+uwbJe7OpF+ycEbQf8=\r\n=bNCg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6a7533abaf0ada9fbc0556c895f599b87f831c79", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-Gyrj3zbKWRMn3wVKKkDHIkt43++0kw90Gc44P5FlrZezynJg4p45cQQAZyShplEPugLiuwdG1Q0uoA1buFTJSQ==", "signatures": [{"sig": "MEQCIE1gQVsj8NCDcOuSsODNnHaEscG/0UOh79bM0OooeeM0AiAN1jFSbsQqfQneZJ+d8tP0qnCSXm/2pQ3DmszeFqh5pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq4+g/+JPtzBG3N4aoVEQrqkr9awBI4w8m0o/ruTLDQBQb3LdneBypn\r\nRLfWkeEjYYT9jYQlIfc5y/cM754lMq/kN+4giLESf7gNkwZnsHzfMntvpgU+\r\nrdfVvY7b+aMrjY2H8uhfRKWDMUsQcVanGP80AG9hYhllkrRD1lIZLjLktS0W\r\nSrfJuxaxNEqIKLj9LPS1tfbvq0vVk+PDIv28827sl5FbdSz7HXVKPlp4mtUx\r\nu63pcUgTB/s/wyz0m0c85n4Hu94k+1viHhOJYgNG47OSEQhSQERk78JzBG9e\r\nd8ZuOF9KKc4fweMGVm7hE3gUu/pzJAnIiitQbGVnhbBDZmTE72gB7SITaeVl\r\n6aEyZc5wBqd2ytFS5Fi+I3Dx/nmK5TGCSSPxKcGKHmzXwoTv/RpPMSbQD0Sb\r\nysI/obXuohAFsbn24xiBjWrMfqqdjnQjBnuRv9R1tqNLmOhwN6rjnI8F/IxQ\r\ni0yocINRHSduf45FPBE4IhCQwWv1Cjmkb2EeZjeFLTNSMaadi7PZDQXSWtPA\r\nWNQR5jB1ZK0lOAgX4Hb1zeRUftk0gGV9vkAu+11FsPQY9sZ/A0XmYAs0ejIw\r\nBmbR3A9PEUYL7+axs1U6awuFVky3CjhQpeCy6IVZY7Xh1Yi6mhrhK03YxDE5\r\n7cyJcHjk6/XAHz+p6oVRc+2QQG7I4DdvyYs=\r\n=PdA3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8a02db11a37cfb1e3d863bafcdf345814f008492", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-3Pba3A9di5zGzPDBBqSdhome3Qnj0FSoE6x9Oqd0sJwVFztD2ggevBKaS7hSAAWSeLtM/8PenBoHe7WqjOiSIQ==", "signatures": [{"sig": "MEUCIQC8MqUY49F+RiZkyRUEWRypqY04VrVEmCpTxlzEPKPa5gIgI0RSLXFylUHqjkqmO1gljuyko5HAW/qHV7vCwcc+v/8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmprJg/+NQK1v9wKZBYNIrBf8DIMbn9yafq8M7pi4Zy4GyFN3abT2oX9\r\n3BzE7LV510LbZ7q1n/l69exnOdJfH7+ZpTDuZ7Y3WPCAOQQ0dkoH3ymmXtX2\r\n21g2HojRTj+apkcYsSEVwM2iQvxSAnCMJoeTU7XJs8lPMfBoxYa/1JP0B8HK\r\nI0eRUj15W2eaiVrzBoLQtmgs7nLUBeLg2XqN0EgpQP5z+fhdDwtRdDXnej7V\r\nV6FIbmYur/mA4BLvdDEqyghjnkz4+9cis2G8lQbK10Kp5L1vwF3Wk4T9sEK0\r\nk3K11SFG+1FLD8M3WI0CgUa3OeQmo6zQdehgvIcv9r2tQpu1lmyRVMFBsHuE\r\nvRnSgNJL2/6UvvxivE9j+fmvkZ2s4Ae5okQXfDV2P6wXxKiBz+IqYPwkwhYl\r\nY1mCdWWdzFvfHbxYH9CaIFRjUq0FJwo81JbWt3D5zTg1X0hhvrGB8J1gT4jX\r\n4+0/Ax3AIrznxabW7EYiyuBDNMsl6IhPr/xUebrAsMqN1C5DGxia5MRrLPD0\r\njuHxitNw7h44leinEXWQFUMLEVJX7JGDql3XqaaJ6EIppNfJm3UFhJqj+Z7C\r\n4NnwKNfgDqkV1i0hAQks63w3BnptmL7KdQ3d1VpA1YWoffli0AEgiGUASfSf\r\nSI9J5gCbRcbmdSYhntaMt864kls0HvWAA3I=\r\n=6VQW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "902ac6e13531cb3eadffe4296a9e0a2b8f9abc51", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-krpxxDpwuV1d6tNsxXks7189rwk3/+0zSUHzzuakALtPiMNW7iwJlBGBUTvhAfsf754dvFppCAEfZq4XcQnrrQ==", "signatures": [{"sig": "MEUCIHOJxzFn15DuDWbliTmx+lUch5xomVgk6LqTR3I+aEw+AiEAlgt8TzApbuSKtDj1/QxSPy2lzA7wt0enDDEmK0/3XHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp4fA/+Nt3ba1ch16PLoIzKrRZQ0mjFquA+/EGkH9fVt4tWf8h/1WYi\r\ng6cWPVU0MgJwsm+M1ygTMSUqDrtU73DxvewdUdAnfSvpRT77vevm5/SSEp48\r\nEVN0LUwXqENx1/72qFJb8WigohIYvh9+qauQBfg3ROzPOyIMPDPbHEK4/Vmh\r\nsz7ycFDWw6Vq5O+hsD2RWvdOL14GxlCWwtafhP9KEMsPTtcP+PF6CS77pFD/\r\n0plRtKdRHgNdaxiD/H8dsjDJ8mIQm2CcEzn8rGQ/M2jJ/VUj8lS+9xcgn+6x\r\nRaP6Mawj1R0/GrTRLPF3WBQsCaWh/RX6FiWgac1rWlP0QLcGXE218KJcTi4X\r\nF5mmxEoVVJsYcey33ZhT0HGm2p7wLfR/Jd9h8NCEuvqRg4keksnqCEr2cx8f\r\nhZCi6VTq6QwwCE2grAMfA1GeZ4MsGJg2reT6fU42wwdbOorsnvpTm7vFFKkK\r\n6xdP9aXdJETYoZ435NOypjtzUGjya/eSJTjsAsCTm73kliCRPM4Ct+JvqIpu\r\nTNkl+INapWs3LKUYX+iZX4Je8ZmWKJe56XtZyeaBgd1PVKBxrF2AMNNhAwuz\r\n5j0qmfAjWCA3MYGeY21PXpJLqMDPyFNlFwhS6qfF2Lu8DtzP2rCXWddILV6V\r\ntb27ZX/DhoWIcJfAZiIG6IDxBf0Mt/cgnY8=\r\n=owP9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3bffec4f4c46104b9830a44434814a181a5f94db", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-T4r5TTgmh1r96aqP+eOlf0IEXM8VUEnFeafj8O+y/UbDLuQ064AFfgCQBscQiEvo14usMPXcH+82QXLApLgaCA==", "signatures": [{"sig": "MEYCIQC1aF7T7SnlTLIz3Iy+Vm8xDJT/JBFrTDI96D3I6PQBFQIhAK6oFkuxi0Cm0AkFBxWZZVMiPBy6VQNRXW34Vbv+gb+u", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11052, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmonbQ//YTgXxkSo2q2TiZ8ib6xm/kQ5XDIJcYTUtncD/SZSzFdjM/Jq\r\npStaVDTd/6ETlvVA2L9vVm2O/Np8dsGLx5osqmfA/F8LFoW/yOniExT9i9KL\r\nppCoiO06/NP6mDJ77h0hpM+QcPuFKzsW/t/55/KRrGWxkWCv2tLMgMUPyk7A\r\ngxLMbMmaUKfpZJDWfkatWFp5KRHSe5xR8LpfKJ29/ePl8TNlw3aB4eaSLV35\r\nDosyuloWPRDlhFxko+QmkGtGIF+HfvrlIY+kJkZMGaUJGhnIIg4d03EQ9I31\r\n0dxLDjjmS3yde09uBXN/fR5f8ygYClRXmJ0LlYIayVYDY0AuEmhlPFPaia6u\r\nPWfDGeN8mUd+4Drzw2UfDs5eWRsEq+WbVidH1KUYnCAxHeVxlc0qL2EWpJ4s\r\n7j15Ablmba/Ut8j1heyBWWN2VkLxxtxwN7T11Jc/FFqmidGFQ4a7wZyjM19O\r\nn8xbDmKQzFi0DDULu4tAGKhqyhfM1W2oNQ3vh1yqxwjIHhnmwQqYjcHfuIIC\r\nFJN26LEsgOGya/8KTIWZvb0gb3kQic4MuR5fpu9yMzuxRXRdn8ko2L26sRak\r\n7zdfbuvEPZ7iYO9r7e/PwOdwTAMyj/1cNGw2p2vxYAM/j/Ff1vmgQBW4P7LA\r\nIDQ4c7jlnMP3IcnAom3LIhS1LdgafXfSuLc=\r\n=9TRl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7cb42dd31b64c4ac4d5f385eccfbf9da0697e723", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-kdtL9kLxXc8Ik5lXyhbmnkPvFvO4CO4X7Sik63OjAHiItIqWxzwSOIWqyiuyhjnwaotBxfn6FOAgbs77Iwn7EA==", "signatures": [{"sig": "MEUCIAX+Hjyz/2NYAah5HTqnrOfKZ1cDPJHY6DsJZ4J5oM5gAiEAjvlDFw5wqDnGEFqUTonf9aMzKG+uwxsBPr0GUQ+iB0U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaY1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo8WQ/+M0xRLT1+9prFjk7KCPKxSlB1NANtAmXq8fDtcnCH6s+LWFfl\r\nL58By8JzmEzkb6MLDlT+SDlAZkeHlz0valMbRTSXB5v2Kab+pQaUSihqkqyG\r\nw/O5CEt9EicfWxyOGQKjzIhkFtbmLy2qDpikT+tv8eU/ZudEoHbgwKgzXbJi\r\nAJ+7ZxuxmAWAScz9yEje30NilS6ZHCnh7svMVjSasecf1seRZJAG2F6HQq7m\r\nTx36qGULRsQAh9jKaWklUV8pPr3fKKtP0DZ0R/VR8G0zH/y3gFhdwYjDmMAc\r\nkR1hgwKzYl735WyPK5fyv3yK3X/oEe0AClIcadE44BbRgY5x5N03q8bRHGl8\r\n4DHWYFELGR7aBKZQv3akB81x7qMNbL91umy1QUJeDeVQJghRHasn/y+OQ20g\r\nAoEp5wOETVrzrbe/X8c+h8bGmbGyIbJ865IifFFNNQlNPNaHPr3LwDNniv6d\r\nBgBvfxR0Zrh9SCob/Okryk47Un1Q+VJ+bebVSGKNzaoRthVTUUtxv/J9S7uE\r\naUNEIAZOtB1urlSPzLo788sn2lVcMocgVHBLL8wrPkeyME/QbXLz6bBDnHDG\r\nGQIbdgh8Z/6W4euWyuP48CQHp5LMulxpM/v6NhLkC6FpjdcPGJHtDG5lisJE\r\niUO3XPS+OPJpNQL7r86GguI3hp+SCMH4Jnk=\r\n=TN4G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "93678de3834944f2f4d3604ec9207c152c3ea4ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-zvg/gEmz8ifg2MvaxT6nY1pbFjOt9XKaj64VZRxULpKFAn3vRH9kFw+gx7AQyLLGMJBsTEvWffbEICAZcQY+ng==", "signatures": [{"sig": "MEQCIDyWHRDNxYDvofc50awImeD8HGGHL7428I9FpOptIk47AiB0b+o6cn5CwwpYL8zTXwEsVwTsGOuk7kVwCDnr1S6qSg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdJA/9Fbj0XK5xDBkbaLdRJSTKgGA/n/raG9kAA0U2k/CFIbhHCXDo\r\nYytb1pzf504TiHRHFL5MyFrDw/+6ORF5yflHQoRlmo+Rc6Mdtg2BB0lj2bes\r\nWOmavx9cMG8gibgjhTU80OxlKTqTtWTEumTowHeD5kkX3+TGhlSf+P6ET69n\r\nrHOhsfmfkajo7pkSx6mQOfaBUivex4x94iS+zMYXzoK7FobtuAo7cNsAEpp3\r\nq6ntfGRGUzfz31UZC9zaKT4xtehxZfezdXMr9OSXjeEZ0tQ5wxF277o14GNw\r\nqaIzR1gctOFsGL4CFeYAgeE0mNsg8GyfC5KfANyY49jgzAb7M2GDy5mZSKlz\r\n+WY6JP/mMfljWUjXhocVADCSmg/ic+jjTbwp0pC1rx4eOei8iFxk5gRU4Fdw\r\nCOloagXNxJvQwnpnPMI9thRrY9H9cl+jIE7eyJduRQA8vJkSr4/wvbmySIJ1\r\ngzk8PYz3YOWfAytaUj3nkdvgpoe/PubHjbrqSu1UWgZMvvO7YN/ZwDJuBaRc\r\nJApRA55H2TcieFIQCcGtGvdFt6hDWhid9xCPaNX9IUY/qm4IjXI/xukg1z08\r\noNLgzys7simYxo7kUh8g3PqEnu4v8fpWumV5QmtWDLO781ye7C3d26xgpYVT\r\n3IWNVpOIjH6bOnTSS/emfIgi4zfSK6f4Ne0=\r\n=U3LN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "54ac386327b7fe263b3c54b265a489b05b2fcc98", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-vuHinz22AUTH411nD+0yrO/0Bc8bkvFoZnX0WACtePr5hdDGEocbfqnspZai5qrapUDn++6FDVtW24Qtl1WUVA==", "signatures": [{"sig": "MEQCIGGPSMz5JbJJPPsa57uAReVRBWUPZK9ggZpHHBoZKjFtAiACzo2Elsrl8vIii3ejCHvXFEkJbuc4JJklsK1twcKsNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtORAAilMeZqcitpyuTUffs5YRNg0VZsCct3K/ix7VlXbRnAi5uh5/\r\naBrbDFJf2YmCGtBuj5Roa4S+cJVuPlNT1J+i/CkFVWh2smw1nMJ0hZ7pJhV6\r\nxmvbE517J8aUPX55a+9lfu8mBkO1GXPlcj2Xm+uoKEC1Eo5vEq99qKWFmK17\r\n1E191hey+YLJOJeHf9dyFfKucPetQ7G7PuDRjAqTQOS+OT+6C+MUBjtcXCHo\r\nUJZ6N395VQBPYxjsam2/nrEs2mnYbiltt1WNsBZI2SDvM+sjevpjL6M7h0xf\r\nA+ILUBauqzzKt0XnTXvOvMFyhpnO8z8/k6f/64QdMmfpO9brQV8fWp/jP798\r\n663md/v3ZyJaxcSL+2ugwkKVckZjlDfr4RaFUcTQCbRVFaYxsfkI3/C88Yn6\r\niYhvkEAJnxjt3/ixs7UGpGM1gseyyttCHB9mGiK07iXXDBamz1Lc/+f5e9JD\r\nBV3JVV6aOmJNdO6ABuukjd7Uje7QwB/p8JJKyeCv+lwH7FujBqAJCYXNY+/g\r\n7FAIZrrbB6qvL8jrWn7e9ScvPRyTWAhFpxRERKsA3JXeZElWj2MzxTGXxdzs\r\nHoeLYv47HCQAJQAE8RUjfkejTuwTDAlQNRHrMJK8z6FO+Gz2mVY1ea7+7l8+\r\nC4XVHuuF6YQeguXIIl0W3sujehbrP6QCxbo=\r\n=736X\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "410cf238e225c8cece4b7b19380b4944d6368851", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-dE9d3GX2+m3riQEmbSjpujw2u0UENIZfaH11fk5jqPFvpvUyvyLkkD+wNKYSuQo15QksEsL4WQgE4R6f87Mssw==", "signatures": [{"sig": "MEUCIEr1Nvdlx3+EoeYCbLHjEHgjc7Avk2vURg+NcLTTRcXvAiEAn3bwEVIbcCzTXZrjSHAg3aFz5P/KQOeeB+yecTeuBtY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XF2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoRGw/+PQUQ9zTWz4dRs6DPX3frYKnjDF9OTiYRNO7RrXYzdSonYgDd\r\nbnKmXn2c14PQ5AJUSz6dRhubyttyNyvuyexPaZxkadaFmn8NJsvjTK5zSJoA\r\n79N7znHVTEeT7lR3RvMyw4o88rvGGnqepgDF3UILOc5bzj6Jwf0cYsUsRVTU\r\npTL8kBU4pAhBdzUxxWshdVxu+BYl4RPeEa1v7O2X8SZsCQ1lYycE1Xxk4K7m\r\nazj3XN4Plhg+E95CAkaGMu6txsVwriL9zYYRdj3tzt9yLg/p/xdo7DHNPl02\r\nB0SNvj4LmLYBFZ9K6Bl67A3WYqT0QJUGsgNDg5/RStlDBSyRxmm/JiHrlsmu\r\nhu17uOrIAoCExgBx7spQxHgFBL6Zqad/3n0CyoAll58ACz9VDZleHaXHRhLA\r\n2pm5hJzKhylclo49V5JoSsUkOWBwEpR937cpU+OuvW8k9TsMfk0fl0vUVROz\r\nvRvGeR2LHYXNJDGUgumgNJxXS0JEzhJzdDEbsyIfXu0FqUzrQxe07ZC/V9RX\r\nJWR0D+VC6iAltKrDAYMoUcvA8HvJmIttl/EdCjuNAp9gHeCtScKD6QRQVhpR\r\nUzrdu5Z0LabC2M9pvMKCXYPe12khKo5vnOSYNJXVCTvHRHashfjvQjwUBjBD\r\nKcUu9wO7EjC0zvyt8hZCI3LaBJsxrfFfKkw=\r\n=UcrY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "adc1461f48a987b41ec266ffc36e8b4c13fab154", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-rkvPkSr1ZVAWFNhCHWyX5/6MbyQWjVYNeO4Bwi/P9An2w9AVnagrh89ma/fBfHL3kM+7/DAAks6kzfE6lr2u7Q==", "signatures": [{"sig": "MEUCIQDIdKbX5v/2Sm0t4SJFvG5W3Lu4QWvqOkqNJ6yxfqFfZgIgQ+Pj0AZonMMa7WgCXMn9adgI5tnNCv5x2fJYPgtpLow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp+9g/6Aulk34sLIWpICH29t0zorb2s5sfmYc/RxBAZS+8B6LjgEKb3\r\naGLA3t1IjjPV0SKUIuTO3olIj0b+0RndFOLHW4ATexNOzh+sk8mkuda/Y4fS\r\ndHnqn8XTjE/3K2xLAwHFw+FAbkInHqiKF3GT23X01xkuBcEvUvLG/ZOnZze9\r\nGYuF7B8rg+P+msKg31kZMv405YlgGH1XcEPQzVGawMaDyOUsfeRX49vANnUL\r\nwc2cp2gPvX4iWnw8sncqwma/2bTiRtlb2MFFBFB707wd+dg6tJzg6LlZU5zP\r\nSnh8iFT6yN4sVRz8rWVihYJT6lwpBzWUMg7iKacchLb2L3dQwUtsgjOrgwb1\r\nEpl3kA8aJcH2lWwhmSYKbsDhcBWn2sGtv61HrUUOpprVrDU3W36tsdOBVI1m\r\nLqGdZMySRGGMk9OjDoa+bVTmwHqTVkCpZbpEEVScTa18Df0EDkEbaoZ2aBGv\r\n4pqk6CXhRMru5hP6GZds8ApGhFr6I6WQXo2oFtMQO76rhCC6vmyjYGpkh05y\r\nXEjFTGO2WnRWWqdpM6otb8wlRytDnLqy5wbBktCqSJnA2bcefQVtFMIo4eo+\r\npuMg0GW9a46eCT5E7TSZ7/rH/k1YrsLG91NnPs5EnEqf8pcxEqVcLeTw+EAe\r\nm8lrlmEpon0XFZke//RmPA1lRL4Dedm29/g=\r\n=6sz9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90d618e18d947d7ea59ec758c8eaf4dffc2a3948", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-etz72KmEU+6j1Ehgzri0KLq7GhW8o7ViUpjCyM13NtegFowzB1nykoJa/PMkOu3+azLHY74mRos2+eTDvoyhcg==", "signatures": [{"sig": "MEYCIQDh8Gu9gXtgZ586Sq1s+zkzc6ADkMWmMmhB37EPzUHjpgIhAI6d0/qE7SYLOpUq4qdiwsXamXSi4DD/+1scoO+Hlemh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197IACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWJhAAiezh6qda45n05QUpXFU2ZbAj08uIONmQ7LJe1sElN3R+33V+\r\nrlv69a/Ub62j60TMQgFnTIBdk4yu9jr02pN8wkVLIwbkY8zIj1arLa0V0JjL\r\n9+kY4bYOEJVieb8ZdhVp4oEdZD+EmXfQSUEU5iWFXXh9/hNfZw67CNBzk9Mc\r\ntU4/8YlYWvLY+mpA3KagBMSenYgsNerBCbnmpxKEzRBtV5DVAuUz9x958rqZ\r\nj5OIsFfT/rOGBtyHAatKOMCKtMDXhOiyXrp7Ddk7CcHgKh2Pn1g/125Mpvgn\r\neABbjFznjMK0uFpIFVV1Vegs7g0su8XcI0NAtVg/zrmU53mRpH9cTf8DBdQ0\r\n0C6hytcMxblZM4P6216fW241ayRdWWQqJQYc7l6fUIBYpzDtIlxcXBluozvE\r\nBiKUeojEdUS0qiigw2elcdyLecwKH99KwlNZmQ2yOgSceVP57fP/BOnr1iFc\r\n3OvELy+h+Nxz0pk4b85dLyiRlD/RRofjo/5s9Vpgpqd//LCYkxYpzChDG+jQ\r\n3gwOxqfVJ3PL8vEMlLnNH26JwjCrSw5mTaYg9fo+j0anbR0PhDIUK9SEMtuK\r\n86B1ZCSOR7bGoQl8rGvYIZwFCsuxpW0VtZeTF4Tn6ZOKL0Kp7+9Sov+7bUdQ\r\noeeMfFktgiJ2AZcWtWKbEFTaA6rxO2vHB2M=\r\n=3j7M\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-arrow", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "0.1.5-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aceea9bbda1efa0a1c1e3ae19d20ff967b416f95", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-qx8Ky3FvWHrd6zHTUwvBk83tAbbzqPrXfFor9O31aE91nZIFyMlPo56OkpiRc/rI+4GxbimB/vxVq8UFz2liCg==", "signatures": [{"sig": "MEUCIF8IG+M97wNCanFyTpwNh+v5/Ocg/FtcOIwEujCuROfoAiEA3y+YxwOTDr3vdfbtSFu3T9Fx49taxPLwMDHo+DGuimw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CDFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRjA//Zkz2JZkduQdWoB+eO6lvnyiFWSOcDgMeFPijqUXg5wKNaOqy\r\nSKgiIq48AzaIs82lYQ4BW8qsbCE93kmIPyE1wSpOcfPuuZcDGTPyRjfVFJn0\r\nuVRT8E8sUIh3645A99N2ns4aNaxLmeC0f4AR5YCmis/jFQ8FUoVY7IvMyI1N\r\nTxLZaaZeVliUn+ghBW8T1w+mnLPCc9XT82eURu0m4dCPWWgfPf44X/8sOhIs\r\nfh1hRZNuLPFYlZJJZCDtWH4Oi4k+t2cIfppkVrtexWEwSIc0AKM4XyBJ2Rer\r\nf1NTc+r1Sf+AX2+I6vIYMFcVxaZBw+RRifG9yAmNra3OF2fmjqn6SqekmeIt\r\nlVNp/wu9bE2bVKB1dNMHPbCp61o/mL4uYHgV9FY2dJ9MNeU94JfhqxfEB9D5\r\nefaOjCEOsImFdAzyfUk2UsPqNhV5D5I00BiIcGD63HAE9R/KniZlqqXZV85S\r\n46FFTMBr8f6qDD26j1+vWqb2nNCywg+1RU1sEiVYM4q2Bg6V6PjaFhz/Fihg\r\nqjG+ypc1O6zMiStg6Qe9SmVlfexLPIDDfunYTp+nMKHSdwKOvjBS9pv/HWaz\r\nN54uk5pEdzWbw6L7pG+ZyjfNN/lx8QOOeaor1tXDTwjwzHYqTeXlTFzVvZ3t\r\n9kZVeUdedmP/rZRMJv97bc01tNuDXQtb3K4=\r\n=FYnz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "69da227cebdd1cb466ae4c88f916a66a5a55d661", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-fdMWxUhITsPHmLQ/kDxGDV7M89PBazA2PpFtQsqZi4GEl6ozRKxtp8ij0+IvfwgD0UIaUbQrEZZ+m4DgK61kXA==", "signatures": [{"sig": "MEUCIHkAenc4F+X2VYTrGFFhyGnR9IA8WcZoO9D7cLFFysmjAiEArwxjYu4KllsFhdIXUoQNm8eAzyn8OvJdLeRUql+ZF6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EuuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqXg//ZWq+aCC+T8FJbcnRFoC9JBrKc1Pki/d02JNdVjWb24qARE7P\r\n6YsvU3i1xQwqhweRF+gDVgHnii8N33iRm5cBF9oKCk9vqYs0OqCljVSodQdq\r\naweua88ND3a30hOfB4eksjqtuqBhYEKPdt3HvEFZ4zJrtomFlQaGdgZO+tAn\r\nJ59cMHMCVNsqDyyQYinoBoq1S8n0flR5tHHX0tSu7q4Ia8UiW5sfSK2aegsI\r\ngA8zNgRLj6SgotKwwok0WAMycgHyJ5GoMi4UEsM399DU/GAWn9fINsUeBqY4\r\nLThdkGxGKkQzt83YWYvcaDXZANS70VJT6rFiGBB9D6Es6i4Tuosaim589jhN\r\ntEeKt9CRFpZVd9dN0FObdJ0G0hwCpCs8AbV+QDPLHIzUHOhViAhOJvZntxW3\r\nLFCN61sLWBV34wtuwJ2WPL+NTt2yBKNeDwblJ1kKVlQ0P6QlduVFqL2aDUsm\r\n2OfjJqZNeoQ8k2nr8mpdU0HP6mZRpULAVw4t9G+ZmDUWN6w8i+YzFCT0hOXn\r\nPNpAYcwMVYtrL2pT27Ti6hPSa1T2fxkbX72H70B/88Z88pyARGQjRvK50xU/\r\nUmseJxVR2IslKZlVpW5kFv4aNHcDHQLnfo5F+R8x2M11wxfZB0JNdPRWINTD\r\nfGiGkEDBSw6kegNETgUiMN2t/UjWK1eoaIM=\r\n=7RPn\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-arrow", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c461f4c2cab3317e3d42a1ae62910a4cbb0192a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-1MUuv24HCdepi41+qfv125EwMuxgQ+U+h0A9K3BjCO/J8nVRREKHHpkD9clwfnjEDk9hgGzCnff4aUKCPiRepw==", "signatures": [{"sig": "MEUCIE1yE4DKgh+loqBVy9rA7sPfwu/hJ1Ba5pGvxArP24dDAiEA1NJZy2TMLEb/IL4Xsbut5QFciAV89Xh+vc2cKs65Uh0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4OACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq3kA/+Mp+I6XyjThC7W6FNJGrjLNgk6gok/0VwyaFQZC0WRf9KRmiv\r\nMMVr5u0pKIdeMI/llR9NFtuGv2pw6ASiKrLd0AZIUyRB7PV9cwFWsURexDP5\r\ngmrgx/V4dFbCgHXfhdqUMsL7i9fJnx1BwM6ZLZvhhsnVQzXhlt9iOKRpSZRs\r\nFj/DrxpQBCVEOXr+gQzVHeWcw8qWrps8EPdzymxH+xDPgpXi9oGElypq9jRu\r\nkekiYhPGe2ZUyYzCVsfIRbd829j388qxKK3BgwQV0SwH97i9INU0lpOYeTAp\r\nRGlUFLYEvXvuWJXGrsUS9A5x4wcoqlJy8iJb4V3tHjyT52ZuDVQktYmXciwu\r\nB9quzgPT7CPUOEvwfHvhJih7HQGjSSYygG85z6gP9wzoIgH2wRM/85m/07K3\r\nsYfUkKOMfwVueJ3fB0gZrMu/D4al47ioBbO4jb0uobC+mImaJxB3H+MSZ2At\r\neQwogDaomXPsmIJ1t8Sj6PnmxymunHYze6KQsfhsPf8CL3m9jFT/QEc5EAoO\r\n6tO8qf0AW/uuqJk+jW5Hyov/pzCckaBvf28hiE5at6U/TbOtdk3H8Ww7W65U\r\nspxJiKqLbnUl9DvUNQkoAhAjmveXGaa6731R70EqDkuzzLynp8Ne/rYLh92H\r\ngTrHzxCdNyP410FVLaXSSiHSbOxtkZxhrPA=\r\n=nqNz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e7be1a8290d76dc580b7ed2573596a934b9099cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-nsbLuGlOC1/heotrRNO6/v57g8oy87siBAUZzy4J2tnHkFr8U0uqF1hf8bAmOnTComF6f+VD8njffAO0V8syfg==", "signatures": [{"sig": "MEQCIALSSUYIt//V6ZAklYfMqzLgs6eSYbccwEGFkHirZdaEAiBVZ5Cubtw4eXn01qi113MeWeXzGosx3KtUE+KglCfRkA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbsbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWwRAAn8H/H42r/dy91zfTgPjVhrGv7zmVCSp+CoIeYpf11vx2R5zk\r\nkgzJN2PKS36TN94vVIwKPuSzqXO1H2vRCWHgnFckO6llRNU+bo2f6KkEDVFy\r\nWCTzrl7idlBqGDTEEEXf+PPpA0kr2klZKCCmA4G67xL9IQ8jVYEAcKCxktYm\r\n7KRekpKvjeFSYIdbABDYWgUUep8giATSoqC+yTSrJBPplTYZlB7w/Wblrs5N\r\nC05E4jE9WcTv/hT2Pp9zfZDPHB0bKHcl+u0z/+OkxDRM4V2Rx3kD9zOQnI4K\r\nW8Te7Peeei9nEhYSyE/q83uToi4XzIw/Np/RtEKh1uyOtKMHTVURNvPjG/yc\r\nYPNODveCXhEgsb7JtQPhIiL93AN7KNYcFXUlhh3/o3yWAo8unLThzOltPJPq\r\n5xtDphO5go6jU5jE5YVm7JauWoAbD8P4E8z/RJ+qFwoqopVkUA7iUjlDvGLl\r\n0e+g1S9UV6tLyK131ka7iwhKOP1MBe+kpr/ShzIFcoyqhdOUWoRswuAv6IJj\r\nKXpW+QUXLgWz4MKpSFPZ03pU5EQGNP4ggi5qVxe6Qmt59xEBnbvI5rIEprlT\r\ngSpKBwlwSovGGe2vEj3myekFnytS3bc4WFtnppU2CpHzqCE0nEH78YS6Nsd1\r\nJ73WasP3A7w8vn3uxbG37+uTqKR77rTmdaU=\r\n=6a8X\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ad76bd28d3e888e78cc0e961139a63ae8e325602", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-YEOMvMxSeXNDB5YvJX8ee/rH6eVjC32mVP0Lt1D3pxXW4mD5sqeK4eD5P+7NC8F/W0QZTvBr6P7W6EQP6HmP4A==", "signatures": [{"sig": "MEYCIQCWx4Z2yAo423T8Dj6rr2RePJ5CCqkkOht8T+JDwS4hWgIhAIhaRt2fV5YXmorne7tIHAbXZ9TQtII+HuckkAD80qgo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKytACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoLXg/+OXyLClNMu9cX/0oXJz/X4MRmUZIacD5o/jE99c61R3OGGG6R\r\nj85elFnwG85TYMpP05/319RS2Lqo7dmCP/hoDPtvfrNjzu5LObB6NrWEJuzK\r\necXfzhMagrNo5ZoX73qGEvmCrXQ0moe0GsH5Ual0B10bm7QcIo00wqKq8rad\r\nJHRcFHgdDqvwTR8ACctlLQBdk8ZsaAaHTK7cp/iNAt4eGHAmfBn3x4u+olR2\r\nlRTejRoID2Oj6EoVcsqJfJI8HiI2ONqiUkqMbedfawmqZ/wPEL2GJNs0lByt\r\nNHzsHENRJEQRCMrSgH9l+gBS7mW/XvLs6iYSFdlg0RHzM86cYv8/WV9umugC\r\nsLgBGog4nwETD696TkdDCdMjc46FHIHmWhEZ6iblJFzs1vOJX2Shw8PeBycG\r\nJzrxK5k0Fcr49SfsiZBI6bdknvIslKabjsDR9clGm4JswffQ7ECgDbQiHzm+\r\ni8PCAu/kyN6SmmbUR+ONu2/LmHZiLnWuluIn7k2N+uJMGMOETUoXmmDxjdt4\r\nM9kC3twuSUc2dDteaocu9HKw3ul6NPeuZrOspdKefjhukMIMJRDEX2j2VlWW\r\nkzoxI35XLmju58sejRaE9HQslmcSQ8VcyWV+lOEHCF4z/HFJhDxhiQfpcjIN\r\nQsXyLtgJuvkmcoxlPuPDqHoa5+CQiZQw8tw=\r\n=4pbH\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2732156df79fdf981357d58fb61615ae21068d98", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-OP<PERSON>rOchZStSmvh2B9FyhtgyYCn4Gk7oQ/67p3dOWwN+2KT0uLXsLCAdQf+DmuT2S+dxxIERLZsDeCGycm5Jx9g==", "signatures": [{"sig": "MEQCICm4+4zhDPbnHnOzsjNyc9HXtlxqx1d9iNeuxCyQNhhUAiAEK5Qsr69kRCFUZfgcw89fZz73MVz/f+pt2hEOHx4EdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdbvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSvQ/9FsMHgyFhjnubjXlKBEIppO/MfDccNKZmzZBmuhX25kjnRLqe\r\nIw0Fq3PZNo1HnEQlLI6it6J5jXNPeJxJhDApnTu1cZedG7PDP16Pf/s9cXkn\r\n9n9Obnc5TCWd62zaIzCbrQqDUv/TpajI0IQWMkBnqZ3yt/8sx6BeVIIIpY3C\r\n1t5DtghNMyS2uIpbPRRJlXZH1Bt+qXpXX+/htDH1/flg3hcACOn/VEvR7lu/\r\nA+dDp6OUgzYcIzf8K+vQkgbcSodttGyWzVcskbgHRtQEt6DQpbvkwsOES9v7\r\nSKy9nQn++aG0lvrf87zmYMPTLPA3g53lIo2IVh5slAoqBffyPpv9GG8em/5G\r\nHgngKPcnJqL/J8ATHrSjThd0RuG0HRjCuOfppI8uPQee5gTaLLvF/kYaLQUg\r\nQetIHImJDAGVVP59tBD3jc8BgRDV/+qlqcKufo8jjItXj+V8c8y1DOUwrL5j\r\nViFqtPWQUrzsaXzuvhyuiywXbd1NWLY52dAKSwUZ4BcFDya6Qa17eM83VHbO\r\nS8KobuCvE+G+YGx7KZVp4d7ze7OsxcTatasLgfGFnWc2gPh/rR/7ImHaA69f\r\nc4MaNveDkERNqMSdu6zheNJ++intJMONkX5aFneMZ1UBmGNmnnjJKnT/2R9o\r\nhqoE2FNqZnmJaiLy3EABDHJ70ogXNOcdteo=\r\n=cb4L\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "be02b82253ac352b6c6cf9b232861ef9ff771bba", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-HoE8k4u8c9QtbFB9uwLXpwbmG1F9Riqf7dB4UOqy3uGoyaf6BG3KTHO2HRyY/gRQBNHly2VqtrRXFfHJhx2NmQ==", "signatures": [{"sig": "MEUCIFAELzAbVLsSUMeHPJIV24jRG48TT7Llh9U3AU1Opx6HAiEAhyv7fbXaH9rvaaDbm5H1pZzW0ypEpOBdHFLeLxAUmTY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfAeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoXLA//fY5JHzUJTyuy9x7pULFwEM8iDo2dcMb45egHYwL+zjT5lBDu\r\nwTDpxq84ZQ54EI1uwoype0D0tchtCqQgvhIlXkqvElQAO+HuLmq25xyc/wMs\r\n8LPUW073aoCsSIcR44SyLzFcNl0L+9n8Sprap6LF5hqYMVeg1Xx0TpQ0I9Dk\r\nfVWVYI4+bX8qb8ohQRspoJ13HMx8LKjj3wXwOY7a9KKx6XG+nQzDCrwN/YBk\r\niim9adUeCOA4ZjocBbKYIPXNnnq5cEXGgzWOkrAjFwXuE8hTuPV2fvkP57wR\r\n/rZCm9NV9V5krQX+VWZkYqp02y62LljdC4uVywoRfxIxBdNd5Pu/ZQ0NzuLc\r\n62qnbpE8cIuLLq+D1L5DUBX8P4YyqwiwYc1NazqDXkKiuNfjqNBamUtbVw+8\r\nCYGFFJ74o8sHBT65RSg88fl9aq5WuwQpcAkakn03QdwX3+QUEqZt4JSWkI2O\r\nDm5aGu5/l51ehn2lMTmCX0AGmJM+Xxp3MenC5yTg3C3wyNlAmLEcVfB/nTNc\r\nNpnTXfqbJqOAhyHpoirRieiMXFOOamtWSHOg3zX521wygUfzn3QXY79RRv/A\r\ncFP/7IY+zj/zEEg17o1Xk8mv6VVkAUZL9G/zoKYIkkMJjbAeGdFS8vBvg7fC\r\n5pK8DB0eHiTo0kIxO7LClnSQbBItILcJgSI=\r\n=XsXp\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0deadbe22b0f313596a85ee42662894acca0dca9", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-6FByxWVt4L50vIiYSNpHynGUL1Ae2tnYl6QrhJ+5k/5sMRnT1WqFhmQOex8D0wqKUUT2Hg4XTMRdL9hKurFEjQ==", "signatures": [{"sig": "MEUCIEJuxD/23J24wuS3O9A/nyMIii6ebGfPcgn2F1k9c4wJAiEAm0DyD3rwiGHuHbC/Ln4/G52Mf8+WfV1hGY1AmJmEnCQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHjg//eva0qtuM2ELGGDkBDgja4JfBS/0rl7pgSuITAaO4bo4ZMg8c\r\nnmn9tVP43G8lFmsfXKrwUswyYAri516V5BKRDZ15QfdoQ8X9SVqtcHPBsX2i\r\nd5NyBnYbHns9hOk1r5cFR6+cKA9fLx77Zfk8TGzkr5sZRUIVck+L4WdQ3EgP\r\neeRQJxwEkHyvmKnDXT3L1URejAng9ngY5Hcjp9WXq7FyC3rJYLItB/eaaT6C\r\nlcB/AzIDv6rRcZPBiw29pXBtHGSxqsU6G1OXXiVdZ5QqetmhRlZr38wvMWB/\r\nq+0kG5BoXl66/4lBqXX5MtIipb6oozzz/N6Ffs5jH/QeMEDNobLDBcFO9J5z\r\nYx0D1QhKlV8Qa1Tg1yXdcJltQc9S/TjfWk+ksurEJW7T/9YSmgEzq9wsrTAY\r\nV/rDPuliTGP2p7vQ5/hq396eBd32unzX0O+QENw1n5ASJ7CzgAdB4W3EaQgF\r\nnc/1QKQWzXHe/laM20lgVDarosGzPmQWIUJIjNRtEb6I0SlgidTQ4dPNajjK\r\n3cDREXa7JGsE0dFJlfIebz7Gfsk4BVJJ4AMQzEPggw5Ln31DE2DQ4s6Fk143\r\n1Sjl+oet+/STbCqtttW9lwStQQJcWmcnm7ym9idf45cgzNlqNei0wi8MUsWs\r\nAy3pIfafH5+aGLzsyIlws3KxwYb4evqz5eQ=\r\n=2O17\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3ede61d956ed48a9a5d48bdf62e57268f77158dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-3vewMJJnyI740OKGR6RrzOoK9sGuN4lrZoDFBxDqjwP/eZrx5ij9FLNAXZU02bkRpfNJTCY51RWo9MLHSadi4A==", "signatures": [{"sig": "MEYCIQC3jcNobf6ianWifQqCb9lWmh6line1EPQVn/Hq9GvzqwIhAJqTB+o+wdW/Dp3eVxEM9a/cPl9qAuYkxAGDNXWXsr7F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwOtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpg5xAAnZR8q22M5wnB0qG0CYEOIdXr5NVLgJhj8Nf1nUz4VgA4Tlkn\r\n3Q+b67sGdvAi/25bruOxC3XpYBbqtwXWqrX0iPd2jWmc1FQFG9ztB2sWqymX\r\nI01GE6HllT4mAlEfYzRu88u+UI6VsN3LjjbWxC+kMg7nrGFfB7KkfssmStvN\r\nMjctxcLugtVea3GVn+cKdjPBjS0gSAahYFUIvvoSlbowSChAY4QukBnaZZy1\r\n48Qu8Pm2PoJEP1h3rB2fQrNvzDTrgPqTtD/K32rZGyYCg0Z3RMoE2MnoAVTp\r\nESf2QHrjkCROMmm9ip/uYXXDtu6rR0mdFSJgTFWMe0ILE2h3L5bsghBNzAaZ\r\nIkQiuP7jw7XjfvxiphS0dlfb45o5QrjcY5+12s9iHQSeMhzm0UYC+UBxco8q\r\nCAsu4CPr4UcFfkUB8nuiEpDyhHKaorTzHfwiYZd6ut3FMZ/oozGnh3I9kI0X\r\nLqxPop8q83SfZ6SlTRxWsuDH48kSCnE8NzxkcbPXccdkWoc1t1rrsUq/6xux\r\nORzNc+NoPzHVaCCr/Hg3pi+usxPgFXqH/32EO4oZ7Tj9HNYvXA2eM54OBddS\r\nawTu6Adc31UwexkcwUqSgXWLZLTBxLcJuWzUubMbbitoB/3jBN3ZoM9lmC7v\r\nlRbNrrkR4HmeOCxJlUXnnTgUt6xTbdDv1VU=\r\n=raM0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0cb759224047923902c8d47e902f8b5f83d25e8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-xU0cRo8ZTFg0qMfoNfOnh9KE6BuxvuaUW9iq2uxBWMLmn0JA08DTRbTEFx72CGTkfZk8ULb41VIY8UjEsrlaRw==", "signatures": [{"sig": "MEQCIGUIycOxnL/oc6mnsZkBkHygc0VN7A2qLG4V79TOtdZjAiAAh9+TAKhH0CRFtwpuJbl2u5ZeqFjLMlQVdUTAAHtEaA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwwiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpL/g//cN0yV9wOxYLWV7m5VfSWtBX4xi3Bc+G7Wm9+htcBdB0tGXuK\r\nJvnd16DvEOGKn94RksloFOvyJrxmy2Y7Mf+8BYvnxeX8/SW+/O6hXSAkVm24\r\nDCV5EkE/uOOhMwUsCDiH2V/Xr9TIwzLrbcFtic9OsH160W49XxRxzajUStzI\r\n4SpIbyuzw5cbq2gXT5dldFHw5yn9srHOmij+PevlpPUMCibVy43iEZBCPx68\r\nafP85rFBInIX5X9crqjfFSVVR8AtMIthZASlblWtguDv3SSOW9R5nbVy5iVZ\r\nlxuGiNi3ujeEgo66jkmRMrWzNa3h9iejF7eQOGXQaCRDHg901HqFMW+10XvB\r\nr24PqPGsyt1BwL8jhEeTTRvgnjRFSJCxtf6Ro5Xt+76h0eUama9Pt8as9yyD\r\nYJjQvjyd/VwBx7FdokvTk57FHqjSfP+H/lWWVMioz+mUazy+0FL2pkO67lC7\r\nfaoXhbs2Hh2mtK4qegxhFyOsy1crLXO6pekKjX4oWjOwHRQu5gD8xR4Ubl9P\r\nRJgWJvvCjoKTq4YPIyuNsklBMKsfJCp5qVCxcav0ObPER4xszsCSpjGVFBXD\r\n2QRgiGyx+XIwVHa9ToDMbtgs6ZbAd+3QtQbkA4DM9GNcP8ZV0Vgwc2YvjNd5\r\nsKEVLtKVTdr28I6qYXGHh/MEb264ggC76+8=\r\n=ZHom\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5db6b4b49381d599e1bb63a19f3d20e61da40955", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-5aUVT5bnbcYoDhKEurhJNLIXSNET/Phzsrj4W0qoEVS5tz+rluNr3jHAICwuARivkWjazKx255a81q5lXu8niA==", "signatures": [{"sig": "MEYCIQDpJq1RIPV+BKr7zZm4KUFYrVXFNTK5aoH6M9EdlCYEUwIhAOMnJAjv2RDJ1/RJvJ4angZ8J5y7dvXunAW3eyuZPrkP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqoiw/9EmO9kOVZH4R1FFRelmhy2VbBYoamXqsKCtfZ1hrEHGtScp3N\r\n4bwPyvT50A045mtN8hmPnhgaEopnIwExIkH48Kds+DYGZqj8AGgLjKHtPEMd\r\nv+B4zVEG7M5/ElPwdsFGH93xfbi1zQalnNKHjmHguZ00EAae4GkjrIXow7zo\r\n/QxutonVwvy+2lsXfydAzIEQ4TmeW3Nr3hWGXs1W6FQBemy6mJy5xW1ZCaRz\r\nKdYL5o46foezdOW6QDoiqHjrT4pZXNV/gVkUGNbTWyhUZiCBbhca8PkcsuNf\r\nXJuVeQbt50CpiiV3410xSfoptuxhFr/kZ9zwalWesZGeOAXULypSkw08psZB\r\ncbRYnU9bggs+lFGYlHN6OjXk4eo94xs+PR8JKaspgiA1YhtDLxlN/wWTXA/l\r\n3pyb3kAWcNo1lBHN9w8w01eS6laA1Y51BPp29we/oTHLggkNTeN6n6pg9gO+\r\nEPwX2qnZrx+R3WCjmSKHXn/KwdREFp7ScNPzbvMAi9SSgbCxUMM/RlKJhJ2j\r\ngc+uVLNSjJzBX41ijFK3UskWxQG+j0tdAfO9RbI/7jokq9lxTzQ5FeyKOaKN\r\nSKTXtvsz4ms1+AfAj/ktxiwfxptwFyDF9tYVnKvOsn2OMp3OBV2td/HxZnP8\r\nvo8iABOM+AFiTrV7h/3yqLNUqsE3FmmZae4=\r\n=WFR+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ce2d2a953a11aec82e9312c965400cc251485a76", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-6EXMGpiH2KVHTwIeKrCTtIO4+Wz4d+TZU0/CuUZyzS9kSVWZMymNBU7On9Hew2SucHQuw9kw9mSBTJCRFJjDgw==", "signatures": [{"sig": "MEUCICIljRgvBmDcZyfu4f238w1K7oaLKxUw/VquN4xDLnoIAiEA6FSCaUHqcn4na9qPqvmR/U1hlf5YY/6JVzPovuKM15A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/axACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptmxAAjcfILkLEg0ne/Gy/hY85UDKTu++Xrl6rmmtLdVYGXOIm5/Xm\r\nkEID+8qKlTShID5qrv4qHsbk6m8H1AisaOCMNAVTyc+rKr+OVC/PG2uuXMGh\r\n/X6FEeN5IqHlStOY4tDKoh3APG0DAB5DNSH4v0N2Kzsv4dml8SfsmMa0G0u8\r\nrapMYoFn7ErFQX8XHcBGpQATFNH9NMVWJNsDTP/hrLhNPpay2HyVxAFyDM8x\r\nyoVOK39xkhgyirIgbkyysifJGcMHcVY348iZAW+2AWBJEllpDToOl39wSTuA\r\nbA0WGSZns+rB8LKmlgRhCYQsu/JR76SRjZBlEeKNBoWpo2YbHVWM3zy6sgQF\r\nErRj8yMRrn3izJR32r7WfMPOQ8gQFacB7Z5tEJoHRnYOIWCzKK/wndTH3yxq\r\njyd4TPiAIuvJYwsfvfu/7bCGNnDsyEH99W/D9JfV2xsEA9hwy/UgkMCru+pK\r\nrlezqHi3GHZ3AwPCa8gb4b9jzWAFw9thkArDHae4RJFBMjz+u4d4UP0o9GpQ\r\nWeDkrznPgE0/cQNKIAxEvAY/e9B5x35bxr8zvONtFotq4wM16zj5x/XWMPyU\r\n+Dqg2ct8a6JAMQfLnFT5+m9jjsSoc5S6h31YvCobtTfbsMbl33Rg3ma15PbR\r\nDXa4DpKk6nugZOvDwhaZ9vrdQE5moCVMVF8=\r\n=5Hrf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f7b29e04c060ecc10717b79ce808ac41056e7e53", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-HwhpWxJl75QapI5tnifCqLnfS4/gWSb/HUipZxFNpmSc4ombWM7K1MLNCF8848Z2WrNf7cW/zsDKmgc2npKg7w==", "signatures": [{"sig": "MEUCIGFPJWMbmNkvh5KuIGk+D9WyT87Qf36Ney7t80YRXO6fAiEAymr7IckzE3oQKgTgEOya1hvYponOvHC0huaXUl0pA78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUBg//VDha5jn/LI4Vi8Qe8yKee3gVcPiBUr1URylMUWiTiNOvWq7h\r\nLoQKpcx7kyEEY8nQV6PcMm9/tP142CLEPO3rU5fkHag9q/YZoZbmpyY4MNo9\r\n32vR7R9yCk5pfI05QDl060nR4G6Vggf4dm5GEgVFZ15aOXPnmjZP4ITA0xtH\r\nWz4eKc0JkcFZPgyeVSAP0BBefft1RBYf4bMJrNljEgWh11r9OOkomf6QDkPr\r\nh8bNjwL1aiM54BhfjhCdxWs4HxluJeYg+o1kl+4biBJEigZAxj88zyLNzKsm\r\nr4wGhKRK5y6V67s9tOFyQ2vuYbd5D/HZfv7gv2RUkAKlLKOtsG8RD+USiMfF\r\neRnY7G2ynUnPaGzHn6kYy6xnQrZ7n3J5reWBox8MKw/rLb3h5sLTChgYNBr1\r\nOnel6Xn5tH2nolYTcHgpUv1pmrxrmYcxaQ9uZzF/h17ua52PFdQp/iV7kPQo\r\nAfL0N4L0TDTQqoOmhf8Ny6q4al+u4W0wzbJGAjgUiCJOcZRGbFymjWALCO1h\r\niWRh5b5hjWU/XR+yZjPwd4NSnh63KuW8sMO+6nW5gUn7go0uGj9mwTKF134A\r\n//prn4b8hBWy/m5BSu37VB76uymICTwvAoavh+eRvIYcMEJuq6i4k9BfTkuc\r\nwYQl/IBtoIR2u2GoKfEDh6yBruYmFSLT46Y=\r\n=1SlD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ea282cf561874022ba0c6201b99da32537e1b522", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ptGRBqbXuBccwokyUvzt1vBQWmg7CoCJb/5WKpvYUSzBkVj/tUyFpMNlalN1EG0uCHZZEuKBsJvnDUNnI0tQiQ==", "signatures": [{"sig": "MEYCIQDFMQe9FSH/MTmymBQ/nJ2wZ2cZW9dZ4E5X1l765oQkGgIhAKTpOtY+hI4fIHux7DEivJ8MOKNJKubM0W4MrJib7lxu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoceg/+PhEBaOWuj/KPyv7uKuwrOX9MmPwHErH3hb3mOOIdmhrENWSn\r\ntGJfzpCptVuEw9Mlw7Opp9YnQvQrWflp6FaH9i+tJISvhlpEuPmwZCvGLuB+\r\nU+ONkBuwEgoG4XvdFUmjHXOD6CrXzv3L3mSj9K5p1yZWlwhmkj685ojUd63F\r\n0QPZpS6jTdvpfzY9Pz4hXSS/WiTfciwjJ57u5Un7vVhyB18j/nguG0EXtrT5\r\nXmqhOCaRJP2nZk4ULi0ZgC3eSHQ3YS9Dn98DtO6jvit1onPG7mkVZk3+GGIf\r\nbc+Yg0m/38d6yQ1w/FEkh8L8dB8JWpvHeNmpuG1sxuIYn8KZXgadGuIP3gbQ\r\nVuFS4/iKzBWsS3srDrhqpABJIvUO6LGn71VUb26tmo0lZ/3nqSMlNYnSm7/h\r\ndnkTqj3SJa9ut1/rNnN/TmCsczDg3cJmWJYIjPTCYc27jIOxp7TJpeBGvnNc\r\nkX7xgBaRYKziEKn2zloHDUNxuKMWPZs+Z61xvNSbWrfgsJF4zHRqK5ENoNVF\r\nAs0qYJN3FT6YVebeI4482pQTa6fxR+fxyyP8w1C28XHCc6JYiu6jN4Iw5YSB\r\nJBj3X53UrA+CO+f7rQ09qPpQNvTtO+sOpN3bxffNPPu16z79qhkKCICTU+0s\r\n8kKyNl3nuH3+vpBkjN+3hL23rJtTgFwPCqQ=\r\n=Orxe\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2e0ca395e5d036a0749417edf0a8c5d9b99aa932", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-o7/wsgtMKXSyyUir22Li92xdhAaOGosneq3MpZB7fgyYar+JkCFOR/3ByI93zxelKYF72vYPeKi4nWY9t+9vCw==", "signatures": [{"sig": "MEYCIQCiLNZgko+aLQWNbRYA0vJsFUxqnap1iis2TB104AzKrgIhAMeqYJvqtIqhGLya/VM/buQSAEhgGsmcZRAX4jRbF1O7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVLvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCJQ//SMPBak9TY4y/zx+rkBmxegM+tUizccb0kNoVJosL9csLqObn\r\nDFmYfHsz5B3SUcHgGIKr7QXVOOzWzR5QVSAWCiVFc/l/7Ei/VJaOLnj0Q6jq\r\noIB/cBDbVuTy6rO+zcIR6jL34Pr9tQwlgXhrJQZtlYmpTcBWPnAZTblWtW9h\r\nIfruTbFHHBHBM64zKz5qedOzk9SVKoNl1cg/tbdQT95OJanvEPMfchzfuFP+\r\nfYHkb051JeXSRvA0LrYaSFTx6YlU4mQT8vraUy0q3WEu4S7YMqmdSJhoP7iC\r\nhTv1j3Waxfd/f24eQZA21WWcKRZ19oNUMmazVLdlgJ9ICG97Yy7gzcCbxJ96\r\nn4YNQumgpIwDLCJdhCvZQV+45kzTj0Ym/mHjaRV0uzdgfCU1P16wK6yO3Eo7\r\nIqEOyXY4TT+mzcshQQ4N8zJSlGNGV3wY/+6TRlNTlZ0vNjJ+spOBBvmVCZCS\r\nWcqwA1bRU9bp1J0OHHDu9JpqfHDEJXiq6QdlIUMy/fGs/+7E2c5sGEvrYxhe\r\nyDhSQqPln0lWQxyLDj7mxvV1v4o9wpAwUTBsB3EFyh0mDqRVXWWCE0+sXfKp\r\n+Q2s3muZvI/pZ5dB2EFF1EEonBkRJnz/soYdkOVXmy4hf0VQvvg/BdPz+ICt\r\nTd3pMjXQngI4VfGzKR7e8sccGMT7JFqzEQw=\r\n=E60v\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3a1fde4fa7ff1e8ae1cb93e7a62074a2f05671fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-75NWiPO7bQ1nn5bw+8ri/53p39/SwJEat2gJigc3CiH50ZHxs2nCDx4DInm9MCczI7pj85ZmMNs41SSgxGs0Tw==", "signatures": [{"sig": "MEUCIEsMCes2mrZrryjHVKBHFSrbXndG1cFnN9jq2dv44Hd2AiEAidh7GKI3I5kKnxw5HnJ6Xl/s7lKh1mC4VfdGVixk644=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnJ6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1XQ/8DJp//OSINJtPbZYqRMHIJUW6BwhOiyz/GrJ6k8PNFAVU61od\r\n6XLKD4FAFUHaIZByTgSHnnK2+vl2SGGUSn3hInTGpxokp6wkK167/eFsKwJG\r\nbT4rLMntOFkveWhzbozLoOSpjjctFznpaDV4KRQ8chq1ps+zdtBbSJgM5+xz\r\nQkwIBXNGwQfmZcZGtrRi9CZkEVugXCtv+BKTLcpqklTRciAwuma9K+EG<PERSON><PERSON><PERSON>\r\nXcZ14LxW4j5zfX/CqrgUEOANQ4Q7HfUCRoaeUZbG0DB/PxuH2XHSGay4YnMj\r\n0owiIKWLE2tRO0FQCNtgD7OVWs81dli7aBAR+GMDHdPVwpcqHYp/9VCTFUt9\r\nN2DQXeOz/Gh9PWKB36lpFJiKMv7CfoQwhyzqqsV2DTcvyHKQ60+B/r5Q+xii\r\nzbNbd+2sxJQXzdKOWCwaLnTbRzqN/b3VtsQTsH2ybYCse9V7pLHExMjzImFL\r\njy3GzM3hZ+g84G0wKl8G2uTRi7VuRpG5tCVGuSbYz46FXzf7o2HMf+UCMJ2m\r\nyxyRIXcgv5+yjOtuK2Sg8VMwypmJGt7Unrgih0oGUiFslj2GWzSFV2vrwvYt\r\nD/bu5HASjx1JEFneQiQ4vzfiLOHmwB+ef5LJCaZHVcjptoDC+4Yrj/LEf+s8\r\nMDdLxi295VKg+uJJ9EtR+ZRbKV8Unwxv8hM=\r\n=tarq\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b8140d19afd35bcfa4b95ac80d75f43a100c4f01", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-RpDVfy8yLWc8MAUlT1jz4YDDgaysZeEZ6lVs/kB61t3hR8MDdNBaqWO/thh7Fzdy5JLwAEB0vvm6XpP77gKvug==", "signatures": [{"sig": "MEQCICY/AWW90bx4bcMdi794QkpAvWz7Rw2spdOGQcGreZ1nAiA4r5hrufi4qiRrWs2EiT5EF6UpzvTKMhkVY7JfglUViQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqwaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9zBAAlAE6d+FURqhbSW4AwjxRndbTWX6nx/S4q+7Jp9JWcI+HuDYC\r\njk3gZtzeIhEef2Bq39q4hC9KoT0iGPr+MEf4KqMApD0QJS5vJrx2/sLK3kmV\r\nRH/ycBjbpLqRmS6KQKflb7My42RH//BHtkeYDOEdSNnUL/q7LhaCQng/4TMq\r\ndtMF9oH9zaI1THXYPNPgCTIF12jEf338Un+5lbtbAjixF+SQKA94OCWCnpo<PERSON>\r\nChUui4sOuqeKtZgUqB2Rm01S4uzBlU9TwzUhCRcBordptyyft7AF++h4jFc+\r\nIJeD/nfVNBbambPJxeexsRuiQIEQwKM8sbXg1KM2C6YhYfIoPA2R1Yhfd7t8\r\nNBwGBffXg3yluzTWVrlcDzH42wWbCsRIjPv2Y6U/I7AfpCiPT39Sp/gJHfD1\r\nuQPpqZZ9ZbxGxOno1FgpQLYHzsFCWo/W2LjekwBpJuwL60myLyNnuOihAPaC\r\nwc04xgvgkom1Z08hi6u1d+MtoWVQP9V/fgZOoPWxfSJpRZAI6eiDqoyNBl37\r\nkgQoD5IoQ/r6BVHDy+7v+W0D+xKPLrWmTe6R+X4U9Qe55Tazg0N7YPaLASnf\r\nUuHAtL2bY1uDDKNZyircZaDV4h9mDpyIZwK9cDqCLn81zGYz66N/7FY+hDiy\r\nnz/u04LWhsMfungmTj8zug55aRLmb1zYUQ4=\r\n=zcn3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8ec678c5eceec19d22c009d55daf5a6535abba9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-iDo0u0Ml7mXZOU1YQo1IQHYZF6Wb/JfO8K5qdat/oKPNIzUwlTr9SEr4S2V4KWZZo35c0VDDS4HXDKMvtR3Bsg==", "signatures": [{"sig": "MEUCIQCoGI1MRapheFiHKVUqIjdZQRHJGSdFXyxQRwUvsuMOSQIgVKrKEGv8V1zAweGw6NIGon9/H1PfuC8PnIg7eBWAKFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUJxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmolHg//UVH1wQW3nStFZcA3sqSs8vJTNa1KOOlVmPVUlMYIwk3QDGxR\r\ng5IYnOcR0cjyjh4UqfJb33Hqki6OFK/3Prh8zHHW34CZdwG3NdLa653SNkPs\r\nDhRi2RQSnjuNofp2E4TrbTBAh2A3SjIpoKNZdKodq7lpNATsI4ePDSalg7fh\r\nGWMcO+DiQzVWIqkKcZ+glWDaE8VLl94jpuYHnEiy27pNvd+7cygFWNZ7i8BJ\r\n3lidC/k3I2MENrZtcAfxZM7s/jbXjGwaD5g+PHrn9I1pC/Je8tyn8Ed05a+F\r\nmlrsrqln3+9wfnVr1+TbrHU3pxT45/2C7NrM5BJJ76Nc9zZCmRAzm2Pjji6n\r\nJSa6h9URMiQF12RNzV/QjKu1dZlDEz/QJNPcumpku0W9tH83GgkPoon5Aodl\r\ngFkqSO+bAt2o1Ps7L0gnarRK9QgirF/p7XWYsGPqmTFytstuzonJXzE6ufSG\r\noKFG035u61UX6S3Wlka9+PBLq9VjNk/Dw80FkRbbyQJQi8JwIxw9mET3/DEX\r\nq/KPKMM9d4uTLA7GI4EArTfUZEQ9w3VNfCE9nFOX98h0Ux/rNezSkOQegQRl\r\nPMeaFowJSGp94Enb6KpGCeGeW0T2FV2hVCRCw0LPJGx+G8TnWA9JT2eC8ieN\r\nWJNgfPgDzLYBOfkjC2h9anrYm7wWmR131Ss=\r\n=UiBd\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-arrow", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a5284fd740da89e04119763462cfd6d306ab0154", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-OxXhGI4O2SZpV8834iPNWjV3Kh5cUiWiXscsif5eHkTmJRQgGUCKUWAYt6DMzjs/KFuVzOvXvA49RYpkk7j52g==", "signatures": [{"sig": "MEUCIEe1ETta+URNZDSKWQx69M+YQ0aNdO2UWHcEaScf/ei4AiEAjPZjOdXmQnPCSmjyaMWopXguZWZ9+5pn51Z69ApNT8k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10399, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRecACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCng/7Bv2+uhQJK8bhVTsZEiUrPvpdtBSzza937NkZ6zUjmccS2wb8\r\ngoLcNjAuYHMiCX8BuSC3RegWXtt2CZHQjUkQ/hWqc1yo0xoPTWrbMsQBczgz\r\nOtV6oB3oNGxmw6bGno8UOwvnoINvZgzqHm7QN/WsutrzQEpf1PBDwvNMl+VG\r\nvtK4p/1uPs4nfhvCtbQ2AfgKLV0n1vpgzW8gGI8ZuVF4EoJfe2oRvIybuV6G\r\nLuz4KbgV9FZGHGA+BLv/kDxyWzJy3WJlfmJiR+0Y/XG8FVqZE1PyFAYQzp8m\r\nF7lMou3+JzHvOb+CLaDPjnjlf5cSnn5jOiaMmJ3rvkJ3biBPhR6dfLQ6TfBK\r\nX3eb1iX1WJzt5lOBnryg730xYTmhRc2MaQL5OZCC11Pfzn97pHUchjFSZScB\r\nn/v3T2AmMhQQcGkQaNVptprHaFEsLU3Hv9WGLz98sgZEoYgryQ3cgVoY+dra\r\nRq4i/P9rcH13iFfg59d9J8lTqZtC4V8jZCtmp4reVP7kCb9kDrgY1N2ARUR/\r\n3eYdX9RbFobPXjGujb6mTIleUuh9XopWUOfup64hi5HaubYTsgvbKIb+8w2h\r\nkjy4+HbSdLBUr++OJR3bBzKhSlu+5usz9rHigRtEJFwxHzJi9wqFP6rNZ891\r\n6oW6EpMR/wv7LVpFVBmcQdHRQZOawzEguZ4=\r\n=O8vc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-arrow", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5246adf79e97f89e819af68da51ddcf349ecf1c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-1yientwXqXcErDHEv8av9ZVNEBldH8L9scVR3is20lL+jOCfcJyMFZFEY5cgIrgexsq1qggSXqiEL/d/4f+QXA==", "signatures": [{"sig": "MEUCIQDMjX6axmUxRaBtCERUCKvnFDNRRUKYR5VyiAHQm5bHjQIgEeR2HMFZeiqVs10wFRc9xMoK95V3ZJ/XMpWOEpZAp6Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSU4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmplcg//Vr42EoKe6zAzI4DBTW6t9DUzDpBpTXKcCAd/O2y/r2oPBfVg\r\nJqi12qgbqregERmTHcPWyisy0OSBn7nRVety/yjNuE1J3CJBL+kbqP+C46KP\r\nsVI4ZQYDktM4VzuMFV6egq9gY2bpLf2xksdgqUdXFaPglfcGuBIK+AVbt52w\r\ndCMlAcsq/Jia8n93BbNl82FofBDm/SbmFlV9nsHM4rhyKzJ8BzmPIHEE3BOB\r\nmheo03CgULUnKaP4JgOtg0Ovz+CwbkK0VA4h6m63sYMWwTd5Nk8BmbMTm+ZR\r\n8fbsK3Tqj+uO64HKFLCONnKuy1QUDA2rw/WZR21Kovvs1R/oH6e3IUhJcSA7\r\noV18SQtADM9Lo88i02MgKfRYHKHPdPFA2ms1HW8HXGTDjYTzbHDxY+pmi+sE\r\nWwrH+22gG+lL11Rz7bxKddbsETn7l14gWvetK1608ljtnPj+IRpxNXJqL/Bl\r\n9YaAJkUQZbYEeEL53TFSQrjA3lXQFOZDyvZT2+PekWpj0awaaPC4RrVBE/ul\r\nx9ks6loZe3wRtho15ku/dSxRYc8WKJtauUrtILUTogn2tVhH78u91yVnlNYj\r\ntd0HUO427tU2mxeI7/hmWifELtwBgdK+QX6YHNxKGI0KV/K+4bEEH45Az4Vv\r\nZf0Efoek0WuJbAR62dXIo47sczmaJbn2XLA=\r\n=Rq9w\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.2-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "71f6c5c72149b4d7b1761ecd70d6dd7c2db84e61", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-jELPI/O/VIWdBIRts9OrYU6RvJcwM88tFVdBt9e/TwJyQ03R0WteoS+nU4roz79G2PI7Q1D7QsPUF6YzJHd1Fw==", "signatures": [{"sig": "MEUCIDj6HdwParSZIUFz+0dewKrkY9b4mrLckZtALrFBcBKNAiEAro3tDtmVlhGhAvwmLS/C7wFeZ/54307QK6FunMOYYjU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBze3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrk8Q//VT4x2ToLmd1Odbg19xrOVyLIt8a7HOVdtYVRzIerCwVuuGM9\r\nlpw1OZCDNFhKdOMC8u+j19vE+5GCbFjxyRKkWJjcvCC4TtO7D3i8afl/Y3H5\r\nSkYoV8kzaSqBkP4UDK9zdQcQqT4FKulwdu/gOKYMK1TtewfkLkxhupv5wbs8\r\nbNjchNv6tLLu2h4XT8as6L55dQoygLNPsqKdsA28/R8wemabhQTXevHtf3xz\r\n4uFN7HZD9L86jRn/egcabtyeBDhl2gbyydWCBRoYS8AMlV/vgoPyCCJYgB/b\r\nBqBMSfNUQaDl53v3mefO7V1ikGoQOwxBaCnWmwdcLf5fGMZJW2C5QoCeCy8v\r\nK69+k6i8ANkY5KP70sYJOZ0IKcfbbSBX7yA8nqcLCd38T8m9xQaetBZ3su6T\r\nrSuVkfzVFko+3sG1gx6Z91QTSrY2E0oMPufhGkd5S5w5sjkom+M5lctfYdiV\r\nqH3O78j0F8czPGAW4XzH/18K3FrlyHtMgl5rE0aOzDpPKSxcG3B5o9DFvDOZ\r\nZMOFQQQR8l2cSaa/AWmZSwQIoUausIv3/b4vmvFosyb3PAM+dR/Aa5iVSmDx\r\nYZEf4EXaaXVN8qr2hr3a1/H4ElYswR4gxsmqi9tCI+2aSZ+O1jeOso1/bo6s\r\nZZxtFhX8vyZ+0+QonoB0pTLsO+ENXzVWBkY=\r\n=Mv/N\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-arrow", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "93b0ff95f65e2264a05b14ef1031ec798243dd6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-fqYwhhI9IarZ0ll2cUSfKuXHlJK0qE4AfnRrPBbRwEH/4mGQn04/QFGomLi8TXWIdv9WJk//KgGm+aDxVIr1wA==", "signatures": [{"sig": "MEUCIQCyU4F2gmA5v/qHlMsqyK3+jAWEOgl0RZ1Pwle0pQsZUQIgeH4SXH7rsm8AQ68EXTmaEDBUhdW8I6lIjXhd0Qmg4ew=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10359, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJafACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdRg/7Bp0IZRZCoSanWE+RysvCaGC9WHlMrQOkpBWXcBpRl2NwQjLA\r\n4WffGOBYG5C+iHp7wn4kRU23vH7te9ol6+0zT/WRb5R7y91M+tzCSLpF6wpO\r\nhVMZO+7L8JefPPy/RK/ZLw0lfDMQ8nw34PCGb7ZJTmgUxWgQXSIHUG+lZdzC\r\neWoFbm+oHTHLDP1TQX/W5pP8GuolQkBKwi9tAxT3bmKIpsptaMaC1XEF6FQ1\r\nyboyniqUh12Bd40I7H4K3/1ueobZabqccOj0EppuVa5aqTn8BVUJUY95/qSF\r\nfuw4fz+x0nziRS9w1E/owGSPTggF04upstnAIXBbrXlTd2V10N0p3vNT6HeJ\r\nR+Xc3FjR0VSDx5cyeqEzNjc15j2bOUvU4DnrCmXJ2AtKTGQmFFsAtqbcE4H8\r\nx3Y83ZZS83kJ6uy8D03+Wj+oFt0mxN0SWPUX+dfQg5IAQIO/PFCaSBK46cIN\r\nH1EcK6J3VgmCr5uzKUZSbzLS+Q/71ml7yricjRMHFes39g25o6zwkV6y+M8/\r\nRidWOc3CexHrH27PGtQF11cNd+S4YR8cnfAPorHXrzbpb0CGibNIRmF7A0c7\r\nPix/nrCEsEShyF0Xxh1cjL6LzaujjgyZSCK09GwIn3MaaepqGbCXJrNNKiPS\r\n+IvURMl04h3LDXuZmJvf+Jx9RhPwlDmMm0o=\r\n=DBVv\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "15c4cfa3a7c1b4120a8ea6bac28ad4ac196f08ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-6/M8txbPhzKvPKXir5C0aCMDvBOv1bJhrcsJ0rCdgWEKB6wPR5g+nWpRQCo6CZcIZoRvTkr5kUBmG7A+ZKcxYQ==", "signatures": [{"sig": "MEUCIQD5szaeYbq+JzI75ovxarBMlKZESKZ8gay3qdSM32S1aQIgTL4gYmew48C0XH+X+5i6ims6lk3B79EdiwNpInbMYIs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8wkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmru4Q//XDTmAoGkCRhHLZNunAUBghj4vmbG29ngXe7rI3BH+MrWldRu\r\nEv4KpEczI68UU2J7RRn6mU4j+manSUMejPkQ0SIC2NxrfP6xpanIY21urHNU\r\nj35P9uguKRnfqG0GjESJyUORkZtN9CMjdcgX1z1Y9T+4mo6xhHSULqLJWVIV\r\nqP5GXEBWXr07wW/bG25gza9qNaBmeWR6fKrCubhKeRJUYawyxp1czBbN3TeZ\r\nWQUAiR/z4BfSXrIWD06oxvky6UYRlSzBwUZVzyd4H+TsYgdDXGW5XbD0G27o\r\nF/h+9ZCgZ7UvbY1FIo59mqJ/v25WBtU7g46NS+Bb2fZGN5ggBWxkani4rwdW\r\nLK4lx1ntnRXyvUr6u3lJhAwnk2t+IeyXTiAF6nBs2cQcraIIwNAkx83n+/Mn\r\nfFmi4iaFc2FO59q8TkhfHLdcRzhorwcxjjhkQkAwx9nGQxadHvn1D6rLJSC5\r\n2xXTlBZ5f9bMcb4tvKgujiUZtZ/xIQI02bS/bIMfiAhqJrGvISh5k4eeBHXV\r\nB1Ix3KPHfvkMBlSwG9oecauulAdzdMHBdixpr8JPwF8HKFENjivZKRBqYLMs\r\nkCYckVru8EVVv8BmMvjycx+SYPHluDiH81mz6fbiLat2KRpFXtXyInDK92Ns\r\nQkYbeD0Pxx2MZfNNHTBDQmu3pDyorVHuorc=\r\n=7JZO\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c0816bc76c7c741a92e530e3750e39d67c9482d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-9yDAJSDqQJMmcvLqnwOzJBoDQ9vV1KM6csVvd+wHH/VlMD1D+Gs5tBNOU8GOKuie1cWPtyICkZXiYG+QlaaNrA==", "signatures": [{"sig": "MEUCIQDXsFfQ+c70zpbfeEgWe/pNkF6lBdBtdqSqpcHz9YYozwIgElevMvtM9AgYzpECbneoPjYoGnDWhi1i/BnbJmJJcrk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397}}, "1.0.3-rc.3": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "97c6f6d55c04bdb9a9f6a6807d0316f67df70de9", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-6otnl+BuOPUU9iinkuRiUUppMWLPQFyHIXLIhVnxjH4m12joJ76SFb9Puhlyzg7AuhQE5okcanZWKNwayFNa1g==", "signatures": [{"sig": "MEQCIEy1D8kqE3WF1GoyD1IkZ77YvUtEiUbHuPUSi33mepBKAiAKbYZJFBhhek+gVmr8YzjgGMKseALdv45hJyKnmQH2Ww==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397}}, "1.0.3-rc.4": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6ca76912dd613c713b4f48f113dd76981822d794", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-AA6VtqWtAiqrBk7m/bn4H24O26okGF9eYDUDsg/z/RCxGPlTWNaTTkiJiecdaO3kG9/6nymZzVlO2O+zDU2oKQ==", "signatures": [{"sig": "MEUCIFWJf4Xk4ZBAt9Kb/tOzmIiInFWVm+2gJx1AHblxLBOCAiEA9/bqWKGvAOA6AMiYB9YWtik7e9n4G5tDuMg99KDMgBE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397}}, "1.0.3-rc.5": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1f19ac877f4f17bb624076bfe736ee19bda59353", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-ZHE9ink3q9rKALRCy67ezYlCPh3/BQwer+ogvXfxeLoOroUuEqgXQoEEwYIew71qS+LyMouwYs4SoN/ye/0PYw==", "signatures": [{"sig": "MEYCIQD2EzMcRVI8Ro8n2FuniLVzKFaghfMUfcb8/v+ss5FnkAIhAI3W+9U+svXUhpmLOyRUEg3RkFIcSn7C+ogZyZsFs8m3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 10397}}, "1.0.3-rc.6": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d1f2a3938be6b67ce9a109220e22917e33dab58b", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-vtbaZfUNb9Zlw5pfC23laBJJVALKuEcyojCjiHt9dcrBF82XPXuYe+zzNhHkobcP8RYHw3bCcO+wKFMFVCLj+A==", "signatures": [{"sig": "MEUCIDBv5/DCDCkpia+HqEIBxXXAsRr4M2Q49YP9h14f+Vk2AiEA2ffStQ329Tnuaf6FQJuKFlvjsbIn0AhHJcX88CZX/2k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11146}}, "1.0.3-rc.7": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "071b706fee419cf8909a9c6ada161243997517d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-XQjmv9ccpQsnVTO0wM6P76zm0bSfOy2k9YN9egL2jjryA1JzKshrmoJ0r/HL/Grd2BRIqghkH6uS2L32Amwjbw==", "signatures": [{"sig": "MEYCIQDrgRbhXCixhxMuSblqYG1tXLUtRvM34kSmEUO26S+oxgIhANj+Cfujy3UrqoJvkeXING7mEBBC4Yg1ZTdz+ElLVw9q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11146}}, "1.0.3-rc.8": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "647a06d7941f2ade45c356ed9bd654c7b3481269", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-vAVkHfhNWU7Voxx1dp7HXNdTVdokMmVH5DTrK8UW18lzxYZBSF7bteVdm7oWZ8pA3GOSV5LvIO+OzYJFfv6AeQ==", "signatures": [{"sig": "MEUCIFHFbC9DI6bftfcoqN0DwF/bENLh3mdgv/Dx/C7QxPfQAiEAsxqEOs22Iy7UmVvqoDRHbvliGfaSgNyHqxviOtvFfjg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11340}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e1a0f5190c4e2a12f2fbb7fd4c7caa0eabe44967", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-iYmgjVOO2aR0nVFyTHFUgGt8I1V0rMCY53AZ9kq+Qk4Jtcpa1JI1HWxVS5VTr6VRc2Ag8mwYpiS0nwz44UXe9w==", "signatures": [{"sig": "MEQCIBq7yjrLpJJ3oK656RjxwnWQippHtB2g6PjlufWUGnlKAiAth97gTeqj/cpO2S19uoWNjfxoZ1i9Zxk8AGSvipDnAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11340}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2d81279dd3e36c736cb2215442ee16ea309a0057", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-utgtgzYcRWkI7D/x6IKw71PDOlG0MHHeB+3oEa3JRP8zlv/RFgkJ31xOD5rHbgy2KUaxwvEf0zx6KCmKSCzBww==", "signatures": [{"sig": "MEUCIHGaw1W46WwzPv1WZWQ5nWaU7mLUMErenrY11HINu1HUAiEA+R3vHmkik58BQ5gFPUKRynWc7UDOzvxVV1QHtO/UhqY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11342}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-arrow", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2c5f603970777ea69d96d8a571c9f2480ce5ca7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-zjgkHpY5G+Z0Vv1Ps9g7LUNSX4UNZUerePaZUXRCyk5/YmpJx3HFcr0IS4G3dYF+s7uxuYswbBbdTXc6klr2vg==", "signatures": [{"sig": "MEUCICPevFWvtR268jx53gZbC+AezW0mu/ASSraQ/kPncMu+AiEA6q9AdScFbX6bn1KRf1RZ9CE6+l9uDwgA6n6orpwUgjM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11342}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-arrow", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-primitive": "1.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c24f7968996ed934d57fe6cde5d6ec7266e1d25d", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-wSP+pHsB/jQRaL6voubsQ/ZlrGBHHrOjmBnr19hxYgtS0WvAFwZhK2WP/YY5yF9uKECCEEDGxuLxq1NBK51wFA==", "signatures": [{"sig": "MEUCIQC8m1if//OVmdoo0TLDtHeUbkn6voEiqAhpeXpvaq6UMwIgVQqPx57QfW4yUZgyedx+hDdkh4Tiro0uqNUpiAH0jFA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 11302}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a439d823bb781f450f114aa3938b5081dbc9fb4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-JnlyD5tBdQtnYXQhjT7Q1yyQH7WQigLudLRrXa0Sy/EbPT1pi7tjl3t59ANPuPQHkzLb/o5BQvsgwyr38k4aPg==", "signatures": [{"sig": "MEQCIACxS2/QmMQP446n2XGRdFMcPmmO6g0LM/x3fZU4qyOJAiASt8OYo/9XkxbXfCV+7VOxc4P8u99uVXqB+95YUTLe7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9377}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-arrow", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "de8acf7665379c549608c02ecf2dc85fbfaf419f", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-j<PERSON>zKcbIPMGORRgiuqP51cnTxOzsKmA4XqyBOvVAkYXUVHPit3wBQsXUefMiqFxzd83lZB2G8v3H1MLSoeeSo0g==", "signatures": [{"sig": "MEQCIGtFwXXGoD86SyRaJI4dC4aPsSfCUxWGm0RbWU5kLO/zAiAK0V9rUoXFy19dmB2tPhq1ANwV/4n5k1BOt4mV30oC3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9377}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-arrow", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-primitive": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "46ba9161891ee41afbe9e7ba7eab995cd1b3e191", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ZTGDbcODI7LuSQoW1pwtPHi4XrSYNE+1YBTdBL6uTswsJ9+yEhJ37sQktY1e0W9lemqEmTOQRl3dAInOmrOVYg==", "signatures": [{"sig": "MEQCIFiZksOhxZT4If2vce2OdsvTA/iBkODpw9QdRxsftegGAiA7pex3oQzBVD6/gKFwzPc1iIgGhALn8Xx6BspG2tGpNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9430}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-arrow", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ff88d5dc5370d33be64dfe1808002032e37b9b42", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Q/EeX/IDnA0My8nTKuQnoYvrttMOsZpB7xbzjU65cMDupmsx+KvkfMDqy/dWSJxmqgCAvewgjYu6s4KbUSAR7A==", "signatures": [{"sig": "MEYCIQC9fVIGNKXtDjQeAyMufTAECSWFSYPNC9fXgGrAXTMF3QIhAIvAAiABbq5EHhVoIDiTBW0qmOaI0SuDRSywMTo7przm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-arrow", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0dbee94555968ea61f05758a1fe918ba36104f69", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-S93duFC7KvKVevKn5jSG8ej7Sq9I+WgAVIb5BtwweIV7j7xm/kQFgX+eb8MGlFLp1g5eUh+tVXC+bSfDPa2R/Q==", "signatures": [{"sig": "MEYCIQC/UQUXEHzkpVhOj1gfrpQmcBWmpn4S2Xvi7LGAob7z+QIhAM/kZuzAci6x1M1hwT5ulk1ZQizgbePvliJuXx/Ot9Ch", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-arrow", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f48bfe9a70f36b2dbe727a0d423ea8080d992ed9", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-poFDb4dwgRvmSisxXEWGJW+rUexn3LhI+eV05CmuiGjAAz0LkvhmbtFpMSzhdcF/+KiClj3KDQgRnm0kRYr+VQ==", "signatures": [{"sig": "MEUCIQDCOad/wh9M2Ixx92EhApES2nlIkt2kYGQn8XZNDgEt5gIgATCCCkXHm6+rp1iUqIdyNMeJ8CH2qQE8mmb8Vm1JYjo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9208}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-arrow", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-primitive": "2.0.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2aae516524bff852f53630f259bfc5d3517ebc26", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-LgdLaqxZzpxQfQCJBfIeP6cvYzlD9TKGwUQaeWW35/LWq6ho/yfbs+psCNxfLpLvRSTb557CDH9pVdr7J/0DUQ==", "signatures": [{"sig": "MEQCICpB7lDYvJctzxMh/pBIBD2ugiGbXXWuyyBx7e1H7CLlAiBWuRcHxdkTF5cgcpoSzA8i//QU9wKpHm4zNQIx3CBZDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9236}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-arrow", "version": "1.1.0", "dependencies": {"@radix-ui/react-primitive": "2.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "744f388182d360b86285217e43b6c63633f39e7a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-FmlW1rCg7hBpEBwFbjHwCW6AmWLQM6g/v0Sn8XbP9NvmSZ2San1FpQeyPtufzOMSIx7Y4dzjlHoifhp+7NkZhw==", "signatures": [{"sig": "MEUCIE/s3puHaHqE9UOtxmpaqMAFr5mkdg3khP1qjy4vUl8aAiEAxFSnSvWmxbjrLiqxg8FBzpoRohkQUvJSjyQxgAcYTbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9198}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ccd02dc58aa8f8be13d8cc0ce5f4ba3e4b28df31", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-aAwUvkAvzszM108JtS36tsFlQXERNDDb59PFCajSik2XWsNHl8QC6KopaZ/NdSi3Ygc03B0etwt6sq7jlSCZBA==", "signatures": [{"sig": "MEUCIQDN08XdLtHU83gppI1ETZ2p+nSuPPDTnCpai6D/ZiYrEgIgdrpiuXy6cHKwv0P48Q/QOp6HdVkBYWqGnOEyHVoZrPk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9236}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-arrow", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "58e062a5dab1460b2dad4bd0c5428a05e393bc17", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-jidP7OtBqAuLC4KQ3IqEp3D8EQMeBezV2fzcC9CChNe63LKDciKXcFYupA/zyjN3XDDfTyGcnrGT+QUjqtQVCQ==", "signatures": [{"sig": "MEUCIEsLwOR8QdBxr3Sm6Rbi+yDoi804P3LLsaciQrh+S32FAiEA9b4prBe61o6hubI81pnyqnlq6ejU9d7LQVhGnNV0T4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9236}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-arrow", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d569c45c63620ce5116edf8bf97754cc7c0628ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-SZHewWtfwPmWvq2DGgHxoju5QPXaiqy/x3Xo0M6VcK1UdfAbqAkymhgbabGa6uHhSEPRmDc9C8V2hvFTs9H2+g==", "signatures": [{"sig": "MEUCID3aclnAHs9ap0YvxTWJ7C0BARkPun661RpNcDWen+ucAiEAvDfl+G6T+waA+tjz+GUgzgCZ1ic/u/ghxGF6t+p4sbQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9236}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-arrow", "version": "1.1.1", "dependencies": {"@radix-ui/react-primitive": "2.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2103721933a8bfc6e53bbfbdc1aaad5fc8ba0dd7", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-NaVpZfmv8SKeZbn4ijN2V3jlHA9ngBG16VnIIm22nUR0Yk8KUALyBxT3KYEUnNuch9sTE8UTsS3whzBgKOL30w==", "signatures": [{"sig": "MEUCIB6+VeW6DyT9+tc20nXuh7pbju5BpBqQvSRO6ryYbNH7AiEA8iOWgJhP95GjSvFNuqSaGxLFBzJKYRyT/oW9G/IHe6c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9198}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-arrow", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-primitive": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6d79133573e594c8bdebe2311abff745a9e5905b", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-8K/158KNTbZ4zXGOvIOOxrU57ubMrQC6NMGgkJANchuPCy1HdDMGo+yolOlidfRR4UJiT/zV01WRg9SIJX+cDQ==", "signatures": [{"sig": "MEUCIFp07PktOsekDbufqDqCOziNUG4ZrzwFjWt0HM0AnDQoAiEAmMVVYJFUvf0Zq7mPmmmRPEcnRZLzLg4KyBwMrTdKQuQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9189}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.1"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ef02ff693ab8ecd378a3048c470e63b856e5dca4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4lqWZcAh3Oz+aYrXh2rrH0w4e1YfQJANnFWSGco3bExwiXm3c3Jb6XjQzeUFGmMR9Ef2Y2BCF9zW69+aHkYqZQ==", "signatures": [{"sig": "MEQCIG4kMO4yI28nW1lwYhOXf8GhACzXBybTcFL6Q0kbT5WYAiAg7lSj26LkIOzFmeZWBzkxdzST6EgXFAe2WcQJDmClEA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9449}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-arrow", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.2"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "26b55885a8c55ea0d9c524da54c8056c5cf1e284", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-5+dhloPcUvPOKg97kT5Qb6i2nddCFExaPYWLKG2Iv7Ms7XcGTXYw7H2crg2zI77LxU6oMDWEgAOfvbVEc9PuBg==", "signatures": [{"sig": "MEYCIQCW0XHJ64GDxc2NWAMLe99puX1rjOFcyddd6cPxZtl9wAIhAOKerijFS/ZkkbS+TmQZKTi9UliidAzwB5qUcoJRX4C2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9449}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-arrow", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a9c69dce1a36a8ebb1d2c91177ae99d626e793fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ZJ20GHTlvERDjB7OOUrKr7Sylo0eD7KQ42LR7aKEPdsX+l+x5kpHrCSfkm9CPKpqVu/aujr44myO6TpPNgQ0Vg==", "signatures": [{"sig": "MEYCIQD+opGDnpnGvCU2IqM2a/kgUx3hFQUmGyA7NA1xyKevRgIhALoowubVHYT9F1wh4wLFot5jUiZDJyrN63mtdR9JIdw1", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9553}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-arrow", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.2-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f489645390e5aaa265f6aa8fb529f87f77af9ad4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-69o2NPYj40Ncu7G16qxBcrkCZuMJACSYHirHStQSTj893iP7ZrXPTwUvfy7CgleFLdINyRER0x7wxcI5VfsIeQ==", "signatures": [{"sig": "MEYCIQCecUNwxTj5Lb30mXJnxZop6wL5TWKh4pou2oCxCFum8wIhAIkfGi7M1s9QzUoB5SfdqtMMgxKH5zeiFK3kn7gcWOfQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9553}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-arrow", "version": "1.1.2", "dependencies": {"@radix-ui/react-primitive": "2.0.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "30c0d574d7bb10eed55cd7007b92d38b03c6b2ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-G+KcpzXHq24iH0uGG/pF8LyzpFJYGD4RfLjCIBfGdSLXvjLHST31RUiRVrupIBMvIppMgSzQ6l66iAxl03tdlg==", "signatures": [{"sig": "MEYCIQDW4voY1JrtwmsjPk/LN2SEaYgoIuVcgTfsjxfL0U98IgIhAKj3Xcmy/e2KwrE+keDudR6YTlSx6j5NzuoR3pcJXfK0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9515}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a7998849ef717f5f1d51d218d121457f47d981bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-0re93s4mo4hcRLenbpYtK9gWnSdHKBAzEb7qXd65z9A+L4zXt1uGrtpufo3P/PpuUdGgFocED0/5g8PG6xCjUg==", "signatures": [{"sig": "MEUCIG8beTCtOjJGNelbCBxZ9HUPFMS+i+e+lVDe+Vudh3fcAiEAmL7fo9eM39cutopieMWZem7ftGz0YiLed+WUhYpVuug=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9559}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0caf855b3c93f58d5e2dd77c72f39aa9c1e65b19", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-PPToOhu/NjkjF1p5hK6SIqAFXXE+IJSyuTqPhAFexL9Nl5RsEqSeK+Id/To1QQk6zRHtXqPLjuznFKhlzUcIyA==", "signatures": [{"sig": "MEQCIG+2F94QUG7VxyswDREsOoLh4BPzL/44MgqhpvYrkokjAiBgrKMJyFrnRCh25HfMvIuYlVYbuU6xNGPwyzXb9Hr/MA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9559}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dbab5596bec9a21985b7c73e6350db5c2bc8284a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-3dnrK7GK07zb4O2FyJSiX/m7idCWpXogQQf+KLag6HJzFAyTk1H3O0T5fjTBIUiXqtzZBF1uVeVYAOb1hZ1OxQ==", "signatures": [{"sig": "MEUCIQD51Zg+zI6X/ZlOTyjFtn9lYLWKC5Wt/HgTC/f4rnm2EwIgDCVTxufTUxJwHibIWo73hgFM6D1CS9QXn7K/cnlwQwU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9559}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "df9129257e0c7d4a2848a6250bdea79cced8475c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-WrUuVQ06idhXTc8q1igxQKD2GJw4E9gSHLAzgNgvuSGE/uowSQo3rKzeJb9dUO+sr6/vsiWaVov5OyV1WbJFcQ==", "signatures": [{"sig": "MEYCIQDpXqYgENcD9GE1rawf40C3ys14Jg613uE0woR0tx6ICgIhAPB5W+INQcMXgp+JlLtZ/wzpTwSQFs86zeJhUxGq+elP", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9559}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.5": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.5", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5bfef8255f7ad5de68c9f504a54f3f54fb8b631e", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-PwV2BbAHnoaVhH9YsF8txngWD0Vwdnq58UhSEDYnzw+pMEnfpL8uwqz5EGiufqtm5++JZ8c422tyuYXP2CsXmw==", "signatures": [{"sig": "MEYCIQC2a8h/Kyps+2x+b0Qk9B4slaOaCYeE7aMnCA9kmEOm3AIhAJZu3lrKiXsNmVz+NdQ9uo4BgG4ydI3rrJBkZjvyWgX5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9559}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.6": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.6", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "262af6c73a88d37810d856401fd6524c1f27d180", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-Po1mLzP4z8t7DgXIs01ywyC6TdJmp36ImQQvVCfhbkG9pQf2jYOTG5JXXAHshLSLoD3pzCDd4h4+m88jPMH4Rw==", "signatures": [{"sig": "MEUCIBgeX9Z/djiF7SXhUng+qyNnVOL/J/hSAM4rVRbZCvcVAiEAq0V2HoD/BKbi+667oZ6Je4YvoaRiG9Tatl6JbWgcFcU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9559}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.7": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.7", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3c259a9c1a60ebc868b90bfaac9750a596549b26", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-j9WPmm7U7cpv4YyQ/YEAcDodNGenDudCHv9nY9k1FhUycy4LSrn9FmJ2W0NX15aRFAcNapGSyObeXpuBZkLAGg==", "signatures": [{"sig": "MEYCIQCCfx+lpjgFk7MWZlaDsf63TBmWN47oHSavuBBdbrxrhQIhAN6VRjksqeOsftECYxGHQBGsAapt9Ndbd826GiG6lTmM", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9559}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.8": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.8", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "edb16575d9f47c1fe21b0ae358646416601759d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Q+jkpkHXkyygM4AXkfDulHub+SWEiIM2s7OUH6AgjSdNiJr1yzlwZXkMpqLIYjsR5uoBA6N5o9mmcdImUHUxyQ==", "signatures": [{"sig": "MEUCIFjN1by1JjOCrUC84Mk9ce37HLBv9cDdQJuEFOLzslHRAiEAzm2hblVykfUY08dJEGNZ5no1C4riaKogIuZipeo7Vt4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9950}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.9": {"name": "@radix-ui/react-arrow", "version": "1.1.3-rc.9", "dependencies": {"@radix-ui/react-primitive": "2.0.3-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a7f051d772a238b1d4f4591f6235f6a9266523c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-dLaJGPzU6kiyhSZCQUqwX8KxWWIaiGseRHFkqfizVSV6sYXuG5ld5oYa6YYj10x1BEpuUxpImI0nuyifZcJFwQ==", "signatures": [{"sig": "MEQCID2xNg6d5MTGZ5p4unI8W8QgXX3GdH3YmH4CZhOmGSMrAiA/OveLqKYenVhn2twHHlXlVEFm/1lheLwubl1cqO87Hg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9950}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-arrow", "version": "1.1.3", "dependencies": {"@radix-ui/react-primitive": "2.0.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8926eb1d87f73c2e047eac96703949f168c85861", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-2dvVU4jva0qkNZH6HHWuSz5FN5GeU5tymvCgutF8WaXz9WnD1NgUhy73cqzkjkN4Zkn8lfTPv5JIfrC221W+Nw==", "signatures": [{"sig": "MEUCIHmQcKAS0nXvOfIIXIe5V5uDi9WyYPszKRxD8eoECcopAiEAykoGaM72kzNSy6fY3hY7K0TJ7zxgFHsyKUDmq7q5Nk8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 9912}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744311029001": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744311029001", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "81c533b763dc3ef51ca22a2e8015087012da18d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-YO9ii5Fg5N4CDzMm5lU6ZA7SUtrFBs42IINRrw3/T6nDk+HrxO66S9eSvE9/7UPEKOetvTQbYglinbpwyrPlIw==", "signatures": [{"sig": "MEQCIFRawQLxnhWwWAR79HHbI1S4HuvdFEGV4OhXf/UAy6nOAiBBzzk1e3aZuTD/YQhK3ylR5DfZqk3Xw1uWbeIP/NMXDA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744416976900": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744416976900", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b9c56374cfb08b9dd517bb3b4a34717c0f6ec8e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-qXzGUrwqXJKCR5uh8b1oEDiLMpLNtBSTnBwwtbV1Lzon+LN7gskvCsSJm2Y7GP54tZM+id/gvnvy8MKYz+eZ1Q==", "signatures": [{"sig": "MEUCIFCJwNmMgeHjgUM6ldnKGcbgErqS0DHJPh4COuIAKw6LAiEAwKfyzfVkYbcKuaY1oVLTAvgtEYCb8lb9cv/E2GH0VHk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744502104733": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744502104733", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "93688f309b9e734f8f90daee2a38d2fbefbf25b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-eNbPOwBtMSdakpFiCTC7Cwb4846zuq+5Ixs5ybRpcdzkdF1xyTZhIvLCklhPIgyjWrrVDIREMDgqtHIDNA3I8Q==", "signatures": [{"sig": "MEUCIQC9NEe/Q7ai5Brq6MYO++sdgD0N7FYER2B4Op+vBZZLeAIgE1JfGWP10efjFr/JMbtEYOQ++l56DL+1GRwistc72dw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744518250005": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744518250005", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "341763997e637eee4e92a7515f61ee1c8db7cb49", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-m97A6QDCOY1/KZ0lshtA1JF1ciW6Sb5wKryUhAhotqvEEZpEVGsBuCmJMI/BNh+MLtTmHe6uamq4oYsTGiuQ0w==", "signatures": [{"sig": "MEUCIQCN/KWpMT2GZj5EaPPx7BtdWw/+V9Ap83VNJ91A4HzgTQIgfkQrUoYNSyJp8wm6sUjGnQbNgjgIY54UxiLiVnfIflw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744519235198": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744519235198", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "76ceab38788868fa36d249d28b52a6bbb4f74140", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-0i0qNez7Wmu3Ctja+Y9Zyv5KnnSSF0NHuP9VCSbuJE44c3KErc4lrXggW/gMPMSosNcwYv7ANTtjVjJ2tZZZGw==", "signatures": [{"sig": "MEQCIBUGi4ELUbCm6w4NrYlyD2ijkNIpAkCq/jokseDOu8CxAiA/tj1rtu9TM2mnH9M8Q+M4wQzybFuZGMBHzyEcPwK0Qg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744574857111": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744574857111", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3d347bcbf8584a0941739607edeb653406fa75e7", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-hvXWyKKybQvHVlatO4pVdAxkrUHy+MqTCHzaBvn9tux6XpLsgJvJLHy1sKthtu+5OBWm8B9ZM2YG6ZU5638O2Q==", "signatures": [{"sig": "MEUCIE7G2cnbZWFzDKBLyZZGcT0xt65fuOoKvD3qfss/A+FoAiEA3LlDHbC99etO0/KejZng7aZt1AR7yrJemF+MdzL2dKU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744660991666": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744660991666", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e238458868a80041516975027f270805e6a3f5fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-7exhSScbP2qZtS8iY6i6AujYdNKSK3K+RV7FQf30LmobNluG2wEMj0Cg+cVmI2BDfqBnCcgRUZghZvQSKGoRSQ==", "signatures": [{"sig": "MEUCIQDopPFd32THF3ZyiQ7jWXYFOqyxp4Mh74+zq6NskTkQfAIgTmhfNlHGQtHwWjqE0HK0aaKOR6piLyeKtJXcVZwFqZ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744661316162": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744661316162", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8aeddcaca9d96e7c4a2c1d2c315337c9b3e62b56", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-XhB2+b5WeEKxgPCy2PajLP4Ld/tIdKGx8mcdUaj3CaE/bLSBZjKPFVoIe3QmPdy5yp9rxwxW4crEeSCdKFV3Qg==", "signatures": [{"sig": "MEUCID+TfF3qHeShpuZUNVWn7tV2fhJFudXvmuZ6AQ7dWRe8AiEAj5TFV5sRoUTfVQV+LRazJiZg2rglSL6dqJbl3kRerVk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744830756566": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744830756566", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0538d3bdcf1907b76f7582aac00aa847364f3ea2", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-Cq1frOZh2u00ZMXVJgDjWs1PYHI72D3pLaBy1/wHnx+fntiNpI39IXPots4VNHU0YRdLpWfxSw08DcHie+Iyuw==", "signatures": [{"sig": "MEYCIQCwaDH1EpH6W8G6EF81J5LI/NnHu9XZog8E+9Y8ZZ+qTgIhANdDVcd63j3SWwKMVdXtwdUD6ckE6TwGICHhR1AVFHRJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744831331200": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744831331200", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "24b675959985329005178bfec8f65984ade71aa4", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-lfO53HwPabwZIAZUeGdahgHrNCKJbJVggK1GkmuztClCvCSCXzFIAwzsxM0zPIflMxwXIDru8qvz8wp5ABzc7w==", "signatures": [{"sig": "MEQCIAxCKaXk6m2HOCdD9ZtCLtEVia2cPB5OfGr7Enmw8GjLAiBWu8hCjX7F643MhV6P8o8TOUDIK7SnDUBeolyswUUWTA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744836032308": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744836032308", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4076f47425185524fce71a64097476b664820bea", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-tKBEBzwqsjgdlJ3x0Ybmi/Edv8HENSPiNLSyUm375Fyvx9DIY7cPI1HQNOnldSUoMSpETf19c2WeU12w/1G5ig==", "signatures": [{"sig": "MEYCIQCM+nwfWiEFSEdZ+PVCIL46oxfeQST0FwNr5U8e/3P69QIhALHLwb+CYGD2TzfC0cc+Yfg+JGa7yNKLEvNZo+rxAv0y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744897529216": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744897529216", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2ddbcd06234af29f7db6cf7f291a17bb1394a697", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-0VUqIpCl1FXaWNG5WrDkUdy6NDeMtqc05bpc7nMZpKCwKAbv1st3VAloUO7J+Yg7WMdXUG1UQD3WyJELTdSOyA==", "signatures": [{"sig": "MEUCIQCR0AdCb1O5Z6jzcgfdyqLPbl2Ac4Ci80VdGMb9z3qToAIgWynVtoqHPgJaA+6O6uBzUj0u0EdPIhJjKkm9dUB8hR8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744898528774": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744898528774", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "afb75fd4ab29d3e7082bbc3dc171ecdf6badc4de", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-rVjZqkYg8qidz9nH2NleM5NZ0Ji3K3trvx6Yf8jJmtiOGT9MtCME75nOt85ekfINyZK5s+4HezXvo6lZbc4w5Q==", "signatures": [{"sig": "MEYCIQCJ3NnGvgCoSJgUAlM3jjV4HCde7flA3GwLAzQMxIJfhwIhAORdTY0nswSwaGTr9RukzCdal5uB24A+s2qhXEdFxRKL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744905634543": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744905634543", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "74cd2e006c7075d6eed7d7610e005c29e3949ded", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-WlxuYm5YmL2Yh5la2e4w/5OsBXWxAbzHhHg7oEzxmFU9uD7XBSYgAdwH1wEoz4Kb2AbMA34htSAJ8dxqFf1cBQ==", "signatures": [{"sig": "MEUCIHDJVSFA3PGFpZ5IH8cWBWl+rNA7o4OjzqayJvGcz1vtAiEAzH4a/oFyB3tejrqj0+KF6wnNLLxszUPacXFehzQRFac=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1744910682821": {"name": "@radix-ui/react-arrow", "version": "1.1.4-rc.1744910682821", "dependencies": {"@radix-ui/react-primitive": "2.1.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "68dfd2f317fb94cd41d8b3f9ccc4a8da38e817d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-DBaUBUaUyAXplyWFmW3tot5ZDJu6nKaJBcudsdPTy9/6Ym4AA66NZz74iFl1AOSt09aEgi5VcUvuusTkMkMnaw==", "signatures": [{"sig": "MEYCIQDGfX8TjEOirqlPI6ELZts+rftjX6QBGy8uZk6s7n9z5QIhAJ/sLSF1A3IWrO/On4weewAieTMMkPZVIwzpasZbrwIJ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-arrow", "version": "1.1.4", "dependencies": {"@radix-ui/react-primitive": "2.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "08e263c692b3a56a3f1c4bdc8405b7f73f070963", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.4.tgz", "fileCount": 9, "integrity": "sha512-qz+fxrqgNxG0dYew5l7qR3c7wdgRu1XVUHGnGYX7rg5HM4p9SWaRmJwfgR3J0SgyUKayLmzQIun+N6rWRgiRKw==", "signatures": [{"sig": "MEUCIBUuqG6m8uHcJSGE6/2OjyI6IpIBAOAznCrkW1TMO5lZAiEAwM07nxiUMYrKZsU/LtfqHwkoFSBj5DKTWo6QICL9+MU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10469}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1745345395380": {"name": "@radix-ui/react-arrow", "version": "1.1.5-rc.1745345395380", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745345395380"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4232eb617349133177a9d2f57ac5cc3992a16da5", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-eOfedDguwgCyM2X/C2xJlWx6j3imoXhuSqkaMLuIG1DJ7nNRHT+fsn8JnOYTLAgvdUUfpyadc5bpLgsAbZuAoQ==", "signatures": [{"sig": "MEUCIQDUZQX67ZIQfjeomhl+1NcIvKOKA9QW72t/PRHzE28reQIgIpNi/OhBpxl6lwo0eR3tUpCxdAD+u0z4rzvzN3ar890=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1745439717073": {"name": "@radix-ui/react-arrow", "version": "1.1.5-rc.1745439717073", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745439717073"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a5803cad6dcbdeb78590597c65737c5b390528e5", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-nAxo3WgdHUVFjas89QtG00J86R5sVCuKeKiAb/+5M7EaV0CkpoW874vQBJTwFpgrOoDfH5QgbDmXUo9zlkWQuQ==", "signatures": [{"sig": "MEYCIQDhXmAA9LAOmKx5iqdhaiEbXS1307cAXN/snimtwy2pVgIhANgMI/6KU9j29GA99z2WP4FWnppnkk/GRAdJa/6zq3kn", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1745972185559": {"name": "@radix-ui/react-arrow", "version": "1.1.5-rc.1745972185559", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1745972185559"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5a5ea22490bfd89a3e1a012752fbec7fd524e23c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-QN+JlQTtnp1tKmt11+KJU1DN458alK3dkN4EM6aM2/IJJhdPAiMbehpJT9aAuJjZAveHaFXWlynnrW4zApg3bg==", "signatures": [{"sig": "MEQCIBHAxI3F9jpbFp1WwxZ1gLzTmUlaglzSseYYbHWHIfLVAiAEihYy8jWHJDH/aTAakQoz+2z74qthLLKAzodrWLC3pA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746044551800": {"name": "@radix-ui/react-arrow", "version": "1.1.5-rc.1746044551800", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746044551800"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "53ba2e2e05f31a772c5dcb31f3cc497b4773fa7c", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-eMqOokJP5St/yn4UBiYyrk5mEC6IsWAs91sT5ZIzvzUORkc94lTLxJqxg2+XQ1fFMOohjxyrReXuVN0ALRZM2g==", "signatures": [{"sig": "MEYCIQCGWGoiDjubJ+pDMCUrG7uvDOeajalV/WAnKC1aGXeWiwIhAPDF2MqbYS8k/0XJ1nm6I3aWmfOAqemyZTNnK0CNo5HS", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746053194630": {"name": "@radix-ui/react-arrow", "version": "1.1.5-rc.1746053194630", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746053194630"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b309c75ae08ee58920e6554ea9ce8e6dd11df286", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-SKg5X11uqQDDqj/BjZoJvTbqknhVlZ1bPs1mo506F1Z/cnXgyKUmwgLHTVNR/5i4gqQhUEQ4PchlzQ3ITEy4SQ==", "signatures": [{"sig": "MEUCIGJnRIv9VGkSdzR4Ltxhwr3Gg2a5deW9xfESmMisUIQ4AiEAxi+STH096xxOMqN1NJsMVuYpQl4mPcOM2ksxx5JR9YA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746075822931": {"name": "@radix-ui/react-arrow", "version": "1.1.5-rc.1746075822931", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746075822931"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b40c3d63493d92e9b4c7e321e9e0ffa78461de69", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-emllVom/sAcl1bzr3ABZh3EFVbFaj+Xx+ovuGSuJEob2rIqWJE5azOXb5NU3FhfI5V6Jd1m01situkI0OnVzAA==", "signatures": [{"sig": "MEYCIQDvU80v4KnHmsmnRusJhY3aCmFIrf7m0BvR0+nL2fGWvgIhAMQDffbdr3wU/XHU9ip/EVqO5wnpLwSzLWrOWRjQXsG9", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1746466567086": {"name": "@radix-ui/react-arrow", "version": "1.1.5-rc.1746466567086", "dependencies": {"@radix-ui/react-primitive": "2.1.1-rc.1746466567086"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3dfbbfd1a86fb0e39c2f32dc8bfaa30aeaae437e", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-MQ7Z8LFEgwReurres3/2L28g1DmUeGXd1sA8x/OBpepBXjiX3ujKIpd5lTN7FAiV2CoGPc2BdKNmnJrx/M/jVA==", "signatures": [{"sig": "MEUCIQCeuWRtg3vzVUxO4Gu9Qv3vzJveZsOE5FfVElmPUeZfpwIgOv8J0Qtsu2P8OVaORKWxBPLmzuVfBrVPmkIb8ksDj0E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10503}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-arrow", "version": "1.1.5", "dependencies": {"@radix-ui/react-primitive": "2.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3d8974c45f7bd18914132d52f786c2891bddcd4a", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-GRdeRWdAH6gTTJQRGjyD5aHnhaxY6nsoAOlP8gozThGOMIloorbS70fc7jbBlFBtyA1uLAkdiy/3JF5eVzJVXw==", "signatures": [{"sig": "MEUCIF/TMGKNAd5xeqAhIAE+FIjVzLE0Y3aQvDzVSy3RSa4zAiEApyLISk69+6qzQPJ2mG9YvwyQhcyJ8HfedurfIg/2OTU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10469}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-arrow", "version": "1.1.6", "dependencies": {"@radix-ui/react-primitive": "2.1.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4b460fdbc1ac097a4964e04ca404c25c2f6d7d3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-2JMfHJf/eVnwq+2dewT3C0acmCWD3XiVA1Da+jTDqo342UlU13WvXtqHhG+yJw5JeQmu4ue2eMy6gcEArLBlcw==", "signatures": [{"sig": "MEUCIDlRME1IMQE+3dQ/oAFUCiF5NdLs3jkkqVvOLoMAXuUhAiEA+j2oJl9Ef53MQu5oW6wNpQ4HNKvUTVQP8IY0SNNFlZQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 10469}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1746560904918": {"name": "@radix-ui/react-arrow", "version": "1.1.7-rc.1746560904918", "dependencies": {"@radix-ui/react-primitive": "2.1.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/typescript-config": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-cfk3oVI9J1VjiSCqX2cSdeERKU2ovLy6S/AXXJWAV8ozxdNIbs864EudKMriicCrZ5G1rYf5ih1PVHir7l6mEA==", "shasum": "895650a08ecb7f4587f44f622fdc51a8c1f0e155", "tarball": "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.7-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 10507, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEQCIFJVTVlZgRbnlplGeujbHevldBHcUI1R5UVp7HKC5J7FAiBLOnRJ0+wCVZEIxxPW5nga7WynhPnqAipcbsKEyZ422Q=="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:48:38.706Z", "cachedAt": 1747660590777}