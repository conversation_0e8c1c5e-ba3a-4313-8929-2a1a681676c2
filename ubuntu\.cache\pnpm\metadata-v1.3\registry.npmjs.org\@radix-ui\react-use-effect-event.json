{"name": "@radix-ui/react-use-effect-event", "dist-tags": {"latest": "0.0.2", "next": "0.0.2-rc.1745002236885"}, "versions": {"0.0.0": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.0", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/use-sync-external-store": "^0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "ff0c542782a7d8dbe6b6eb3b4c3cce368f6920c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.0.tgz", "fileCount": 11, "integrity": "sha512-hPHasHVYa4dyHRSq9bZNQ97a36UH95EOBdbfasnTer2iqhx7GkwC0AHCiF3gG+Xe931op4dW3erhXfBhJuwuqg==", "signatures": [{"sig": "MEUCIQCyuLbOGgQT7ARoyIj01QRWJ9qz3uaD46RIuB2nvVruvwIgKXLpxw/r3lkynk+j5t3wSB2jugVvGw8bMmnBGTYRrdA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11674}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.1-rc.1744998730501": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.1-rc.1744998730501", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/use-sync-external-store": "^0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "6bcbf59eb62c3b93deca6f710942211591afc969", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.1-rc.1744998730501.tgz", "fileCount": 11, "integrity": "sha512-qODZ6c1iFCaWwlqY7+HsKy6nh7Fj6jNXGvhqVq0caCo94s/5DwnU9O6l9GJ470ffjxapLcIq8RK2Y3TrRQISfQ==", "signatures": [{"sig": "MEUCIDIKMeCrkHVmoljwbBRUDqTaSq2BDl7/HfCnxIE/5MzWAiEAyb6iHQWYArAMqTt6eGiROeX+7nACpDfSi1HKLgF+cO4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11338}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.1-rc.1744998943107": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.1-rc.1744998943107", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/use-sync-external-store": "^0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "562b64219384f8de467fa8405560cdb1d2381f84", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.1-rc.1744998943107.tgz", "fileCount": 11, "integrity": "sha512-wWVof81I1/pU6gRkkGXltIYbHZ0LGg5zxrExg0cRXu9iuDPcaDevmdy6PL1sz08n/coJiHeYXvI1uVNXECIazg==", "signatures": [{"sig": "MEUCIEYKJmlkY51R7kRoBoHiI+t0dy2J9V31uAk7VkwhC29fAiEAqereka9FhSigQMABlRXiO5pM0JtLnbrK/Ezvkw+O04E=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 11104}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.1-rc.1744999865452": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.1-rc.1744999865452", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/use-sync-external-store": "^0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "2249bf8b6530ee40e6f2905f40093b39fd2f1d9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.1-rc.1744999865452.tgz", "fileCount": 11, "integrity": "sha512-cKNQGBMl5OBrM2sI6hbrlwlSfepMg4unOCaGhRMzjHAQvU/t0oAAGc07KGJMqtY1yNP1FuuNweuVyaieiIN3fQ==", "signatures": [{"sig": "MEYCIQCZDgbijxcX5l7pAVSyGPWq2xSylBuRzS47RvZz1625ywIhALYGOjL0tvDjmAziBIYcZdYkN3JVqU/WlaV5i+EuKmZw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13209}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.1": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.1", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/use-sync-external-store": "^0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "23a49653b16c189e669cdbd6e7d7a6526b78e7b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.1.tgz", "fileCount": 11, "integrity": "sha512-GUYSVhFnN8ieFGIcckBHGV9OyOiIfK3Y7k/zNt1jM4vGlGNrvoyUnTwrbsZ+OP12Hzl5tEYzAOc98zF+TPRtFA==", "signatures": [{"sig": "MEYCIQCKQKZRmHyBMmCV9vyYhC88MOLFg8okOoZWVSa73wB+6AIhAKGxuV0Q4PV6/C8rhaOjfdKu4W4sbUpu3F7bWq8dDfJj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 13192}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.2-rc.1745001912396": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.2-rc.1745001912396", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/use-sync-external-store": "^0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "0453a8741d129eeaabbc1ea7a0dbbfba5663f587", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2-rc.1745001912396.tgz", "fileCount": 11, "integrity": "sha512-CKy1h9nlPqFQnEHuOarnY3oEbofFEZCYY1+j1/tVFN8uoFN9KpDD+r4hovg6nqkQCyxBerCgYavwWrOHIakzaQ==", "signatures": [{"sig": "MEUCIDQQGeCvawGpHx9pDsdlBWWkgAsJdGeIi07DPUN15dZVAiEA1f7P8Wks0KLcSQRl7TOyZUQ7HMK2G7hnewMCvPw7uQo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12077}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.2-rc.1745002236885": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.2-rc.1745002236885", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/use-sync-external-store": "^0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "8300c2af79631b9b15c89af69454baf7b63e82d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2-rc.1745002236885.tgz", "fileCount": 11, "integrity": "sha512-P6KSj/t83CWBd96na2gKxCXyMtSOAKy7xft/79lQOnVdBiCVqnH9mzCCCfES1Q4zfQaNlhE5I4ERI0/akrQhWw==", "signatures": [{"sig": "MEUCIQCcXO0iYCR56wRqNT/573F0bGPx9GSnAli3D/unt8ExxgIgdj71zWodH6ihTrTEMMp3khX7K5/gEa+Y8k+3QswPSD0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 12077}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.2": {"name": "@radix-ui/react-use-effect-event", "version": "0.0.2", "dependencies": {"@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@types/use-sync-external-store": "^0.0.6", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/typescript-config": "0.0.0", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA==", "shasum": "090cf30d00a4c7632a15548512e9152217593907", "tarball": "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz", "fileCount": 11, "unpackedSize": 12060, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCVbVXKXS2IyidJyZ+P/Jj2UD70SFHCpzoFudC5VTNuGgIhAOr0ULkoqU/qcvcbHLCSdphzP/sf3mDC7lP05BpkeX0l"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-04-18T18:55:32.858Z", "cachedAt": 1747660590680}