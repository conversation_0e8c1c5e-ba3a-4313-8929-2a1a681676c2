{"name": "vaul", "dist-tags": {"latest": "1.1.2"}, "versions": {"0.0.2": {"name": "vaul", "version": "0.0.2", "dependencies": {"react-merge-refs": "^2.0.2", "@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "a278e4d1573d238fbd4d6c3361eb3b5559f5f1f7", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-3f+b4eKUaBtBqOio0wXnPCXWYtQ905KHy/IFvaSaywWZdappvXUH3srpb0zCAt3VObp/RHMHujp++EdrWg6Ldw==", "signatures": [{"sig": "MEYCIQDVTubfsnAwQy0zk3pIObHfPDZ6n5dWCL4LK1Gqg5Ry9QIhAIKyfJ9tmnnoryG3oK5acnoat5LEclU/KKZjrjlBo5tB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 84261}}, "0.0.3": {"name": "vaul", "version": "0.0.3", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "a74709050d187ca28f5b2a1856824a47a75f5116", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-KPbUIZk2RdBwPIjZcEiEDxWCsqVVpnDF1T+y+BrsCCvCSCciFNr0s/QeQox1VAOANs5xKsq3NxFpKhNkVDNdxg==", "signatures": [{"sig": "MEQCIE6INWj+m4ieKwEMB+kXF1tKjOJM8ltbgT0q70zz63KjAiBE+dTt8GKgl/tBUavJR6swTYKnto8Dg6k+x+p2CE39FQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 88028}}, "0.0.4": {"name": "vaul", "version": "0.0.4", "dependencies": {"react-aria": "^3.26.0", "@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "75175b33a4bafb2ec1bf306e374093d3e7a67529", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-PivQWKGSjF6BfZ9GEGk+ReWeky6Ss7x4I3uYQR3GqerJQvzl1ie5fACz0FdtCOyF6uzYZSkqq2ca8ifm59fI3w==", "signatures": [{"sig": "MEQCIEPQiVsfH0HAEWdTKp4/AsklYvcYzlygc4IVv19cJVanAiAxVr+pf55K1hdfKY2XffigSoTtsLDBs/v6tM1VzNWuRg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118276}}, "0.0.5": {"name": "vaul", "version": "0.0.5", "dependencies": {"react-aria": "^3.26.0", "@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "09b065c876e296751ae95677211b36e35831620b", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-NJQLfuWOUpuacg+6SYUeUhrdY8UJiyIW9PzI+FuSlkRiVogxAZe71lpnmqhK3LQZ4vFN5O+ofrZTa0Io2k0BJQ==", "signatures": [{"sig": "MEUCIClc1AfgNo8DVnhqL27VkyYkmeMAYtB6rFB3GMN5OPNDAiEA5E971dj7LfskZaAfCS7DAmilK9zJ5Y1XwXb/4zLzOZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 118994}}, "0.0.6": {"name": "vaul", "version": "0.0.6", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "7b9a0b38bcf4637a462062a10419f5f9857a5904", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-YTI1wkukcgw8EKrZ4cgnj/NDXue0Y6vicGkix1iu4j2oNX2Wcdr9FXdPlvXxhzBdDfEl+dIMu8zjgri3UIawNg==", "signatures": [{"sig": "MEQCIAg5iIGcw6rP8qn5h2W3/XQXmSVyDPEO2btwKdm0Cfr3AiAS3de0z91cgN0cP+FJh6la1yBo7BVRg+hQUWYFaKWFdQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119515}}, "0.0.7": {"name": "vaul", "version": "0.0.7", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "adf8a1d79fcac62ae4d317f64f6bb14d4454e9bc", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-OuVdSVSkJ6+8yj0wr+6QvM1BwQPtHJ3vksbb6OJRbgV8/0D6PdeEmTNdgICrRrvAuEKOHI+MKCS2SGahQPfOdg==", "signatures": [{"sig": "MEUCIQDo9EuVwos1bn0pOc8wegts0z8QUMUrLYoWSBmb+335HAIgN7Dtt9pDBv+TVo6kDC/6pSnIK+XyEwfMhigEa1nV+i4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119472}}, "0.0.8": {"name": "vaul", "version": "0.0.8", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "1bc29ccb5061ca0303d73683218f4efd109d5a92", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-DS6zRRy5dYMZW1XuyW8RRbjNRYgLdAvIpUeiCCZ+n2u9nb9VJ3NDHekE84LOGQB8ZgHrRx65ZIkvzbkIIt3ihg==", "signatures": [{"sig": "MEUCIQClj6Eb+oi8ChB2KHtnh6FDul0BF2sRxfWGY66OxD3keQIgVqVBRlU3ZYaDveKjwQ76eMbJSqru3fYsPLtMNuVtovo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 119202}}, "0.0.9": {"name": "vaul", "version": "0.0.9", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "4f8b069604b5537f3dc9f4eb5503ca9d4d0ab632", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-sOPeNHOq/ROoFFZobmTgO+y4bjdxjyYcuyFae0rQXTOK8WEgZWRWQPoizpp8snluF8bGN/gb+OQz8mLpoYPosQ==", "signatures": [{"sig": "MEQCIHgnpE7QhBY2jrLe8mtoHiSTm/Ovemm6uj4O6hMPdo+8AiBAi2A1Qq1mR6ofyGzA1oocNfRz8SGDBQXXExP4SvH9iw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120784}}, "0.1.0": {"name": "vaul", "version": "0.1.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "d9157a16ae8f9139f9d7faf5fa98aa8ca7dfb943", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-x78j1x+AAvjgxSUNorDKisK0Lx/e9f8aIXCbelXO4zIe7klkmd+lB1jrUYaAnt5GkLuXBdUEw/9wDJNzNFtoTw==", "signatures": [{"sig": "MEUCIEk4wiHGW+W2vFQhj8EVid86UYnUz/sjZjc+s8SI820SAiEAsJW9KXhXPrzucbBhYhJXvQMEfFh4wHJP7Rb+zkpo4Uc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 120784}}, "0.1.1": {"name": "vaul", "version": "0.1.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "74260c12b4191dc5645c7404af6f003175dad885", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-cXh5GOAxSE4yJUahdlwkzexCfJMEbAzs1HPS43tmmwuRMLXG1Z+4HQMu/xF31hX72Yj8P22HAW71jBXtV5CQyQ==", "signatures": [{"sig": "MEUCIQDhVZb5YBVJXUe/qQhIfl9wI13IUIHNkr/F1RcG3BCYrwIgbRCgIzvpMJ3AOiAVwTnnm96hrTS1zUESTkMBAnRoddw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 122087}}, "0.1.2": {"name": "vaul", "version": "0.1.2", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "29e58aa3aba5e26c11c08b10fca29434e75d2acd", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-6jDR5RanBrabNRCydTK/aj/pWx7lZDoRkSvS+b4usBJCnitcUXxFV9B3nK/qS3Cxw0h/dupJUvffqEQgcpgsqg==", "signatures": [{"sig": "MEUCIQC1BK7G8IrEtHeYe+pXwUjk+lsOkpThu1LjIhvbFyZj2AIgfektzfJzYMzJhfgG+bY3CXlltXCHDdAjuiKCbyptsuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 127850}}, "0.2.0": {"name": "vaul", "version": "0.2.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "83fa44d63e6bf31daf1b7589c915559e10807583", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.2.0.tgz", "fileCount": 8, "integrity": "sha512-bzqiLz0RzTOewNFRptP9b9Ri5Zrz9cYoK/Qsmo+cbPBJOjiI/Y7Ch3p3BBZKmsmXcQYSp4FsQme9I5DCHxXfeg==", "signatures": [{"sig": "MEYCIQDXRkgE0UcNugIZpqrajk3EMlbCxN6tE2cNAQWpZzLj6QIhAOpDWKgj6R+DFn9VjYidtFoBcA8xVVwi1u725ReTfUEJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 140806}}, "0.2.1": {"name": "vaul", "version": "0.2.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "9a93a4a6be9f9a3e7fb367f4a38dfcf9e2a9e384", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.2.1.tgz", "fileCount": 8, "integrity": "sha512-ofOzhDS8l1TPmjse0/nmyEY7WH4YnDjCDKl5/Y0K2TzMsyC9H8FIYTZmi+lYIhRend1MXMG1zO/tZoTZpXIzIg==", "signatures": [{"sig": "MEUCIHd3d+t1uvyyQWIjVjjmx6U/mPTMOqP7nuQ/3CfyCvzYAiEAx1ErBVjHLMao47izigO3J+gOxZ4ggq57Qxz2VFl5exA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142882}}, "0.2.2": {"name": "vaul", "version": "0.2.2", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "2beb34def3940087c6f984b5c185ce75af727c2c", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.2.2.tgz", "fileCount": 8, "integrity": "sha512-2j4LLnQ0RZ0WfzzxUAZHIu19gaDaR8ZugBNUovDLB/0o/zBTGZ61DPVAhl2XL8feTrE8FFBL7bpiUUP237KuTw==", "signatures": [{"sig": "MEQCIEyVhBomhbT2IFgcDT7QyldHQxyp4dxEYd26bK/6huUtAiAHhKx8CvdNqEI+R6xuoIsRGh9dVyA/lb81O65NEe4r7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 142882}}, "0.2.3": {"name": "vaul", "version": "0.2.3", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "51433049d055d8e99f09df2c86944c0c159b7a19", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.2.3.tgz", "fileCount": 8, "integrity": "sha512-BEMMkCAQ3ZQ3O2sgwnu7IuD3scBd7sVVr3IN57QpF1NjX+mjSaW2Rn76Dm98eEz3tXiukqRcVAiKE6F4tYwgCA==", "signatures": [{"sig": "MEUCIEfjXSVAKEII5usrPzNS5290TMNrGOBglq2bFUKM+AyzAiEAp26qbW67U2/bKhJp69BSk0heOi9UTMetHPAiI9/Afhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 143879}}, "0.3.0": {"name": "vaul", "version": "0.3.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "9b0daa661ff45a0f6fa49ba5ce9c18d6e4799d2a", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.3.0.tgz", "fileCount": 8, "integrity": "sha512-r0oMKjn6seUnFw0fBNMcmsUfTZyEq6oT2ztnvv9ooleIC/7KIi9pI1d2vgMBxioB0//5keNV9zQ8zSmO3tzORQ==", "signatures": [{"sig": "MEQCIFZhEkbSfSy0GRIb4LCSKsmHjFKpi7oZGJDj+Lfzmku7AiABvzIHAHEEVpblc10DETCoWp/nSHy6mP5tnCA9PSb1ew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188117}}, "0.3.1": {"name": "vaul", "version": "0.3.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "69c421ae438c43c8aacaba9544c7a910a00c35f4", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.3.1.tgz", "fileCount": 8, "integrity": "sha512-WYb3pVrhp/byFpFlHxvWOIhEvJhJdv40xnYcQ2xs2G7ZeGeT3lBhTh2Z1BogqMht/yVX9UYSQZlC3GzyGqhaGw==", "signatures": [{"sig": "MEQCICLf64OItcLpcjXlrnvxCRBKk+G8lEy7U2Z5zPCkelQsAiBXjX9fgbbuClgyycIWVvDhlz7sRGLDL1PKJ0mT25ni2w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 188671}}, "0.3.2": {"name": "vaul", "version": "0.3.2", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "cc17da7f7ec31bf8fa06cb4b15bf378dea21c93f", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.3.2.tgz", "fileCount": 8, "integrity": "sha512-L8LU93qZ+V64/EbIeQNNLjwu2LftW1j4DL/Ecuf4ViRmlgcXMpZ0a58gZ3pTVAUwAMBzebZUc9X0WZ3y/t86wg==", "signatures": [{"sig": "MEUCIE/O+gsoq/wZLyN/kC92T76wMlF0V0hobsXZRA970a1lAiEA5Vr835Eh0n25sS+8EDEcwUlz8pXDb12/trDYChV7n4A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 206971}}, "0.3.4": {"name": "vaul", "version": "0.3.4", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "4643df2567778e8e8ddd8d5423b1158d25e5ed39", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.3.4.tgz", "fileCount": 8, "integrity": "sha512-a+fUsQp14yaengaCJeR/tLPsOQLJNVE52W+2Rb72NvuAdlPji9yvTOlXt4DQV6oNitX99LGzh1ZQx8nW8SL33A==", "signatures": [{"sig": "MEUCIHNxd+W4a0TIY7YewVulAVOD8n/ZlOJCsh/dHhIxTQf+AiEApbK09rho3+3+pbZUxWGcQJgHjp0w/hSg4oEh9ML41/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210502}}, "0.3.5": {"name": "vaul", "version": "0.3.5", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "e7fd557f767ceb38cb67cd475e76dbb99829d822", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.3.5.tgz", "fileCount": 8, "integrity": "sha512-/Dmtiy/M6eAZ6uped5BbJ/17DhLjOrZaGXGsxr+odp15if2ulQvLC740kuC9xUunj1H7Dguq159ISX6AHisiig==", "signatures": [{"sig": "MEUCIQCJLxcGinMVU5/184bdwR8qewhQ4Nv/hoYWK5z208utPgIgK7G97nZJ9oZt3ZkLdKEd3TfFSnLWF0LcRyJ0Fuc31kA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 210502}}, "0.3.6": {"name": "vaul", "version": "0.3.6", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "fc580eb6de8008dce4137b677000b8fb0ce82ed1", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.3.6.tgz", "fileCount": 8, "integrity": "sha512-otNvssjAucrN86p6ih+ROE3FGTVcRuZXXq87JKxxJn1SiSwp2E0vQkJidhQK+6YK38ja22rmnA1rU5gBR4qckw==", "signatures": [{"sig": "MEQCIEFWi5BbY6hy2RypC7Xm68mHuErrXv5P9U4z46/Xdfb2AiBlWuYPK5paxY7FO2c59we4nxM88YvFOkqcikafm8nrMw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 212055}}, "0.3.7": {"name": "vaul", "version": "0.3.7", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0"}, "dist": {"shasum": "ff02c55dcf710ea85425f94a7ce191f95bd1c42a", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.3.7.tgz", "fileCount": 8, "integrity": "sha512-1XrxVz5A6b4k1xYCLF63SQ8A4Fepwt71CkAcIUCXosgjZyj9kRWYsTov6ca2cI6FV20dXOJnlIdMycZvavYJlg==", "signatures": [{"sig": "MEYCIQCOUgrlGj6HR0DOek/gEEpi0ikZ9mABKt8NFgBY2RiSRAIhAOIWbXIqWVYD9/Fl3WVjOwzyb+7g7JbsbGKbpfXhkGYz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 213257}}, "0.4.0": {"name": "vaul", "version": "0.4.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1dc7526dbe46fc731883e4c99b0f77ed1dbc428a", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.4.0.tgz", "fileCount": 8, "integrity": "sha512-FPFApddzhVQpMVts4tkdDi+dUc7t9eeB+y7pFVSfLIlp9SvHqvlS3kB86mymWYRAL1g2NqkRt/CvhvH2FA6oYw==", "signatures": [{"sig": "MEYCIQCWEmI3kK1iYvYkQvxC3WbEy15g7XmLntdNCsYcmSWBrgIhAKWF+HQPqx5VD+wz7fGfm+XkI1slUID6Js4/3qR1GfDF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 211783}}, "0.4.1": {"name": "vaul", "version": "0.4.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cf2535eeb4aa00f2bb92e6f571a9f2b1254ed56d", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.4.1.tgz", "fileCount": 8, "integrity": "sha512-H6VlyuPDM2Icktic6oeE7oZ+uOsht+9UegzP935CJ9JAeyiWXpDFIU57EUVJnnUkfs1Z2OdsfW5UxNHySrZJ3g==", "signatures": [{"sig": "MEUCIHkI5IQ4sivGNozEetRwDT7FPq7KgmNj8arJjkSA/3r3AiEAre4ESx5D/5V+tkH/52dCmXTdQU9mPDbatL4ChvMYiEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 253134}}, "0.4.5": {"name": "vaul", "version": "0.4.5", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa4e5b9d534a15fce0ddeaad34cc4fd47423c469", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.4.5.tgz", "fileCount": 8, "integrity": "sha512-6H7Jlm9yi2Iyc4ZnE7DS7dgI1F7eo3sFCD1+4xk1nVQMSQ4ku8scftSLGZK8IqlmezNQguI/PtlZybkckmpFZA==", "signatures": [{"sig": "MEQCICPMth6FoLkc7OaCFVj5ZiRappkQdD2pYiPZvbCh5VVoAiB/XruO/Ki51oXjAtWkl3MTXhTGeZI8Yw0sZ86BnRHzfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 254184}}, "0.4.6": {"name": "vaul", "version": "0.4.6", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fe1f1f45e92a956c2b54050b8c335f62605a6c38", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.4.6.tgz", "fileCount": 8, "integrity": "sha512-fb8xkatdCabTSYtfFgtTHZM+XKe1RtzF/ZLuITzDt93hEvjSm/WEtRZEM/fgjPx9U0fkldyktcNVAOuX7W1QLA==", "signatures": [{"sig": "MEUCIQCTZ3qkc5DNW7dnvA2OPocKD0mXltxtCInXAoX+k52UhAIgcQLAvyR3NUuzZ5z91s17rAnFjSztuzPSQnDzAQBkZN0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257576}}, "0.5.0": {"name": "vaul", "version": "0.5.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "37f8863afe66f0599088a9ef469dca7b1f0f048b", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.5.0.tgz", "fileCount": 8, "integrity": "sha512-elMr6H3Sr7DGsigSmose0nLp799uZZ7tDym+7qpoCBEITax23iG3js01BsdTV/etuJ7ki8fpc26/0Ty/AXFTXw==", "signatures": [{"sig": "MEQCIC3VC/HowQi7iMo5UKGnVfj2pACpyGly9GV5c5uOiEJBAiAepo5syDbdq92taDypck2UM3GABl6+DokYuBoy8IypuA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 260578}}, "0.6.0": {"name": "vaul", "version": "0.6.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bba81ee29ffc4c1cd9eb4f6f163b218e6d00c3d1", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.0.tgz", "fileCount": 8, "integrity": "sha512-KLGrHya93SWH9xbWRNxXifj645ydCUe0KBiGCOBlIHsFRK6hnOr1qmrNqVG2G+fnxIp4DwDdHWOmcx5gNvHsCA==", "signatures": [{"sig": "MEUCIQDKiV2Vqu1qtNqKBK/6+em4zu5zX5DOxVaTcCvj4EoRPQIgBATHi7GurBkYcqdwxGdvGwSiWvXKmU43hWFarMTgvMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261044}}, "0.6.1": {"name": "vaul", "version": "0.6.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "78909e171458631ab7a76b3f20d3b987a4a68c9c", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.1.tgz", "fileCount": 8, "integrity": "sha512-/AMuTlLefi3U9CQJqHbNQeIMrNq5FxYLCXcbuRIf1wiP3fpfvoP0GzgsnREMtGuvhtkwv0BdEC1CvTfU0+W9OQ==", "signatures": [{"sig": "MEQCICZnld3TtezGDhmBE9CKXJXgyTw+X2AHcm8KNkTt+jNNAiBcWqd7+c4rhKYjDC9qviRsB5DmEQ/Ji4YwsXEKSG/PKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261628}}, "0.6.2": {"name": "vaul", "version": "0.6.2", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b4401f8e643f3e581060b5d76d0398d704552a74", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.2.tgz", "fileCount": 8, "integrity": "sha512-1ElkOEcKAN9pjj1Gw+ZiZ5NN6KIh6xQTThpVzrzj8/yUMXGgJdnLm/k9qGDQAY008BcCuxwEeb0gqrbNiCuBMg==", "signatures": [{"sig": "MEUCIQCbF+7qaKwFmG23mv7M41EevKvZYmBAfxrhqBXmxf8pbAIgTPZcutLlLXKAszMxHQ062IkctZWK4Ttouyp35Wqe+mY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261628}}, "0.6.3": {"name": "vaul", "version": "0.6.3", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "276cd7df728cb64489733cd908e22beb75bad0c8", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.3.tgz", "fileCount": 8, "integrity": "sha512-6+SNHnARI/e27jfMJV1GjOK1Ko/35qd3Fn2dJlWTVTbeUK47c3KczOe00cgpu2TGZPzLfeCVeBIwUDZFTm4ZfA==", "signatures": [{"sig": "MEUCIC+JeArY/e6A6WaNFp2Sgb5/yI3ahQLbkwfaZfhN/LTlAiEA10Q6WUHs8P9eQYsI9XRJMPjnlR9p9CRmNESSOnQPwLU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261788}}, "0.6.4": {"name": "vaul", "version": "0.6.4", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fe95ea00e71beb234c5a01253944f1c75f5c8639", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.4.tgz", "fileCount": 8, "integrity": "sha512-c955kymhdH/XhQEIl53F3zGy3jfo0D99r9FlkEQaPIJGLbh5pmrReEmp2d5Y45I4b4aM4HOrrOuse15uyxngxg==", "signatures": [{"sig": "MEUCIGRzBOX7Kg6gHfCj2bz9qIvTivEg+mXVuSY2wPnzXkB6AiEAuAMjKtCGCu2ihlJBgDPObZ2jegcEogxSV3cArKS+bCc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 261788}}, "0.6.5": {"name": "vaul", "version": "0.6.5", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "80d33a9e093243bb72f1ac0eb20c4e9ad0b02af7", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.5.tgz", "fileCount": 8, "integrity": "sha512-BjprZH8x6L16INTCehNOwgG2bY8VNQRyMAecFfLnCJIx8hc/UXsOjiPyOIrtJnj+fy+OkyYa2F9stMmMafQdKw==", "signatures": [{"sig": "MEQCIB4mhySOisUXrjlR3qm8MONWNuuo0i/iCO2djWGZKaSsAiAfyQDv8FDLRCIpFboFJKogXjj6XeTQGPZ2d+HHwtjsPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 262624}}, "0.6.6": {"name": "vaul", "version": "0.6.6", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7df84e656696dca1640344928d0c42854e96dba4", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.6.tgz", "fileCount": 8, "integrity": "sha512-ge35KsNd7+p1/D7G0EY6nU6iHY08V64TAS2r7fLoqtVUEvY3NyiTCFTfC6OGFOTDnwxhHqWd8lrGQLRgqVwqVA==", "signatures": [{"sig": "MEUCIHhB0pFFh0dUSIuU203KnwzUkOL4dnYHdhoVfuLM4gVDAiEAoRwaCbIZB37jm3/iclWlGWwkTqwGzOXPJB6Sdrv8AFw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 222241}}, "0.6.7": {"name": "vaul", "version": "0.6.7", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ac7875a87b113ed70e30147f7e11a09c95e5174b", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.7.tgz", "fileCount": 8, "integrity": "sha512-zoRs6LmOqfREkghTgE6H7NjYHoEsgzaaLMCXDMN4wJ8yTlsmqHkBzKPGNWZvFgD4eDW4n825Y18S/bnDDAsMMg==", "signatures": [{"sig": "MEUCIQDL+C1BFSdrUVF62bl3uMrKXJmlm0jryk4kN+vef+BnwAIgU1DoW6EytRqJa0ZuR0ty2vw4F2L2CUnn3oprQHT2T10=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 217401}}, "0.6.8": {"name": "vaul", "version": "0.6.8", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "633bb2a0e352adb73f84c497d516c93673d4f01e", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.6.8.tgz", "fileCount": 8, "integrity": "sha512-Fn7Pe8Zm/jqyIf8Nn/iBMoBzQ3L49xB2vXj7Kp8W7RCMRDg33X5rCp/K80g1Q35VNtKP8AgmFSM7lbticurXKA==", "signatures": [{"sig": "MEQCIC+/HByyXdACpQnVj9a9pv7ylE7l7waBeuVWO7TOvzlWAiBtQ9g2rqfjwBt+xW6kqPdSWFoM9ukfLBozeIY0Tph4JQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 218784}}, "0.7.0": {"name": "vaul", "version": "0.7.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cc34c83c28e935bb70ec2f1e5a4236de40d7fd61", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.0.tgz", "fileCount": 8, "integrity": "sha512-qhn8oEqvoisxLsebjyatO555DZAnCyRk0+zZlYPOOlUAs8+LWjFCRYg5KjYzVlQiAU/bkwsOqr1YVHztY08H9w==", "signatures": [{"sig": "MEYCIQD6lAq31fzNQvy+lIGBqe6N1MPBHUmApwRbQxud/VBJrQIhAMWPc0GTZOEUdomEPirnH17dmue9LMJz7P6/djrlJaSW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219587}}, "0.7.1": {"name": "vaul", "version": "0.7.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76bd22baedec1d4ce56b1fa4e1151d3924b6c2c6", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.1.tgz", "fileCount": 8, "integrity": "sha512-kChKcUCzW2m+WwKojuJFBSViYksVKTnicb//8U8KmxNroUVJBPQBJJ6S7Pi2rYw3Kp7LO5UDTPYI3tnTphuolA==", "signatures": [{"sig": "MEUCICT8FGjWPvKH6wXq/PHXi91UDDhrzpPmrslOJLDw8YJsAiEA6OWp6SI3kXoohOMaH2UTel7t+ysJMMG57Y2JDnnu9gM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219637}}, "0.7.2": {"name": "vaul", "version": "0.7.2", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "10594441989d274ffd8b91720e2218bb7b23aec5", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.2.tgz", "fileCount": 8, "integrity": "sha512-pdVwdV93YNZnm07ix2lcq8Xmohto9ExNDLiZk3oo33lIbxl9Yx2WvPOADvVNHxoOO8nzE87M2RUgXD1GuaWxMw==", "signatures": [{"sig": "MEQCIGHE5TIGWI1aG7g/WqMnNw1JedIhUr5+yjto8VxoXS4VAiAShIHzjH1LdAXZATS4aGfkZ03aycQXVueslKPXsKM5YA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220352}}, "0.7.3": {"name": "vaul", "version": "0.7.3", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13808352c7c56b1f908e258fe8caf15dafdca3ae", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.3.tgz", "fileCount": 8, "integrity": "sha512-B0d2Unc1qiTrUCt4kLMon7NtPyhiWxzXemDH42XD8n57mttN2PMgGQmThk0NJAJeVRx3UXKApuNPCVU2+N2P8g==", "signatures": [{"sig": "MEYCIQCwSmyYmhqyHhFlRVaikEQKsGY0w8Lxmb1usmVgNW8S4QIhAP7QxQdm83f7oBJhAoxdTpO9F27Wuvr12btwHjTiON9l", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220352}}, "0.7.4": {"name": "vaul", "version": "0.7.4", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "615551175850ec48226281331e8a656de14dda88", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.4.tgz", "fileCount": 8, "integrity": "sha512-+nvUjrAaDKITr3Tn1K9f6Zj0dCGbA1TyR5BOWvOu8OX6tso0clA+4X064O+k9yhRGmiyAf9BHDzW+bEqs9Va3g==", "signatures": [{"sig": "MEUCIQD9lGQLAP6VGwS70mv8Sp+FinECfx4sAakobKJ+vj5magIgdsreInuoFFo3c2cCYqOpsVaoYlzuoMBhtyZYrggBsHc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220352}}, "0.7.5": {"name": "vaul", "version": "0.7.5", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7e53d3dccfb6d965f44b18de9296cb86133df3f5", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.5.tgz", "fileCount": 8, "integrity": "sha512-tfihsrhkVNfIrSioiw66CqiqFAjBQ26mlbbPuypfHQV0zvQetpXgpSDLVqIz9GPi92iwWFbh1jNA/AmVmdy9GQ==", "signatures": [{"sig": "MEUCIQCnGs0Xh7u5GCL4OEsVdYwLYLw9KVcnRGbnPSeGJpKwqgIgMSdItMLnmNAGfQxONVr5tb08yvOIVGoMcUU/DinymDs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219830}}, "0.7.6": {"name": "vaul", "version": "0.7.6", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cf7e0258b87c4da653de4f6907f3ba8192bc4bb3", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.6.tgz", "fileCount": 8, "integrity": "sha512-MXH6rFQKaHY974rXZmwgqmfq1f3yZBQd1YhbfIUvziBlulxWIxa2kPhX1dym3DLPWPtJYts4cnVRO6tl6Rcndg==", "signatures": [{"sig": "MEQCIC/kt8NUJXgkELMtlNlPe1ded533fZ3htzoYfjx7EMBxAiBJNV19276B4JcZu99V7L5OXfEFTX7kX4PaELNezaopcg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 219872}}, "0.7.7": {"name": "vaul", "version": "0.7.7", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9b746d31d17b1f588ee4e863e16780f680dbc21", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.7.tgz", "fileCount": 8, "integrity": "sha512-NAQTE8836Daxq+VC74srRfGqiOLQft4yc0x8YsO6Vowti0RC7LWzSpIxKd7RGegzgMMZOho+9ysH+uI6o+tUVw==", "signatures": [{"sig": "MEQCIGjn4HMJXKflw0pch8XfNa5DietzyRU0QhmCyN5zTphnAiA24vcRZZlfVEvfLopohsOjDrfawE/Z3nk9nJSHKNCprQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 220996}}, "0.7.8": {"name": "vaul", "version": "0.7.8", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1643e9dc2112a4b73ce2bbc972db8a23e2e24771", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.8.tgz", "fileCount": 8, "integrity": "sha512-mlA3uI7LRIyEMiWWMfFieRWhPHN67NloUPvNYAPWj6wZGjK2IM1C0a71PF8hu1WC6Zb/7OktXlk/8Ds/RxDxGg==", "signatures": [{"sig": "MEQCIEvcTvIPBCIrkGgiYsNrv9FSN4+AEJSSwe/uoAFh99XaAiBvqN46fp1j14FhHzm+YBAcL9tEpRQkhJ5YFFNO9z/Gvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224302}}, "0.7.9": {"name": "vaul", "version": "0.7.9", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "365dfe8f6c1df3a81a26508474db0e0ceb98ac8c", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.7.9.tgz", "fileCount": 8, "integrity": "sha512-RrcnGOHOq/cEU3YpyyZrnjh0H79xMpF3IrHZs9ichvHlpKjLDc4Vwjn4VkuGzeUGrmQ3wamfm/cpdKWpvBIgQw==", "signatures": [{"sig": "MEUCIQDW3URy2XSPRkH3Izh3t7+gssuco6MexQIQK/BcsOhCQAIgb7EX9gLEZ5erqHn08Mv2bzBGqTvqGn5aswCLlBrY1o4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224883}}, "0.8.0": {"name": "vaul", "version": "0.8.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f2155dcb5561d33df4f0794603e91e35672a8d58", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.8.0.tgz", "fileCount": 8, "integrity": "sha512-9nUU2jIObJvJZxeQU1oVr/syKo5XqbRoOMoTEt0hHlWify4QZFlqTh6QSN/yxoKzNrMeEQzxbc3XC/vkPLOIqw==", "signatures": [{"sig": "MEUCIECG1VKizxZWQKC2rwkvHAA4buNenUf3uf3MIVe+XnuEAiEAhMU2nLDN+LqBBpPLUmEN/yn+4SmVOQTVvfla+Ex0I+8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225417}}, "0.8.5": {"name": "vaul", "version": "0.8.5", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"react": "18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^4.4.0", "prettier": "^2.5.1", "react-dom": "18.2.0", "typescript": "5.2.2", "@types/node": "^20.11.0", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a4cd190158a7eb79f3e67be7a91619a9bcb0674b", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.8.5.tgz", "fileCount": 7, "integrity": "sha512-SZa0UPg+bNqbkecpKkiMvS7QeBzZLFiYqX2HoBLN+3yIvoJpu8xWcX6tJVuqg6iwjUg/uK51P/FoNqxXOdV9lg==", "signatures": [{"sig": "MEUCIE3mhGN5gcp7izO2Shk3iIkQSzfMKuBKTUVBRSLaZjlhAiEAqcZsi7nVdZmeAq4tTBpxviZW9sO6Kou5So9rK9zjh38=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 137784}}, "0.8.7": {"name": "vaul", "version": "0.8.7", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"react": "18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^4.4.0", "prettier": "^2.5.1", "react-dom": "18.2.0", "typescript": "5.2.2", "@types/node": "^20.11.0", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2825cc13729f0e6ee5b1b5cd60a57ac7a855ce76", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.8.7.tgz", "fileCount": 7, "integrity": "sha512-xeI/qpCnaMkFWX2s0mPXZV77WP4Cj2chjNRsegl38jp+jc32ygmIob0xaqdVur/m+AmdGs9wO60NnrlSaTWvCA==", "signatures": [{"sig": "MEUCIA7TjE8UoqhVwofbeeS7HYj37U7OztTE8mauhPhbduOHAiEAmm6hxWOoqIHdlpn8t7GqyclyjRHEUd8BF0ROE+1Kktc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138378}}, "0.8.8": {"name": "vaul", "version": "0.8.8", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"react": "18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^4.4.0", "prettier": "^2.5.1", "react-dom": "18.2.0", "typescript": "5.2.2", "@types/node": "^20.11.0", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c5edc041825fdeaddf0a89e326abcc7ac7449a2d", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.8.8.tgz", "fileCount": 7, "integrity": "sha512-Z9K2b90M/LtY/sRyM1yfA8Y4mHC/5WIqhO2u7Byr49r5LQXkLGdVXiehsnjtws9CL+DyknwTuRMJXlCOHTqg/g==", "signatures": [{"sig": "MEUCIFlG9rcZIyeHKhtAzv4FfeORA5SYn14+YOjTvB2mzpo7AiEAmaNdECg0WGit7tQ9feTt3xwIB5OnjJg+6NjgfiT2DEQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138378}}, "0.8.9": {"name": "vaul", "version": "0.8.9", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0b6e4771dbd0e8e330b3c4b0415d4fd15cd47e9b", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.8.9.tgz", "fileCount": 10, "integrity": "sha512-gpmtmZRWDPP6niQh14JfRIFUYZVyfvAWyA/7rUINOfNlO/2K7uEvI5rLXEXkxZIRFyUZj+TPHLFMirkegPHjrw==", "signatures": [{"sig": "MEYCIQCgjW/TNi4CmNlWVzAtoZEXRu8RaQ6Ji0usWCeHwtyJhwIhAIMoissXU/a0DxjpN5Ko0tyh7wRNzJPVZg82/iy2984F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 252100}}, "0.8.1": {"name": "vaul", "version": "0.8.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e5087238ae584f8ff1dadddc3d0c539690787841", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.8.1.tgz", "fileCount": 10, "integrity": "sha512-r1PKXj+oXVdeqUBuxzPIy6hMUc457UN2uBtSlwpvvZgwBp2X6QLk1Yk0q517Aa1MNOGXaSwZ2lKEtIHJIM0AgA==", "signatures": [{"sig": "MEQCIFYS32db7eWIs3AkihyuDLJTiU32RXdDI1gnon3RISAbAiBZM9Zwnv+gW5omlQDRfuiuczN2ZRRIxCP9S8koGVGIdg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 253350}}, "0.9.0": {"name": "vaul", "version": "0.9.0", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"tsup": "^6.4.0", "turbo": "1.6", "eslint": "^7.32.0", "prettier": "^2.5.1", "typescript": "5.2.2", "@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e45a653f89d6c5c8b3c32bac29b2ae3457481d9e", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.0.tgz", "fileCount": 10, "integrity": "sha512-bZSySGbAHiTXmZychprnX/dE0EsSige88xtyyL3/MCRbrFotRPQZo7UdydGXZWw+CKbNOw5Ow8gwAo93/nB/Cg==", "signatures": [{"sig": "MEUCIEbCXb13JTRQCEMfwLVuO7v8wbaC0JBHjPyJmJAFLtS2AiEAo0IpvrbtSz//Nv72Jd00XGMBarKHn8mog4BJ/KMb+Wc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 253592}}, "0.9.1": {"name": "vaul", "version": "0.9.1", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^4.4.6", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3640198e04636b209b1f907fcf3079bec6ecc66b", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.1.tgz", "fileCount": 7, "integrity": "sha512-fAhd7i4RNMinx+WEm6pF3nOl78DFkAazcN04ElLPFF9BMCNGbY/kou8UMhIcicm0rJCNePJP0Yyza60gGOD0Jw==", "signatures": [{"sig": "MEYCIQCTjme9aOKSQb130o6dUAd5YDS3LQwjF4Yy+6SZs/uJXwIhAPAD/snNmb5Er1REQpr+/wagCPcs1ZxkPEB/ndXmMauQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158613}}, "0.9.2": {"name": "vaul", "version": "0.9.2", "dependencies": {"@radix-ui/react-dialog": "^1.0.4"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fe7ad8a0acf0863b9bc7e956da27a8ce6169ab7c", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.2.tgz", "fileCount": 7, "integrity": "sha512-m2A7UgAU/JMWiwUhmARK8LMvEfXiudA4trJxfZF5AtH2uBTgN855msZ2yjPnUDfa7i5glocMYLSfML8wriBtBA==", "signatures": [{"sig": "MEYCIQCY/UVDyfuROnxvyWUu250ezBaqQfimQ1bYAkFOBmKFigIhANMW/1OlTk+GF58MSzAodJ3v0H+Xhlj6iCLFyohgavm+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158591}}, "0.9.3": {"name": "vaul", "version": "0.9.3", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "363c21e7c7d404bacb81591c2ca1e8cc5cdc5829", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.3.tgz", "fileCount": 7, "integrity": "sha512-VNWuzw37fw6DWptl9PMUc9PH5NJpGqpetcxDMRiH/GzH2inJAEMwo/uZqaONlGseeOG8xDtrrp3nYoiZhyabpw==", "signatures": [{"sig": "MEUCIHF1d50SYhJtGwyGNYmcjxPsrZCwDPtDWUbeDVpYtLc1AiEAvb4mZqkcQJBjxrLS99HTBo3OBjufwlvTZpW6s6hmKsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 146583}}, "0.9.4": {"name": "vaul", "version": "0.9.4", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2e1d8f90481a9f25169226f9cd6f764ad30ca65e", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.4.tgz", "fileCount": 7, "integrity": "sha512-pcyIy1nEk6798ReNQpbVH/T/dYnoJ3bwyq7jmSp134s+bSvpWoSWQthm3/jfsQRvHNYIEK4ZKbkHUJ3YfLfw1w==", "signatures": [{"sig": "MEUCIHQh9YYfmC1Zwn/ivYzKpul5tAdvsLIc1iBQ00aCxFBEAiEAg9fvIGChWFdb7b3jLknl02+VfpJAZBATzMNWTYZobuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 159487}}, "0.9.5": {"name": "vaul", "version": "0.9.5", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "079219044ad11d0eecf358e40ce22b01a2db62ac", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.5.tgz", "fileCount": 8, "integrity": "sha512-3aQhcjf2AyD4OZRo7yHYUP7AIiOrUGRiDZtwSwFWMq5iwmjfYaLrRg+hs7M0qFwMkOw3MnKSPp2uRWZv/jbOfg==", "signatures": [{"sig": "MEUCIQDelJHJD0qKUILAMo2sloRKtoIKl04Zx48GefRDhTiypQIgRmkw300qfUoxy0TjN9Exur8ezVAWO4gPQ2FEhNLzluU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 176857}}, "0.9.6": {"name": "vaul", "version": "0.9.6", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b6b08997a7c408a403af158f118a90c19d2ee07b", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.6.tgz", "fileCount": 8, "integrity": "sha512-Ykk5FSu4ibeD6qfKQH/CkBRdSGWkxi35KMNei0z59kTPAlgzpE/Qf1gTx2sxih8Q05KBO/aFhcF/UkBW5iI1Ww==", "signatures": [{"sig": "MEUCIQCy3uau0fJduKc3a4canAc5uwjHxVUqHw5lUWWE9eibogIgMsVYcI+WUvsSrV3ATauRtO/QVbMdyQYT2AiDQ1LsWvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 177355}}, "0.9.7": {"name": "vaul", "version": "0.9.7", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9a1c017f3a0c4cf653a0e69d7f50c3d27cbf4051", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.7.tgz", "fileCount": 8, "integrity": "sha512-7Fv00cv95ZeZWNuo873AyPbFFyAq63gf7SCbA5/+pZtXs8mcsYYzPTTunsS0lDV59mtGKl68idZd3CofczksgQ==", "signatures": [{"sig": "MEUCIQCT+RTtGfAdyLFQy8qeSs9TAOFTLagag+59jqNxSJggKwIgJEpWycNWzxPdPTJHlFCDvhxuhIPT0yALeO8SfBWGpH4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178553}}, "0.9.8": {"name": "vaul", "version": "0.9.8", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3abf9bdc2b55ed3c4997a15d442c342975b61164", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.8.tgz", "fileCount": 8, "integrity": "sha512-ZTf7dyhivqMARAdCh/gHQpDXFnmWrYgCreKZmyb9j9G5R/svqTspAT32D/Q5Bz2zCH4RRqrWcw6fryy5FZweMA==", "signatures": [{"sig": "MEYCIQCtJbrq8IypkUyXAs6FGXrQeok0HYaC2Kiaf0TOcl9VfgIhAJIfKIkZq02grOXTW6L89ol3DEg9SrLKlwCLdH9bg61L", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178719}}, "0.9.9": {"name": "vaul", "version": "0.9.9", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff075c3cba6193d4859bb6f1b09efcce049cf812", "tarball": "https://registry.npmjs.org/vaul/-/vaul-0.9.9.tgz", "fileCount": 8, "integrity": "sha512-7afKg48srluhZwIkaU+lgGtFCUsYBSGOl8vcc8N/M3YQlZFlynHD15AE+pwrYdc826o7nrIND4lL9Y6b9WWZZQ==", "signatures": [{"sig": "MEUCIQD5PH+QtURkORoC3YET2+8SC5iT6lXH0MVlTh7WFJc73QIgcGUrwknK6YCoPrkwSQmb+90Vn9eTnj6wX/CxRW+FAZw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 178853}}, "1.0.0": {"name": "vaul", "version": "1.0.0", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b2c84e626ffa417ee9352cdfc435b26e8f60f547", "tarball": "https://registry.npmjs.org/vaul/-/vaul-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-TegfMkwy86RSvSiIVREG6OqgRL7agqRsKYyWYacyVUAdpcIi34QoCOED476Mbf8J5d06e1hygSdvJhehlxEBhQ==", "signatures": [{"sig": "MEYCIQCncq4irgQGi/D/bGH2GdFz1lylbR7PtfjNLjDqQqYapQIhALVXkvbKfj3eqZfta68MVsUVcM4SALGwPWkG9/Nc43zX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184873}}, "1.1.0": {"name": "vaul", "version": "1.1.0", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7da4bc965e0b184ada632f1208096b0f5575d920", "tarball": "https://registry.npmjs.org/vaul/-/vaul-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-YhO/bikcauk48hzhMhvIvT+U87cuCbNbKk9fF4Ou5UkI9t2KkBMernmdP37pCzF15hrv55fcny1YhexK8h6GVQ==", "signatures": [{"sig": "MEYCIQCGAFndoCLgOoU973X6Kuk5cTAr3qB1NFnnPqPH4/vDQwIhAPup1qsRWzfq43jmtG5Lx9l/fr21y36Nut30CJsHJDvk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183763}}, "1.1.1": {"name": "vaul", "version": "1.1.1", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"react": "^18.2.0", "turbo": "1.6", "eslint": "^7.32.0", "bunchee": "^5.1.5", "prettier": "^2.5.1", "react-dom": "^18.2.0", "typescript": "5.2.2", "@types/node": "20.5.7", "@types/react": "18.2.55", "@playwright/test": "^1.37.1", "@types/react-dom": "18.2.18", "@radix-ui/react-dialog": "^1.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0"}, "dist": {"shasum": "93aceaad16f7c53aacf28a2609b2dd43b5a91fa0", "tarball": "https://registry.npmjs.org/vaul/-/vaul-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-+ejzF6ffQKPcfgS7uOrGn017g39F8SO4yLPXbBhpC7a0H+oPqPna8f1BUfXaz8eU4+pxbQcmjxW+jWBSbxjaFg==", "signatures": [{"sig": "MEUCIFmNmHKjB2ASwn35PjtezF6Kws6kbr7xlPmE3+vuy9V5AiEA4J+Hc+KwTwURAg+38U9vEeDOn0AwK5l2589LGADNHCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 184007}}, "1.1.2": {"name": "vaul", "version": "1.1.2", "dependencies": {"@radix-ui/react-dialog": "^1.1.1"}, "devDependencies": {"@playwright/test": "^1.37.1", "@radix-ui/react-dialog": "^1.0.4", "@types/node": "20.5.7", "@types/react": "18.2.55", "@types/react-dom": "18.2.18", "bunchee": "^5.1.5", "eslint": "^7.32.0", "prettier": "^2.5.1", "react": "^18.2.0", "react-dom": "^18.2.0", "turbo": "1.6", "typescript": "5.2.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-ZFkClGpWyI2WUQjdLJ/BaGuV6AVQiJ3uELGk3OYtP+B6yCO7Cmn9vPFXVJkRaGkOJu3m8bQMgtyzNHixULceQA==", "shasum": "c959f8b9dc2ed4f7d99366caee433fbef91f5ba9", "tarball": "https://registry.npmjs.org/vaul/-/vaul-1.1.2.tgz", "fileCount": 8, "unpackedSize": 184301, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCcEZtGv+aQAHZpvGkReD4GBO0bVgGrS0P3ry4Zvy7t+gIgY2dVDQeJiUg52IVn1Ghla28VkfuLWv4YwV3vMJ+10Nw="}]}}}, "modified": "2024-12-14T00:56:01.059Z", "cachedAt": 1747660588123}