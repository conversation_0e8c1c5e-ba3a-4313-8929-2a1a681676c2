{"name": "autoprefixer", "dist-tags": {"latest": "10.4.21"}, "versions": {"0.1.20130409": {"name": "autoprefixer", "version": "0.1.20130409", "dependencies": {"rework": ">= 0.13.2"}, "devDependencies": {"chai": "1.5.0", "mocha": "1.9.0", "sinon": "1.6.0", "sinon-chai": "2.3.1", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "06b04fb73779015e79e71df85e261421d7b80742", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.1.20130409.tgz", "integrity": "sha512-g9PVgAfftYSvZXD9PsvbutKnYExxJinBDCv8gHnUjwChnqmcl6nBdDWivJT7P/eIups6BrFKBd0rMKZO+YJTbA==", "signatures": [{"sig": "MEUCIGISxPGN2wHvjz6wjlcNKCuQQLZlvOwG7dctyBCRUKhNAiEA+PqAkJX8if7Q/ACKP8neFORuxlf3Ag63gEoBI5ZvaR0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.20130413": {"name": "autoprefixer", "version": "0.2.20130413", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"chai": "1.5.0", "mocha": "1.9.0", "sinon": "1.6.0", "component": "0.13.2", "sinon-chai": "2.3.1", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "63170ea937afa0aa4bfc1401e8a21078b4b1538f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.2.20130413.tgz", "integrity": "sha512-xb4lIAZRfp+V7SF+enYVGLA3gG2JxamomzpQfEkiv5go1roNhqVM/Tk/z8igxnfm2nKOAhCE8blSK32xMwqR9g==", "signatures": [{"sig": "MEUCIQC8L0VNqbEUiNFObyNg4557o3TfXUFnedg/FjUJM6fFtwIgFm+04Ke5vuWrpWld9j4VB6FijVr0J+MEA3N1Pnyk04U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.20130423": {"name": "autoprefixer", "version": "0.3.20130423", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.9.0", "sinon": "1.6.0", "should": "1.2.2", "component": "0.13.2", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "75c83ea15305adff15bc5abeba9400a79be454a2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.3.20130423.tgz", "integrity": "sha512-7DwPpFNuQ9EyvA1+TgOSgyWDhOaJmbyFfRcI1jdoSTqJy07kSpBky7NE0SN1k7Kb8fjjUVlmSXQRZ41XXjzMsQ==", "signatures": [{"sig": "MEQCICw1rMe5TGhJ5Erq6gmWEBQmqMN7h+xQMC3+W9izgNfTAiAkh6YQ+Nt5YVruBfx3cICERvNssxXxovCgRNM9jcXsQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.20130424": {"name": "autoprefixer", "version": "0.3.20130424", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.9.0", "sinon": "1.6.0", "should": "1.2.2", "component": "0.13.2", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "30346a8f3a39c5e8c280255c3fe584f13d975432", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.3.20130424.tgz", "integrity": "sha512-0T9tZiAqy/U/Ntlgp9Vlrs6BoPJCfgEuYto7iIy5vxXaxAQatw0hHauoOdXQsoNM95O4f89/KM7YQdsTtUaasQ==", "signatures": [{"sig": "MEUCIQDu2gdziBPhdwcvsouAx2n3O3os9WCVP/3peRXRuiDhoAIgVa+tjuax66gKdYSaDUg9euG7hFru4wf/l434Q9459oI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.20130427": {"name": "autoprefixer", "version": "0.3.20130427", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.9.0", "sinon": "1.6.0", "should": "1.2.2", "component": "0.15.1", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "27da062b75a92e46c8d662f485136644ac7b12d6", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.3.20130427.tgz", "integrity": "sha512-B3PFz7iFKJi6r12YxsedsOVfRW9/IzL1UBoJ6QAzq8AdrIlj0UL95+x1SREGXiCzi+noasOcdLToKg0QcA+l0A==", "signatures": [{"sig": "MEYCIQDFisLKLIkSDWISlwKntQea7Sgnz+txZtVqoZJkMNuUXgIhALcFj4/h122arR3qRLe3X/lRBBFAHZdjwwGHlBWDW1wl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.3.20130502": {"name": "autoprefixer", "version": "0.3.20130502", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.9.0", "sinon": "1.6.0", "should": "1.2.2", "component": "0.15.1", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "2f152d1c4a47c75bf74b2fc0cec9d2c2c1e8a2ed", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.3.20130502.tgz", "integrity": "sha512-yoZb4DmoL5HkoYFgOl17LFXbWxh31RSW72C/aBD07yHliQ8KR2DBy0s4OTULmOgG8WYI2x4M7TFyG3R3iZIOjw==", "signatures": [{"sig": "MEQCIGRWKQW6rsgEIOKoaghySXG/oNYdLf6wjKIKQto3QHW7AiAgGIndmo+tpnmaErFa0Ndc/B35uZggJzf8yuBR3uVhTQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.20130507": {"name": "autoprefixer", "version": "0.4.20130507", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.9.0", "sinon": "1.7.0", "should": "1.2.2", "component": "0.15.1", "js-beautify": "1.3.2", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "ebf597a6789ceed55edc6774a1ea5f91d8b9d935", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.4.20130507.tgz", "integrity": "sha512-Y6rAt5XRliP1km3Be6hvKDkCiH3zUVGdZxVWk6BFEULUxMK/eFbtS4CAgJDgdCP+KZ4oxomanCTmAAkEiy4FVA==", "signatures": [{"sig": "MEUCIQC4E3bdI/GA8qJEW1h31hltmMERGWws3KT9ANuTa9xf4wIgX8bK8MvAi8aZNQFb+p5WsvrkRD62bASmJ2yR/VXkTnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.20130515": {"name": "autoprefixer", "version": "0.4.20130515", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.9.0", "sinon": "1.7.0", "should": "1.2.2", "component": "0.15.1", "js-beautify": "1.3.2", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "b6fe5b2a59ab465fff3016167f3f06866d4f8ea9", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.4.20130515.tgz", "integrity": "sha512-3ZTOwpsXNqsaDJ6HJIGUz+yy9Jbim9cAE2cm4A6MS03CB5yf1zaRvGVcNVQ5r2aFhLvvGbmKvjWZ5TvvVTIHYw==", "signatures": [{"sig": "MEYCIQDyZxf9jShS0DiKpwC6w5KiahZ6l2CLA+7gbIdKG/asvwIhAJiSYT/iIa0s+pa3xxQ+Bi4qvnKQpojA9gDK8LNpHrBm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.20130521": {"name": "autoprefixer", "version": "0.4.20130521", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.9.0", "sinon": "1.7.0", "should": "1.2.2", "component": "0.15.1", "js-beautify": "1.3.2", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "75c4bdc76767d1def708f6b3bb34c019b9206cac", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.4.20130521.tgz", "integrity": "sha512-e0GOQAWhZ8W5zCnwWYORd3yLRrMfhBP/sienKKOoXr3nTLWi86j4UiMLzU9T8009eaA2OZY1ZM5xR6f/9B7jUg==", "signatures": [{"sig": "MEYCIQCujvbywaXbkrv7QFRSpAxuRBRkP6ianiQb8uXqapF7zwIhAJLcvlcLzLWrXQWxi3kCq2mhhZ4gddzJp3sz45b0mDWj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.20130524": {"name": "autoprefixer", "version": "0.4.20130524", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.10.0", "sinon": "1.7.2", "should": "1.2.2", "component": "0.16.0", "js-beautify": "1.3.2", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "d127c4584487effd9a673819788837abca67fcb2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.4.20130524.tgz", "integrity": "sha512-UJh/b0QRDkNFhMGZ2TnR2ChHR4j5eSPbdH3OdCI0+//smU1fKXdBCBCMP8rsvoVESueLTFe39pfSxFh15WTFYQ==", "signatures": [{"sig": "MEQCICm0OtvtUnkSTW0Hx5dNAIb9SidAnitT8SL7KpnwvItWAiBGuTYwNb7+nriCiXWLkAIOO0Nm9tZaZ5WwtsBess2sPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.20130528": {"name": "autoprefixer", "version": "0.4.20130528", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.10.0", "sinon": "1.7.2", "should": "1.2.2", "component": "0.16.0", "js-beautify": "1.3.3", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "d4d10a626ed7aa484e58229df621a2a62d54cdce", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.4.20130528.tgz", "integrity": "sha512-t59LSu7w6FfycNaFLd+6n227W7PhoyydaB0Gqt053ldDHFQrQEwWAlobOgcLNd5CRzk6UaSSreGv/AE0aSyQGA==", "signatures": [{"sig": "MEUCIEAiJKfv2m5awGbJg1rOzLl4MTyATQGY22t9WyBU1aWfAiEAl3Q0dYjXSkWPXxPBESETYhHswQ7qAeBdrRvqg0gbh/Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.20130530": {"name": "autoprefixer", "version": "0.4.20130530", "dependencies": {"rework": ">= 0.14"}, "devDependencies": {"mocha": "1.10.0", "sinon": "1.7.2", "should": "1.2.2", "component": "0.16.0", "js-beautify": "1.3.3", "coffee-script": "1.6.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "d98fdaf2a899523c8c5b1172eab56880279546b6", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.4.20130530.tgz", "integrity": "sha512-Qsfo/7bladcUqOth2jYmFG/kYkm7zCGyw5mbaWplN8JapHbGUVrF3RnKjxF0bcAg+GsI4fd13uneWHMi+A8pvg==", "signatures": [{"sig": "MEQCIGMcfBJpltN0UmB7vuAGg8dwmPx2Wor8r9CgMWPq2/jaAiAQ7iheCdl4Ya/O530oRRgvoV7aLVx0BdUa378XtBa0eg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.4.20130603": {"name": "autoprefixer", "version": "0.4.20130603", "dependencies": {"rework": ">= 0.15"}, "devDependencies": {"mocha": "1.10.0", "sinon": "1.7.2", "should": "1.2.2", "component": "0.16.2", "js-beautify": "1.3.3", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "e51dc0c9ad983297bc4a8bd80b5218a012a786eb", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.4.20130603.tgz", "integrity": "sha512-x+X6l9IUmU9mwWNna9YoJtmwNIOZXYf2o0LnP1Dqgei9/xHbXU0HUWcTSKSUFgtOzFkWd1Tyi5TzX4I6s8E1CQ==", "signatures": [{"sig": "MEUCIBuKibwh5Qo+j8pyFD7+l6ZJclOwhA4QzY9AoVasWsmJAiEA04hWw9ykn7dCAFMr+Z0GpbcEOSzu77Npgu5woMq+Nmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.20130615": {"name": "autoprefixer", "version": "0.5.20130615", "dependencies": {"css-parse": "~> 1.4.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"glob": "3.2.1", "mocha": "1.11.0", "rework": "0.16.0", "should": "1.2.2", "fs-extra": "0.6.1", "component": "0.16.3", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "b518427db421b106c33b0136459d551315e7e1ef", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.5.20130615.tgz", "integrity": "sha512-HGivtdv/OCeReK7jqVOqif0HwwctGQoF6QW6veaAr3t5fCgUt+tX+NBF+w4KgV/D2swtVR2dy52crQSwyxjyJg==", "signatures": [{"sig": "MEYCIQDN+O1jiO+GLi9d1wDfLAid4EFB1GgOnqx1orXTNZ33kAIhAL2/qy3q3TSBSPUpNGCzHWP0fr0qivy3GnoXs6MoTew6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.20130616": {"name": "autoprefixer", "version": "0.5.20130616", "dependencies": {"css-parse": "~> 1.4.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"glob": "3.2.1", "mocha": "1.11.0", "rework": "0.16.0", "should": "1.2.2", "fs-extra": "0.6.1", "component": "0.16.3", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "ea7a9cc3614357c78edb64388e32ccd5d0d15614", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.5.20130616.tgz", "integrity": "sha512-xw1fS3oF/xmO2DnuKB6bkV7PZGUMplcutHxB1roNO/vgsyeo84hGFS40rUsm/YMAPCijnpEy9VrkeDFkzejeCw==", "signatures": [{"sig": "MEUCIQCpAIwrvj57cRTbLJq9wJOiR3M5YqouAAVJg1rHv9ZgnwIgZxvrYFCfrSFDBcZ1VC0Mcbyg6dXvbVm3uSHtmHob14M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.20130617": {"name": "autoprefixer", "version": "0.5.20130617", "dependencies": {"css-parse": "~> 1.4.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"glob": "3.2.1", "mocha": "1.11.0", "rework": "0.16.0", "should": "1.2.2", "fs-extra": "0.6.1", "component": "0.16.3", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "1eecf6fc1c83831b86531f13cf9cc3bb6f7aa581", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.5.20130617.tgz", "integrity": "sha512-uJ0a4v/O75bHGVMMGYq9RUg/TRW/GEYWk6NJfBPFXKHfYHTcw6S2CtZ8hrjd2cvAbOdKsDLyQF8lm79Kg+vNkA==", "signatures": [{"sig": "MEYCIQDmvvUzGeLI/XiF/xz2H74ETe7JvPHIAfvT2ZlbZuQbFgIhALd4aM7VGF+cKRM9iVSIfftDX7pAQtCSdia1WD3a90iO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.20130625": {"name": "autoprefixer", "version": "0.5.20130625", "dependencies": {"css-parse": "~> 1.5.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"glob": "3.2.1", "mocha": "1.11.0", "rework": "0.17.0", "should": "1.2.2", "fs-extra": "0.6.1", "component": "0.16.4", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "7dadbe1a7042ff1cf4c837838ffd4e6ba1e8512f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.5.20130625.tgz", "integrity": "sha512-3myJzGnLhT9O0Bvk2+6CCBptLOqDl9avJYiZhl+n8LA/BW9YgxdNVzHyJ4RzjVT9H9EepIVuiFUV6SfU7EtMwA==", "signatures": [{"sig": "MEUCIEVo1f8CJ74Mmf8UBb60MUQsZPtu18XQprSpqWxpFKEwAiEAy/ocUXWn7xAhqYEoUyWfiey1O/a0Exw47nsyOkh1unY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.20130626": {"name": "autoprefixer", "version": "0.5.20130626", "dependencies": {"css-parse": "~> 1.5.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"glob": "3.2.1", "mocha": "1.11.0", "rework": "0.17.0", "should": "1.2.2", "fs-extra": "0.6.1", "component": "0.16.4", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "0716c2379f835e1dfbd7296a7a19d0e5cf96c22a", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.5.20130626.tgz", "integrity": "sha512-eKwvopKoV56nnFWkf8fbhc/krD/LbiEjSzohOJ5cY9bOhBJlkXG49lwt+1/ObTo9y6bmq/CMwNkOsjbnV2uKoQ==", "signatures": [{"sig": "MEQCIAu7uG0YOrdRkemWCASo6oRzVI2/xs9wTRRiL0Jj8OhjAiA0JZE87WWfL+mkOqPvOm+rPF2Z4/phaKMmSoeBd4QCkw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.5.20130629": {"name": "autoprefixer", "version": "0.5.20130629", "dependencies": {"css-parse": "~> 1.5.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"glob": "3.2.1", "mocha": "1.11.0", "rework": "0.17.0", "should": "1.2.2", "fs-extra": "0.6.3", "component": "0.16.5", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "94dfcdaf7d530df83e1f803e87da0350ed1fde00", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.5.20130629.tgz", "integrity": "sha512-/BQSoabupFMPP5a4ez2W53O2fuJJWanMF/YK1WFVikdddRvL8bqcO7Y+0X55Z99yCi/r1afM1W1JfybMKHyZ2w==", "signatures": [{"sig": "MEUCIFUJLze6bERmddJd7OJXVUmKVNsIC+8XTeOcpF6TEiSNAiEA13m1j6+xZnb/1zCDc3Cwq4tERKMW57fbOZt8Kv5tTyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.20130716": {"name": "autoprefixer", "version": "0.6.20130716", "dependencies": {"css-parse": "~> 1.5.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.3", "mocha": "1.12.0", "rework": "0.17.2", "should": "1.2.2", "stylus": "0.34.1", "fs-extra": "0.6.3", "component": "0.16.7", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "9cb6cdf0ca2d46d47d9fe542d75bfaac305ca91c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.6.20130716.tgz", "integrity": "sha512-gwQuJjtRQHYo84q3833OJz9PMLvLkptVRQ+rVcqvC30sPzFmqDow/gcxOs04H9bFLdmOZ7B0X0OBGc573Bsh8Q==", "signatures": [{"sig": "MEUCIQCXKiqv5S4A8bOgE1AKzkwFvU6p6QoLXH+ce7DplJCzVgIgXHZAWQnUElZtBiPHCgLsY6y3ekQjDW45fbkV5MjygRU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.20130721": {"name": "autoprefixer", "version": "0.6.20130721", "dependencies": {"css-parse": "~> 1.5.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.3", "mocha": "1.12.0", "rework": "0.17.2", "should": "1.2.2", "stylus": "0.34.1", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "dc408546bf1a8c7bea9dfff1361f68d650a2fbfc", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.6.20130721.tgz", "integrity": "sha512-6XxkKuotfOK6F0gS6qbYO8Z2Vr5uAl4JXcEj42xVgkJd8LkPto1ACfviAhvDTsnlig6DAbaqrAR2xEjlGov1nQ==", "signatures": [{"sig": "MEQCIBxFJl87f0QcMZ5WPmyxnsSWfe2Pv51R6CwPlrf/4rdoAiAN2Zjlcm7JfhavusfF7y+UscPBRGoqMeBLnvfP2k10aQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.20130728": {"name": "autoprefixer", "version": "0.6.20130728", "dependencies": {"css-parse": "~> 1.5.1", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.2", "should": "1.2.2", "stylus": "0.34.1", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "6190702405dcf020bfa139b9a5a6bbe46613d8b1", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.6.20130728.tgz", "integrity": "sha512-OHUiGoIzY/kAbsLWRhqy+JrglWcFoDJkradjatE154d6Ov+PReFr3C2TRciKMr8GBu2pyferw16PBjdWyU0wEQ==", "signatures": [{"sig": "MEQCIDuht50X7vVyZ/Pq/4Iowrnt3qzsqHy9kIE8Cysoo8uTAiBazxvHQ1/ChmHPtTBxA8+br35tsJzlasbIXoiSJZWy8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.20130729": {"name": "autoprefixer", "version": "0.6.20130729", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.35.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c07610ee6869827dd1d59974c0ce79250296c626", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.6.20130729.tgz", "integrity": "sha512-bfai5ah+yfZMFiULDps/AylMnE/v9Fi2/BurbHHZ76hNCy1cBIPXToiZC9ivEeHUq917NkZiQX5BmB7h/Gy/ug==", "signatures": [{"sig": "MEUCIQCVgcV9YKwT0GvdUoM+VvlzvnbrmkStrOcOfd6Tc9+/IQIgb9ShcTSihimvRhOTxNWlAaA1fauGSB4TTECS098dBYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.20130730": {"name": "autoprefixer", "version": "0.6.20130730", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.35.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "57482b1e875fcc623004f29e04676ec71888986e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.6.20130730.tgz", "integrity": "sha512-TkA/OcuIce8zDThr6hHpRb2b9F2rTTNtOWXN8kfsQop8viIDX6nKk+67Js3MkZiOYmRNCzOa1AsLppgUdvLGbw==", "signatures": [{"sig": "MEQCIHE2+wMffnbIBphUUD0opGyN7IJJFSBHpeEh2av5TRG6AiBujEzyJ4DfoVASF6dOBrrKrRi7bQgUPfTTV8kEfV10mw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.6.20130731": {"name": "autoprefixer", "version": "0.6.20130731", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.35.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "43844a3611f26c6342b1abf510e5df57dcb3853b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.6.20130731.tgz", "integrity": "sha512-DOoYeugGwc4zFh3mphVzeFTX6UW4QLtMFQEOFyZ5l9ibaIhXqNGFepmaTnYPBZcU9ctT5cGVzarRZ66litTPrw==", "signatures": [{"sig": "MEQCIEv7hdPIHD//sq+0QIiERQKqLFPc/q4aVff5ds+lPMEiAiB7l99XlCOe3yTGRDAe5ONN2c/kHUElmYT62hCeFCCqRw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.20130805": {"name": "autoprefixer", "version": "0.7.20130805", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.36.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "d78eca87819fe52b5b9820a90f45401e6d989b14", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.7.20130805.tgz", "integrity": "sha512-UNZmrGlTzTvU7aB/jRLYhdX87jHunL3T/TKkS5WEQzqf/7U7JLdgJtErHOzRLIh/ciGfYeWX32+6mudxjBSiJA==", "signatures": [{"sig": "MEUCIQDQiMjz+AqjWfbQArpJPhnBRFXmJhsJyzhHhVx8Dt40SwIgCToI73/g2aoyZIhW6H8tmRB81y+17KBt9OuA09vXwpQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.20130806": {"name": "autoprefixer", "version": "0.7.20130806", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.36.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "0c8aa3684169d7c007416dc0f2330db9eb57d269", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.7.20130806.tgz", "integrity": "sha512-BoMq7CpL5CknWV41xvdcQL05jbOXB8mVoUHM7C7NcH2MB9K9+LHVmSpGhBg3AhiBWLfs8ecDpmcrOLfSOc8oYw==", "signatures": [{"sig": "MEUCIDxobLs+WQoK2AVwbuT5JL+cv/JyLmfqrexKHjf2MHcyAiEAmY3YB1TLhsi9I35sXCMOIpZRQ4+kMrHvqJ24O57tRaY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.20130807": {"name": "autoprefixer", "version": "0.7.20130807", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.36.1", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "65a42da469db2073b7ef2e47519928140e4ff203", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.7.20130807.tgz", "integrity": "sha512-rWL9HdG8s6K5KPxxRrtJm8hzMHz4HthzOx+7taHMfzZN6tX16r+4fut1uL2ImPd41LoYJvc5bvm5QOIiZUIXQQ==", "signatures": [{"sig": "MEYCIQDuEsUZ0y0xjRP8rqwZrijSbHCnxoKFaQKF+xyKQPifjwIhAJqMOza7n1oaIP1ETRnl3yoovluiMayKSUjY9DA5zmnE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.20130808": {"name": "autoprefixer", "version": "0.7.20130808", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.36.1", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "a3a0dc1870db9f1db6f65fc40768f19227c15707", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.7.20130808.tgz", "integrity": "sha512-ech4B5S/x0Yczm9vQZlI7fml+E5be1lWH6J+/zCRCUCnYPB2FhBW9yg+cvrO3nWMd09zUE7FI7LDd0Lm8pG1vw==", "signatures": [{"sig": "MEUCIQCw/KRrsRH6YWq1GG1hNuz9vTtljjc6Y7pwY8T3c3WAdAIgfN++KlZfR+YUkeH5qM3eKzdVj2D/+DDLBdMxLcCd0c0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.20130810": {"name": "autoprefixer", "version": "0.7.20130810", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "0.9.2", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.3", "should": "1.2.2", "stylus": "0.36.1", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "3b32d09a72f3a548192490ceb2c71d5516076f76", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.7.20130810.tgz", "integrity": "sha512-CLV6lhaO/W60ZVsyDFnEz4QPDjvBZiTLwzx76z8lGjKgg4RmIs+PjRsAWaih4r4xWErPzMLJV6YM0kUQ2NQfbA==", "signatures": [{"sig": "MEUCIQDWs7yQ6Z0NcWIazssPG4YKC8aAwOI+MdcSMUF9jgmQTAIgNfzLosTjRfEJ/sGeR98/qD7QVf+OuyiFHhDd+2VhFpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.7.20130824": {"name": "autoprefixer", "version": "0.7.20130824", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.12.0", "rework": "0.17.4", "should": "1.2.2", "stylus": "0.37.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "1fefbe10f9615844e4a47dfe51a555e7b4546ca9", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.7.20130824.tgz", "integrity": "sha512-FGLnSgeb6jf8e/HQd3/yoQKWqVpJwqYxQRl0cVoKnW3RyQw+ZKtHruTQuwrmfbS21Zy0QBs+jWcCBnRx2x9fKw==", "signatures": [{"sig": "MEUCIQCL17jLpC/hc7hgVwtlrlBH81fRw1xsalu1FOC9WeadmwIgIB9R+2SmI3ybRVQ0NzwkgVYFDhGLRoQ95RTdBg0I688=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20130902": {"name": "autoprefixer", "version": "0.8.20130902", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.12.1", "rework": "0.17.4", "should": "1.2.2", "stylus": "0.37.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "69cf6addb39eaa119ad41638f2166f5409c6d4c7", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20130902.tgz", "integrity": "sha512-piUx7hYALbZfdfMQPzBCSKLSkYG+iv9ckZp4Sn/U7dULR3oyca04CrRkJcoyfW8Cx4LcOf4umeZYUV1jl9BggQ==", "signatures": [{"sig": "MEYCIQDIP6qe0StbDhIprgvpjHwIgwLDHf5S5lCUfYAeOQbyfQIhAJOP3Tu7WZLyIwgF4Vy/nHFRrgjkqcbizNosfkHG1gzO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20130903": {"name": "autoprefixer", "version": "0.8.20130903", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.12.1", "rework": "0.17.4", "should": "1.2.2", "stylus": "0.37.0", "fs-extra": "0.6.3", "component": "0.17.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "83c6e3bdf940a3e341d4db455db0fdde6eae4895", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20130903.tgz", "integrity": "sha512-vBIvZ9WhpD3yPOKOYl3QtjBpqKzoDa8nToBVGUNKh0I+cd1MHhja6v36fGZG9zLyco82r41TVgXB1YvEtTDT1w==", "signatures": [{"sig": "MEYCIQDs+hWzCc6Ma2hyOtmbMohUE/LHKk2htpKn86rsztPJZAIhAOvZKlhcHk7OMYjeP9sVgT3LaAGUqE3gO+u0TLa/kTY1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20130906": {"name": "autoprefixer", "version": "0.8.20130906", "dependencies": {"css-parse": "~> 1.5.2", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.12.1", "rework": "0.18.1", "should": "1.2.2", "stylus": "0.37.0", "fs-extra": "0.6.4", "component": "0.17.2", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "eb7ae450ca2e5dd3fab333f4201c3e18a2279a1e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20130906.tgz", "integrity": "sha512-NWKXsWDqtHDLeXov4QeD8vPyqg8CCJpTLzZ5mTPVEb+fT4A/FkV1ANKILv66EXKzQcCRDiE7flyUQcY3V7/yiw==", "signatures": [{"sig": "MEUCIQCWb44KcO9DKvdaEvuXC1aebThLFOuSbeWdCOl4gWMNsAIgA/w5YHjCvfZp4bpQM9QJagGVPj3Sgy8rW7yPT8U++8Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20130911": {"name": "autoprefixer", "version": "0.8.20130911", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.12.1", "rework": "0.18.1", "should": "1.2.2", "stylus": "0.37.0", "fs-extra": "0.6.4", "component": "0.17.3", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "3a2d5c33266c8a22325e3a88f5e0d1f7911224ad", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20130911.tgz", "integrity": "sha512-6NTfSFySbZt3t1ELQL8RKI60hFvPA8ogi4nn7xPryGOQuqkfjmqHluRJQadXyBqv38t8mSRAJtqNoIAJY+NFAQ==", "signatures": [{"sig": "MEUCIQDeJ92ytqMLDUGmC/DuHJBVKpb1iTQTAKfCX4qwDxhU/AIgfKnBoTvKrpT4Ypwk8hyQbEJv7g+K9O0NXUG0X29npB4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20130919": {"name": "autoprefixer", "version": "0.8.20130919", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.1", "should": "1.3.0", "stylus": "0.37.0", "fs-extra": "0.6.4", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "2e5e7938090104f34e46dc753d02d71d6fb3cd0b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20130919.tgz", "integrity": "sha512-xOhb0Oub7+OhIVV/SDTuPsWzekfOm2Jazhy/u7yH6JSUte/N/+GLNe9OK58gD72QeRRl4mMiyanXOpLIVExxsA==", "signatures": [{"sig": "MEYCIQDOaC2XHpxxtIUtYf4KRVUCjjmivxjfretyn8xBlQyJXAIhANXtBc6pMOecExYuNPBBnZNJSSoR8kX5/L105cpdhYC7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20130923": {"name": "autoprefixer", "version": "0.8.20130923", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.1", "should": "1.3.0", "stylus": "0.37.0", "fs-extra": "0.6.4", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "9b2a36b9e26900db85eed4f72ff296351c98842c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20130923.tgz", "integrity": "sha512-HIXIUt5M1aMLu9r5ANDVOzEEXdM5rGl+noMpk/Z6cnc4s/bMxIhPAuJF56oVtBqt+5WfyEZIIqYcjSRBtxDwow==", "signatures": [{"sig": "MEQCIBsLTkFgAFWrLCb/apCJm2cvbaBP5FGqfevhpdzyiqu9AiArUoVu5GxgxP5HRtergenW6CGc2Pj2tJ4Ymov2ZsgJ4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131001": {"name": "autoprefixer", "version": "0.8.20131001", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.1", "should": "1.3.0", "stylus": "0.38.0", "fs-extra": "0.6.4", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "89ceda30818afd81bc4e9e7b66c35dc84ef59dfb", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131001.tgz", "integrity": "sha512-GJ53p8EklCq5y9BDA7HukExoJ4LG7nXkQyOM/7SHABHUqOK22qi34Z95IZzU7+Fx+DG1CTZNt/A5MXTb+Z8Rwg==", "signatures": [{"sig": "MEUCIQDfnSZYFtQHE4JFZ+Gta1xmRvj2kiADd5pKkb9qLo5V0wIgPCvqFzUivvapDRNYMKWlHjRkRRi05UWPcxYDZfkVzZA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131006": {"name": "autoprefixer", "version": "0.8.20131006", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.1", "should": "1.3.0", "stylus": "0.38.0", "fs-extra": "0.6.4", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "244cb9b37631aa0a8e8a333d3ddbc648312b0d22", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131006.tgz", "integrity": "sha512-CEJivBybvC01wWQr7n8IbqPESd9DAzqCmalmx+oBLD+8ptBgi5nkinMTJKyHpKEm3t04B5BCTk/KKkrIYsB9Ew==", "signatures": [{"sig": "MEUCIQDb2v6wngonYQCT8CNrymdsOlJBLykQ98Qk/GsfgTpMawIgMTHfFz1S1wS0sceTT44hcwE34gtXW+N7JxZayMHgak8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131007": {"name": "autoprefixer", "version": "0.8.20131007", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.1", "should": "1.3.0", "stylus": "0.38.0", "fs-extra": "0.6.4", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "76e1dfec5068395314a066136747a98f61a03f01", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131007.tgz", "integrity": "sha512-Zs2fK6ru7PSoLMNcvfr2q089NIRaxk5iC9076y39Bg31st0GAJzn6Rkg7vkmJURwk5bACs/VL7Djj7TDohe1ag==", "signatures": [{"sig": "MEQCIBTmp0g/sR2nCC3BtUvi1Nb/3oG8jTdbvx5Tti4MdieBAiBruNH8p8Of522qIOWFOFrGUFrUnXXpdC41JznV1fk4hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131009": {"name": "autoprefixer", "version": "0.8.20131009", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.1", "should": "1.3.0", "stylus": "0.38.0", "fs-extra": "0.7.0", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "e099983dd1b2a59f89479c2ddc85995feba8113d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131009.tgz", "integrity": "sha512-KOQKK90jGlRW4dO6/NtM1wQsLPFtMh9tDIwXNmcAVfGQnFBScX5g+3+LjluYiowe7sS2cHjzvJ0vNIEsSevExw==", "signatures": [{"sig": "MEUCIAe8rpU7qpaK6CPGT9rFJEa2iz9kutkJnmtsO726I+lRAiEA8Ht7aSMTEwCt3xxO6sXjdA/IO8LXwkBbAQB0TzhmKbM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131015": {"name": "autoprefixer", "version": "0.8.20131015", "dependencies": {"css-parse": "~> 1.5.3", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.1", "should": "2.0.1", "stylus": "0.38.0", "fs-extra": "0.8.0", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "75864225d6970ef844c0cf6db463f77bc067d635", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131015.tgz", "integrity": "sha512-SJSHthvifLgckAiFYFmCQ0EvylNtfSfwl/rACrxNK2mGTazhQoFvolRIY76xB5pFLmiHNY0sy37oewIHP1yTnw==", "signatures": [{"sig": "MEQCIEvqc5azeP6F77GuKYMGIIOll2A/ZDOLaFnbjZTqfCMRAiB8Bn2edpKZOO3mB3QLocd9bpLbutfPklvaM5WB43XqIg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131017": {"name": "autoprefixer", "version": "0.8.20131017", "dependencies": {"css-parse": "~> 1.6.0", "css-stringify": "~> 1.3.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.2", "should": "2.0.1", "stylus": "0.38.0", "fs-extra": "0.8.0", "component": "0.17.6", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "44be191240baacf1374c212cf7b93357c68ccc1a", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131017.tgz", "integrity": "sha512-KSOAFhcdAfsY2aTY0oy2DeF28xFH5VPs3PngCp6cnaNZ9jAhG/fh0k6AP8jna1fhTO9QKihHcyZK6iiJ0SsjJg==", "signatures": [{"sig": "MEYCIQDpw5Isc2/OTCzXax2/xCDmI897siqF5gLIkTrxcCol3QIhAP1ETyjDS+2b2HVcIva7kv2vsKDvHmz8QACWIO1igLIp", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131020": {"name": "autoprefixer", "version": "0.8.20131020", "dependencies": {"css-parse": "~> 1.6.0", "css-stringify": "~> 1.3.2"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.3", "should": "2.0.1", "stylus": "0.38.0", "fs-extra": "0.8.0", "component": "0.18.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "494ee28c93dd39be75f572f86000fba2f4c27dcf", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131020.tgz", "integrity": "sha512-Ov1jQ0b6GonLuSb1cggEehjq53ROUVaCC0bDYtnpnBWHlPOfWMieiL7Jf4ITLHAXhCBu+liPIRM7UqqU01ZVBA==", "signatures": [{"sig": "MEUCIQDY+/ivawantWlcPwoXsWeGzj1utBxgidyEeWFgyZxFgQIgCQe9uJJKxkrHkhr2m9Te6kFKJsld32Z84n0/EXyhZi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131029": {"name": "autoprefixer", "version": "0.8.20131029", "dependencies": {"css-parse": "~> 1.6.0", "css-stringify": "~> 1.3.2"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.13.0", "rework": "0.18.3", "should": "2.0.2", "stylus": "0.38.0", "fs-extra": "0.8.1", "component": "0.18.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "3a8aee5bae85c8d364f472da3430a530827fdb66", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131029.tgz", "integrity": "sha512-o2D++z95fjy0VmWQ131b0JflwIsOJe7xsYi5cWP6yQWO/iBDLRr5TOpsGKP98SApj00sMrkI9HKMs3pDNVVElQ==", "signatures": [{"sig": "MEUCIENoHjNQz/MFvXk0b/oM2+mWiTaje84+lIUSnpIHBT03AiEA0jFN5yyQw0mXJOwEhNR1xOaCeoKlfpwGkiZ1jkUT0R4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131104": {"name": "autoprefixer", "version": "0.8.20131104", "dependencies": {"css-parse": "~> 1.6.0", "css-stringify": "~> 1.3.2"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.6", "mocha": "1.14.0", "rework": "0.18.3", "should": "2.0.2", "stylus": "0.39.4", "fs-extra": "0.8.1", "component": "0.18.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "39bdf9a7e909b2be0cd7931be6f98c2c96cd67c7", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131104.tgz", "integrity": "sha512-OWFSZpTFBECO3bvUysiOI++xBN+DtCKN+jY8Nd9zyO2WWzjBB6LVy5KD9Tot+/inxGky7JsGDZ9g/mdTQttNrQ==", "signatures": [{"sig": "MEYCIQD81Nb2JlqkG4jVVmpS5CwmhjB0gLWGpEyFUnDgHQ1lRQIhANtJl2eKxuEfR3rghJ4dMqfjAJp+w8F4zN/z7o2/L47R", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131209": {"name": "autoprefixer", "version": "0.8.20131209", "dependencies": {"css-parse": "~> 1.6.0", "css-stringify": "~> 1.4.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.7", "mocha": "1.15.1", "rework": "0.18.3", "should": "2.1.1", "stylus": "0.41.0", "fs-extra": "0.8.1", "component": "0.18.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "90ace7e8bbb6af085758fa0d3d3d4ddaa425324f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131209.tgz", "integrity": "sha512-I8mi1mPfTwcAC7h8t+u7FFpNfrkds033vQfMHg1Cyp2P1Ovk3ELgrYF+CvbVim6hz5tenv+icQnCf/EzthYXag==", "signatures": [{"sig": "MEUCIHErouHyIvjwjhH9gEDjTabLEYeXEMukqXx6qA4KavRAAiEAspcvnNojWkGOm5BXWjRDYRPPqZj4F7vFy+N7UuJ2m18=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.8.20131213": {"name": "autoprefixer", "version": "0.8.20131213", "dependencies": {"css-parse": "~> 1.6.0", "css-stringify": "~> 1.4.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.7", "mocha": "1.15.1", "rework": "0.18.3", "should": "2.1.1", "stylus": "0.41.3", "fs-extra": "0.8.1", "component": "0.18.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "fe0a9166576087e2819758bd09ddf62859fda20d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-0.8.20131213.tgz", "integrity": "sha512-Aoxj5E4zF41nnERg6ZXnUJOltrGFDyqVd90SJPrkXKDvkmESb5LjDpbSvMfWz0T3vjC7RJVXhAegmdkROM31Aw==", "signatures": [{"sig": "MEUCIQDqr3YfuwUl9bmNxMT4P3bHZtyMSU+mp8/ltV4SkXz5pQIgRmPAFxyxjG0z23lktEW0ZNwhhfzJ7HrswPfzCjkcuK4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20131222": {"name": "autoprefixer", "version": "1.0.20131222", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.7", "mocha": "1.16.1", "should": "2.1.1", "stylus": "0.41.3", "browserify": "3.13.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "87d2d65e8ba6728fb25498814731cf45b5cfcb16", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20131222.tgz", "integrity": "sha512-toDYxm5E4vNxr1LMxmAGiEndUKnkmUxNfu5Goc6Ill5x99y78qSMcvVkkfVFtCnTntCinmL74mmn+P9VC5+OjA==", "signatures": [{"sig": "MEUCIQD9KMgO72hzJ9tUe4ZZFWjegkT1GGUSe+Ocxl6hkBkNUAIgFpL3foKgRFyOmj3Kl4AeuwPWNoOrZT7xnterpvqnq8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20140103": {"name": "autoprefixer", "version": "1.0.20140103", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.1", "glob": "3.2.7", "mocha": "1.16.2", "should": "2.1.1", "stylus": "0.41.3", "browserify": "3.18.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "cde864bc248892fc878b1b7fa696d21e869bccf4", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20140103.tgz", "integrity": "sha512-FYTslAOGccNxFsHK99LMECoURUPGhaSutkR+Y/QVGSqy9bmtOFqf5yOOOgS+NDnCW8Jjwwlf3NG32/rkPsvqKQ==", "signatures": [{"sig": "MEYCIQDRirHRKALe3oH79fWkEuHhdyYbZs+L2AjXlt4hWFPSXQIhAOJWY1IXxPBqf0SaJ4b6u3v3402QugjDxDY16eSxKMKE", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20140109": {"name": "autoprefixer", "version": "1.0.20140109", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.0", "should": "2.1.1", "stylus": "0.42.0", "browserify": "3.19.1", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "7af0c3c4536617dcf7cef325d76de28033633787", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20140109.tgz", "integrity": "sha512-Iqo+BBZGUc9g+KHKcnRDio+M3I0zfX71OWLx3ElJn+/xgjW8RNbHn04PWJg6scsPtfwn/iAA/qBMnInEIfBb/w==", "signatures": [{"sig": "MEQCIDthHekpyuRjLLc7XXWtVt6sy5WNUUNwrMRlK1jcXEQDAiBcZfdufkFT2Z4RFm7HMW65MmYA+6kVhHxbaBLi0h7rQQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20140110": {"name": "autoprefixer", "version": "1.0.20140110", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.0", "should": "2.1.1", "stylus": "0.42.0", "browserify": "3.19.1", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "2c1cc546fdb6b4aabf3ea3e3b84d0a86594c1648", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20140110.tgz", "integrity": "sha512-WB5zamYjPva3+Rk+rKDfpczAJKthhb0LjmCWsEAs04UK56uKnIfvv5O6RL8PBI422RicYkdlJenHxE9QcH8aTA==", "signatures": [{"sig": "MEUCIQCzyYnt0BBBBsWsP3ekY3vv8pbagIuuThw9xm+R6swFKgIgV1TLP69iRd1zNqkqCwYveqdsT+thwgWUKninWT9I9dE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20140117": {"name": "autoprefixer", "version": "1.0.20140117", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.0", "should": "2.1.1", "stylus": "0.42.0", "browserify": "3.20.0", "coffee-script": "1.6.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "1b4706f06f779c86ed316fd7eef1f335939c20f7", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20140117.tgz", "integrity": "sha512-7JYpYZ7v1FSIVIzIkQP040CpEWTZCAEVOTpmoD8018d0sJiBo9gmYjzw4UhXNDX3XNEHrUFXShEziqRbxGuC7w==", "signatures": [{"sig": "MEYCIQCF5VS5BhK9lIqt7NRs6oBeMezGKUmrr+mwum1M71tVFwIhALKb7MGF0exm4nwW8MyQy6xqrHX7WiU3qkGgAcbGaAps", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20140130": {"name": "autoprefixer", "version": "1.0.20140130", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.1", "should": "3.1.2", "stylus": "0.42.1", "browserify": "3.24.6", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "75bb4b3993b1179d412b68832908a4ad23fa218c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20140130.tgz", "integrity": "sha512-Et0WWB/BqKoA8BkzIyfrx+Y0N290GEx8Ms3t8dy22MZc+xfEttytaaBx7XHK9Nf7xot7Bz0QaKPFcq6YKm7qwg==", "signatures": [{"sig": "MEUCIQDpvAFz0cJJHadeX2H221uWgT6RqTKMCwDyFB1WkLJ9AwIgL1CT+NciySTFPHCNgsmBzp8TMuqLYltBK1DXtgT0sxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20140203": {"name": "autoprefixer", "version": "1.0.20140203", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.1", "should": "3.1.2", "stylus": "0.42.2", "browserify": "3.24.10", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "7894f87532e1b9fb907f367f9de3327bc457d1d7", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20140203.tgz", "integrity": "sha512-EIGT0HIn2kkSc6KDLRcbB7XzoWcq/IQex3i/lCJo4snJaw7FtByuZcCjs4BE3bR4Ic+3gsKy49sd7ieC/g0sdw==", "signatures": [{"sig": "MEYCIQC9Y9YTqv4TaIX7VCb8/U344Igusi74v2gRCjhscV+vDQIhAMq148PSgNl2GExLKrJe3CasTwLXfXE2JoQShnKEcSDm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.20140213": {"name": "autoprefixer", "version": "1.0.20140213", "dependencies": {"postcss": "~0.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.1", "should": "3.1.2", "stylus": "0.42.2", "browserify": "3.28.2", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "2557eaddf931421676b65d0445fa0acdc459a054", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.0.20140213.tgz", "integrity": "sha512-A90Yjvmf+Ww0NuWfYyqUshQTc+tTIo8p+gGeJbaPTdAQ095+EpTfC4GaxlBZ1fSJbE33Ouz4NipP1/z8XeZL4w==", "signatures": [{"sig": "MEUCIQC8bLknlNo78z/uoHYni1liISew/12C0ktzv2uz1i0WWAIgaWN3P2TWWFJy247qnjrcKoXXhtkwsDfG6MpI2rBSfkg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140218": {"name": "autoprefixer", "version": "1.1.20140218", "dependencies": {"postcss": "~0.3.1", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.1", "should": "3.1.2", "stylus": "0.42.2", "browserify": "3.30.1", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "8c41399875dcc872caa89234c2f46aaa29642aba", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140218.tgz", "integrity": "sha512-6hIrp9awuswWyqIcqjvAQJHOXcdLmMFbq2dP4Nhi5DobL8g8fcTsQnUjhlUmwc+gPpC09/3J8OzsUUTaxtXUlw==", "signatures": [{"sig": "MEQCIB35sgcYhA9Vy0YvO2sZulzUnNg4PD0icAUyKKo/vUbKAiBzVclt38mX6QEoDo6xhWvcSGnNqYocJIm/U0jeRPs+nQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140222": {"name": "autoprefixer", "version": "1.1.20140222", "dependencies": {"postcss": "~0.3.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.1", "should": "3.1.2", "stylus": "0.42.2", "browserify": "3.30.2", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "0ad5eaf4064bc4ab0204cd85dd1feab4b67656cf", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140222.tgz", "integrity": "sha512-f9UQXVsyXixKmx5648mkJwl0XFnmKF1tX9K8rFYmTo6NBGLC2KXhDeqfm6qjzKc+fRr8Kzmxey+GNJlkuUA9jg==", "signatures": [{"sig": "MEUCIHJcDANnOFZfl0WjQSaYxqK0rslmEgyTu0k13/8decPpAiEAsewb7G/8j0SwDPCoPK8jGSFe+Jpz5v6/zDYt04Xyr4g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140226": {"name": "autoprefixer", "version": "1.1.20140226", "dependencies": {"postcss": "~0.3.2", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.1", "should": "3.1.3", "stylus": "0.42.2", "browserify": "3.31.2", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "da294050ea8aa0292bbb31aeb4687152ca5f3b87", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140226.tgz", "integrity": "sha512-IHt2DlfhR3EAUGSM5m0WxltWt3FS42KL0NNyFu7fTfPgJYmR3t1sw1OHT8xUrQfipgzCOW4yc1zK4EWmzYderw==", "signatures": [{"sig": "MEUCICos80PxE6mJbA1N/wyXitEQS44DRkvahjbAwqQ6rx2rAiEAsg1HayeVPnRg1W+heup7QWN7lwLsp/C8GNvH1et2HyQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140302": {"name": "autoprefixer", "version": "1.1.20140302", "dependencies": {"postcss": "~0.3.3", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.17.1", "should": "3.1.3", "stylus": "0.42.2", "browserify": "3.31.2", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "4de90cf16a77492188f3cb9e91875828ead376eb", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140302.tgz", "integrity": "sha512-EVvpx/apMkux6FrW1mXAJ3lZViLmJnolKI+qybrK/6Qi57upR/JNau2LIDISQ+3Mvo0cWICEbMBeJti2ALjllw==", "signatures": [{"sig": "MEYCIQCF6OzUxAudjs3BI370TRIkfqFAahANTaZS7lJP/gm5cgIhAJBV1a1YbogbTXsXFS+8k9L0mBrFch6tTesxspAmus5n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140319": {"name": "autoprefixer", "version": "1.1.20140319", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.1.4", "stylus": "0.42.3", "browserify": "3.33.0", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "dc727872f32076b6a3108773d8bc748e1de97d61", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140319.tgz", "integrity": "sha512-i695G7+nz1/dkbldM6cDTv3WTJGI1QljWdeaamNysfNjYJ1j38mbdochE5E3hc3Kfp2xKCwOdvU6A999z4lXBA==", "signatures": [{"sig": "MEUCIQD4LEjdemP+XhOt5zlMZgsshPPwIxcGSWfAaHMSKoNW0AIgMLohoO/zxDfxcOomJH23gUv94r47yvVTQRE4DGUk2aY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140327": {"name": "autoprefixer", "version": "1.1.20140327", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.1.4", "stylus": "0.42.3", "browserify": "3.33.0", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "530d78aa3e2f740ce45ca17ffc92384bb4f3241e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140327.tgz", "integrity": "sha512-3gVmLExL7vxO2xniNkHzawXAKe008ZfGxtc1nrxEnTpmOC20gAMW2cOPNN4V6iQLLrAxiZtRlYFL/eIGa8iFmg==", "signatures": [{"sig": "MEUCIQCvTuzH+QvuIxQ82ZkqyAUtu4SwfIUIOQ+VYcPmhHQpAAIgHNlL5KXLneCj6OE4Q9h82W1u8+/f6w9wMQ+ZGgnj4yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140403": {"name": "autoprefixer", "version": "1.1.20140403", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.2.0", "stylus": "0.42.3", "browserify": "3.38.0", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "dadf0ff234c51b4d05f20fee44e0cf0997f6052e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140403.tgz", "integrity": "sha512-i6hqSrONKlsN++DKpR+JhvW1hW4xoAdLXxXk+EQfIyo0/+wgSfHzX9UOvUIEiJi6qv5Uk0KNsjb/tFdK60dXHQ==", "signatures": [{"sig": "MEQCIHAKXfjt9xBQ8rVAzcgPH4mYXzAW4IEsR0kaJZYNvR/fAiAneqdqA6+e8TFXBsmFJ4iF75QfkjueC5N5GtpDizqodg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140410": {"name": "autoprefixer", "version": "1.1.20140410", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.3.0", "stylus": "0.43.1", "browserify": "3.39.0", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c57173ea6c13468c4fb4a26dae36144b86b34c29", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140410.tgz", "integrity": "sha512-SMDFGirF1wC26S1nwkPfLetnOtbW6ItYFzSKxKggpSNzWGMFuzjgEfUgN4be1xR4WQcOA8cSvbr7zZHGMwK45A==", "signatures": [{"sig": "MEYCIQChJLsrX/HV5EPOxBxY7xln+sdY7AG1HhIU0Tm4kbNNYAIhAMTx09JKiDB/0R1Bc6pwmFCQb9r4eHfPKPjtBM/O1OYL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140429": {"name": "autoprefixer", "version": "1.1.20140429", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.3.1", "stylus": "0.43.1", "browserify": "3.44.2", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "197a824eb17df27f2dd5d621987b57da06c4331f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140429.tgz", "integrity": "sha512-jOrMvUgMAkcoooVT2BoYvGu/EwEriAvDgBzcdrp31gtp+9IDo8ADCaDbwLsj9b//8QZp38KaKiCVNsxtgELvLw==", "signatures": [{"sig": "MEUCIQCOzsd5hF/LN6w8KWcgxisbre+OHurXDwOqZ8Yg5PkdmQIgV9j4HD6omrvA4MstF7B9qLKkukzHLg/f0MR4Ntodlr4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140430": {"name": "autoprefixer", "version": "1.1.20140430", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.3.1", "stylus": "0.44.0", "browserify": "3.44.2", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "d7bebc41bd6729b2fb9081325e5d0dbf7fcca5a6", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140430.tgz", "integrity": "sha512-NufBDO/QqzFbSR4PHArcT/QRAR7jRw12HcO7rvMqrPmyGQhngBAZ4Rb+HJgJSWqzcQHIPA0Z8KKdZSX4Hkfm4g==", "signatures": [{"sig": "MEUCIDQkW+d3ijt+d54A1TCeOmgsoqH4r4AEublERyvl+3x5AiEA0/s6OTuFH3I+1PC512lQWSuClUVn1Lfh+E7jM5tjdRQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140510": {"name": "autoprefixer", "version": "1.1.20140510", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.3.1", "stylus": "0.44.0", "browserify": "4.1.1", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "d15fa7f205f9656b69035fee78b96f884a148fba", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140510.tgz", "integrity": "sha512-csKrjZjJV4SZs/5CpMztlUZ3RcUsZgAf7pHxfit3vrmxuAt0cIzz57G11lnD/Ng8GyFiLXrZBGn8B4oUMM459Q==", "signatures": [{"sig": "MEUCIQD0D1gNFB6AZo4ehtVJMZj+hYeP0B/NBgf3tg2Z6z2VMgIgRR66ozNj5xfugE2g6+PJbRqyPEmCdnym+3LR8Mkybwk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140512": {"name": "autoprefixer", "version": "1.1.20140512", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.18.2", "should": "3.3.1", "stylus": "0.45.0", "browserify": "4.1.2", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "73891071ab498f7af0a8ada6be5ff8ca47d41844", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140512.tgz", "integrity": "sha512-r5Sae07cUZy4H14dYZWCAy15EKamPe+jVe+YTvvi/UEQ8yDcztbDPh+x7Edm5124H+WS3S+wP9SfB3QgRIE9YA==", "signatures": [{"sig": "MEUCIHjIwraEgG22uZ+42b00gl5b6CVNOg1kdW1qNZF3emiEAiEAq+HFYZR42i0uFcpRerHTKOYEjtXeZEhkclnqieDqlnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140521": {"name": "autoprefixer", "version": "1.1.20140521", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.8.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.19.0", "should": "3.3.1", "stylus": "0.45.1", "browserify": "4.1.5", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "5453219cb14ffccc92d3362bd7ea7ff0475fede4", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140521.tgz", "integrity": "sha512-qLBR9Cj/0Qmlvhk17R2oxzZAhmCCiyUbpMKNBdWY4/0zaHejFkx4aSd2u8QrJ3f0KeE2Yoj+8R4Xj5Vqf+bqEQ==", "signatures": [{"sig": "MEUCIC46yXkM1K3iGiixmP7oxNKWJIDeykvom7I13/6Ux4J3AiEAi7ZeBK+AoMi5xrvSHuEl2b6ZBKMWpTVlRckKuegT5H4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140523": {"name": "autoprefixer", "version": "1.1.20140523", "dependencies": {"postcss": "~0.3.4", "fs-extra": "~0.9.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.19.0", "should": "3.3.1", "stylus": "0.45.1", "browserify": "4.1.5", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "8b90eae7404234f51e28fc97a3526f7355a10937", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140523.tgz", "integrity": "sha512-sYvZBqUt7lfDjCRkypVqERBvZD4WaFBOrYWbrOh8bbOjl0SKsBiV8aR4xRBU1SXen1O8+c8S9vx+qmay18Q5lA==", "signatures": [{"sig": "MEYCIQDVAN2VZtLX5bNx6utqDBWTfOmzGGlpt/XUg01oDtc37gIhAIE2eqti5XUUsHUPP5rvVOycBnjVZezfLxqRz/WriVZV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.20140605": {"name": "autoprefixer", "version": "1.1.20140605", "dependencies": {"postcss": "~0.3.5", "fs-extra": "~0.9.1"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.20.1", "should": "4.0.1", "stylus": "0.46.2", "browserify": "4.1.8", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "2534cd5d041709ba221187713b81b3862b67db7f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.1.20140605.tgz", "integrity": "sha512-jhkFOzdINLOhusBL6ipUcKM9gvHAbsgq98JML68FCqrL7MCvn6t5YsCBLjmiBbFOfJgBZ91gWkQX42mW96Pdug==", "signatures": [{"sig": "MEYCIQCwtDcgc8uNxceNZDxMT4mngc+AAh/9OjBiPAOEClgvXAIhAI1uL0kwnfUjkiNuMo4nw1hxxoDwGP7epVwqkSWrtn91", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "autoprefixer", "version": "1.2.0", "dependencies": {"postcss": "~0.3.5", "fs-extra": "~0.9.1", "caniuse-db": "~1.0"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.46.3", "browserify": "4.1.9", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "ed9c5cb0e48364429fa1a90a9c39d95b156c82f4", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.2.0.tgz", "integrity": "sha512-PfP+UmmSsnB7wmdLnUnPRGE3lQ/wqWrrhmdgaXLsNxO55uqCcCOhlQ+O0GrpwveELFBAay+4OpeKqwkKlxrtpw==", "signatures": [{"sig": "MEUCIAk06fNz8vFRzgwCxH64c1leHm7/Brr+6SV+aINF2IL0AiEA8SLzdhrMhFsQnG0IwIFoIJZMgGLvXOKLtLGv6t3BTMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "autoprefixer", "version": "1.3.0", "dependencies": {"postcss": "~0.3.5", "fs-extra": "~0.9.1", "caniuse-db": "1.0.20140618 - 2"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.46.3", "browserify": "4.1.11", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "dcd97e0d4e3ade99a0da5ccd76f78bbe48d5ff41", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.3.0.tgz", "integrity": "sha512-3qB7f9ubsztNO/1Ct6NJHxcQno3p4E6HoEYCCy4+wbHO5dR88sasjC02DhmMXdWqNI0cLZRR2U6ZrxAozuY88w==", "signatures": [{"sig": "MEUCIQC4fnVMsjN2q4G3QU5gynuvAsEZJFvoeQKzEF6G36/6WAIge16MJWSLwPeF5OQgPRqLdrXS8AZo18B/UPBI2WuzL7U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.1": {"name": "autoprefixer", "version": "1.3.1", "dependencies": {"postcss": "~0.3.5", "fs-extra": "~0.9.1", "caniuse-db": "1.0.20140618 - 2"}, "devDependencies": {"nib": "1.0.2", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.46.3", "browserify": "4.1.11", "coffee-script": "1.7.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "bd731b558daabbf4556a74f75766f9e78102fefb", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-1.3.1.tgz", "integrity": "sha512-TUMhFBOjSFVRuxLlU3YNr6ZyagzPPpXbveCk2qSxEoE8GCK1D2k8PpNTF8zFc7S3QosTIZcC1PVm3YOJFcV8WQ==", "signatures": [{"sig": "MEUCIEq7mU+cm97oFtXcZX9gwcDoO1QXhZmlo60nvKj1nT3VAiEAviQf/WKMquqztl2pyPxQ/eNeqe1/Hn0BR4x5VoZBh9E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "autoprefixer", "version": "2.0.0", "dependencies": {"postcss": "~1.0.0", "fs-extra": "~0.9.1", "caniuse-db": "^1.0.20140622"}, "devDependencies": {"nib": "1.0.3", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.46.3", "browserify": "4.1.11"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "f074cb735ceccc4e79f73f1e4be5f9deadb004d5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-2.0.0.tgz", "integrity": "sha512-qHwd3K/eOb5nJDZSUnTTusIl2G2Q82MpLMYv2POcxYqPGhgFAimYTPtGQxQFkR6ZuQ7/dHdT27CX7FeptbJBCg==", "signatures": [{"sig": "MEUCIQCG/LkHvLUPSMCHbYxyoZeLis9k81+zeWpKW4FfIZdUJAIge8OeFWkoVkoYisLbehJWyk4zdlnroSiwV0CRkqrR4oQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.1": {"name": "autoprefixer", "version": "2.0.1", "dependencies": {"postcss": "~1.0.0", "fs-extra": "~0.9.1", "caniuse-db": "^1.0.20140626"}, "devDependencies": {"nib": "1.0.3", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.46.3", "browserify": "4.2.0"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "eaf043ede53fd20bfca4a37be06e9ae98edf76b2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-2.0.1.tgz", "integrity": "sha512-SKcec6fmRmc8WXUIZFJmjSfI68lNCgHmm+YrxEPkGfMNXLDTR8lurGOpfiyMfqoVlHP7QpUUrCLlD7vW9b4PtQ==", "signatures": [{"sig": "MEUCIDxeQ+/Lr2zMbGumc8JlQoHzuP1KseAHzu+MwH8KetvMAiEA4rVkoGAy9xc7jVwO4i8XieQ0b3jThm7qYsLiPGaNIaM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.2": {"name": "autoprefixer", "version": "2.0.2", "dependencies": {"postcss": "~1.0.0", "fs-extra": "~0.10.0", "caniuse-db": "^1.0.20140628"}, "devDependencies": {"nib": "1.0.3", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.46.3", "browserify": "4.2.0"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "e84c05387c11b45bb24b785bacde9b48b84b6f3e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-2.0.2.tgz", "integrity": "sha512-fmusJrMtopwuC50GStE6d5rzyxkQ4xzGmIl7qGR7c1zxuhw3kg5rOHtfd3uaWr2YGD7S0XARiHL/4QrJmHq7tQ==", "signatures": [{"sig": "MEQCIDNotVQ4INbHuYRIM09Kk1nSzW9kVPm0L/Gs72wjpV6mAiBXFbyy/k4VEK4dLHpH0sPcY4GOzCVPbCvzXufSH2B8mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.0": {"name": "autoprefixer", "version": "2.1.0", "dependencies": {"postcss": "~1.0.0", "fs-extra": "~0.10.0", "caniuse-db": "^1.0.20140628"}, "devDependencies": {"nib": "1.0.3", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.46.3", "browserify": "4.2.0"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "93ec96f3609989bf745b09a0e75df09128ca206b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-2.1.0.tgz", "integrity": "sha512-ixiaXT0WchkKWN+IEeut7x/+xrRuTdJIo3BJdktEhE1wVP6e0FrqbDQr+UqlrfUwTSbkoWtJF0RzEPLPvtvQig==", "signatures": [{"sig": "MEYCIQDpdVZusZNsrVgHluc67PyT8RNsMUz5po73Q9QhrIod3QIhAJtNM09e+pFSLAq3O6eXAMGOybn7+WMyoZo0xX5AWJZn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.1.1": {"name": "autoprefixer", "version": "2.1.1", "dependencies": {"postcss": "~1.0.0", "fs-extra": "~0.10.0", "caniuse-db": "^1.0.20140710"}, "devDependencies": {"nib": "1.0.3", "mocha": "1.20.1", "should": "4.0.4", "stylus": "0.47.1", "browserify": "4.2.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "6fa859627961e7bfcba9321d4daaf8141b53be8e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-2.1.1.tgz", "integrity": "sha512-SQJEG6k19BTg6ToOteapic2kaJFW6ObPKq5JTWdA/fbJWMFgS6ZjnEqMkNRbe00Er6Gc7fSmYtU16qqwsH2l7Q==", "signatures": [{"sig": "MEYCIQDNheVpkdxmyYMIikTIoFJX6bOoy/Q7YS39uuYAaOwr6gIhAKX3VYsa/vYQm5gcC4jVPpXA3ab3xW1PCh+QqbZgvQE8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.2.0": {"name": "autoprefixer", "version": "2.2.0", "dependencies": {"postcss": "~2.1.0", "fs-extra": "~0.10.0", "caniuse-db": "^1.0.20140727"}, "devDependencies": {"nib": "1.0.3", "mocha": "1.21.3", "should": "4.0.4", "stylus": "0.47.3", "browserify": "5.9.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c7c7936a280a64f8fa0e5f6108ba22f92c639633", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-2.2.0.tgz", "integrity": "sha512-yNj1+HVcvTxIaFhP6reZc7uv0UluLUO+bdn/JKABaTqSzTKakFMVDDaY3IT67OvsTjBnw571SE5w5mNYsqgbeQ==", "signatures": [{"sig": "MEUCIHsMvSsIbKaSpCZ0607Jo8SKl7fe+OhM38x1yRRg4jqgAiEAsc8HnVcU+qq3RdS+E4mLYRzcMMEpx3U1L16h68sFxm0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "autoprefixer", "version": "3.0.0", "dependencies": {"postcss": "~2.2.1", "fs-extra": "~0.11.0", "autoprefixer-core": "~3.0.0"}, "devDependencies": {"nib": "1.0.3", "gulp": "3.8.7", "mocha": "1.21.4", "should": "4.0.4", "stylus": "0.48.1", "request": "2.40.0", "fs-extra": "0.11.0", "gulp-util": "3.0.0", "gulp-mocha": "1.0.0", "gulp-jshint": "1.8.4", "gulp-replace": "0.4.0", "es6-transpiler": "~0.7.14-2", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "bin": {"autoprefixer": "autoprefixer"}, "dist": {"shasum": "805a1bba79ba83082c575260180dce15bc1fcb99", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-3.0.0.tgz", "integrity": "sha512-nxjVRGerD74BUMANTu/IkflqZk9dBnn+Dpa6SdKZcPUlYalnd8IEG+I7dHT0LJD+nCFvIiWhDWk+QXA+McP7GQ==", "signatures": [{"sig": "MEQCIGGQOEUfESCHrxOXEdeuwkcdNORbD1zGioRFcKR7NbS8AiAEkbwMoOezLtd3w4yNu5wXMdjZyTJKg7VFqP/T+nTQZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.1.0": {"name": "autoprefixer", "version": "3.1.0", "dependencies": {"postcss": "~2.2.4", "fs-extra": "~0.11.1", "autoprefixer-core": "~3.1.0"}, "devDependencies": {"nib": "1.0.3", "gulp": "3.8.8", "mocha": "1.21.4", "should": "4.0.4", "stylus": "0.48.1", "request": "2.42.0", "fs-extra": "0.11.0", "gulp-util": "3.0.1", "gulp-mocha": "1.1.0", "gulp-jshint": "1.8.4", "gulp-replace": "0.4.0", "es6-transpiler": "~0.7.17", "jshint-stylish": "0.4.0", "gulp-json-editor": "2.0.2", "gulp-es6-transpiler": "1.0.0"}, "bin": {"autoprefixer": "autoprefixer"}, "dist": {"shasum": "b50e6cc88a543ff81018dfab0467ea7570a7a172", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-3.1.0.tgz", "integrity": "sha512-WiGslcCwgVz2ywhyYQDkereK5/kGbCB2HJmfCStVnlyaBEAD8HNo7CdCzwwb8/2MOQH1MScXHfiMgO9LvFafnA==", "signatures": [{"sig": "MEUCIQCUo1PhvWfC0Caa5n2f3KEIEahhtxCAF1JctHPAeaTH5QIgRoc0nV6phVl/YZ27VlSThjt8Ffuixji5DGWIN6LtShA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "4.0.0": {"name": "autoprefixer", "version": "4.0.0", "dependencies": {"postcss": "~3.0.1", "fs-extra": "~0.11.1", "autoprefixer-core": "~4.0.0"}, "devDependencies": {"nib": "1.0.4", "6to5": "1.12.10", "chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.0.1", "stylus": "0.49.3", "request": "2.48.0", "fs-extra": "0.12.0", "gulp-6to5": "1.0.1", "gulp-util": "3.0.1", "gulp-bench": "1.1.0", "gulp-mocha": "1.1.1", "gulp-jshint": "1.9.0", "gulp-replace": "0.5.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0"}, "bin": {"autoprefixer": "autoprefixer"}, "dist": {"shasum": "bf58b968008aca51b0945b48df0c6f4ca2cba046", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-4.0.0.tgz", "integrity": "sha512-p02oUuDYYmptj9HFoazilsVpQtdW4JvYlgIXivWiFOwZfwENWj6KJ5XsO1tEJWJVyalC38ru806To3yVJLSTzA==", "signatures": [{"sig": "MEUCICGHKCiQKKSoa6K06KXk7h1NCYTJgaQN7vJHVdiFjOeVAiEA03MOc/TgObZ7Y8LI8Xj5dWhizvs+RfnxWltyJRPESfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.0.0": {"name": "autoprefixer", "version": "5.0.0", "dependencies": {"postcss": "~4.0.1", "fs-extra": "~0.14.0", "autoprefixer-core": "~5.0.0"}, "devDependencies": {"nib": "1.0.4", "6to5": "2.12.2", "chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "stylus": "0.49.3", "request": "2.51.0", "gulp-6to5": "2.0.0", "gulp-util": "3.0.2", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "gulp-replace": "0.5.0", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0"}, "bin": {"autoprefixer": "autoprefixer"}, "dist": {"shasum": "d7ae05093b187ad2214503a8112fad598f0c3ce1", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-5.0.0.tgz", "integrity": "sha512-xjMPs+buOPk/xBsLZHVXRcSUVvSjoc76EdgWV1CkKYbBWg9KFRQ2j/ny0Mg7nRdl/41sTyddLuAKSptK3NlOJw==", "signatures": [{"sig": "MEUCIQCaluiE3EiR450V9HqqnPwdlPLTKvSg1TmUfobGrcxGCgIgQo0hWC5wo6bfQlLA7oCpIC5hW8m071kEK/q2bPPwuHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.0": {"name": "autoprefixer", "version": "5.1.0", "dependencies": {"postcss": "~4.0.2", "fs-extra": "~0.16.3", "autoprefixer-core": "~5.1.0"}, "devDependencies": {"nib": "1.1.0", "6to5": "3.0.16", "chai": "1.10.0", "gulp": "3.8.10", "mocha": "2.1.0", "stylus": "0.49.3", "request": "2.51.0", "gulp-6to5": "3.0.0", "gulp-util": "3.0.2", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.0", "gulp-jshint": "1.9.0", "gulp-replace": "0.5.2", "jshint-stylish": "1.0.0", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0"}, "bin": {"autoprefixer": "autoprefixer"}, "dist": {"shasum": "2db51c2461b294c35b7745e197791735cf54af41", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-5.1.0.tgz", "integrity": "sha512-sstlPEyEfeZu9T4kX080iO2A2peUL9Rrh8nwAXUULO59r/Rqg3kBqK/kGdZ9qDSrCyeU4BNTPTM+dttLa7HTVw==", "signatures": [{"sig": "MEUCICX3mDN07mEMBBIN1nktFG+oPlpxIsaIcyH105Y0ZHbpAiEA/96MNeYDLowKORTIXvJGRyNwHpF7GSuL9SzqfdT8Tec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.1.1": {"name": "autoprefixer", "version": "5.1.1", "dependencies": {"postcss": "~4.0.6", "fs-extra": "~0.18.0", "autoprefixer-core": "~5.1.9"}, "devDependencies": {"nib": "1.1.0", "chai": "2.2.0", "gulp": "3.8.11", "mocha": "2.2.4", "stylus": "0.50.0", "request": "2.55.0", "gulp-util": "3.0.4", "babel-core": "5.1.4", "gulp-babel": "5.1.0", "gulp-bench": "1.1.0", "gulp-mocha": "2.0.1", "gulp-eslint": "0.9.0", "gulp-replace": "0.5.3", "gulp-json-editor": "2.2.1", "gulp-bench-summary": "0.1.0"}, "bin": {"autoprefixer": "autoprefixer"}, "dist": {"shasum": "1c4b4e51be1afa9cd7c05d52a6edf606fcfab4f2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-5.1.1.tgz", "integrity": "sha512-wTI8FEXMTkOkwSdRSOfW9KV1K0wH3/K6Cd5vUQxRKdLMhXyaGmRpyrqTBLjStT6B14ZhOBnEGC1NcNgd+4mPkA==", "signatures": [{"sig": "MEUCICwt2JzEzRu38Pl0JMSIXr8jnSmG+UVKe6LqZL7EJ4FgAiEAntry2k9uZBZB+7VKF1SsoBJ5WBz+tIqe53nNnRyKwcY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "5.2.0": {"name": "autoprefixer", "version": "5.2.0", "dependencies": {"postcss": "~4.1.11", "fs-extra": "~0.18.4", "autoprefixer-core": "~5.2.0"}, "devDependencies": {"chai": "2.3.0", "gulp": "3.8.11", "mocha": "2.2.5", "babel-core": "5.4.7", "gulp-babel": "5.1.0", "gulp-mocha": "2.1.0", "gulp-eslint": "0.12.0", "gulp-replace": "0.5.3", "gulp-json-editor": "2.2.1"}, "bin": {"autoprefixer": "autoprefixer"}, "dist": {"shasum": "94ad5c1940c0ac2f93f365918ebcc36f8a065491", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-5.2.0.tgz", "integrity": "sha512-SSm4bPAjdQzWuShw81m3/Bfvf6FBH3Vtu7lEOBvDfpRaQeOXxmPsEXaUtC/7eqgGTYS1iryAQzhrBva0AZp9Gg==", "signatures": [{"sig": "MEUCIHWY9oRYO+jOOZyPi6yI+hnUFYz172Y3UoO41p8J74s1AiEA/y/Taoguuidi7NIwQsDHC1qQDXMlLhoC8vCOid+ZM1s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.0": {"name": "autoprefixer", "version": "6.0.0", "dependencies": {"postcss": "^5.0.4", "caniuse-db": "^1.0.30000291", "browserslist": "~1.0.0", "num2fraction": "^1.1.0"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.0", "should": "7.1.0", "fs-extra": "0.24.0", "browserify": "11.0.1", "gulp-mocha": "2.1.3", "gulp-coffee": "2.3.1", "gulp-eslint": "1.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "b0e40d8a344bf652a7be950589c6a4cac6d74a1f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.0.0.tgz", "integrity": "sha512-H/DWz9UwR1WlB2TcwFXp7V59nQwAYSCl0w1D5Mbo9/ETv1cX2jEes7u3AmS0kh2NPvHocZJWdYHhqDjx8HpXAw==", "signatures": [{"sig": "MEUCIQCOhDRTn/CuTKgAcgdmrs0/YcG0//4qE98V9YvIbWkA/AIgfGG4fwf4MQaC8zM1coygZce2sscBSJho7lEUGdDOnVo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.1": {"name": "autoprefixer", "version": "6.0.1", "dependencies": {"postcss": "^5.0.4", "caniuse-db": "^1.0.30000296", "browserslist": "~1.0.0", "num2fraction": "^1.1.0"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.0", "should": "7.1.0", "fs-extra": "0.24.0", "browserify": "11.0.1", "gulp-mocha": "2.1.3", "gulp-coffee": "2.3.1", "gulp-eslint": "1.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "01e2c40aa8ffdb1837e0b3519260e731e87e4e0a", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.0.1.tgz", "integrity": "sha512-r7f7y808M7rmLsYFZX1YOHbGXyKrQegdqYLkb+6ZUM6mluioIFI6VL4IGtEd0/94S3bBM4nIv+cctsO5wbRRsw==", "signatures": [{"sig": "MEUCICdesjGQ/U5V9H/tPQVqUkmF/GCT+7dvNzDOsSCCIuYQAiEAgOVkD7ZEF1eVhNnEr5f4gML1uSY3mzYnDOAcC93fU0s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.2": {"name": "autoprefixer", "version": "6.0.2", "dependencies": {"postcss": "^5.0.4", "caniuse-db": "^1.0.30000296", "browserslist": "~1.0.0", "num2fraction": "^1.1.0"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.1", "should": "7.1.0", "fs-extra": "0.24.0", "browserify": "11.0.1", "gulp-mocha": "2.1.3", "gulp-coffee": "2.3.1", "gulp-eslint": "1.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "ab61d0e58c539599e5b6fe55ab41f7c5d5701015", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.0.2.tgz", "integrity": "sha512-SRzbj77D2xdyAgwbrufOZCSahbdyeWZENVvusvWKm4YTvNOTJiRZyLR81YaB69eivzMcV9FYipnRGWY9rXCUxw==", "signatures": [{"sig": "MEQCIE0s+eltAcCgOd1+rP3yrZEotYmHtJa+RPBiUL6aq4KKAiBV+wtn0pzmYSh9tXhx6KpYehh4A1aiJMEoCz2KmQM73w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.0.3": {"name": "autoprefixer", "version": "6.0.3", "dependencies": {"postcss": "^5.0.5", "caniuse-db": "^1.0.30000313", "browserslist": "~1.0.0", "num2fraction": "^1.2.2"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.2", "should": "7.1.0", "fs-extra": "0.24.0", "browserify": "11.1.0", "gulp-mocha": "2.1.3", "gulp-coffee": "2.3.1", "gulp-eslint": "1.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "06badb15cadb2d1dd92d5b95f8f4522911f9c79f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.0.3.tgz", "integrity": "sha512-S1IpR22ZEWuDhjxd4gLPqVGStcsGJzCB8kFVSzuR+CoJ1/o5OKSUnpz93rtz//UHB8BsCuGoBWeJTTVJ7NOVNA==", "signatures": [{"sig": "MEQCIB+VQrw7gbT3gHHGDio/apsis7idL97Lm9Ep7x16EibGAiBwWGJdlZLuT5Qn3+tK9xpazFf/o5LHWj3ic8fCn2ZYBw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.0": {"name": "autoprefixer", "version": "6.1.0", "dependencies": {"postcss": "^5.0.10", "caniuse-db": "^1.0.30000359", "browserslist": "~1.0.1", "num2fraction": "^1.2.2", "postcss-value-parser": "^3.1.1"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.3", "should": "7.1.1", "fs-extra": "0.26.2", "browserify": "12.0.1", "gulp-mocha": "2.1.3", "gulp-coffee": "2.3.1", "gulp-eslint": "1.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "7578acab4f52fadfaaa6004fd2a096c58480c21c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.1.0.tgz", "integrity": "sha512-Wv09TG5RaZB3cANM6ubDitcNRNaeUQfclbG7py2ifvkp/bJzgzAeCOCh9zUUBKb+qL+ztmoAxNGVGg7RPbnVKw==", "signatures": [{"sig": "MEQCIC3xWd4ZfO3OOZeRnVdEtI3/0Ik36X+VolA6gNMzD/GUAiB45aYIvcJg4x1a5MEdMZFyQzqKkBCOInz1EYm/g7VjPA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.1": {"name": "autoprefixer", "version": "6.1.1", "dependencies": {"postcss": "^5.0.12", "caniuse-db": "^1.0.30000367", "browserslist": "~1.0.1", "num2fraction": "^1.2.2", "postcss-value-parser": "^3.1.2"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "7.1.1", "fs-extra": "0.26.2", "browserify": "12.0.1", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "318b62195a89496300a7361a95212d4ec7f19678", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.1.1.tgz", "integrity": "sha512-7ms18c0rTOq7ANZ15yKVUAEGPol9QD2t4LpaNjXnu3nJYMhYDa79g9VOWUxa69CGif+hnJjRexzn6M4tA+aNsA==", "signatures": [{"sig": "MEUCIQCn2WEIfiJ1M5pckZ11iHQ2bewDDzOsnHw2QMjzA4+0OwIgUfHorH5fK7+kckPKzU8llPKiY21WIZ60+bokpwADLQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.1.2": {"name": "autoprefixer", "version": "6.1.2", "dependencies": {"postcss": "^5.0.12", "caniuse-db": "^1.0.30000372", "browserslist": "~1.0.1", "num2fraction": "^1.2.2", "postcss-value-parser": "^3.1.2"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "7.1.1", "fs-extra": "0.26.2", "browserify": "12.0.1", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "11a36b50cc0e4adaab5570aed28cf81876e1ab3d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.1.2.tgz", "integrity": "sha512-0DbXdRBs+NORhWmDynshylIYhj9X/PaT5soavwja6lptqKN/pGpO/ghOFt+B5Ak9SticHJ3LuwD3pWVx4VvPyg==", "signatures": [{"sig": "MEUCIH9ar+rApf0unbXYyJEhAi2ut9DrJ86G5Kk+cgKlux/oAiEArg7SS3IXOw8/frJdru6vA3RlJIlA6KstXj3Z5+/T2TQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.2.0": {"name": "autoprefixer", "version": "6.2.0", "dependencies": {"postcss": "^5.0.13", "caniuse-db": "^1.0.30000377", "browserslist": "~1.0.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "8.0.2", "fs-extra": "0.26.3", "browserify": "12.0.1", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "d136f855db4bb6901687ac1c0f4559a18442b524", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.2.0.tgz", "integrity": "sha512-RMs+vk0SL/JieXPjj6mJh4FOfq5+XFPYvoOyjuxW4TLaPvt8ZwtlyjTRXj1ZscggnZ6wjk/8csfd4RrTII6MrQ==", "signatures": [{"sig": "MEQCIGr8YuZZPbCfM6FHbbYxf4oNxVT2Lc4HRoRRZebwzIDSAiAar3/XXhMAE+qnnMSRACv9pUya7Wv8Sx4ktFsWHEqkcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.2.1": {"name": "autoprefixer", "version": "6.2.1", "dependencies": {"postcss": "^5.0.13", "caniuse-db": "^1.0.30000379", "browserslist": "~1.0.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "8.0.2", "fs-extra": "0.26.3", "browserify": "12.0.1", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "dab8b22b200b190ff78f37dba721af0eb541f4d7", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.2.1.tgz", "integrity": "sha512-tfhvJ0jkLN32Mq25Yq+6szLkjy+VDVKIDItGXcOVmVO/MvlZHKIfdQZPdLIzWRU7Tl51a4hvedzMFO2rix2sEg==", "signatures": [{"sig": "MEUCIQDk8q4vbrrzcqX00FRxgURASME3+GkreAq83BmGHK/4ggIgcJFkFQsErDzMdkqeaLilbeRZ7l4xubGM1uUIJvlRwQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.2.2": {"name": "autoprefixer", "version": "6.2.2", "dependencies": {"postcss": "^5.0.13", "caniuse-db": "^1.0.30000382", "browserslist": "~1.0.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "8.0.2", "fs-extra": "0.26.3", "browserify": "12.0.1", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "2250ec2167f4c9374d8739e477d82ab2d70fe476", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.2.2.tgz", "integrity": "sha512-QkucjOXlVNgKgkfSwyVkP1Idn6q27gERF3CtklzMDoOGOybLHn34xC/lykdv6/bqZiZ/c2inWSQmWpvvq3yBPw==", "signatures": [{"sig": "MEUCIFbs1xmUnaWTOff2KSHBvMl/0fSd5L57a9IlBsVa5pHDAiEAnwrEyehNJ6DlFadarnzRgsUnsOj8r+idDSJneXN/zEo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.2.3": {"name": "autoprefixer", "version": "6.2.3", "dependencies": {"postcss": "^5.0.13", "caniuse-db": "^1.0.30000382", "browserslist": "~1.0.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "8.0.2", "fs-extra": "0.26.3", "browserify": "12.0.1", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "a9ecc9fe1a7b52b0b32d110257375d02fa841f1c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.2.3.tgz", "integrity": "sha512-7XC5JUvVrJkn1KObAXEQb0BEFxJvzzcrirB5CRQKPQZLAvjftS/oHtfYVOwfV+Vyp/A6g3TXdhUZQIa1fsBkUw==", "signatures": [{"sig": "MEQCID/waR2lz4313Nlz07q3G9lREI8HMvarRfNrZ3M7wZi+AiANOr8Hc+SFowe2kYoJoFsSpdpTV7Tf2aK7RRvFLxFqqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.0": {"name": "autoprefixer", "version": "6.3.0", "dependencies": {"postcss": "^5.0.14", "caniuse-db": "^1.0.30000387", "browserslist": "~1.1.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "8.1.1", "fs-extra": "0.26.4", "browserify": "13.0.0", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "729baee3e8962a99b3c7b54001fb3d6ec1a6b427", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.0.tgz", "integrity": "sha512-1NwcnH63iyh5Gjw/1u/cROXGXWXjrraceAUxBGvdasZg/dIqeBfJ+6SjQODhpjZKXWa+hCBYEDhxmx3HjffvPA==", "signatures": [{"sig": "MEUCIQDmT5AvRThTfPcLop+e3lVQjFts1gglF3bkwBypGctQpQIgVh3aH3nlm0qwwY6tSTu6OtxiulFI1NZ01yvQzL4a128=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.1": {"name": "autoprefixer", "version": "6.3.1", "dependencies": {"postcss": "^5.0.14", "caniuse-db": "^1.0.30000387", "browserslist": "~1.1.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.0", "mocha": "2.3.4", "should": "8.1.1", "fs-extra": "0.26.4", "browserify": "13.0.0", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "79757c0caef79f00b93bffdcf1e0da7eeb06cc88", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.1.tgz", "integrity": "sha512-D3AmS8z74yXWvmqrXNUrj+KKXvjm8UFXIEkhykZsLyzT9d6+1KF4AVrOALW+CN+z5zLc1SHWdUdJVCY7rRPpWg==", "signatures": [{"sig": "MEUCIQDPpOS4OPt54XCBPoLoEfaquYa8aedGTIis42LAzuYCAwIgP80bhrlLGXJZ20L/CiH0ERQWPcLRdbdT+1D67P74PWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.2": {"name": "autoprefixer", "version": "6.3.2", "dependencies": {"postcss": "^5.0.14", "caniuse-db": "^1.0.30000409", "browserslist": "~1.1.2", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "2.4.5", "should": "8.2.2", "fs-extra": "0.26.5", "browserify": "13.0.0", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "1.1.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0"}, "dist": {"shasum": "9e37caa8b7ae824a3c041efe6cca3fb5d0d2cadf", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.2.tgz", "integrity": "sha512-47RyPhiQkr6DLAWxTt1B5xLxSBKR0ThMkzyW0ACeSkslvtuhoN3HuuZSE1b83xI/ArCYVDdwRa0Vr/o1OAoQ0w==", "signatures": [{"sig": "MEUCIB9IDqLw4uVzzQ7OuZxVuWVYKLdPrVTI54OaO9jGryVqAiEAhoOtkBIgDd0vdZ0va1HuDymPJ2FQzBiNtGy2CkVqwuY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.3": {"name": "autoprefixer", "version": "6.3.3", "dependencies": {"postcss": "^5.0.15", "caniuse-db": "^1.0.30000409", "browserslist": "~1.1.3", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "2.4.5", "should": "8.2.2", "fs-extra": "0.26.5", "browserify": "13.0.0", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "2.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.0"}, "dist": {"shasum": "38bcd301504e7b40a9feeb392709067787c521a5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.3.tgz", "integrity": "sha512-bFctPTGa2UW43AGPmKumiNRlnqs+rC368j1Qmwd3oBj41SxuaJXcuDGET+BnT2kgD/0gi/mjYG7cL1hy+QV7aA==", "signatures": [{"sig": "MEYCIQDWTstHeixsqcjNKeFUH4FE8oOCUM4/Gh+yLPLseDo/LwIhAJUgezvrmpvvYbX3hCaCuWgVgJyzlrXEDo6hOFQeXniX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.4": {"name": "autoprefixer", "version": "6.3.4", "dependencies": {"postcss": "^5.0.19", "caniuse-db": "^1.0.30000435", "browserslist": "~1.3.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "2.4.5", "should": "8.2.2", "fs-extra": "0.26.7", "browserify": "13.0.0", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "2.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "9d28d7355f01a794ef59d0f4b1dd91ad3055766b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.4.tgz", "integrity": "sha512-CDng8C7oPV/j6DRWz4pU2FewNTQZgwTmNUmVMrubDoSR5kbnXDuWnYeRVTGwjT5aKrfOeSZGQAdwg8wWX0iB4w==", "signatures": [{"sig": "MEUCID6CfNAXBzSZ8EdDLSjsa+fdhDO4eJq0eMY1zoW+D7Z4AiEApWOVkFmTa/3Np3jNxrTIGlMRsKYQMhwGCCteNPMmE34=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.5": {"name": "autoprefixer", "version": "6.3.5", "dependencies": {"postcss": "^5.0.19", "caniuse-db": "^1.0.30000436", "browserslist": "~1.3.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "2.4.5", "should": "8.3.0", "fs-extra": "0.26.7", "browserify": "13.0.0", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.1", "gulp-eslint": "2.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "6bcbd038a337c70627e7fce4438fa58188f0937b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.5.tgz", "integrity": "sha512-qK89CpD6q3wnikO196+PwzADyu+BKMB6UtAiOafizhMr5HaIwvFzgMIh3AZEZQEoQb1dTgqFDDEzW60/DRqKsg==", "signatures": [{"sig": "MEYCIQCYy0Ld9X73rkaGFN0QYavJXzRnhXSOdPTPUxlnnjqwWwIhAO0tNKBQuUG9JsKL7tJi9sKGw3aZAYA2uvYmNnmIwln3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.6": {"name": "autoprefixer", "version": "6.3.6", "dependencies": {"postcss": "^5.0.19", "caniuse-db": "^1.0.30000444", "browserslist": "~1.3.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "2.4.5", "should": "8.3.0", "fs-extra": "0.26.7", "browserify": "13.0.0", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.2", "gulp-eslint": "2.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "de772e1fcda08dce0e992cecf79252d5f008e367", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.6.tgz", "integrity": "sha512-MchiI2D0bzHHbRPcyQgCWsqqK12rox4axvplNzcMFFYhSAWj8bvuUw94Kad0rPPEKF62SpUD80PpoA4k9BXlow==", "signatures": [{"sig": "MEUCIQCoIgSwCRFQywKHlTT4GOsH1myLGhyw7NgKR/xVOsPFXwIgKDAU3nobgbCEwoW6KvzbCtiFkkUwPnxkksnigAf7QOA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.3.7": {"name": "autoprefixer", "version": "6.3.7", "dependencies": {"postcss": "^5.0.21", "caniuse-db": "^1.0.30000488", "browserslist": "~1.3.4", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "2.5.3", "should": "9.0.2", "fs-extra": "0.30.0", "browserify": "13.0.1", "gulp-mocha": "2.2.0", "gulp-coffee": "2.3.2", "gulp-eslint": "2.0.0", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "8edf3166dd9fd6116533662c8bb36a03c0efc874", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.3.7.tgz", "integrity": "sha512-xnArQBxKETltXW1R/ZrmlaslmU5vF4huqAw0iARn1VXXc8TztdtWQJ9myUe/ywZbG7tvErKQ7hZORBf7G8fArQ==", "signatures": [{"sig": "MEUCIA5BdNx0hTUGq5SRdpR22tEhiDs5802/GiB7uhtEqPAFAiEAuCeZaX9tWlJspKkw8yt0X6FkjJkDpgRPdx7s4HgLjaU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.4.0": {"name": "autoprefixer", "version": "6.4.0", "dependencies": {"postcss": "^5.1.1", "caniuse-db": "^1.0.30000515", "browserslist": "~1.3.5", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "3.0.0", "should": "10.0.0", "fs-extra": "0.30.0", "browserify": "13.1.0", "gulp-mocha": "3.0.0", "gulp-coffee": "2.3.2", "gulp-eslint": "3.0.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "4db60af585a303616bb896b50b30bbdd5858d2e3", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.4.0.tgz", "integrity": "sha512-uTNTXJowzi/81E/NQ0GhGVQ+7KZtnpOQnIwVjjrkEPVWExzBjiJ3EWCWH+fFapY39J3bsLySQio8tPTU+PBKkQ==", "signatures": [{"sig": "MEUCIAJtII3wDhUqvnKz5CqRwv2SUXDRAKuFL+IvFRiVsqmGAiEAlQzuZVUY8qkJu3mUjkQweY8DVZNHPfaVmdpc1TCU7qQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.4.1": {"name": "autoprefixer", "version": "6.4.1", "dependencies": {"postcss": "^5.1.2", "caniuse-db": "^1.0.30000527", "browserslist": "~1.3.6", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "3.0.2", "should": "11.1.0", "fs-extra": "0.30.0", "browserify": "13.1.0", "gulp-mocha": "3.0.1", "gulp-coffee": "2.3.2", "gulp-eslint": "3.0.1", "gulp-replace": "0.5.4", "coffee-script": "1.10.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "c1ba8461759faf5fb7737dbdbe5ea46a78c68a6f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.4.1.tgz", "integrity": "sha512-n9Su9p0kPiYE8IujWRXNXQZ2RiE4EDnzkDWd+TwEFSAoauE3NkcyXcPBp//efM/C663QhNzi5kQW1rTcFWNX1g==", "signatures": [{"sig": "MEUCIBrIiVydv0QT9yH0BWp5mnKqSBII2gmX8C2GCrVGeZsKAiEAsCQAUFfQsNY049c8ISmTThFrxzjhf+fPEDO7+Q+NizU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.5.0": {"name": "autoprefixer", "version": "6.5.0", "dependencies": {"postcss": "^5.2.2", "caniuse-db": "^1.0.30000540", "browserslist": "~1.4.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "3.1.0", "should": "11.1.0", "fs-extra": "0.30.0", "browserify": "13.1.0", "gulp-mocha": "3.0.1", "gulp-coffee": "2.3.2", "gulp-eslint": "3.0.1", "gulp-replace": "0.5.4", "coffee-script": "1.11.0", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "910de0aa0f22af4c7d50367cbc9d4d412945162f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.5.0.tgz", "integrity": "sha512-SsEJpxWjW9RShkKCTQvHoz6hQInr56YWsZcWU7m0fiMknXVLf2lnxC1G+qW7+glBC3AYmXh02xvejNdPS9RPqQ==", "signatures": [{"sig": "MEUCIQCyvZjmR1MtYdwYz74nGM2OWbnRnlh+iEwf6ZV3vz7+MgIgZ7ri22SqUgzOpeZhoNtWwJFVn2PTY/hwOt3lu2kSdyg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.5.1": {"name": "autoprefixer", "version": "6.5.1", "dependencies": {"postcss": "^5.2.4", "caniuse-db": "^1.0.30000554", "browserslist": "~1.4.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "3.1.2", "should": "11.1.1", "fs-extra": "0.30.0", "browserify": "13.1.0", "gulp-mocha": "3.0.1", "gulp-coffee": "2.3.2", "gulp-eslint": "3.0.1", "gulp-replace": "0.5.4", "coffee-script": "1.11.1", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "ae759a5221e709f3da17c2d656230e67c43cbb75", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.5.1.tgz", "integrity": "sha512-STbn1sXvjG/PiS08X9Ue0iPTPXjp9hlLQq6qAK6twHidRgyMgKyXxi1Z2ZUdK1OD0gznnHx6dhioyFU1EwzV3Q==", "signatures": [{"sig": "MEUCIQCy1V5XiHdul7C/lagUXsYk6ybx0dtfwJU7IphPctFqGwIgTOLUC8W2eLasZ3OT+1GopaHU1RcZjJqeYwyHsDgmqNk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.5.2": {"name": "autoprefixer", "version": "6.5.2", "dependencies": {"postcss": "^5.2.5", "caniuse-db": "^1.0.30000576", "browserslist": "~1.4.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "3.1.2", "should": "11.1.1", "fs-extra": "1.0.0", "browserify": "13.1.1", "gulp-mocha": "3.0.1", "gulp-coffee": "2.3.2", "gulp-eslint": "3.0.1", "gulp-replace": "0.5.4", "coffee-script": "1.11.1", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "37cc910c5e1139ad341a006d5f6d441a380b742b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.5.2.tgz", "integrity": "sha512-2/YP+Y+ST/KjFgQxXiWcJZGAsxWl3LkTtVX/fioKzS9mcGnLmjTJwflQMNjzihyl1aSHSMzwmUqyCgxzkNQUUA==", "signatures": [{"sig": "MEUCIQCuVNb+Vk/0H5Ix+/pgER22GbZit9Wjmb5vNNOajQLCPQIgAOmZppe1Ql39tGpABJGZwFsbq8t5PVpGEQmlaTJsajo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.5.3": {"name": "autoprefixer", "version": "6.5.3", "dependencies": {"postcss": "^5.2.5", "caniuse-db": "^1.0.30000578", "browserslist": "~1.4.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "3.9.1", "mocha": "3.1.2", "should": "11.1.1", "fs-extra": "1.0.0", "browserify": "13.1.1", "gulp-mocha": "3.0.1", "gulp-coffee": "2.3.2", "gulp-eslint": "3.0.1", "gulp-replace": "0.5.4", "coffee-script": "1.11.1", "gulp-json-editor": "2.2.1", "vinyl-source-stream": "1.1.0", "eslint-config-postcss": "2.0.2"}, "dist": {"shasum": "2d853af66d04449fcf50db3066279ab54c3e4b01", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.5.3.tgz", "integrity": "sha512-L/jzO2Vl5mW5JbyvbVcFePY8im/9AA9DM5HaF9Aztlbuj6uajYZJt/R7e2QDJpL6GzpeWDpYkN+AjIC9xwLxlg==", "signatures": [{"sig": "MEUCIGQ2/pVtfAc3jw79Gj/DVENAq4DchxmxyHN0TvrX6sNgAiEA5ZBX4thNB0qtn5DY3/fGzNMm3cXHnW6UtUSHEn+UqZk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.5.4": {"name": "autoprefixer", "version": "6.5.4", "dependencies": {"postcss": "^5.2.6", "caniuse-db": "^1.0.30000597", "browserslist": "~1.4.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.1.2", "fs-extra": "^1.0.0", "browserify": "^13.1.1", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "1.12.1", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "1386eb6708ccff36aefff70adc694ecfd60af1b0", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.5.4.tgz", "integrity": "sha512-4z/6SOSGrVVKUI5nw9yDnboUkpCTx5QxItoyvWe3m134wgPguMFRu/gGmgGZ6QAVA4DHyue9/UUmb4hIJT2viA==", "signatures": [{"sig": "MEUCIAZP9LINZ8u3rCuZjsRgBv4lxI1sG7/Toq19lOiq9E9fAiEA5k1Bk4zw5eleX7x/001FWQLsgv6dRoSMeRHuZxVrY5c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.6.0": {"name": "autoprefixer", "version": "6.6.0", "dependencies": {"postcss": "^5.2.6", "caniuse-db": "^1.0.30000602", "browserslist": "~1.5.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.1.2", "fs-extra": "^1.0.0", "browserify": "^13.1.1", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.2", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "d5b347ebbaf79e79d30b81c0ee3e482b288527bf", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.6.0.tgz", "integrity": "sha512-8kSuMQVDq/7X/AGbSbLxXWfpV4fT42OvJg0YKiIRTtoeGJT3zOhUHGc0xiEH7yEMTvdO/+fZfW0clbiMULF8bw==", "signatures": [{"sig": "MEUCIC6RWg0MBJeNFBtOSw1tViWzDBplXzixwa+Ae6NKTh4eAiEA8ebTtxqVCiKc58N35FCRZpkJFVIE7STdQpciUOe4yEc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.6.1": {"name": "autoprefixer", "version": "6.6.1", "dependencies": {"postcss": "^5.2.8", "caniuse-db": "^1.0.30000604", "browserslist": "~1.5.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.1.2", "fs-extra": "^1.0.0", "browserify": "^13.1.1", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.2", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "11a4077abb4b313253ec2f6e1adb91ad84253519", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.6.1.tgz", "integrity": "sha512-fUGC+qvkab/Biu8/dAw2YF9BgYO2+fO2F6Ydqf3VzLermd/oWxovCccRTX04ljud8tYc71TtEjXjTsqHb6Y8Jg==", "signatures": [{"sig": "MEUCIAD1Z6m0uPikHZPU2wtttC729pktItZxQuo477n6360kAiEAgcK1m6MciMTmoJqxnMQaPH3a2ONbETK/Y73GQDqbqyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.0": {"name": "autoprefixer", "version": "6.7.0", "dependencies": {"postcss": "^5.2.11", "caniuse-db": "^1.0.30000613", "browserslist": "~1.6.0", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.1.2", "fs-extra": "^2.0.0", "browserify": "^13.3.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.2", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "88992cf04df141e7b8293550f2ee716c565d1cae", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.0.tgz", "integrity": "sha512-ghIljHY6FdZv2ky9QC/39AiE0CdamzLwYfMtN/IYyjUpnNIjgc5sAheUjldllhHFrliKFQVpMrqPF7oPTFYS6g==", "signatures": [{"sig": "MEYCIQDz+t+fP/lf+7quGR1Uqis6g070USdQPBt2WSygMttuKwIhAMS2NZI7F+u1OxzB3OEU3BJEQgdTs8UllCc8R2dnCvLa", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.1": {"name": "autoprefixer", "version": "6.7.1", "dependencies": {"postcss": "^5.2.11", "caniuse-db": "^1.0.30000617", "browserslist": "^1.7.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.2.0", "fs-extra": "^2.0.0", "browserify": "^14.0.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.3", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "d14d0842f6ef90741cfb2b1e8152a04e83b39ed2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.1.tgz", "integrity": "sha512-pREvuWqcr4JzzP3UNyChdaf7V1iPNNOvjKJv9TtZrV/2ZerJU9b6G+uXdhxLQStHSYei3mdg67I3UwfAlNITLQ==", "signatures": [{"sig": "MEQCIBWQe4Wp5NkByKRb3lythQIsAo3outUSOPNk1s8/14VnAiBnZ/w6XyooHCGUKwSD7pRvGkJduu6lytu1j9OnChx+IA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.2": {"name": "autoprefixer", "version": "6.7.2", "dependencies": {"postcss": "^5.2.11", "caniuse-db": "^1.0.30000618", "browserslist": "^1.7.1", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.2.0", "fs-extra": "^2.0.0", "browserify": "^14.0.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.3", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "172ab07b998ae9b957530928a59a40be54a45023", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.2.tgz", "integrity": "sha512-BrtFd8nGiHJrfPfGZ4/iv3yFH15Fvo/+nb/ObqjfEaPM/KTUWprhJTBKdp5V3hATndPNV5v6ZLxHiqiQeZBgqQ==", "signatures": [{"sig": "MEYCIQCgF300Vrd+BD6d/uPFxlbJMS1BI9XiqodX2B3Of2Z53gIhANjgL6pS/ti7a/K+FIi+Pd7mqjHLdhB5kxfkLjNtt45U", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.3": {"name": "autoprefixer", "version": "6.7.3", "dependencies": {"postcss": "^5.2.13", "caniuse-db": "^1.0.30000623", "browserslist": "^1.7.2", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.2.0", "fs-extra": "^2.0.0", "browserify": "^14.1.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.3", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "bc2c28018e9a226f24f0ded36ce81014dccec817", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.3.tgz", "integrity": "sha512-4hGgl5c9HHE4FYS9mRKwSAz6FXIDnJDKfWwKWFAAU8VlfTjvHPgx4iqQqlsLHdvhNbJstOyE+o2VU445VT757Q==", "signatures": [{"sig": "MEQCICZdYz1NcXVevY1a9Dgs6171P010LtUnWlDzpntte5R7AiADVdDv7ao+iN80/T80KJfVpSwlnKi2yC8J2L5IBr5ixw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.4": {"name": "autoprefixer", "version": "6.7.4", "dependencies": {"postcss": "^5.2.14", "caniuse-db": "^1.0.30000624", "browserslist": "^1.7.4", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.2.0", "fs-extra": "^2.0.0", "browserify": "^14.1.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.4", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "b4405a263325c04a7c2b1c86fc603ad7bbfe01c6", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.4.tgz", "integrity": "sha512-iGQzFuiC11bFtVNSR4TNabKZFCv39pv/nMZy4+2FUSnephasu5yEh6PypYKk95vHVePipxaXt/hg42whrdkVBA==", "signatures": [{"sig": "MEQCIFp0vuUoS618HzebqD5kF7h8Ef6a31pTVgZBl3QGZR92AiAHPnmlunMh+/sYrd9lKRacjbu4JwLgGY6t0vCyF+Vudg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.5": {"name": "autoprefixer", "version": "6.7.5", "dependencies": {"postcss": "^5.2.15", "caniuse-db": "^1.0.30000624", "browserslist": "^1.7.5", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.2.0", "fs-extra": "^2.0.0", "browserify": "^14.1.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.4", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "50848f39dc08730091d9495023487e7cc21f518d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.5.tgz", "integrity": "sha512-LCShooTZpattm+uitdbaEnhMyVhfYPA3RrPUbYrXaIPoMaqHjiIxGexYTZIXc2sls3K1jQ/sy1jirShwLfzlKw==", "signatures": [{"sig": "MEQCIF/D1w6uLBQVVMaz4UBL0X8eWeLvTkUKUiVfXoAnV7YNAiBs83Nt9jR9nmmVbVddS8GCNN3nixs3Lj/unrQjAZ8vvg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.6": {"name": "autoprefixer", "version": "6.7.6", "dependencies": {"postcss": "^5.2.15", "caniuse-db": "^1.0.30000628", "browserslist": "^1.7.5", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.2.0", "fs-extra": "^2.0.0", "browserify": "^14.1.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.3", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.4", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "00f05656c7ef73de9d2fd9b4668f6ef6905a855a", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.6.tgz", "integrity": "sha512-Tq0youP1YjL3vBbIfA4gnuiIHPsu3m1vhoXW8jWafDq0XtqmnG9WQuwfJcMC8lkKNusuL+zDnftFAm6/2Ze6vg==", "signatures": [{"sig": "MEQCICFBec0qpvKPDX3SRutxlQJymwMDs9d4pyp53tUJKeBoAiB/k97v3Msz6xH4ijf4/aR5ADfq3TjxPTv3shdoHULzvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "6.7.7": {"name": "autoprefixer", "version": "6.7.7", "dependencies": {"postcss": "^5.2.16", "caniuse-db": "^1.0.30000634", "browserslist": "^1.7.6", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "mocha": "^3.2.0", "should": "^11.2.1", "fs-extra": "^2.0.0", "browserify": "^14.1.0", "gulp-mocha": "^3.0.1", "gulp-coffee": "^2.3.4", "gulp-eslint": "^3.0.1", "gulp-replace": "^0.5.4", "coffee-script": "^1.12.4", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2"}, "dist": {"shasum": "1dbd1c835658e35ce3f9984099db00585c782014", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-6.7.7.tgz", "integrity": "sha512-WKExI/eSGgGAkWAO+wMVdFObZV7hQen54UpD1kCCTN3tvlL3W1jL4+lPP/M7MwoP7Q4RHzKtO3JQ4HxYEcd+xQ==", "signatures": [{"sig": "MEUCICB3HMdp+6NA0cTW3aNisU+qx9qnhuOX8k07YUuXLxEqAiEA6v92hJ3FcTx4/yH9g4Fs2jnPsctrNDKOaAsFfLYiTxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.0": {"name": "autoprefixer", "version": "7.0.0", "dependencies": {"postcss": "^6.0.0", "browserslist": "^2.1.2", "caniuse-lite": "^1.0.30000665", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^19.0.2", "eslint": "^3.19.0", "fs-extra": "^3.0.1", "gulp-jest": "^1.0.0", "browserify": "^14.3.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "gulp-coffee": "^2.3.4", "gulp-eslint": "^3.0.1", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "gulp-replace": "^0.5.4", "babel-register": "^6.24.0", "babel-preset-env": "^1.4.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "734cc4f56ec964fcd4b6048e028b60c4226e7fdd", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.0.0.tgz", "integrity": "sha512-z8lG9sKI4avvfTuEyx+PP4qvqF+x8do/KkB2HjOS9T50nJozsCyssR5eaoNiDpSxHU4Ov73Gu9oCRxigeIkrQg==", "signatures": [{"sig": "MEUCIDlds37kAKMR9ZpSi0ljnEAcwW9xZgYZ5dQcIwI5uzCkAiEAmd6Fv+8lfnLpJBxRfRgBnHAT76x7kzqwXHv5JF34lfA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.0.1": {"name": "autoprefixer", "version": "7.0.1", "dependencies": {"postcss": "^6.0.1", "browserslist": "^2.1.2", "caniuse-lite": "^1.0.30000665", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^19.0.2", "eslint": "^3.19.0", "fs-extra": "^3.0.1", "gulp-jest": "^1.0.0", "browserify": "^14.3.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "gulp-coffee": "^2.3.4", "gulp-eslint": "^3.0.1", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "gulp-replace": "^0.5.4", "babel-register": "^6.24.0", "babel-preset-env": "^1.4.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "472d7620a1b286e55ad1d8345a09d76aaed99d92", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.0.1.tgz", "integrity": "sha512-ZRu4DbS/F5o5BWHsZBhANJelpUWcsozOF4LtYM/bNclIZCtHP1rInAdlL8esUseOpZACzksLYvSvJyKJW0n8gg==", "signatures": [{"sig": "MEQCIHkmTJmlJVsqdU42zoRaIT5qiHXGJeXiuxwmfWJVLgZmAiA+nDHVT6ypRvO6/yedWFEB8Jx/uiZ5jWfRH11CtD7Jtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.0": {"name": "autoprefixer", "version": "7.1.0", "dependencies": {"postcss": "^6.0.1", "browserslist": "^2.1.2", "caniuse-lite": "^1.0.30000669", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^20.0.1", "eslint": "^3.19.0", "fs-extra": "^3.0.1", "browserify": "^14.3.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "gulp-coffee": "^2.3.4", "lint-staged": "^3.4.1", "babel-eslint": "^7.2.3", "gulp-replace": "^0.5.4", "babel-register": "^6.24.0", "babel-preset-env": "^1.4.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "ae4913adc221fa6ca5ad3a6f8039f6a5c06b3877", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.1.0.tgz", "integrity": "sha512-MRZ2DGCRaHH95fRraOIz2/TL5ggQrOCYn80YMBknfLrq5FCncam/anGA88Af9MpbCTX7bUZk3athjRD8Uxk7pA==", "signatures": [{"sig": "MEUCIHYNii7mjR7nDvzMaUbJeeAh2C1t7Q8eBVeC4T2LvS9SAiEA4jVCFxN3PnWBNtZ39HiN9ZuPx0ZHK0wFYJeWo1rwZxA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.1": {"name": "autoprefixer", "version": "7.1.1", "dependencies": {"postcss": "^6.0.1", "browserslist": "^2.1.3", "caniuse-lite": "^1.0.30000670", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^20.0.3", "eslint": "^3.19.0", "fs-extra": "^3.0.1", "browserify": "^14.3.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "gulp-coffee": "^2.3.4", "lint-staged": "^3.4.2", "babel-eslint": "^7.2.3", "gulp-replace": "^0.5.4", "babel-register": "^6.24.0", "babel-preset-env": "^1.4.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "97bc854c7d0b979f8d6489de547a0d17fb307f6d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.1.1.tgz", "integrity": "sha512-y3U+M3XLC3oKKkShZXcsjdCY6bTWmqOboqx1iDeaMumpqumPbEDUqb9586imSWcy8nboBTjqRu8bNe4KcoYOXA==", "signatures": [{"sig": "MEQCIHwPIdDOxv6YvWtKR+0ghbFVS3WfiaPpOhnFv37fdm/5AiBpzpVWnQlCw2pj7VLOl8bYjjScavQ+FooGl0e3UomXJA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.2": {"name": "autoprefixer", "version": "7.1.2", "dependencies": {"postcss": "^6.0.6", "browserslist": "^2.1.5", "caniuse-lite": "^1.0.30000697", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^20.0.4", "eslint": "^4.1.1", "babelify": "^7.3.0", "fs-extra": "^3.0.1", "browserify": "^14.4.0", "gulp-babel": "^6.1.2", "pre-commit": "^1.2.2", "size-limit": "^0.5.0", "gulp-coffee": "^2.3.4", "lint-staged": "^4.0.1", "babel-eslint": "^7.2.3", "gulp-replace": "^0.6.1", "babel-register": "^6.24.0", "babel-preset-env": "^1.6.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "fbeaf07d48fd878e0682bf7cbeeade728adb2b18", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.1.2.tgz", "integrity": "sha512-PoN1+x0sy4ocZLq29AKZFrnpxNqJNlpYWHP12twS+MxM+BHl5kgE6MgU5qWdJIEKAE1cW8+iSfOSwThiN5P1WA==", "signatures": [{"sig": "MEQCIEqrVgQVapUh+RfSiAf7KjlMDShA10H1gKyPhrGDrm9PAiAfwhxu7DQ5L/Ha0+DxHY/BDAkQ9FBpZfxabWDlIjm12w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.3": {"name": "autoprefixer", "version": "7.1.3", "dependencies": {"postcss": "^6.0.10", "browserslist": "^2.4.0", "caniuse-lite": "^1.0.30000718", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^20.0.4", "eslint": "^4.5.0", "babelify": "^7.3.0", "fs-extra": "^4.0.1", "browserify": "^14.4.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.10.0", "gulp-coffee": "^2.3.4", "lint-staged": "^4.0.4", "babel-eslint": "^7.2.3", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "0e8d337976d6f13644db9f8813b4c42f3d1ccc34", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.1.3.tgz", "integrity": "sha512-zGy6aRrZjWhR+25n89jYkpLXutg6xdDGKKnVNzkfamnnhK3mYchkKj5GJR14aJiU0x4UqZ1+N728tkFNUL5N2g==", "signatures": [{"sig": "MEQCIHu/VHSE8kLf8F9GZnlpywML8YkLvK/+tnQ+n6LgD6kDAiAtPJmE4PMkA9E4+vmKNm8bNB7BZ8mVk+Io0dcTQ7bgMA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.4": {"name": "autoprefixer", "version": "7.1.4", "dependencies": {"postcss": "^6.0.11", "browserslist": "^2.4.0", "caniuse-lite": "^1.0.30000726", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^21.0.1", "eslint": "^4.6.1", "babelify": "^7.3.0", "fs-extra": "^4.0.1", "browserify": "^14.4.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.11.0", "gulp-coffee": "^2.3.4", "lint-staged": "^4.1.2", "babel-eslint": "^7.2.3", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "960847dbaa4016bc8e8e52ec891cbf8f1257a748", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.1.4.tgz", "integrity": "sha512-MB1XybOJqu1uAwpfSilAa1wSURNc4W310CFKvMj1fNaJBFxr1PGgz72vZaPr9ryKGqs2vYZ6jDyJ0aiGELjsoA==", "signatures": [{"sig": "MEUCIQC3SYHQQUhb/LQbBMK3va/xtiiHovtmFPbB0qxBZ1c9TQIgVjomTu1J0nsyhQsCKd6BmXlWMhm2X3ZGdHFDmDKPnY8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.5": {"name": "autoprefixer", "version": "7.1.5", "dependencies": {"postcss": "^6.0.13", "browserslist": "^2.5.0", "caniuse-lite": "^1.0.30000744", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^21.2.1", "eslint": "^4.8.0", "babelify": "^7.3.0", "fs-extra": "^4.0.2", "browserify": "^14.4.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.11.6", "gulp-coffee": "^2.3.4", "lint-staged": "^4.2.3", "babel-eslint": "^8.0.1", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.0", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "d65d14b83c7cd1dd7bc801daa00557addf5a06b2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.1.5.tgz", "integrity": "sha512-sMN453qIm8Z+tunzYWW+Y490wWkICHhCYm/VohLjjl+N7ARSFuF5au7E6tr7oEbeeXj8mNjpSw2kxjJaO6YCOw==", "signatures": [{"sig": "MEUCIQDzf5+h6kILRuXePwyqQm/CHmwLeGvEYQrSYl7i28+bcQIgYHInpdlckiW9p7aXgZlrUiTJcll5j4WbT6ebXuhZlow=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.1.6": {"name": "autoprefixer", "version": "7.1.6", "dependencies": {"postcss": "^6.0.13", "browserslist": "^2.5.1", "caniuse-lite": "^1.0.30000748", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^21.2.1", "eslint": "^4.9.0", "babelify": "^7.3.0", "fs-extra": "^4.0.2", "browserify": "^14.5.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.11.6", "gulp-coffee": "^2.3.4", "lint-staged": "^4.3.0", "babel-eslint": "^8.0.1", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.1", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "dist": {"shasum": "fb933039f74af74a83e71225ce78d9fd58ba84d7", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.1.6.tgz", "integrity": "sha512-C9yv/UF3X+eJTi/zvfxuyfxmLibYrntpF3qoJYrMeQwgUJOZrZvpJiMG2FMQ3qnhWtF/be4pYONBBw95ZGe3vA==", "signatures": [{"sig": "MEYCIQDwtzNF7wZqjX4h/7qy8DddyEYvcdur3niEJhY7tguFVQIhAOZxRQDw5kuylJ2c+SAV5fYTpUUBWB19H0QGnKJ9EzXv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.2.0": {"name": "autoprefixer", "version": "7.2.0", "dependencies": {"postcss": "^6.0.14", "browserslist": "^2.9.1", "caniuse-lite": "^1.0.30000777", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^21.2.1", "eslint": "^4.12.1", "babelify": "^8.0.0", "fs-extra": "^4.0.2", "eslint-ci": "^0.1.1", "babel-core": "^6.26.0", "browserify": "^14.5.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.13.2", "gulp-coffee": "^2.3.5", "lint-staged": "^6.0.0", "babel-eslint": "^8.0.3", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.1", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "bin": {"autoprefixer-info": "./bin/autoprefixer-info"}, "dist": {"shasum": "6bf29695ad19da447bba2b5737660e1e23eac72d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.0.tgz", "integrity": "sha512-vHzhN1Snqf+vG0FUF2pDVlJn3VNHeQM+r0v3XEdwS6vhGqZX/fcaFM+0uvza0vzMGP1KHwr3r+kFCVf1WN0raQ==", "signatures": [{"sig": "MEUCIBZRmKTeqQi/0F5wMhIBae0tgHvwVr+UmfmVJIdDnyP1AiEAg0jvw4/nQRX0W8yYfj+YFNI5gpB7PmnVRTSxL20T7g4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.2.1": {"name": "autoprefixer", "version": "7.2.1", "dependencies": {"postcss": "^6.0.14", "browserslist": "^2.9.1", "caniuse-lite": "^1.0.30000777", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^21.2.1", "eslint": "^4.12.1", "babelify": "^8.0.0", "fs-extra": "^4.0.2", "eslint-ci": "^0.1.1", "babel-core": "^6.26.0", "browserify": "^14.5.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.13.2", "gulp-coffee": "^2.3.5", "lint-staged": "^6.0.0", "babel-eslint": "^8.0.3", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.1", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "bin": {"autoprefixer-info": "./bin/autoprefixer-info"}, "dist": {"shasum": "906b1447a0e6a9e13b371f7909bc4e36da5a5a79", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.1.tgz", "integrity": "sha512-lTbsa2X03maxG45xCNh30sJaRKDn8JPnanOeQOW3wvD9yPGmIsf041LHqlrZ1lXPF/1M3yTZKXqqYfmxU69xuQ==", "signatures": [{"sig": "MEYCIQCzYFYVv0Dk5nl+hlWwv2DNbv6DukJUAGrtYmlBpFuvcgIhAMtjDgB/++NMdlcV16g/ER1Isa3tHnbNAqt3Gjxg7nWt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.2.2": {"name": "autoprefixer", "version": "7.2.2", "dependencies": {"postcss": "^6.0.14", "browserslist": "^2.10.0", "caniuse-lite": "^1.0.30000780", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^21.2.1", "eslint": "^4.12.1", "babelify": "^8.0.0", "fs-extra": "^4.0.3", "eslint-ci": "^0.1.1", "babel-core": "^6.26.0", "browserify": "^14.5.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.13.2", "gulp-coffee": "^2.3.5", "lint-staged": "^6.0.0", "babel-eslint": "^8.0.3", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.1", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "bin": {"autoprefixer-info": "./bin/autoprefixer-info"}, "dist": {"shasum": "082293b964be00602efacc59aa4aa7df5158bb6e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.2.tgz", "integrity": "sha512-eTVoSHiGp2cDytg7RS7gtqAnfH+WFcNQMTjywGNu+hH7ViQZ/ZKsvNz2C1oVhCtd9DjMIC15iatpxmtp5Kxvpg==", "signatures": [{"sig": "MEUCIQDMdBZY2Usl0SP9k6tXIdwTVOeOUK2MOoHUhOwaKrmEEwIgPj0z2uLBkWnYPWzBqzs99BPx+AgD6GHQmsql12iPL/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.2.3": {"name": "autoprefixer", "version": "7.2.3", "dependencies": {"postcss": "^6.0.14", "browserslist": "^2.10.0", "caniuse-lite": "^1.0.30000783", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "devDependencies": {"gulp": "^3.9.1", "jest": "^21.2.1", "eslint": "^4.13.1", "babelify": "^8.0.0", "fs-extra": "^5.0.0", "eslint-ci": "^0.1.1", "babel-core": "^6.26.0", "browserify": "^14.5.0", "gulp-babel": "^7.0.0", "pre-commit": "^1.2.2", "size-limit": "^0.13.2", "gulp-coffee": "^2.3.5", "lint-staged": "^6.0.0", "babel-eslint": "^8.0.3", "gulp-replace": "^0.6.1", "babel-register": "^6.26.0", "babel-preset-env": "^1.6.1", "gulp-json-editor": "^2.2.1", "vinyl-source-stream": "^1.1.0", "eslint-config-postcss": "^2.0.2", "babel-plugin-transform-class-properties": "^6.24.1"}, "bin": {"autoprefixer-info": "./bin/autoprefixer-info"}, "dist": {"shasum": "c2841e38b7940c2d0a9bbffd72c75f33637854f8", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.3.tgz", "integrity": "sha512-dqzVGiz3v934+s3YZA6nk7tAs9xuTz5wMJbX1M+L4cY/MTNkOUqP61c1GWkEVlUL/PEy1pKRSCFuoRZrXYx9qA==", "signatures": [{"sig": "MEUCIQDqoqjk9UNksGvFpFs0DG9b6EuFwFxMH+4q9k6QPd1boQIgfZuzDfRP0HINFW39kmLH/LhUGn2sNXSjVLM6DD2BGyc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.2.4": {"name": "autoprefixer", "version": "7.2.4", "dependencies": {"postcss": "^6.0.15", "browserslist": "^2.10.2", "caniuse-lite": "^1.0.30000784", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer-info": "./bin/autoprefixer-info"}, "dist": {"shasum": "29b367c03876a29bfd3721260d945e3545666c8d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.4.tgz", "integrity": "sha512-am8jJ7Rbh1sy7FvLvNxxQScWvhv2FwLAS3bIhvrZpx9HbX5PEcc/7v6ecgpWuiu0Dwlj+p/z/1boHd8x60JFwA==", "signatures": [{"sig": "MEYCIQDw00S58btzFagjI7QnM8Xf3SMk1t11T+Z27Hs4lukS8wIhAIcrX8Xm4ZXHm4hP7GfyyHaFDIa4G/Tz+WGOueOBu5mf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.2.5": {"name": "autoprefixer", "version": "7.2.5", "dependencies": {"postcss": "^6.0.16", "browserslist": "^2.11.1", "caniuse-lite": "^1.0.30000791", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer-info": "./bin/autoprefixer-info"}, "dist": {"shasum": "04ccbd0c6a61131b6d13f53d371926092952d192", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.5.tgz", "integrity": "sha512-XqHfo8Ht0VU+T5P+eWEVoXza456KJ4l62BPewu3vpNf3LP9s2+zYXkXBznzYby4XeECXgG3N4i+hGvOhXErZmA==", "signatures": [{"sig": "MEQCIEZRIbQ5Dm8yMZtpU+sVuari/hkjTBhXli3+6tnI+xbqAiAVX7f/zJ62pTrCJZR21vp3WB1peuO4zsmDiJe/YRL23g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "7.2.6": {"name": "autoprefixer", "version": "7.2.6", "dependencies": {"postcss": "^6.0.17", "browserslist": "^2.11.3", "caniuse-lite": "^1.0.30000805", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer-info": "./bin/autoprefixer-info"}, "dist": {"shasum": "256672f86f7c735da849c4f07d008abb056067dc", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-7.2.6.tgz", "fileCount": 72, "integrity": "sha512-Iq8TRIB+/9eQ8rbGhcP7ct5cYb/3qjNYAR2SnzLCEcwF6rvVOax8+9+fccgXk4bEhQGjOZd5TLhsksmAdsbGqQ==", "signatures": [{"sig": "MEQCHzZy79uCCGCKTUAB7KnVIVvBPsIS8sKw0lkX3fJ+Q+0CIQCgKOoajMjDVo/kGP4AtYRObCPrDCd0WaGJ52+0FBEUFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321787}}, "8.0.0": {"name": "autoprefixer", "version": "8.0.0", "dependencies": {"postcss": "^6.0.17", "browserslist": "^3.0.0", "caniuse-lite": "^1.0.30000808", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c19e480f061013127c373df0b01cf46919943f74", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.0.0.tgz", "fileCount": 72, "integrity": "sha512-XBEqAoESCyGu3daYmWcTC37Dwmjvs0y40UtUO3MMX+Pd/w7jwNFfUKNtxoMFu0u0wcotP+arDpU3JVH54UV79Q==", "signatures": [{"sig": "MEUCIB6YxRzOhjsEqXbU/K7vLxpPI2j9Gxmsx8M0QO38YskZAiEA1R7zrtRHe4eH5pAseDOpiROxQ9TQv6jVFWbvVLOn9QA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322197}}, "8.1.0": {"name": "autoprefixer", "version": "8.1.0", "dependencies": {"postcss": "^6.0.19", "browserslist": "^3.1.1", "caniuse-lite": "^1.0.30000810", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "374cf35be1c0e8fce97408d876f95f66f5cb4641", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.1.0.tgz", "fileCount": 73, "integrity": "sha512-b6mjq6VZ0guW6evRkKXL5sSSvIXICAE9dyWReZ3l/riidU7bVaJMe5cQ512SmaLA4Pvgnhi5MFsMs/Mvyh9//Q==", "signatures": [{"sig": "MEYCIQDOhqukaJn3EvuKKjJUwg/cqvuV/hnI6d1w8I2RenbW6AIhAL/twNiRNYcAwfYVzeC2XJKkckNEMdo2zgsQxMGUiMWs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325385}}, "8.2.0": {"name": "autoprefixer", "version": "8.2.0", "dependencies": {"postcss": "^6.0.20", "browserslist": "^3.2.0", "caniuse-lite": "^1.0.30000817", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "1e49b611b31a5259b86b7a6b2b1b8faf091abe2a", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.2.0.tgz", "fileCount": 74, "integrity": "sha512-xBVQpGAcSNNS1PBnEfT+F9VF8ZJeoKZ121I3OVQ0n1F0SqVuj4oLI6yFeEviPV8Z/GjoqBRXcYis0oSS8zjNEg==", "signatures": [{"sig": "MEUCIQCLigFsS+7fRcAKK3BjzSYr/aZvQRYbYIAaM16eeA77gAIgQ+UuLziqmHZVi4Bkscq+Gtx3Mh46LSuIADddY+cO8uM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 327222}}, "8.3.0": {"name": "autoprefixer", "version": "8.3.0", "dependencies": {"postcss": "^6.0.21", "browserslist": "^3.2.4", "caniuse-lite": "^1.0.30000830", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "22ac5151c3c8946bb8f75f337d5c5042c0ec6404", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.3.0.tgz", "fileCount": 74, "integrity": "sha512-HY2K4efAvC97v6j83pgV97Lieal51xhIV8EitvS4SrWcI+IGVZgjpihvXImsmIUzA6kb/tglPKzERG1oRFOvRA==", "signatures": [{"sig": "MEUCIQC31HTWkmzxhU3KQQDzz/rEUVUbVEeNlBXq2Phy5U0tggIgNMknTrQqYtBJDdbV7xY4c4NOy/npsfso2trDa/GlhqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 328588, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1BJXCRA9TVsSAnZWagAAhXgQAJBNxIMdvIJU1hDTxsE/\nC8VJe10UK3i1zT6oEzXE/OA/txSDwZu7j5Tox5pKi5wp25VQc/QbDH8SEJI7\ntxhKIqFsEAb0Bupx2gDuCOcQFN5b5JROxhi3ZS7w3fHo0owS1Hix1c58+5V1\nCAVI8UKLH6azszeVfQuD3Fcqw8XzLPsO8a4mzR2akczKylR5/mGzQU0TZv0k\np53Zk9CxfLoExLB8eorgF7f69IkAzu2AGD44iI4go+wWC1S936baCwPq0Y8G\npZ+k8y+467+vnY65jWv9ijMTs0IwkclawQJv3pLNtba0+c/Wpy3k2HgtryRj\nuCyUntX9ImkD0cuyQw6nYIMGy7AE6v4XGHHKqroniiVPWGUC1ev3IohDbWj7\nHlZoul89+/BYTAHIwZJJih4Fy4LJMf+pmkLgRPnHWYWxh59CGYWpq3lrNUpq\n2+bV5a3fYMnrRFv7zz9VYvT+zsF0rJvPK65EsItJlbG2Bw7DM3lR9CwwMDXA\nJf+3hM5vvEOLGXhS9wXPnB5XsRD5Mo63L1p6B/BdOpu+dr6yn2WpsOf5Uhpv\nSB+q8h98yKBXJim4GC8J6JY7TLfzicuZ5Z5knJSJ0cmWQgNRuc2JhAgOXjrv\nnwy/v+BjJPcR+XFyhcWYlWMlDbtljsqTEwi2ezfxtEMe2fNCMm35EbVEzbuh\n/Klu\r\n=1nEg\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.0": {"name": "autoprefixer", "version": "8.4.0", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.6", "caniuse-lite": "^1.0.30000832", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c44ddc69cc15ad9500c47967068868dcf7aa82df", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.4.0.tgz", "fileCount": 74, "integrity": "sha512-3IxFQtSH+aAlN9fXcovgRqxAoXeI6g9f8VR7yMxMW0b8vFqsSSJHlWL7W0OS+7V1Imsjnf1zD1X1kdAHC23r4A==", "signatures": [{"sig": "MEQCIG+aPF6ny/bt7x/F1ivYcRh6SUOmqI6Cz4xZY9BtsPWEAiAjzC0XqMVk94p4fzp3A3cdobi++glWbgX7CRK/5PMzpQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329448, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5BjpCRA9TVsSAnZWagAAvD0P/RCyzZsk+hz6bQqRNuPy\njQtC4tDDPmZqNkkwiAruYw8Zo5VGQ8ou3fOhhkCSGkCkIMFqXohxc3F6WqvO\nts7KZUaBRUF8nUs00P23tfIDz0ytV57xxNfsnKzgM5E38bE8X7cIAzrbeSI4\nmImxvBuhSquTLuowIvpDqIo408DbhZDjV7iauEmjh8SN6fk0+qO9HGb8hZpp\n6RAnBnL6ydeYsMjlHs/7vgxN0SBoWAC5veVDqRsKwwOqtFznig/T+uJeZg8J\n+1rvWw9qUSX1ecsRrU0t1OsbsUhwBZx6K8mFNwEf3IYVoYUN2FdckPnc/ydD\n5tdih949W4+MnY3Ybt8xd+ksxrggm21mDc0H+UZ1bWPEr4J983ry6BwcoMkl\n4RChzhuRe+6MjsAPgDfiQIv9SEJ2qRtDgax9Dp6ZKdnKtGvx76Sa5expjBXn\nAouKJrh5hLivvHkBfXM5uTDLCZiClK4W8MOCk8NNbTHTiNJz1e0cBQWrlVWn\nnOna6GaWm/9A+RddbLov06cekEvN5vYqaI59OB5UNZ98yzvTas0dgrfJWv24\nJswFJiBphGNYhGAH2SJ50cOcJwICmGGHxR5v11EUWlN4MQRlhY7n/vEttelM\n4KEiukLfNvHgjsOvo+l1u0MbgKBy4Sl8Ml9EArut5WWqAy9T5vD5iThGy458\nBkvi\r\n=JyYA\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.4.1": {"name": "autoprefixer", "version": "8.4.1", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.6", "caniuse-lite": "^1.0.30000832", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c6b30001ea4b3daa6b611e50071f62dd24beb564", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.4.1.tgz", "fileCount": 74, "integrity": "sha512-YqUclCBDXUT9Y7aQ8Xv+ja8yhTZYJoMsOD7WS++gZIJLCpCu+gPcKGDlhk6S3WxhLkTcNVdaMZAWys2nzZCH7g==", "signatures": [{"sig": "MEUCIQD8Iie0WsAxmgq4pdbDzKM+xrovfb4xPhtL6fDyMawt6gIgCHDU+/2PFAeZqNETuC4FslobjfSh2FTzZ6hxwxG6bqc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 329560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5DTVCRA9TVsSAnZWagAABNkP/AudaDtqJahavA5y8rYG\nNoF/cb9LkfkkCQG5S1qzZ0DuIAk/vMbqTRf+rFxTBPjtd6trhH89ZoI31OdK\nYj7SCcksxLz87P/dDWwnzYBGBol0jyAf2Fz18TytX8PIhyOCw3bKMm1OP2Pv\n8Y3Kk+zayLyu3ugfzWWcm+QGZCwhX2ibN7zZ0UzOV9hpXvQXAXFZpl9lAG0z\nxIPjjHT45l56XpBuXu/YML07nbh+50V/4zjRmfytFGdHKO6pQy1tdOxnWQaI\nhxHjs96aysW6Y9rgN/WDL0bdpFwtJv9dh3MM9BzaDDZmXhIeq/agLODTj0cn\nLBAlJ/NncFz2skk4WmGcHXvSn5vtSogWLyKnXboM5fiA41O79hDn1/CQ//3D\nXKfWuGvkDG7OynuJGcZ3SkUgmXTwWc2ReM7F2UTS7IK4IDPnvy/izJlfR3ET\n2mgX5w4P7lGG90yQ6irmKqEeDP2tPp5Mxd7axO1/6aqyUOrDrk59oVZxdtTs\nw6KYxCI4OIB8rCnJ70sv56YYooCSdg1B6C4+O1XPBcGBX5mDIAeIuShGO5EX\nUSKKh/8aWjYOndO0BlwaVHpwjXQ/JqMK1zuqDmto5foyR2W35SRjMRLhZABg\ns5sGs6ZWsYrZcR8PFTTexA6TR/0KrNcO1a6lXlJYfHjI6RrXVPt8tHpRJlqV\nnisS\r\n=DpTq\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.5.0": {"name": "autoprefixer", "version": "8.5.0", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.7", "caniuse-lite": "^1.0.30000839", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "89a39b1316fbe7bc2b4997a0c7dad0149d99511c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.5.0.tgz", "fileCount": 74, "integrity": "sha512-buY1XxFoBrXvLsoFb0jP+niSu1tCj2RwMwHj96+RfQ8DJTgb0vUhh0dg6wjJT3JzsFYBrkSj8/sGtarNdlxTFw==", "signatures": [{"sig": "MEUCIFEfWiNCY+jMrCCYkp4J7wk8Jp3bz6Id8kQY0EhUeUYVAiEA2InCPqU/G5/a++nqNqkEKRM9OGZO/mpVV9e2f/AI0UQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336328, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa9/LqCRA9TVsSAnZWagAAgawP/2NSpQt+K+a+8TeS1SIj\nqJdwSp7ga9TLBDPuqRVVIFxLSpGuj+NRbm6q/+jlLOGQH5ijwQR1Ar8yHuC5\n9TRqVTFRYxtxGGp7ym4Fw5NCD3HfQZrvBrJ+v8997DSGyamfanoB/kuyMUvP\n44F8+3yIPvGqZcRY6Mh2y6FRBYbvUfM+4YJk/anDFFwUHp7exSmPrvjxXXVU\nBxlmUpPRlyTH/1CeQ0FMtYMMOJMGm6OmwrYHieVlYX6nch7TVCTtkUWlqzg8\nfAs4a0L46zLtf8tAt75S3o1SB4ZJ0kmqxkl9OoIceCCVFpFHG67Jgl8U7zre\neDu9Y1odDq4ImDnV1RpJwOMKLJa4b7qNEJehk1MSgXx1eg8gc2iidVefSlIH\nD+LOCua8lEqdomhHioumUnFbBLlal8dlr14xbdWsioAi338bWERUMPulVfWN\nN+5nBWdSMUCpTBeUzzydEYnx5riKny+mcvOW2yREI6JusmO4mC/B87dHLDZD\nLE1CGaQVrj46ZqamnTij+XueZN1ta8Nf//xRp+MgCfohtMT9/kk8XDUQdVys\n0sNp9vsoiwMmBgcMH+gCl6MIoV1AiA7g0Lwn+4dyS/IK5HT2c2PwK+YbX0N7\nvv08u9+K6MkgzUWo9LaCQGRbG4lX34g+0K3veV0KbeOFCj38tiAnwUxBxKZL\nvo25\r\n=+7Rq\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.5.1": {"name": "autoprefixer", "version": "8.5.1", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000846", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "45b0271b0e634af66613d5a4f99d96f3dcd94474", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.5.1.tgz", "fileCount": 74, "integrity": "sha512-0zXQ6OqbnVaplQKkKTASxHFPMNy6WfrXS5QRDJ4zTDxEBB3r7NPDSK4h9KCyQi1tq0tX5MsN4RdzChVBn2k/aw==", "signatures": [{"sig": "MEQCIHKRQobBXdYaaCQMgNt4dRNNhjncamGk7B6Cr+O/KnffAiA6h//Yl76VPRB9c7NmPjKt49feE4R/sRtn9SZKUqsluQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336507, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbB/VHCRA9TVsSAnZWagAAWpgP/iqPxsDimZqZSpMelsyl\n6PGdyh2aei3WArzLf0BKvnQgdvF+Th8RdR6XhkyR8TZOBCl1/RqvRcJ4Xe4G\nut419ZwUIMeWQVsYFx2x50Ghx2Gh2dH4wFygF7fU179YRfVWRIqAavnCVxXO\nUEy4emI+9MlZUX3ez2nefbN1AXf9JAOEf2hyJr+WceYLp64I6iExpwCa2ZrE\n4HtfQnsZuFCqs8BV96S/DfRvQUEKbRReKNfaqiI1JdHtRoGWO5r4ZMaO9ktu\nHCzWmCnq3Kv09bEy4DhQkMU0P66WhoYBHVu+v0iloGR2zkuueXeXBOAE/XYJ\nQuTfU/WRyavke0ylWZKk8SDxAnGWZ/jCjUnrxi5cxKWn/gtXnTgL7M0rzgKz\nEEdZ2I9acoI9BBwjAuaMhVP/KHPQunEtOjr3O8ok45A9qx8x9qpwLungmgaT\nA+F8Y3SEIzuoZheL+gV3lhPHZmlo9HMNd2QhTCbCLB8O/qozAg+Ltg4HUFMG\n5L60SXeCQ94If2bcc+A8hjvfDOXOvE929N/fKQXofTGJZjK5FcP2YpHDY2I+\nc5ctjL2RMAy0o67iZykNHuAR8m3khINnpssFZo4S6UETw/oedDUxJrB187+z\nfFTUWABsS26gsVCMqS33ViUGIG+3TT7mHO+t/NsTXj80ndoqaKmmTQmoI3X3\n1VZC\r\n=xwV7\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.5.2": {"name": "autoprefixer", "version": "8.5.2", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000846", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "52d86a5ea51a6191024d843f88f2748ce3ab39e5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.5.2.tgz", "fileCount": 74, "integrity": "sha512-X3BmI+0YBTGaqw0cR9FCPTyTAAPRpijzIUPkysRumGmJC6r8vhix1RjzLNjX01Kbb/iSezjZ9OR3G92pa31+WQ==", "signatures": [{"sig": "MEUCIQD8klDTKY8SKnHF4vMmn5ESDaXcUQpBzn9moDsKVpvStgIgLKf1md093V/tg3O0O7iXvblZA+Pp2uLaSJnA6PIrJts=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336892, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbDnzfCRA9TVsSAnZWagAAf4MP/iqrPbByqym4y0F5wk4a\n+chS6oVexL60y3S+hNPqKeFUjVLW6bxAjLbnE1P08GK2VXNsrqWCJovvfDzy\nZPabF4bxmyxnMEeHclw7srxGO2jEasjjjudb+3GCKE86JiUja3F+z4QLojc/\nLIG72WH9UsQ7oQtl49Q7owetRBtI+wz51Fv7GksyDwaUq8BCoegMDc+mG+xi\nU8Ofr5xxpndNhxyj7/iwkyMYcDJa1p5YDhchRDoinQrQrsY5RAzjPNTSDWHG\nYAzNbXMfIn4zHK5/nIN/xrACu9SttRYgeCPZ425Ji92u9YXwjqy9BpkoqEcL\noyRmCJ1vJc6/paQxh+k+UMaujUWb0h8QB0RvKeqPyfj+yPTj3lwhYy+k7U65\n0zzkvsSNoVCVOnbX7JuitJ8JaqhKhIZfZbrdAP4ma4/u/SiVW+n0bz+JSlyO\nzJgUtimKmnZjYXwPm6LNfu3RtncsjmekiXdpgTYOOVHOS02LB6bcg3YV8bpb\n3pTa86TEBS3nOMQXVQYvPNshZ84kzfAB5XgdVFCCD/T7dey8A+e2hp89I/pS\nSyxouYs20mndcf/yayIZob5P4FWgfG9/xJ55OwfYOMg1pwEGTfuBtyIDpivF\n9p3sAiiFR3xJGiSJl+DdSmKqVCK6H82SStyLXQ++4L14FSsQRxDLZ/PGl9PA\ninXH\r\n=q6SW\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.6.0": {"name": "autoprefixer", "version": "8.6.0", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000847", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "6da6b0791e15cb0ebf33c3f299414afd340672c0", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.6.0.tgz", "fileCount": 74, "integrity": "sha512-JaYK4gmyklt+es0VDdWVS9R/X96D8eaT0qDLAO6R4niBsoKv6nI4QtfFs8YQskwatIdJ6XZeTBDRpjf4tH+Dlg==", "signatures": [{"sig": "MEUCICTYdazTkGMRnaLcNb3lUAx4LmyYf/nxQHIGnUB/JuJIAiEAlDbnYQDe8P0mSCJiqlkQVfgUBqxRWQuX8m+EMKpCDes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 337880, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbEm/0CRA9TVsSAnZWagAAsm8QAJi4OD5f/cYCQRwWvnl6\nsve7k98ULRHFR3ABDiaIfkVmhuAqIF7z3sijInZwK6OK2fvQH2eZ7Z3Pdab7\nIZVrOyo1XmMNBEQZwOuQAv2lul2rPORsv2LFO1pwsZwNHs5yCOlU0XvX8lfS\na3EFWsmE3ViQv77s01FWrVyjyDXueYErjhlswcJeWXs/om5OPml5eMa3svop\na97x4NKcyjSX9lUgGv60bIEND+jHkvt37ZDUfSj6sWRaEd6aG/swINPRsKW2\nbyuYPR5FG1+O56b5EhSILHnu+I6ROodsnEMi/2ZBxJ4PgW8Hf1Xa3YNlU85r\nX/L7thCy4VNMaloT3+k15tmrVpkbFBTmJJNrhM2jEXT0FFk5yw8nFjpypbCy\nMKyEosyG7h4ffLHXFaVJOhZ6BWwQ8ZRyHY0JIFiC70ElIH6W5vdguDUcE3WK\ny79CtLrwOwr2Fd1O5LeFg6uv2qAnxqMZ0/Uh6uhVgT0rPW81pR8gShxJBef/\nzeejp4XC+mjFKdrymK9YSFiNDfJ6b89aQV7OZ1RmEYoOVBty8nPh+cvH6Sdl\nmk648s7Mu5fo7xsvCOCWPmNEoSVZpGCOpLEdpEXDoQGumzM6KGmOCeSONqGe\nAU3xwAX0WEwteyCiKKhAVPQnXvvZm3S+aOOFRRI1/NBDz8t0SE7aMIBSf/6v\nN1aU\r\n=I6/4\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.6.1": {"name": "autoprefixer", "version": "8.6.1", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000850", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "cb186e6c904cd54a4af7bda078fd27a1fddc7be5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.6.1.tgz", "fileCount": 74, "integrity": "sha512-DqvyCbff+kvfrZgoDHIRK28svWSSFE/Y86FXUd9zflJ+aU7rr+6JCSuhNf1evSPzh+42GdI39BuIjixN5W/EPQ==", "signatures": [{"sig": "MEYCIQDYso6jHfoy8qY5cmxit11KBE4KmRRxjQDVKDK+PvOMkgIhALfc7RJ8EHxkAWMX08Vh2enAhl+HgBl3Re2pLObs+Jhq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 338237, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbGT29CRA9TVsSAnZWagAAcmkP/01FPZWqRshl1De7te61\njwodLmZyu9C1suq/dHfLgUTfGSi6lT/N1diK4DNAquVTiOrt0W98GaUoq87q\nFvJyYx0S135k904zDqUL55V1T8+MArRvD1BN/2o8NtspwqNEdlfsl/ZGLpj+\nrT0i9CJZ7eC74zn7a0eYhmvntkYfCZ/bqu4d4Mvs89Ad4gzG63W+lhJgUVBi\newTMv69IECQtAM04+JUkpZlhqZYbeQWv2vX67brzGzxo4Q5O+NrHtR8cEXLg\nBz4I/w8He2qodVAps7Q568Xm9FTGmdH5EJzForB6ShS2fhw7i6kCDMZfVySm\n2FBi+OXnVThjmghHtRyu/rvgtyo3mz1740pd2Tr6OUBFLsLJeJsPEoiZ+tAC\nZk6InR4dK9+dHSewxyhZ1BXcbrB5hG11tjP8BvNmrvRyqsP8EFT2/clQXCOL\nosH2ecKiMQdlIqiX1ACQIqT7gpkeSpL019C9mS2sTywRD1uo9igrU6Zp3wy1\n5brB2FCzNKE9mr7L2yUn+iEiiCJ32flzodfSedyHOm4MJH2Q+GaG4w62bX/z\nj/h1/wnnIfCKVTdx3OdubyfSUVXk2dLZdzDx0Vh0PH9OjwABsNPPi7sUXFrJ\n2SV8WVhG4jtgWnvOirxEfzl/pf4Fp0l5SdBQIUvbatzhALhjS/gkpSM7ZRja\nK8FN\r\n=tpJb\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.6.2": {"name": "autoprefixer", "version": "8.6.2", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000851", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "51d42ff13243820a582a53ecca20dedaeb7f2efd", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.6.2.tgz", "fileCount": 74, "integrity": "sha512-cv9v1mYYBcAnZq4MHseJ9AIdjQmNahnpCpPO46oTkQJS2GggsBp2azHjNpAuQ95Epvsg+AIsyjYhfI9YwFxGSA==", "signatures": [{"sig": "MEYCIQDdnRIZU83kkdsAZV/ERe0mWe2CZZC56Ox1cRWxYxF+KwIhAMck20ya5NoViPrm8H/JRR7iAzaLiXN0vEOUlzmy/uyy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 338329, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbG7AnCRA9TVsSAnZWagAAu0wP/jhZ6G/CL3+QbbaVY94V\nVmPguCHe6cenDRukUY4d9/kmQHmhvS/nNNP0AmcSROZgfEcoH1TqOTzVKXMG\ndk/73yIJXxjnKE5sNWnNE2Y7ATqHbdV0se8pTJnwzlG3iPjS4BfpkSOkkTtt\nZhBrh6oYnn6vcHgavNiTBL1Y7yl3j30dKZXXghFQG37OMF8Q//a6d9VPGsAB\nm/7Ps+KbkwezDL+SGsXr3d+US8YL8lSyIwsS8lr8wulnCLdQNiG9BXaRcLXk\nAEqruy87LzFfuQa99RgkZW9bOobKjk+hlNMAUNj6r5mSzpv4lYucJ5pZPynJ\n7AwN8CqXNLwoZWzBvKzbWepRvhYwTe6SYQL8Az/dnlOs7p+HgO9wdf+CeAm+\n23b6SnBTI15PDUZXUXe0CFZw6BCBcmNidAtJH6lUlmnDYb9wMMMWPqqpiEOK\nbO/rDCK2pO4kzl5CWwm68y35tI5vmCBRlQOM09CR0txVQ1638iCtEjYWiq3j\nm0tnFYSveTzjlLVcy5zLA1n5Iw0TC64QSlprY9UvkpA8/mkzMmYPoJuX6Pi9\n1M5LRUMjCBFtInq1aPsftQnT/BgZhaubyaS1SrSyvvjoW9UhAdo8T/kxTB5D\nNit2qVxgQKK6NZALQ9TGqWUv3nbx3MpynARzgqUuJUBnVQaoEcwz6Hnv5whb\nJARA\r\n=/B/x\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.6.3": {"name": "autoprefixer", "version": "8.6.3", "dependencies": {"postcss": "^6.0.22", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000856", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "1d38a129e6a4582a565b6570d16f2d7d3de9cbf9", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.6.3.tgz", "fileCount": 74, "integrity": "sha512-KkQyCHBxma7R2eoEkjja/RHUBw+Fc1nY46LdV62fzJI5D7i8mLLCtAZ/AVR3UbXhDZ8mUz4C/PF4lZrbiHa1ZQ==", "signatures": [{"sig": "MEUCIAOKQTjlhckFnn/4XK44dRRq23atLMqnyrNSGBXb8qS6AiEAwxfGWgC8rnkNJucxRFfu+BuW67ULFj6PxVXgkwxpZhg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340511, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbJ7I5CRA9TVsSAnZWagAA0bsP/28vkKxLJfA39M/VUiSy\n1O4NTL3PHvtrGrlA0d/snJG3LgXy3w9Xel/WHndFeXiQJPSFt7nyySJC6QC7\nWJwuVpdobRDs60smLl+HGTPcj4UJxNcV8McLTPR/4xkCyUW2X18PGyxYUX6l\nJyFVY+V2BGzWhFoRHzOUIbbphKEyJeqzCsaQiX7I+H+Y3kXGyqQBJRJFmh1v\nxWNZi7jt1XB0Ml1/IbLy0bEfB/KCHP3eL/1QWgaretWCdUF8KK5CwxKTW3zS\nE7zVG3LkQtqRsgXLW6DBWpjyHnNzRY9yy5lkVXn6rSIRjs2EaZLi5Ilw7mKE\nBiLf6TEFydph28UB1Oj3uUqJCJlPCYx9rkwscjln8G2gMXmxMceSP3F1H/7V\n1jIVSRufKzi5dRnD6SGiZpoSpvAGpK39VP0BQhaRwrUBH7KygdPhkWFC9RMO\nqrnV9Jwf//Bmq1nvUQRifamevnJeuUq6wMBWW1k1hmyoGBiPIio68VRJgZoc\nQdOVsb2A58rr4ZpXTeP6Dig7bUK4oUphDlSuZgjkF8gtxK9LFqlyM3+S3tLS\nnKR4g7cNgzJ7ks3fwOnbmosOmSYnK2TbSlxTHPqwnzM3oC7F018qV2rKPf6u\nejGJT3zO8udYdABxfAveQStWb64pvHqRcHiA/Ku2rfCT2ADVVYuFYXTHZ40X\n2r5V\r\n=oACG\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.6.4": {"name": "autoprefixer", "version": "8.6.4", "dependencies": {"postcss": "^6.0.23", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000859", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "6bf501de426a3b95973f5d237dbcc9181e9904d2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.6.4.tgz", "fileCount": 74, "integrity": "sha512-9D0OoxWCqq9Okp9wD+igaCf6ZaNjYNFSCKxgMLAxAGqXwpapaZ+D0PBv265VHQLgam8a7gld4E6KgJJM6SKfQQ==", "signatures": [{"sig": "MEUCIQCE1mBeCV/T9q9yZnFDPO0GHhFg/m7NS48GezDOP1kDHgIgHITstv3qtzv2vhuocCmFaSSafJfct6Ox/UUFAjfCTgA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 341047, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbMlntCRA9TVsSAnZWagAAoDwP/2eSu8gn+cdiEaQpggIE\nyJNQCbAmf/HggMmVHBp/5TthopIcGfJyJWTV2KoXpfs9aT/8Nqcl2FPpEMIP\nyrIsXqn6r9DW1BvGoP8bsryRvMFfZTq7ii4ytdhvrEIZ42A/UnNCMvnQSYyp\nB2LqrXTl0WLXKRJon1wbHVh+rdfPtH6A1cdYB2ihYAGsuCOeGE6GkWQYwraw\n8KWBNbqxwZF6ENnrzUK9GvuW46B7ZL9nrO9afwcxRe728ALOuZaP/n0a9+Dz\nHp/GiAFH8LQwmm00MToooRx+f6W5rlL9ReBvcz3OB5YIRW6IME0CfdNv2CoJ\nmAXHPQCDUfS7YjexuXu6ZLoZX0AA8D+qovWvu4lzmEtxqzd4gKe1W24UDpa+\nC0aWxmZM1ueKZ4scgs7gtvIv42NpvkgO3q9FVe2J9sSAjhMfuwMz64+55gq/\nNpDq+yf39PdRcasHQklhZS7kjsWfRzmeup2VLPQY3dyRdzQvMlppOyUwkwKY\nkit84Xx6hA94tRhAW6M6TLbwVLFabQFGH/ZUwVt7iDaHGl05m2oleiwzBmF/\n4GtoLshV/krP7XJvXyvUo7emym63oaUUBO9Y63WpBgmpmmS6CFt/Z93AfzH5\n1Q+ys5GcEW0kqr1m5YCgaOvjy+2gC1M7i2SeRlTLZY82H7pvcXWJna3G+DxC\njz9k\r\n=ASjd\r\n-----END PGP SIGNATURE-----\r\n"}}, "8.6.5": {"name": "autoprefixer", "version": "8.6.5", "dependencies": {"postcss": "^6.0.23", "browserslist": "^3.2.8", "caniuse-lite": "^1.0.30000864", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "343f3d193ed568b3208e00117a1b96eb691d4ee9", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-8.6.5.tgz", "fileCount": 74, "integrity": "sha512-PLWJN3Xo/rycNkx+mp8iBDMTm3FeWe4VmYaZDSqL5QQB9sLsQkG5k8n+LNDFnhh9kdq2K+egL/icpctOmDHwig==", "signatures": [{"sig": "MEQCIFBwm9I9AKWxSP/Exp6ESVL/d2gzestK3+gKW0Jy8M2mAiAN35zn0UFYUriEHFKmWIC1oBAkAg9ZGREVoE+GqFuuDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340824, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbPv5ZCRA9TVsSAnZWagAAW2sQAJYVt+AZ1+n/LBvbrdwB\nAqLiQa+tsSNKpV+oAIVYEpKEcWqf876xMZ3hkmnpD/hl1tG/LP3S5TbeHnqu\nN2bzPvqMdaKfvDJL3S6YdoalapI0qWU0L6YmtU0KOjCsBxiGfZnVPOwZg5gj\nikyiAT5LdsYii85kX1DSHC39YpYtHq/779B7z8Mf9BUeTV2JCHU2WTfCLrL3\n9CEQxhKJz4YiVoS7YD7Iid/10gqxddt04bxDAhJSUD97mLKmGcbv6To93Vcw\nRuRKXGC8BHbNStR+zSDf1XRR6icQxCml2nxu8RC+hUK+qIfnKJzNrufuwvmq\nXD03hlm1QRxnOStH/cDbsNBqz8Pf3l9zufGSjFqhXsanm/V7X5SaAR95Cu/I\nRaxFl5nxG129aMFHs3Mf5E2MMfcLG2q353A1sjz+WTlJUlR+EqYBUTNQ6KQR\n8froNrY9V6lT1YVMiv0DnnWrQZUDSm9H7SPcfH41JPOPjwMCKBAIjnCSNyjh\n+/Ipj2M9OOyAgRj0gK8AVQjyYblK93KOooAcUuCUcA79rpNUjG28o7GRvirl\nHCg0uOKG/pDmYvI4xpYZcsJBP50X8nILr9wbaQGUhI+WRWeQSX0VhPPfC3Zl\nAuALlIn3mg4DWDFmVaHjuiccS5botLiU504Wh9ZBA1PoUlyX4c5NATvSoPZq\nwPaq\r\n=EyW4\r\n-----END PGP SIGNATURE-----\r\n"}}, "9.0.0": {"name": "autoprefixer", "version": "9.0.0", "dependencies": {"postcss": "^7.0.0", "browserslist": "^4.0.1", "caniuse-lite": "^1.0.30000865", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "fce072cb3c9c61374bf6fb7d1906f630afdb2889", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.0.0.tgz", "fileCount": 73, "integrity": "sha512-bMt9puCb+xk5ds1ghr1zzfJ09+SjOcseHCEawhMjibM5KfxkodW8PQMhhEnllyj4Cz3Yixy9A+/0De2VC9R+dQ==", "signatures": [{"sig": "MEQCIGo6pMj0h/4q9ZzKzkbjTmctcbF1BgQYGiSasCa5uEShAiBKIxKlVJ4oP9KNGNhpGZGl/pisHoGFLtt2lPCmksTHVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbTRaMCRA9TVsSAnZWagAA3NcQAINZqLip62uQkhg6o4nE\nlplLwsDx6RRIpy2vJ08CuDqmwCr9st9zvMNPKBkJ8tj5r5ow0Fe2FKp/PKCG\n3D1qYPlaF+N5Yc9OBXTHSrDPrJ4rwIlI/P3NlTk/LcO2egSb5g5LVRT8g3Vy\n09YUVLdJk5U/dcECE0afv2rVZhWmnTeSOmLaemd0aAUdNNQ4fOtfAACuhx3A\nD8oNhpwjrsbMr+uzYCrFbch9kCsUe6sDrYrWqikd0Y5yHPf2RdevKj2J53mV\nhXaGy8NwcOJptiTswv8lMhMFGEfJfey8BMk665YDdfPpPHrqF/FqbOSnNnLJ\niaAdBea9ezxoEUUpgop10gTqtA8zxGtxJG79DkH/5e4n6WymiPRG5xsiyao5\nBwURfVb6S+UBwJ2yXNbd/pAiQ3fII6tBNsydiJgmrQCk3JHthC9RTRWABwZb\nWr+AVCp3oR4GAGTMcwVhVX+v072op34VVoPuqs1FLk9wXA3q/VwkJIl7FvIn\np0i9W0M7P0UKrNvlvCR1usp4MoVZG5/qUFn+ZdjWVgyLBfjI0hY1p2eltKrY\nNasJUIv8ueY3/YPT894Tb83YXwi24UaeZZtUDKqCxs9zijWwfGEVJvlvQrP/\nNVlQor4F1Sy8My+slG+tDhEwq1rPoZXo9T2A7pBkf05VnfGog419dmUtXjXJ\nOv05\r\n=6HVG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.0.1": {"name": "autoprefixer", "version": "9.0.1", "dependencies": {"postcss": "^7.0.1", "browserslist": "^4.0.1", "caniuse-lite": "^1.0.30000865", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "b5b74aba3fa60b4f1403729e46a6a1246f16818f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.0.1.tgz", "fileCount": 73, "integrity": "sha512-ytUcgSKu1mZh8pCJq54BkaK7ijigK+nhqVmu8PYOR00letCkrU71qTfKnzhQgn7by/QJvlJGUAofMt+jyXJTxA==", "signatures": [{"sig": "MEUCIDrOVF2QsKHH22enjtoz8MVntUK1DyIlne2hv/TBqk1HAiEA5NdKXzmLzRKKGu0ddgpj0pzE3BsTyiyo5QqsHt6OPHY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 311604, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbU3tJCRA9TVsSAnZWagAAV2cP/0d557UcMhvCeCGx3AJi\n7IqiiwDaOibCe9diN58Lz0ZTKgjZ2TNblWyVWsaacp/zrCqMM/+/JxscoiTT\nnmJtk6uShs9SBh0/jeg//qR9yefLvDLWmBSfZQZ5r3IN9O7Grve+M6MEDPQf\nCCY5st+Q9MxCxzl/bP72vBzTC2xiyPWXRQjBUaGj5uTiKjJGdAvl3C1/fs0j\nD3qVmjrcYTwwUJA9cKKAlPrhabTBhLULfIKC+wvvQlDaGXC7i0cIMK8MTzJ7\n8Fha/PH5Duer4MN6hXBN3pTQrK6RFkwS5PNlh758z7n6MBPI8nzNnAmG/sNm\nBEONJ14laxIguH8YLpxj3U8SNOLrYWD848jWOOASyIkV/YlIR945Tx/1iuVd\nl72bfAnN15Bu/Hp1ZWzKcWQPJ/KNb95GzejsD16ZEk4IEoHe9lmq6qXoF3bp\n84pcnZEQ/FdI6JhmAp4TDOL8xRqB6OksFPZSFJlBt2olp+GdzwQAfG1ugGf+\n2fcgasUocXbmxBwgIyJ/uRyn5CH8Vc/W2CD+rDSkttVus0GqJoR6QtPWAhDm\nS/z59EVal7e2RiEbrSJEYQOPEWyc4ZS53V/WUFB+KJR7gWoPewQ3DWCwuHLx\njWorn4CHbCxR3bX2N3NJW7OUTM/Aj3epTu5IYjuIaiTW4VUBqbcDwYob8cIA\n4VJ7\r\n=jgJO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.0.2": {"name": "autoprefixer", "version": "9.0.2", "dependencies": {"postcss": "^7.0.2", "browserslist": "^4.0.1", "caniuse-lite": "^1.0.30000865", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c41947aa155038b3614414dbc58b4e70908af6e0", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.0.2.tgz", "fileCount": 73, "integrity": "sha512-t5PpCq5nCNzgPEzhty83UHYLmteY9FTL3COBfRjL0y4BTDB0OADbHVzG/S7gzqvITSsAZiaJPduoDEv2n68JNQ==", "signatures": [{"sig": "MEYCIQDkITCVCgceNY3aKt5r7cFtwFxfGBKXYBSSy1tbxZPcuAIhAODw2GCB81AA5gcBX3S0n7cMqoYHWBzcOXqFei8AuJRD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 314823, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbX24cCRA9TVsSAnZWagAAf4AP+gL1C5RWpsoJeMzx1wWS\nMJ9fVQW1eS03iumhTFyWJruNxFqy1q3Sx0IUDJRQEOkP1pXAqk0cUljYIXOO\niFYpTpKsZ0UI1qcqeO8P8d7NzXLIj8sAJFE69esV34aKMnmWcnVhleFW+x9w\noh3A+JJhproRwJmVMLqGltuR44SLc/cMXW8q2GBsG9PjoCOB6aVsm3N8PKpV\ntyNq+CWBKld2ZhHA48+upozEzgzW5b6mH2HS9eeBnIPbe9U5pbtoVwWhw8Pm\n7Klf5SNiEvqkYuvh2UrKGdIcAyHKySfou75l21L7492dOO08F7W/4GwDxplr\nd9pa5LrzrSq++PT+0tuc3J4VCTAkKoqfB8UqEW78C0halA6fe5bDlirZsEzG\nEN90eBEAUYyoVpnHKzwNBbtnNJx2RXoLv0Jk22SS1DWRtcQs24BrUwZfmjF5\noUo1SKU9CXnkF+pM98//Nk8tJSlvPJlGIp8C0k9TJuAniNboAyCBLsXvadBA\nWT7/2qH/EXbBUshJTDPiNYENuduGu/ke2+TQYL/teTlQ14L9lxF2KgJdYI5a\nLiS6O+77iG11xaOFcB1EHxUOY0dWs7x2NMabcyiKDpS5N9CCUJNawj0PMKWx\ndOw0cj1IVdyVnhdHr/pfkxVlrptLVN7bbNV34FXYEbM/mWaadjnyYZXuMkiK\nzUAv\r\n=dEos\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.1.0": {"name": "autoprefixer", "version": "9.1.0", "dependencies": {"postcss": "^7.0.2", "browserslist": "^4.0.1", "caniuse-lite": "^1.0.30000872", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "566a70d1148046b96b31efa08090f1999ffb6d8c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.1.0.tgz", "fileCount": 74, "integrity": "sha512-BbAIdxNdptG/x4DiGGfpkDVYyqu4nUyNdBB0Utr49Gn3+0RERV1MdHik2FSbbWwhMAuk1KrfVJHe7nEMheGdBA==", "signatures": [{"sig": "MEQCIHs9F0SD0v5C06UMwhxw+mk8uiDBrzoxyuqh1hAd3Z0vAiByoaZ9eaSFMjjUIe2M7H+492sRp5K93YtcY3cI0fJwew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 319101, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbY7GvCRA9TVsSAnZWagAAs3EP/0Sbwumpkzq3hkAYZNB0\nSkgg0Jm9vy0c6f3WDbkiQIRp5rufQTvD0iJBAD5tSZxtB172sA20HekMfu8z\nFGK1Qrh7kZIlwEqB9Gjy/gcGRGcHRGT+wZQD9jGgi/GJ1hbJyStPwSfjlnK1\nB76kRDSFRR9yLzZi9YayJNhWu44pJEzveQU6+SOppOerd3Y8XPIcBaB7g1CC\noPas2nxEXfB9GC3cvJz7EabdM985hNmRKAxSuggpnRacZVkY7JznlOqeFnTq\n4O/AhK/UPus4AT7485peKvRChTqN5O9j1DGHIcSThS9DK32dJLHvC9PemGMa\n1T/VEhLkV8wEUG24lq7gZ4eer9mXk2FWmw5E2Fj0GlHYo/8JNEks9ruREEso\nkTi/TaRf88JUWEpRNbMrJK5m2XQCVcWQMn0e27n0iyP44RZfpQs/FRZE5dF8\nNkTVDMp20QfVoI/wMtR2+wQvepEeqFfBt7mgX02DcKVviw/WgUYs7jRQcPEj\nLCDnyE7Br85aYzup0OrJlzztK7LVZiElIMYQmGSi5tQAWAiysxdgLXkldBdE\nYChpMlaWVw2umPVGuR3bkmnf07vqIDV8dixoNK2L6kkc5/KbcXRa9rf8utyH\nScWpK6hX76atvEMQPjmmcTCqtwDid/08F0Mh5T5D8UbPY2SYWBTZTYYJnfMJ\nfuE7\r\n=u965\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.1.1": {"name": "autoprefixer", "version": "9.1.1", "dependencies": {"postcss": "^7.0.2", "browserslist": "^4.0.2", "caniuse-lite": "^1.0.30000876", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "e4ffa96c71270b8a1967d1542abc5f8453920a77", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.1.1.tgz", "fileCount": 74, "integrity": "sha512-Q/2zhVEglXXCBNCOFfnihQcQystPYJt4GrIopeWhPjFxRPXya8eOstB89AafW0nWhSscByp+rSXp9EE5X4zgXQ==", "signatures": [{"sig": "MEQCIBtUpcAPruV6UOCAcwny964QdX9VoBiQUJbq0IfIQqk6AiAS6LRUru3K/KFHnm6uhdKxswlhpP4zxtxvdlAX9OfiHA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324179, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbcPOFCRA9TVsSAnZWagAA3u0P/17NTd5kiAtWJCn8vbEK\n5VVb8wPYP6naqLSftfyRG26uNzrYh3a6P3iEixUuXd7Ua7+O1gKg+Bo+xTv2\npZuZMJnQ0yXJNRGwfWi9HmHW7Uu+RoeaHqPxApwsTyaXVZo3OLZorrOMNTPy\nh+z2wQNsz56hMgsUn62JSg9CO32hJGHRCFKmb9nS8k0pIpEKTQH3Tw9P+jgk\niJDYd+8Rmiurvi8VRJKxzT3B5IbCXbROmGFsbRcTc+0PIO93O5v3QtQgEoSX\nA1GWdns7Jh9yZ9bfkkkSaxPPrPUPIt6jXOLA73u1W3J5h/mco6kbcVOJnWT7\nTCF9JVXJL+Kv+9XwI2S23eVEAEdQAZ6UrS5wUmFwwR4hTegRfBrG4KuDtRZb\n+QipGpRn35dQxjGrbKN6ojc64pxJplbBz3kniusy/PntdXa2MtkdcNDg8rrR\nqVqe4zdtpLpYeX4ONhRRXGFGa/4T+MWe4TWeoLjPbr18PDbmK2+2dZ9sNROI\nYJPL2N2tVeVcSkV+Pyk0ILBHMBiSqbmVLbHR2wvJSK0KsikZVC1DFLE+Rbil\n0KVm6gzVAepNOfmFMJdhStAXl82yO8lAEWWuda/kH/mQD3h1F0K7UZkNGMUT\n4SWHNInncxEYWK/MBwnx7HA+EtU/SUtQnhLuDbhg/yZZubjTWKQ3lplQczIA\nipUt\r\n=nQN8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.1.2": {"name": "autoprefixer", "version": "9.1.2", "dependencies": {"postcss": "^7.0.2", "browserslist": "^4.0.2", "caniuse-lite": "^1.0.30000877", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "73672614e3ee43a433b84c1c2a4b1ca392d2f6a1", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.1.2.tgz", "fileCount": 74, "integrity": "sha512-deo8jkqEIQALuN4OMbVPmtH8BUTRUW2PtINom/ZZBX6MvDUPcFaxTVB2USkWyeLlfd9garISbhjGN5WDHgRH2w==", "signatures": [{"sig": "MEYCIQDnSRv4ayl/cF+e1kYXwGux32UkSq0PupsI7sJ0Z5AbOwIhAMeo6jf9zsOt3gaooVDzI0Ecgg90615mMF3hJO1XJvdt", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbeb2oCRA9TVsSAnZWagAA0DMQAJ2+zBMcPiRLQE6DNMKZ\n/gID1fLyNR34SJCrg/DKvvLlUIcKyqCN+YDRgkVViHWgfffkXkoFiQiZnelE\nQv5MgOJ50YEMpDOMVzvc6gb0Lq8FqtDLY/OjS8PhfRfWKUrqfNmbVA7/apZm\nPWwOKv7ltGIMz+/VsK6v3nzp9+uXlsPxTNxFD3GlFuRcbG0xb3YCrj5yB7MP\n/M4ZP9fj7tHBofb9PhcnaOyFPW0t5Y/fF6/P1kg4Y83SUw2a40bxAYyJcc9R\nnmJDXsdajHmmJ/K+qHzr6kSRmhsyjYh3dN86lmUxnjMygKO475GafGQEXJZc\n1YJuB29cAesfM911fFSlOD+xIGtWuDvtw8qWIY1mJpG1iStCxZkKxAUIdj+x\nxbx6XZrQnQVBGJj5EH8LJ9hPhNiRP9bUHMcr5nyrq6BqJAPMQNV37Ni5xT28\nXX45suxj0PkK9OdaigvgHgYyL7z8Zu6He+fUQ5fXJ/7LnbsiBJk1cnRxbaVi\nM6yZE4K0VphKfT0ObY02NOjYiA9227n5CMM7agVzoKn/PwwQBxYosvRe7vtF\nUBpqWfBTdNN8C3Ieu9MyXnc7qVXRueXhrpEuycAPJj7BPdZJcnPjQHAc7rmL\nfdv93S0UVrQg5CVfh9Y7YmSroLNoK4yz8cEsBZ+bhP4fHXPY/t+A0S+JuWZK\neYbB\r\n=kgui\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.1.3": {"name": "autoprefixer", "version": "9.1.3", "dependencies": {"postcss": "^7.0.2", "browserslist": "^4.0.2", "caniuse-lite": "^1.0.30000878", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "bd5940ccb9d1bfa3508308659915f0a14394c8d5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.1.3.tgz", "fileCount": 74, "integrity": "sha512-No9xrkPCGIHc9I52e+u1MuvkwfTOIXQt3tu+jGSONAJf4awvQmqOTWmk7JhA9Q3BTvBYIRdpS9PLFtrmpZcImg==", "signatures": [{"sig": "MEYCIQDfvR9wy7qh2fkLoyafVbAujvnprlxW8YKhAeBbznk9GgIhAN6jFaYpBMoCD7IVexiWcx0Uq4rDOleCr2yRrDFM6ben", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 324505, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbfRYmCRA9TVsSAnZWagAAYvkQAKUwrFJIREZejXpLH5mZ\n0lBuhCTGBvTMSQOP3NysFkigqQoRTmrWHUtMgb8DoQyCYoo6xsjekZb28RS8\nJxYANe989tLyryKTv81Yd05cXuGk3y9xCyQDjkNh+zixJSsPdFvEeR8Kat5P\n4GN+uT4J+vddrBc6ubknoyMssPQ9ZN0l3LsPL88+730SR7Ch79pXUruzmo5N\nvmXflNGSr3Bdu+nC599a2PFfYtM8//6xCoO6kZdu1zlhjje9eacbuMTdxtr0\nYHDAKhslenIPKqB13jTM7ybIT68Oh/tpRbN4hct06ooBLvMdhezMfuOMDEkD\nRONgoMIi9ECntv3rpMVGsaV4PN3SmwouuicmQaypEHyF6kMv0jihTb5skq2M\n9KkeQC9+8sy9ycMkZEOu3/8tTylFMZh6/XF2Ft6lS4eBPkokbc1KA8QEC/Xs\n0Bahfo0a57o+wh9jkvh+y19O7QaJcETE8cAV/HYtNV8fBOf/k+cZw0xYmuQj\n2Y57+rDc0SKdPmrnF95pOMydItEo/o3sd/Xkkb8nAdvOtSGNYFitfnNV/FO6\n19/mzSbssFmK/pzICeFtFs5Kg+wwLcVYWeItXC1Ao701iZmZXPbMbkbfy58s\naQ8E50Z6in68XT8lJVP3xOOTBua4nLjCTgJyAfusfNpxRlhte3YflvIxhPI0\nmm35\r\n=Cwh5\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.1.4": {"name": "autoprefixer", "version": "9.1.4", "dependencies": {"postcss": "^7.0.2", "browserslist": "^4.1.0", "caniuse-lite": "^1.0.30000884", "num2fraction": "^1.2.2", "@babel/register": "^7.0.0", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "2e850724836e7d6974def410cb184cebc971c9b4", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.1.4.tgz", "fileCount": 74, "integrity": "sha512-EZ5oGTl8A7Tpr/iKxaX/j898Y3QvWEAEQ2xIc9ddgZwI4p63a5HLIxpZmfRSOBC4/AEyBdtToTVKwV1rUj6qQA==", "signatures": [{"sig": "MEQCIHIx54LBt/5i8DTRkect4bhuSk/CTcBkjbzMvMh7ImRTAiBSJBaBsKs6Wa7nNjmt2m6S78U/WJ10tsbXX9MfFGLiAQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287773, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbji5hCRA9TVsSAnZWagAA44cP/i4MdiFWcEnhh6WxX7t9\ndMCOz/xZqDG/+Zn5lHlJyqBlEyobprIo4EecNW4gVixajua3UikhFfpgAbbG\nmyGP+/f92eIxE9kQwCVh2YMVUf+wqmKYFrACkZ3hqGZvzz3BOm3vwDU128CT\n+zeu8JIseaH3uy7/GHEQrBJLpapbu6PVltjK4eN1wiJxUMIXsfDOP2ewpgWC\nq+OB+fHLzyIwLVDt4c+FGzkmoCqEbqQjjxEE3Tykdhc3hHUX18XPJScVJDtq\niaveLEjCyoi0+NbUtJoDl9ShVkzJ3HeyPoD6TJW96wI+5J8NFXF20XF/H9J6\nNgw09JYOPCOZ9Yi07026AYQIehItZW2wtQa0weh+CFJzlICxMfmauLTx2x3C\nh7FubhNbiFfp9QULeE4Jg0YINcXFJ1K3MPxusK337vY65E/ihLAgJxaX725W\nSSeI+INneGN0o7mqB0qEMkTa3c8q7+Vlu+lzDK8qyQuE5/OU/YCdWNgkfVIi\nH5wJbnKVoKhNPmbhlFQ2PsGqHHo++iTZEjoc7T2SgisvnpiyHQCBMpethgj/\n6Muv2hbVQVLbL3/NdvtRkeqEFNQybbpaWgExBRKJ7aiDpMKENwcSfQArLqVK\nUhjbowGCDByagHDQPNlToudnEq7i7+5WDhtHBMAVlSfFJoydw7NK2oX1RbSs\nOF4N\r\n=36fe\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.1.5": {"name": "autoprefixer", "version": "9.1.5", "dependencies": {"postcss": "^7.0.2", "browserslist": "^4.1.0", "caniuse-lite": "^1.0.30000884", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.2.3"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "8675fd8d1c0d43069f3b19a2c316f3524e4f6671", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.1.5.tgz", "fileCount": 74, "integrity": "sha512-kk4Zb6RUc58ld7gdosERHMF3DzIYJc2fp5sX46qEsGXQQy5bXsu8qyLjoxuY1NuQ/cJuCYnx99BfjwnRggrYIw==", "signatures": [{"sig": "MEUCICZZaAuqwlIxBnANONXDQ78jjcoRP73nSB4ZbqT7QyL9AiEAugNHD6HEnQUBjVf6PMjMSmmiKQTPlAdhVADggIreh9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 287796, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbjmV2CRA9TVsSAnZWagAAv30QAIq1DHHuW9nmfN6ANJ8m\n9iSXY+7ZTvpL66GU/RT1yn5/pdBqd7xAuyknFxonNpR1OFOsptxVh/cKjMXs\n8mMErgwb7OgLUn3rcqLi4uy5sAF7TiUYHSa25iamulwP4pSeUSs/9fvmXE9v\negtWxFETOdMTBVyXg6yHhl3UajzzapX62Z/3YwzUHQZusRGzBI2MrngSIWH0\nZ1ANs/IYinit2+TQkVoK13g3E6Ljlqlj7QosQBDPOMm/P8vHafbXlddPIpqi\naG/8ZFdW+3PyeOH4L37R7G6Sp8FOt7b97wUBzdJwa8iHvwCz58qKtAkSDzQE\nyJ5FL3/VHt3XqYwxx4OIfjWLr+Lv+Zwzp/HBZQ4foxV4Ao6FCMEvYcinmx7K\nD4F0v8ePU22YOa8IzXIaEeDV72KUCiMp/9gB6zaoJPVdydXHUTaZTG+gxwuK\nCvtUtuOApxRZk6cz0n53Jvq0oxQan/fBWqgOTjkOASv7Vn4nXw73wRMBb6Qv\n2zGFsxKXNPwXjfPRAi4utIK68gipeiF227Zk19HVJY3w1btMlqCc+wjT+l9I\nzXelsQuMLpVDEo7CLakTKE/gx7kcSvL49xU7ot3STMVROXeFm3vIwBPGhnaM\ndTnl08KtXh+h9HX6orbAqoHdlGmb1oibcKXygV2yl+p9VijU29U1RX24HxsB\nSVaY\r\n=QwS+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.2.0": {"name": "autoprefixer", "version": "9.2.0", "dependencies": {"postcss": "^7.0.5", "browserslist": "^4.2.1", "caniuse-lite": "^1.0.30000890", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "e46f893882b19a160370e7bcd3ec6bbeaace4d10", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.2.0.tgz", "fileCount": 74, "integrity": "sha512-OuxUyTvzRe9EvKyouPqfr8QUkQ0pH400NOFzI1LFINO8zwgJr7ZTybLql03P//LjR0iWile2lCoy2vRTRSFpMw==", "signatures": [{"sig": "MEUCIQDp8fm0nrWc9taHl/pc7N8c32TFgY2xsqRV1YrdDh02+QIgKTKXPyq/8v+PTAy6DWWb6UQEKN6EN4Yte6GBCGLmQX4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301432, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbw3vCCRA9TVsSAnZWagAAwqkP/34HpwrpHx3ANKQTFRWO\nO/W3ESTZvH0ptZ5mqtprp1cSxzvIM/2PDb9dsNta3vO061LXFzae858MJTXK\noFu7Vv0Vk/iZ9btzsorLLAu1Hb3qoLOOv7TB1EYFk/2kApTP2XFhi24fpStX\nr5TYsuyqlL1/6qx7KJc73uwd7SHiJQ0Umen1q4Udpove1nXCnAqwJDufB5X3\narwLAMCnSESxiUbo4stoyiixquUmBMZXn4f3uER0HPnn25Ylak4BOW0laHCg\nuEDA0TSHjxG3wjDtXeGaFs+tHlR/aOiaAjMhbtlTLR9ArfQcrHCbA3LtrVq4\nlog1axtf+31aLxN13C5U1eNGD/hAtdss+KVEdGfmij138xzaavl8xEKVITOD\nmIp23LV41rSyiz3W8phTZ5zhU87FuIfqz+VhlYADe07lPiGcsfnncFiO8bFe\nHUjT9zOHuVtiaWc4k7eKNGNCa3jXcMlPvsMioOe9kem6c8z5H0sHoyvPH6K6\ne8tkaMLfzgyXxzsSMJB7PI0LQGkPH5tmyXMX6VbYEP/C2LhmppXFV9ME7rJw\nr8kV2z8avqcpfekk+RAboDX6gUcQngUUkD+PtA15cPnHrFh3Q+n0T/azK/kj\nL6StsBnoke4nNjVnfWyYV0WzoG7P0iR30VMBSC2IYOyNEZKm824QiiKPJvvc\nHB4h\r\n=GB6T\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.2.1": {"name": "autoprefixer", "version": "9.2.1", "dependencies": {"postcss": "^7.0.5", "browserslist": "^4.2.1", "caniuse-lite": "^1.0.30000892", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "1f2f1179ceed4711b7ab064dbd5c3f9e83d9dc62", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.2.1.tgz", "fileCount": 74, "integrity": "sha512-qlK4GnZk8OXLK+8kBn9ttfzu2PkhRe8kVYoWcc9HsrZEMWiBkQuRYdXyJg9cIIKxfMzhh6UbvlJ1CsstMIzxwA==", "signatures": [{"sig": "MEUCICnTvtIrswGIjL3gjMNHesjTWiCclnCkyanPZ5ySFVtxAiEAvDuyUccPH3+iaGtsgy4WoXr/CpUJgahg/6tb2cokct0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 301475, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbxnkXCRA9TVsSAnZWagAAJK4P/1dJ8cnKpQmWAdfVupaa\nfKUq2pB8JJA475Q/E0y6NUUFNB5TfzKcS4e+5oZj4xfczGojoEzssVZ2YLna\nQgnwFdzsZhZJNO8B83SP7Qn/7gvKSofOg0dMa0G5slnj5zRpR3K5a/R3HiGm\nWdrI3rvpPh/FGwk5UAPXeF3BWRNTAcs2Q82tewNenfwZOCKDIa/lbUIXzJp/\nUxSuDLAeV9cMdCVXI3libXojUhq0gBFQR7rAS5Zwa3eRRmvvO1VCSYRMKcMb\nyXPg3g8JW0znM3RpZE3bhJFqZSYAwHe4tAqSw2j9D3zdPxigaT84DgC10a6J\nr5LQ6kxdOd7Irll7Ms7wRpViePi492TACsYnfNZj0WJO+MhYfxSoZCm6GVXk\n6rqVv/J1bp2HjAB1DEYHV4Lo5HpI7kHqcsBJTyAGOThIDDlpqXuK43JEtoe4\nJVUmIduWMbHGP4Yv+JQY5cQaTPVLBYtuhsTfYJftuBs6ZGJhWDuWimMBjsVj\nGhEWEkH1WJcW9q5BqybXkGTOdCc76jmsWgZgGuUt0hyi05RAhZdswI/xYASo\nfACVq+GYD8CI2kHSDttVZ29pnu9vDRAfd+pGDq0dusJ2jQOpLGp51t11sscC\nEAJfuh/2LuJJkIDIjjRwHxVRH57aMPZzByywqXSIUbVHGIivFDzD5A0kUsX6\nzY0S\r\n=lp97\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.3.0": {"name": "autoprefixer", "version": "9.3.0", "dependencies": {"postcss": "^7.0.5", "browserslist": "^4.3.2", "caniuse-lite": "^1.0.30000898", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "9189fe646f8b804125f0424329472894278b0068", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.3.0.tgz", "fileCount": 75, "integrity": "sha512-rpp+REfk0Ii3lCoiXhU4+CGYn8FbYckmvj6JJbJGSdzaxYCGJ7EvpHncDqgfAn/P6XhWig4u9BBNnsFAfAd5wg==", "signatures": [{"sig": "MEQCIBihHM5c/0YKQi6SQooQ2Jrozf4a9mGCe/DyVWPgnVawAiBc73T0L3RRKtqEfN16hr0lRYkC4sp+mnuqorTxZkiPRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 303718, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbzv6iCRA9TVsSAnZWagAAyckP/18szSG0xSOZg3WpAlC5\nmLkcUmFa/N4MtJWN+j02c6oW3NgHd+0zLk9tEPzjj0ylW9F0Iq1uxVegVCXD\nNWhjedCcLwFwEI8sizWJY0WHw09lw4RX3qtM7E64xie3jl1ygzxk0okQJIJk\nAcYpr2F3XxqHQuJUhp+BWsHWpT+khCOuydeUO8xXGN4YI5zmWm2NNkFArPFb\nn2e3mj0C5A+EmurOfOwOG9E49AjglJfH9qinN8gvFdUpnC77Pd43moJUpHCu\nxS3GBJnWHxZiShLilkk4AH7CH1rYyVLMRG/AgpX+D5/wdkZe9NEQQRitnu1P\noFb5MjWg1bZoH827gM8ItU4/LPBF/Jh3EGZ6krcluvWr888LeI7pUHGhU+nj\n692MHMdbVhpv0edsm2KdXLTJCf42KXsrGjf2OAUx+LJM2RFTJ9DxrHiE8bJA\ntk6EPEQEn/y8zpY4RMx4t49HzuMnliieLxTaF2jP8/SjU35GXNyTlD9214+w\ndW+cPYdp/bJzrOk2PlgdR3EpDTx2/5gCE3fgGEV60ccjmXJPbLJtU7iZtXsX\nWgcMmvzJFGWZvUX7r+3HdxRbVlev3lFW5mKCgDoBm6EIEq7ZPQllr3lrxEl1\nQeEoPrVMCGMrtBNZVCy7O8YcS1UXFTn4kBYnVjPbrhVaZcoWPEV5qQEmzV+i\n5yJX\r\n=J5wk\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.3.1": {"name": "autoprefixer", "version": "9.3.1", "dependencies": {"postcss": "^7.0.5", "browserslist": "^4.3.3", "caniuse-lite": "^1.0.30000898", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "71b622174de2b783d5fd99f9ad617b7a3c78443e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.3.1.tgz", "fileCount": 75, "integrity": "sha512-DY9gOh8z3tnCbJ13JIWaeQsoYncTGdsrgCceBaQSIL4nvdrLxgbRSBPevg2XbX7u4QCSfLheSJEEIUUSlkbx6Q==", "signatures": [{"sig": "MEQCHxUKVX0se3tLBEZQAksmMoV9sI9PCr3fDrwZGWKStPgCIQDQImG07zg6AhrrhGQCIhbDzSjQSItTYxIBv525CZhhfQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 304142, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb0GAwCRA9TVsSAnZWagAALWcP/1IzF76iTQQQamkEIeul\n49Zwy/cDeg5NERwI/i9VY3I7MPB0VVt9ldQzVvsqGpaOGCf+8uRMBWidHrAG\nWRsEsXa46dSgNJETl/+t6yRJvTg7+ePvXHn5SNQ3eyI5UC/UD6QCRCTDaZAE\nwK9AQQsbH7jtWE32iNMM5hLBXJ83x3xrcG7Ov/rvxCHZZDRPa4fsYedDmhk5\nlHmPQqDhAoPuE1qpsdc7vaPQDQutVJeRoUpU0rZ62W7M4OpTarkD0DaA2P0F\nCJdtbMFu7c1vZvpPh9po3LXtnSR0juDc131ko+dWW/I8XJM939yDTQuxHXsf\nDErnUq19hKsB2kea4Mukeb77w7eYslL/PoHVwAu3iTz+W8BNpXaBrFpv4xTR\nvbR+/RivBy6aMTVyl3Y24oqPtORgxy+zcU/ednzIZvJuhimhiRK7MKc7864E\nwh04jU5zQkqX/pPgrwjWMGwEPTfR7lnwFBDal7ujZu0mPRA3JfPN6ozcFDnq\nVSXdkUmB0Ed6lvh+gHD/Ngc7+Dgg4gs+aZPGdb03nGsBtru3aeY1SgG8YCwy\nsziH+gnAXLzle9noG/z5uHGHQ/c7BQTU5veXl2y3hQ4uBKqDuIPB93revlQ7\nrDuW1Gj/fQUC/oWE1VfrHiLYKfbh4XzigpWge6eEOuaaefSZxOk5g+nT6oCa\nPngX\r\n=9QEl\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.0": {"name": "autoprefixer", "version": "9.4.0", "dependencies": {"postcss": "^7.0.6", "browserslist": "^4.3.5", "caniuse-lite": "^1.0.30000912", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "4264ee11fdde83e6a2fb0136e6954af6361bf0b6", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.0.tgz", "fileCount": 75, "integrity": "sha512-gv1a1rzOyxmB2JuyXDLVNWyyUl1uelaKyxO1OP3UNWk7jAP+EhAT9rZdcDBH0C+ZEeTL56qNR4j2TjRZL68bXA==", "signatures": [{"sig": "MEUCIAsAb5yj5fHySMN6dg1Yq+5mrxDJzs0v1lOwfhzcap3TAiEAnLLrnH7EDoFDOH+bfSeGROPgmwgVe6Nj8ef1hG4mLSU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBN1DCRA9TVsSAnZWagAAQacP/1msZobZ8c3uVeIV2xH+\nC6BlsICak8VOssAwxTYhH63IPBMnznT9ys4RJBYjPfxYbAyGkvG+Fru/oF1Y\n56RjzYpYFZDqdZPY8WqJ0tsbFPqjfegsv3dBpEq3/aT9DydWDMpl0wpPjJWR\nXzu0xRiKihzWwi4XCIaFDXadezPsC68XIqFU5dEk6oNZ9w9RQyHpD04XV48F\nCR9q+PeeFUxL67FX4u9UIQIU7yBSYJArvEsoU7fRbF6vuF1fWqZlo/Qe+r2X\nuyJygegZYQN4rojoXhyOUcwXXASXHKjVa+ydl+wN4uFI3URRnnfVwxVh5ll5\nBo9UcVYbWjb1aLlagJZ9NRZKXx3GfOoqqKekOKM75O3jMepP/FhDhHs+xLVK\n8p2dqmgT97QCPu9oyDSKizjPUAao2tq2pNMhvahLhKt3AuXxjkgDQyTVBve2\nuG60L2GxaFqPegb7C+BfxQCwsgO2UU65QSlXF5iB/AlTKjR0lkcEuzfq12C/\nSX4hLPf686RNsAceYuNTirZTBSvk3d/YKEfP5fKR/3AprT2hA3KJcIzls4Ck\nvKqii3605Kpso4REmK4cPAxCOA9okRcQLxM/2quXDtNuKuZayweqW275OSw1\nEogVYGD+fWg8O7ipaPkXh8Wyizri86v1uOJYHtN5lmpmjCBiqFxGfEbpP5tF\nEH0o\r\n=ViHF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.1": {"name": "autoprefixer", "version": "9.4.1", "dependencies": {"postcss": "^7.0.6", "browserslist": "^4.3.5", "caniuse-lite": "^1.0.30000914", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "a3d7e9c5940ba85778ab780536f1cc51bd2dfbd6", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.1.tgz", "fileCount": 75, "integrity": "sha512-T15Bt8akUOGqhkXPLKipYzzmmOOQ+H4dmZtk3VUNBbFiYuUh5uvXb/THxGGxbRlaNG+i39Qm3UdpLtpdiCMw9w==", "signatures": [{"sig": "MEYCIQDyjl2JdDl/h1IGfGIFFm1G8VhneI1dmM13rghWPEVLswIhAPMheP73nNLzhCf0qPY1CDfTmD/nYsIcJyS8APQSNYTd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 321984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBkwxCRA9TVsSAnZWagAATIgP/i7Sh8sUDOGaY2BMFA4R\n9boTqXAUpkpn5AyOxBdCtvOuLO2ZMe9OvTPq1aC38cPjfEsrkgBkdPVuq9/m\nGOtYH/MZLiNXkPGnIw+m/gwTbfxY546mIRo8qusyDKvkNrtRUG1vuOkhxPwj\nfV6xn1sr4s/eeWusrDOvGlyn9xmBVNd0zbO49G1pLiFzYqa577qgOJ+cbQR3\ne3vDvz50+y3DuAX1z1EcuEg8nWTyZauGEDCRdZolYmRTGmb6Y4U9BsTPF1tC\nC5bFyZRFZXUcvNEUAx0XWM+AS4hi5ZOm4tYGafwN92Zv6DsjteMf1HLVSNV1\nPUcFx3EHyonRCyDYJqbDeGq4jb2LU2HUMOsJ1HlZ0BVkO03Ie4r5Nt0PbwxZ\n16TkjJ2QiPHOkxdLRhsYQD9o4TPbvAE/DNeW2nlww/fkQYigr1tMQVrMK1Y8\nFa2ze+2CERiyUTrWJ0T37yUvtgxJBrLhiBSbcYPd1zM6zQI6YcEm1iPLw6H/\n9Kvs8qtnUAtbVRjFIR4CEov9H/jm15X4K6LBx4BKi+OpSnRl3riJ7Rbd6d4v\nOKimT8EDQLSRYySE2na41lyTdfv8gx3eOxlEXLkFYgM9voCaLyaMx0H5mXoR\npg036LTPHa+CC2ZOnt/PcJw3ttJs5da5IaSF+6PanMtghOA/Cq8HZrE2Yn0K\nXMQ8\r\n=0UQX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.2": {"name": "autoprefixer", "version": "9.4.2", "dependencies": {"postcss": "^7.0.6", "browserslist": "^4.3.5", "caniuse-lite": "^1.0.30000914", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "0234d20900684fc4bfb67926493deb68384067f5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.2.tgz", "fileCount": 75, "integrity": "sha512-tYQYJvZvqlJCzF+BLC//uAcdT/Yy4ik9bwZRXr/EehUJ/bjjpTthsWTy8dpowdoIE1sLCDf1ch4Eb2cOSzZC9w==", "signatures": [{"sig": "MEQCIHGXCh/8RTO/uBncFttfOZKjsn+nWRvpbImWC3l9XUg3AiBt7vfoObzeuhay0pZdRDUk80jsO3XjloaaacluqKG46Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322050, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBpR3CRA9TVsSAnZWagAAyCMP/jH/qTz55sq58QmXp1BX\nDmPk5EIEIaC/aQxCPZY3Gwd6D0dY4oIz3RGaPYE6wUjIzBkkY0bA/CvNfIu0\nv2IBq/lsDpJXOqfsI8EPOYc9aDECQ+fyzv2HqR0RhYVfdLMAhzTT81X/DNgY\nsj86F4FOodz+VI2iMqdANz5CruXfWrI6rI0tR+MNZUnNSZzOrAYKnza7wrEE\nUMKQzFAtxdXE9A8sjNMWMA8STvrV0SarJf2G6iZEhWPeOqf4Vvv55LDmZCBv\ngInz+DHJFwa/cm4sWBCXAwUbSPIkdLFShqIPjVN1JunzJvjm40/8aq0YIZFm\nl6M5sILXstZmf/HKsxGgH143V2n9vyLtHlkWkirDoVjCtGR5UIYcUScZTas4\n3UppaNpGNH5XeZCqQbYyBU88Tby9kO6kKVq84MXyBgN0gOmH3IdQUzSvJaLk\nbIP7+aTFfqPZXFakj+wmN7wkqxisE5RibQ/1gshZpmNgNPFUcSfg2EKa0Q36\nfUY7RYxfLQ9vDqmSuN7lvmsxLNrzuNat6/YQ+lmKq5LUHZTvWq4OfRItazkk\nm1xhd/6jLglYpm3289x5DU6sVQLv4G7PGj1TPLQ4ERHt6ggrrr8Gx9BIGYuC\n21tcA1407k/YqqKwuY1SC7mxPY4GTPZ6XW4IA0NBwFyksltufqd4pdMYE4mU\n1L+K\r\n=OhEX\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.3": {"name": "autoprefixer", "version": "9.4.3", "dependencies": {"postcss": "^7.0.6", "browserslist": "^4.3.6", "caniuse-lite": "^1.0.30000921", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "c97384a8fd80477b78049163a91bbc725d9c41d9", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.3.tgz", "fileCount": 75, "integrity": "sha512-/XSnzDepRkAU//xLcXA/lUWxpsBuw0WiriAHOqnxkuCtzLhaz+fL4it4gp20BQ8n5SyLzK/FOc7A0+u/rti2FQ==", "signatures": [{"sig": "MEUCIQCMeN58ajR/GmNMiODq/hjuLxIlqW/+NB95R2L/HWqb8gIgYHbgxMqFNomrXddU08uBjmquiuhRg53gxZoxnvbcCeQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 322961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcFu4xCRA9TVsSAnZWagAA6o4P/2MFfjPAVVbBBgvDErDu\nN1tmsNxzIt8x+MZtc+02HxYvGxnNBFQ0q5WeXpzMWBHG2ShMryh0MQwYHgo7\nfbi/O1PufUgod2NvakrFx9GcsMosX2vLgyJxSIyVqxVGiGYQb/SaVpglvemL\nAZ7GN9M6tVYxpHUAnyYiq7iEWRjlO0NtRPBXZf/SPw6QD02Za3AnaPfrabuW\njOPpQJ5XZkjE1rKAufmOzFJ6QvT9NAHteJr9uzupL3+UK1LPMikyhJzdMRau\ncgq6Tykp5dRauXRSC5297Ip8i4o9Rup1axufaOPdVcdubEOrCEhbL33wIb9e\nW6rO5rV7WYSf6yKidEj0qBuNsTHtRfmYvUbJ55zZxPaDWNDThHg1cb0sZ1Y+\n5WPBPr6kPJAXMUfQSvNI/NlTHgr13KKLBPH3jp3AnGPT6A3seLULmzYx+47a\ncj//w1ESeblwzLlNjP76+nUrqQgkadBgU1IXqkIqfQJ2WbMMD4ONqUzbwUDb\nbSNO/N/goO8JmOXRLcu9WgszUlFH0C1dQAOKwZtiUibLpUgi8WZ6fadzDtej\naiq4bMayTN6TqqIJAM0bxV7c1xmyeRaXINhojmcxE5foa/zXlPVWfu104T3C\nL9tAEWWwSYEkSP2byfAAMXhMcG/WjW+FjtMbpGDdbpj/6o8JhFbAMg6J7pcB\nxF5g\r\n=ex9S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.4": {"name": "autoprefixer", "version": "9.4.4", "dependencies": {"postcss": "^7.0.7", "browserslist": "^4.3.7", "caniuse-lite": "^1.0.30000926", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "40c42b335bdb22efe8cd80389ca82ffb5e32d68d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.4.tgz", "fileCount": 75, "integrity": "sha512-7tpjBadJyHKf+gOJEmKhZIksWxdZCSrnKbbTJNsw+/zX9+f//DLELRQPWjjjVoDbbWlCuNRkN7RfmZwDVgWMLw==", "signatures": [{"sig": "MEYCIQDtgFPU2dlQSwQb53uZT/nby8M5Vu58kyO2x9KEJjMHKwIhAMyZENAWY08ru9V4OxClaEskgv2MTtrDxCu3j+QOeHzR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 323602, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLeuhCRA9TVsSAnZWagAAsjMP/iHPyNOVnmBp+jXMHyK8\nzdruqFsMF1baOgtGELqZyVquS/+/paJ5zRIRGhpE+lozcjG2g2nodJ3gtz76\nT3eVwn36Zz84EKXaWFCWT1faEdvx8borRWYk29/gGWl0W5F8Xi4tT+6igA2D\nc7cLNlrFeme4RTafpoegDWtLFDSb7LI43NNaUTktrljHzFdQLqw0UhTMWLst\n2F29Qoz9V2+mUE0FE9O/z75orAIjg8v0xAlH99U5hclF9xAVO7VeBUX/znna\nib/lmyrGTSAFhVk612BwarYdyOY1gk9ZpKjkSzL/HEselycaSVXWqfBWz8vc\noM3l7Urs8v2Bz3wQrWAlDcOWkPtCY9KxTTZADCUhtw4Zy1XvVbUmVFC8+0JL\nWyTaYXp4mM3hHARjdsyOISykhUU0kyb7RfpboNzj7aXAgwsPmywF65ofaCzH\nOqJFijTgPTe6Q9HvKW3zNFPQuzVQVTb3bM7xucdPLcg7uzS0YmCCksJTJXzX\nB+y6F8+ucENBvWYwIf6neHbssvEyBtTiMRkVAkZOza2C0RmSicvXgoOtyh3k\nUbAZh50cuFnjVFFHw8Avdsc4Bskrk2tcqZwD1EJ/lBTYpHodbj0fSJRrFYK3\numbmYp/zffuLSt8OV/hZrL62yXSPHKOobrKxahvQNqQ9mKUjXPJiEAToUu4O\nF65k\r\n=YxRD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.5": {"name": "autoprefixer", "version": "9.4.5", "dependencies": {"postcss": "^7.0.11", "browserslist": "^4.4.0", "caniuse-lite": "^1.0.30000928", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "a13ccb001e4bc8837f71c3354005b42f02cc03d7", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.5.tgz", "fileCount": 76, "integrity": "sha512-M602C0ZxzFpJKqD4V6eq2j+K5CkzlhekCrcQupJmAOrPEZjWJyj/wSeo6qRSNoN6M3/9mtLPQqTTrABfReytQg==", "signatures": [{"sig": "MEUCIQDkt1Ck28TFzH52etnXU6u4z4VMj9ygjWfqyIiE1pCNgAIgbZJviOBIAhV6Gf4gQQIblXFO5PAG8/D5AOVvCTBv19c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325577, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcOh+oCRA9TVsSAnZWagAAJkgQAJDlFWaPU2ZldfYMZGoV\ncnAfoJSoQJOnuvMpXli9zpiMnvuO5cZ8ngJiRD7trzvOMZorO8NMofdChYmo\nFRZwD0OHyN96CXaI7sszAm/wYV2rir4IDMaP/l8zqr0/VH8inYG43crlqqTN\nPUDXZ6d+CDpTzHLpLiSDZrYi1CBStGIBX65Lmof9m81rROxxyjrKqwzuKqkA\n8Nm2xw8uXMUAS04CtyuRSMICG7bi1gSEpbfHacrvYX/k5l9rAxRR0aRdxl5y\nbg/FeEPJdWFbAzC2eYlEg6wmwqsaWRfbZcBh06QYcHDbXuwRB3ZK+CyGGVYx\nfBijzQmBF/4Of9jp/uWpXse1qkyIRJItVqquPle3QTI8y6lEs7NLjWPveRhi\nlWMv3eW8J+Hi56io9Kzuhbwg7YLN0R39Ja6LYSy6XrGqphiyxzD70qUpm8h+\ncMZiUMGrF5oD3VXIHqHDVlXO9SdAOQ4Z45V5ivPcdx8TxMn4m7QNcxDiObO9\ncNLUqsU/qS2y4P3u9ZWwtiS4RungO/a8NbHqDvfJlb2dGX4JsdmhZxr5PuV9\na13korKlBAz/Ufyyc+qBadtw2DeUbyKKwKf9M6MHTDd7UffoleoUgDNo4PWU\nrv6FVTYW+1WjEtCl52Gj2Eo1qv2+2kUUWCSukayxOIik7PwpbrRBCg5Xw9E1\n+YJQ\r\n=8aH2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.6": {"name": "autoprefixer", "version": "9.4.6", "dependencies": {"postcss": "^7.0.13", "browserslist": "^4.4.1", "caniuse-lite": "^1.0.30000929", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "0ace275e33b37de16b09a5547dbfe73a98c1d446", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.6.tgz", "fileCount": 76, "integrity": "sha512-Yp51mevbOEdxDUy5WjiKtpQaecqYq9OqZSL04rSoCiry7Tc5I9FEyo3bfxiTJc1DfHeKwSFCUYbBAiOQ2VGfiw==", "signatures": [{"sig": "MEUCIQCX6Ta9auwSYH35S123yAcWg9nqYHZERI9RSGoILjOkoAIgViC5imypqG1VaV49xV7xmqJwBW5PfhAj4WB7DdD+y1Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 325634, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcRb6tCRA9TVsSAnZWagAAZPMP+wQRzwO9bHf3D0xy1Y+Z\nWI9ePayzFctJNoSZCx4wXEAcN4PiSgn4x1Fe2TDZ0Isfh6pM7hSRyuir4lyX\nRo0Z2yn4MsTz4cPABUIgAVf+2X/OX77qKy5h7sr3V5k5Ufl13KqemzQyd9kq\nEu42ZDwG0UBQzCcp1Ud9UkYA0UclS7Xdv8VYfLUZQUuM6c+RUlgfG8c1OYUr\nOtBP9AaYuZYpdmf2O5rvunA0w2ZiCYbsCqP+Dal4KFRM/UcLq0A2VoOk22vk\nSrIL0AdJvKuLH6+Qja/DCKEx1LDC0raHFLYQkSqfkJot4CveDEm1eyCvwhL/\nfVZwxyfrkNeOgcjT8zi4pqDApQvIr1L2lZ2MAovEoLDZ2MVORZ4xzso/X72e\nuhh+H2NWxkB0mfEHUp7FjkLgc85tpHVSgqJwRhxNFReLdgEy5Z6svAu24GG7\nte+JaIUHFRAYfVAlmJNIN8KRrU5SS+ZgUb0akr+QlnCeu/ywfu9OIs7JDmRs\nJTUtEjfk2RXA0CYHRZbcEeDt8mBPSb39R1BJSlay1WSUW8s66rQzXxf7Zly5\nKlY6UFB/60h9GwqaKPJL0zsR+g6GPx1tXl8CDSZdKbJ+J2GdMozX3eGaliwD\nNKIdLTYyYO+HAGGmbY30Jk3Yaq+KmkH9YH3pHZC/lYYNOkg5jiiRHhKrs8Wd\ne/ih\r\n=+x+M\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.7": {"name": "autoprefixer", "version": "9.4.7", "dependencies": {"postcss": "^7.0.14", "browserslist": "^4.4.1", "caniuse-lite": "^1.0.30000932", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "f997994f9a810eae47b38fa6d8a119772051c4ff", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.7.tgz", "fileCount": 76, "integrity": "sha512-qS5wW6aXHkm53Y4z73tFGsUhmZu4aMPV9iHXYlF0c/wxjknXNHuj/1cIQb+6YH692DbJGGWcckAXX+VxKvahMA==", "signatures": [{"sig": "MEYCIQCjJINNukmjVVYepC9Dp46X35ikd51OByqtjcteAglQ6QIhAPUPZLVEIzRkOtYNRk2TAyNRZcpyPW9TouCxK/ubHxfB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 326905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcT2H3CRA9TVsSAnZWagAA0+UQAKMVtfCaAgZh/9+qzFN6\nBtZkmSevvVCmqJNFLGR47fX4/S5NxBQxeClv8u9vaIdPx5/gXaMD11xVKLw/\nuMcym3m0XBFapBwk/PhQu7X5jFO2skeXiWwkCiUMZaijHbv3OnPaprBLYHcD\n6zVS9GwZJVzZ4+A+2Fos843PjbfJ1kYN41Gi2TBUMcK+ePrrJiwB3yaJGQ/2\nj/RB/r8EYRL9oBxXrM8Uz0vDZrHlWfZDVcAljmxx6S88fgrpEg00GvxTkjhp\nvnT9yq5XnQiCqLcb/xT17k61NPehSRPhwebh+n6DCxfDRAelBQW4i9iTaee7\n15oIXtYdo5XeAS5+NPkniIyZcHm+mWdg5h3ivzLCZ+fHBud32KALaqKmQz6q\niIkDrYmUVWGAjyn6I2HGtxjTdvK8BI+wvlEHE7a/QQk0nD+DNlyYzLwPgGzX\ngxElmLMFGbRJ2DQ0HGPFO9a+R6eQ3IU6slNzsAvzR+oKaP1/NOJl/wcGX2U8\nzcmRnbyutUHtIbY6oaol0FPXi3YwlNtjO/Mf2y6RmjfDTkSrKlXl81rjGMHy\nWO+9suMEkCRJ9KRGRP0BcH1oWJIalCY5gOgIUUtAx/bXvK0aZusTCLN+mpDt\nSoCoyseWz1Ai34rgR0kquixRAKRGCvLGiYT/kftJsqp+I2278mBG6KMfBkiu\novEE\r\n=DUjO\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.8": {"name": "autoprefixer", "version": "9.4.8", "dependencies": {"postcss": "^7.0.14", "browserslist": "^4.4.1", "caniuse-lite": "^1.0.30000938", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "575dcdfd984228c7bccbc08c5fe53f0ea6915593", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.8.tgz", "fileCount": 76, "integrity": "sha512-DIhd0KMi9Nql3oJkJ2HCeOVihrXFPtWXc6ckwaUNwliDOt9OGr0fk8vV8jCLWXnZc1EXvQ2uLUzGpcPxFAQHEQ==", "signatures": [{"sig": "MEUCIQDYkwA8MNdR26ThGeDSV8R1jWtzvrITdWhk7WLF9UKLKAIgNKOvi49jT1NmkIzE3rzttM+WMTm3b+QeJaC/ifq7E7Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 326785, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcbKR+CRA9TVsSAnZWagAAaX4P/1EmsNHKOMJjohk6vs1A\n7CSvo726BMBCD203AfU5zjT2ejEzbi/QWJp72tBu90Uf+XDqMeFqJjI9m7gp\nzWxL9Hw6Kw+rII8FN5OAe5ipbZMa8eEoBc+1EaG6rXrTZAwwmAt/fwL1GZC6\nJD/SbGqZOYDqti1ajLAD5/kI2EimAkOCrITofrPGoIFKGTtvrNhMZABTUb5X\n5eSkFW087OBmNeX4iyAE5q73nlhj4Dx9Cg4g1nQRu5nFp8wsEZA9aAVCz2lo\nUzUjkp9ir7EhoeD5iEqQD2CwJMjLyExxtQTST210BEDFUcWdCb8Hd+jvh8Dr\nYFhmcJNElDxurX9ANR3M0WXWtscZmAxG/j2KwVTffMFbbeOugC2AzTeIJuyK\nDLXnbQoMBKoqkMfaekUwsugnND0CFucBmqcNDNc063eW/qC7R+htEeIQMqdE\n9qkqbLM9MJTLXJnl/qwYJwm8xev4rDvG+/mp0LqvpImCUzXuooU6MT3kXay4\nnHeSz42IIejK5OeIjqc6DBtaw7pvJkP2AgzdO0a6TCHwT7byV6grUw8C3QN8\nVgKemA2NB6Jgxzr3dd/26QZdi6s2XA+m0JVlF2y2BoDKx3Abk0VeIk3ouZS2\nGfPexZGurewotUmWJMjwErEnrgBGd3SHw5XF3lvTrtXI95UY5EDMpewtqchW\n+dzv\r\n=+y6E\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.9": {"name": "autoprefixer", "version": "9.4.9", "dependencies": {"postcss": "^7.0.14", "browserslist": "^4.4.2", "caniuse-lite": "^1.0.30000939", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "0d3eb86bc1d1228551abcf55220d6fd246b6cb31", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.9.tgz", "fileCount": 76, "integrity": "sha512-OyUl7KvbGBoFQbGQu51hMywz1aaVeud/6uX8r1R1DNcqFvqGUUy6+BDHnAZE8s5t5JyEObaSw+O1DpAdjAmLuw==", "signatures": [{"sig": "MEYCIQCjS31tFfMsZ40DrDq1Gidwaz/5e1r/MJVqZGH+5QcuOAIhAIRyVdctVbsVyMgrC8vDdrF3mI5/NL/SSs8OJInUhUuN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 327016, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcc2YkCRA9TVsSAnZWagAAsdUQAIMxmdkPk/5gWQxDKOJ/\n47K/g3b3TgwoQzBVdJhMJ8ang8kvx9lFNkyWA1jUJyahyqOxx9SqD5GMKVXU\n4MNGthNPdavV66d5IqFoX78VJ/Yuu38GM5g3PQ77OBplPh6IdJv+H1P/wGIQ\n1ugVv3/pgmPuahCa89ksRjYbdX1kiqTTe0VHFG7E+FmrqOhFaDejBNRtbU2M\nYsLgt8o3VDKYOhcbw1S3AtcAoYrepZp7yPVKLW+SWt3b3GW5Z9xUfTPzcR56\nuEekqPj3ilZF2w37s5lCz1y6jj1bmazT35qzpOZmXWZIDgJUu88IQfwBk/LQ\nCxmCeOLXy43ghFSC6HDHN+BODQG+X88tmhGaG5K7xuv4S4C1dySNgO4iyNj3\nw70Z7GN17SFkXg+9Xw+DBsm3KuVcUR4cN3897aYBMEOGBv3xB96SqsuBGU+Y\nd2+bWsRtKfOdP3dCoup4pTRLVB7TJlpwAqK+Bizkr7GYzvgWIfvZ3fRtgRRp\ngFLO1RXMyAWwmShJkSumOujLE6ucIbOCLvHXcl09ieJDa05OsoXMZFA8KoCH\nkTJcQ/Qs05I4ToGm+eLGIH9nu+8g6xAgjjwB3TVpq6hZCNlf9fhdK7kg69em\nYNJ+DQvpitd6Bab5uZ45qWSbO9wnp41SKAwXWq8YWsugzK3Ld/qBzSgBj2rI\n61eW\r\n=cO1x\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.4.10": {"name": "autoprefixer", "version": "9.4.10", "dependencies": {"postcss": "^7.0.14", "browserslist": "^4.4.2", "caniuse-lite": "^1.0.30000940", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "e1be61fc728bacac8f4252ed242711ec0dcc6a7b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.4.10.tgz", "fileCount": 76, "integrity": "sha512-XR8XZ09tUrrSzgSlys4+hy5r2/z4Jp7Ag3pHm31U4g/CTccYPOVe19AkaJ4ey/vRd1sfj+5TtuD6I0PXtutjvQ==", "signatures": [{"sig": "MEUCIFQfNJg2o41PhYkIqGzmJQpa+IY6/Xdj1GXs1kLyI6bVAiEA+vV7QlSlUoeHcxaOxtHspuEtKkk4fxGVud0xQKkoGUQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 327422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcfdJ/CRA9TVsSAnZWagAAPfMP+wZ4L3W0alhYGdLGIsj+\nDr2pV4YIqMWaDSMRHOa/4NxZh1WSGwlrCNnAH7RxNeYnrjqS5IY9DcNYD5ZF\n0GN8kTDrH9XUjFWihvUM1XMKwa+N4aBiud5w95RHg82JYLMEd3rrD4x4N5iQ\nsX28RTxuR4Z4CsvD/z4VGKVjem2C1an7ia+uhjWPIfK5LX3yNWLvRaIao+51\ngigTp9HcBJ4+DEIb+p6BQke81MtRB0lxWY4S3Be1gNZ7IkHZXjkAqsdFzHyd\nkcpZzLAvk/2B+FiuaNKMEARRAr/3XNvs8ueLni4Eqdea4rT35ZMtlSQuXSKl\nmk/kvF0A66UojnwXAbJeKOwsHYXgXJcsXVNu/cWKU4F+NnCbefgOO6IbJgjT\nvxcctNd08TtLcgy3owAhb34qul0hFo5TqIWSA9/+zOMhhXf7skgr3qyiVIkU\nf0Kx/n4LVgGevGyS6oxKMwohXWFutVUUn2/bxozUUJ4GYLCs7AUS1YJA4Hdp\n7BFWhjcmh5CngRcDdO6wsKk9Ul3UPIeW/v+xTlTEWtUwsXAoGZGTyINduCqJ\n+7114x5nrrGj9DDyqUZoO6qPD+0TCc/Ep1UOtuFoPk9Bge44Lppbmaq1Vji5\ndjptbyrGHih+efWaC5nmwKm5b990rjOq0Dc1xa27jOHlwnuusoRoYsaMfK/x\ndUJL\r\n=Kg5i\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.5.0": {"name": "autoprefixer", "version": "9.5.0", "dependencies": {"postcss": "^7.0.14", "browserslist": "^4.4.2", "caniuse-lite": "^1.0.30000947", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "7e51d0355c11596e6cf9a0afc9a44e86d1596c70", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.5.0.tgz", "fileCount": 77, "integrity": "sha512-hMKcyHsZn5+qL6AUeP3c8OyuteZ4VaUlg+fWbyl8z7PqsKHF/Bf8/px3K6AT8aMzDkBo8Bc11245MM+itDBOxQ==", "signatures": [{"sig": "MEUCID1iHCJof/I6IXDrN6lyA5H+heEKo/4lFySbgAag/67HAiEA3CsbwjE9lhGVpuvg7Di2x3sDzUfSVjyzLDCMMPNUN5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 330831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcipwCCRA9TVsSAnZWagAAKxMP/3NOARZYb2ZHXrMoMJbM\nHZaCIdRok9oYYTnshGE9C77J9KjV+SkOZpW154wthNaDGngOnZIxWuTujSTl\nlHEikDR9VZrO1YE3pxJgfDg67nFBbBVvcVmxC3t9fDVV7hCy3N77qHlsSPuV\nQdppvUKsnajHpE0umDDhxU+khndtrLgl+1H60dVj26b5A5y4J6x+hEHcMiaS\nGqodzAiKevdKPD8VaoXDvQsiK2CVDh/eoQewBOPft/uk5+WrxAqkh4WHYcG2\n72Rnu6UR0YacQHPdmPB0/2EDCaINm3dgLehnuMCXkquyi1wNQn1bbl9YnrDv\nNeMjztCbcXYtOqV6JumOdwEZaiY+p6BugY5jqSq2o6j0+sV2OsH15vATB2eb\nKnFzwgECVOtdpZYp7l5fwLKJmGj5LiyMzdcynDvLOfTN3riJqJ6hqQ5XrWGL\nIf0PXG+rYpUcd4Ia/XVmpv57pS4sf/vs/HKeQ+EWciZ0OURtELjoEazE1oNI\n9ggSn2VPOL0pwFTdDJShsgxG5JDyQykMfMwbqdla99KGm4H3wDrrsQyZRRcB\n3etlHbkoZk1+yE9WwCV4xAnY1vDUNAsx79VNYqhaGtrGIBdmd1ijFyr3av+C\nz0RR1ui5yPCdwITx9BU9v3Lbrdma94yWDzdbM+s2ezSWZCSqw6N4UlKpKWjm\n0R27\r\n=E9u1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.5.1": {"name": "autoprefixer", "version": "9.5.1", "dependencies": {"postcss": "^7.0.14", "browserslist": "^4.5.4", "caniuse-lite": "^1.0.30000957", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "243b1267b67e7e947f28919d786b50d3bb0fb357", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.5.1.tgz", "fileCount": 78, "integrity": "sha512-KJSzkStUl3wP0D5sdMlP82Q52JLy5+atf2MHAre48+ckWkXgixmfHyWmA77wFDy6jTHU6mIgXv6hAQ2mf1PjJQ==", "signatures": [{"sig": "MEUCIQCvh7Kn2Z0rqpXJpJg/A8pb08K6yJBXS19vpaq+iAx8xwIgWqbWOa47iwF2x/KqMvaTs0z8R3+1TozMo3+TMV++2I8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 332685, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcqigFCRA9TVsSAnZWagAACQkP/iLagVt0lazP1bYl6umi\nxmXQt+4O5OZ7+OUC4KatJJ8ryQRBBBar4DtVgVVrEmqki6pyv7WaSSaTM1aC\nSH/FnBJhqQNdT0M4Qi58NaP8C7k9nA3rAA7ZfQ/TXebf8TrFaaee7lGRscCW\ncc0rkHKNN1PLxBLXpnDyTQbwE7MmSz0fLOS+o+2i33jlix9ZH8nR69C6BJOH\nOyGIevsJKIHSaWaddyBSfg0QaK0DDenBD/EeNuv+kYUTTn798CPel6OD7N3e\nxP7NGI5Ofj/9qUgDi1ns7ZKGNF4RJXqJL9f0ndxoMD7lflFT1MLFI3Hc+JFH\nbwCMyAB7U7/6GZc/4Mw/A3e6tbUBzhPedEzHKkHy4ee6bUIcsXaLMZ+eQ1iy\n5JgnrGSh2vUE+hs837Wpjv2bNz5RpAvLcuErJoxsVZLfuDocab0hM4axaaN+\nNJkvXuzQrEe2oPv8XJkc+RLD6xH8Ze9ZjZxmeozMW0HjEHE3/opId8qvHYkD\nBIxngsJZMQVFHrFDXdEWwH52ny4mu9drvdB3GcsTZC9gSV+xqdqQCjku8Zyr\nL5lgucq8nwqJ39rErxMOrPAC5cADFHD3W4JTaPimSwTkXYMzFdAgjhWVuPeL\nUF3W8YO6Bkg3+6pvOjYTCfATrRbk5oLiaGakggGu0EEw0vkQBNctknuGa16m\n66V3\r\n=DMti\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.6.0": {"name": "autoprefixer", "version": "9.6.0", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.16", "browserslist": "^4.6.1", "caniuse-lite": "^1.0.30000971", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^3.3.1"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "0111c6bde2ad20c6f17995a33fad7cf6854b4c87", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.6.0.tgz", "fileCount": 78, "integrity": "sha512-kuip9YilBqhirhHEGHaBTZKXL//xxGnzvsD0FtBQa6z+A69qZD6s/BAX9VzDF1i9VKDquTJDQaPLSEhOnL6FvQ==", "signatures": [{"sig": "MEQCIFdQ83Mqan2XlZy/hBbsZGp5uCClghrfYXrv3f6VI2jLAiBZceLkQJP1IxUL+j5lEY7+iGfbPnPLOp/Kt89xr5Zz6A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 332949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc9WnSCRA9TVsSAnZWagAAsz0P/3lTFXkDRPDd1PqIEnJX\naPEyzCbOZ0MmeGeyLPFn9YgA1rELnxMLIuF+lS3XJTh03fzxt89bzD+3B3dd\nidbV2lGQ+plxbFyQeIjlprv8k3v8sIEpnhpTIYELPGBDwfPgaFr1YBG2XF7c\nslmzSst5BkL0eR0sHXH/kNyH4ejJ/jlPs/43xUy1Rnq9qciEHg3gJ6w1Qxz3\nUJl5RxEqxTOCdO2REG9l8hI5XUnyP1Gf72yYE5qSfZi6EEluuY9YS/ZaVYc6\n/K9XbsdN4JuDH+XG0xmhv8i0aYiuktO7dnCNiKGdpqZSXxZJURPsrnlE2R/Q\nXHh2Nnvm9gqifpFiiqq6SylOS1T1KTNpw4TrVLQLixHv2GnMaeiDbSSKG7up\nwGviKunSrdvh6KBz1Jdi8BKuasvuBtovyLt4BACtqioZx4T9G2kgi/3VjzEM\ns6fUqbdSBMvFJvzN0T8X1dDNxlkU+KVVTc0+RqQZP0e3wVlHvQEzns+m8m4w\nPOOVvW2KdnNis7bmrlaSar3YJk7/rEvsXEHTCJ/ov95AXeoFeR0ZgHnHFzW0\nCCHs13mxk6ZsYh8aFZvTk+EMfBpiB54KttxqBGbujZUKrQZQ0F/oYQYYGJb9\nsTFkB8q0htYpXvEZkoN7qQxUJ3RyW8nymOFFKz4aRcqgtNEUuqrpiu5RucdV\nYYEK\r\n=st6/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.6.1": {"name": "autoprefixer", "version": "9.6.1", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.17", "browserslist": "^4.6.3", "caniuse-lite": "^1.0.30000980", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.0"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "51967a02d2d2300bb01866c1611ec8348d355a47", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.6.1.tgz", "fileCount": 78, "integrity": "sha512-aVo5WxR3VyvyJxcJC3h4FKfwCQvQWb1tSI5VHNibddCVWrcD1NvlxEweg3TSgiPztMnWfjpy2FURKA2kvDE+Tw==", "signatures": [{"sig": "MEUCIHiZ3zCkQlvfycSikq36yUmCPRqpXRCwXN46IKrZeZSMAiEA2Kxta4lT5pn0GO1gwYGVgpTF0RAIa95Q1gVZ0isX09E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333289, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdH8AXCRA9TVsSAnZWagAAwc8P/joBf+MGGcWsn/pgGquJ\nma/zkPcml9RDVp89K0Pn7tEixsQuwn2shLDTEvOxIjb6SsBhSGzVXxVxfhRl\nd2wnzWIuibMCE95bUkjwxD2YRx1JZtAWUyfBSsaF4KLx561rRVTRSfub3D6p\nK/R3xAZKBLGL+0DOXF5yfijTyu0/fneKRtGCnnEwCs1euOG6+pBWE8WIsuC3\nHHUN+2ftj+rBHwB0DnBJc4UhHf/Ww/ueJ+urXOHUlnxAVmWX6wFyJvGZtrix\nYXg1T0DWWzdjEpBFnajbZSfvlG4MqTj/s4yI8wV3OfWrubIQ71Q0liVmkSu6\nrptU5GnWgf9JWgk/LTy8s/6cwGkGLPzvvnEmF0RKHpeF88GHd5MVy6s75HSu\nMvoOb+vuvSh+hVwuahsitU7xPOx/0AYeoYg9GZkyFFz+u3sHMKZdbJJOB5EE\n2eBHnlncym4lauo9lHqtojMezgtkg/39h0zOSPIusiUNc5hbEvIkooRbznLV\nD8CAP6ZnwRO2EPSMVv5gnxkKbAf7A3T9lqiqZuEUAZXXikMrLIKb3knvZOTW\n1sQ+Osa1Ufl1AospdSUu5y+KPAVbHkFsN1JYyNkwdRlJdsKmB6eoLEEu8V3b\njguKVZ/7sjA7tvXQIgF4couribbCPNjsSIMhvAUnfZFCdYcnwLqYFZ5C8aPM\nhpvs\r\n=4PJ2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.6.2": {"name": "autoprefixer", "version": "9.6.2", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.18", "browserslist": "^4.7.0", "caniuse-lite": "^1.0.30000998", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "30ef0c9f090f2681880f76e335c30bbed29ff22d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.6.2.tgz", "fileCount": 78, "integrity": "sha512-0hXVBruiJrTMJprJYwdWuIg87R8MJ/Yfrt85XgOgRwyC0mpBIyDdGZhSf+AEh6lAA+R/70Y+89G6YSXsCnnrEw==", "signatures": [{"sig": "MEQCIG9yKcajInF3aEFrAgrjObRrQPBrM5gP9sEwXgJnLUSjAiAspinkPxzxP+NcccwZrGHjUEU+jD2xmPPJ6/cLTrEAfA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333868, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmCvuCRA9TVsSAnZWagAAY/4P+wSb2JfeRrok2CnhqAUD\nY5nYSUWAxjZzmUh7awFPgEqELMOAPtShnE7gtSR0KzUtjzm4MAkxbsiiWyMY\nFnNCwFsVT8MjtK0DfLnP5kFSFYjuwlFEJTVG7ITWeEqoJLHSVSCEp8XSAfH3\n3fYbeeyD8/zUMsIyZXlKf9RyucUm7dwqNFnjl8Xo2x/449PQAHGV7iHsoZie\nIbS6lR6mWziBmT/GWiz6yuF1WDVxyQDaQnUevfmrhE/4rIfPcS4PVM40+UUU\n/qW9v07H+pWVaVrWVH9YI6XENRbgzF4TRo74iGc4gk3bleJhrQPs+wyDRCf+\npuIy7Bn5yK2FToIeZaWaMYGUYe8GasrMWMQeJGGnTU4sZg0XQlxykg2ZbJdw\noZoYxXYijukb3c4QRqecfbccBaUC0ExUywFlU3DbDMdJsJPMYquivYlhVi6+\ntTmqP3HM6/A6Vlr5gwFwTQ9zK+koju+dZPj1Hdg4D3qOT83JKbsOadHqf+VB\n9KJpxLmulv8lEIE9NFp1KkWxoQTkHsHEXfx/EQesnyjL+HnhQC5uhK35CHoE\nKXDrM5XXh4zAigBBpvDBq1mvrmOlzyIQfM8mmq+XyhFqbexgwJIRac9NbDbk\nF9zj75LQI4h9pmWtvHHcVIQDIB6MJFC+cRIUjInOjYFD6CGGHLLX57/3fWcD\nuMFQ\r\n=zttU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.6.3": {"name": "autoprefixer", "version": "9.6.3", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.18", "browserslist": "^4.7.0", "caniuse-lite": "^1.0.30000998", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "1718d89a5644a61735163fe02d0e6a2ae2c91a6e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.6.3.tgz", "fileCount": 78, "integrity": "sha512-Mtaw6y+DKDo1LvtAIoggxV79HR2/do0DEuBzXcpTZ5VzRykgXddsHQ1ZEPP1ydMubKvmmxBO0XiX53S0EJl3ag==", "signatures": [{"sig": "MEUCIBp156QgrrMY3JB65jcvMJ8IElJYWWWRn4bKM09fIgyFAiEA+UCgAUzVoMOsND/HRJDJoRCZrKHI+lz7QwKorsxLip0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 333972, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmMW1CRA9TVsSAnZWagAAEJQP/21Rp9Pl3uxhC6Y0Ae//\nMK+hT89k6SDnyHBiB9bqtZFFc9/j+Y0VZAcPhjWJpu3rqoPdnqGtt9UGQAl4\nXLfKnIz03TypedCcTleZEcyhansaCc/dZmK/uDCkhCUPFRQNbzBnUf34Ah4y\nZHe0KI/XWmvmMp6FIhCQ0c30wbba7HjKSRBbgTUvheUrWV4eFTDC1jzyRYqL\nM2Y2m83ZlSaijNUb7QzhXvHBZoOk9kTqnXqP3VZsQkWHtSTJnBVxkZKJlGOu\nXV1GKHIrT1PuMtblv8IcQxQCfrIwJ8KNWlmvsrKKbS2v0ae6R2ncabgs4AT3\n+UWCmRgc+LMFc3Yoi/kbilajCtsoTKW40PNjOpQ8kANkGWjfPmWBy4IV0IUF\nIlM91UYwQYJ1f+/6r2SsKl1sdJbujK+yEyjWF+byzYhV/5jgIvwqIH9SJaTp\nqjNA0R6TThsM8L9P5IHn86hR8K/WYOzX8HLDQp10s+2k5vHv0OerXFQ3AtzC\nO8DoWyvzLgSYNMHySJX9uN4eCY3O1BYFoFriqsVxPsIziHJR/o6tNiDoXORi\n+smhgi8ySACPsXz6pn2e52UWO194kC0fSrOJZqdP76hm0QW8be4aPdDYYaos\nBhQU1Qc3atHqQ/hW8y1ac+1SSwUgejX7RNjD7x8xznLWPrZOKuDfkz34aUWb\nsfRh\r\n=bSny\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.6.4": {"name": "autoprefixer", "version": "9.6.4", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.18", "browserslist": "^4.7.0", "caniuse-lite": "^1.0.30000998", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "e6453be47af316b2923eaeaed87860f52ad4b7eb", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.6.4.tgz", "fileCount": 78, "integrity": "sha512-<PERSON><PERSON>2<PERSON>JU9dKOxG8P1f8uVaBntOv9lP4yz9ffWvWaicv9gHBPhpQB22nGijwd8gqW9CNT+UdkbQOQNLVI8jN1ZfQ==", "signatures": [{"sig": "MEQCICdqqvWNbFTt9RqA2TNUPLtfSFnD1Z62pJV/+2HlurDaAiB53AjDcV+RZzWrHqOTn8hypLGkKCkPR3H4mQKAQl3dCg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 334038, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdmMuBCRA9TVsSAnZWagAAjX4P/2W552UNgMEzZLzVmfPU\nqw//u8VEdAu2E3xRHnSWHdOSrLoSR2bhirmzlUm7mfskhwiB8xsdw1Km/nXs\nN0QrdSLJ877sOD3HEyGNl/tKBDssZSB+GO6zsvvVEuRZwbYVdfDcSHe44lc0\n/E0ipIbNCM/pi2RVMcjzn/Xt80zSScF4rjW80C14X9YQTtF7k2r5sz4L7km2\n1kGlXsZrVu5WuikGsLp8jtojFJkx/lYngYqLsXmehrjGhwDPsWLpr8CPX9jg\n6B1+vcUYsqLw1TV29IviN8G9fuuY3Uee3GDLp+fegYGS132TPN4I0RrwoSV9\nN4ztRxvKvdboXAxMeqWzHk9cUFxpu+/167KQ+4PTFgtp3l+65Rg6agHRtdQu\nmRYUSwHu6flZYx4SsBYY551/p9pBPbDovFV8WcjZabUybKB98be3KgI6zHkA\nTBn1wm+p+mdXrnoAQB0stukrj4gkGXjDhl3qH5soALPrUjn1ZVHak/8atsuB\n1nvXsIflr95eZ5TomJ22WklEAiItGnP7hEMRS3p+1Jcf4halMdl7JyKZYhrV\nTMTsTZTh/ySRHFzdUjt9dCW9HqnvASAh+/qV2BrFcJhFVk6hMntUH9teAgQQ\nr74neIXUIMi/5n+FYnrXSL+XjTqZbNiDzKbpjAct2BqhfsaKBg6OZ1572qR7\nydJd\r\n=ex85\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.6.5": {"name": "autoprefixer", "version": "9.6.5", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.18", "browserslist": "^4.7.0", "caniuse-lite": "^1.0.30000999", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "98f4afe7e93cccf323287515d426019619775e5e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.6.5.tgz", "fileCount": 78, "integrity": "sha512-rGd50YV8LgwFQ2WQp4XzOTG69u1qQsXn0amww7tjqV5jJuNazgFKYEVItEBngyyvVITKOg20zr2V+9VsrXJQ2g==", "signatures": [{"sig": "MEYCIQCTHjARpAZ4h1gDZMjTh0npAQR/cU/YZTimGjvctzTNXgIhAM8u4pz5pxtwCY8iXPhX5nPJYEf+T6MNy9mGHaQTd0iO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 335293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdpCqMCRA9TVsSAnZWagAAyOMQAI3NQS1TdOkJG+F2DYzK\nWHVDuCu46RzJv9c+gtqR++JNkZziTO2/hZqdq20cR3ZHzHQ21tjxT+yoeG8+\nrpo9aWzryFqeQrNQmHr7QItpwSmpe31xqd/YVE16te+qjv57669vHDrmhL6R\nH0DEKyRVHmLcVuhkoFWYTBiYL6Pmf42phdvFXe4BqmYj5T3VEJ8mcti/sLiH\n6cfNft8Ujul2V1gOYGyFmZUqqam6fxQ2IkjlDEXyc/YCvYTYdHMrDdUMNwCC\nVR6Gl0dX4gzqj7Zn/l+xWnmGdFPgN6a+C3G+50sBEo45mBGQltIAxMVi+hw4\nm7aboFZcka6YoMf+68iuxFlbrJH59bMNybQtayPsl5mGDUZdGdRrOf5ZBe/S\nydnDo29MKjqJ15/NEbnXgPx06BKB2ROo43sMFp7BE8FE//z0d+xw4dSltNd7\n06In1TPoH4c+LWcR3hi3aBsTT8Mx61xVSs+DwSeoQmdMmE4FK/OZbfD1AHII\nB8fT3PRmCnDwCgshWd9blfMhhD1vTW9bG5RnlqFTTtk3Hy/ZgQ9KnYiFqcGv\nKsZrgUL1NBr5gygzBwW4eM+65EQ41dzZ122dzedwlzklNJksUtp889O5M14c\nbJfOS6UK9AhfLF0S1tu1DO+P6RcbgNb5oB04v+G/5j3S37N9KHAP57M8C/J0\nbdIR\r\n=w6+F\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.7.0": {"name": "autoprefixer", "version": "9.7.0", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.19", "browserslist": "^4.7.2", "caniuse-lite": "^1.0.30001004", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "905ec19e50f04545fe9ff131182cc9ab25246901", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.0.tgz", "fileCount": 78, "integrity": "sha512-j2IRvaCfrUxIiZun9ba4mhJ2omhw4OY88/yVzLO+lHhGBumAAK72PgM6gkbSN8iregPOn1ZlxGkmZh2CQ7X4AQ==", "signatures": [{"sig": "MEQCIEhsdBxGix+iYzH9nmLehnQdI7sOuLY6ip9J6mRhxduFAiBfzEzWHtpu7CYiIuLu3SWd2y2PrPd2LD9CMcElGWsOOQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 336512, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdsZ8tCRA9TVsSAnZWagAA39kQAJc8ZXVlkriLATyig07F\nviOFDhEFUNPZ7ZISgbPN8sgMjasUoTi1Yqce4B8sdy3tR51BR8HH5JgLeKbO\n4mdOB7hBPgWxfU4fFxFK1gOTuDme071rBV06USczc1w8jz5k8zRb6nmVHQkH\nbiC0zqb2Ag8wOFECsW3pc+Qi3M39D7YnCsCJlyLMLZFflR9aGYA8Q2RYJM8D\nXrDp8o6HSxlIkDp4Y9+VlNF4pyLpuiHDrXllIXYP4C6X/9WF5Zv+vWe33NM/\nPbuZgNYhkoOo6fBSMMqpyCXLKKny033d8HXuurmoyizpKtONNGT/eETn1QuC\nlviU5Ohfvv5u/DuJOqvUEG0bASRi3oJ9/F88kOWsQ8CuxmNt6LHXzcE5LBAW\nLDzuCaF7iYayMdMj58HP70y88pUF2lM4Dk2wTsdB1RPNwJ8Pjnpx2slRY8+j\nv7lRHG5sYsszSAgyLlB5UdVLw5m3NtHl+lkHC5pe17lMETj8h27qTylVQZbI\n4R5wZpNqWsbR/IbxDIajCdhMwJtDPPqRJncFLQTeHxMY9dNh3okT7LP63BA0\nJp/Y4DVNvflnp7BInq1CGmrzYiLXvEnCciVbKjOez/bFcFLOH32aY/0aM/I3\nCL32u1ONuib9z7E+5nf321LCQqQ1qtxU+BBkO2/09MQa1cd/nx+SSNV0I9tg\n2LEI\r\n=lrvI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.7.1": {"name": "autoprefixer", "version": "9.7.1", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.21", "browserslist": "^4.7.2", "caniuse-lite": "^1.0.30001006", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "9ffc44c55f5ca89253d9bb7186cefb01ef57747f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.1.tgz", "fileCount": 78, "integrity": "sha512-w3b5y1PXWlhYulevrTJ0lizkQ5CyqfeU6BIRDbuhsMupstHQOeb1Ur80tcB1zxSu7AwyY/qCQ7Vvqklh31ZBFw==", "signatures": [{"sig": "MEQCICEe1cBp1lSg+zDwB8RVFfcgwTv88cxb8yilsG/HxAKDAiBfRVCVhdCu+RbEP59aonD4Pqm/8i3r6wjYljGM1b88Yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 337286, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdu4SACRA9TVsSAnZWagAAVlcQAIkbNqhKa3Ubf+LfUUN5\nJTCJbtf8gDESwd+g3lFCudJdH+t9Een8iloCxXmLpK1qzhvYfHVghjn9n86P\ns3J9zjlYd89N6z6zXeOzXC0BAsZI7VX3cRz8AVufeY+s9dMzJ0xnQtjaIIjo\n1Z47vPgFYK042/gP8MuTi9pE4QRGBkT/EtHlHzVQx1JiPStQ1qh1U3HGmrSC\nnxOjSN0ksT1mNWfsNRCBE1rUDtnkGDESipSp3fiJRwOmhyDwoJMKOmyJGHP7\nH/T57ctJMhMTue44R3ILo3Qqhmy+GI+KNDq3Vc6lRPOg44Ap5nYgaL2ylM+P\nIbUWV/LSae9haWZnyO1hHeTNOpmYc1mPxI1tqL23f+zDRThEsVcIgenprHqB\nlZi9QM3mIaL3phSISEd3EnnO+r4u9aVuP5lcxhWz7eK2+Jr0wAudyPGfuWaa\nIEuKVgf7veOOaV/e5hWnTC6ZBieMxHpqfGTzdOw0E9ubqPAyx3UdEDiHL0aC\nl7Gm7fOSLw6WAv0K7uJjci9BDybAeFPspCGrKopvaIyCePQcYYpWJU1YHb4y\n29XM6llDPj7QIlS5gxumKiNIRS3zbBK4jBlpDBowCouAz9au7QQTmDwtMu57\nDldW/xVA+lY8E+3d1JfxbC9MysLKrN+ggLmYJ5Z4Sr6bWI2HCg2EnVFoDdaP\nqlS3\r\n=jJM+\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "9.7.2": {"name": "autoprefixer", "version": "9.7.2", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.23", "browserslist": "^4.7.3", "caniuse-lite": "^1.0.30001010", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "26cf729fbb709323b40171a874304884dcceffed", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.2.tgz", "fileCount": 79, "integrity": "sha512-LCAfcdej1182uVvPOZnytbq61AhnOZ/4JelDaJGDeNwewyU1AMaNthcHsyz1NRjTmd2FkurMckLWfkHg3Z//KA==", "signatures": [{"sig": "MEQCIAuuCBP3M1M93QjYdA5/BueOIYDSPazXn2zom3+TtjiyAiB8U6RjcZK+xotT9SkvT44lNW2S20TqDdUvu3YssCZXJw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340395, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd0ykSCRA9TVsSAnZWagAAlvgP/10+y422snnw1pqd+Wwu\nD2BwSAQ2R0/SmnAh2zaFV+jhwLVmEdUi6wZKHnAJ//ovXfMGAxH33KbXoDg6\nTJxY05PKfL0Z13BwZq1HoLbMfNqVknqWCKaZ46UX2eX/g9Nz0i6OAY/9E20Q\n6XZ4devVZm882HCqhXXNmFCIa/sy0qb8Z6WHdSdZ6HEYCEt25yTvr6s9CAtb\n0Jk03ymjMFLbWaiUuHsUXp5bT3PtOYLDDPveMqTm5VaUNcSn4OMc7zAtF2aD\nL7dV5GP+HGerCd1VenZIVn6R4/gV8Sj6N4BYroJoJFe4bCXy7uV+rRB5JdKD\nG6IU0WalshxozRbNLzqCPnikQynnbI/cb5VgggFY9awxr5L2Xf2bBe2j+rnc\nm5iLXvUKEK1jY+ljlb6qBNyb0nfkqCPqrVuS9wmrZfV4K8d7LM2JAAohxt/a\nCwaXKpc3wyJ/Z/p1Tq+u9eaImfgL/mq2PmVMp7oa4e+dQo2FU5fqjaUwMQ9V\n/B2PSgiTQKRDtit42Eh+3ubJdBYzRb7PF/2NLQOrcIwljHOTYw3XDSzzZsAR\n6atFBYXoFJZBF0n9xYvIxjHHmSqqyMZEPudQ7iPXhkRazlHrmBxA4XW0lqA+\nhJa+hAebV2NmlnN3/eZX2luNhJ2xMmSCoD04VJ+drp8QzcmT0iAlxEcEdBd5\n2nBX\r\n=b/jQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.7.3": {"name": "autoprefixer", "version": "9.7.3", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.23", "browserslist": "^4.8.0", "caniuse-lite": "^1.0.30001012", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "./bin/autoprefixer"}, "dist": {"shasum": "fd42ed03f53de9beb4ca0d61fb4f7268a9bb50b4", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.3.tgz", "fileCount": 79, "integrity": "sha512-8T5Y1C5Iyj6PgkPSFd0ODvK9DIleuPKUPYniNxybS47g2k2wFgLZ46lGQHlBuGKIAEV8fbCDfKCCRS1tvOgc3Q==", "signatures": [{"sig": "MEQCICiW3uu+ul6JBmXFvSHqwLOZTmMhObTo3Do3g695ORuCAiBwE//CANJKlt6V5zHOMn5GgCScjtkMI5pQ0/GAbZ1Gtg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340531, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd4oadCRA9TVsSAnZWagAA1hoQAI8da6jy04JH/ShNKEio\nL4LaGa66IUBnjws7nL8m0aqUW0eehSzPv8ZCf9muinhK8hZ0ZmvWeR3Ubxs9\ngkbZ2k5GnsjJ+R8WCsI3OSEVsxvu1VzpoHCeGijm1MJ9JiW8BCdXgVSVX2UL\ndLuTOYyAb4PO+DdaXkTjxut82jzvuuQ6otSQTzSvtKEmzstXbBAD7YX/HHQ0\nE4Kp92eL5o1trv1su3OrEPwvIo0HNzWZXX0syTC5QGgkS8X/1nNomZqxRJe2\njvqmtSXSCjfd6+3m6c6oRDuZWCZJQR6c+/hqoGoVcdUDttD5m0YTXmd/0ZsR\npIMU2e5EWbA9Jyxus278y8KErvW4JeTGS90FMrlHzMSNPRG/SvaGVOiUW4Aq\nzgwxtLRnbOTGbxZCV3tVPXjWVaKrJY7xLRbn7FKuW1x70lBqKtnmEEVZCsWW\ns3lzlA59c5BmLkHvxZA5RI3dhwugXfotsZ85MMwo/OcMpaeIyqQrtQ/54I8Y\n758UJomYEjyZiXMvy1xthDJCvbt4QXKJl41BWHAszdNaMDH1Ncr2/IWTvG9S\nnEmR5dJbEx/E9EE2QGSU+OzJhyVBGluuS/lBcU3dz14WyCu3LVnu9zzIZZPG\nNZb/DevFZVE2+vq/3Yn6GOYiFT7ugpu3xQ5JKnSBhb8ozEC+4+SfDTPscT0Z\nyjNA\r\n=dgQv\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.7.4": {"name": "autoprefixer", "version": "9.7.4", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.26", "browserslist": "^4.8.3", "caniuse-lite": "^1.0.30001020", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.2"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "f8bf3e06707d047f0641d87aee8cfb174b2a5378", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.4.tgz", "fileCount": 79, "integrity": "sha512-g0Ya30YrMBAEZk60lp+qfX5YQllG+S5W3GYCFvyHTvhOki0AEQJLPEcIuGRsqVwLi8FvXPVtwTGhfr38hVpm0g==", "signatures": [{"sig": "MEYCIQD+gvTz0T5+sFxV5RleWJ+rOyRhbrQVbOLHaZOU/+B7MQIhAJF5xMMma8RGNkQMPLHPArg1KXhmN5wU75lMZUowoC1N", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 340554, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHidYCRA9TVsSAnZWagAAxjcP/2kjt45wRDrttRcSjpcr\n/gFGntTT0hANcma40BojwtpKocXLQCARNFohmTM9CFnUCs3hbkpj0Ak4FkfJ\nSdll0ou4onWesMiP9KY75K0DGVkAlwEizGTaOY5FUCoXfqfFGVGl7GjJ/cH6\nh4cY6lht5EXE0VZvzo8Abburte8qNWf6z5EcDQI771UGP0m5EA2HNp73yqn5\n58I7e9kUd/eTJYvrmycvvKySxOWBG4CuJeFNlX+gg0f8zhMdJmRifl41b1LG\nsBpCCYNd5cnS4AUKCW7JkJoYxo7cLexuJUDRhPVDcxQo8zvs8H/NJtsdNhBp\nz0ZTx74MsBW/LD33OYJ5hnEBYF6CNb1S+feZkM6QUBAxT4K/XJ/PItMBaLW1\nV40lz8bJMV0fyaHREYmT8FtuR1T2pjdHStCCV9gZswVVYO86pARVAZA6Qkay\nioT7xKYVseEozGRmMuObfYVKHYrZsFurL+u2RKSGVCsgHDiIEdiOwAva+LkR\nftpIxQM5rV74IOp4e3ZtssznSdZcEwbhAVVRh/sKB8cFF5JDoCw3+9LGa7go\nNEshgnzVqGoeIAPbzmFr5PpBS5AXvBXT0ev+hDLQzV/OKkc/YaxJV/n+baND\nMZhJwEvMA4LslZU6FQgJOr1Btmv45y4Khw7lQtz2r6mHnj+fGR4UWnhjs9Rf\nTHw1\r\n=Q4yS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.7.5": {"name": "autoprefixer", "version": "9.7.5", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.27", "browserslist": "^4.11.0", "caniuse-lite": "^1.0.30001036", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.3"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "8df10b9ff9b5814a8d411a5cfbab9c793c392376", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.5.tgz", "fileCount": 79, "integrity": "sha512-URo6Zvt7VYifomeAfJlMFnYDhow1rk2bufwkbamPEAtQFcL11moLk4PnR7n9vlu7M+BkXAZkHFA0mIcY7tjQFg==", "signatures": [{"sig": "MEYCIQDR6/eeOl2z1sWtLE0pfk/kOUwnyaFXBqX8xfi0F6sF3wIhAJ+uh9+RIWStYoCkJlg7OEvq8F4ePXCJwwPOD8vcdXB6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 418720, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeeVTfCRA9TVsSAnZWagAAAQcP/0NsWOGzlS/XPOc55lyJ\nfmiU1rGUp3isaO3ZJI0tUJG+y2u6kKKdH/vhpCMT9w1aTne7UaIf33sqBqAC\ndD/GBdnORh2u5r2mOSTcLy5Q33XHYbmEl8t+w/nYG6ihaijvytb88kPDctMw\nc/zfxWCiA7W7kOowZYziY9RX/fu2VblbadOGsC0RALOXMKNvXM3WiUgPjuFz\nivu3HUiEdDgCv1JoFpgP1PHw9fgqO7606csnnR5E8ZDh6Hy2WeFlFYF4S6Dr\nuUSoEBu18sTodpAT5hBDayIi40AoH+D9ajs0CnGBK5oiSxe4yxLLRpH3xIKg\nHcSRu53J9oZFKj9eR3vEyU5x7fTMjufKd9Rg3UZFfpBZQavtfBPKL1Xp6d+k\nViWW5MuQBb1vpStD4WYQ+j++J6FCAPlIrepi7soEOkxIoW+XQzy/ocZ8HQdZ\n0Z6rPg5XYa+nXS2HVZXxn8qjZEbEyzKKbql22GOhMDo+8ojJfgZxk5OsG58E\nm/zXUetUeDhqx2NSXLH46jn/i9HmNT2klgKnZA9JpJ4FWq5jGEbGqYxP1Y6+\nt5VxnTjQEg93uJPVNLEcjA52fKFfuR2t2YfJuEzY5GAsbu+A8JpTvTNcYB74\nBzCjOahJsCLuPw+kRmenu1fUs35la5PLmU+VCJ4kI2sNxMvXEF/IofBLQ8Ob\nXJuR\r\n=8JrS\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.7.6": {"name": "autoprefixer", "version": "9.7.6", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.27", "browserslist": "^4.11.1", "caniuse-lite": "^1.0.30001039", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.0.3"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "63ac5bbc0ce7934e6997207d5bb00d68fa8293a4", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.7.6.tgz", "fileCount": 79, "integrity": "sha512-F7cYpbN7uVVhACZTeeIeealwdGM6wMtfWARVLTy5xmKtgVdBNJvbDRoCK3YO1orcs7gv/KwYlb3iXwu9Ug9BkQ==", "signatures": [{"sig": "MEUCIQC2yLbJ6W02JwUHt94kaBYPZw7hY6bY3pgD3q1hV4BQegIgegltrVicj6XLrTaLbZWHaFmZhuohRZkKKNoBaLE3/zg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 419340, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJei5xFCRA9TVsSAnZWagAAaRUP/jQSvU8NggTiFm8k7+Wi\nKm0PlDKjQL/rUurl3nXD7E8P5Q869BLJVYT4enXtFctByFV0w47UwyPWCTJP\niLIlhYqhLcyOeowQlEvPrqdSoBAdSoHOfJpoMgLIenBdMowyP0M47inOrkSe\nacZiOkeGNEIgbC7yEkS3O/WVAggz0eft0cTyjUJ1BeiAWu180JPf86GOv5VN\nQZ/UzrsCftVVgwJ8XeN0mIylCpDOQHuzVWsMW5WXhyc/6JRIZBkkUVpOhVpO\nfTwUhUfvIf8uinYDIIFHjAo7hJ5hlXkLQmDmTSpFu7pKYJGMnzCIYLbAR1M9\ntm6eMewWBmHF3Pjjef3LsSjy/uKsdDzv99EEu4LSkseTsNwVQjWtt1WZr4Nb\nsGutkiBVNUIz8hYazRCxEUDtngtpGKLQ1WodtbJRg/q+Vc9g0VX3Ae751of8\nLSA2Bea9ojqmwxRh8fwLbPWPlJEscWeELzgYdElVfCqoBninrZxopIZb94gK\n2y8Nnixja9D58yrY9e0wYHYGoLPvOOFYXcEGJkZ/7nc7V3opuAPmcj9wzHU8\nVAr+1WzYLEs711iJ4zauPJ50KFbfTP+HyU6CEBEGfVVUQzDtj4vCqLqHxo/x\nf4oLciuqXIRU2sunTdmYrJusRe0D4n3WJP22NBJv06wEAkj9e6EyMqWMsvyj\nNsqJ\r\n=69bP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.8.0": {"name": "autoprefixer", "version": "9.8.0", "dependencies": {"chalk": "^2.4.2", "postcss": "^7.0.30", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001061", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "68e2d2bef7ba4c3a65436f662d0a56a741e56511", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.0.tgz", "fileCount": 80, "integrity": "sha512-D96ZiIHXbDmU02dBaemyAg53ez+6F5yZmapmgKcjm35yEe1uVDYI8hGW3VYoGRaG290ZFf91YxHrR518vC0u/A==", "signatures": [{"sig": "MEYCIQCedHLxgkG3VZroXthHN0bcAdccpirvo+k3o/LwswV6ggIhAMt4HcpsSJ6IsFiff5YzoLkJcbeewqQnlVfQBhoROOMb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 347004, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewMXMCRA9TVsSAnZWagAAB20QAKKROUES32rvabuIrr/+\nNARQCkqsBloDbdlmY4E+sqPpKwYvXxhIRsyKmcd0f/fZORgPqY/vOzSXglOT\nEtrj+74b/YS1CJdduK/+RfbEUYQ0gE4NAPoV7iB0DIdEzKHxYdFLcxHao1lE\n9OhYeUlXbsfFb/De4998wFTF9n1noQc6hNO2l4Rpq5JFFFI+MLX/Mfb9LEPc\n0/0IiusIOLcIr6UMMy2J8Pm+vc8ui0pxYCS128Ltk9cNv7Hf/OWW5t4LRHzJ\nKQMgag6LnV7ohiAGSXHIts3wn/hynpOta4bnlshJ4UEAM5mug310x/aGMLrO\npA8+aPeheXUWG7fItsMFroWfafgx7X5MKdv/l6LItUza4Oh+qd5/VfJe676s\ndC/hLqzGfZFwKByFD9Ke/0oM4KfV/vTLGE/ofzlwJruB93eABk67AsY4PYF6\nNL5t0vZ9wY013U0RJGvdACoSSnwOjPkyA3fuF/ljjOHZljQn7vFL5IAryK7X\n9M1RMHESxXheJvQuHXSMlUPvrLA43kl6eiOXTXqrFFiExtVbe5gWN1UlAr3+\nwYqb5bZH/r9YX2FMnAnHw+F84CC6Whdk5SJw9Dz0qkt1sJEGxTCAZqpY/779\nCA30bRsiNMiGulit0ezWmxMJWYcxXg2YCZsXZ8kDreQb87kf4OFA938kaRbB\n49bN\r\n=Nwqu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.8.1": {"name": "autoprefixer", "version": "9.8.1", "dependencies": {"kleur": "^4.0.1", "postcss": "^7.0.32", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001084", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "09ebdf209ddeb6900b310c71219f367138950ddd", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.1.tgz", "fileCount": 80, "integrity": "sha512-zDw9+mkCdWZHloBIGrOgMq1tTUed4qy6ZgNAe2Ze2xERZA7CyTgW5Bw3XZbwSeJe8lfDHZIkw8Hwd/6hI3p0NQ==", "signatures": [{"sig": "MEYCIQDIyeIpKWgsF1HxV5uYrmc0t3AuCfpjaT/PssdIa97JiQIhAKiU+ZWUcg58z0BFsr/ca9amOE8tAlzLB7wzd4zn0FDY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348754, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7S3WCRA9TVsSAnZWagAAI00P/RnWxNe/6+4XpiAz/GvN\n+kfbstthTAI1oIuWWB4lMlnIfSHCP9s12Zld2KSE2V8CWyydzi9gcr/Od0en\nwzU61JQklkxekVHJNvlRA5zFatJ4EUbWiHWsxlDxVOj+hgg2Kmfwk3w6Qwxn\nx9LWOiVIHnhAG56scrFJVLC8dX+S0/CKtJvsGnOfQpmpWviCNqEruvgCF08d\n3aS9iNPPWvnxeOSmodMkTX1EiEdbu5jdbKd0veo3eAcPALLa4MMk/C2wQFPR\nz5jUE9cdd2RwYqEDJtEvn4uFdemcjGhOcUzEfLrQfcFIp8mMGYSltSUB1cHR\nLpIOAw2sNL6lXJPoFvIjNK9iWn7A2+ivUUmXG/CRXWnjjjKk49K5R7EaxAvP\nAjrFO1a+VWR6audeFIjqU9Vi5YRSz2Ka344rlaM+gsWZcEAlqBFw8a9ZHDZo\n8X24a9JFNSzF44qQBfEGQTDICskmN/XKziNGcicdiaBKtz5lTVVdWCNZ44BN\n0BSnN7mzOgMDuGT5TGtd4RkrG2Z9uhNV7KjQXGgJ5AZ6dOpHhjmIVt4X6w71\nYkhUFh+ykxcuR5FjVHqCuBIK9Zkz5S7hJbBP/RMnenVwnuxsNHxG/uzqLKmE\nI6ZfsbnmwRIs3dgi5kYTWyCtCJLtcTdV6ubAT/6QOPrOQ6DvNa9N0ib4QAoT\nYVIh\r\n=8hif\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.8.2": {"name": "autoprefixer", "version": "9.8.2", "dependencies": {"kleur": "^4.0.1", "postcss": "^7.0.32", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001084", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "7347396ee576b18687041bfbacd76d78e27baa56", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.2.tgz", "fileCount": 80, "integrity": "sha512-9UwMMU8Rg7Fj0c55mbOpXrr/2WrRqoOwOlLNTyyYt+nhiyQdIBWipp5XWzt+Lge8r3DK5y+EHMc1OBf8VpZA6Q==", "signatures": [{"sig": "MEUCIEcON8ke/a/pbe5LD50z2DCa6zR687s6+BuXWT7JGIumAiEAi7kQpVa4+27YJ1Fimr35gFK5kN+hlRxrqUm1hQE39DA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348870, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe7rcMCRA9TVsSAnZWagAArVMQAJxo5fC6mSAyeKiNYoIR\n1YoBZ7DOPuLJOG5AuICaVk+YWzNdyozLnjxzb6XH0D3O3Xydx1ZMpCyU9qQi\nEzaKMFxhulT3nR+G8B1BY+Dyg+qFg8zSuCSzEx9pc0ZAbQ1q+kpxIpNOWrde\nSQjPdjQQq6PrF3+xfmIBWvBcegFs5XqynTJibX0+q7IV1NhFBbS7LFRW2s2e\nW+J8QxArQdUv4x4QJhjJGbShFgLOMaCn/TXg7PFIb6c1bugHiPYOcz4EF6vJ\n9koIc3LggsdoS7aiSf1+kvIcqhE0n2lO4pCHvWjbLzMEuMovMx/oMuyl+tIt\nkW0harYGNlPEkukojLTXh1sQfra1UH6ap5IfEs+J9xAjCs9z9+TSVZMOStD7\n1kcdYeEIgI3PmkgYqog02cULSJM546ZbLCjmdSB3vDrJt/7/CcC+GepnVUxC\nego7ga4o/9inEVWcRGtqXTagKPE5u8A1zvShThKYFEBBCSxmXp+c3ny5Bjt0\nlsNTnpMU00QZRG28LjKxoJbXNivJucY94Drt8W3M38m64XK05Og4w0ycqjmW\nydArzdn847EYzXUCYLcod3O3OF6vHp+Zbu+Dxwt4gTAKF5POIvSlY+ZaYMx9\nnv7KhL/8nJyZwn9smyhp3RsfUQwvdqQAHMgMp1Hu4s8kjYuvWiAqwrdyDU0+\nYB8R\r\n=lSyh\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^8 || ^10 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.8.3": {"name": "autoprefixer", "version": "9.8.3", "dependencies": {"kleur": "^4.0.1", "postcss": "^7.0.32", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001087", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "e04c8b7f72c608e4aef1b74a542946e2ed271753", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.3.tgz", "fileCount": 80, "integrity": "sha512-Y3CkEPqPqGw0TNBcMoUAxeZT9WEOAh0BPYENOTrN/bEfNBqjlYWjHbR1PKduBrmAVn8WbEZtMA3gAZO5MgV7Pg==", "signatures": [{"sig": "MEUCIGiBPcjl+VpQCbfoQf5U+3A8iV+P8xYI+K9lZj2qA1JsAiEAvCP+LHSR4R3bW7+lNWycoHHtRsypUYSBqQFvQeSeCPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8hMSCRA9TVsSAnZWagAAOewP/Ap76UQUGn6CSHernzRg\nXQFRhXNrLx9Te6pYvJm6GTWj6zSfFNRSAU5baCEF9b4trCm2bPLZtydIkRNx\nl0VcY4auY5Z/vNAGVosiSM1uuHnpCeRC+FBJu9C/qZKK0bLyzlYRIbhX9SeM\nlp3hKo0UNktM7dWhA/rwAmMCO+9Q3Elul3FZjqN6bV/xhyJk/hc/GfNMDrF7\nXtbohgDmtiMmqChiEnFizcrAUDXPin7s9AEB2I4kERO2VzNWDCLxExjcNXl1\nMhLwKDB53p9K3/WCAbFzF8KGIBZvOjhBw1bf2O0XEO1wg4C5fBYVtxOhnT5i\n7Qp5zNS5iHbYWPiykYR9lAebCCEJtqUyKcIp/jrSz/KDKl8+jqDirhnT4LUF\nfW3sNdErm8IAMnp7pNo+xWxlUa7HITVkkhoSPCyytoiwUw5HXBpVHC8TF8gx\noajivWH4bztdfnN1qjtm7L354zGptU9YJS1PoQwDoee9YCgld/CRlGikPrSC\n4EWowR6XEFF3ZZO/SkX3DUgnKeG4Nxa9dpVu4TXZ+PlvLtNFgqRiGro0Mcmh\n1ZTMWMcfsa1nWiwmzARD77g2mG4LCJ+8K2xCQRhC43VbCMsM216j1IAQhaP2\nhli7sXpJV8/olgRRUBUE43H63DijuW6WVioeCThmCjLAQCTdlo5cxzeL/IoE\nnH3T\r\n=L6KU\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.8.4": {"name": "autoprefixer", "version": "9.8.4", "dependencies": {"postcss": "^7.0.32", "colorette": "^1.2.0", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001087", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "736f1012673a70fa3464671d78d41abd54512863", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.4.tgz", "fileCount": 80, "integrity": "sha512-84aYfXlpUe45lvmS+HoAWKCkirI/sw4JK0/bTeeqgHYco3dcsOn0NqdejISjptsYwNji/21dnkDri9PsYKk89A==", "signatures": [{"sig": "MEYCIQC+yp7UDMn2RdSWynWujCa/TSkyotMlXb2D/IRUvF01WQIhAIKVX02JGYNx0LHOsN4c82MEANTzx+NBdx/gKQ2/fK2n", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 348961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJe8sj+CRA9TVsSAnZWagAASWkP/3oKbp8Mw4uD63uWBm0P\n5GSebHOdk5p+bg9kf14aIoW1vhNaNBd45Xk2AOeYME4hMtBoQQn6zk4T+2YK\nYSiVx+Pv+0U++54bdsLBLWPNd05MKO1srxlkSArrTrOAru6BNVbEIiGXZHew\nYu99X5L3KxJ9X2Q6f+BJ4l3jIEwNWRfE7PzHSpoIfqyGx0rMU1rfLw69xGsl\nAz4jRrV5LeSHbNRXSZ/SFJfMlfa1AXJBCkCydnVBI9UDbbvoQHd7hJzhC9rn\noTnlkXBiNCDf3MBbKkG8pDEAG1i1gyYWBf+WLJLLaFO451mJMgegVIhVRIk6\nnx4DFRijS+KIIwcrd31zBSn4gGXK1UVmVVGpkTiu+CqgV+C8S44xc/MWnSmE\n8LKmg/aXCa2VJZ4URJY4bcsHCZXzx0bqALkZRy6kI7/xWRDSWSjx+I9Ctofx\nXXuLbKtz2vZ5yxvBhMLaHH6aWodtqSJHMRva7Z1T1/SGQx8icZ1O19/SJrCK\nB67R7gkotvdVETx7Xxw6smSwuklnNM7l3fIUDGQDvA0NMdPos+MnFwh5tBq7\nL82WPfFfh9QPbZ3l13tvfr09uoWF2fRdXIsPT1LDQ1oRmtgWAkGDdX0vTr59\nL1X9QZwtrYSgSjP7v2jhFzZj2haZ9q891Gc2YMQAKY5HeKeP6wV+TciaBsqh\nOz2p\r\n=81S3\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.8.5": {"name": "autoprefixer", "version": "9.8.5", "dependencies": {"postcss": "^7.0.32", "colorette": "^1.2.0", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001097", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "2c225de229ddafe1d1424c02791d0c3e10ccccaa", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.5.tgz", "fileCount": 80, "integrity": "sha512-C2p5KkumJlsTHoNv9w31NrBRgXhf6eCMteJuHZi2xhkgC+5Vm40MEtCKPhc0qdgAOhox0YPy1SQHTAky05UoKg==", "signatures": [{"sig": "MEUCIE0v8yy9GidytSozp41oTxtje8HLWpQrTIiyxgC4AUOIAiEAxDf66WHXNvMeP+ZmcM13aorza8xP5/vrBNn2Kk3oKYk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349115, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCVG8CRA9TVsSAnZWagAA++4P/A/AETedHF7GjdbrxNnR\n2waYpUoU3+b3hAsNRT6SZaC+IVUtcm8KZv+C5tavNgiWece2VpBt1fQEW/lD\nnr75CG3MDilGH/Q2qNfbTSFTZdtcrovcLn0fjhsJ2LImcGxKagTwAYgnt1m2\nfSFTB/CDImon/JOwSU8U3NJLiK4OerdhAs+Z4bPmLqQjP9ALypSyr+zeeUqd\nRn8zBzllBY9ZDKF9+/fodvZ0ieoZRgX1jtcz0VDnUyFhKIMNIp0j3w/0Z6AR\n1wH2sdnWjjOGxcD9btULGRgxEoBoEShDQK1aTNMb2YSg7tVPnmChVGO8Jk2a\nVGC01lsb1oACN7Hnm1GykwhNEIlyH6egCZGatZpiH19jG0yHbIw7xGckLLoe\nqFg4wHe22UL962ybDfZ1RXkJ0rBu3IFKMEmzna+BK1EgGamteykJdI+C06+o\nig57icw1V8pXzJNwoKJWkI67PKxlQJgk24k7ceLe5nWjj4ob85qS/vxsfiBS\nOO1I+7kVsgwvNqKlYJaR+LIiLS5DfQRPa2hfdlKNrQmv1xda2Y/NNYux9LEx\nu9yajtTD5Tog/YNEMRMCW03zMw8W51J5pDUknTKVX8H8y55UAMZ3T8iHdhV1\nq2jxB1O2nmVRLhAvkvjTQ40y8G449EujNUeWyV8LoRmhxkSEvttb0W8Sj/dl\nBlxC\r\n=/8e9\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "9.8.6": {"name": "autoprefixer", "version": "9.8.6", "dependencies": {"postcss": "^7.0.32", "colorette": "^1.2.1", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001109", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "3b73594ca1bf9266320c5acf1588d74dea74210f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.6.tgz", "fileCount": 80, "integrity": "sha512-XrvP4VVHdRBCdX1S3WXVD8+RyG9qeb1D5Sn1DeLiG2xfSpzellk5k54xbUERJ3M5DggQxes39UGOTP8CFrEGbg==", "signatures": [{"sig": "MEYCIQDrm9j7NIG/4g55VwzfWIQ5ESc89F7gsROWNYLKf6k3oQIhAIyv48k3C6QAku7inEd3hgYFt/cWP8EfZIm4Yuh4kFNe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfIyvuCRA9TVsSAnZWagAAwnoP/ioV7W5g94O/f6zfxkdu\nmKr0pWbq41AEqbWU/wPGoE/6scLnH3kXSmS8fthcFga1iLkM9a90zBLsmQo7\ncv+tDPQbVNNBo0RXYgACa52tLRsP42A4up8WsXQnU4ABoM4uP1zUWeXuWuSX\n+KuoChGFxcx/WxvT30ALzCxXSan2eSYil/01koBgIK62T+SZZbfCase6Gxs0\na+wCya45Bt+dGG5e/5sgoXxv78gJLS7SreUXX/zjg02/28E5aNt3hNGZJo15\nYI9jbWkcSsR19R9/717yu+HVN2Z4D6Fzag5JCNW7Sa64/gpv2qyOCbcJY66X\nG9xEYzxjJt5OOLodr4v0BY7yrUf9pTkaFMXy7vHfZpqP8nraqEDpVqR4m4C3\nVq7qEOH1KxELlWYALwCxigZl84TYMQ8ks6Xol9fMLlQiBP5uozbXJ/Dkl8fk\nutnp1gH3J89Izwgc75ZlyZZamMWtaeWt9gm3LtmlyemoY52leZ+4GWDLJYDS\nXeVIPNlwQuMfRwcmF+6CziSEAXXFJTjatc4eKA20G1A5TeG0NgTmnnZIK1P3\nlJhZSnd58YL3Xh/ivrdsDOX6gaOw+HJcQ+9WCG0qihiHtgL8aKwISBwPtqyT\n+AbGKx8eERrWbjKBMWzB16OEvj2Rgi+l8UPdBLq4XGkoJ3cLzHbesHVr9wAA\n3bcd\r\n=IcNB\r\n-----END PGP SIGNATURE-----\r\n"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "10.0.0": {"name": "autoprefixer", "version": "10.0.0", "dependencies": {"colorette": "^1.2.1", "browserslist": "^4.14.2", "caniuse-lite": "^1.0.30001131", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.0.2"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "4afbe9eb902ffb5d7a9ef6d36013948fdb34601f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.0.0.tgz", "fileCount": 81, "integrity": "sha512-rFlVYthz6Iw0LhEYryiGGyjTGofebWie3ydvtqTCJiwWe+z6y8H35b4cadYbOUcYlP495TNeVktW+ZZqxbPW4Q==", "signatures": [{"sig": "MEQCIDHItJz5y4OJWDLuBoe+mdPV9bvqhZVepbMlZQHV91ZsAiAHtkK7VhhMbpsKQpYVDC8FHscOmiCcScFbfsjUhS/PQw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfYQ54CRA9TVsSAnZWagAAU9wP+wV9G8TSGguBk32MiLgP\nGtWIi52b+uvwVhPAQHamMvvEG7FAn+WmrfXC5Oqj9AIzv2/ELRNbeb/B5qhC\nuByhaLrcGA3uyVB62Nye0SpNbMAO2MXj5uc3sCU4xIA7nhn71bYzk0LwL0iX\nmhzsSa0WT563vT7Z66oLJwAU5eqFTM9+r2M0rv6QJhj0j9HlWmLb/kMcrRJR\nTS0UBqMOEb8ZuSlr/Ox+SIXo4xMg7JyNtk+VKrJQXfGFuIHCLByMvjRNnPJ0\nXEzupO4whKYjHlGZe14nnkApbobjQ4huv0UuWVzf8E09omrMqDBDU5eXoXs2\n7r4nqECxxf2bBL8Oov6tkgSWT/8u0nBBzk1cGIm+2IdccyfN57QB+f2hVUGH\nfsrYvmXQEcNn9cTGjPB/5Noanlp2phsQwtkzsV/OQSeRAonbsEL3eZfAMtwV\nxyt+cgM3RacLTUKQcpQTaossEKl9Pdjiu95IgUnGziylAuHdUXu2IGvrLfQV\nSsOwxdjWjYadX63Vw/QwV/KWPsCMfRDIgO3U57gtof4O8JQXaOqHGFg/8v/h\n+phzNFkq8jwJIPWlpFYA7mlbxDUNDhHgPdxIJipw9UX0+WlxaAd7cND9f3jI\nyfPwCabQNI4mIoEYeyIxPNtbkYeA2sWK7wMg1A/FWZ9JchEava3huaTa7Urp\n0H4r\r\n=eQM8\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "10.0.1": {"name": "autoprefixer", "version": "10.0.1", "dependencies": {"colorette": "^1.2.1", "browserslist": "^4.14.5", "caniuse-lite": "^1.0.30001137", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "e2d9000f84ebd98d77b7bc16f8adb2ff1f7bb946", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.0.1.tgz", "fileCount": 81, "integrity": "sha512-aQo2BDIsoOdemXUAOBpFv4ZQa2DrOtEufarYhtFsK1088Ca0TUwu/aQWf0M3mrILXZ3mTIVn1lR3hPW8acacsw==", "signatures": [{"sig": "MEUCIQDHKVAzM4tnG2abCibpOcb1BQPVpLnjUGRVUs1FtsyeOQIgHPRgd4gPFiUveUd5nV6eAil+mLipwoCVNxnbfBgvygs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245646, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfb+BWCRA9TVsSAnZWagAAbBkP/3eL5zb9DhoKwM9oxfvE\ng+bEeIlILlI5HkTFHq+qSxrKnqtjcGgls6KS8mTiVLZjQmyUBxREzuVWSDJu\n9VYk6zuw4Sy14ouXrg7oGVxoJ0O0OdVp2Hm2/e+5lYHz3VcASsR0b+MFrYfP\nkjXWyBKT9u5QEp1MsOT2DyySJMnSJbRWyxHpNLucO/DWvBRtq51TyNsNshgb\nHj0OF/R3qspLJFMqDYpm3QE4K9OB7Pda6RG2sg4FODjydfMjcXBOS7JowXWF\n0lTEnvVs7/rBC1DKH5VYziGcSgjzsuybPIFMQR3ID632ru4mr9AaNibyKDja\n+Er1mOTqUnn6M/owouILKqiGM9tdBqAxofHYPBMoDrfGmC6PfrDnUsrRBTwq\n9Ak1J6bb1/Mewr7YQLpNUACs7kO0AwM+/9nbCP5h/zJW4kxXPmSaS4rYOG/f\nX5+iFlZqV+Sc+1Z34Oe0TLw76RyKunTXYyJmPqXceBgDBq2IrXSsJ4uef050\nga7rhL7CHb5WigqmXXYfvJu4gPTLFIghjIgRdesuLsdHd2Oz3HBMQVStq0rO\nNOoFkgj+Z9j4QwdNP8CUfK3rv9b/mw7944/ieksqq4u9HPc8eMftUlmLBRfY\nxrlCwgnoMWmpi0LqDmvliAp48cen66QssKk6VPsBwNOGFXqbmkH8ZO/+ps7C\nUM9y\r\n=Ks+Z\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.0.2": {"name": "autoprefixer", "version": "10.0.2", "dependencies": {"colorette": "^1.2.1", "browserslist": "^4.14.7", "caniuse-lite": "^1.0.30001157", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "a79f9a02bfb95c621998776ac0d85f8f855b367e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.0.2.tgz", "fileCount": 81, "integrity": "sha512-okBmu9OMdt6DNEcZmnl0IYVv8Xl/xYWRSnc2OJ9UJEOt1u30opG1B8aLsViqKryBaYv1SKB4f85fOGZs5zYxHQ==", "signatures": [{"sig": "MEYCIQDD1Rw/9gjFSktEZNjVxFREL2GvZTw4jAvRHxDbs1HzfgIhAKSCap8u+u2GzP6y5ZaUVnRZL18tFs5NaZtBBe+LO/Bb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246044, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfqqugCRA9TVsSAnZWagAA08cP/A0EefMc7VLbSYuxfRIk\nqXyIB4s5H/WH6/jtm4KWQeQnvtV4N2pGfBzzYsPgq9v7shc3uDHZyc25rWwh\n6cKsxeXzdUrmFjyZAYbNyw6s89sg/rtsaEj5q58aEH8JF3v/oDhNX6y7xBbk\nCd2SHxpQdn5pmPOKhCvcka/RHsbrtZP62l5ypUyMzlajqzpm5uLaWThiywl9\nVYB7GYNsQUkbqaM2gE1nP9QG+AbX0IF8AZ4Qs+IpcIWn+ZfQQZWWM7NRaHol\ngUKtGEMD/Y8c3AzT9vd7fpU4xmhxXj0Mc9NfneIIXzgO6jfuEmTF/rjil4ep\nQCWhGYDtgHJeFhoaBhS1m0EfanqmSkPLG6hbLbymi1tscV+w+GhTxdKVBGo7\na2onDNFCeYMltTMxpgyEYXm5Rlhw+DbYmSRa3zWFo7xY4K+EZVT1mBqIdIRt\n0fLWCSw3vYm/kfKzfGu+L5sBZz11jGfvKj0kQ+q5Aoog8Z4PS0asYNcYWgtO\nkMdFXwnfYpTPK9VaEzSY+CBuUUQZljTKVCS7UG+IhjTO8gkpcfvYk5ZzCAsW\noFu0tv5I/bfPntS239Tnx6U45hTRDcUh2FYw2gAq+AQNXp5ziWPcIbs9fxYv\n9/1oUqP9A6CBPBaIJiPcLFgHKI6bTq/PFmf6pxH+Nj4WCfLV3DVOWbGjeD2i\n6/CJ\r\n=Zy0i\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.0.3": {"name": "autoprefixer", "version": "10.0.3", "dependencies": {"colorette": "^1.2.1", "browserslist": "^4.14.7", "caniuse-lite": "^1.0.30001161", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "43c8fb60ab095f4b6ff7bc5d24cd05925112f2ce", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.0.3.tgz", "fileCount": 81, "integrity": "sha512-SIUDNQlGARsfL69nsmi7OKnDtz1HVwPAiYPGn1Nv9eLMpBywXK5DNwAnvTq6aTjuPx3XKPl2boCQRYcThjJa2A==", "signatures": [{"sig": "MEUCID2RH3BxDQwBLe+MvieU9ZKXlRyRROOXmIGXdfmxHDmQAiEAlzGQ4ehmL9/oYF8sCDcMOo9UgGV5IwEWzcXq/F4yRb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246135, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwCZjCRA9TVsSAnZWagAAD7AP/RTq2RhXFxfdl9QXJit5\n+sUmMyropBzVSe1O4qFRPmVC3zhpNnTKHqOXUZbVX517AfToHTM04wS2zucC\nS1sx5ehSiTV6PtyyxX6v3CJbYuhe7KePeosGYD7lB0f7YweUApqYRE8czvkF\nUwGURpfJ5BYSD7GJANUH7B/Zzz8g0hhi9Ope6AEdEIDRbU6mMvgzgyx7PJZD\nxfnsmb6lJRL73Pn5kS4d6OxB/pGdQwAOJWd1nhLjgvPRRdjB3k2PRb0Hr828\np5adRl2Z+nl5ESNyjrwqnwHStijW5I/lnA3a6BtgEMJif4sMs18590WGTNWM\nm0hiXmwd9nZvwnj5mJDVYexyPUlWZ+VMi8XEm/ZoUXP96KVw2Au6cCRng7KX\nsj6jQndUchMaPe8VEWOF6+K5DieXfy1bDunmP6iXXnOcTDZIHMvexEOfXitr\nhYW0HpYihl1cfQInoyVNsrHZ/SwIiB9AqvLn+J5BwpQ3/s4hnTytvSLim6Hi\nerCfhEt5CjQ4Y6bxKWjs5m1yG9z07j9AGrq8na4G7FQbf7/SiNjDLrRyJBZW\nnbsNmYybvSKnbOXA1tKicEubSqeqMU2NhbMBlZyddob1h+ZaqFOSPJ/h1DoX\nEj+cxoSrF3eLcph1Tow21EcqcYOkYJdgpYIew+HGiVcmNf8lGpkGQlZMf3vp\nfiEH\r\n=idke\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.0.4": {"name": "autoprefixer", "version": "10.0.4", "dependencies": {"colorette": "^1.2.1", "browserslist": "^4.14.7", "caniuse-lite": "^1.0.30001161", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "f87ac6105d7861e31af794b8ebb1c6d4390d3d55", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.0.4.tgz", "fileCount": 81, "integrity": "sha512-hmjYejN/WTyPP9cdNmiwtwqM8/ACVJPD5ExtwoOceQohNbgnFNiwpL2+U4bXS8aXozBL00WvH6WhqbuHf0Fgfg==", "signatures": [{"sig": "MEUCIQCLsySDVV8jNSB1MXdyvaVUVrBJ2CxlOt3/Ft42leLU6gIgK/QDfnmtkfulHS3lGxwUGa/rLRqzEmnHRHaOXpV3l4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246400, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfwXBSCRA9TVsSAnZWagAAq1MP+QA9izf3WUklcTYIknRi\nCcpcuO8Wn16ck9BNT/2p4TZrqnKzr+POWcMRpZ73SHXAGVKGGZXK8IlTnXnt\ntu8/Qy+A9J8sQ8syRRxJ34mnltWwy2fo/+JSfiMbqCbVs3DKxPVhoHapzxbh\nbFJbxz/3ewLGbxLEExL3rGJ9wkz8TeTLvo285NTOyUuPtycgHMI37l2DebEp\naGhFaC3biYSoN2385uqh8txkxOPRTvWR9umPJomceB2N8pVl0zc0ciK2qhO6\nsi7oQfpJ6T/gwCcOUR7ZmNjzSRSvt20+ky5Iq9/59K2Oheo7WTE8cgUvRxtl\n0cU6V7qOdMEMgPWLdKxG6F2ohP/NtT3/6FK1yKKe8E9KKxAghybaRdi+lIsE\nuz0bsmJiKhxj4duU0VYgqDgeIoD+/u8ZRxevMKxbjWW14qMdOvzy4k0mRXuB\nHBK/SsrsR5RbmkdyycfzM6G1xXMGrNPFStoN5P6Oxyraiiza7PVKhMAWGonn\nuLFr3/hM+ae8XySEDMx1BSUyogpgunnf9J4qLj29ZxrT5/uphC4SZXCcbIP9\niM7dK+IFrX1100+SE8afvPpX5de2fd9SflTJA0X996QVk2kToLm1NdGZhcBQ\n86wm0Hy71ocJCCh8taYoDYKAXhLKFjxM/OtTkWz2Aa4EfxVlW3tNuVIVe/Y0\nvps6\r\n=OOeM\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.1.0": {"name": "autoprefixer", "version": "10.1.0", "dependencies": {"colorette": "^1.2.1", "fraction.js": "^4.0.12", "browserslist": "^4.15.0", "caniuse-lite": "^1.0.30001165", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "b19fd8524edef8c85c9db3bdb0c998de84e172fb", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.1.0.tgz", "fileCount": 81, "integrity": "sha512-0/lBNwN+ZUnb5su18NZo5MBIjDaq6boQKZcxwy86Gip/CmXA2zZqUoFQLCNAGI5P25ZWSP2RWdhDJ8osfKEjoQ==", "signatures": [{"sig": "MEQCICWinTuQ+IZTtg856aFfrE9gxeJfzlnuWNu6BxTINUxaAiAHI/+K2+fuTiuyAyZpY9+ipHR6hEnkxrh/9XmRWXtXMQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 246768, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfzy5zCRA9TVsSAnZWagAA/gEP/A+FtxbSJMuorGMDvrjS\nljqcen0c+4CW/gsculhbZpCQHcYNpM2DW8v+XPnMmaV/yjbuE+gqIS8XeTrR\naYaMy2ixiLZqO4XzzZGzjL84B2saULV5/XOYsgsDWp692jInfnLB6ApvKbz3\nkz3TR7rfW7TLUOHUWyWDJ9z9c4DQLgG8s9fVYqCL61SCB6D1kvg7EncF55dk\nd3jx7KBhdbkk/1Eab2/waLlybEIOH3WMk1QVx1pEPbVtuXmPYoH13oYgE+XD\nWiWkOVb2nj3QmFNz1MW+aAGYJ/34Kq1ezCos2H94s8xqXrzS539OYeptOzYu\nvVr5T6uS8+zicsgESTw8EO1vv8SHDOQbz8NwNQIhvSmIX56F2f2JnFtP8jFT\n5GUUUM0s+a5QYPLoRYQDCPSS0cBxYKDP7iu1K/4mtHm6r5IqbhcOLqaS3wPI\nwrkLXPFJVRDssKyyEAun+3KPYwoOqYGPLpKkzA3SWarCvWyRc/Fc1p16wxhf\n+26Md1hfVstfqyF2bxfqlYNh6ODBKLEYrAyLFRRFAOdXrsTaT8ChGaWIL8Xh\nE3oikt8X1pvpm7Q5eDokz/I5egMGXCwLBBe4C3PY2hy0gYmHybDpm3QK7gtR\nd+9TEcjtRf9tE+mxejvttfz9Yne9FG2Umkd8l6s1K0KC+fERJ8OtetNLJpjq\nYini\r\n=C/W4\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.2.0": {"name": "autoprefixer", "version": "10.2.0", "dependencies": {"colorette": "^1.2.1", "fraction.js": "^4.0.13", "browserslist": "^4.16.1", "caniuse-lite": "^1.0.30001173", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "73c148882ff771b48df9e76eaad88565516eda0c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.2.0.tgz", "fileCount": 83, "integrity": "sha512-5SdPoK2Q3rxVh4xC8GKdy772KbEESdl2SyJADCw5FMUrd5eNtpOTLo7oCFMEKvK86zQdvsIVTYk93E8dAfRy6g==", "signatures": [{"sig": "MEYCIQCPjFJa8CWrusBLbrgg7E1H4yvqwwdMw+ZvG+J2jNd8/QIhAOqfsn4QfuSl80bXrbuizHsW9e6uGiREH1I7JhrINlUB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249539, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf9RyqCRA9TVsSAnZWagAAuoMQAJdBQ3czIUELlr/Fm/Ya\n7zPEUJDLuBoojatGMVaOYIeuspt5LYYwIO0jMF8ESeBaDoD35wUbHptjUPQ0\n6yNqVLflct5/PFBLx8q8KLumAZ/vN8Jei9CjDEmbkAQl9XUzwcuJO4yjfN02\nKtfr29saN0RROvAS3HO3DMx8udncTDAj022th6M/3sYIN2xm4lx0aAogdHBw\nV6M3VTQh3TrxopMiPpeA7hMe4RE6GLbziuVk2gElRsz1fc1Y2d+cFmp6MqFM\n30tgivhPgi6Ikcnc1B4MKgjuKfq3jpi8pgML5oLdWailM1DMqyxJmnrDzF/b\nqFOnI/4SZ/pwuJMcY3ocrip1wZPz9NlEaNlbefbc9tzwg8xERRjHC8z93j/l\n0+eIT6J+h5uuOq3qL8R/prMBVXY133hg0NFJo9LgsNbto91NdBwxNjw1dtkt\n6rpsLm0y1j26BtZKJwrhYlUo4eW25LVCRpmKs5kImcpwETumlLD9nC6hP3yF\n28F4v1QNVAcQY2a/nOfAWUPTlTnWd8jHHtE+HVeCViwDEpTkWuugcZeVqygx\nR9Mjl/dwOa8A6fY2czHI4znl0b8yjXdlfFG37RlqovCBdSzBD1+ipFbm3CyU\nb1ImhDT1/lDp+Qe74RQ8QANueX4zZGdT2VWv2D21mlnXKrKl/lj0ghsfp93P\nwgbI\r\n=+eRE\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.2.1": {"name": "autoprefixer", "version": "10.2.1", "dependencies": {"colorette": "^1.2.1", "fraction.js": "^4.0.13", "browserslist": "^4.16.1", "caniuse-lite": "^1.0.30001173", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "ce96870df6ddd9ba4c9bbba56c54b9ef4b00a962", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.2.1.tgz", "fileCount": 83, "integrity": "sha512-dwP0UjyYvROUvtU+boBx8ff5pPWami1NGTrJs9YUsS/oZVbRAcdNHOOuXSA1fc46tgKqe072cVaKD69rvCc3QQ==", "signatures": [{"sig": "MEYCIQCwJOBsS2PAxWp8aGFCFOVoEnIWz1dKsvTQU1furqgpSAIhANV3Kn1vQLxXhuZk+n2vY0/JVrO3e9WBDTbd/ogFh+5B", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250147, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf97h+CRA9TVsSAnZWagAAricQAIy3q8QU7vCOlqIq+baw\nsgezIqx/cR0Nfw0K8KG63fG1aIUCrSGPPnkzqfQcoRQ+CVHoUqUka3AVs4fO\nUgRYge4/1M9M0K3afpZEnlA7KSNEs0BeLQvgyDeO6OyM3AqPIa25tS19r39M\nYNtBBXEmLCLB12rra4Y5fe85X3YjubdKQjL91NJkrKfH9/ZZOErZ09NzE5aN\nfvjriv8SP9pqtBfcPUtz0gI8cempDpZ+ujT6OSy0TZFaG43IuwKgAu02oavD\np1BxScWmAlONpjAjOeJwuKURkh9hC96GfaG6nq4IhQaybmEXrqrAZc5AHfvF\nTTtafYHIwMmRXP72nm2KtAMQP1W3V4DgzbJot7nO71s1zV9Kzk7fXjzJmZBs\nkAZe/xJWvZyzous6tkIVMjREc34ycgo0Kf9+ZXMlY3AkteeaAZxzFfRLqfUd\ngXdLRyh6w6sJANDQltNKe1Zfomk6SGYx2BL8FQqmYXLgCm9CSXMYRb61XhHE\nd/QTh7/2lr3is1Jx1bnJwxr85pMBz6FoPkXmpfuj7eKKh2ksoeNBqD8IRW/2\n2UMP+EmpCYAS7y9DafcR+IvGXvba1wKwVrh0t+MgdG48B+6xRV9QniucRWvn\nym5wNP+dKx75KTGCjCZT50inl1KG+DOk062EJuN103diW0xrmI5eJv2w6n4O\ncPMg\r\n=c+LF\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.2.2": {"name": "autoprefixer", "version": "10.2.2", "dependencies": {"colorette": "^1.2.1", "fraction.js": "^4.0.13", "browserslist": "^4.16.1", "caniuse-lite": "^1.0.30001178", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "a452e4d2aabe54b9f22b2229a99bb1e594f6d90f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.2.2.tgz", "fileCount": 83, "integrity": "sha512-jOQss7vuYBoPW5dYt4lvhDImjGE1Fvl9NjUCzKI21TB74Gl7CxIRsWgJpOplV7dbtKNVnY97JuwULpIGa6KZ1Q==", "signatures": [{"sig": "MEUCIETnNn1zSbitejzk1IplZ+U3FcyrrrPuvu2Ph3v1mH7xAiEAkzInfDTn+xLVNZOOCrqIQXdSbQzn7awSgVX25tPHS/k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250203, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgBybxCRA9TVsSAnZWagAAYtsP/AuQhCMGqhd3zcHg6W8Z\nfA3Uo3v2kLWppbipbUpLWgh0DkzhnS/TyZGsXcHPFeoT8IqaesWLq7lXo7bP\nLO8/O/wmSTBHe6B+0kezUjz0ZpaYB6rRHPpkPup1F8udmFgpFljSqaZxHB3y\nd84WDde6nO6et7q756tb27qvxBnT0el06psgDlfADLkDLBKGqJGo7A7tBzH2\nxienmJRISMw/8QdBZzv0uuc9vZ2GUM5qKV1gisEKkdg/s8fRt+R+SieoCFiw\n0b3AhhjlvBDr24cT7CU4qrLy+8awC0MI4kVb5l+PTno3BnHkVYNzAOG3uCh/\nwddP12wq7OIHqhnphMg6lp/nzUnI4r6pcEto6uiu4zuexcRPCAluYnDCklNz\nt6CmF1Nakcd2cdOYyHWU6JQA1Hm88MynFTaKYhkEzAmMhb3v5Z0nLamVMvsV\nqOtUmubj1zi+lFCW8U5pm1W5Edcv6X3+4x6uH0RwYtpOM/84vLN9MpFvuGzP\niDLrXT/rv2FMbIv+ImNqorg+EpE00N9MFzml5DU7+wUOWVyaurZTGc0DekuT\n2emmRLmQRJ3/7XivsWx5bFaxHLwl9H8FDnnxt+cvyjUkBq4PL8cg0WGwp4cK\n8HCjbJ+/bGdSw2FL8/ZjlX7megv6dPRFvBF5rRIqyOyPMKfOwEPr1+0InnO2\ns8Ee\r\n=x9th\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.2.3": {"name": "autoprefixer", "version": "10.2.3", "dependencies": {"colorette": "^1.2.1", "fraction.js": "^4.0.13", "browserslist": "^4.16.1", "caniuse-lite": "^1.0.30001178", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "2834b55b75cfc10fa80c66000a66dc94b7136804", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.2.3.tgz", "fileCount": 83, "integrity": "sha512-vlz+iv+EnLkVaTgX8wApfYzmK3LUfK8Z9XAnmflzxMy/+oFuNK8fVGQV79SOpBv4jxk2YQJimw4hXIKZ29570A==", "signatures": [{"sig": "MEQCIEciKY3OeykMw8UavDr/xtPZiPMiYZ95YSIO8df2rMpXAiB+eDRqRA3Wh/5mq8N9cmkEYB+XMyOBUAzz9ITEXqqf/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250293, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgByjsCRA9TVsSAnZWagAAVo0P/3b0XQQJXoBXvCXyYsaI\nY61c1xowdIyqH53o0mXLGzpHrhxfVNA8NgRXSgN1gPhlHIEwr0Z+aZTMxzNo\nGJIFprcFkaC13xbW83ielk/j+g02lJzmhn2OGV945NW8XynN82G7xPBEEvZk\nRJbv++lEqy6fMfai2PF6R3029IOAr1zXGuVALDqt1xBhpZCU2Lb/fYI0WOyQ\n4md/rfExl7XzoMqsU66TDYb6Asi8nDi8p7rlY+rYwKu/V2oYxxw4zD+e8zor\nXal3KDY7xI/mKEQXz48dYGPqlR3XHI4i+ixucr+sz6ALPphCdeU90nSBR3gY\nCwh6O0bI25oXI0+HnEv7D4Yux9X9KQ2Uz7lBVooOCY+k4pYcPk5HoTKkryQc\ng9RwKOkCpM9OSyNAcuTmzXpIaZ/y22/Y1pTFnJTp8l3F/K2dHXkE/v+WMryI\nxkc20q51Wsy2vJSk5edBu9n99Ay0BpRBZ4Im5TX2Fel3xSh60fdph0+OIpbJ\nXZDhEHt1+WhAW3Zzwi0hAL/8OLwf1j2kwH25YG1+FgiU2yI4Od8+hCapsWUz\nurEOYy7QiMYBnskdLSAOowsyofHE1I1uCy0bPrYqkTXv2/VjXyZx3j8PXtrY\naM4zkgaw5wyEVL5nMancv1KOZlVKqhQWR+5oSe17iJnID+swexAMCoJMsr3T\nRWyp\r\n=CxE9\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.2.4": {"name": "autoprefixer", "version": "10.2.4", "dependencies": {"colorette": "^1.2.1", "fraction.js": "^4.0.13", "browserslist": "^4.16.1", "caniuse-lite": "^1.0.30001181", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "c0e7cf24fcc6a1ae5d6250c623f0cb8beef2f7e1", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.2.4.tgz", "fileCount": 83, "integrity": "sha512-DCCdUQiMD+P/as8m3XkeTUkUKuuRqLGcwD0nll7wevhqoJfMRpJlkFd1+MQh1pvupjiQuip42lc/VFvfUTMSKw==", "signatures": [{"sig": "MEQCIBoAR/EqpzTLGGQHu3IMTpe4QYjFXd3s9RP+4Mige7ovAiBOhp/ymB4kAN7Kay4pteeg+Whi5caVc31O864WQHRtBQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFERwCRA9TVsSAnZWagAAWBYP/iYeeRMwr/CXfeQYbPDI\nhQ4Gde2IFMEBPu/JBEdjXqwn1YGXF4Km4n5sjRlHaIps63DbazMARPtOYs/p\nvJMszouHhjGqZ4ExIbXLz4PdJtVrKmUhHGN1PfyAr+v3YWIH+rXr70XFKvtW\nWD+z8FBOjx4GcWYFFulJy4tVbrXT4PCIAe3MfaJclHMpSiwmsyhg2ndpyE68\n8JwdRMqTwnzanVp34ZMWXC+2Mj68jrUWXTdIVj47Na/6LS7Zv3MBs1blFYVB\nA422uutXo6kUgeMePuUDEvyHqpYJusU9iI5u4S+VfQuKrkWZxPkasIJ6FJaH\nt4NTJCYAhGFs3vTp1hikY9rdq08tYbwXuFkYwfzetVHuP0U3o/Gi9mM1R4Ce\nBIRB0har6/ItIYRw5Akmh1qYo5Pnu5aNAtgR3tC9J/2yxqrtqmUTmjj6eUJc\nOIHdMqNjfFb16DZHNn4QIijU9vfMQAuIrxyJutiT1FZutOpaDwHRNsBIcu+Y\nctUhUPLlUBrZfkBO12KwQFLjSOmLEB3fmSNX8vKQ2QNOXe3T+1N3DhLh2QOc\n3HPFzGt5Jn/Ikg8M2/0BKslgbJewOrcQcfQhDB8YOgLRBgUvE7eeHHVQ2d/n\nACkVtQQXuQbQVNZZ3efs32jpBQIqLSVy+ZptTfX/drdvCQ8g0TxfqHPrPoq3\naWlx\r\n=TTR2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.2.5": {"name": "autoprefixer", "version": "10.2.5", "dependencies": {"colorette": "^1.2.2", "fraction.js": "^4.0.13", "browserslist": "^4.16.3", "caniuse-lite": "^1.0.30001196", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "096a0337dbc96c0873526d7fef5de4428d05382d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.2.5.tgz", "fileCount": 83, "integrity": "sha512-7H4AJZXvSsn62SqZyJCP+1AWwOuoYpUfK6ot9vm0e87XD6mT8lDywc9D9OTJPMULyGcvmIxzTAMeG2Cc+YX+fA==", "signatures": [{"sig": "MEUCIQCqerWs2ZtHSCdJ/YHwLNyhY1Sim/bkVydplPebkjdtxQIgZviGu57DXJfXmKfjizheIf0u0f0e3P+3odXPFSMpyvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250719, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQkN1CRA9TVsSAnZWagAAoxcP/A8TdDg9gDtMvbuvIHL+\nOGxq3nqo0o1Hh3q8/7wHU3NRuwbeWjIN5L7N/Ii2zB02Woyil5Cul9iOCCDX\nBMWk55kQSa1vIT99SAbbjyUmmO/sCV0J59Pdm5GmxUaYYDoT94a1qjccqgR6\nyPC0MkVxa8rLeNtM52kkxinZX0b5U4GbuEFqBkMhf37U4nYMncubwYHbf09E\nXsssz9aVivUNa8VRMiJUiQcPuToZep6ss+jSqZWyiF9b0IgWFsHopKDMWY1A\nODvTF4F7JYYrWiRrbSlf9ZIMYSTwL54J16LQEjfiSrRXI70hfj44181JQdR5\nt6TUa8vi2kqSLU6Fbje7HJTjbMiKEnORCRQGHMPeSDiISB9xQdp2DmBqGuoR\nhXVF6SE2ql7q9h5eCuf1Z2YS6X1UqqK2IzocBR0K/EdU3R3URcd32p6ofaml\nn1K0RhK9/DNGQofFPm1/vo8kdAEVdGdktgg4xgtd2FfdEkQeYYJNsJzAbosl\n+d6/4sT2DQkfzdRZGfIAG1hYSmw6Q41Yv5d0OepqSBwC7ELNCi932jTlepSq\n38t8aMP4AqQxgXqWfissiG/NcqDSAP2e5lmi2s7lP0iwhf4asbTe9wIW0dty\nbXK+SJuRw9hb0pwlqUTdhwxpGcbhdz+kzTu/eqlo7r04UmPuZ6KAu3wox9F4\nTHq0\r\n=CCRD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.2.6": {"name": "autoprefixer", "version": "10.2.6", "dependencies": {"colorette": "^1.2.2", "fraction.js": "^4.1.1", "browserslist": "^4.16.6", "caniuse-lite": "^1.0.30001230", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "aadd9ec34e1c98d403e01950038049f0eb252949", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.2.6.tgz", "fileCount": 82, "integrity": "sha512-8lChSmdU6dCNMCQopIf4Pe5kipkAGj/fvTMslCsih0uHpOrXOPUEVOmYMMqmw3cekQkSD7EhIeuYl5y0BLdKqg==", "signatures": [{"sig": "MEYCIQDTLnayQqOYgU4daNcuPYJEz5fdniqMj5eMgPW2mwccxwIhAN8EbMABrHpvr/BAxMVpYTelVkcphgFCCSMuk4C6JOsX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 223958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgrlvrCRA9TVsSAnZWagAAmicP/1D4xUur9tuUCZls0MS4\nRkpaBM7rDnONmKoc1MxUgYTdlZiNB7advO9sfNTS9VD+ucjvZPFh6qwqln+m\nduPaWWsgb9DhhB9Z2STXaAlyOb6vU/sDjt6IibRQ9OLUIT5z84iWIEDvf27Y\nYHwmJYmdsNlHRVmO0xoi0vbOKP4x8I4M44H0inj/nVVopKFj+Tyj0S18zxht\nIbteENKyBfXz03NcyzFzv4SC1LLbnKEGbhR52znc7TqKlV0d+XSz7Qo2n7KQ\nK+hq3hvg0OUsNEZn65GGQMMo5H64CLXtc1IZHCjqM//Wcf2pO+nrgPTGlADE\nUQsuOq20k/kaBvTCtf2PCfUbHbCiDCjS7N+1zILxbBgpCg/eYZum6SylOokf\nc3cNLJD1ix6Xq4xflUhdt6sszOlHa+tALH7eDSK0b3oxpJ6KlCVLjl999Cl4\nrgz/P9WMevqUh3ntdC3/FtMCfB5JX6JZAP4teQ80L4kt7eAxAl146UmJMrgW\nsNhob7H/dunZiGc1owfyjY6PuBQpe2yufdymQvdzsVXZma7lcG1qc60VyNrN\nHi+zsrRG4I2+1ZaTeTTB6//L2ulI9om9Kk7tSJ6ew/S9+vaTwI8EwMQZJN71\nh4MtGz28K6770Td7RltAXnAwl5Ym35mTYJJJY1CG47g658XGte7XydrpsFbe\npCiV\r\n=EEHW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.3.0": {"name": "autoprefixer", "version": "10.3.0", "dependencies": {"colorette": "^1.2.2", "fraction.js": "^4.1.1", "browserslist": "^4.16.6", "caniuse-lite": "^1.0.30001243", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "c60803dce9268f7fe0a5e5c1fe48a74356d7b864", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.0.tgz", "fileCount": 83, "integrity": "sha512-BzVzdjs47nT3MphTddr8eSsPVEIUCF96X6iC8V5iEB8RtxrU+ybtdhHV5rsqRqOsoyh/acQaYs7YupHPUECgmg==", "signatures": [{"sig": "MEQCIBK61Rsecj/W9O5Fv3jZMmIIBEUQI330lPcJXATR9GecAiAnITarmzH4Tu3NIPIUjvyg32nV30O7x9DxoAstlMy3aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224648, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg6YFQCRA9TVsSAnZWagAA1NoQAJgX7qztD2ADQIagGQY2\ntaBchLttE3zOMRtwcN+6mNGgm3XRjlOOT/ysUmDxVxKz/UbYQ91s0gpIYHZO\noSPDCAdyVmndyErdir/xJGJdNN2eVgQb01lW/WRnMU+uzHl3xwoHXEwY1lhs\nERJDGS/8tCK7QwfHRA1+w4rH3zsOKIx1TVzGsk0ZfbbDi5utOIjKbgfKnI3h\n0v9uNuVfpPR1R+dFFiZld3UMH2SF+IATaGHrU/kCAcf9bPTAg+ki9aWc3Lbh\nnrlGRVRO/EZe3QCrl2VXDD3c1Dyi61GIiTePaGZ41g7kQhrN1/6Q0in0z+Ce\nlW84ogRn02m3GGKJ/vzJAwHVkPIn1yw1goD5J0vDBq55GLi+usU/wHXaA6TQ\neWeXDmsJzBaMm7hXpzW8XU2c57wkX3F8zHFr6DShHeaOGqsDldrBpvgqTGXD\nVWTncmC0ei5PBJp2wrwGX4q0rmfRpeZ/0EAs2Q1XpTb6LOKqUXqZr01fgV5U\n9bbjNEF1haIXq+Q8RHBYgs2Dt/DSYY4HZ5a6boeewjKAywcZnimguMxefoTq\nCr6ZQPaA3c0o3h27ZmROKXz68ndb1k7M30eqnd3PcfD4KZKdPvuPtVcDfH4j\ns1nlYWFA83kTz9ito9b9hoL/XAseHAPjthKH6iO+oD4rapbd7qjiPL7lqu3+\nlvs5\r\n=KDh2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.3.1": {"name": "autoprefixer", "version": "10.3.1", "dependencies": {"colorette": "^1.2.2", "fraction.js": "^4.1.1", "browserslist": "^4.16.6", "caniuse-lite": "^1.0.30001243", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "954214821d3aa06692406c6a0a9e9d401eafbed2", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.1.tgz", "fileCount": 83, "integrity": "sha512-L8AmtKzdiRyYg7BUXJTzigmhbQRCXFKz6SA1Lqo0+AR2FBbQ4aTAPFSDlOutnFkjhiz8my4agGXog1xlMjPJ6A==", "signatures": [{"sig": "MEUCIQCJBRgLjcvjiIhfw4rvpP+0HV76yAnES7DSvZkKIhsBwQIgclWySDNBH8ZrrNZvB22gRs3QIvD5lYtvjc24m5uWMHg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224731, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg7HwyCRA9TVsSAnZWagAA9xgP/2EpyILC+DuNlu0FfzlD\njpFgnXJutQBQuAvNmbhqW0FDGP5x+jFtrSEhcjOcT/1qhlxq6GowylhJ/IEZ\nFxRu92aHqRGAaFHsQKs3XVrfm2vBcIcopBqYE6ESDvbrQjuC9fQPzRlMKn5v\n4u0GGA00zQ9yS4AY78v0MdNhYUEmqpLpAU7e52KP90Zsjn9R0GKASEQEkk3p\nnqDqfUpQRJkemE5Qysl6xYB3kqgezaWUt5cLj8D8w/Z+nc8WtpD+ab2krr4a\nrkDxdNhzqPLnC5xZ2HpYyGg8I+ZEA1u0IpRtrGU7saMkOglI+QCsagPoNe3C\n1sJj/jChn0grpWQqtJvYYG/hx0ieMVS2bq2wqt+EIsKgWL1lhzJDOMBwYzBn\nYH89pOuGgGZtOefC/+FqGGsU3x8jpujCMpJZJOfM4oGZx7ZnEMtNBeJj+jT5\ngn4I7LoUh5ipN6Z9ciuYWFnWDG4E+tTjxP80XlNat6qMuTJkHXZItJrtn7hV\n7aMN+5D1USLnu6XE/Pa01CnmD41xh+z/4TtIxCBLlx3Vy8w+HdcBXbvcrCYN\nkeFxMKQ6zhvqeqMVgcZh4xaotCULVDnbAqnu8GKS6WiPWiGnogZbYRpw+2HE\naDioTN6eMMyaSwynw578/zflv7VkDLVPjCrwk4q7bTrrYxEWOrxPBpscW/+F\np0Ve\r\n=XR2/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.3.2": {"name": "autoprefixer", "version": "10.3.2", "dependencies": {"colorette": "^1.3.0", "fraction.js": "^4.1.1", "browserslist": "^4.16.8", "caniuse-lite": "^1.0.30001251", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "836e4b4f59eb6876c41012c1c937be74035f3ec8", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.2.tgz", "fileCount": 83, "integrity": "sha512-RHKq0YCvhxAn9987n0Gl6lkzLd39UKwCkUPMFE0cHhxU0SvcTjBxWG/CtkZ4/HvbqK9U5V8j03nAcGBlX3er/Q==", "signatures": [{"sig": "MEYCIQDK2z+dW2yD2GYIok8FQ5Ge73aXdhIgBPBo7wCSZwB1owIhANa7U33DqI5sYTAbghRSntjOR+bPYZkspOzvHjG8i4KQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224740, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhIUN9CRA9TVsSAnZWagAACdoP/i96HTqqLi9CKytU8fNe\nwjDDKVWotcNvvL8EsvxvGMmXyeZevW4jS00fsGLWmAxf+UtH7Z0IYmFwb7gJ\n15DaXMWFHa8/3xG02HAz/VRJTpxj/vdzBy+dyvFNyV1Auc5KuhOAhjhZXFYi\nGO72j+0AZpuBBiZbJodXRsU5k/O5Qh9mHGOzN1e7GuEQBoEAxzzg2VVHW0iV\ndJ6lIb/fLU6Ckvl2DwF3I0aBenFDKyuM9wuS5UW75fJdSMRB0A/89IXqRMn0\n9c8r5V4TFj7nPlaYY+l5eJTZIeoeTm+BtF6rtDnj0fB5dsN0a/6ZvPMnnA05\nsRKC/OI7DsLHAkn38V8QU+ztLYodxNKMb5YHNSagR+6WhOQ8twbhyjZx3gkM\nqbX/OxTdqoSzJAKSmSZU1yOb/rfEniX0XiWY7fBqbGcbCvJOZeUKEjE7K+aL\nG6M+KjqXwfkopwGzwFRZTb8KWNVbtJ5AOKQZlZzU74yWNKp0x/kxj/10VwM0\nqDjvOrL+2pDxgNKinMJkvDaG02BbDhL2xGeWDi95iNK1ZtqY4uQD8+7WF6ig\n5POls4TrQD3S7D77WhdldMV0Pa6NnwxOb6jWXeweyfEr3/jwdB75DVdEnNCB\no42+oVJeJg8w423P7QNIfUIMe2OIiZjAvNgH2H7GyvkT7bwCgqJENwRy4ffT\nTvjW\r\n=Tyop\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.3.3": {"name": "autoprefixer", "version": "10.3.3", "dependencies": {"colorette": "^1.3.0", "fraction.js": "^4.1.1", "browserslist": "^4.16.8", "caniuse-lite": "^1.0.30001252", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "4bac89c74ef98e6a40fe1c5b76c0d1c91db153ce", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.3.tgz", "fileCount": 83, "integrity": "sha512-yRzjxfnggrP/+qVHlUuZz5FZzEbkT+Yt0/Df6ScEMnbbZBLzYB2W0KLxoQCW+THm1SpOsM1ZPcTHAwuvmibIsQ==", "signatures": [{"sig": "MEQCIHq6p3unE8ms3fdYqgBYLbaioX15nCJAMfO56ciuNMN1AiBLAwcGxwDPmRRrGghOPsexouB7d8XESskrcN1rEVpXNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhJ7FbCRA9TVsSAnZWagAAiSkP/RCBsOyXn9QJNywS7SaE\nuRR7oLrBvNd6jz0JVCie2EuzYuegeHibqRvqu1h4gX4b2lICayv0ajPbdo7z\n5naHtqRTs1KwgdsINYzkRNLRwpcBSUdLMQjrzjZL3ad5oYezJGwA+Gd1YJk3\n/O9VV6Q60YE20FIZY/1KrzkaAZDYSLBPFnOi6x73YjQURpK/UXjZADIb4IB9\nV0WL8HqqTLtni+UMG5QA5SELHHwn4sp9m907meqEzyNGXfPUmGJI7jdLhDNx\npURh3J4/9rye231k6pwT76w2ThaXei+ynbiQGS+vqd0jIhXKJLCKRxUXgcFQ\nsjn01VeHj8CUrS9YXgRcVvyicUN6hxkttXX9Rx1Ka/3e8322ASEKmngsEz+1\nkFKply8yZrgLckZVUrYagOdTr9Ug5WLKty8NrWhPB4O2Yu63f7d3OKWCwaot\nxWSLmNxW79ma8nwfkZH5u5NG+XLdGURmilAsMIaKwPxbC3Z0gZkZbt0RIx2a\nGqwPAA6Pjki1Zx1laYUPfIroFo5utpNHFfgsqb3GtEyGuWYECCgQPjyRt9Oj\ne1cp7GBsVfARAarfjJ3QRAuiIRqqp9fQ38fF+fiQ9bcd4cgSaPQainYpR0JO\nGwiQVS1cJboqfa7cMkf78o96/xrErOlX2COfk9z6U+bw51fnwX05b8IWRDnm\nkeCt\r\n=p7MK\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.3.4": {"name": "autoprefixer", "version": "10.3.4", "dependencies": {"colorette": "^1.3.0", "fraction.js": "^4.1.1", "browserslist": "^4.16.8", "caniuse-lite": "^1.0.30001252", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "29efe5d19f51c281953178ddb5b84c5f1ca24c86", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.4.tgz", "fileCount": 83, "integrity": "sha512-EKjKDXOq7ug+jagLzmnoTRpTT0q1KVzEJqrJd0hCBa7FiG0WbFOBCcJCy2QkW1OckpO3qgttA1aWjVbeIPAecw==", "signatures": [{"sig": "MEUCIASjxeesC2LFKPjO74HEaxM1+XnT2UZFt2FR6030c4DUAiEAqP0tK3uPf2uAQYdIKoU01YYsYYT48NzZcZ0wf8h2eTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 225131, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhMUR5CRA9TVsSAnZWagAAv2MQAJEkK1y+cw3kbx8OaXi2\nc8mDl+0SZUNfwiMTdHCi+/8UaLKEj+nqaalV5nHzIoyi8RU+K8iUzqCRYTG0\n1AgSB7YrDEyVD+wvXiXCrEU/jC1dk6NRvzkb9PouXvTzro1cVMS72WjF09lc\npE2EwVlbQWiQmIC/IoPVbPBg22l6U/W0waD+DSXGpaWpSXJ6oUn2bqOL+0/W\nbZJ5yvL0/lFJaHkRiTcpk2D3XtDuNQuE3dtb040vG4y07VqUulUy5NCm/nOo\nZ9nAW4Txo/2Xy220s1KFR5pAct4CAB2zNh11I22/rq8Ws6Nrrjm8r1B1UcES\ntGMxdviZBNoq+Dnw2BdJBIobGnE5+4aNAyeVv+V9upyuHxK9vEBzZ1SQbTiR\ndWiGUStWeYo9AIRkd/eYpNaX8uypyX9SvqWwqr2FHFWFNCXZFwPIbIlP+0wn\nu4KWERsY7gp5PZpz08B7B3i2FihVlXwWu+hziKtR2WRGjXZT+ug+D6HS6P9A\nfSKrzQTEZajaQH5BWFXKEhZnPNFG2Q+yLZ+TEhcqO4nUf5uPVG7ZqZ8qugdr\nm0+EhjUsJg1iYzHCq0zu54nIhJbVjciXSNQgM8vz+ddoPb/4Sdo/kbACTZ3C\nuInN+HyH5elu8gaWixircA6/j/vYdSikbj1jpm+Hn/PH2iLasoQuETcOVijS\n8lne\r\n=a8ml\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.3.5": {"name": "autoprefixer", "version": "10.3.5", "dependencies": {"nanocolors": "^0.1.5", "fraction.js": "^4.1.1", "browserslist": "^4.17.1", "caniuse-lite": "^1.0.30001259", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "762e6c13e30c5a0e650bf81d9ffd5713f1c8f344", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.5.tgz", "fileCount": 83, "integrity": "sha512-2H5kQSsyoOMdIehTzIt/sC9ZDIgWqlkG/dbevm9B9xQZ1TDPBHpNUDW5ENqqQQzuaBWEo75JkV0LJe+o5Lnr5g==", "signatures": [{"sig": "MEUCIAu9rUrYIWFpZNetCW2QRYd47nGmlLsLKOykJprwRJu6AiEAxT8lxrLOfTv9lIGko+CVSQEqa2kxn/KVQ/9tU7stX98=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 224946}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "9.8.7": {"name": "autoprefixer", "version": "9.8.7", "dependencies": {"postcss": "^7.0.32", "nanocolors": "^0.2.8", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001109", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "e3c12de18a800af1a1a8155fbc01dc7de29ea184", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.7.tgz", "fileCount": 79, "integrity": "sha512-7Hg99B1eTH5+LgmUBUSmov1Z3bsggQJS7v3IMGo6wcScnbRuvtMc871J9J+4bSbIqa9LSX/zypFXJ8sXHpMJeQ==", "signatures": [{"sig": "MEUCIGp9NYygwvsTfNE65b3QZI4RiDyyOjt4Edkk7G5wX7mrAiEA7XId07ah0MEPCclnuozRTSGNh3J9TYdX4x35xmKrVvE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 318890}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "10.3.6": {"name": "autoprefixer", "version": "10.3.6", "dependencies": {"nanocolors": "^0.2.8", "fraction.js": "^4.1.1", "browserslist": "^4.17.1", "caniuse-lite": "^1.0.30001260", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "537c8a046e32ec46bfe528bcc9e2a5f2d87cd4c4", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.6.tgz", "fileCount": 83, "integrity": "sha512-3bDjTfF0MfZntwVCSd18XAT2Zndufh3Mep+mafbzdIQEeWbncVRUVDjH8/EPANV9Hq40seJ24QcYAyhUsFz7gQ==", "signatures": [{"sig": "MEUCIQD6qY5NPeAb+yqLCbe9m1F2zoFZjaHGJun6uADp2Dud/AIgFTkVwrCFZxVRgTM/YgnhVGhGsy9JCvGHzH3IyYVrgkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195386}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "9.8.8": {"name": "autoprefixer", "version": "9.8.8", "dependencies": {"postcss": "^7.0.32", "picocolors": "^0.2.1", "browserslist": "^4.12.0", "caniuse-lite": "^1.0.30001109", "num2fraction": "^1.2.2", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "fd4bd4595385fa6f06599de749a4d5f7a474957a", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-9.8.8.tgz", "fileCount": 79, "integrity": "sha512-eM9d/swFopRt5gdJ7jrpCwgvEMIayITpojhkkSMRsFHYuH5bkSQ4p/9qTEHtmNudUZh22Tehu7I6CxAW0IXTKA==", "signatures": [{"sig": "MEUCICF3wf18E9bJsvS0wsarN4K15RMcnraK0H29/gJzkwygAiEA42+6a4KS/hNczNcuI6uMDLqMVG4VWGUbW5MGiUrNpBM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 289279}, "funding": {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}}, "10.3.7": {"name": "autoprefixer", "version": "10.3.7", "dependencies": {"picocolors": "^0.2.1", "fraction.js": "^4.1.1", "browserslist": "^4.17.3", "caniuse-lite": "^1.0.30001264", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "cef2562058406bd378c94aacda36bb46a97b3186", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.3.7.tgz", "fileCount": 82, "integrity": "sha512-EmGpu0nnQVmMhX8ROoJ7Mx8mKYPlcUHuxkwrRYEYMz85lu7H09v8w6R1P0JPdn/hKU32GjpLBFEOuIlDWCRWvg==", "signatures": [{"sig": "MEYCIQDIBiLdiLjO7qWI6cRwdrLuOylZH4XC2o+gbHJ+gWfsNQIhAI6NxhNmICC3ciWQ9IWjhj7egB+IumGaOsonea21JdmJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 195292}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.4.0": {"name": "autoprefixer", "version": "10.4.0", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.1.1", "browserslist": "^4.17.5", "caniuse-lite": "^1.0.30001272", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.1.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "c3577eb32a1079a440ec253e404eaf1eb21388c8", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.0.tgz", "fileCount": 83, "integrity": "sha512-7FdJ1ONtwzV1G43GDD0kpVMn/qbiNqyOPMFTX5nRffI+7vgWoFEc6DcXOxHJxrWNDXrZh18eDsZjvZGUljSRGA==", "signatures": [{"sig": "MEUCIEJGc8lmGxjaIbYFAiFJBFqBaIC//hNrkaz9qXjT/XNdAiEAoDhjvokspKoDwqtxjFbQP5WnzabF/l7SA9Uy+DK/Mnc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196164}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.4.1": {"name": "autoprefixer", "version": "10.4.1", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.1.2", "browserslist": "^4.19.1", "caniuse-lite": "^1.0.30001294", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "1735959d6462420569bc42408016acbc56861c12", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.1.tgz", "fileCount": 83, "integrity": "sha512-B3ZEG7wtzXDRCEFsan7HmR2AeNsxdJB0+sEC0Hc5/c2NbhJqPwuZm+tn233GBVw82L+6CtD6IPSfVruwKjfV3A==", "signatures": [{"sig": "MEUCIQDDginG9tT7vrUCt64ukUMK8Lf3vgENeOE5jh6gEHtlHwIgc4azhUj64t1P8qJ1B7c3ul5+RfqMQ+BEEKryxlKG+Yk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196102, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhzIsRCRA9TVsSAnZWagAA1yQQAJrUZvf8OsaCnQZp6rlx\nWl52wM4O2E483wD5D93/prB0ggMtPniTd2cmJL0ikcZXMslHYjd/83j1Tf/i\nYvfpWX9gHxYhrdq/GjMnqAu3HFeuw3wtXB+Wn7WYMx3lhalAeh31lvmO6UfI\nJRR6QPb5exn7HWQvPRP+XqnH8TkCKeQp4Y8wD32d7EVGqPE2HI6QPCo5242W\nYvNI2pouC2g8h2HatLzEiGuKt+gqlMSPXFygdik581i0y1sXhtuLN4Uj9a3a\nE0Zi4v7HEN3CNeUHZttiRPu4Ex5I+VLJsntUZkMwkNuzJwhgyZPpN9nsgVRU\nLusq/81XMcF9FQlFcs0Y0l1fcIIs28BNHAA8ZIUxjOx5vz0jPzAyWLlSFLYu\nZg8xX0jXbnrk2Ovy9fi0ThoLksT1FiBSQcj1g4230LMHskQDyo/7Pu/WdPB2\n0ZDjGaf3rp3fHzuWC8mmkjUwCKcf5duO9l4qXAooMoiDDUH8F6B0IivpOMOZ\nZ0E5CtRX5tkpWzUi0ZbtZ/NjP83kVehxi/cPDJdhLqxkvDhK+hOXvRZj7tx2\nAxrKeb/7DA7wP6OG1GqoiM/ZFXYcv9WdCyEgg9/r1sAtkRBXyH8JjiD43QO3\nBo2gl8A4a5IyhWT6L5cy7DQ8kFG82u8vRP0Lojgyf2ng8DAvr5yJ/z6/HU7F\n1smV\r\n=syX2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.4.2": {"name": "autoprefixer", "version": "10.4.2", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.1.2", "browserslist": "^4.19.1", "caniuse-lite": "^1.0.30001297", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "25e1df09a31a9fba5c40b578936b90d35c9d4d3b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.2.tgz", "fileCount": 83, "integrity": "sha512-9fOPpHKuDW1w/0EKfRmVnxTDt8166MAnLI3mgZ1JCnhNtYWxcJ6Ud5CO/AVOZi/AvFa8DY9RTy3h3+tFBlrrdQ==", "signatures": [{"sig": "MEUCIBJhHTwKydmxTKfRXbH40PSaTmh7GVe9GNc14D53jwMoAiEApcbtzmRLaXTkjR7ZIROwMvf4MbNrHNkworVV5OYPUek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196176, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2ELECRA9TVsSAnZWagAA4G4P/1W+paMBaIJ84M9MmtGJ\n8HeHVacE9pcuwiFcxHzrkFekP84UdRYpxmdWmsBL3nOISz/gLqy2z/S7HUzP\ngbQmI36cQ02xx2GT7mRDsngUIPdsZ00/0ylyzXVqO1vEOmBbT5UyZlBIitgk\nwrBtL0TvAMx3KOHoQNsWVPUEIcLqqX2CFn32N07TGbp5fvvnrjvjzOHy5Ywp\nrOJStmFC0co9WfIqFRRopyh7k0PjUT2UQ4WaSG4erAqc5/6KlpRC41Pm4+CL\nNVHc2h/ENJ1CpfPPG6KbrI91uQSXlRmBifqedE9ERIsJYmKdKH48sGLrqNzB\nCncV3N8UTA1EsSisou8qQs7RiMp1PmkHAXeJpSNS5lCTxfr9Eb5+BcpJ7arm\nE/Nmi5rcfFdQkK4F3JyXS13Ctcwesm7KZ+Ow9l7yM+ESwvp/mdmzBgUy89zU\nSbP/enCNDTdYlv1EoJB44uYC8HvrWUfuHP38d4Qkv5vHbDMiVZA9CPV8Umhx\n2EwftM6KWlAm5VzUXjcPGpchUbp/ICzyafGn058k1IKwbuoV6+CcQXh+7E3d\nazbBOFWo5wkyG2JTb2L4rZ1+n4y7RjinTYugvBM2RA0AUGhIGYF1y9hGzvb9\n7IAcfzex/AXhXz9WMcDsf6aRl+N4rDkiC8PP+T1rJVeG6rmu+KRI7a9s/ovt\nXDiJ\r\n=oXr3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": {"url": "https://opencollective.com/postcss/", "type": "opencollective"}}, "10.4.3": {"name": "autoprefixer", "version": "10.4.3", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.20.2", "caniuse-lite": "^1.0.30001317", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "d4b9358be607c30d1900c1412d0d668f98bb5a6a", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.3.tgz", "fileCount": 83, "integrity": "sha512-3EIK6tHl2SyJWCoPsQzL3NEqKOdjCWbPzoOInjVAQYo/y/OCEFG9KwB5162dehG5GadiGfgxu7nrWCpExCfRFQ==", "signatures": [{"sig": "MEUCIDWsURCCdv2tEQFQOPCA4AwRZD/HvnyuHwjbU3iVO7jSAiEA2awFgGXHWJErmHIem/+CoBGV+9a6jL/VfAd0FC6Ngsc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196298, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMNsYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0QA//ZePI+Qxx3QgGXxstZdioJEOsT3uACtP3RJ9QVvWt+JGCMmAE\r\noXCCQeMk3NNbJJLtzsLvD1jIGVqV6p/I5wM804xpSZHtcqzz9RQLkpylzXnt\r\n5jfqNWcKvKcKs6cRe1NQ3j40oAqiSZ9mbzuVs3aQa/Pdn5hlVhJ5TaPzo8Nb\r\n3lXydbqqSDr1ZbJfGCsstU6/lXqtCJ/Zqi87X/giyHmE0wPCZahIotklSn7I\r\nqRyR+D5ZKtGawOsh1MQIbL404KOZvBAOYiU87Ja+6MtmIi7LyR4RKsMlIL+4\r\nTaWqBoHXTfDjt3dQDHRJRxoyoz/DiGMdJglI03sUSVzJCjDnR3xT3EJg6qYX\r\n8Mv8EAlzRda0kok1kA982mynM2OHisTKneQQKBS2Kzz3KYBSQzdK0+n3saEv\r\nt6R+K2ijdLp/4Zch4KQhHClqA840QeyxcDsa0qwEVTAG1slBG63TdnYucYTj\r\nXzDH4CPsPKeISFwajAR37x0OmC5qA43D51ldAp82C/DmOqPtZ8h8K06OiQRc\r\nUTHUNRatIkZACGZ8rlppgxYmCYNAiufV59Yeexaw8edu7WdU/o4AIvF4+sSs\r\nFHZBrJZpzmNFTyGCZHI61UBWadx/DgMKiZehmxsshLUviXqXhrClRZ/0bwnI\r\ncf/ceDVcb1QMElUlX9Eb45kYXtMwvNjfac8=\r\n=5vkW\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.4": {"name": "autoprefixer", "version": "10.4.4", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.20.2", "caniuse-lite": "^1.0.30001317", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "3e85a245b32da876a893d3ac2ea19f01e7ea5a1e", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.4.tgz", "fileCount": 83, "integrity": "sha512-Tm8JxsB286VweiZ5F0anmbyGiNI3v3wGv3mz9W+cxEDYB/6jbnj6GM9H9mK3wIL8ftgl+C07Lcwb8PG5PCCPzA==", "signatures": [{"sig": "MEUCIGfQJY7FLbQ1DIwy/VGFeDuk2i6k9QV8++eOoj33U+C8AiEAsk/7vYplr899xBfpM8KS39djsTZ31rmgSVuV1S5lDTs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196299, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiMaJoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpstg//TisTH5VsavMoipr36MwKCJSgI2O4kCFzBdka7FScWQ7TuPCs\r\nCP8qdvkCvXzcf52KHAXaPOqv2NzXkj/4UtgP2Xew3K9UuRs2miDvzFQV1wl4\r\nJSfqWBpJrg8PbCeuE/hysFUOrIjn+05yhFojKbv/f6MC1XaAbUDL7uIQTo+Y\r\nfDmXuh2gFHC7IEGGjRCHDEP6yTOfx31QTzYuiVhRYDNrVCH4QvD5GdoOjP8C\r\nvHP2TuFEGl5jEEHrBmBkBsjMvLDoIXJYmCNlzoyPoWPpTeKTzDjiUGnoxAIX\r\n78jgbu6jIunscEDZ6Is8czhz58dt+NmoPcCZUz28xWOKYy9H1jQXRR4McNq5\r\nwQ6vVhyrdZKNpuJ+GyZY7qBsTuUL0FbA3ThuXaWO1czDltraegMdJuFQQwxS\r\nhchahANebyF6qWCf7hCsPh4I+NdYcn53cdoT7btXn9cHTp6eT5x4RJlCnZb3\r\n3xgc9MDFR3msF/M8vI/rWhVN6d6nk4eIbBOFTntwDgs/6dC3ij63JPFcs2Iy\r\n07XYK4mTewgTqBcf//QuAow05h0Dag87ayVTkPKr5HKZOpcEL6WD3+lis2Y1\r\nxSrSMQaOYURiiMvNqBbaQdnBPg1c3TtrU4uCFdvveXpvn2dvni189c3gEAxT\r\nIjlkjc0z5v0Mx0q6PqxcpoNduP4YlrZH4DM=\r\n=zTIT\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.5": {"name": "autoprefixer", "version": "10.4.5", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.20.2", "caniuse-lite": "^1.0.30001332", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "662193c744094b53d3637f39be477e07bd904998", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.5.tgz", "fileCount": 83, "integrity": "sha512-Fvd8yCoA7lNX/OUllvS+aS1I7WRBclGXsepbvT8ZaPgrH24rgXpZzF0/6Hh3ZEkwg+0AES/Osd196VZmYoEFtw==", "signatures": [{"sig": "MEQCIEBpzhX7zt/KJ+XEtj1EOFqoTAbrajumym22nxX+mJGHAiATTzhOoXU/15E0Qgy/xhJSINRF23oGxlEgF9Yu1wNC8Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196746, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiZHGIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqkug//Va0ZPTUJknIZ7a2wcwS1geZiW6SEC2XjpIaIEV20ADPPVyq0\r\nfoJkFGi5iOjlX1yWBcshAYhEfX+9ikQLhIj78No7fglcHpHs/Z5/7Mx7x7Xt\r\nXYwtrOHsjhNuZlBvsQGVRolZfI80D85of8VnN0nkdjLZ8ijJon38BvkE1L6A\r\n8r4E260uELqKGpUTqi5bHvq0Ff2ky5hJeGgWKte1gJCdzaBpNS4sYNsZVXyo\r\nlHYarEBHs1rDnzaBoEz096jX3U2jWuraku7i1b6RqxDmbryn/cAkJl+MNami\r\n1v0WyKctV5Xtdrp9P1f0uRz5rmpB/RlQodBjyXg+per4Bn44rDEDBqgwVmJf\r\nADViJXhIlo3pvGN1PCashGrzfkE5BDECOBG+GOKqFeTUk0GBJyPzNzpifXQ3\r\nTTsZ5lihYuyfBl7OYYITNu5LqNpu61lryMHZ0tZ5XlhOESQTNjIkj/qy3H6B\r\nniVxnKx5gAgjyja8IIeci1Uarzkq/QxVFplL1NWKGY8ay8sWs6KpwInuiYmx\r\n+/iPKFif4ZEbovv1yL7pvDrDfAaA6t3ENRmch2pgy5NwZWB6ap4e4lEw+dpJ\r\nCbM7clm/xeLUJcYnAhGImUSpqGvLlNWl6DKE4zvoHp6p81gqZZwPrcubO/tV\r\nDNThELGJUGdXhQnMawwEdPY2f6jMrc03dMY=\r\n=pVZ/\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.6": {"name": "autoprefixer", "version": "10.4.6", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.20.3", "caniuse-lite": "^1.0.30001334", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "ce6eba3aea7c03adf0f6a907bd594fd170b3f0b6", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.6.tgz", "fileCount": 83, "integrity": "sha512-Rv<PERSON>0<PERSON>ZO9tJNm3ydySK80PpkWoEZTGC5bHUh/xbrP8qJCy08NFBwNGPcozy3d3SDIM0b2kNxw2K7jAIYFF01A==", "signatures": [{"sig": "MEYCIQDCS4u5c9xg3ZRatX9CYp5dii568+fxyr0dnQaG7gA2yQIhAOS6sbCuhwr+LfIrn0ynzUx4x3EZ9J5DFKPFMvepujTi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196961, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJibxHIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqjCxAAksvP75WnSQMU+eZTzG1EG/W79jc2aZf8lSNd6Ri9qmXTeYiU\r\nTtzgGzG/rSHeA79zB8PnuZ4wJgW1tSivRqhOTWBB97eUtk2OVDtZjDdON9uJ\r\nRU7cpeB9ZHh7HkPB0Qgf45BBIu/wqs7lwKYgUZaNdxgQBaVHYLyuVyU7CAoo\r\ncxbXmoh1cPwYMiiuFx8tr5VN+7K/PuVvcydUGHN71lyTRpHB/+ojWapuk0UL\r\nTqBsWptBFr1bEwRgnS5xHUO8dUn0f14NTFgpuGy1H5TehhhBAuG+dbvkozJL\r\nULPRnlanSrYgtTa7rwTs3T/ZPbEiyFdeqlvkqR7lXatU5t1hjnJak8AWkTQH\r\nwD2RhpVy09dz3++FL0fMAQdeHen2dWsEeeKmCygHyAXOGuNyVwJgP5LP86o3\r\nDOkfWAxqJ+29flvph8B1QW5fq4c3tES2NAgoWYtPkuh+fnezi8MDRThRBq4d\r\nZZ36eseGMSIMgtKAbBKRLMst3jRNjFeNA8dmLLRC//zpJ7bzRcli1YjFKvXx\r\nRI1L5owZbj1zMvTWeUiv4kIpo7BycmZ8GCDDD959BPYqMGjq/fKIwVdz9I4a\r\nLO12Z0KTNWyMXjkr57dGg8EqXpIFv223vsbk+ePDv5e2spUMRB/YpP7Xs2lr\r\njwYMHRfIJTabgYOgY20UlOxwkdHTZAHGSi4=\r\n=dOHI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.7": {"name": "autoprefixer", "version": "10.4.7", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.20.3", "caniuse-lite": "^1.0.30001335", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "1db8d195f41a52ca5069b7593be167618edbbedf", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.7.tgz", "fileCount": 83, "integrity": "sha512-ypHju4Y2Oav95SipEcCcI5J7CGPuvz8oat7sUtYj3ClK44bldfvtvcxK6IEK++7rqB7YchDGzweZIBG+SD0ZAA==", "signatures": [{"sig": "MEUCIG5DjPVoIwZIakMSJ/iHGgWHbgLs0ftj9rjhLCCFC88zAiEA36v8fBjUsPJTD/ETwxUAJE85UeuFUZu+VkNI8nGGybQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197040, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJib/KQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpj9w//bm7X9189cjPl7tAANki0XMiNQSu3xZ5dm7Al7qjR06EOkK4d\r\nBApMPoOAwg8lJtmSiFAofqCb7xi1VY6Q4zrp9D8/xolB9ROj7M6PHBPnQx10\r\n/ybZc7lteBGcTEpNd7jACSqUAJFKw9q6ppLCkaiWseQmkVa9MV9JTdEXXWU+\r\n62omAsQjnxzy6dur09pr96ZdTCaJrnXxSk9GOGz6uJX319SylyhmmVCGRVu+\r\nL1ScfZOOgrxmcjxR39pN0/0zbHRuR5MjeuxmDldft8CETMzMSexzc2G38tsR\r\nkiEZihnxXNXLFN0JTT1/ptGV+pcktQGJcTrFHFs++BTT8z9kKyZvHfpTrJSv\r\nCGI9R45PbBdWWnMZIonXuIZ68PfAwxT+p8xhDlZtxGCdEOjP648RIDQUBkeC\r\nFXukVHPjx8ULtTFpyByTwPiSrEpOYz2c0I2oP7czI8kVmBBPyZcSpp9VJZsf\r\nn8bxoufK8na3cXIdoS9UkzCcAyXOVly7CYdh1rY8nRGFco8ig8k3eNttUKGX\r\nYkQvL/x6q5K147IM9jmaaGNRZeOhvpz7yBptHOKb/hrOqSdGB3NrvlEvqB9S\r\n95cUG/Xyth4dIdvNoBxnV55XEsxCKCCLTPkvQI/ZPQRZiJgaVOP4feuZyB+P\r\najlPuyQ8q30r/utTvLw8SsdsqXvyhT9cnb8=\r\n=gPQu\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.8": {"name": "autoprefixer", "version": "10.4.8", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.3", "caniuse-lite": "^1.0.30001373", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "92c7a0199e1cfb2ad5d9427bd585a3d75895b9e5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.8.tgz", "fileCount": 83, "integrity": "sha512-75Jr6Q/XpTqEf6D2ltS5uMewJIx5irCU1oBYJrWjFenq/m12WRRrz6g15L1EIoYvPLXTbEry7rDOwrcYNj77xw==", "signatures": [{"sig": "MEYCIQCf145pAIVrKpyPaNKP3vLGBxZt6l4G+4fqvR+mMSBTiQIhAMSAAsxtlYPjKmUn0RJsd06MpbScN2RT1ZuBlzM2ODRB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197084, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi470ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm6w/9HagnFhZmizkAyIzBgy49yliETxuWNj6ddSMQmLJ6nJ7uCNa3\r\nJYzBAIHAivHwhyLR/BcfRo3cN+YLbDV3vwnhvYcPwcZqha5ZiGfO5HCKNO9x\r\nZ4cakkeFS5TT8VSQV9luWUhBHOsgadf6WL9HLy+oVQlhQwxe7I+LyaeNF1Dj\r\ncMvwRQTJ4amBtTWceu683frW1rsJ1Ht8kCba9avnsM+uNnDifLwxgNeUHd4r\r\nSXTgHcXhOYjCXGcYhwPjtbUx4YUA3qh7JbgHilw8Oq/pn3xf7YHWLIqK5kIf\r\nj/0MiVOu5DFbK4R81x+kW9HW/If3caTf/SnF/+YvaCVqmGIcu+Hte7uJRl8W\r\nD6dzJ/CT+lUDsGqwPWuHo8nD+8p1gc8OfRgDd/FihEybGEJtdxZIdMcnM9JD\r\nmwnK3fjmamOZn7yLTfwf5wahZULibxlIDSwYbVSSzfsfy3aWZz/KPDOguR7q\r\nMTaRPdd1ipFtdntFTnhhmSHzS8N01jYp0kddSvqEgVRCbfyuGGUtrC0vAv0F\r\nmRuGIxcRawkow7aMdCfP7JuEvwmJZajlZzDHRgzKSJqdwu7b4bcoH6B4oHdy\r\nfSAk5jFxOKbH9vfb3+fHXMygIo8P8jsGOKuDhywwnnM74tsYzq9wgUKuj+Hh\r\nBEUCtxxZHz/fCUR5v4XKLLaQVskHXVYXVeI=\r\n=RD+I\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.9": {"name": "autoprefixer", "version": "10.4.9", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.3", "caniuse-lite": "^1.0.30001394", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "40f932f7d0535264823882031f9254ea72c693e5", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.9.tgz", "fileCount": 83, "integrity": "sha512-Uu67eduPEmOeA0vyJby5ghu1AAELCCNSsLAjK+lz6kYzNM5sqnBO36MqfsjhPjQF/BaJM5U/UuFYyl7PavY/wQ==", "signatures": [{"sig": "MEYCIQCQUUXhOtCvH671G32Pk14yrsLBH1nS2+sdzT8jgthvewIhAN5Xp2Xq2nN0omuT2/ZewrKShi6WMwsZuAzbpFXaonVP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198775, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjHnGmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrAcQ/9EwM7DBmOS0oWYyoAdEwF+XYdOXNd0xC8RhlpH/kUcJZ3uXKF\r\nJDGSIesoM4Xj3halRf4AYkMm8v8JG3AmjkmYKGPR1kcfIZgLRWAu7sfy1zmn\r\nXeBsyBsZmlw1D1nBCyalKXnA9Jj/1xkw4TAlr0nKyyqfjDJMfyu33IGTVFMd\r\n3OLRfjWLlEHhYvPbwPRaSKKplHqd88UZybCHsDLelygKPJXI5LasMrGa54xY\r\n3D3zPdW1rNCuEbeHvGCVisHRlACNlzXw4mCVGerY3ug1sxwB6kitqh1laudX\r\nY8ongT9n3WXWFWlzsx7jhgq5ZIK7dtWh4vah0x5U/ciRqInen7Y7UjzSN1Oe\r\nrtrdsfGzZUjK6c1OcQn/8oxjwu6EYOoXyJqYEa+bHmIEGjXfSyqlBROw3A9j\r\na9pVaU7QyduIO9yMHx8EH4N8vpPGn/NpjgnREdaDDmXFYzEEdcM78KCadSX6\r\n4NoTUxscIBayYMhlfMMyXLHMuINWNVtyyUPXNNIVCzkK1yK/biePNOuaClG0\r\n4ekMf+BjW199YK+SoM1lEOkAMGW8v1dGRicDNQk+wLebBi/vwhBTLpsLyh6o\r\nkEei9t9EBanzp6TKlGaYVwwmH5k4L4mkoo0cvLygM0hoIgzOk9uwzOVeNz62\r\nzVhkt4ooIUiocNTbHgL0aRs4jo6idC9aQe4=\r\n=VsF7\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.10": {"name": "autoprefixer", "version": "10.4.10", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.3", "caniuse-lite": "^1.0.30001399", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "a1d8891d1516155eb13a772b1289efdc61de14ef", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.10.tgz", "fileCount": 83, "integrity": "sha512-nMaiDARyp1e74c8IeAXkr+BmFKa8By4Zak7tyaNPF09Iu39WFpNXOWrVirmXjKr+5cOyERwvtbMOLYz6iBJYgQ==", "signatures": [{"sig": "MEQCID3IwgbFM+Onuc/afgOSkZe6Rl2VwxgFMruwHWEKv5LyAiBKgoFdQNWDNAqd/rs7LXwGFKPbizOOIfKW9B/CZCvAgw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197266, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIJlWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmql4g/9FNn4+qiWGhxUUb+Gv87swCI5gjxxfYDLFuicAOqjmaNJ1OVJ\r\njq4q4ToCLnOFvuebcr3CvvjSQZAPadvP2lRL8bChJm2jlb5VbEpzy4166GrV\r\nMqCz+B+kfiHuvzBOK9Nhyv6/0zIww+oQSGXkLANzoPAf2JAEFeqMZojK3git\r\nv3R5arJuPQlC15tUVGfahF5/dM5KYxQsY3PahIWbpquJAcW8sSH/JP0HWCMz\r\nlth50aOh1GW4X8/Ulgpu7uHoi71zXQzvKwnKQIgAPs3UGfK6BXK73CAGbyjH\r\n+BbNvzpE4xtQWGjd7sy9NJEgJQC+e0a+tmOsLfMkaBtPdvc0J3qrEqoElYkn\r\n7/H0QEsMMX3fqdXu3vNlVjM8Dt1IsQMzUcqGWpbjJc+gPhoDhjazu+eY4H/9\r\nz/eSUb0SxVXFIDNcXkeop0jcshakrljOYp3oWfa9em2Pj7rcgYgD/jqo3Y1Z\r\nrxZjMpvXF2FUtSCaFZA9swJw0IByhrdRmlxNZZ23Fdq3pnx6JYEdwpf8ZZsK\r\nsVxFb1t0vlieRjydy9RDHF04THsfhiTQ5n0L6zbSXj2b3tIWP4f4JCGJtGNR\r\n0QMQV9xyjntZPbSqpHcUbbmZuA5F0vAkSUssNfh1NQ5Jb2kbU27L7y9LgUyS\r\ncO93aKXtOPT1YSpzazlg6LGjSrkfkq7SdKU=\r\n=yZcY\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.11": {"name": "autoprefixer", "version": "10.4.11", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.3", "caniuse-lite": "^1.0.30001399", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "835136aff1d9cd43640151e0d2dba00f8eac7c1c", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.11.tgz", "fileCount": 83, "integrity": "sha512-5lHp6DgRodxlBLSkzHOTcufWFflH1ewfy2hvFQyjrblBFlP/0Yh4O/Wrg4ow8WRlN3AAUFFLAQwX8hTptzqVHg==", "signatures": [{"sig": "MEUCIQCPY8/Gu/cmy3VqUaWbpuwJj3njdtzzF2as8YeCDHDieAIgHkQksoKr3KJc6PQ24OVL+w3Z9F++nHLBYKe/b1RX290=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIhU2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosKw/+I3sm6jaAFro59N+WSLpVULjih1snDY2ISiPtZTs8nmvhnvN7\r\nIeNxkwx1NbYDEZa8uwj9DE6yYsQ9Ey5WZ5XaY2eCPrOI0mqClsb4U4V68kdl\r\ne/j5DbTzoGPxjnqY4aJa/DXqudDSnKKeI1lshMmoGQWMfc9ssaNRUYjQhceb\r\npNAe7LYPGXDQ9bgMvLvPTdst3HL12Kbjvi0uQFJHTqC9/zFA+SojwhKuqVLH\r\nIj4TmX9BgmiDtp89R/d+Qo2L4UjiXmFgDZ/qmOCdAwKvSuLVv2v7bfWbJMiq\r\nMbKXlc7yFXFP9cmgEAKzMroq92FKWrpBxCL2LC/Ic5GcqdXmn2+/3u9Ozqqi\r\nE6/BjKopu+gkTS0WO4P9Aub0+R6Cwuer2CPjQDy16asovGizXMxRkzSqacTw\r\nRvpn9kqzbU4p+k++3EMxjlNF7sG1BNnn9OqFNTpWkqEpVJKJ7vMy+oX8dwM7\r\nUsSB7sBylI0Wb2YU/CPrA+dnv7gB0LrafzjDGYZCf/ZKMQ9DnKm3T8YyiQDz\r\nvA95EhYceGYNAb0tkX+Ne2e7Fg4zoqI812WBfU5aQeccSr8XpYw6KxhCfGIS\r\nt7qto86wk0SYORd02ztlIik7eHistmAj9+8r9YUe0vAAHhWxpHrsb/mZ7+3a\r\nTuzqIlwVKoozBLsamoeAiE6Qp9JPGAg8Eg0=\r\n=1rDp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.12": {"name": "autoprefixer", "version": "10.4.12", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.4", "caniuse-lite": "^1.0.30001407", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "183f30bf0b0722af54ee5ef257f7d4320bb33129", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.12.tgz", "fileCount": 83, "integrity": "sha512-WrCGV9/b97Pa+jtwf5UGaRjgQIg7OK3D06GnoYoZNcG1Xb8Gt3EfuKjlhh9i/VtT16g6PYjZ69jdJ2g8FxSC4Q==", "signatures": [{"sig": "MEUCIQCqtwnUggjMD8Zwow+hyxD78ZPCcVA0ibQpTM+XVhgZGgIgXikrgwmUOq4hLNLJiSiiJbyKTWLxSVj8R/CQYYFriTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjKkZiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSWRAAiIjw4vOy5fJVhlZNCphlofEXexpdEfvvPVJrQF+U3toZfvz5\r\nG4pYlQV8JpMAe8XIjTiO5pTHzpvxAw2uQ9LgsntyIdkFt7Sz8EEpBLMWQRPB\r\nSi7l/DI7NMDXx/KQdcovAuf8GgoqFOYk1zOCLQMV+Erq77v0ZLv42wgfja55\r\nCEDF8cKZsR1Fc4vlWze1NoYnWBs9FXLjpnOfiR1JTlcR3JrTDubWVeuzjKST\r\nou2upnL3WN+MxdAOtefMNruTy1MUmtuTN3v771d0m0PItAb11mXLV72rqLtu\r\nzc88ju3Z/O1CTpv6AROl37METrLrfOUf/bJMnYXUnoGRReqrTecE2FMrnZAu\r\nIwLUC3455J7g9UGOgkhBvTy6y6v/I37LKGf0B39A3S9K6CBXdd+nwesaGI7Q\r\nt8Yq77T8X8bvwCpXhyThR7C8m2Q1p8Mw6djgzbxOYdkjQxZzSqSoRMLPJTMz\r\nr5/GipeMAK1CBabfmoN5dbcMhgHlp2+iiYGQDqejT75zxnlUzclJZKfdzARI\r\nHjl+iGtDjWbNovKcwqwcBx1+zd98CeIaTfNPOmY3N+fAYYdsSw2BxAD82LvU\r\nRXa/Utx5zt3OXefvQM+WQ/wc0ptRgdmMS3zEMmhyRbli+B9ffVs5EJQ0//Z6\r\nFF5YvE7W+cu+baf2crBJ4yPU6jiACAFgN9g=\r\n=21pI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.13": {"name": "autoprefixer", "version": "10.4.13", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.4", "caniuse-lite": "^1.0.30001426", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "b5136b59930209a321e9fa3dca2e7c4d223e83a8", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.13.tgz", "fileCount": 83, "integrity": "sha512-49vKpMqcZYsJjwotvt4+h/BCjJVnhGwcLpDt5xkcaOG3eLrG/HUYLagrihYsQ+qrIBgIzX1Rw7a6L8I/ZA1Atg==", "signatures": [{"sig": "MEUCIQDLw5omiihNPtILBDufB85TzIApalt7FS2p8UgVOL2z9QIgIk9C8puNSyZF0tbOOUuAJPvpK1A/EV2/mI+gXfH1wO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198690, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjWupsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrNDw//db7cQ3WUDo9hfMC4IIIHw5mS29JewvO72nxVBQVIgGaf4dOr\r\nt1QLdYaL3XOU0TD9k4ayqxyvXcCdpEWoGPNqmCQ9N56CI86cguXCK9HZ1A7K\r\nFyEj9yflSFsfgdSxrR4AY/j6MTfrVDAyeuYJVp7DHd2Ui+P2s+81SfX8aJ7t\r\nnQOe8de1NhKbflAwRUbbQS33CtcPjwWX7pztZKODwbkqBTM6Mas9Rm40Ohc/\r\n5XGWkJeI07G4AlAT+zCZfU44DCFeyQyqhDuOJlxusN1Jeb5HJZ7xLGcduZOL\r\nG8frhxaCLeh1jV9hGVsoQegbknDjihjvVu3NwTZ5Qei9yrxbQ09UKrVGPlcs\r\nXXgkApPBgFiGRbCUqXdyNbzGlOGlHVbFdGSDsXJIDGIk/jTMcdc2aPVdqV7m\r\n6kGWISr5ykMUJtYUq7+rk+YczLnSzL0ILU3mj3WQp8MNtRdXjz+R8OjeBLIU\r\n2mPvVfIgZZUuVbBIGQfbzaTeGspDXQzyrxNtAFGqStWpbOUT04qfDye1TV8X\r\nrHZTq4LHViQWVAwBtH/VMxKbQdxrLx4h3VSNRTBcTauvo9OPh4kegCeAbwzh\r\nOXGLRrt4uc9+Tvm6/LQj+IQ+reJNjqrOhBN65AU2yRU8+Yd0Gc1TKlTkOFA5\r\nzeKQjFUTP/5k0wTb8C+gBk5ykwShotdB6rY=\r\n=vDT1\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.14": {"name": "autoprefixer", "version": "10.4.14", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.5", "caniuse-lite": "^1.0.30001464", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "e28d49902f8e759dd25b153264e862df2705f79d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.14.tgz", "fileCount": 83, "integrity": "sha512-FQzyfOsTlwVzjHxKEqRIAdJx9niO6VCBCoEwax/VLSoQF29ggECcPuBqUMZ+u8jCZOPSy8b8/8KnuFbp0SaFZQ==", "signatures": [{"sig": "MEYCIQDbaPVpk/hRBScv7vP4h58dCbdZctLzVGOSM40BEdLIZgIhANmKh9JUosI3TCNjA0Rgf65I78+qL4Ik60ZfP7Nz4HY8", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198756, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkClKvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPCQ//UACWicbRKEnz0WMamKne+Py6jGL3SxUzymlmPvlsxKcI/4B6\r\nPvU57CyH4E1jlk/NMXcDKX9TMMkL8wgrTw1kFW8/X/kEV3eb/ioBo9dVDfs1\r\nF9nE3UH6wj9x7XoJ7HiXAb8wgAXlNh/UXIRYz0NOcWbol+MSFTIUvWuEapYa\r\ndFg9T1WKI5/GzMoJUw+3D+f2MHwTQjLqHJSUYsVa82OeHD3ZINSsj4qBhgLD\r\nG3j+TQIU8N3nPO/7sCHErgXY3thR519q+Lfw+/5oW//kf7H9hp4e5PUYICWS\r\nJS0PXSwXmHHgpfK6//naPfqAM05tsXL6ioR3qlTusw7vMXaSC4l1EqzaNj1n\r\nfh5Ku34vwp3/blMGZIIHCkfiCWzrJFqg5NywXagJdvo0A1OzzucW9DI5Jmvy\r\nCPyJ0pOvuILGEOhm2t/yQ6fPGbg7TI4V5C7nefS/401IQPR2ee8SRr4foaV7\r\nxqtAD8Tikq0PTuAK39uEWSpywOVTZ4lvVm7Le/uNiNaP+28FC6vB8IuCQ85q\r\nh3HD5XQDPyf3xz26SbbOfZImysSsSEbxVfnhF6UKsPBkQrWkhx2zQrjB6pz+\r\nln6T6ZQjQcZiF5Vf8cW81hEFB61d2/+mUJHUpkYI0n6wMTr0ih4DgsmCa62G\r\nSSKml+YGOLmnouBtf56v+/+5cw3ljXmJT7I=\r\n=wMzp\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}]}, "10.4.15": {"name": "autoprefixer", "version": "10.4.15", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.2.0", "browserslist": "^4.21.10", "caniuse-lite": "^1.0.30001520", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "a1230f4aeb3636b89120b34a1f513e2f6834d530", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.15.tgz", "fileCount": 83, "integrity": "sha512-KCuPB8ZCIqFdA4HwKXsvz7j6gvSDNhDP7WnUjBleRkKjPdvCmHFuQ77ocavI8FT6NdvlBnE2UFr2H4Mycn8Vew==", "signatures": [{"sig": "MEQCIFTrTAGpK17o3h4qWOi0kbZNIph4TVMVg3H2/myZpZAzAiA/ALXMA6pvw2CLTuAFp4wrQLF3tyE70g9p/W3krkBA/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198998}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "10.4.16": {"name": "autoprefixer", "version": "10.4.16", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.3.6", "browserslist": "^4.21.10", "caniuse-lite": "^1.0.30001538", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "fad1411024d8670880bdece3970aa72e3572feb8", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.16.tgz", "fileCount": 83, "integrity": "sha512-7vd3UC6xKp0HLfua5IjZlcXvGAGy7cBAXTg2lyQ/8WpNhd6SiZ8Be+xm3FyBSYJx5GKcpRCzBh7RH4/0dnY+uQ==", "signatures": [{"sig": "MEQCIC82B1gQOWE6quRcFbhs3ZcysJItZNY/WmtqTGwHUlStAiA/OxQkG0itaeZxEROOI1mJoc9ioa5h4DWVQjJxuXJ1yw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199002}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "10.4.17": {"name": "autoprefixer", "version": "10.4.17", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.3.7", "browserslist": "^4.22.2", "caniuse-lite": "^1.0.30001578", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "35cd5695cbbe82f536a50fa025d561b01fdec8be", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.17.tgz", "fileCount": 83, "integrity": "sha512-/cpVNRLSfhOtcGflT13P2794gVSgmPgTR+erw5ifnMLZb0UnSlkK4tquLmkd3BhA+nLo5tX8Cu0upUsGKvKbmg==", "signatures": [{"sig": "MEQCICt6gvm85usHPbd7FfVVoWABXX88hy4nMc5ClCHU8sSsAiASdORA6pVWklrVlb3GwJ60+Qs7BYi8cWFZpLqLS1hCEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199134}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "10.4.18": {"name": "autoprefixer", "version": "10.4.18", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.3.7", "browserslist": "^4.23.0", "caniuse-lite": "^1.0.30001591", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "fcb171a3b017be7cb5d8b7a825f5aacbf2045163", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.18.tgz", "fileCount": 83, "integrity": "sha512-1DKbDfsr6KUElM6wg+0zRNkB/Q7WcKYAaK+pzXn+Xqmszm/5Xa9coeNdtP88Vi+dPzZnMjhge8GIV49ZQkDa+g==", "signatures": [{"sig": "MEQCIDHUyAJZ1ek63c5u7csO1uUas2akZQukWRlcXpSrEzbrAiBn4N3wwaVMgKVybw/j6GbYjIrIIheuD9AtNym7Dd5BFQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199401}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "10.4.19": {"name": "autoprefixer", "version": "10.4.19", "dependencies": {"picocolors": "^1.0.0", "fraction.js": "^4.3.7", "browserslist": "^4.23.0", "caniuse-lite": "^1.0.30001599", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "ad25a856e82ee9d7898c59583c1afeb3fa65f89f", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.19.tgz", "fileCount": 83, "integrity": "sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew==", "signatures": [{"sig": "MEYCIQC18U6ys51kEwBlW5fti/1bfHjFe+fk2q0h/pcmmc/UrAIhAIHD5HZnY/uDLq9WqicNypq2eQ6aBNgiLR3hSaUH3n8w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 198892}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "10.4.20": {"name": "autoprefixer", "version": "10.4.20", "dependencies": {"picocolors": "^1.0.1", "fraction.js": "^4.3.7", "browserslist": "^4.23.3", "caniuse-lite": "^1.0.30001646", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "5caec14d43976ef42e32dcb4bd62878e96be5b3b", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.20.tgz", "fileCount": 83, "integrity": "sha512-XY25y5xSv/wEoqzDyXXME4AFfkZI0P23z6Fs3YgymDnKJkCGOnkL0iTxCa85UTqaSgfcqyf3UA6+c7wUvx/16g==", "signatures": [{"sig": "MEQCIH8KVxq8drNmyIhkY+PB3Vwiss3ypNLWE/8u8Bd9kTuKAiAgFmBVcPgFsJf1tbY2QldttdqSp8XJO3txE3mwb4bfDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 199114}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}, "10.4.21": {"name": "autoprefixer", "version": "10.4.21", "dependencies": {"picocolors": "^1.1.1", "fraction.js": "^4.3.7", "browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "normalize-range": "^0.1.2", "postcss-value-parser": "^4.2.0"}, "peerDependencies": {"postcss": "^8.1.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "dist": {"shasum": "77189468e7a8ad1d9a37fbc08efc9f480cf0a95d", "tarball": "https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.21.tgz", "fileCount": 83, "integrity": "sha512-O+A6LWV5LDHSJD3LjHYoNi4VLsj/Whi7k6zG12xTYaU4cQ8oxQGckXNX8cRHK5yOZ/ppVHe0ZBXGzSV9jXdVbQ==", "signatures": [{"sig": "MEUCIQDDvEvx86F1Kl2Jk9uzrU5mhkPo0QuAd/1ZkF+bIs0QRQIgPghUahicnY//3CA7+Tu5lMLLL3enwdd4KRAasZKf58c=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 199184}, "engines": {"node": "^10 || ^12 || >=14"}, "funding": [{"url": "https://opencollective.com/postcss/", "type": "opencollective"}, {"url": "https://tidelift.com/funding/github/npm/autoprefixer", "type": "tidelift"}, {"url": "https://github.com/sponsors/ai", "type": "github"}]}}, "modified": "2025-05-07T01:51:09.388Z", "cachedAt": 1747660587438}