{"name": "@radix-ui/react-context", "dist-tags": {"latest": "1.1.2", "next": "1.1.2-rc.9"}, "versions": {"0.0.1": {"name": "@radix-ui/react-context", "version": "0.0.1", "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "23524f0d605fe12ee806f6e1e3834d41988c7d3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-oNm8xrhzbCKu/Y6E80x+mbzNlF4v34avfTqj9ZvHQzRhTKAhlgGT7JRXT3aeV7vZj4M1ofsaj/b57pYPx+GKXw==", "signatures": [{"sig": "MEYCIQDdWPIzx6LYGhqBJ4cSYu8hmEPPI0+HYW8znipj7JyRrAIhAPewJzJl/vyydBAvbF4ix/OrPg+Qpa39KXq6RyuUeoyo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VGCRA9TVsSAnZWagAA6nsP/A1m2bh2KkqHjsFb+NtL\nniDmUW7rnEaUi5Un6YzWtjNYkIc6pQKDrjd9CMgrCZEmZPk58qvHc/zkSaMP\n5KA+ewhJL2WZ+O1XsvQIYetm1jgDsU3HZRc35SrHnlwG9TXh/5SoXJEaqe2k\ng0oDYs0u8YfzV+VlQocEhPbNSThaejEX0UvG0O/TgOmkXa79IO5yFLRO0ON3\n4n2ynPAK3NuVpnhuW7hIUXj/DlY+QF+2v5ET6605u347B+pg47kMoPv633ei\nLBALukFAsNnR66908SERgOw1V1B1v3glJbKabzcsyrt2GKuf2S47sTtOVToO\npHLz2W1rYygTvrSSRecU7iyj72rDwZtAYPf6uXCcT8qtTpGBB0/6IfgrxtsA\nOuhoGGAb+GdPKtvANdRIF/OnMTx0XU/ESDkBf9vvr8uRFxzmGTaINmmLX5Mn\nAj2krD/nibjZqsh56l/VQDu4WqQ2mysn9x2iIJGy98nPMJ5jvDMrif7arlT3\ndRTfojZTv+iil2d7rBBIVVYAIH7X5FrwalpZb1mFZEgwU0a+gK0rQn21e8J9\nMon4JJVg1F2ABaMVTS565zPuwCcvCzyQiHUEGQ1NO3ACGvzeYFFc8SsXJMRa\nJbruaSyZ0pIcQ2D2GuUY3oSkwcNjfhJzuvhW7L+sQIRmapplfOJjv+H2BiUO\nb32U\r\n=UY6S\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-context", "version": "0.0.2", "dependencies": {"@babel/runtime-corejs3": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "83f0c4dce935fb6525bc124b1d5c43795f4fa86d", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-<PERSON>yn4UIyuchfjYop02CQV3yiENTtPARe7KcdwRQ6A5wtXWzE/hDRGZcPidILcIwkGkTZ/FhIiqh0H0xyFpDP7GQ==", "signatures": [{"sig": "MEUCIDqTyUVkxarN3+ZpC6PV0TWTCyDXzqh3/oK3mRJ6hmhSAiEA9bsgc9PEOp/z5aklo28YQtKm7v54Ew4e8Xr0UAY+sWw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOrCRA9TVsSAnZWagAA/1IP/jFpeXbaAJMGwIU7fcjv\nWGpL+uQEDftWw1ib8ihbirK9D2qRVoy1RSQSrCXafNN/BSwPXG1h7nnF5eNc\nEcQG+aSLdN3g/QkDQ59SzKtabDvIMAIR/fMWllHHNw8B45JpCznqEhoIHJWy\nOixk0X/n3DKx2GOlWVPXQWtrwyBPNwAyEpO2q/RJrrvMwCEGiUEUYYo9pNM5\nfRnWXr6gcoko/IW7T3xUghwZiOF/UUoHyICFUas/cq9eaG9FKLD+xV4LPfVw\nDqLN7RDcKvGSzS7CiVjrNJnGA//VV/Ya7NJ90/bluBP6NQTIb24qrG+5Qz5O\nWX/W6SWFr2up56lAQvWVQEvlDwIMLIzdNK4yS9T7FUOj45tCMJ+aNLPzsJAi\nsBxUv6GMp6lcvuUWc0spiNIUNkmzASmg67xfCbbpOC+JsvMYsylleotL0vcW\nTwobpWC3dkkxY8mVo0BQ6NjD6C3nH8zOrLfsYb/UzZrtuVzHL4fPI1Ve249l\nmxdat8dfz2js/fBW0WjCqfV4wMqNXUf5lF2I41GMubZiwDzz++iA0HfgT/fQ\nnpXYco7j53btso30oGkp8oNa/z4AKj3iR4qaQ9ObHACZEX8FM0OOHCcRVtWB\nOYz502t/KjqGtbRAAcTq+6k7GkfPg3tDqCGTyAmzMQwqQf1I8dg6VqrH6jzm\nXakW\r\n=olkc\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.3": {"name": "@radix-ui/react-context", "version": "0.0.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "feaa25cc597a3f065d61758cf621e442366de295", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-TwboD/NhqL3Wn81oVGaIgKsTkYptiPAKkZMXOuBtvmFTfmpDjJe+sGkkmcKdWoV82s/49Tq6scVnIimn1SZ5dA==", "signatures": [{"sig": "MEUCIBTCEWNZ1R/JCUolPfJ/kZXSBOb3xGyQe0tktOFH+8fHAiEA7zsNZJqeWCVxfQx5Wu8I4yO9b1leyMGd5h2z4STMYCI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HpCRA9TVsSAnZWagAAAwUQAJKL6IEwLK2Up6j8hDXU\nn7bbOeiBI+7SJDodV+O5evTE48JQW1hgVLHXT3aJXwUTM9eahfZxW++ZGwb1\ngkGKjSTZ21GJdIb2AU8hqDCqve22AFhqLuIrRG9WZjD3nZEd6paOX4gug+zO\nGGcdw6SRBHe02CFLeo6BSvgCaNJbA7bPjQV9YYWzutsDsUQml4tsvNQ9l3uc\nFBrqwG9B8tX5P2sF87QtaBLPjnIEQP55Pl/tDi8anTuUWwXFlGzFWqvrQkvH\nNyOv53M7PFBOb1YCbW+Uc3Bhf3rptlYZy9xu6bOdd+ERYIuEGs9ee3C35Skb\ncEZoA9/taxR8RngLkj14RSlWoCGeVx4QbJx0vzbQpQt3AbPCbf07k0B7VUva\nG47X+Wi9cO/+xTBg4J54jtrfPOwVwcQpRMM2zRjh+KMhPMZg5Y8foGT56iTM\nSw+puPstF0j/7/WtZF37V8392BlTnj5be5Hh0USFZweNJ6kIlEU4YdqOSAMY\n+7wk4Ud6PRbEFZ5F9zDXK2m1LDksKqpzIoekNa9wMGKDFST8H6Ls35uxJSLi\nTxVR5s7j69LXHC84amjwK2CF6kLQAJ8sSdfA0y/5SFBws9Wi84jlaqvf70Bt\n4qI6/hdiz9nr/ZOYyODU5FjpRzGV5bB6Ilv347mXFYm4CUhdXrCvvW7sc1a0\ndBAr\r\n=UXkT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-context", "version": "0.0.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8f921853d92dbd5792bc64d1fcf4c99abc915ec3", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-Eg6SjC8jjfEJTqLUfnPdo+Lj2RSSwTo/yr8MxOURGsQQ4OJo1WB7N51kapmsY3so8vufrPGAGh8guV9m0OAGFw==", "signatures": [{"sig": "MEYCIQCv0Jc1t7pC3RsJgeSqIxbs7X67kZvcVyxGHBnLncw1cQIhAKoofMqN6zfwrpt3UmXyGBhXJI/mq3uDm3T+Efoanotx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7413, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vpCRA9TVsSAnZWagAAzTsP/i8urb3ExXr010IibaoD\nDJ2xiaQgAWihEF7fzYjSNV4TRS0fceyB9MXu5V2F/c005QjbZRdGrfU3PA1X\n/5Uyo38Mad4jZ6yYz7hPWvmFgI6vfotbX8U2ets1tsRAIHC9tt45ojNd6WMo\nCqnLyCGExEKZodCcPW3n7/2HZkuMjP2recJAWU+Gk9T9/wqLQwFOeo46c3IF\n93+S60yu2GxySyY1GM2eZmsiXA7UuFb+FFtnjLj1BV6d6wgUV3ZVqyzMftCF\nlejpbGhboWFPeFsQo/2T4bAfojJZ5nbwItoHRPbiillUUv0oSw2f6wJElX1c\nK99PGfBMK7gZH9fuMl1PrrkqJR4GTIn3s60LbLm0plUsk1p49G9xVJuGcekV\n2WcerlF+XwP2J1xef+zsgGMwzkuV/lB4VOmfQiNVDH8DP+oe4dId+2QzjoDF\n3FMM+wLe8WgQHjpgnwEqakFU1DB0GbZM0L/TvO3StAC49BU3qFacVmcFuje2\nbvvlyuOVsLs0kZGRxgxLJKg9d36/nTbMffrXOdbFJLcQS/Q+qRYHSB54+mww\nOX7Hiq5YNs1Ec9k+8aqW02Z5LA//SoIFAnuWj64DmUCi6n748b99p/smMgjr\n/wDctP3V0oEpLzXHo3LuJy2UoaPCYCjWBy2o6YMWGpfoTCCuBIV/cqgVfshM\nLbp0\r\n=nTDv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-context", "version": "0.0.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7c15f46795d7765dabfaf6f9c53791ad28c521c2", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-bwrzAc0qc2EPepSTLBT4+93uCiI9wP78VSmPg2K+k71O/vpx7dPs0VqrewwCBNCHT54NIwaRr2hEsm2uqYi02A==", "signatures": [{"sig": "MEQCIF0Pd6+sOYfV7Sw/YxxMIs4BtcE7OKcU2arp4S/aaSPIAiAHDag6S794A/E4KyQb/JYlSGdPkvsnoKW/q64jEx0vVQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7467, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmfCRA9TVsSAnZWagAA88YQAITMgWISH0XxZd+3QVB2\n6OV+cunMg1735nAaqB525j0+wK2k2mr+LabLev5so6TNhdBTO4ViYNVmLRi/\nQW72dDq/drb6lZue6VGxG8rajPghmKDN6kDOty63vkfj4qtaTJPTh2Ma4cxA\nQWdpOFkeJCM5YTOzc2AtSrcfQzkZsNjNbPGgkRg7OE4wOYgfzQ9+XocF7K/c\nRgSbanYquZdwYGpMGBy4hisRoci57LtiPwqhz0yYk00rTh6afd25bMWfQMLg\n5X2XL10gx4jui8gD254wmyy8lDLEBv5tbjl8LZt/4DkRDDyDG3VjyNJ2Y+Kx\nDjpMEhaABXFcsuMj77sh1Oip+p0UrQfq/UPyFbTZBWs3fl+wP4IOerCplK8p\nryPC0wtbyh0y1KxIElUoQWSXeQJ0DUfbR5ytR5n+qekwi87NYoeNzxxjLfre\nfruWz4/FaqSCu+vuq8Ws/Tn7C78aGLVWI/qqRb9WSV+3GMrUXRTlqcbswXwI\n78OoW4BHqbO2TWH5b5qsebH5yPlo68wI0nQLGsK+GR6r/vFjICUk6QNq8Zi8\nypS/x3iYLF7osch6XzG+/0l02UD/jzjHrtSQpEMcHD5+LTZQZn+25jmxQ2mc\n6Cx0eF1xnohNhEbnO3wMJEn2BCi55RIA6M3Z7oTDjTHFCqi6Dkq1mTB1+8Pm\nNvSl\r\n=xbVh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-context", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8c2a3534bb203066fb5690006459316fa1e75d8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-gCLIrAwsjbowmVUesSteqh6ji4rOCIv/pwRS+0bD6v5QSql2P0Evn0+GAw8q3LR6L9XahzMccc2RR1T5jiumkw==", "signatures": [{"sig": "MEQCIAfF+qXiEeEXNktZYb73bsvgWVIwAAQt0efn9D6KKRdwAiAguUFWPgXjToyP9Z1vrFUwvnCgYcXNbmXEe3BuemRy3g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 954, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpUCRA9TVsSAnZWagAA1T4P/iEvfvBophL8KoSW9qbM\n1HQM+H29wfpzn0da2It2FXDNl8h7SQ92cTPHyYLEOHTBTRAjF7Yx+2Ilkdrx\noWcMjoV427rLj61FBHL2wWGOXQi4posaj0OwZWl1vL01QlXE8NHtjtzxbSzT\nUP+pZAbgy/AfudNVN0DnfeXBBZO9VJqVdimYccK7H6H5CHFxMkCXNHnLNwiU\nkRwE66/P2KkprBe/un7kG0Wlwr/0Iz6dXMsz6IDFZpU7tOw2WMH/uxw94Wxq\nYHtFYqZnjVubsB8i8bG9+BMbeM0lkttcwOdkGkt1HSl4+pzpdNFrye1aP0Of\nvSQo6v7gAS0b6bxarZg4KZJyZXYzxH6Ka4/2iU0ZfA0SaaQF0X6nj0j+7HKv\nclFT5IThT7pWU6Dy2DJ8KOgIy5JWjZooPLEnu/NYTxlQ6W2qb6aF8GJqgvFi\nXDP4CMntviYKb1hUn9BJHimjnehiu/HbuWeYstIjAQXETPV1yU/KZxda04rH\nGxeZFnzEDrjqrX8mFEQu+orUC7DnAl1U+bNqnqI+hXejyqRgQfVdOvu7ET/U\nNh9kj5K+L/+c9OsSGJkPpYKJF173KVf8L5pGRckmp8d/92PN3cejE4tIxxkk\nN18oBixwLOBA/biSCgtMo7D2qJM6gq79ekhzXgUMqXCYqyXOhL+08TMUtCza\nSBX8\r\n=QDaE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-context", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "410ed127fee05d9e1940e28690eee09982660bff", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-M0kn9yUp6TfB8RRVrEiffeiSVQciKo738Fpudbx8CjqjKYi972a8D0l+t7XksMXWN5rr9NPJlWSnPwpyTJYxUw==", "signatures": [{"sig": "MEUCIQCIfJUAkN21s76HVBwBTf28pSTY9Y7nfroVijx8Gmm9/QIgXbs8wHtnJUnyEdxo+7AYpq6m95p6NUWSE67hg3uoP5I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7468, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyOCRA9TVsSAnZWagAAFTUP/R35INgs1fDJJPTX45lf\nvUaeiEFolrz+YP8crBqn7oYqr7x7A5tUNVgFOPgf0pP1ziHDkkGdUIL/e4Ks\nda+SOssMjkV3eoIFNtFL82SplDBfrIRwKe2/XAFfP1kBocYqmEUJQPhFKPi4\nVUZ1UY9LXIV7gobbEKA7V5r33xhgQiogac4hTQiCS56UpmoHd6q/LRjEyfwe\nl/RU7svojiSPbbW5PQ3Ss1FdBA+US1WuwvDrlbROTOH6D+gNJc0LvFYqw0+M\nucy7ABI2U6+a03f76NyZkswnkRoU/LtwRT2jHmbhBzetatzF+QdxWD8y2wxs\nn17KGAu3AJT0k9JXE4hLeCpWPH5qcpJhoHAhNbJ6WBxqVijlmZJzPF7gm0gw\n/ERg5sz0p+IAeBJvOT4cbhqQ1Rlr2MerSK0xti7tLLV6Y/C+fpEwQRuYA2+a\njEyiK/uQm0BF3otpXmllDiy/k++QQDO02cLXKPBXFwy4kugBHrFzQk8EdHwc\nur8mo6RP0r7WRUNWaJj3xyqi0m7UCOOVGd9nhXEcszKerytjnpr3SOq6PCq+\n5ea+mDZ2KDGVh+iSsn8/qRPVjaO32mzDSLGQzyzGhoQ/+lR/7doUnQjPEIFc\n4n8M+nrZAzJi4Ca4iVI3v8s8mrNYUIyaSe0yH08UZNjgGsJfHLyh5ttYCGj4\n9e+A\r\n=jWiY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-context", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "670a7a2a63f8380a7cb5ff0bce87d51bdb065c5c", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-o8h7SP6ePEBLC33BsHiuFqW898c+wiyBiY2ZC2xFJUUnHj1Z6XrQdZCNjm3/VuhljMkPrIA5xC4swVWBo/gzOA==", "signatures": [{"sig": "MEUCIQDsRlaT723anGJu/emdfmFBBEvamghGNEuykHaX5+sTMwIgLaF4Cw+aZTdQTTiG+SNRMheNB5o48qMnaRAy1UFb3sA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7435, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmaCRA9TVsSAnZWagAAtwoQAIBba6XppSyN5qKkj6tB\n8AVm2PDNs1gGA18+refqo/ALcEbAIf5bEQd46/tKkqY0lEDYVesEwo58nj5d\nDTMlzPD8avA9UPmHymnbPYFr2vGH1j3HKX78TIQlxhvP9UedyMHkZ8t7eJuG\no3KIlqg4YuoYPYtGAy+DO5wLypVoTftwFGl944MZex87QIUECWP0CgDUEjlo\n0DXUR1ikqZqimFnPuLj+vyzLzOnxnbEzWJqTtpz1iLqLNOqwqTUltjq5e64B\ncTAP6F9Qw8U6TSIk96eBbAC4auNX7+p6++BVYu7nvNY4jPq1KOVC/twXEFAa\n9ZZpSFgiJkzVZIJ0jAfK2Xh1uHOlrUbi6hmWeEZLSEc33p+JZt60SCCu9EiO\ntLN0FB2/A1rx0xjVqo4vk/fKbUecziahew34mxVCvzSDJPIrq2i0+krm7D+y\nRvgsEE+aYPNWrxNijnBB53yBIy3RHTXFwRp7MhlTN5JIZO4sIM/rY2M7oqc2\nMEmRYImEfKhB+RCxA6A7coaD34AOv+lBX2zgJdsOAefyp/QvEffJGvWwWbu4\nJvVE/uuYq8MsJ1NxXBM83lcCe1H4u32XAFV8ThF/BFcnCiz9NQv8V7fwg+or\nrL9KNHEteFccy5kqDJQboN1Mk7DD3QI9IX+/crJnFwJgIDbTpareZiAr2WX1\n7VeE\r\n=jb8G\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-context", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4aa5ec017635292e7ee7807c69df478445e89fee", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-wz/4ImXjGsqjzVKdSCpKL3b4bRTp1Bed5Pp09c3M9uxm0AZaAoIS+a6jRY3FApScc5OZQ9o6jwZ/fAjgkNstTA==", "signatures": [{"sig": "MEQCIFgf50KEVvUxzSeLgZ0KTJ8DgZeVTDWaI2/wJQQSGKuOAiBHvhJ51Zugb54T3PiIuh6CGvzMfBT8IxCHPd0sQtHV/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23204}}, "0.1.1-rc.2": {"name": "@radix-ui/react-context", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "887b47b006cab61147495d402b6287db5556556f", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-10DG5JDOgn1gMQT/8k07Bi/Urm06VgiPBoNJMNgf+URtUXRcsnbe4XgK8AZhuOkqQ3vWiuhikJyq7FQl2nbIwg==", "signatures": [{"sig": "MEUCIEK7kMwtWM57aZDbxHZa0GlPCaQf3yLc1RP/+gC53g77AiEAhGSzCFVNsGSVAQZrPo7g8jfQh9Q+EMMfHHzrENUcats=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23204}}, "0.1.1-rc.3": {"name": "@radix-ui/react-context", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cceba9c0380cac8de32e4df20c1219f678b9416e", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-XNkJ+zT32W0ebx2S0ZwPpQAxCZQuSfubiHVPJ8Vveuh5UPtTLAuY1HcA1elfMDC9nymxILVDICrDOXD6z9SqUw==", "signatures": [{"sig": "MEQCIQC1ETxtbXNYjsVVex9cXQt9fG/mnKUtZ+wOHQxxi//tNAIfXVQ9ILByOfEZ8XhR7Q8aQC4HZPbI2u0PQuqfY3Ixnw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23394}}, "0.1.1": {"name": "@radix-ui/react-context", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "06996829ea124d9a1bc1dbe3e51f33588fab0875", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-PkyVX1JsLBioeu0jB9WvRpDBBLtLZohVDT3BB5CTSJqActma8S8030P57mWZb4baZifMvN7KKWPAA40UmWKkQg==", "signatures": [{"sig": "MEQCIBImi9YaKwB+BVKSRHjwjeokG07Coky/5V/7m/RZ8Aj0AiBpyRBWdWvHmN4XRL41zdvvt07XeKSuwm4FkT7QgWhtew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23361, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh2X4rCRA9TVsSAnZWagAAdaAP+wQk1okk1/OdfPOJfWIg\ne7iV4/ZZRM0ajFg290r+A5974O5SY8MbE1LgzuMmKgRrmBiYqgUDR/Z2N/NK\nD/IWkrEUMch595MftH2XlxiKQpyKFxVY/s3WRgF329y21WX5TEI+RUMy09Du\nW8CkNPPyf2tm9wG9p2KrLSlyHUa8mSK7RDpM0yFeKWIgTQOPfTDfrHc89q2n\nfCoLdPP6rI18d/FFT+9Dr0rVyMIip9Qp3wEtCFNriU67ALBV/o9E0zX/oeMN\nK+88JuZwey1eTfnVIcLHLS6SkXBEnwQx5pxVCxts8BVZeoPgRh8IPFCS0JcS\nv+Uueux/F6zcPPgBov2EHgU4hpbXutw0Q62zFzlwOcBSQrcSg4NsCfAnx6yG\nhPRDOM9Fs0VzKLNzyDs5kgF+/WlvB16QYC3sZDQ+mB50Egvbtma5FaANATHx\nLB6LjCYUBfME3WBQdxQ9I52msTxQDZ9zzJ9F92nGEPYZw/BaJlq/QDTLIOTt\n3s52aVIGMP3BqwADgAQCx4szqfHdSy6ajLwUpHewFSUg7sxllk8fMbW9/fbp\noydVpruMrRu8qe1REk6YV8az14zu3VEk8UicXC7YtjolF3dynZopBNirr7cu\nThkSpdghZnzblIxuK+Zu3HBNSq4lJtYm95iqksx8ykwMgSjP6qktiS9IEQqZ\nsbZb\r\n=f6GJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.1": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "33ace8b6dc3b10ce1549564a1d48262b401f8245", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-2B8JzDYzluQSGJcQjGg+xl3Pm4SmZvGBooGASSlApTmDMZeDs9zKZHb0dIlRNzWyidARp/m8zP2QDjRoJumghQ==", "signatures": [{"sig": "MEUCIQDzKD6VHMWbGii326UPP7mF8Av0ndDzcIS+OJGNDpTDBgIgRU7Qmkfg13SmZVVsogb0ZrvD3nA3Fh1GLXGGOLaKhNc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAP2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrpWQ/+Ic9Wt8ELyklrwolc9cIE0J0RqW4k/Ztc7BYf5GRMdNQuzSzz\r\ncR9FE70ZJnzmvOb+Q+343NQIIcQ1MN4GYIgssqei0h5D5xPPMXR46QgOpeUi\r\noZ34ofZkhKfeN6HbOrvtDPJKEB9EdH0JW5QLLWl8jK7oTveGmzznmdDpbfDP\r\ntFcNmA7MA5grEsfLIaYFOqEsYXlNhj/YA5gzSKE5ZBBy4E2dgtOLv1iaMBS/\r\niFXEnu1766ZyFeYvHjXu6Y6Ze2RJT+Rxzcj76ozB/zbs2XFjTfoDxPOzyhNB\r\nk5acOKrG2YMMhk64y/eaF8sEuY99LSLxmwNw2FYf/o+dQfZB7JFG19HzjjJP\r\nl/jrZw9zqYSPBr6w7ZwKuDEJYh7bH/kNTY+zMQ9LJhzIUCoKUI/t4Dq5gy9M\r\nP5hnLTpJNGTVpbYQSY0XnVaHkcnVhoj0cwaZg7o6Xs1djwASVp61y+WHz467\r\nEpEl2sE+AMnMcDUhhWweD2/Pds6Kdx+bgfzOhzWiuss1DJXDtmwi5b26ft3Q\r\nxlHBBihkyA50a65bEAM+Kg0Erf0tr+IHJwsFBWWHjcbnloQvB2HRVdMSbtRU\r\nGlDKFjxYstpCyaESykPCBHgleM0+jLuzRHxOazUNsD5MZoaHy+LLsv4zi0Gl\r\nehTnX8/LlLxaAlrxFHhvkxapZ1Nm23CdSLw=\r\n=oqWV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "578ac31d2664d647d97522b074ea1cb8a1babaf7", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-7yvb6pzF+gQyJlsdc9b8x6MgSyJOBLmGiFKyxmmxpMenyBwBe1QBv1VSCxdqnYKEeA2Ro3Tx+Kbr3yw9el3Mdw==", "signatures": [{"sig": "MEQCIAssxuujfavxiRyb9CshhZ0gd2/UN24O9OTIsPju9+aNAiAbYxRiMSXQKfiSo79QHiEYP66rGkXOkxgOH7hPLsiLNg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 23403, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvCw//ZnX3Lcea4prjh5aOcS65qxVbltVDqgP7FS1aA+KiUIAFrgx3\r\nUlhAZE6d88PjOp+wd+LbAqxESiCnO3WsnFQHLg9EbvFspGOGNpDEQrRBrRS5\r\nNlqxRZhDuIUQ9DNCij+LobrajNKXrT81hUrKss3//eH215SqHciDYWGn8/8U\r\nR6FdE4xmsjmPG6gV/yg54VuEU81CAFaP9vYOpE//sg+gtj/urU641pVjQ2bv\r\nbQlym8LDVz+V7mDusCKkC8bdLFxHJjgwLMQohM26B3u3BjHmhrtBfrnndyHZ\r\nuC5qTCPIZVl66f9N2oG3kr9K/nT1oFHYdLdvmanm9Znnp9zCGaaVNp/n0ARG\r\n6ZoqJpecRwK8ml633rUCck9812vB/e6KQrs/6q5SXA+r9uc9pH1gxGoIS5Xm\r\nxtIWfpkRO2hnrJlKMAcCDunDh5QSSoPMuZmHlK2g65n6qvD2KfLRKrfV5fpL\r\nnT35wLB6vdMudih+5EIRqaDodWPHPWWD/rgdj7HFxhTd+yPzGrT8UEfoUqHV\r\n4Gjqd2nguC9YQyzoK/pr9CxLWS0H7r3wkKvYPlFRllONznhJNJODG4lZCX7y\r\n2F/831CA3fKdgvFz0Amxstb3lSI4R1T+s7U4b177Nh37CcIWiLUW/c6Q2mTK\r\n9EBBlzmWdQUpAx9mbV4Y8q6oGvvzbXLTQl8=\r\n=bzJy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff635f2acb28b1d5fbb84d12d54d0198b0068d53", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-UauJa+0duzRJ6+K9qR14Xyy9dXNYog/SpMvhu3d83IlA7FUaVEoR/V0ulwPSQXHbXGzaT4qzzMYNh9qMoRsqiA==", "signatures": [{"sig": "MEYCIQDRwCtWMTOzmWWFC3hfU+EoEj5I40cxifVnXhOp0yOkSAIhAOMwmyj3Lnpm9SFGzaNdqGwq9vUKcBAOZzcu5yQL3Ops", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZLA/+PxAXLCPQDFsNuUlCYywNTREgfaa3INzxNWbNt65z7SybnXcP\r\nl+z/zpTS00NFLnZK3c/7sfQYc1J2KyKQLwx/3lF07vYDKC1UcTw1Zl8904ye\r\nWcHmRTCDzTsJTiE8llSWFKK/S0XeQXFUZLuJGZTCL2+OByJDDyelWp+Hbn1l\r\nZj6VzcbKD/QR/1uiSU19sjzUrxrTcZHif/n878QzdnneHLWuhPYdpqT3UnGf\r\npoqXHhtX9BtofH1A0Dh90hOM2x847WsKJA19Zn0f9RYNPZdCWr0UAD6WpYzs\r\ndtKSovLv9ISAr0B0SXgtakfWqENlXD+GGmf6+Plm8CXO4rK7Z4dvC6sL+le1\r\nuLQmaP1BoNR5g7cSDsQCzPH5CvSZ9MWpkZ6T0/Z3dQEoIc8XV+zMyt0i0a3K\r\nM7iBhCoWZTCHGjw3Co/kU+yE8UEP/HCtpmK/N7QFJieZfKsUUQb+VCAQF4yy\r\nnwZXPFs9E+H2oGgNW9kHmdnBu5JjGTzslA79nDKyi8mAIrM/sW9ooBPpMyBH\r\ngov6ixCosUOANvFlwxEt1iWRl7WN9JtMnMIN1nqUnjrooVq5nbJQ5T4+4B4G\r\npfArF4PWNVMt+3P6mJf39nkA2OxJJZ+dnaSIE9QOKQHEJWEHnCVqz83Z3V6H\r\nb5KePgpAJ166snZ5nqz/OADbqo6LsyFjhUE=\r\n=ZR/f\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f7ba72271ad61fd8d0cf7a940ad9db37ee961353", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-nHu/UkVn+SGL1obbKDFS2/Apt88Dssev712nqs7EM5KN2Thkx49KC+R1YqEiEBxR2+nBsH4hULNU6eC5eF+bxQ==", "signatures": [{"sig": "MEQCIH/+fIuZx9ASeNo8cNnx/yMM8jbqjOj057Lrh+A9DbQ7AiBBS821eokTUyO8I/CNw/EZAiqxqgWGm4y1wClrdx3eKQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo72w//ZthyvBQVzKxuuxlACjweHMHDzNVGygKCoCbp34qEx5HWbGDb\r\ngaPMRtNynRd/GkMmdMjrMY75iy22CVL3Mr3eUMa9ZFJXpTOMxirr/7RJ7hqR\r\nXXuoCyYnN6E9aps7wogvEbrJAs5xF1trpO4E39aSFtpUXR4+H0JUoyXR4gbj\r\nJ9G1KZZCIG/qNA4dtP08gnC4hAw6btq0JsoektZLKhRb3Ott0F4rY2qadojQ\r\nJhagB/teuToqUaPEnE47mahUhV0mnxRG/CQHOpa0Y4koCFDCk2p+PIMYVaUx\r\nZZYIn/ctDWpRkpaBiBFQirX2BE890f4cwhiT76ynFDll6D2M8fqt4yoHdVYY\r\na250vwItcl+IrUJo0F+Vo/vFIdL+/c8E5ZPEU1g96ybdSfqeGGR8jFRAVCNi\r\nWRXOorSVwK8y/zZVAQA/vro9XjSZ9eVmUDa2UtcKWdmhCsGaCt60p4Wb2Dvr\r\nCq2SeE8Ol+Tp26scmzDIWApnPKm1t8htpTZt0mqukKFWSxcqh0q8vV/UzUiv\r\nagiXuIM7KdcKy44PkvJYZUvn6XrYLpF4zlh5ZzuXJ0syIR4oBqlCctanCBDK\r\nJXF+02g31aW2QXc0GxiXxASdeJ4R8+NtHDf8qoUiFDfSyo16sus2c3G4rhmt\r\n4QOJA1j5IA4w0mKEvljsGG/3DpR5FuDEBBM=\r\n=JGEW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "92a2e33cbda2b2bec79451173b20d9b127855e45", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-e13/ZcepDbihCHiA3AZsWnQ/AsQJOsxT4qXEt1XQYhaU6hS7as682z+EF0+os0CeJCZ4gphqZv57T4RZp++3Sw==", "signatures": [{"sig": "MEUCIGe3giGUTQ/5mfSBft/+c2zejpFk8E9RVH98GfV0upFhAiEAuE+LQnCHxEiw4tf5EZbk7i2OUFucSfL11Czx7fUHjZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqGUg/9FgvXFZuHjek/1HgRxce9/yOU++CFRb0/SBO6HSYBbXL1+YnA\r\nZ7iE0KplThP2YYUPku8052RtEfsxab9H6H5/pzrmlLU/d4PB5AD/UvLR86Ps\r\nqBZRPpBBrfXFqVCkbfRuzyDuWQDyw3jErzSWUl+JzS6S6JqJ8KLsuHnMFvCW\r\n/vaXEA/c9XAne5mWGZeggWdMDmX/tJHAV5dBuQgQoPP8kLNn6r68l81+dVAd\r\n8j5eXDEGwjAYia65oHVn+REvH048Z7Q0177vxQ9/adDau2pCv6zphCZXYo2u\r\nhrdd1y3qapP/fMdSgx6FlnwOfKcVUPlRRVg8B4q5t/J6mx9KxPP+8ZV2shph\r\npVnlPI1xZljpCaNhwyDT6ZqmyTqAsiIrrz/KxzioZxm/tRkczgasr6t1XTU4\r\nK4WOWtpDAKXlBQH7gnPSxrXKyDNO8LaOjIix8Lj0AKoov20NkSOeY+sCkPC2\r\n0z6+QUE4LeX0xdaf3SIWdw5lMjCBXt1HA7JFv6/jFV+8aF27imqzUZtWHpvS\r\ne4rRSt/g3+PXrMwhT153qXkTia9d18T+v68dT7CArYYRpub6E1xnmjKBbaUW\r\n+DGI5cBfIItfQKjyeqqfZOX/jiXnoEuswJjQ2w1by420wAZQ0k2u1x13Eu6s\r\nW04oA9CLeRDlygvDtoKjIQgrQh7tNtxVBPE=\r\n=nwIv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "870fa8feb425887b6ea37dbde0fac42ad5a72f38", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-omi6TBJbDtjprR0qRJNjZN4gyncaqELN1NR5HvPOhsvi6kump6PUEM+bzMGg6goSfSrJju8UsvKm6UMBtFrOZg==", "signatures": [{"sig": "MEUCIChLwBAjuPsjnvKHs1EIBzOFj5Ieu76vji/XbDrInJkCAiEA8EHMQ8NHxvogEtDP5uyBUxv3IQPIcHPjN2wVNIkxFs8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWaA//RiEUJgKyMIvgIM0uj32mRSQGdJSuNSckCtF2e9OlVvWfwCBr\r\nQMuQa1wJtthhYGRQHZyMzDg3HjEPZssQMDKbLRfMplhFE5gYb8SsNygv255K\r\nyNIFYfdsr+8tH704oiy7N6ud8VHgpDV2NIktVmujn1S2nVP4ubl0CQKHUlJb\r\ngkKYNFHWAaMwVQIM7JwIdEItIVrUs4ndiQaEd7VWL4twr9vC02cI40hiUnEK\r\na2hWLlkq1fsyY1r6C7GrgTKZBdTksA6UEa7aB38TF8xHN2kG0nPrHdLHqXkL\r\nMT+kMP0b924hyXx3cCM52jrjO0den8H0xdkJJ2yC3hbKwSnJYaXE7aL2LGAK\r\nR53hL09D2qfKirBeElB7xhNaywVVxESVdN4hyyRAEDhT+Ccv02H9edbuexK9\r\nwM7bRtBKCkjprlDyoV/KR+QRbGj1PbpYVLT0Tzbjj9Go4O6cTXN/LJMDkaEX\r\nG3Nvat5JEODhJo43OhzPGR0UrbjqDtYxtAdCDObOAG45WRir0KjI2I9fE9Q4\r\nBdBSLMo/JNJTWLdCqxQ5EqmTyLBVMy+plVckWoTjXjsmIMbHzoHrZhAby6rZ\r\nUO7t7B2vrs+2FXz1Iym+Qp/xX+4oeRnYMfb9DcwSn+eM31sIHztYqi1uJHwx\r\n7UPAmOsZiqkagWU0RxKbSpgaLW0uuBOxgMc=\r\n=zS+8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6a5fe5009d7dbc027cd3cc79748d1716f7a8945b", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-IuT4z3WU3ThSJKRuU3br7yKQjgIKMpOj4larryOPdlLgT4cwcY5Ny1hU56j6f9XQQ/IaWejwts6733YcV6gEiw==", "signatures": [{"sig": "MEQCIF0np/DdgAzZa81wlxGRTwsPJ/5W8eH3EB9qhkKnYYijAiBYjvHzNJYnT3rwYyoyrqMhXbo1ddoDDSmFSaPAJc7BEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqU7A/8DYEHQnj+4WM92W+kFIjXCFS0xsDSRwu+rNANx7FSyMdxXJQS\r\nzL0GG8suVq1LruHGb0m0ViKkyOdrJtxY48Dhw0W1TyjSChe3Avl6IJ4aDuR5\r\nyWaqTJ7IiP/xurtp+OKYQA5mVNTHoHyY9xmjs89G1fgxi99Kf5DSvOKBVehi\r\nQgd7hJprGCWWWCrTqHbUcaHAMFFM7tYukX8nRR5dEcaGbWzLHPnDDgpTeY3w\r\nSz0Bu4cqmotfsutOS9BvgPiHgvjxI8cCqyxorHHfAhKLmq5HkZTj77O67ruq\r\naWEHxlM1JvHJ7V3KmLxncp36bZBHNGIjoAVS8FCM3w4nKOtKB8Zx0Sj84efX\r\n/H/hzVXQFfmKM/phO4eXn07eoQoxI/xxcMvbSY1df/8dorSlMtrmZco2u8ai\r\n/57Bgjge4hhHEFczahw0hbP9ts7XTnOk3A/w9Js4heRBcZMm7MJbbyt8QP0h\r\neepw44HfHkKwVDoC/wdtAU7thcJb41ti67Yc1rgxAH1N2zGEXWK/s+Db566k\r\nDG8UDkMFCTyq6HyYp6cfeaWwhYJpDvM0BAqkRiojZ+CBQYNNGXUS34t5F+ot\r\nFCOF1Ox9+scLET0tAl3YFVjiE2L2ku4+ruzElk6T11R0fMx6HC1HE5HtXWQa\r\ndJ7cTahP2QRcc5BOaXnASxV3yfhNmyK2Fck=\r\n=K9TP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2a90ae30a7c5a0e4702b8dc9b0f43d69647c6712", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Tp1FHGouFZUD5XZeJad86tAuc6kmNmmemprfeAZdBD8aoAikfCdpBUIjLmKkd0YDkCLT7OA8i9tIlJA3likh8A==", "signatures": [{"sig": "MEYCIQD0VATciQeD8Mq1DGv4sGjXZSQRPc8Zs6E5/HyDgOJRywIhAPk5WQ72S0j4zqnFF0tO7kmrHLW6qZ+IDLTdDSQTuUwv", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5BhAApFzGdvSGCnUy96qgGH61NRqo59zuRFU0ILU8R2qu29ZQ9dIJ\r\nQQssuAlR0vnzRt4TaRRlC4S728uvT1x/a0P0GLl7qyarOc8FLOVgqHijeTTS\r\nOcuoFJiWojlx60IYB/5qWQYELjHZrYgL7jAYPq7sWzuJhbDnrphbdumhNFCT\r\nmgm7zR8NBjqDR52fIgGOv5+rDfi6LRSfgAbscBQisaUIvjYLWn36OjwQSaAV\r\nAWSL8vJzhBLcvm1Dy6TIYQ67vyumH+svAJD1TeJqGWvui9hUtBNewSDihFFU\r\nODNjQF3exCq6xf3k9SeDYqY2ERkOXPtkPY0YC/XIhPYdP+/vbD5PHpsSDTJL\r\n0FaSgTOhHJJO5Pn+gQQu8ocZrK8UsvHMtul3x8F4owBJmhLtHoB1Lukap6Uk\r\n0cr5q3va75uETD0ydN3lVptgkoTO3iQ080Fofah8CzoBuHC4kgydlDiTLzi8\r\n5qaPzG0bc+o8suzds4uKxArcg4mYHedUN3bysz/UA2tRidz2+h90YEmdiwbp\r\nQYqjmKhbxbAw0U70zOv8polG0zKcBgghtMQPAstGV3hKH9y5KlHcx9MYMhrp\r\nceHHRM/Y4InugGWMG9lmg2lwqg0a17XqQQy5cdhFqFFl7wnFQFCaHNDGVRjj\r\nZQPGiBGyQ7PuWY3jA4jTDd06jp37FO/eumA=\r\n=Y71C\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cd4ccb64be4125e39af974c51ddf6cfb16d40f77", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-D6sAs4mFrkLUNDRUT7KHP3xQFS7NCPOHx5qiqDqHZuzdAJ/KmdgGIqvQpGMR7Z8xQKpTwV42xARyhP4j/6IppQ==", "signatures": [{"sig": "MEYCIQCSLGM7fKbqWOdHxJS2rz4xnCREZVZRPJ/0cYUFDCTF8QIhAOFl3gj7YMinjTCx4D1PUOvXqnCFqRfbnlcsuV25T7OB", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37165, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo71w//dcZGFkNW4bDXFGJkXQTKPW68I8eIrR4uQlB0naoTKbtGJGqS\r\n38dRaaEk0Bs1RHnUIC0pOt10y8j3SNQsA9xv+0rWo64iNKVGj4pjLu+yEzJt\r\n/sA6VZKzudHv8vOimQ2XP3wAcc9cAueaR8+jjiZaSqGlEqXCaNRACP3GNlsN\r\nByDq/g9TLBfyFoUYYZPyYh2SqdjvSZqXsXyPZ9i9N64kPBOO4nYB8btQxQX9\r\nhtk3IybJ9mxFXdS5yvSfc1IATV1gZ9coLxiyhSDTwd0ofbn/IHQGMbgesXlf\r\nXc9B/vaOjwiBKENZA1r2F9P7Pk8B6+oJObxbhNp7isN7OKCTLp+UX1JghHg1\r\nVvXvGje6llPNUOJ+29zqlb2turXmyIPSPrui8GpNZRfyAyqoQ6nW+fGWvLpa\r\n2DW5OkdIGOWkHo9zuBN6A7/MOAO7V1r8K6zOkN61HlEXqwtcl3ql7+nvmRL1\r\n2O8Z6gcrhhMH/T93Ix6b15jYGTWI3RohA+SBuPNHe/VJqhbgU8SOClBfEpuB\r\neAt09o7D27AXN9iUkZJjIP12MaJKmuvTzEdEbYLbS98xLxQZ4iefIChhGcQb\r\n10dugN2EBOLKKBlX6vFVBM1zTCtvjh3zeDKwdh5SQ29TNzHpJBekGH42KUkv\r\nsX/lFLETCnqTq9daJ+JsYceXsWYh+5YIk0g=\r\n=XjZM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.10": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.10", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3a2531b6de71adeeb354004b2ddbbf74b85acd43", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-kHm/0A5zT/Km4947Tg8Oc4VY4UhR8BXqJzsRXv/heV/h6NMO+G+T3S7VGHTEkoX+KuocxiX3+9nmqA45o2cEsw==", "signatures": [{"sig": "MEYCIQC1quYE3/3sJc7Ro2+eKe2qKKwnharxe3njcQR3QCsV0gIhAOogHBRFSvcFCpxbGwy1ydl4IDCNhbSWCmxmljifcjaS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN99ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpMog/7B39ZRjJnErS1nkuvSg1TVYfDAoVAdUUMsO60nMJdRQyb+DhS\r\nrpkiIzHJeQDKWJ6KUXbYoorx9U9zZyf6LLic+/uMuOEkFHX/38WrVL6aDCaF\r\nYRknAAHs0IPyzjPSbX2ngc89xMGeQmlYsCEYcT8lZmrlx/KgKraC4Q/3vgmU\r\nF+9CuoqKXwfLdpd8F6rF65FdVJXQ3hppyWArW4/Eo1RQuPybqdVTUIYc6m8W\r\nbTBmfO/YDRae6nbRzkjJFSDYcsz14+2kZzhSIbCyFK5lyYFseEQ1DeizRfph\r\ndUgPU5V4+n0D4lqs55UWq3vYEbVo5/wTswomp8rLo7HqWqvxBTLra6mREkLf\r\nWJQQSgf775ATVBLJ6DE9I4NrHYbxl9sRjaN82OEnMsjk4JvxYrITD8EZ5Js/\r\n/kqBOuxY+3aLB5pjaXqPz8mhLXE1cYhSFtmAnl65a+yZ/XlDaoKyrEjyWyQO\r\nylhNaPiQ4UuGGjn/H+x3nMapc840b14vJebgGmldpzw+Qt5iiANNgrMN/JcI\r\nFVaeV/5/lGrQYUfbo7gysTVziOErt+h8lNzubWwOtR5kG0DMkzkaukJu3w9Q\r\n8GiELZqE11N3Ji0F79wxUBcwHqN540Rqphl1NfALOiFcf3K7iJBoRArKUakh\r\n6iiVXwflmqcTEkzOpiKqbHOhfyFCefRmNfw=\r\n=xjJ1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.11": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.11", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bf0c7ea4abe90bc10a287187becf9a5658138da9", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-0k7WqegpEVJ6/lr4gSD2xLUMMImpSKk0VwbKGk78HaMsYJ06gOU0fjg26jWx1ALMeBb5iYRNF8VdDuy+s+wNnA==", "signatures": [{"sig": "MEQCIDwr1XIZAYOpJYIXrAZ+uZUxIAPmIIy/6xKMJGFetdEGAiA1YJRdiBh9gBROsZ4jvfpm034nLJQB7GqQN9eAfyAE0g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSk7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqnvw//cT3t2xkajTd6sRgEeVYM54rpTl06Bl+mPN1N2Qw5VRhHVKic\r\nRyr+zaH/xKdzIV2uChsHEcqE6JSfcOmg6ilYk9RSN3hEl3/1Wvi4QYwgrW1L\r\nZzLw7ElE6Y7nQZdYi3+yqLLP5j7jWhR9KFYT551UgQcLDMR6Zx4o/6/wsHfr\r\nFHzARlJKhphSJYPGWvoA3ceZlc5d7kObVXpmE3I2d7hWrQigI3HQBCYIcKtk\r\n6sVaqS4gXOkDfj1nBAwo3yYMohFkO3kqVmBKpqMNSQ82nAzvXkTVmEhs6u4c\r\nQ/iSJi/+IB7ezHwhqCb4Vr9z6CvmgM7PoafhZNiSJBoOX4EW/00yHSx/Dxwr\r\nAI3XWv9vet/ku9PEpKqIOfWB/HMY3CVNEN7I+bbeeei949+860ObRRUkZhVV\r\nHVE0lAkpoCGKKHB6XOlj9U2BbtfTgPus3A3PAlcGoAFFpo9pdDuNWfZg2rZy\r\njkpL4hJgJyh3Kj/b//pDb/kEoK+UbVMHc6xc8aivfi1b6c9b6M5YM/HiogO4\r\nSVPvz3NqJ64HaJ8DPOo0vLDr1doBVef2JZS0nOIJcKPZUYoSjh7OfyWN64G7\r\nZk0WG2uvHWnwwvD4wetblsvReASHx8G00pC8TLFupDGRXi+5Er5C6IRLhddd\r\nSMBwFm6OrHs5Zf2LNYyRT14lLYxHH3US/AE=\r\n=MNGg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.12": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.12", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4bc77b79ccfa85a5932d6e0bc4f43b6f3f0f4fba", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-+IcgiDVNuBZitEN+jjlAowTpExUa3KAd3TmrJVbYTZEaZ52jlzadX+7Jl6lXSCeCbHoszEpIcE/m0JSB+glYPA==", "signatures": [{"sig": "MEUCIElRE0ah4BV3/QbGgEu0eY9ONjQci4zKdAALCap5GNC/AiEAh/ucFZdKzEh7Ewt6aIzSLskM0dcBTKdclTnV4xKo+Ro=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieoftACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmohtA/+LGyiGGa77ReaBvvott5QgIJCFZDilHHKJDwDb71DX8J9EIci\r\nPVxNPIInFo1Y2tBghGgAiP26Zrgx11EGj8Jow7KlbI3+P7h2gzadwaYHFHSq\r\nif/Ym7lo/nkFKlAGgDRb6ehsi23X1yZsBE7TfDGk53Bu83JWC4Elh0Y/catc\r\nJO7/IqL1n1aMrKf/uqbifdai9BfaCXmCR8Ey3OsIEw9WOv1sPj2WQWuQlROd\r\nGIgxUt7L3cFhrk5FIed9A9VtxZGkCW2KyPsrZWGVsj/WJtMhFoip68zIKiRS\r\nRNsf9VWenU5weRnOnwHpGOqT62cJPzm4C6NBN889ySTiIQmY3TwN78pmN7HK\r\nvotv5eGh9G/B+Hsd4sDNEhoFGTGv8+8e4QAgdNAM9CYM2Isya2eWAY77tJre\r\nmZ5uVNxbt5u/JDY5Bcii4XbIWUntIQDXEgd65yA1X/LmOefDUzYWsvB+nBId\r\nlfiwMeGvKoMDQ+GLbDxS/MgTapX25fUwzfyrrFh8mlLUfDVelM0MiI74XwNl\r\nsV80mBqunLUG4Dngpbn/rFvxF6LUKz4Tq47KPyT/+poenUqfYwDkKMDfOogG\r\nT+i+Nhc6VoBIHHtfnyxMnOppCiLRyIfD4QqYdA2LAyOIASnNTWtlOmAcTepV\r\n5/yC5bSHQwpcGXn62JihariQKYi4TFiDToQ=\r\n=2LSC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.13": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.13", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d5f7117aa7db5ae884321dd0f921ce777e50e20f", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-aBrd6j4gexK/uisFCJLvrmsbSSUaKiMLNJxARMf9dhl+1rJhWWR/QY2kSZdjWouhxGNlrxuTFvGnGDl5vN7A3Q==", "signatures": [{"sig": "MEUCIQDlQ6DWxjN5qwIxVSbieZarVP+PiT7hTD5O4NiFSwGFbAIgSQOzh1soE+o2rRxMQmfkLPh6ADsfyWuKociItGBxOyo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepI2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRHg/+LsAG1PV/jSjmpoaV8tBS0UaDc6zFyPIOAFO/Wn9/xydXWR58\r\nvibxygh16nt5aQkyGS19uU/F0DKD3J+aNg6K1LkyO89fZ1DEpivcWYhqBEXg\r\nN3OZ3onW65OHY4ybbVB7DGo99Kfj/jg92CFI4t5D+fM86Sjo+NOs4jYTQHSr\r\n8JSBzMdrgYcBHACaKggjCLzXFXWMzJ49Wp6cCUAsZV6E2v8911B+Auvix08y\r\nPQty6Sz6qQLN9puOK2H7yw4mArZCXqGXwXXuI7kWGlF1ojCtjaOf+k2qC4jh\r\n2otocqQxtacALNwjvywJXUXj5Mu1uhbfTVtjd2R4qpJ7UMvN+shiqCAN8sjn\r\nVDEezrak/j+9mwqQHsYAYOZVhOBWbHHlrRn/4UciTxRwk8unU6J/VFm3aYRo\r\nvpUOKaGstm9xq7DaLZtWAGFYSMIdJlPtyvigrjayDClp9LYw6SLy1NT3hOD4\r\ngvIOsiSytCAK36jzkKrv1eifuEAG17MjoTCc313Ad8a0KNkC78U5XU836gy2\r\nj+FPM+v+mJpGf3Q3h9I0mvC3M8cVME/ifp10VdBy1O/DSFfPxFnYk3QpPOaW\r\nhmuEj7NoETYReNqs1IRdxfDkcR8ZeqkA+z+zZFOB9pdNMfz7GNKK2X/akIgK\r\nt14oIEJa9xuitF/W8W8qCbtrNZbFWwwCc7c=\r\n=66/N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.14": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.14", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1b365d3fcb06feee44209fa6875b7a2bdb0694cd", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-itgSm+JYcgf/IyO7Rdrhq0aIuaTIBJ1x4AqqLlZswwSq2wTy4VP+GZ78x2CNNRRjh+kPVpz/nW7FmGxs8JK5cA==", "signatures": [{"sig": "MEQCICCyPLdsC9ShpzmHwwEeP6xgzUETJZcFViL7iTD/HeQzAiAv41L5zCSAYUFRZMgjDLdbhexqjv1zHIDzmFgbgukM3A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpXNw/+Ih7ta5yIYvnVdfdaHeH6bafNv6PbsSn6Gl+5lk3aAjFw5mtL\r\nCXCzF55vYJz4/xHU0Bb5zgnZR5HhM0cCc4HtXfJF1FYK/r11phpxgGkXwtT5\r\n1pWemQ7CX9d7Er1vCgNKjQJT6uplQvIcAC/CwcjZ3z8ZysJxdcwIywHgqm9m\r\njWcnr43f6tuPDs9pQ496ojD+XdZX2/o10BjaIHz0uopT/MiCAXMch00Nc86H\r\nm83tZVMAw4EZUWhhYzpL88JHNGrXNGTGWexrDcICMTfpYL0WX5+7lVSdUhHU\r\n6znicUXHrosVg6AS2D7Ic33mjgbe3zjYPPN4SlwsG0Ou57HgkeC7aU5KGI/k\r\nZA4SF3KMsDrsYVldJ5X7ODXrWJIm1v7vYZ9jRIwf1mQf20z8/TuAM7HqMEyp\r\nTxH/71gWW4wMg2BsG6UTRYgp60uemxIFXy0EvEfCYfc7SfKMxjT2EWkZ/ALg\r\ntnQ+Aj2W/clCBDXloSfimSDlRNqgXbt8xvmlB1riWur+EoyjHdhcexdB6BPm\r\nILChI2/9W3d5QGrvLR++PJd/BDpRR5r0aYgfIH0GlTYAgFcWmX5TUrqViAMG\r\nZ5RnmzWspw5HscQDAgW4AY8hqHOfo0GTPwlQP079CieuN40SFaolUF7xJTLl\r\nElSKlke6FLCLVJJzJ0vTtkFyzyEb1/rLzMA=\r\n=H+1c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.15": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.15", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "969c92dce51b931edc3313e8df4e3e11cabfb0b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.15.tgz", "fileCount": 8, "integrity": "sha512-SAIiO55PAWwKjnilCJkpFo8v0Ru2pcg9omjUJW8Sk5vjeJn1FEvlv8g6NGJ9NgasD2JgmLOJqGY9IdXD2qLV9w==", "signatures": [{"sig": "MEYCIQDcvxMhhrSCHIyA4u5wxrqhlgYpOkDTKHNrqWmNHSoirwIhALekXxUitmIqp1PhTsr6jUDUtyk+w2Ceddhj6nrMSbxu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAz3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo0Bw//Uk1MyEMTNPi6JepIog+C+jWXCM4MH51ZDUSn/i/tztbGh3lP\r\nUuF/O9jIp9U4NU/+9z32UxIL9P7E3Sd1niLLYDK5NKGDGiwk1iUTfip+QKpj\r\nfVZrO3F1MTFyp2f0HBWivy3+g/Bnqxoq8muE/rpf/tEjeUIM/YT0MOOIVFkf\r\nBqkQD96ijOjdLogFZF0Dt/d8lI8IikNm/I9QiKl9oybWVL+6zO7Dg56QcBnz\r\ntJUyJxaeTRdSF8e93IX3YF5wCWl6Ls7HvdR/R6bF68Bk4keVNxErQWzIHmFx\r\nHX1J0cBrzLIO1JE85/htF6Qxi1v7ycHMwMh0oqjz9J9fsgBLrh01W/ThM71x\r\ndJvG4fgc9YOyVECnRTMzuDdcZnT5/wjzr75LI8GW475O9ekPxgMXEPUZ4xsC\r\n3+6bx6Elkoar6r2DsUl8YISRmFl7fKMTPwEZV99voiKqH5B7HfA143xnWROV\r\nNkrO433BuY/jQLk0vSPNOEeRvLLU9FOjp24kL0yPULtB84qH2bAjjhyH8EKv\r\nSXGeIkcDe43qSykPFz/AjC83Y2U2Z3zQbgPoFmRRES6/r5qQpVCHieKuh9qW\r\nVGb5F7j4V1cCiMd4vuC/exXrsMsMqlovu3ZnvhVnuMUYfQv47ifZDOKMjHNy\r\nqFnqbaMWIA1jL/i3qaTcxcNKXIzyEKYJ0o8=\r\n=nXFQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.16": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.16", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "660d096d64e2f3bab4429b6558c73a0d02fc7444", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.16.tgz", "fileCount": 8, "integrity": "sha512-/IhhhjdEFt1Wn8/XyoPPQ57MZLLI31xYJ4H7sKkU65t/BqMtjToTR+ut6HOWMZYYdLArEZJ4PJu1hAe5jg+EEQ==", "signatures": [{"sig": "MEUCIQD2uHqT1mRwIxwOifivFDjx9HV5xSHPlR/Lycy5C3vyuAIgcZ3jHut6bfQjYNpaWQ7ZYLc/8hRKQukaW3suh36cyVg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq1vw/8CacCbyFwSprbWdP0yK9ebjjxWFCI1FMcGO8QpwSohCDHomo4\r\nuEuO1JDN9wt/Q6k0B2DgX46gZr1uzbXCg1usop5DiUH8HgYoga8oqkHWH0jV\r\nkrZ42DIsYfrYXXegUnbT2W+8kZQVQ14+QEWNFVuX+bTrtvJXN7JGfUyNzIos\r\n85350q0Wju52ZpJqlx4dVuqVMpgGHUchCAmtGA0PPCbWbcW4ESrs24NnCIr9\r\njD5zBcMQwwP0N1oBrz6K0mNClyIDKDpd1gAqmrt9P8aJ6Xfw99UuVfQ+jhg5\r\nFKsZRbwUdYh6LlQ+fseECdLL9POLYKL04mVg41RiIp3Amo7qFySVGVpt9Eko\r\nJ8sdZHesMBSLKre2Icbvs9WoiaUcYa8x5/uJzDRhxVTOEDPnNyBzvJId4SUn\r\ndz+zRRM2vcumAYfyArb9p4AyDgSGbUmOngB7lXq2+dgM1HPk0Z2zygqR2EcA\r\nfhvfs6bvCrV6X3q65fpVGx14gOuTxBl2s4IQuTofj4T+37EkD/nusD6qdwVY\r\n+pYb+ZI9amx+x2B1Y4BHPYm+MzHqpFgx4NVuRBqbAMu90uKk2md0CfdQf7DT\r\nAbBl5aj45F6UMOGW7v1fLYgmavUHbL6WSuk+GcT5D/d93ZgJOBg7KA8hysZ4\r\n30L0ELJkMZ9FMqJtcokNQJhpR6xpGjqdNVQ=\r\n=Wj8h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.17": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.17", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9404b99c9844f4710cd5124afaec6a41ee7f28c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.17.tgz", "fileCount": 8, "integrity": "sha512-7JhoEviMRHnJHZOCgDY/e6EawiwZ5yjxLXvSX0Oug9XYTmfW5PdCXPBj4Bi6aBiMK7NQDOs2/uwSoS2cDLpmkg==", "signatures": [{"sig": "MEUCIDINrGEx3X24OG8gMOVI4d1e6TRg+ILqAE3lL2GZsVrOAiEAnupCnVVm9DUuRrFcn/ZEhfo0txjd/HJYvUfzlKEYAUw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp0qg//dnm8C4zaIF61EowO9Ck8EVTIyyFPT949dbIuSEDXeFfth1fs\r\n6tloPHHbKqSQMrs7wxwXvbFAHfFtn1JqWxOhLKBAP0pPi7/FG6n77umIsvhq\r\nFsARIn2uEgSCBbvUMqiEtbvSk6TzLWNufagoCrji6nhHKGcd2mzuv3IFQwoR\r\n3iO+o99ghKhk1t+4LDEF6Ts5FpfwXnUD/QIEa3VLrQVrrq0vpS9R4yjjqN+1\r\noj8F3YavXSGtN7pA2V++fWyCfUX4OQLAG0FVbkaeUMiyJLKxCwyNw7D9qUmM\r\nnESGjHcLU/IKf7ZRVk2imWT+4iInnC4IJSMEEFRPaCTTlIKAFe70x1Ldb+v5\r\nzylvd1taDm4cWAFl2fE8ZirRhGCLutRP321y8HxNxLQLGeG5ep+q8rKUnS6P\r\n0wlcljtPm/Itdcl46kyiNsKpD9OQfr2inEMjP+P7VaH1ubqQiduBUfR7jiEo\r\nkkhh4SlBdEd1gjM4YjFCp/FNOxQosC+xfCak4lKtvbQiqKQI5Ppf+CtBhc+T\r\nWCGnNs0pNARY/ByTrghMx9N9qQGEK7XukSbhlMQ7nfuIBNcD3UPSAvhrOYFv\r\nkf9kEb8JDTqUS2urUlxe7jo+752CAhjlZWieoSD3UgGwDoj36qa1zukc2uR8\r\nUqECBQJHxTP8nIccjv/Qql84qDRLYBrk18A=\r\n=7/ZV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.18": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.18", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b5746bd609d6d7372ae6f5c778c7d5a6c7eb6834", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.18.tgz", "fileCount": 8, "integrity": "sha512-ZQD/FTtNb1Y+O9kML5RLWYzpdbwrhwQHJcVh/BSeaxJvAqQtWqfMlKPSKjvQgkPMljWK7o6hqUptdicVdNpIRQ==", "signatures": [{"sig": "MEUCICsJMNxxxkKo3RQzMkuGQdKkdLV5fWpx8FaeQ6FJba2HAiEAhN1K0gpF85RUzrZO2oXo6IOijSHz7PQScIoXCYGXc4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQz2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoSiQ//dRpz16uhQ2jV6U/hOjBYJSMjOYpSn9epfVCAWv1yGE3/59ko\r\nAbfc0IDMVewD4sFYyE3oiyMB8p3gd2ZOhWJHXw0ffNLG6v77PAX9wWKzq9QM\r\nMzrv9T2dpKPyt5kWcnKAsLw3Q7TqrYkOFE7Z+Dwrkb1SpXEOE8eFVRkznNEX\r\nNHqrGpPibSaf8Uj+0w3e2YwXKoXQtp5P45JHuiLXkpYRYPCBTvR233L4asGW\r\nKFr7bFrQYED3d72gyqDFHBYJO4LyVb2dg9T5Tc14nR0LBkmVps+lIxrQhMd6\r\nQVx06o832JdZR/Eh2rXfHpD8zGtGLkkGCMp3d/g5tfXkYrGtMeiMAUpJLIjB\r\nfF1SB652aropv7rdgmx97cmLh5wGFBjlVlaL4Wb2tbAV+C3vBGa02ZoNev9x\r\nOAbDdKkR+wl+iNdknptWVItxVMADFEi3wPHjoY1hOEvE8+GqncxcvVIwljw+\r\neF8ulQXkuu9y6FzL17cNI30jD3pTwByQNDLIBui8F2INTUx+H09FfQAfYa4I\r\npUQTK/OfiyVz2bqfg7pjlOLt8YsTNg8m7XTmCz7+h1H9pdJCJPQCsSGu6p12\r\nHFN+fAH8mM2frd+jRD6bz3ZmvTO7rpYmZmBXVbVR+XO37i5ma5ElOjFhrxVS\r\nuqURZcQ0KiE0+y9jA6TDTkZGefWCb1t39JM=\r\n=L2rt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.19": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.19", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7ff09da00418da843f4cb53a64c051e18a29977e", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.19.tgz", "fileCount": 8, "integrity": "sha512-iUFUf34wfDt4/oq9fzIhI02/crwcRCTXO6fTZ6zLX8KQ94e2F5QsxRWhK+RKT+dZQ8WKbDkesYtEC+8lEp/p0A==", "signatures": [{"sig": "MEUCICoo1Z1vq9KQdXpWydsD5ZXDmObZa2h3OLnSozZGrB+jAiEA+lnaI00BHSO8efAgplJBLgwmYB47X5QPem9E9WAB/kQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0sQ/+LZlUjoPHiJ8AI6JlPDuncjimTPMC0cPbP2HbBbgx6LpInpb2\r\nWo12CXWuADXn2PHG1V5oX1htqZtrUJhTMmnoRJKw92GpjFsZnw+BvU4tIU6/\r\nCpOK9jbYUXQvmFpKWfBpctugHZTv5xGPQGBshAPSmjDaTtw11kYz65A1N7qd\r\nyTMXNXu6YopSdplV7CVB+uaLgmNlzqGZdt8TERqmcNyVBw0airhygL7QUNK4\r\n1GMq0fl1QdIoJqEuCfATYjnJA/luj5VGtd3lRVs23f19rqkX6OXRqDpPbY9V\r\nL56wTPP8vmRcj1X6o34o6HIcMTM1bJARbJAVuI5XjVFTdo50LIM2Qxvoy2eN\r\nNw7k+0FnD/VeKhBdMPDbbphpWBgDerrn41ZC3asxTkH+lt68rqgNK0r8v0bh\r\nF1k83qdZvnW4lnL5PHPbKqKPf36QE+8PQGFoy4id/tWt7+7flBcWd0aCAPbN\r\nYVVtKgJr2a7WlhEi2Jy267iUA85uVSpT2A0rn0orKhmzJ3oWH5ZZTwRkJIAS\r\nsfa5P3IIqm+bSVXI5fP5aPXCaq8wAG/NHPp+F623/4bfQi1uAHCJLaaTA2Gj\r\nw2nepOmlheTF8JoCdxTfYs9YxrhLefREEuH0A49xL9ElbOWv64xdkLIfG3Xs\r\nhbW7EPkagUTbIrDLyudyJ+ETGBrvemIrfF0=\r\n=Q66K\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.20": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.20", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ec4d7a7d58229d3ff4fe04e0ccd18639ccf64ec5", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.20.tgz", "fileCount": 8, "integrity": "sha512-1/QUAl6JHyoiTvfr3NYK+9aN9l5QcQlH5YufhykOU4tp7BaBGXXDcnQsvXoeIXCR2QrRd2uxP9ndbDFRubEApA==", "signatures": [{"sig": "MEUCIQDSgsd/jaA1eOf4TwiactDpVqMzXFNcnuffImMaS1V9SwIgKNtViVBgjA6D8iqB9sI2pHzVC5nCDg6Pts6frPIEs7o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpUZQ//UUtaozgVAVyXp2IeFLA5rlnHZdbQQBDDCrGCWLPHfw6CB9hv\r\nUFnKcw/7UASCZSDU3gDxsFtjLtUltrr0MXWcUn7a92drGFpOC/azNudXqx+d\r\nKzG/qlaaAeyoOTxeSXLDiOjb0PgkDVIp4403R5a+03zkraEDwC94xJkR05tU\r\nAIM2h4gknePDpHBgL2r9ZiCHMYe2LqQVBWAlktp1gAIVNiv90qobrO63XW0h\r\n3c8ECWzxp0OHKZ5tR2GtUNG+dkB2PWRcOq027YpswP463YNahBJ8Qnb8Hfqx\r\niMalSUOZaV0dWRFZ7Fj8qUrgJOwDGVLGVv+LtMea54tl075+XRsJUvJr8Zrv\r\n1X+rqhRI3hP8XVQYqeyFWJ6PJOzpmK+1KcYvdmruVNEMJGmpYJEBroA9hXz1\r\nYLWBzquD8lbyovY9sJ0GawvTKFnguYPGktku/XYtwR3QHzbvTOfXh/JqKmws\r\n8+7DrzdPbFyQTA5l60tOsmwwhdlfC8bDHXlEnEKKNwYgpKGSu36458z3aQQr\r\nP5GH7OHK0rVYIucK3w6yXCY2EbHNUHNairNPC39Tpsr2thyfyVxwRl58s5YN\r\ninylSyltPdjDMVSwOGP4MHLkAUpb2bNOb9/kkAQxPSS4iAEzrwbay30YmXOk\r\nV0KIAUmG4FeRRI7rXD7+aX0prLHsWP0pQsc=\r\n=oZMV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.21": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.21", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ce19ce3cfec7c50993a8bc675e778c22034a05ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.21.tgz", "fileCount": 8, "integrity": "sha512-cOvdEr8AiO5f5wXbg3IEg46TiN+LgrNSnPg9yqondNKF9rMuIvSzMtlUFirOuisgYa+ADG9eE8lcC9v6ZZxUDw==", "signatures": [{"sig": "MEUCIBTlSuyxOJ9M4W3+dLV0O58NCIzwTUoX6Z+2Y38ozc+RAiEAoG9euK/N11/bHKYMGIuoXOoTcAsP4eDut28/TKM4JIQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqm1w//TC97HT9ApM5oJGGC/2RBAT8SdANTYApuG5k+54vYMUb4D9K4\r\nIHQpnU1GVhCDZT0iZ1IAav20sRMNcREiSMbQzQA+g4TL2lmtD7FjwlxQEsij\r\nAarqMgjiVry45zJ9eJ7sq1ZJbcd1hx5Pmlxl6oXhftpsDAJdWIVQtdXVLj9T\r\nT2L4xuflEF7lDusOYyPrhwzhAeH1E6Dg8lFG8nidagDpOHQhBTI+fC2mSkgd\r\niA4oa/0nhe8cwJd2a9Q38dIpQGyxr7HLufF2YepJuSz5xEZBiMkhUwDW0sBv\r\nDb2LznnGLefToS80G9gv0RF6ys4ZZVfeKQ9992u21R9C5GF1p3qTInMbffLW\r\nruN7bNeNS0SFlCYYYLH7MEqcUSKcOhilOCLIDVXxZh5eLxbsj0ZNJofWcbLD\r\nvpUO6boqlVq4FxWcmE5CbTAyXxs6QQSYnCRxlJte7GNsL4o/1imNWt1o+AOb\r\n1vwRR+0gudeTyXILoGtH1Yfv33HwZgS4OCTamYL0xGb2kOY+FYHlD0FW/tTK\r\nuY5uhx5JfQ+ORaxRdHyNiHfzZ4A16VOwyAQs4OEMms1HI5B6J1gdUD0Eul/O\r\nOjcMadcP/RQVnSvc6G4DtHHJyX/Vk7A0D7MmX/GQpxQSUa/ksIZPf4rCxqC5\r\nMl06ARwz5HBcQXtZGMKKEDaARh1SSDHoeXk=\r\n=luja\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.22": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.22", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "96683a5b5420c1fde589928d7fee218e7d8291d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.22.tgz", "fileCount": 8, "integrity": "sha512-nyNx0lOG3TIVfI/Zxx4PODbllImGwaRw1GCkkm7yvwkSPYqvlxwGOET2e3ODpEhRsuJLK5FIeRY/NrXbOO3c2w==", "signatures": [{"sig": "MEQCIDfmKk2vAQ8TAAsFtE93lnrt8o+Qi9lDHWvHi6xp+NxuAiBDiU0nSmAtYmyzWNXRf1Fxce16x81gMRO5Ih7X5ddofQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpumg/8DwuCqnp7P340MqUtWoSVuGDwW9GBEyNUxBk29ky/QUKmDm8n\r\nn0w+hbM8XKHe0A7MXEJttvzS3ObrPHglSErJkPbLMTrFqaAmthT1MrI+8fOL\r\nEtfNTPobAnszsgJTKWAzjedBr3mOSgiIcorkcnOUquCF4HWYRQhEKrn3bhcR\r\nKF5EulCMZv/HCWKTm62hhqPpLycegwZXB/TJtVX+S22v70e/w37ko0B6jm0J\r\nAhJLwVYZLTzdO3DoArVA7q90mWY5KlvoZDIDPhou6obsK4p95k9ZS+nYfeaf\r\nBvmBTHrAf92nw2lJGCcF1UlApLDJ1UolGH0Mxw6fBc3kls8Znbj90ieLzhCP\r\n/VtBF3T18twaY/4XYzKmtYdh5701EIm08M7MY/xIWiyr2wALVCkcQ+B+Empa\r\npnwvApe7M0MlIJO4f3C6BkismfdbpXdJn1QPwUxpRDbdG5Absef7pCCSM/ci\r\nTc2BToA0KWeEUXdFym3riip8Piyuvii+K6Id7vrTcB/tT44OXW4EwCknci+F\r\nX2mXaEiRYdof30F2Lw9R38hWjqDyLpOCqz5xGsBVK7U6EfYcC48yQx+9Lp/k\r\nmq8/RPiik75HvuMev11ejSRjckWh9lVMoLq3LCqchhfP+JbvrYOy8iivsQmt\r\nF8/Cgeofx3Mzbx2isSAyfiIXFVE+RThkr8Q=\r\n=14g8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.23": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.23", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "78083771c4babaa77cc1c74ef6edb450fcae10ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.23.tgz", "fileCount": 8, "integrity": "sha512-prmnDILzJSC8v685bMqRg5Bx1WKw/FJrC2lTA2eVSImSw+xC2E8XlVzeqBj8aamGlpafhaATSWGu3nbhgPkAzQ==", "signatures": [{"sig": "MEUCICGHOGmU/u2ABXo4t1woVErdPJ/2qxZN/x6yO+GNgLjXAiEAmFncqQcXeGlzjGzPpa/qciB/XHgjkfFEugq3o2HHxoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJoQ/+LD8iycIhxj1XyOvSnsI/G0CTBafIUSFwRqpsvfk7lusv86eV\r\nQJbMxPhPCAKWeFXiAwlyuBYaXq4AdWRPMZi8yN32f5Ov5ZZKvYG4ROpJ5YKY\r\nPDObDQmmY6nztk5qlpsfVVrHMjaWsQA9D92AS/KsN2V+xfO4yzGjDnfFCFfI\r\ntkJdWSixabquClKIxhwJHUlP2v58S7oWMXRwiW/f+mDu3rHxpQL4TXo2dK1C\r\nbCIEPSIcTFpg+T5Zo1bUPVRNeX1p1IXkNvmFhXNU2j69vyHHw+dVuWNCo6ZL\r\nUZvUfg+/Xp3Ds0djKAoNG430GGzPR6crFRruG2wcBr2aPLU/BnNi0b+JMjnF\r\nFUisa2hqULP3bfFGjLKi9wx4NG6oOuHetDFj6ImHLShnUyxbrm5Kpq7yhoav\r\n3DVFUJHJQIs5sdCOnnWZHXUegq05wmAH5t2HoGQWpBuQlwK/+SnwwrfGF6b3\r\njmnUqlDtYKk+UyrhQrr4sqI3PUm1jnDS9Qx9A94JXmLIsVg2jx5lSVUnUZre\r\nCre5YNDEKl0noGz4N/entXLb90FCLW37Q0V17+DZG/ul1bpM7ueq2bF/f8Vl\r\ndXouCt+Y5LgQRIoB0xyKDIyLrBbHOV45tnDpps5a9abbGnCorjY3CVa5hzhD\r\n7qCI9Y8zMq1JLbv49p0ScR8W1ZZniVSbXK0=\r\n=tXqm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.24": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.24", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d0a26be9da3f4a98d7cf3778e42352e72fa59f0e", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.24.tgz", "fileCount": 8, "integrity": "sha512-20lvXPVpMayjHeNdLYMVaZbq+yBdHrguogA7dJLxRI9Ucz475PoV9Fh4g/QIlYhWCfHr4qqhIcH0yEdSGE2MnQ==", "signatures": [{"sig": "MEUCIQCYqkWE6n4HrR3ofgArkHTARyDKo3WyUKDlVriQAZCk7gIgAzb4x0R4Q6um0V97Ceg6cLhh7m/ZAjVJ48jcFtd5ank=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPWg/9H4ga2IeT4nbaFRKV6cfzf1bcipW1DN+WUd/hP7mYMsrc0/se\r\nKvrv9WzRu1tuWK0w20383UAMVe3xqvLclqASt+eHur3dkKwozVlLeY3X2sbu\r\nxB+OP0RFFOylTGLz833MxgsUgKMATHTP35PsiP4SaOScM5w5bfzfDL6gf+Q+\r\nLq1yhHDqBEH++CTAa2KRoO8Fkv25gd871s2XeMG5BZNgoWhVIqnxFB6wfOHl\r\nwEIdvqVe1Mfr81FVOU4UCfJvyNkXT9AWvSzjONGtJelDCvAiKiBTKyW6nerM\r\nKFngG28yoGU8aoI8JXFY7CBWGen5C62rQkm1jxVRqy6vHnDRqAeAizuXIGYl\r\nu5/aZTFjozha3YZy4rPZpy+S0ZvD+Kku0qUjY9aJplY/GcqWFS4hlny31FHM\r\ntuSaOLCZ6X3mtl9ACzNBJ816RsQFFg8shyBYNjufXYRD7aFv2yErK8qD3TH7\r\nI4Aooy5O/RCMCarGnPozmsQs+xZywIsMhPh20i1/4Je+6fgGueD1+GF8IMIn\r\nicql1gyI9qJieMewrUN/z/9PM0EWslJTFafT93fS+Y9iE/Z0uOl9jAgnTkht\r\nChjnFyK786Aysex53HKmsr6QkWF6tSelXZFrSzxluO1qUf6/MBsx1XxlKgfu\r\nH9CEpjGdT6mdl0loUvGicdXsKzjtSMkQjqQ=\r\n=20M0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.25": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.25", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f1d69bf20e43b9847966eb865c647229898125b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.25.tgz", "fileCount": 8, "integrity": "sha512-pNf18ExYtKYmm2l8TSS851TcEJS66sTKSpsY9j9EiCqZqWa18ECIa7eljYXGCt00qIPmcxdmzIGHEu/rxqyI+g==", "signatures": [{"sig": "MEUCIAZ6A2iTTLiqYIA7Jk1DZK9+GvFHHulXhxcDwGWMmEl+AiEAm8YEIUu+IykvC/zyvf+YuXS18XVASjGUM9osAE5J5k4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuDhAAkZH822ykn1TUyEoZVpLlRevC8QCQ3sayCCObtSyMA9Ar/14y\r\nO3CybYwPWwV6pnAcVOjWOqscZE56MHr0OSmOH1uEnjMmXiOfFmNKiMjpatZK\r\nw7IVvwn+gJlP5VXh+2fIrhqDTsY2d/hLLmT8HfXPEVNH0N1maiQrl4DlTqgM\r\nI9aaYck7sbb8awk9zlm/7VDi54OxNGxN5Qw0TERvUTfMG/10DiO1HfTJQpa5\r\n6gduA7ZagO1KjZb4rkwqlr9bhJ71N9xgHc+3L2JZ7b/R4h963IQoosO0Zc5C\r\nwc7LXP9zykrvI+JRtHLjsfIWwKV8GXfpkT82qA4KtwhHHG6r0N1tXJ+eJCg/\r\nwn8YdECUCsAJwUEzx8/GuN3P4cYd7RQSiC0rNcyg1LGmfnJWwbRTPunXvxSJ\r\niyJU/5HlUU+isi3Bcloft+mOUmOGsPD7bHJJJTG1nsjxB3C1+1ZP/rkxqclV\r\nVEUOrecBBGpVT3qe5ENONdVfJW2Ka/DMr1tlHKNa+Oos4TQlJBD4c9RYA56Q\r\nm1qoGpsgNuQUWY+2888b9AaRajr6sNChTBehINcxqJPEesiyLTS4Ar+xmCQb\r\nhB3YDnde/L06z+H+9bb0u20PzQjK1wPEIMhOoBmyrnzg6Odph48vGsue/Gf8\r\n5KShhwwoTwD60Vpf9R3ig0vDWfAiUOF4OAQ=\r\n=bsdW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.26": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.26", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6be78a33c784ebee0c9f6a2a5b6f2e7253d9533b", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.26.tgz", "fileCount": 8, "integrity": "sha512-VHryRi4mhsJm4RpiWXA6BskUgT7DLnFTW68K4ylrugyfY30y5vAD27xNfaMkMCc1NgA5CXSjgLJxOZVzxMPwJA==", "signatures": [{"sig": "MEUCIEqL7sNeDuki+zX1svxI+K+gpj0MRlxeiASaK0YkfUzeAiEA9qtR6QWMha1iFz73e/wzs55XLoZnuYpxRVyX+QqVgds=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmraeA//RNx2Dti1RJqHtI5coIBvGKmu25vtxxZ6D7mlpluVW9+/rcKP\r\n/6boYi237XpYwDVO7S8tDMJj9YbueHll2NdNgpqW2MHZOV3UbgJC36EMH51F\r\no+IAKx8PIhZugBGzAnOG1E76nysQbyAqiWRmEcMcL/KEd/lNFTDCkbJxwFc1\r\ns5g0ZT/BBfrm4pfzI86/r+Z3H6T+ZhxBjgCBZogzqshcmyijWfH7cnaWP8r5\r\ny73IdTShBj4Mgd8sPbzHf5EF2qpgS13IAJoPayahLthNva7sWd3wfBXaSULp\r\nI0qNyuxv2z476lcBJ62C54xztLSheBA/9JQ/k9sk5q8ufo4HZxXIKCEuplF2\r\nuFEu+SQZNKVlrachDJCKZBFZ4WpdgNYIVHyI91ZEgc0buvAd2Z4gBVMwfsLl\r\naqVbgUZd6Sw1bHj36FlEG59BdBDcHBr/+eRDzbzUeMnhTudKMsWt6odxbl9r\r\nLyeuiM23/w0LRtGolEl/wkfY2cYwcOZdfE54z204Qrz4sIvLM/WPyJn5Azky\r\nVGrribUhHvFCORxnzfea6/cPsQuFL4AaEfpxIT52yYRDFEJna5JVhRS9PU2V\r\nodqGCCzfSPKRD9k0KEzbk/TUp6je+B5IoGZHZXtWUea5UHyuJFEoUXE+GsaY\r\nYJaQpJ44hFz0tJdAhn1iUbCBp/KLZtMKj9M=\r\n=TQvy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.27": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.27", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "97100bb014dde33543d411a7915ccbf544e0b0d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.27.tgz", "fileCount": 8, "integrity": "sha512-k22O1OFE5frIJlyUb2jBTMct1AkFO7flAjhdwY7K/suKikIgO8zYMUs1Rj3daz3I0MewDBzZxXjmOa+aGbrZ/g==", "signatures": [{"sig": "MEYCIQDchTwS1GPS+4gqmEP4NQc4rIRKodGDnE5vDqpLsfNaPQIhAOEXe5l5mbFf2OFKR3qyo+09BhwNN2lXCzVktaH37hTd", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ1DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1Ww//ZqJkhzruF8MROMbaCR0KuF4Zz+N/ICXWovZMlixpzaajNKcy\r\nP5ZRo461wbohOO5z0MclrE8uJ6TMO44gOX2+mhrsstzpMXNZARHCjIrTwSwS\r\nud2V6/YrRCLI2Quiu5GV+1q9X3pGV1uV0nc69bnQKWJN6xx+P/4SBHRjpUbI\r\nJh+ZF8pOROhzlk4UabEmsOQm5S6n+t6bVXYRbMTmoZ3DZCiI9UV9Y6pZ+hkD\r\ncKn5EvZ0jbOUJEkpedU9Ohv+Xg7Dz7QjoXMwsqmJCuMg7O5BJvZhl+k0Ehtl\r\nZcsZuI1b8xXNg9f5QyvvtG7xBpfM17oGeY+ioCVwTD728Df5eEh6/ZH/rVbg\r\nl+WAXccBznSOMg6zTs3of4sWlhtof9kVsCdTXTv/dJsFxl23M2g50rLOIqcJ\r\nuPyFAsSME/I4UUU0FJVmr3PjLrH2rvOxxQQNt23cV1PDeKaW0YmJreVc3Qqk\r\n+7wWHcDNg+WGpFH2xTausrlDLW+YPjVfAgEa8062HfUFjdChdHcn/+W18ewp\r\nzEhRW3huVn+5UBgJUIMxgz7f8loZvG42UHFSxYI+0SLJOKGsjVt+6BHYbqTQ\r\no+36oMiYf25p/hofAg8G87XmZRo7zUbTfe7PEZnYB7kjTslRTHFcn07k92lr\r\nbbMOq+AdsBcUXCMZvHcMOLTJDlNoaVmA0WA=\r\n=FTRt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.28": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.28", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9532a439c854784556ce0626461beb18362985f7", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.28.tgz", "fileCount": 8, "integrity": "sha512-EGjek9qpVDuNonCvSIsHYqmUxqeNkFi1S7da27esZ4NliZ4FRMgeDTNZbJu4q1YUn9lP/3zaop/IN+VTlL/y+w==", "signatures": [{"sig": "MEYCIQDiS0jX6XL1Zk5tE+MPUYNdZoBnL95tzAndJCQshOQSNwIhAM7XPgB4PEepkdJGZX3qQ4sxSAJx3e3ljEuDIxz6Hu/S", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqHxQ/8CZDcLFl881ZkZ5HPpN2zkeJO92Lb83IFUSMfBf06Cysda2Xm\r\nJEoOu6glH9LyNJAXQHu82DO1OIgoTLbNf58uzXN+0lOWoKJ6MmP+fsRFU7e0\r\noAQUQ6tr0gmDdvwCFHKWpsNa50mYG4XBO9gn705T9UGEGVXuN2JRVx2c3VUW\r\nt64tad+3PykY5L+ktfj/S1cO7RVzvTwem0lFCWR4nmLcu+UnXTIY6Y4UYsBN\r\nikcpwdLhBvDEr2/zr34mBj/unxazz5dCs7pwO1iRRYArhCPcGTyq65034aGg\r\nGaDjEFluEj/RLO9B/H8HPhby/cUzX/YAGfkNgMwlDVUeUyEqdrAPywhq8l0R\r\nuqnLzrcja8HDPfGUHUyrsdccWb1QCbCqqTEaqSVG/AhorHfYN5PLCV/yqEAA\r\n7f+Ptjxy/FMrFRXf59y/SFKWyVIFHAVdXR74Y2vqTgrqaCtWBSfVADoBQoxp\r\nsrw9H2hk8dHY+jHsJdZ9RvuY+5juhmOu3U+4PtrYbGnPtfIDOim5rO7bm+Du\r\nwjR4B2vHfs3YNUcgAFtgVRy4chrLUvGq8JMFEQfQJLOQDmPoZGJZMLS0NOo5\r\nhir5q1mmHh7ldMYOkkWmu/kGkCR2Vamuh4hsWAEy09wGpoZwicqBd5GNM0sU\r\nBvj76zD9tIGUncIylowpbHOvGaz35dUYxKg=\r\n=DZzJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.29": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.29", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9ae38b713235ad165f063c6bd2f61c0c62cb019a", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.29.tgz", "fileCount": 8, "integrity": "sha512-GI5S4qFQ9fNNJwpNM7AX8nB89nNAzUcMcyW+CuoF133Ccz6mzCv7i3uMMqUcAV9R29pFm0wMyK3xLjcEGo61Cw==", "signatures": [{"sig": "MEUCIFIiJSonnd4F9wcYDV8HJm1cIg5ChG60OZxz7AaO1uJDAiEAyvaRyh6r7nAXWEmPsBBjQeOboBF/twzEJBd56aVUYHs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoTnA/9FfmkbH0MX8q0tRTx1VAlx+d56VBfXCDDTU3/mSWLcP4LqZkK\r\nGEOhULD+yKGQzJ9epUF3CMp0LV2JoOf4NpeOixpIyVjP5Tazvp5DvsLGWErl\r\n3LSTl6PI4n34kaZSshD8VjXTdTljIv1IcubMr2nja/r6FKZVZs5yr5Tk6mpL\r\nxV+QehC+n2PdFOPPMuWxoOQF5InwaQC9Vyu6BbMkCkYwTRYs7WhmCnDo5Z93\r\nCLSHcQCFmhdGvQJK+Iyum/I24rGs3UbYP9zoayyCIX/pKdrV2/HnqZuq4axM\r\nTBlytT0co+Q88mhh7pRSsNC7rClDfBMtCA2Dm/KVgSF4nXKSUYitW9mG/rzK\r\n3o1SFDVNA1KxhPqmfwT2n3NTTYbSWQahGAm2jhkEZip0i0CkTMb/4Yqw+oV+\r\nNcSI8wqQWMifWYdrU5/jNAW6QVfKItjjzUMQ5kPPU0MTYbEEFC6g7wAqnPCG\r\niAYq0QQ601hrQU+UA/xldRjO/ZvwNOcBSSo16FgHlLbcpzGMvGivcF9mS2q6\r\nrMTmXj1ItRMr2lwFL60kmbaZAIScT7bgpWcSt51DhuIIC6l77xnP0itSdAYd\r\nXnCd4ZARrbwPYbuP77DKaRqDjUr7lMXIWEmOr/bRNNP2y2SLS79DOPQkVc+/\r\nikYsxpBD5iyA+vTF3ckXHKT/cOouGqNfA4M=\r\n=luPN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.30": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.30", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a9f12cced604effb2460731a108e28a7f7274bad", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.30.tgz", "fileCount": 8, "integrity": "sha512-Bo2Or2F8bts03QvTupknHAYYBfTuZQ7yzY6ulaKXVmJc1pF+rw8GVd6PLTv+vdg+8W/SNd3a5/e9WRweOPLIBw==", "signatures": [{"sig": "MEYCIQCSYbHIGMVhk+pBKaA7B9rV3khKWMyp/gyi/clRGpn6qQIhANcJbX004/pIDOQOEYW6RRGckthc8We4rOTo2Z5BHGtM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile14ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrnzA//cBz604u6v0W8bU82Ifvez6L6E9CD4xKusHfhtNsLvE4dd54O\r\nz+hcnTWQZXLitmtWn0/GyCiDKkRn9ebNlJX6quKAdDY10a75bc0YAR/4yUYv\r\nFnUaKgX08KpUcuQhIYcKqAmCZtaCPoZzhxomlE3HuHcnDeRk/XbhyJM35Qbz\r\nvfzLhv06nv2H9TzwRlJ5ln1qATc+RpQcYMATO1CwTDXoXi05f0FYoN61ftE6\r\nfUFpdVYB4MlEKqWi5eNR0H4poboIP4U2P6qRsAV3dEAx78NjeALHWRS9Brga\r\n8/bvWv+7pz2uEmN3Jdm7yUgM1v3Z+97pIVcIrVfPZzEbG8NPNKLRBgfFwfxm\r\neSTeImSjWAesQe+yJtqeDt+gOJ8TZetBBvLTdJysOlfJiVAV29W6p49t0f1T\r\n63Xur7mRmvS5JE7RTUpV7S8HxEg9bUFLL2izucNWKbCZ0u8ee28Ugg1mne5+\r\nnACs523XUZ5v+L4os/asONQMbQzBpirR0hvIYiQz/73f2HfUwJhsN9MDbq3I\r\nBZ+D3e8UMd+rL7hA1SwPkWDUQRCfbiiQgaGAHpWPhFr0O9S1KfHdQ97jSldj\r\nSyQdlK0QmG6IxnXb+q4qKtDZjWpzYeXSUNZzHs6VnXxwysNIIYCAlP3qVPLl\r\nDu1E7QAvoxSxzvjJOoZoQwM297Uw9ARAqfg=\r\n=u/fJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.31": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.31", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ea587abcc7b06ba1a23a408e5a02f5dfe285be59", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.31.tgz", "fileCount": 8, "integrity": "sha512-rYRxQoF5FsE+UJKQ0we7P4K8So9ix2/xQn9ob0H+E/c8Dg8z92OUrLGMSAZexN1m5C4ysx7f7jjyLxl10UdHOA==", "signatures": [{"sig": "MEUCIEHT0MQuq6KT7C5wOC+KoZCzoi8CKsPQ8D/M/w/FhQZpAiEA1VP6ywMCPRl9P+43bwKnHtxbl2JxZo1m4gp3iRzr2YM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3W8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG5RAAoanvAUpTjfnknKEqgn15F/QRL9gRuVhwfuIutguPaT7st2lV\r\n4IOmtyl8/3hEfa6wal2jRoDLFHT1eyTk0UCjjdbwx9VJrIk850bZ8yneFB71\r\n6UL3dzS+IiDtV9zFRlPJMBYXiSWnPsdWrlXMTSmv6OhlZcNBaLTTXNBLdq6u\r\n2iJaIl63CCoQ48ZQ5XGDhccE74C9gTZi0zpseaHsxnjfIVK2uEEZhY41qqFK\r\n65aZ6jDsch4wAOj4hcoQwiDFCltzAtrDBh+KDl13F2bJzUyPeB091iNkaj+l\r\nLux7JGupY67XiArIo+k2tSHPV+MZYGGP0U1yorK6xwnFCHHEW9cEo19t5csI\r\n/aqvSIvlRT1PrYeI8orrN6AxQzzjZ5b13Vg3NUwXkwE1HoegcxAIGW+FWr9K\r\nDj5trMscMtwoQiaUPMy/gr23C3fdd4dz9fXJAASD43JsBNTdnvGz1K6W9+Tk\r\nA7rqGjTjR1J8GcCG36/WNrGTW7e9TaDXd5Eg6B0uOa48AqUwdHKNoPvTqv3O\r\nsIcjZYhvZJmMW6lMCvb9KcT8afBPcskAa8pf+1OZwKKWgwNCswxJb29InyD/\r\n5YiQNCBkXiF/y12ef0DCo3jNscV3FXyM9dFf+dvTJyyIkTKwsonf2H7jcyNk\r\nvGPxGQ5V86BSzXM5lzbpRNeG4xykjaH1/Sc=\r\n=Knvd\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.32": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.32", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6083b9177f21f63f83c658a384e3d28dfc326913", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.32.tgz", "fileCount": 8, "integrity": "sha512-7Y+LNHAUbH2fLqXhwpNUmc1woHiej/snAvyz3IPVd2EFuPEQQB3x27aTKz+enM2pMY4Sm2sMw7jK7ojsym9DvQ==", "signatures": [{"sig": "MEUCIQCZjy9OikyRBEbiGhrZtMOtvcLADtWLcQAbhq9CXusKwQIgY/Ou0iuKZPH+KR3LQifwNYPvWes9iw5wg+0asQ80D54=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDIg//UG5bh7LmL7jok+oG7W/hm9rezV5wq9bvlHBaRVkUsV/yl/Ee\r\nu+cp38BTmwqmUTEk+qcs+02VMF0/SLb8NZMYQDkMD0bdFUdE2D3hmigMup/j\r\nBNc53ulX4fwSMlUU1MPFI6Bqq4J+t+90L2DawVjzhbApeHduXYepmPykSdxq\r\n8wse7D5PRxlr3lmZ73mVPV0vRc/X5xKOiY9DDQuviPosZ3BxkY3/wLXV/yfQ\r\nBCm+QM+0JoAMvMSTSqBalPFN208mxo2wMJwM9iCNI7NS8Lm+r/TXOJXYfa7W\r\n1iH986yqb13X0GHp8Ubbs4mVRO1fwBC9nQKGvu0EXPhQickpnii0qbJAogI/\r\nDB8LPjW4G1EfeiV+uYiZUU5o1OyKcfH0Cvn7L3bhjd2jjIphozbV94j5G5CC\r\nR54iNcn6c88EduE8L/Np6i98DlfX8nf+2fm9dSUryDTOD+gbGCoOLKXYyrRx\r\nyhHfyG2nBxkJsmIdoUINXkOBmY3vdI9d8xs7BP54kcThUbs6xvc/yoifxAI/\r\nWFyEuxNfpD/H/cSwjIKgF94OHhxzcsmu1wIEU9su9oAG9NAwikKiwWUw/9ia\r\nsUEsl9YE0cGUN2C+tzooGCctnmyIA0DtLzhwvX5POtSatDybWQU7no2e70tH\r\nw2Ezo+9h+/k8yK/WCpuiDSqzVSxPiKar9qM=\r\n=zHba\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.33": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.33", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c343740d574197c3b20f4cc3b1e98201afa81fcd", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.33.tgz", "fileCount": 8, "integrity": "sha512-RbpoK5+OY0SYc5bIxMJKTS7FsLi2Fw1WdIM7V/fmGOakYNgC1qYeZ0628UyNXii3uA/Ej8erBKyg0CCZXD8ySQ==", "signatures": [{"sig": "MEQCIEfsRcr0JV5ouBP2gkk2imNepY4c741Z0m77454zI45dAiArHmNGEAx8y+egYd+xG4YCXL2Rss/YPhK5FNJ+5LI1oQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHb7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpxzw/+IWH5MlzJSRncHLRS9RtbjDXKol4/PA+pN2ILYYabB+c2HKfO\r\nY3h++zu3Vq+FMwXonwYJV8LWTRNNzsQsUEuCy0cZKZDE6Mn3xruIUKM8F6Fq\r\n5fItcj/8D2AQIoSu0rC3H6EJT33avzHLNSYabCRrH/yFXAIjnwbdW+YDv2g0\r\nGSN167B4Z+/vKGWvRrZDVR6DEgPW/bcb2p4hAnEbn25Rma8bTc+Wx7MK76Hz\r\nqiNXlzIYgfcFGGybfCdZRl9oRJpqRS+QxZGuPQ0a9tcKUx5YWK6RdxLzAR9J\r\nawNJqMbVkIKIYFxdweuYewVKbaYutomy8bQEodlfw5+BX8dmtntcYN55iI7z\r\n2bie0jf1RzKvUI7dunku4O5EhzCHAJ590FXbo2/z/N/DeSNTpkCRaimiQ9uZ\r\nha8g9jqkP3Hr7B4cuak3Swq61SbEa3pq8orpF0I4lwEADBoJKiA2HplyDkKX\r\nIjAy6CRA0693EUnAJQmzrOLZ+z2cldh67X2UuZxNalEtFbyBXTpr2D/L0bfd\r\nKz/tJ3SuW5Qx8quOEddzFfxXGkH+PLE+GC2zMlMXQaEAgs71+KN5sSVv95nh\r\n9bDQACpW2rrurzsz0T3spwa6GZXmQSCYKI+/ZhKicZPr9TpgOuiv8JagM4OK\r\n3irpRC6jnNtdMywhJ0m2pEXcMvmPGXbEx24=\r\n=R6Ks\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.34": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.34", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "85d4fd1ed82a7cefb3613bdeb1bc0b408f0a9494", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.34.tgz", "fileCount": 8, "integrity": "sha512-aLtlXimmiY2ZVECvPTdiV0FWLocKTmLLa1GPr47cEYvt2YaRWz8fJ5peqU1g5NChFTpPK4r7UCBqyKH8LqW7ag==", "signatures": [{"sig": "MEQCIGecRe3OnRWadbbJ01g9CIU6oj59GgL+tqsyzDZFk8uBAiAni666gk6BfgRe9N/RswxkQxfC/sKAG8kxd/iCYtLuag==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqF/g/7BO8zoMudpTwWPYS9+XECigHQ6b02nD1dsasLjdM3F2w1VZkL\r\nruhH6EXZFldmrM0+ucPWkYJHqe9wylOT6iz+IYlxwwwOI56RhfO/DpNfu1qJ\r\nTHTuyv4X01xX50XyQNplS+wxAq9XBi1Xv/9CjCwbZnJ3EU7PJNgdRz81a3II\r\n5gR91KfK67Edv7j5W1fZ2U0lhYE+3D32yEkXzBaFmFYcpe6Ip/iAxnEBPPZP\r\nC4jwWfgDzGXtn7buXmxSFEYAhBbkGI/gdhDOp7iPxQyJ09u3LYaL0XPm8dEI\r\ngnHwBA53CGlseOTF7LD8Nv0fMIF/n8Inln0gQBiIwaZ5yQcbBgLUf20IRvYi\r\n+YZ/j5kpSUyWEqeGQMBK0Oqm/CeYE0okvyd8XMLiL+T4i1QpHdt29w8DmjsX\r\nMEQt7HeRnW55PDLAvhgjKE47kzTCKSB1lQr9H4xiHSZU0zU1MNrNb1hRFO+2\r\nfL5wpVo0G0lK/5Hv0SXAwoPaQCABBfqxA06CFKBktrzaGAT/sqvwIxpj3SNL\r\nYS+0okKlkFlKZsAuPa0AKbni1FYNqsFVupH41zaNXLVTgFCfL93QBiLqstzo\r\nmQbs/8ltC0WMmHNNMcBhUQ4/QSqm8Pe2SYjLI4as/gbK6AVf2SUOZIbQmBHN\r\nOYSCvvhZ6zK4BcWq6amjDxBwRn6HuqDGDU8=\r\n=Mppv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.35": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.35", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5f31007ae10f35999bf6430c86451fe97ea12c25", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.35.tgz", "fileCount": 8, "integrity": "sha512-jj5RRwoxYEgzAYyQkm8u9sinkvxkgltCuMPOof/yYfh95DiWl/ZtYPq+BJ2w49PocZvFIYTON86ku57apKLiSQ==", "signatures": [{"sig": "MEUCIQDD4ZrBo0sCkIYY1grX9/cmnr6YEf5GRz85MYAZreDT2gIgTP+C1UZL2+2YVXZeG6BvpEVSngoFuipFfKSt6P3pggA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqe/A/9FjpXJiYb4P1R55DmmHuXeSYI45aV07C3qRbKRbfQrjru24Ay\r\n4uSqRElY/7TO0Y06oaIpmNS3W1f7WReeK28x90bw8TVoky8VsfLA9Iwk5hH/\r\nodqQIcNZHbspY55bMLOItE5tW7BL52T9noTbgdKhclN8Zsz+ivk4ueTnuNNS\r\n3vDx6wVgG96la/ytnRdx+UXxyB4PYcwJYc2L3BnCgH9H8AlIc+9A3F8SpA8s\r\nHrzHEEcaUmZAInGNhHg4jvEDWxR7MUdRlUnrcnDOJmtrHOJ9I7d0ISnDf8MU\r\nZAEdy/XBVAPo0aQrUI7B9tfYOr89DYI8WF662baRCxox85i2mgdbrFYsUVGv\r\n598PPYmbhib5TB1/mk/sXnBW2tnooufhmHAOWQFvTW7I1Jb/aShZEcfgkE4U\r\nCTORlYr33yNWdB1egsJPjU6maSVeYc7zHlbo35JzsxZaAJ0X+3IngTtKLFiC\r\njNFb6vSeMEaPt0I5FpK8Z/fYVW4IKXsQcUptpVE6OcoUJ0ZBvZ0r8gtlGhuB\r\nx3SiUwlDMQs4SVRCQStAtFrPB1XSgQ3toCIi6iie72OB6tr2GQS7FoS4xXtG\r\nP8WWnRjKlAJS77P82eQFThQBVKHX08qvDZsJzt915jy4pRpAMrV8Fi/xgsUh\r\nzHQPLZUVV5UPHl26zxK7ChsL/zDsf/vwz0w=\r\n=yKSz\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.36": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.36", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "52592dfc51fa708f12f9143805032088f149e2dd", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.36.tgz", "fileCount": 8, "integrity": "sha512-iWlC+70uX61fEx/iPWnS8O5KVpIIwTYIO0lBbWyYsNkxbl7tW/perAlsVUyVL2TlrGpmP2o0jNHpLZ/dTJFPtg==", "signatures": [{"sig": "MEQCIFBsboZG8k0xhLVCzk3JX6foqruYu+sjTBNo4d9KEjIvAiAORZeBAXfC7jo9+N654am1kRk2O6JbI1J1Q/IKxuC0Ug==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoo6w//QB3kOfAY2EkC6CPvvr6QzEzkahhIHD77TNx6ZiqlAuMpdhsK\r\nweJ4xcToFNNj7wfAcvG1lds1ZI0X8/Y4dJKWiSUYoerdnGYkfVYt1ocPdk7J\r\nsCye2jL729rN5BSOYWaOwONIlsiSJjNAvxH3LcOwR91hwu5jleGP2fr+RMvF\r\neY+GIGL4ybvtP7v9lw4KucOmYt9LdeTAXlGGN3kxFFzQCvBwuHu9yP9jTpyt\r\nwwkfJaQmV+QaJUUkcNCyL4wXnNHXFwGtOnVcYG2iSVjGvdHiS3K/x6W85Deu\r\nWoaXyVWTRnC8C4ckZH8huHEuV4Qo1CRkjN29nQT+PtqkOmx/M3LxL/kcG9Jf\r\nBUel67Xh2Ri66O+tWKSzuSN8ldW+E7+qRcyvnBWXgCILMd0rXvuJLyw/KOej\r\nZZZfz+9fdNrcgsIkqZZxJHILskImI/wLtZgHmn4dKqjqypgLGkkxQRGAhMMk\r\n9FweFs9U/bxChHwQAU15MBKEBHBv1dAaVNTwCiJaM3kx8oK3Lr0pbfIsVc5n\r\nphqmOHfgnmOiMRTk5FZUtBaQ0V1PEnUo179q6Cfz8UDDtIjFsWWa1fCpBFSW\r\nyVbylrKc7VSQ8mvZn44CPiN3DmiU7SjjKUSgd9PLuHhuGUg3u8n9X5mo+cCp\r\nqlO2aWNvyWXuPPMAH8ThC7cqu+9zhg4WQDM=\r\n=vzph\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.37": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.37", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "61cdf840a17e21a80253374b9fdd7c081a0530ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.37.tgz", "fileCount": 8, "integrity": "sha512-geocB0Ft+39EYcu7RzGtGz6kmRAqqW0fjO0QbA4Xo7ItWcmi4oOb39qCF3yYmiPxofblZ/5lH+ICYwxmkQkZqQ==", "signatures": [{"sig": "MEYCIQDjdj1HgjNPfHWICuALb3Yfy2yc8isOFoV9nBmcWYKmcgIhAN3hR4qrICv5lyw0jrcNj5RcJBq4Ebw+vcrG6HvJPE3/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0nhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5bBAAnaBk3aFuqnQzc2NucxwNqGiFjMySh4Rq0WZraNvswhl4LMGr\r\nCbpL9TiymjKWD7qLTPET/XnlEC5GvQYMsEQZ9FJIsvXNjTTtRHE4dEWcmRxp\r\nyTA1awMfiuGCfNEXaJhsq0WHEg1fGQdpkm9SLLqJOO13A0p0gNnU/miW3G0q\r\nWcmL2x1ObggM+z3c/jL3/ODB2YjBvlAV9UZYf7+mMyUr12E+AbDkBgblR0+L\r\n6SygFDUzOZ/JNp5/oeuFS1r2XTEhaAiORsXPNPamhWIrZbimdi3GTnt8I1sp\r\nv8s5odFuZmOVLW4KyNW1v/UwR3QpjfSJDXhdYe9VQaq9BLqMWj+mldLIZKwC\r\nx6o4yRygrSEWVBMdFepXmefpXPR2KqiSwf9lAHm0/eZ+U/zzcFbadl3mH14c\r\nb30vN8dIUpvel5rcM+rUmAM3TvHqH7M2CXCWnNNIZsZzU6T2g8LGR5tz18zI\r\nyq3XY3cx366SaSnVtb2oztwHBd6z/QAHrWgZ+UHOtRl5LAK8xIpHfZFDohqB\r\n35C89QI7xRPWVU9IEvLHFIqGlvsxFL8p0yDzf50+jHHLA7XHZ3hlGdGQ2pno\r\nidmXQ+Nsj3LuaHVYb3aa4ofBpo1hfK9T2FVwbU8oawdIOpsxAjGdEr21FcFG\r\naYtBIEYADBdPYNPc5Y930sqZ2QNDRpaMhCo=\r\n=uZQB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.38": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.38", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "301159c7ad342e78fd14c72b572a2d879010dd8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.38.tgz", "fileCount": 8, "integrity": "sha512-IhggBbg3Xt3s8KyPnbVsyLl0/y1y5j08w/kgPJUQ8tddfl7zPF13sgql67un7qx1xrrMm6jh8DAPyZUFGn6GxQ==", "signatures": [{"sig": "MEUCIQCXO5v+xi+tdlRxpf4IlhQ+KdLk1oIxn5ORr/yAQFY0MAIgf5MKpcFwxaR+GWuy8vzedXUFU5IqconQt1jIVMVKwh4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzphACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr3PhAAofNVMRSr9Sc1xNq/qtpbJPk9VfYs/eybcbxv+bnjwGym8GnW\r\nxmacJ8zMNs8dqdElx7/i+S8b22GLOYsbLkVzzadOeO8eYoQ+NsnAOIm+jDhO\r\n27z0r5nI4n2Xdiw6yXOS0U8X7/tb143TyelxVMeL3sQhpp3Gp3vpqx0ThoGw\r\nZAxJoHRuUb2RmYTWWAC7E0pqTS70G+1aLM+MAalhXzFF3SWw3/6NnjbtXVkM\r\nJEuX2noFyqwzJfGUxdG9p0nboVtb7DrNiysn07cgpeBzxQdM+MRc6TgWDr4b\r\n7etJdU76whOqnEhHlNE77IeVd5Tiq1r/4b9BOvg0IWVxXbudQER6WzNqu06Q\r\n2UZ4VkZHGiF7cqKpIfFoM6GfS/bV+pvH4SweSiOdkYFBPdBKg5GGBAtr3IaN\r\nXJRkf7ktdiN0Eq9pk2cK4LBZUzbGbRjfaJ2CQujGFoFCZWwXELNMtNYR57qq\r\nUl6XdZWm6quWqEeL5S+EnreQ3Q6RW2y2jLQvMa329oPR2IvHxPTOe6ADORL9\r\n2/v++XIbwp90ppjXBNDWbV0ndwlThsJcmZy8GFuIerSsdFR36tdfbhyS9b2l\r\nbx3V1KXHcomBWeK29sLFlO0mJCL/Pyt4XvhIIoOAA0SQdpQBakQCTDO+Pf7Z\r\nFq083TH07iV/arlpjyRdfUgud7jm6WUqE7k=\r\n=NBHa\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.39": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.39", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "165dadcf486cb65a0b18b155fbbb72c46d41ec46", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.39.tgz", "fileCount": 8, "integrity": "sha512-j16nQIMdHoiHIWKyqbYiEOZxnLkMac4oQbbeJwITdbC1wKfQxxDzNAu0PjuXXv1uDorJO5cPzQ+0aChN6oqUFg==", "signatures": [{"sig": "MEUCIQDyByzL5tn3+osoawFSvwRG79hOnA7Q03p66/Y5dYT9WQIgAOJEoIB0HKYFU5HYfIX6SswpbQCCYZcKm/5N3PqTuZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9bACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoCaRAAo78oSOC2wpLGjQ3FBpYemGWhC0AjOXUYbjB2w5ph0U09Sndw\r\nbEzs9MoKyr3DWiK4ay4Gp0QPDtJ604Yan4VyNgBBArpMga2+diDUDQ8XdsnW\r\n6f48TFvpf+gyq2C7k32aciTX5LOib1j1uj1FWW3UnQzMyNLEuJ3s4cmihEmt\r\nsTs/aHMLs8s/callI4jmRXYandLXkHkKSeD+sV92racIskPDH23fLxON0A1h\r\nmMT2bI57Q5DJREKwmmp4BCbCzv29dfDyOLFLYBezJ0suDmTnfYGOLAY3r8ua\r\nbwoJpNYaKv+K4nlsYkDUOGTLo7TRUd7U52famCqDou1apOUal9V8tVjYyIeS\r\no9XVNQSPja/ooIr10WLeK9FnJYC/q+4pR4wOhoy61ZZR/yTW6ppM4JxWcvrJ\r\nuvWfzpTYF72fZ9ev9pK4HQ8q0s5Kug7tbwcRVxvW4fuBkJkZ/RcumbCI6R/K\r\nCI0RkB/TE91TmsnOT6FroFaNHRivEs3ceX550OUhUmLMvu48UpMp4Bb1sSKR\r\nPdAHaU4PMefzqezaj+VIpmTrtggxBnFLVfEBCfXB3KK2yTDEH48nhGV147dv\r\n7SH2B5k9VcdwRh/bAbXgaekZeO8tKcAWhBSVGG4ZLnfAvyFMjdWR6Fp/N8B+\r\nBPu+LRsBkcapsYjug8lExRhc9WI2HqXK1Sc=\r\n=WJ3N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.40": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.40", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6c7ade9b3d0177bf555da41a5cf3910c4e5bb154", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.40.tgz", "fileCount": 8, "integrity": "sha512-Jv6HE3RypVP9S1FlLyswxx42b+MVDhIS+/BUTWAr4941vweQVhVRfNThpBgHKcxC4Sk5dUyBhn3oYJImSQDskw==", "signatures": [{"sig": "MEUCIBwvMvJYf7d+ydU15k0TYJ26ic+sRKMNctvxv2TAkogaAiEA1jXzwl+hPJNjWcWMgEBXD1LIs0EVid3kUmCZ9ZDNJqw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37166, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpRYA/9GIZbDOh3uPezlrLY9R25NxB13eEHnkYrk3fKvxUHhZvCu/R4\r\nB+jdKVH1Fw+jQHssn2Od8Cfv5JS1pNASws3i7l1E2+cSglcB/oVrlMi5flph\r\nlR0Hek36EfStXygCbqmckWXbylHaGY9nMVujNGpm/UNiYiwltSQvrgNCU8+w\r\nD9BMaGphV15sj12SQFTy1qjN4hf6O4ltYyW6e2AjmPzHQd9uiXWd9S5F3aSn\r\nvZltyMUxO99msuukly2v9gI0NpCSsj9Z2LuoPCA5AsfVhh1X4rXiWsGUohev\r\nH9GzvJqaINa3BxM3XI28m1aZqsZeMJbERNaWuOrGjFU2qWIkPk4Rlz7bwWx6\r\nqPYC2YKNNMfq1COziyGu4PVUnZIMYPgAkTVLTQ/bi6u/7vq7g7dzBrQ6+btS\r\nxN2uoRIk3LUscGXpyb2/TQeAI+YLIexwbnUiFcTGxv3PhJ8xkCL01HPCNW68\r\nUC+UQgpo2/bciNeigpIg6iPfkH575P0/O+IM27yljrXw2qRad+SLWlDiJzlv\r\n3zc7zUjQ16Zk71rOMjKpgt18DOEtMJvzvYRqX+8oJ77baPpsNQlf2lYl24jc\r\nbEAtGHLA8jLm5lZa7R0S/Wsr3INr+llhz/r7EIUsCxu40G/c7OqU+Q21Qg3T\r\n75nVVCF60amjyNXIGKfnVdzuUE/nzRHs7HE=\r\n=cNkf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.41": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.41", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "13c01044b05944a39ff96f8f66a99c95e5d6d1a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.41.tgz", "fileCount": 8, "integrity": "sha512-kOV8CfuHR2Qo2H/UOaDgOn2YnCNqLrfa1XKjXHezNgXJhM6dQxnI9YcgOcb6+Ws4uJ1vNW2P8ZTehFqMbbs66w==", "signatures": [{"sig": "MEYCIQDYtTlWi99CcTqGyR5egHVHTdcVAKdSY6GBrwV+JWD9mQIhAJnzOzW80QHSGvhQqqUUFfHVgMTdopTMim9rk7VQDYxb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxKw/+Paos8HI9RPYhQSUVkdNJ8Qvl0p6AyxdpKwxE2piV8Q/XBDpe\r\nI2nSMYGlEzI/LGnUnA2k/l0tK1xd915IgLfB/pfrUbwmZYn1ZsNqB8oouKWl\r\ndtnrLmODb9VhnKN7goOe0vYnlrLVJAYKpUReMhFSVXHB4b53amOlx3vi0flZ\r\nH8KPTYa+pVCrz2VZRO2N/z9TYqqZtI3uspIwz+WIvPQBuFsHbmW1cG+v5Yqw\r\nsXb2JFulqxx7wth581p9AtsqEFTSM0fFoVDHBPYEsj/Awjqb8OtTKoxgBP08\r\n8joe6LFswBbshKSzi31EzlriL8RG8WrOD1VhCcwQNj2ueBrTnRtBtFd9osiF\r\nS2JeOlgp+2wtMRg0SNHXCsKMmMb1X8KIrOMQ/WAtscf77NPG8HEnUAfh4kEd\r\nVl3SSa3uuDnhJYlEnc5NfKjuARlPK66VEWG79PJIPYonWkeXm2gjerz5iNTg\r\nyb0K53La1ufiUwM4+4p/eXCEOCGFPosRR92P8p0jDaNcUayekJyUeWtKzC+e\r\nl2yz//vxsEiKBpucsUx6vCBCWbT7ipZ8nqUHZZrmgbm8Pm+DJbIynGw9rtbU\r\nRWy34QZpg+Kq0teQGxa7h+1jvBzyNDeo0GbyISvUN49V5iqEl5pra/j3VSH1\r\nyeIAx9yze4hYPaa0Cj/L79rKEtsVaH0lGjs=\r\n=rs6q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.42": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.42", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ce357be5543900d92ff44234f88be1884ca44088", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.42.tgz", "fileCount": 8, "integrity": "sha512-JTcFC4IBhoD5EfqV6rIwhhv8AgCydskrUyewUyyUKbDu/H7n+oxs7545wGQcLobDl6H499onkivp67yHmBpMsQ==", "signatures": [{"sig": "MEYCIQCbdElaUGaGdd67McTem8VbtmB/lADgiGycrr7GtZxmVAIhAIVDK4XHL3HJlUOLy13qr2j1bvyYJ0915iX16RPGuNUM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWmhAAidVBJjnOZGb62Vv9TqyzPnuPcVgHOE4d4I2CW4jSWc101An9\r\nz+iyaoR05TOZ34GdF1ODPzLJ8SJOFnb4KrvLQEpsKBL/bbbeESFoVD3QIL58\r\nokXvTnu6p4SE0faPsXzU4I/FX+MP5giCDzGH6gAY6LfMHmTu3FoAg68iGYCh\r\nncN0Cc5RrI796MFVSYUIkhZnoTWGYnZw+hc7t8Ud3aj1OpsKsNI5yM/3huQr\r\noNK2jTEa1wMAmyDFYINOY2XLhfnrIEloXAjh1bHGj6l0CbYX/kG1RL0Fo0zj\r\nZW00G4gN2Nzv5xLU+e0CABW5SMNu60+RgmyY72p0fNeGGJW/WeES1SdDJDAo\r\np/NcDNzaZlQwL6mBMrjV23SWO55WOVB3m8aTzAUQSaDDBUmlIUyNgw9On7V5\r\nuJWWTzCv4DGLEAI1NYLK2K1TBoREX01rD/knOneVFEcDTUEP0EZY6hzEkrj+\r\nTA9Zge4ZSi9JbKQTfLZJYzdhC6P9FvmgnVEJJFoj9fgqgTtn26K7NtkRzEhQ\r\n3AeEXDHOjhL8lSdfq/Lh1WC45l3RqfSDX/vaurPXr3AMe2N8G61wZphi7jKp\r\nvCA9hW55I/dOV0GoQ2gecjgKs9G77Xu5qKKgEPA3lWRsgGUfZ5Sa9RlmA8Qf\r\nwzJD/t3Bbb8Pt+JfhH2z8J+aPTSLdFhTAqg=\r\n=PLma\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.43": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.43", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "077c0bc14ccf4bd829cb7856b3fcc14ec9714c58", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.43.tgz", "fileCount": 8, "integrity": "sha512-fJfl8Z9Lsge6Lu2qf4unGu+jvMorFgi/+sSgIDIb/wQGzl353J25qljP3/8qbY5J7naNHEggvnLsyuXWe5LBRg==", "signatures": [{"sig": "MEUCIQDMiuk9qUzg8IPbKpuyjBUIwb6dZcCj/FgOMoRPpyQW1gIgTWjX7F3+hz8hWGe5fldcB9fTHMk1n6aTgAHvjpb0RPM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvruACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJ3w//djQd/w1DCsgQ9St5Exs6XfsLjNZDz/Rt9o0FDlGGfhDMAsFH\r\n0j9WhmsTvqMwKpx59oNhTHMDYWGcidz/sgrfUwgRl/ftQTDaSVxr0nMARmlG\r\nCV+zsXkeb/sZef+TXRY+YH2eqoHZgOND9AXg2lbjxF3Z9vmrj/g9cxBEel9Z\r\nBCIFJG+69wAt4AzF5ivSFudTEo7zYT5cG8tyoEHGVGC8Uk031STjBXZtgix7\r\npYKu7+MAhr0IE9baiZHXyM6iz8W7yDhYJm382z1fX974KwCAzqmhVsQqLuUL\r\np3++vKS8DvjUYemZIOzBTjoUBCPpURZGGXlDg9uoVxqzBDfl6ba/PTEawLGZ\r\nh7os6N51PtOngqg7/gQegqAjk3OVyNBj7+eSkCWkB/PG1K+IuJUAyONB1PU3\r\nTez+wdCsxl9QCO9bshxwqyLc5xw2HGbu7EbrEr2oQgzuS0Lfp7u7qcDTsp5b\r\nc6J6dnI2tMdwRPQNmgl7In0osioaRCku20cmN0Gg2uhDOH7ZlKgeJkOGXupW\r\nS7xtKJE+oHb7HqqVa3BNb4MZNkF0DyAachkPcuyAsL5d5L4H4CTeoQCoDSzE\r\nNL2HALd4O26OXSBret6GQlRwLJyxHp/RygwYBtuUyrkn+ifuSkE6KViSlzOK\r\niAQCLVLGXhgMrEPSovQqhSQ6S/V7WUPctPU=\r\n=x204\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.44": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.44", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e88d458b0ed6e16437397ec7755012fb781b284e", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.44.tgz", "fileCount": 8, "integrity": "sha512-tKkLkbzVkWji4WrURG3zEcrEVmXWD3g4lX2eWmKEe/QW3bwxXnEPE4ayBo4tkoKrBID8yZ+OfbUQwdEVSp97sQ==", "signatures": [{"sig": "MEQCIAunyMQRfNg08wfpHDItaPvli2vW1P47xKg3fZ9+oz8uAiAxjzdgteQosUOoNVzZq/ED+JCfQB4hX7YP46JLRDn/VQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJpg//eyp3EdjXmeJqTTmsDkJSqimIMygA8OxNFquxM98AvAjP/24s\r\naOl7kDQwOL6YgzdGubzVA4qqDLlhLrgSlzeCO1iZrukPROGhGZIK0q7O1pHM\r\nBmHAAr3biYQ+9jEJtkwqPuI+edhgUdW8XH5wgRw0kQKud2k5AdLDCkRzRGQX\r\nESqXWms/95UMSR4To3sXM8iBcsdhitXWY6VKOn8Cb4ZtZ8gJxYqJXKfgu4fr\r\nYhKFoYLLLiZqluudhX4bfWtPiyeTwxeoNzL4VfA2FqGRhhoC7SKec7c4yM9i\r\nGbLCR46GMss5wZZttrbHzldT1yZqnbypOXyYBarKPF6E6xWn3GTuHl20yxYj\r\nt8EM8kg9m6qXo/U04PQ/R30Oz8bHRqB8nuRWPR5AS/YYrbQlUG8cirDX67cd\r\nSVVIAmec9ENsq+ApuBEclVQy1Nwdx9OOxc+MhONNmm+x8IKre1sKqrHUWpb5\r\nPC747CUcLBu2sS0UEvwmRjaGHo1gBhTIJfoiFurtGzSimg8LixQCj2SgD61H\r\nhPM5Nc8HT6y1iAwqKMo5gvKDa+citjXxEYtsaITtmHym2heUNft2WwZbnYS9\r\nnaYrJdmWHqQLnhHoBYrNGDedE7z7zyVEEuYHaUIOa6fTAPI7+lMXk/ZvqMUK\r\nbzW0yJE5qGv79EIaEt/2dJiNQSI57+ZWQhI=\r\n=537v\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.45": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.45", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3e391e4d8bd1bd80ff01cf752253f5ef8b4e891f", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.45.tgz", "fileCount": 8, "integrity": "sha512-RlJJsg3jGcNQHA0v/IOuOrg9Zx1IckmeKadGe+RkKRSAeqqqgJpqaxPy1PW599w19pv4va23U7u+tA3pXbEaVg==", "signatures": [{"sig": "MEYCIQCq954r4VnrBsb001xjIJZXJN1t6coaknQeK1suPmvBGQIhAJ0xRlblruhvd19z17i4NEpBwphbzKq65010fwhsX2X1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqz2RAAgc6G9hoOhCvZ05C0dSJat3cRNvTSH5cUyrXYi/29OhHmBhZ9\r\nHt3af+BgSjk8L1WU2Ey5j6rXYYZa38+w7M8VymjxjXtGRRy0OZye3u5tqVll\r\nag1NE7hYMWfIDzl7NeJVEdPiAl+qK4ZLs4k3so8R46cvcF7fp3d5teuIZUfW\r\n5hqKcvPuyvKV1r4V4WEButALaNx+W2llIhlwmUUE/civ2lcKD595Oi32s/aM\r\nrJ6rer585CqaFm1GNyKJpPBfloUpENm9IH/MpM8827o3/eFRwq9YnS4N436I\r\nwOw83dB9gBE/Ypk9rj90ip1F1a4nW4q6CMhNwqFZlL7MmNGs5EBiZINx6J/w\r\nSV9TD68ZmOmxsuLPDVI3WIaU1wvuHiON/622YgabPNpZFfXDxfXtZDD5o206\r\nFciXYEru237L/8cfHI41IzqZwDFudZ/yPDLCE9c8ZypbnrPGxds2r8DGIKTT\r\nsTEsWkef6YKPMALiAA941Y3jnREFiuAGthSibfNK44FesnNfbilHlsyvBKN7\r\nc10yEiAQ/cyR0MEctxc8snzmDJBrbF0JtWygFNi0JLwKebXfA7+eOdLhP2V4\r\nqRx1RBB66HOYzz2zGnnas1+VAvOAwbssCCRiqKU0cAPRgEb3OM0oh5PoOTpi\r\nrw58IqVz8C+ayB6uDrWBW+XoiNamoawGmiA=\r\n=JQRN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.46": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.46", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "95c9edc9985fb46d6224a81ac2b717d99ef5b9d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.46.tgz", "fileCount": 8, "integrity": "sha512-8HhDf6CDnmtz8Gl58+dzRFSfHd9UPMkgstawHIiDkA7rDflx+Ckwdq2JE4u+V1ug1zwnaU4V7ndeWANLMxrLig==", "signatures": [{"sig": "MEUCIBUMJzY/rIxrP2l8XGGXn9asGGhEINMLGtB4MWZdHfYLAiEArSUaj0rMfWWKB7lrp8frew8g/FCSmTFvJaIkNffuf0Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197UACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmorVw/8CZe8AE8rVXAsSVY0BVS2inHf8iA5pvA32COhBOHh83UJu3rz\r\nhmwVq34sT+7AmzjuAq0MBiOBxyR0CgFoDciD2WQAXWAWqW6e/gtYCw8G6cJg\r\noWuNVR8bkFEte+U+GaurzEbp3TJUSyJSrokumlvjQvEX8NAdEmC5aik3D/Or\r\nGtblD4zuk6gCUsDbLdnXTnbNg/vdtimL7EhvvnxDXBAUGWsry2lpenKlzDWw\r\nS9CazGEIKU3nTdGzDaI4blhoKtgtaMCRh1Insg/DUUFDqBN3xtJIFX9C1SO0\r\nMt7tt68ckmEhQ76ryK3KbWNfsPFzBy2079ZL3pXjyyd7Ofibl/SP7oyUxDH7\r\nCTGnssdDofoPgDHAcdJTwMz0vTNxbky/zZd1NujdXDTTB/f/sYy7wTVuzJaJ\r\nkWCiAZcrwahbn13IlnkTpeCm4wBeK0AoGoGwrYdufp/mhBEooAhMFZpG5ava\r\nLnPrmVdo00LuF5Uq4XQ6NVKDIvwL814adgidN2b1++u6GEpSxzY01Uge986s\r\nVhxDAiSS0FYXurSjGflTt4YBVxNWKiAxDvOWBZYNLV7asHNatbiPRSfDUWpC\r\nZcxrL7JQstAzaAHnOmJ9E4ovv20AETH7mc2atLXfYfv+NX4JHAZI1n7ey6TY\r\ns+Ouuaon5HReJZ9jaGEuBgboLDFMyQloqRA=\r\n=Th0I\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.47": {"name": "@radix-ui/react-context", "version": "0.1.2-rc.47", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cc308efef2eea477c585d6ad0a35029c611dc8d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.1.2-rc.47.tgz", "fileCount": 8, "integrity": "sha512-0FphpYHmFDKXnxBGgshj0rxDTkws0OfxJ+9ozYtKLDXZ0XLfTt4iTdtjCz8bq7y/6NTa58xnydooGMvR0sVibw==", "signatures": [{"sig": "MEYCIQCBeGlJ2QLOqyFl0JDQHGuCBqE/LruDvrGKU9nZydMtyQIhAI8+gjS7PjhwapEdOXWOgpqjRq224EYOAZ8mFLCuGwts", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36564, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CDXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHfA//d/jtFyjBu5346QX9QltxOob5QpMVyV4iv4gCZR94HdK6DU1C\r\ne/1kPyQiOeVUzvLn5t6h6IOWLaRWCDrXu9DH3m/DRTITa+GNLRjzI5+69FnN\r\ncLyYZOsXWp5jTjzOgqOB8ExnY2DjTHwj+7O9xU4peZdPOV0I+wNTYzAb0hO1\r\nlVqf5eQ4Fb7aCMfmiWGYNAqPHtZf95FKRTxh87IV7BSJVUm0Mq1ZjdbHeKv0\r\nj38q7VvP2XeQz58XWnMDtpnf2jMDHX7413WWFVXjpsVbSzr7Xh0RgG9jcAIc\r\n4G7gvfgVESANKtVOaVUBpq+v0gcvxxx6ECWWJyJBxmfwAASBZI/55E4YIV8/\r\npoEK/ZaGqex1uOYqM6edbPCzfvIqmvevD07aPHwGvAq3C8soC/B5eNST48eu\r\nEX0+bzghPuDkrJzJ2RFkdlDcJhBtRKlTp221dui/7tff5gavi2vXaWJYcBh+\r\nnMYeNb6GJZwrV41LC2VNg9EFE43ui7ZUpwh1s8QemHlSZENH39ooicn6yz1f\r\n2Mz9ci9eigo+EVRysXNAl2mlLilsNhm5gHU+7VdbxSsXr232k1obFSB8mHC8\r\nzdZjIzm8TpDBLKf9lRebUOkTI27XH7Ivuc2lDUQ1k/UK7gfI8Ee7qguh/8yM\r\nKrLXXabsL3I+50NoVGSPJ1jibOASOVuotz8=\r\n=Ssht\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-context", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "be356fe1e138633f19df471790c87b17f4ebbb9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-UnUoECDunGi3/yRf09o9suZV5rY35zNCP2R2GgkDis+LjpQu5P6JE8fEhLqCf/S2tcCYUzjLlZyM6y2rLjRFQg==", "signatures": [{"sig": "MEQCIEjLKiyfeTg0PZig8XTXFny110pX+UnSLiz/8J+iNBCAAiA7EGs6udEPiYl0LMIyR6lwFDMwu+3iMsYC6mJs8odr2Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36563, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Eu7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqP6g//YnG1gLOO54J5R3b3dtf0Z+C1qCsX6GunAfTG15FgEt8VP8DI\r\nYcrwYfH8PaJqfi6CT8GhZxpI2NyCO/+9nGc+bws8Yrn35XF63LKahQXMDyYi\r\ns2v/J/nJfkNxXJF5tubcGdf8guIl3pDc7BwXxfvEyy62tRSwB0t4IqIlcqN5\r\nTcMjmpl2AuRUH7PBE2SzwXFe0ghDDDr+LYVShGJ0u1+l/Fp0ssdUUmXSULyq\r\nInd2ZoOSXXlLznJhSG5vbNLQUe9hSmRxVsw3W5J9FeBVWdMjKHTXuRDs/Qqh\r\ntJqJknU5OZRym2c4Y4Vvtm9J6A+rbjy1/I36mzLG0tO1wFBwVIeSsA5vOT8i\r\nqdRWT1fv3vUJhHgHNMLoMm50kc3Ke5h3TaLZOyUYL92ePsaVvsnBao59V8N3\r\nz0zyKXb4yd1xSi7180nEzx1FmzLXZEOOdIfkRAImyIfv2Yc9n2tb2+hMvuRR\r\nENE/+DvwgowtEivWyyvJy6+NouyjongRlHWaM8J9gj+az62yw6HgBTz9FME6\r\nu8sBnE6LyBMw0npPdjP6RAhvqaKZqmw0/bcZdXDOCkNQXIi3fk67qmXpkZI1\r\nuAMyu7izg5mrUVLKJMrCsVAdWdhTx5x3LtBoNRhQEbNOXgKX7E0o5NLpkh21\r\nxAT3+D28pthWdmAqALlmmCMOT26PA9uR0U8=\r\n=Nl16\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-context", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f38e30c5859a9fb5e9aa9a9da452ee3ed9e0aee0", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-1pVM9RfOQ+n/N5PJK33kRSKsr1glNxomxONs5c49MliinBY6Yw2Q995qfBUUo0/Mbg05B/sGA0gkgPI7kmSHBg==", "signatures": [{"sig": "MEUCIFzz00sqvto8kNaxUDwcCgcFpJHHks4W3JCJ1FIWfxaoAiEAn0aUnIeT+OjrCLR51DQU+gUM2Chgj45n56WNxUG7TsU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36530, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFHg/8CiAVJI+MO904DcrgaZjwjSDNBpMO6Ab20cVPokW/z7IVPl2M\r\n7Ukoe9/4yg4SOOradUECDtiUm/YVoSRhrody91dTyPDczRZCgDQeaUAIoPme\r\n6acog6vqEzEXrD4SWlbmQyVBDkt7z1D7DQ6bpRs7sJUVIcQsDRdYH1f80ccE\r\nDdX//KHu6oxSPMugK/l905yipzaE9N5NR1YDub3EmVp4R4tk1s6Aa1F60lV4\r\ngEBTkt2D83HY4Ln/bQYBNr5F0WXPTmOhcPd65s0bxyiOanrHoTTsvQoHw93Z\r\nT/izpglrgOTvRjBt50qxxwL3QeUhNtc2IeU+P1lAlI29KeDBYftQEXR7IK/d\r\nThADZdkr0f/1aQTDOb2WKiBBkqmo+u3id98tWHxe62br7GU9e6VRBCX2RKBv\r\nXEEmWKCBfqhBQ2rQ++62hY4iqF884FOUGChoc6D4Hgl2BL9as+xZCFGWtnhE\r\nD39Xura2AJhlPvWWWBgD1Z4qc3CILaaqj1m+MpuTAC6oQ99ea5bu/sbWYOfV\r\nzQXrKO162ZDDrypHyqitsc0E8/IQQWhbCZnGDMy6NMDwKEotKndPLED++LRk\r\n7FlOaSP5LRYUcH/KebJy6L6+koMrKiQ+Do/106072Q7zC2PIfKWXcAH7b/wv\r\nzLBZHEJLWHkns0YGXinrxX9fYMMhYhHx4tg=\r\n=zkKA\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-context", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "00ef209128ed9b6431c6788339c5e17301eb5e4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.1-rc.1.tgz", "fileCount": 9, "integrity": "sha512-KbC4a+N6xYBZTNPsqyG+4xEGX7JFXkSEyJSJyFhMI62tiRfFxnQQXCXo0liF5F3yHqHwIme7NpMKlvqbCKTXJw==", "signatures": [{"sig": "MEQCIBiUhBM7/DGYPHCWJ2ZzHniCcpsQ8/9UET6mGpd25jaHAiAbQVIdpkqdpWz1j6YSBdrMTpmby+2kqM+hiquDKey4EA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37907}}, "1.0.1-rc.2": {"name": "@radix-ui/react-context", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "755940c14045aabf657217959614b9adb6635bda", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.1-rc.2.tgz", "fileCount": 9, "integrity": "sha512-IiYjAOXFn9P20FVrKdjYN1bRLmZ6NGWXfk4iwwS+pvuieo1Vqfd/X5T58b/9YT550peyWqruHWMcHAqFs0Pwpg==", "signatures": [{"sig": "MEUCIQD2rgt8wa1E8eu7R2BbzPtL21gV+pwKAMwKWXRyydhmSwIgP97qC7uQK1XlGiAFwZQlYTtzP/EtihnX9hNgvVjgFa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37907}}, "1.0.1-rc.3": {"name": "@radix-ui/react-context", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "1bf89bb25254cd10af0623b365a527106b0eaa5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.1-rc.3.tgz", "fileCount": 9, "integrity": "sha512-eVAmClbowh+0lqiJoesX7kc/xLiwoCshwyFxPFtfHmdmocQYgN5sxNdMkP1OE4EkdY3xxN3HWpk/7pHMk6rqGw==", "signatures": [{"sig": "MEQCIGL6Tg+kIDcpuIL6EOeAKrSntCDV/edRhw4S+2x0fQjZAiBhPRD/XwZepMveuOmihnezcd2cLH/e3HOFYiVLjiGudg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38016}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.4": {"name": "@radix-ui/react-context", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "fa499777b18654bf1468773021f2051de48144f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.1-rc.4.tgz", "fileCount": 9, "integrity": "sha512-I0R76dfcESuTkoPzmb/ZdFnsU3wX+2VCgdJmBZqSEwIu8Y8T2NFap0VGO8sznbZhOo3IvJe5bX6UfuDnXMBQvw==", "signatures": [{"sig": "MEQCIBaJOtKoA5X7t92WfY1/G0Pi3V4aml6JxNIqDLhwULc8AiBGzFyYqGINyZcOfhsAdK68EZRVDpZnhjr0zp/CfGGc7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38016}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.5": {"name": "@radix-ui/react-context", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "2b74975a74e039882d5f141786adc7d00b2a360b", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.1-rc.5.tgz", "fileCount": 9, "integrity": "sha512-KQTNZlpULb7OyADKtyWPSXq8FZuNv7TEYVPNxM76K140esU2jIEOASCBx2KBycIoQ36Q6vOV8sOfpoI3OB5fNA==", "signatures": [{"sig": "MEQCIDTRC6++1cTfR2BK5tW32JtDs21fzfz3gbEdHbOzZYloAiAdUSR3y12OSoekD7tKDNgaTZEAfOw7ZousjjkNdH3K7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38016}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1-rc.6": {"name": "@radix-ui/react-context", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "4b9600197ab873de41d669d2c6d40f2295160c86", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.1-rc.6.tgz", "fileCount": 9, "integrity": "sha512-MoKnfkNAVbwRpCjhPJR0DPRWq+ze0V9VElfPPlkpgKPgnDv+NzJA2NE2AE0g3RnXwHz8mCSSXBFe5PTQ8JSiwA==", "signatures": [{"sig": "MEUCIH4db6d8q7RX6bi3WC7LQ4BNEqnU0Z6xajmvPbmm0daFAiEA9gE59rUX/JEtw3CpHxVrPaldrG/ch5S+0vaMvEiY+DQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 38016}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.0.1": {"name": "@radix-ui/react-context", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "fe46e67c96b240de59187dcb7a1a50ce3e2ec00c", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.0.1.tgz", "fileCount": 9, "integrity": "sha512-ebbrdFoYTcuZ0v4wG5tedGnp9tzcV8awzsxYph7gXUyvnNLuTIcCk1q17JEbnVhXAKG9oX3KtchwiMIAYp9NLg==", "signatures": [{"sig": "MEQCIChJcuKF7mPwt34Jeai59mUtkWAbQZ76LATQWxYoHI0uAiBMBTHMUFB41XlXtovFRPuiJEInf57pRUaNjDTcwE+Cmg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37983}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-context", "version": "1.1.0-rc.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "22f18a7e24b478b4e8026478f0494f9257c761ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-3QPNjirfOVqhj00BoBxS9NM8cii7QmoKnuZhD8HZO8R/3JIDXAIhT1QXJyFX/v6Fz54PgOVoxiucGlkBg2iJHA==", "signatures": [{"sig": "MEUCIEyNhZk5Cvv3XsEapY4Pj3UkrwXfv6I/qWpXOGOC3tz1AiEAi59uIUaT+X4OQoy4YC2Fks8+vFvLk8U8XtS1ffuFhkU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27985}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-context", "version": "1.1.0-rc.2", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "1987b3e13862ccbe0369eb81780ba4ff969d4d23", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-RGxwhQKoo8Y524i26Q7RfuJOCzPChXSCo6n+HpPBFObPdFD85C85uMxcDLNyoyohNHXCj+w06vZhvNG/okqfhg==", "signatures": [{"sig": "MEUCIQCoeVoA8YdReLFsw/lCF9naMEj/EFkn+gAtAxOOWOBdgAIgfn4BHtW/6LfDw1BgrEEFUgazjvFBzXe0I5gnN9Jm/Zs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27985}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-context", "version": "1.1.0-rc.3", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "@types/react": "*"}, "dist": {"shasum": "6d594f2d04ddd0094a7a582e6e5abb93e03a60c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-U70bK2FBpvZU9KWQ69/OmvcDZ/5g8ngz7iqCQJWJMTYVzVSS1t/W7exQK1rQcvkNVjAbtj66GJpj8EKSXXARqQ==", "signatures": [{"sig": "MEUCIQC5ROUE9a65HHGxljyJY6Ipae0CG+bxS4oufIWbbfG0KgIgZDt4f1sfwd1r4at0JQL65elxHe4D9pDRf+cI1d8RrcA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28025}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-context", "version": "1.1.0-rc.4", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "ad4f9c2775a32d7486937664ad52d2846e977ce3", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Lca60TDM4pCKK9qWpTXyaYNQnbimtXNV2wUHJsVJgCkq5NsCouGMdMSfWpTwoLR0rKrSMJFIGFyrZR8nkavaRQ==", "signatures": [{"sig": "MEUCIQCLvi/+6L+KmJlBYDnedZJ8r9/w8xT/3rQ2Xl9YFkqNUQIgXgG89XZAy4L6Hep7eeM2CjFGWkffA09jAMPicHruBHk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28186}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-context", "version": "1.1.0-rc.5", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "51ba1e34469c145f704805e8e2c4b634c41155d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-nzqCSUthc1OYjEunu9S7P0PsquRSpKZ6qXbohEYUTWlEnmW0a75fBL2c4Oyg9U+WJ8T34Uj92hjoRYAGuiyaiw==", "signatures": [{"sig": "MEQCIGqNfqWOnsoKzaHeqgby/GlAjOjOHGUrfiXxCprk3L+BAiAfcpIbsI3vO69RjD+YPfB/vv/i1VY2h/xl54SXUGmw4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28186}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-context", "version": "1.1.0-rc.6", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*"}, "dist": {"shasum": "bc874b5681653b15ae93d7e25f4c5711a442b6df", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-2FM0kbBBUU1TNdGEy7k2YMjMvIOuoW85KiS1wC5P1B8wpKRRiSSCz7O4zpvGQxGrY1T1goA4ibdg/cgE7CU+uQ==", "signatures": [{"sig": "MEUCIGLeahgBIO+h8DN8MHSip3RW/pXlvm6dxblWjnPMhcb9AiEAxmfcqgXsMvZWvc7vUFA0ipQG01U9rlUeN15JnWBRrb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28186}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-context", "version": "1.1.0-rc.7", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "a33d69c0bbcd9be7fdfb35176d97392dbb07c620", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-S6eAl2yMdKy/hgh6x0UCFGqOqjid3C2g/9TgIBJY3mCgQ6M88Qz2H1JXuryFtlwSIU9r/9CrZCc8HFYhnfz7RA==", "signatures": [{"sig": "MEUCIQCyzao7gFF1ahZI+GX2r1/fjZ01la/5MRQZvuc2SuAFrwIgfwhez0yzP/kRIxOJJilR27NcYa38Y0fjUCqO5uHtrWY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28200}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-context", "version": "1.1.0", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "6df8d983546cfd1999c8512f3a8ad85a6e7fcee8", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-OKrckBy+sMEgYM/sMmqmErVn0kZqrHPJze+Ql3DzYsDDp0hl0L62nx/2122/Bvps1qz645jlcu2tD9lrRSdf8A==", "signatures": [{"sig": "MEYCIQDNpYRqF1OXjXI7leySFuHVstSoMsluDP7vX68ZjceOQgIhAPfxlg1iK3b5NuSDvIn426YUPIYhLfU3uifn9yAmi0CN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28167}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-context", "version": "1.1.1-rc.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "87ffe448a11ef02af86a4e5f7cb9f4a794a65db3", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-CZJ2aF4H3Rl9wiIYtosaGaiOZLvGtlgHie/gTPFOAMlR93tpXZrnqeEF0w3BCVimzXRc1L3werZy25V2rLkuBA==", "signatures": [{"sig": "MEUCIFJApZcWxElUzTxUNU59/ZU5CFSvLXvlPtH2OofTCGXOAiEAog+IlTw07Eaw6OcaMA2aaaxy8uxmfP973sga4WUsiPs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27892}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-context", "version": "1.1.1-rc.2", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "8edf80007be58d0ee247e235fde9bea1ae4ee72d", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-+9W1Dv7pVQ66hYftK1huqDoZYyM2kP9sg7NrecIZY3LQNMmCdchfwvmGdESTYvUxVgtebyvjVL8sdNZO3IFGFg==", "signatures": [{"sig": "MEUCIQDOVXEND/MyoyDpvfs75jSb/btxcts/kQ4nt+aO6e7BTAIgZWrn9CJHv+2Lae32jjLjLtNWsO1PeIPJOmbeoKf3zGk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27892}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-context", "version": "1.1.1-rc.3", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "89b74f611456950235eae0b1e3eec3c6395884ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-5UcJOisJp1OzCFgWDQ4hWFly5pLLVb3ZoZm7883pJEmzMvtQTVgHz6AXGry9MF1O+9k0OyFIL/Zzsll6A+evIA==", "signatures": [{"sig": "MEYCIQCF/uu+AxkI2h+UKxvWLsfXyp7ECyVUtN6J/7YTfrS30QIhAPF3JRLyKUZGruzLOEgjVFPikp+N1ccd8EnZSkv8JjPw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27892}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-context", "version": "1.1.1-rc.4", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "f350f416978b3a18efef9809a12c149a48b5aac2", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-jt8Hwaj9gHreek/d5FwchLnpNGAUHo4TjECL8HzWbuIT6xVWjaSYil8DXMAyIAARosQ4Pqf4XAAjWeYgBDtqVw==", "signatures": [{"sig": "MEYCIQDyBQLKWy0KjKKa1ouPSeFXy8BE7r6za6K9c3wdlvQ6owIhAJ/62IRn8qjNd5lGPIM+ybk4Byz2modVOFoUyjjpvM6P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27892}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-context", "version": "1.1.1-rc.5", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "34303b875af01c9ef25f4ab8f619119ea495a525", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-C4W/JcjHK7OkijWnCzEOrHypOIeGkqJ5JzgQwwA3h4DtpGSnop7FHpKn9fax6x/Bu9KDaFq9dFIC0tbZ2rPwbA==", "signatures": [{"sig": "MEUCIQC/Xy91Rm+SLnJiDn9AuXidPeL+NdTn/OPCWiz3wsqmiAIgM+92SmA3zF7P5k6c3CLzFeAoiphRBcyTtjraCoE65Cc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27908}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-context", "version": "1.1.1-rc.6", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "ad37aadd04eef0ab47d1bb6839c4c843251246cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-3DcJRgLXBuvifGCYKrB3xNnEVt197Y6F1puaw4zu4vK69SGZlt29DiIqX3K4MGCgaa44QhI+Otgv2eeeArYCVg==", "signatures": [{"sig": "MEYCIQCDm86ggMxOBZDrEJvBKAAlk72s1UC2u83wzX2pmcpOxAIhAPIcrRMDzW+tPfeEbe35gqoGSexYfLVJTgIKC7K7uWEl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27908}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-context", "version": "1.1.1-rc.7", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "bff5feb7bee23408937118bf19dc7b63ff0c1813", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-bAKoVgwnmdhm2RDI+CspVwcDGHeX/wQEdlwm1YN4pfV8pAyXCntWyB7TMKL3zgr4+0hKBiSATmd6/ZjJ78JoJA==", "signatures": [{"sig": "MEYCIQC5aBA4cUz0vo1Atn9bg+mM1WWZY75BVRfkQyghucMOfgIhAJRKBp2cl0EA3mLF0fi6mto90bDvW5tb0X0TbxCBHtes", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27908}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-context", "version": "1.1.1", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "82074aa83a472353bb22e86f11bcbd1c61c4c71a", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-UASk9zi+crv9WteK/NU4PLvOoL3OuE6BWVKNF6hPRBtYBDXQ2u5iu3O59zUlJiTVvkyuycnqrztsHVJwcK9K+Q==", "signatures": [{"sig": "MEQCIBmgZp0+s29Q7KtthQfhMo5ETAoL8wtrFEN8ItHih4zZAiAyKwhZ6xLXEK4mwtbhxwTM1nrpWW7K6pXnalhN+Hs2Aw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27875}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-context", "version": "0.0.0-20250116175529", "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "14c587f23829205dfed25348a60043b31a04a07e", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-6z46pS6QA/MfENtvin9z78aOUVaa+vS9yuZhi8i6RrK4RuCAGO4C5c8a+xhhOrlLaBkA2dlgGB2ZGWUER85V1g==", "signatures": [{"sig": "MEYCIQCpOtehf0I6vVkq/8qxP9RfWC9Rs2VrXDjYFysuDnra4gIhAPDT8lKNY66WBQuKwT3ZTC9IntWCQJlDelv8EPrdeVtP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27836}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.1", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "e88b0ec8be98a60103aea35cd4acd91b679080ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Ltu4Ox04K45oGrj46sq3uGfGBkS4E72LKmEke53THjO2EMtkIZGEZCGPm5bKageUUkiS98XI4XCd3DcXwuVokg==", "signatures": [{"sig": "MEQCIANciweIdpguV3Q6sNwYu3ljNbsJ3Tu6B1+HRFN1ZPCYAiBl4rxwVbhYDBXMKxNWYbE1K8OgmjpNMfcTDyP6eK7ucg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28213}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.2", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "e58ba090294611b5a0d19470d5e3eae8012fc06a", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-OE6vR6M2E14fCLcD0qz8QtqLQqhFGs04nR2b+cTsjuSPnCEObjw4DWkEfeh2w/Q1OpwkKBFb5yGAlbhSotLDyw==", "signatures": [{"sig": "MEYCIQCnpEIYaGbBBB+G4DdMzm18hHsOYlP1xUwAiHT80s9UVAIhAKuKMy6phpAL42Zz5OBNZOrPQY4e2CsOgrkdWf4jmtBv", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28213}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.3", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "b36979fabdaf4ebeb0f9cfc619575d19491f6226", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-8uVjf9JiYMgeHzCeHAmcYyXxI3c8cSAAwDxlUrtTjtq7SdOP26w4l9v2mNYcQW28HoxqFv3Dq4Pwp7DY+vM+Lg==", "signatures": [{"sig": "MEYCIQCR4MH7131Lp6LEiTerCui7eBtviMkpFXmgW5XUjlRCxQIhAN1ovNJTYIDmFdC10doCws1QqpLVgctyKkIRBxadz3T5", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28213}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.4", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "c5025d0bcacef395b9cc8d8b386197de82152d0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-+pezldSn1hUGEiBHdvoMqXVMS6unE/lmU3MMDBMOKDMOyu60nNBsDDwOuMA2SS78WJhQi9dQx3gGtMnnKjon3g==", "signatures": [{"sig": "MEUCIAIvtj3PMzJwhBJNYLC37qUT6IzFZlpQB/stlZjmn/oAAiEAhTFfu9mXf9Dvs0GYPUFZeHRBBDu9GR65/Uj+/lboZpo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28213}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.5": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.5", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "84c947177c4236faf628a6aa9dfc94d72ee76327", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-WMkoKzDPh6LcZubfvfrfdeL9WBou+WUZODT7r0P4F8JlG5kbaC0FrGBOMzWAE+nb4jfnwo20LiWYQsMhWCP7BQ==", "signatures": [{"sig": "MEYCIQCzg4APu6RgnXfEjDjp2ujoD1xIJRmuNN8Eyafo9fsFdwIhAKeWEg/TMdlP6XS0HO+GtPtNGvH9usJW+ZVye9JNxF3I", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28213}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.6": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.6", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "2ef6a3b6e9d9a8902e76e057a3abc2fef6d22b80", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-+2UfCW+v8fGD8dPPJfNq953+ArJMX+mhQMEqEEXQIn/ynq2BArlMB3JkpTzxHrM4Or3EkbCMDrlPKC8O18xDlQ==", "signatures": [{"sig": "MEUCID75/j7cNJ3x/e3Ltl1gTxNjbxuwcujprVkDcx7xRW1yAiEA5k521a8gWELIqOXASNcvPycoimx0hSQycANn/Kv4tsA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28213}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.7": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.7", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "1e9963e5fc253f764d25273ef8ee7c453051efe1", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-8OV6bwrtq97fMnlXm6Xg9/63yVK38x4QJWZhaT9RD5jr6zGpBJHj9+fw3OfQD5E20IXhi6xGvhkwgP+5BKszZA==", "signatures": [{"sig": "MEUCIQCx3IyVgki/FFdINXwJ9mPidTelYOQRKza0LuT7TXljwwIgOsYJPQ1QTRAox+2d153JmKjNypdYGvPArUgVFWY2IkI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28213}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.8": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.8", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "71726bfa64bbd14ee5426ce075d859b0e54dfd2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-tMZzqUrhBrw4rtvlWaldsIvzWGiPhCBk3ooWZTJTmNH5NGc+xlfpTVlDgovoP3v681e9y/CTXCKB237M5kBuZg==", "signatures": [{"sig": "MEUCIQDUIblmNFXJ6JUVG0Nq7UZBEmAaVPsQvm03mMFs5ZuJZgIgEawDEa7OzGEUL8MV7cNH2lQGtps1mxew64Ar5uf3mhY=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28604}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2-rc.9": {"name": "@radix-ui/react-context", "version": "1.1.2-rc.9", "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*"}, "dist": {"shasum": "e727a42ffed2c442a08e08a318de06d1c143788e", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-F1DbRG3+YofFI2dlWDCfbnnJGbFEG9fOvcxSSclZ3d9B2MbQuKL+jX6LQFaUJ4ITp7fPSzNPWSmoYlPvIjS/eg==", "signatures": [{"sig": "MEQCID7Og5Q/aPUKebxWCpcPFXt1HqUSFhCf8VttmQDHXe2PAiBkpKiGqAuVuIlImBkMbT1NwuN0bFWD+NQMW/4ZI2dmOw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 28604}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-context", "version": "1.1.2", "devDependencies": {"@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3"}, "peerDependencies": {"@types/react": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"shasum": "61628ef269a433382c364f6f1e3788a6dc213a36", "integrity": "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA==", "tarball": "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz", "fileCount": 8, "unpackedSize": 28571, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCyP5CwEmIDT5gpj0Irx07c7gH7r64YJiBhOkPloqc1PQIhAMMdq7SXcf07c8jvCJK4EnLtOfO2rn3ScVIB/I80+6AA"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}}}}, "modified": "2025-04-08T16:46:06.696Z", "cachedAt": 1747660589278}