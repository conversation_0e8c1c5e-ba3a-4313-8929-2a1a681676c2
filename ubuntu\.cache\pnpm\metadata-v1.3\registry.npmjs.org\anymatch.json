{"name": "anymatch", "dist-tags": {"latest": "3.1.3"}, "versions": {"0.1.0": {"name": "anymatch", "version": "0.1.0", "dependencies": {"minimatch": "~0.2.12"}, "dist": {"shasum": "d34e745e4f79f7c07e65a5e6f46de0e4489bba05", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-0.1.0.tgz", "integrity": "sha512-8ioep5XHFKiNPPkkMGFi0k/oDtYKtoK2J8+Lo0ZdJpzkqwIU90qYGObR6Q0hJrCjJWke9yilfga6w+PvWg4RQw==", "signatures": [{"sig": "MEUCIQCBVXNIsmKZDHQ7bQZhMJ43cl+eM8Nd1pEkDms6HDW4GQIgNdZXjdI1O/ADt6o7yL8SOicOOPVNWpljL+Lk9Qvly3M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.1.1": {"name": "anymatch", "version": "0.1.1", "dependencies": {"minimatch": "~0.2.12"}, "dist": {"shasum": "cdb873bd9083a424c2c4e9b6835cc40f35c2910b", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-0.1.1.tgz", "integrity": "sha512-I8za6r2or0IUj7LEAolVI/Iz4k36rTh6cuiMhRO08MP3KSIj8LhSpX05mY8J7p1zFBaQ5ivVVGZpVrIbVPQxOw==", "signatures": [{"sig": "MEUCIQDsklf6oqzVLEk0Bp3qtALEHVbonF6QJlw+uwr0dWlCeQIgd6H6chL0GdDN38/uG2XPAGWcUMW9tkWG+8Yhn2Ketfg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "0.2.0": {"name": "anymatch", "version": "0.2.0", "dependencies": {"minimatch": "~0.2.12"}, "devDependencies": {"mocha": "~1.17.1", "istanbul": "~0.2.4", "coffee-script": "~1.7.1"}, "dist": {"shasum": "e919a97cd43373e6e645bbb7b644e2daebeba405", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-0.2.0.tgz", "integrity": "sha512-+Ga/bgOG8wVyuzBiOoRX9NXMMmNa5uHozMnJgmjoTWAj/8cPQ4Ni+2izK5O1tHATpuagFi38xYkfrQGC4vUgRw==", "signatures": [{"sig": "MEUCIAuxxOEp7tx69nZ4cmOS+k3t+ffxiGQ0JLHNlXGfRGg/AiEA+3APgJtDOFIsEukUcJ+51QSwCf9SMgE910OonEBP4O8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.0.0": {"name": "anymatch", "version": "1.0.0", "dependencies": {"minimatch": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}, "dist": {"shasum": "0aed64d30bc25973afdb3155eb87ae6881e21b1c", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.0.0.tgz", "integrity": "sha512-lLcjwlmCnoGH3xbMVvFWTZiftwrCE6gRA8I0OhQNPbdHJYs92F2RjVwy5Hc+gYtCpDhmMA6UUCe1+6OUjM3beg==", "signatures": [{"sig": "MEQCIE7xq1AoxygQyUtrBFNu9X7jisM00yGLIiLhc1Zs9aGtAiBl1/JIiV9Smm6w1kWQPfaOFGg3rBieRZMCBNJGQ8prdA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.1.0": {"name": "anymatch", "version": "1.1.0", "dependencies": {"minimatch": "~1.0.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}, "dist": {"shasum": "ebc63275cee368a96b300f31623bf9f228d428e3", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.1.0.tgz", "integrity": "sha512-daz8S6od3trUKIvcpAZsuBz+arvdkTFijfnAuT9QRUff77lkdz7kU0qyx+5bfdFbU6zVlMN/HbdDKsKZLxQT6w==", "signatures": [{"sig": "MEUCICAMbGb4sdLwlNxBWONtgx3jKoYEdCh67e/4SVAyfvBdAiEA4Waz7syuopnBd25PnE2QaJLYicMDC4Z/CKwG2vZyA/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.0": {"name": "anymatch", "version": "1.2.0", "dependencies": {"arrify": "^1.0.0", "micromatch": "^2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}, "dist": {"shasum": "f4df38e0f4943bd64cd6fc0ae32e4e191f8db819", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.2.0.tgz", "integrity": "sha512-hQn2PR6ZmJgV0+2vaP0ntDK9FWPBsVyL5nYs3ajt5rubkdjYbR6Y8oCxWPVQz/qd1XEyMf0S/QxFytfej4mGyA==", "signatures": [{"sig": "MEUCIQC/u9VMGmJGz6CxKvFklAZLw1BHMSXaGhv+MGLA8/nC7gIgFU4XyLZKnkVdegUgjuGNx/+zHFmVWUMr0NDz6xvAbOg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.2.1": {"name": "anymatch", "version": "1.2.1", "dependencies": {"arrify": "^1.0.0", "micromatch": "^2.1.0"}, "devDependencies": {"mocha": "~1.21.4", "istanbul": "~0.3.2", "coveralls": "~2.11.2"}, "dist": {"shasum": "a7d77e8b62bc27cb5309d5ed905915b8da3f210f", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.2.1.tgz", "integrity": "sha512-bejASqqXzaLoWTdA3XJFn1hsDjD/aTroO2xlGOSNQe9OmYhjJ8vGXvHFDMwK17b767ff4X+4H4KDVABxYArmuA==", "signatures": [{"sig": "MEYCIQDzEXPP/fWAvyqoSHb3U8dLByHP65mP+mlfiWlieEkkwQIhAMnQzku0bCErj7mpQRhvYNhZd2ME7epkuE8km1iWnplD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.0": {"name": "anymatch", "version": "1.3.0", "dependencies": {"arrify": "^1.0.0", "micromatch": "^2.1.5"}, "devDependencies": {"mocha": "^2.2.4", "istanbul": "^0.3.13", "coveralls": "^2.11.2"}, "dist": {"shasum": "a3e52fa39168c825ff57b0248126ce5a8ff95507", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.0.tgz", "integrity": "sha512-GbRpOH/EMz/3Zq70whK2Q2tkbxbaM5IAU+EZL4zxnEqGtzJWFCJ3leKc6P/w3UmDFIB/GkwfeZJ7ChL7bZMXJw==", "signatures": [{"sig": "MEYCIQCBhixmMqLwvANLxy7fjFSCa/u04crVfGZZgHad8XFQvAIhAMNFYb5xvKZjPPh9K6yqqXEokSRzlQe+BJBhWHee8G69", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "1.3.2": {"name": "anymatch", "version": "1.3.2", "dependencies": {"micromatch": "^2.1.5", "normalize-path": "^2.0.0"}, "devDependencies": {"mocha": "^2.2.4", "istanbul": "^0.3.13", "coveralls": "^2.11.2"}, "dist": {"shasum": "553dcb8f91e3c889845dfdba34c77721b90b9d7a", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-1.3.2.tgz", "integrity": "sha512-0XNayC8lTHQ2OI8aljNCN3sSx6hsr/1+rlcDAotXJR7C1oZZHCNsfpbKwMjRA3Uqb5tF1Rae2oloTr4xpq+WjA==", "signatures": [{"sig": "MEQCIG5xLI1LLNylaorA2IWCEIM1HWF3W9jKefbRUG0RMIf9AiBde7dPAETOgWw7fknY5FYD7BJjEC5Qm7wp6Zi07OJRew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "2.0.0": {"name": "anymatch", "version": "2.0.0", "dependencies": {"micromatch": "^3.1.4", "normalize-path": "^2.1.1"}, "devDependencies": {"mocha": "^3.0.0", "istanbul": "^0.4.5", "coveralls": "^2.7.0"}, "dist": {"shasum": "bcb24b4f37934d9aa7ac17b4adaf89e7c76ef2eb", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-2.0.0.tgz", "integrity": "sha512-5teOsQWABXHHBFP9y3skS5P3d/WfWXpv3FUpy+LorMrNYaT9pI4oLMQX7jzQ2KklNpGpWHzdCXTDT2Y3XGlZBw==", "signatures": [{"sig": "MEYCIQCKIzhRa0to9Wg0MM0aJQvXBnkMfe29dJDkpxLe0oUcyAIhAP4KyblHVv489+g5j3ja+JTSYhRldthEsrBlP3iXceHA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}}, "3.0.0": {"name": "anymatch", "version": "3.0.0", "dependencies": {"picomatch": "^2.0.3", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^13.3.0", "mocha": "^6.1.2"}, "dist": {"shasum": "53c81b67d20bfe144b3c5977163c6ee53141dda2", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-Y7idCXpN0RtVcBs9OTDPiMq4g4Klqr6ycj9ht4y+jIf5AoRfX5W6KRzp+M1rfWj9C2/r7G5ViiBWtMqjpv1KBA==", "signatures": [{"sig": "MEQCIEJI/zG4AJq024nmwJiC6bo7jk9nbuQtCQk5Wh39l4M/AiB7v976DkI0j4huLsh2kxCQ8aM6oaKzLjwLaqIeQUN3Bw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8837, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrmRyCRA9TVsSAnZWagAA1ccP/1KBgENFXZGXC4d8SqMQ\ntxibc6E1Vfi5iEu+/cZQdUltMT3gsGDbEZw1aTgXzrlTbp6sqDjzv4Uf1Hcj\nQYPDvt5oJvPNoSwgEgHVLtcoc+VzkWib/A2WZ5XxT/rNJkiOtd8jdcYyRZRp\nRxxuNxigLJLLbxZe9Ru60Gil+hwQjgeUlBeGYc/s5lTfTIKbb/JeHGbvSavR\nWtOD02rl64cUXFfFFIb5wZz9nWINA6qHZElcvOMOB9HPyriFJfz1VJWSy1vt\nWk3DL9bioJCkWm/FVPMPvNNInhHGTZgt4EWZY3nWrtbp7fXtILeMX0DEwly+\nrsE0R+aVxpir49WFFULthW++W4H4R5HuM9u1YY8qUUQYmI8n8yGokNxg+af2\nrku07uXC6maXqhhZpwyWBaSUyA3tiFDaUDmTfJOvO3kcH0ZKOUhqdWQnpbjH\n03Cn3J6MOKhjx2XFtkb4+iaji0/d9U34643ueg2Hrkw8YhJ5IMKzE1kf8M+9\nuE8wckTAPXJu/iIG6QupJCllvcnzW/rEQzbwnKk1fFKU4wS6e890IEnnOGPI\ncebBU4ejvNuVIJde++gTZeIdkwOcOWFIaA/Yd2enhuGTPFWDzd09I5iMpB5A\nxfcO7Nodd8Iu9HuN2iTA+7tbeNsa4K6qkug+jRDXElLD/tcc2F9It4XrPTsP\nmMLo\r\n=3aNc\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.1": {"name": "anymatch", "version": "3.0.1", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "dist": {"shasum": "a47b8a9e3c3f7f17420276e05ef39746ac1777df", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-WQdpV5fo7XSY76HPN4pqdUl13Q282JsV0gQ8OnIxQsqDEHDZJCBkQ89fL1Mb3tNiPzGQnxMHM5G2iG3k9O6yng==", "signatures": [{"sig": "MEYCIQDgJdIlA+h+GlDyCL2L2Dp7s4GQD8iB4OPdA73Pv5q+SgIhAJ1x7rdeYqjrV7iuv35e8LyHv4oMo2fz/VS6jyAo7q9Z", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJctwZNCRA9TVsSAnZWagAA0oQP/3+N7O7LxqILEuM6lj3p\nIhqfp2Ll1tZUNJ8pWjn7+9T+sjehym7jiyAjVRtu7y0IqUWCFcG3OGWzHc34\nZNZte5V7EOq7qSLtkfLs1YMCFiL/eIF5vMkgv+GXPbwQT4pXX5IWlM9pbqhx\nxQXx+0jKRQhGM/blvNYDz7ZLWqbzzzDY+zWu/aHG17CWP5rQLRABFHU6jWBA\n0tZdv1mpWRFHwfEeNeeTpoiWWV3g7CLvH2ovu0jFyI4WVpzc5mSkI6SDG7EB\n4ASL/cn0611k3a6OzLn76uOvgXZb8owZoB1UiruTfT1zT9J8pJ0XM+0fZoB/\n5z0oIspPkS5p1f8RxU++/5u89DGDlbqH+H3hD/JanId8Qq7PIeTN+ohLLDHu\nHeCtDKKMKzEhHme0Bn60qfK2TBIALADklwhbHF91WOsD/rnAtsoZYYCqA4ET\nrhAiXgepgT+a21R8B0/G8k7OMMl6YM/xWCzOwYWOFp9z/olBe+zOQ4QOKafb\nUt4afg7g2H2boTLaYor8BrsyC//lWA5UibzzzE9JA7BgQSePct1UIobLceXs\n3RN/fbctvm8kjpdNBx7pm2QDuTcWWNSv3BaVRTaEW8AwdrYs84Aj9ct9oGVD\n5IV39zyW/1wIy1tmp+U5kjwGeqoFlNEi7Mby1h06FYz2PM+q442eT6mzSj+t\nzeiP\r\n=QpjV\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.2": {"name": "anymatch", "version": "3.0.2", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "dist": {"shasum": "ddb3a8495d44875423af7b919aace11e91732a41", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.2.tgz", "fileCount": 5, "integrity": "sha512-rUe9SxpRQlVg4EM8It7JMNWWYHAirTPpbTuvaSKybb5IejNgWB3PGBBX9rrPKDx2pM/p3Wh+7+ASaWRyyAbxmQ==", "signatures": [{"sig": "MEQCICyxfT+acPzQVvRpPEq5aAuFrG5uoQ8h+qkzhzIX7IQtAiA9fzTuLAcypNwe32p1sjNDNX0+84/DivF63tst0icX5w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8909, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJc2gZJCRA9TVsSAnZWagAAbk8P/3PVMM9Fo0vdbx1PmXe6\n80X34xHFBAyIztkgABwgkT8Jc6BXsvtjftbCg+QCO04QwyTusab96F3v52+8\n2FTrRTIVh1anbOC81SpQAXYekHnf89jtEEeS+214Ey30csg8yHEBXa/YhwCt\nGcgDITv2qKSAeuaq0BgRoyyUm/N1dB7+x8fAnHEJW2ONGMaggdEhJ2Z9COws\nNo0qx3rnl5sJ65626IrV8baQHqHuQkgl0T7Evv9HXzdhUvPd86p0Mk9HDy6I\nd/lhXYbZv4bhPMQuPMzPq4MX75lG5eNI4ljfHh0QUxPiWjl8q4R6P7iYeJsb\nNCfylBRtImCvMe+foqYmKNnd0LZvMbVWw2U3LIHXN8WMmJI4z2QdVNeEYXD4\nkbKKecO1w8XEWW1CwnB61OepT00dssBiYo2QAo9dbac0azZeNaiq5lj8ey1I\nSHMwTnzQ/vuU/+L1XUBelNLd141E2dOaudeCw8WfMEsr9+jKbkAfig/iFSCe\nou4nRD5Adp0aXvxN8s7NT09D/gToouUsY1p6iH2foCLqEQZ7yMi8jsmurKQ2\nhjMJf0P4DdZ7ZjdACbHvCjzeUlTMBE6L3XcITVhtA6AbWUR1Hp5QRxHoMeoM\nAnx9jzlLDEKPzD0aSl0IXEtWEDsNMU93zb+erjSANSomWglwwEdxR6dFR5yH\nTCvV\r\n=iiZn\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.0.3": {"name": "anymatch", "version": "3.0.3", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "dist": {"shasum": "2fb624fe0e84bccab00afee3d0006ed310f22f09", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.0.3.tgz", "fileCount": 5, "integrity": "sha512-c6IvoeBECQlMVuYUjSwimnhmztImpErfxJzWZhIQinIvQWoGOnB0dLIgifbPHQt5heS6mNlaZG16f06H3C8t1g==", "signatures": [{"sig": "MEYCIQC363knsI88/YnBYorI8n7BGUS0lY7hqKw9j1BcSVOEPgIhAPyxg58g9RIcy5wBci07y52N/Ro+Yh0+MYb1RkrxyRbo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8966, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdIT6LCRA9TVsSAnZWagAAGRoQAKHilWT5oawHndGya7tk\nsRSeHwdT6s3xC9gNNde1m2oiZCnWmDMyjrFD8ptjWQuiR6Zvrr6xdn9p6Oyt\nSKodAN9nX0fIfyI+fWx61aqnm6qVq1OF9efyDLZumCtIXVtSzSz+n1K55h8y\nOWeupQitP3YJQa84uCm4HjWzN4TtRZM5E/JBaoEiOin3PbYNXpsGb+vqJxjV\nOLh1wD2dtb5P2Zqs7MEIKNT8GitevxsL52nM9jQYiLZ9+TPkNo6g8EAvN9U2\n8nXhiLsr233Pix6lxQlDcm1oMx6xow1SYm48FJCK3Z2eyZhlC/Iznh9vc1Wu\naXT5Jj5BJc3aDjBFYgcn0opx7xec2zjgOFxvemvvL2Sg40EIDyOWkcqZM0un\nJluuzfhSqdY6QpzABUBqzU8A160AcpSzjZHMwaImMuFnKH0DFqo8MWJRkMR6\nQj12Eu6+5m5oNPhA6WWFVBVokGVj9dTrcIaGZ5IQfmlMwqIArmN+D1s0VGmK\nyAHKWTtbcS85OqtrsHglrWsHdCSfdzhrQBnsxDOPnCoujyOZ0yUhx36Y/QZJ\nrKXTM4risMNwMK65ABFK7Vm++EM0CwbCmMlaPBp94CcMQW8rowc0EamgmryW\nq+54aygdwNF/2EGX210pHEmTT7i0CvJJXfQyg9uBOHNuvEVvbL+O4qtJVuUW\nmX+K\r\n=qBBz\r\n-----END PGP SIGNATURE-----\r\n"}}, "3.1.0": {"name": "anymatch", "version": "3.1.0", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "dist": {"shasum": "e609350e50a9313b472789b2f14ef35808ee14d6", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-Ozz7l4ixzI7Oxj2+cw+p0tVUt27BpaJ+1+q1TCeANWxHpvyn2+Un+YamBdfKu0uh8xLodGhoa1v7595NhKDAuA==", "signatures": [{"sig": "MEUCIQCldXDynb08HBoOW6z/XyxSofb31JKjfSTVHNHra85VjQIgRcwlE1rPtBY6SL1mZZmVCzl7iv/ADcoNdHnFidejSrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9376, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdaH8dCRA9TVsSAnZWagAAYJ4P/Rde9PG38zsaas4YBbIs\nIceSPgLOSb0Cvnn+mbZzLT9nnykESBBaWjDLknjWnij0E5M9a1LEKyxl5rAf\nPnDT7sFEac95q7OBbLbZxGttT2FhAv+dKULVV2F8n/YygOBu4IXunZlWOKx4\nEXs/CCROrfhgEbpSvCyIb6Rx76VOZbTJ681OGqDdnM7ONnKtlwzDCxRDT5mQ\nn0VzZ6zI0hbp9WzJ2k8lJmGiE7SN3EXV8ZdlO/ofoqPwoKdQLBZ1xNZnIGvQ\ntZeBbWzegLqujDO9bvoXIECIs/HYYqc702CfUJ7kFia1f+WYXuNqZK/Tw2q9\niSBWuu6657WPJnhxytScXlGuUVcrgJ6V7gIb/mJ3SBZLw5iOwissd1vt0/X5\nxnR7b6XzL5beNUx1N5XebiJ4TjpXBSAoIgXlx3+zApWOUF8sl8ViLi4vrnCL\nkatVNebCwHxFrVpDwjg+/yTMBWzdpncJ+xvCpHlBTDiihPqzR3a8LmWvnUft\n9K78EO94OlnUpmR9xei5oHlo6WHW9wb049wWC3Fqmfm5rbQd3GvqotitxpkN\nnj4jbuRBsOPSunmXDyUDh5a19odYNZ+HRo9OT3PvPswHhqy4uOFlXKYAIDPe\nrATPr7dqm00a6T75vV7VYrR+1MsRm+HLc0AezpWPJIRtWMs5CQ5xSOeYUCXX\njZuy\r\n=WO7A\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.1.1": {"name": "anymatch", "version": "3.1.1", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "dist": {"shasum": "c55ecf02185e2469259399310c173ce31233b142", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-mM8522psRCqzV+6LhomX5wgp25YVibjh8Wj23I5RPkPppSVSjyKD2A2mBJmWGa+KN7f2D6LNh9jkBCeyLktzjg==", "signatures": [{"sig": "MEUCIBCSnnnF1vMIrCJ3541UkIb4rz6m0h9MG226A79N4pOkAiEAlwF73CmCKFw6KGre3j7WU3oAvS0B4ahzrBqCgXmxRsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9436, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdkrtJCRA9TVsSAnZWagAAAswQAKAWXsp9Lm/3tcBXanuz\nodcqg9m39/XiG6yc1/WD/7bF4YLcWMO6Ra5Sq7ouGtbsvdBAifOX4rsMBfy0\n69UJrWKb8hC/x6w/uMK38rC4nUB3nSmcjTcEQ2+7EITi9oHusQNd6trQ5Y34\nH0K06RcjsrQPwnir2MfeGhsydXqQWgY3NR67DZdosK6hN6jxBq05BMwlQm0c\nwaRJoTA2wFPCi0vy8nqcGoK3qU1/bGId6kpeXOW8TBH5gNJktS0isCl0J9iW\nt9q5Ebg5sj7P8BM+w78684bJfITFKyGk5KyFa8MjV6U5UsUHSNbU4OI6TLMF\nbUqvDd19mt3r+yfWECgmlmZDuwT5q5hDc7Kv96q+uvApXDYIHdZVdihQmQt7\ngPmIdvg8BEM6DoU2Hrft/ijqv74ZQPFZXRFKgtbKCK3IYPJZyqhEm0vYkYdn\nTRpBp2SqYDyXPg/hrC61cyKqB3aEoqxcIw5sITX9voB+FQUXBHOZafOtfx1/\nIB1bveWkdT4AcMaerbJqcjyrSMOoGlX9SiFxjjwsV8euF/GUwH3FL2zS2raJ\nXEY0ftOY07mPSHJLhCKK8LYWsy9V9GQ0D1318EZZWHdZfp+5b/o7Jp6opJaH\noFfksudPT3fXSuZBbjJHxF0WmAzHVnw/W+LJg8RMISGg9utObWZlJpztEDtZ\n3kim\r\n=DCgJ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.1.2": {"name": "anymatch", "version": "3.1.2", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "dist": {"shasum": "c0557c096af32f106198f4f4e2a383537e378716", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.2.tgz", "fileCount": 5, "integrity": "sha512-P43ePfOAIupkguHUycrc4qJ9kz8ZiuOUijaETwX7THt0Y/GNK7v0aa8rY816xWjZ7rJdA5XdMcpVFTKMq+RvWg==", "signatures": [{"sig": "MEYCIQDXiWE4Op4ypi8XwggX31wsPQRX1J6LgzZFVf2X9AXnngIhAMNn4Zl0JUF3kjvxu9vuk/e8s5TX8pZ/0Zh+splHXl58", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9544, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgbN2hCRA9TVsSAnZWagAAGJYQAJyPOwO3T8jXiUuhUyu5\nQHBRwDjwbwdqi76AZghu7TleqgW4UMEkvfDwx85la5o80LJWwySCO84VG6fG\nNbKN87Z9XZM1yqs/Wf0tFS+SGithE3eCZQIkzrrSmzdKkyTr9ffjl7HR9fcW\nCphEJwN3ffoxSDcqtid/g2Mxqy+i57x8Z/emmx4DJuSFAabL4049BMZzfZdw\nS/ST4mK5EaMrGwyJRP6DpXGdEOgdrID8FwbO2k41HX3x85LAtzBcO0qhdl7L\novL6T/a91OoyLAk+Ugd2vi3wuky5gmclw9QtNVDkAgFmJUhYhtWG1qTcwcK8\nm8zmr9d0coUo/zqXtpZ6/kf6EJC6priCKFvoIlbKbciord4qZWKL077C2TEa\nxDvy052nqUPhq2jiSa0JKXLEhDwKi2wUy9z8c+Hnoa5dfMWbw2N4B6aBqOUa\ncccGVblc/d0Pp7lbqUEdypzCuHu5SJflkELn4MHmGglXZamjVJdzxm54jlzl\nG8wF+g/KGMceL/W/I2ZrmjIKvKDwzkPVYtFnaVQw3+OUGVAUORfuNSLP+KIe\nmFu6CXjnuGe8wJCKxR4JJp10YRX8hO5YnkmD23CxL24IeOfL4V/5AUU4WdyU\nQ3n+ohPtHnxJ/lSEugbxfQfRvABWZ2qjsHKGdKFcuszhkWUnXwSVWdAnB49n\nfT7R\r\n=GvXf\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}, "3.1.3": {"name": "anymatch", "version": "3.1.3", "dependencies": {"picomatch": "^2.0.4", "normalize-path": "^3.0.0"}, "devDependencies": {"nyc": "^14.0.0", "mocha": "^6.1.3"}, "dist": {"shasum": "790c58b19ba1720a84205b57c618d5ad8524973e", "tarball": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "fileCount": 5, "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "signatures": [{"sig": "MEUCIQCb/AbEWxEB0qqOSnPkeWgpv5RGNzN2jbYAbN6bT4n6BAIgJDTSn9WVoppKMohO7pEEOa1eCduipQ3FOe8OsWJPa1M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 9654, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJje8GxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoixw/7BaWCNmw3H7ScsUAv7j5Btl3Wg6s0U/5fPxHwBvEs6rYiZi3G\r\nF2WhPTWMB4aioZ8ZX3N6gVrANlHDEnGPs7UhNCGvc/Pej8iwLtpOEgoi/vlQ\r\nxucB2FPnCIFb2avPfaM4xJa2oMpBaV2zBnaIQ2yzV5mWceZs/587gccxqW+t\r\ntWOyU3ftD8TXz9OYfEiVFhbByItor8e7s6jG6KFrOqALimF3sleYV2Ze1zVF\r\nrwhN5jipf4p4ZQQ6Db+m9bP5VXyWLXQOBqvA8fEDMz6ZIpzFusDgCLSP77QS\r\nuxw+bTgSMFt199VObbiRXSEGT4R4rCivXd0FrQWfcuE9APhGvq6xQYDv+XsV\r\nH1Zd4t+SM5xxvEjWGtg+EpEmDjsaTYDMHSUvowOUlgc2x3HuhluTDwKqnbGF\r\nWF14U3k6U7pBfQFhFHokp1c+xfgiID79+7muPZ+OS9v/dzkaiCTAD3sEXXLa\r\nBHiQ4Ek2IKSxhhIweWQzaRkz7Fk4MhqF0pXoJVa+zv4De/X1+xF5udl0QVUL\r\nSO9vO7SLcJtpz59ZvubiB9TErqmqkvUmyfIRQyu8LNcKDrfoSpQybfIFOZLY\r\nDdeco7mDlsytKJQWiPitXmTeoH+uEtjLKR1or14GqMnDyE8iiHl4bBLUQNDu\r\ngWy4MpvD3/FSZdpIgzYmf00lis6zdROYO3A=\r\n=AdxG\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">= 8"}}}, "modified": "2024-09-18T05:27:37.897Z", "cachedAt": 1747660590429}