{"name": "@jridgewell/resolve-uri", "dist-tags": {"latest": "3.1.2"}, "versions": {"0.1.0": {"name": "@jridgewell/resolve-uri", "version": "0.1.0", "devDependencies": {"@types/jest": "24.0.15", "@types/node": "12.6.2", "jest": "24.8.0", "jest-config": "24.8.0", "npm-run-all": "4.1.5", "prettier": "1.18.2", "rollup": "1.16.7", "rollup-plugin-commonjs": "10.0.1", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-typescript": "1.0.1", "source-map": "0.6.1", "ts-jest": "24.0.2", "ts-node": "8.3.0", "tslint": "5.18.0", "tslint-config-prettier": "1.18.0", "tslint-config-standard": "8.0.1", "typescript": "3.5.3"}, "dist": {"integrity": "sha512-0ZFt0iU45mvwZN8z9pdyC0dgGdcntVY3NtBiQXQvDxgtOzeJGIGhxr4RecNdEVKaC93UZ2CrhFp4LlQbCyvIrg==", "shasum": "c22449f133be742c7c640011f899705f44ca9188", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-0.1.0.tgz", "fileCount": 10, "unpackedSize": 44406, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdK4yyCRA9TVsSAnZWagAABz8P+wdzdVwfLtMzt0P2npjm\nrBihgHA77dymQABy+0GTuhW1iy6UcwQDvBPqRoasT1ZwpZkPCcom0T6ckZb4\n5kjFe2g8qqg+H9pS/bHR97+c91bweNSlcvkaGl2h66Vvf2kaEsZSw6koL80m\neItPfudDeZiDUgFZvhpDoOyRmsQwy30p51VV35wONhgF0OTqmY8Er7U4MLVp\nOONeOVtFVeDxdkk8DgJdk0VRLSU0orkvuKKP+n34oxLxriNeY/Hmj76TbH4g\n2jiLTADJ6jzBEBM3AuINeiZzvAd0PvOHDOpqD2Gz+vxhJVNurCfGnNGItp0W\nIVNOZ/UTvcn6CcB02buUsMrm+U/p6whZ0Jrxv93sGYb5Eso1SfQtbeOLS+8M\n7pSb3XYccGJ+D/5ywGBJHzU77VVDh1asi0Z4bUkH4XP22zjUHv8zAwhIo25n\nSQL1blPO6HW4IsdvTnDzxegyhjuYm1a58u1p0rjOwbYtImjM/U7WspC7q7xp\n71Eoj0kDVD8nNDfbeO3z6EIgkbkV70ZSOWQlIz1FAydjTF/c1sRWP7uQwISu\n+Jv60zE3GqAbFtpU+xFiGLPBNed+Glz5j9bBeXZ1FVeCkz7yBakZ4Mmfr9XR\nMtuIleOEwVGx3fooalQZRpbNJkt3oW4GqMX5HG3ITsrO8cM166HiGvupSu3+\nu/f2\r\n=J9GK\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAGREVakeZcEeKDHysIHiarB5gmYC+tjvqpX43Frs1EyAiEAn6xfyf46eZwf53lEri1UdfoxabQ9zBpuyQF/Xmz5bW8="}]}, "engines": {"node": ">=6.0.0"}}, "1.0.0": {"name": "@jridgewell/resolve-uri", "version": "1.0.0", "devDependencies": {"@types/jest": "24.0.15", "@types/node": "12.6.2", "jest": "24.8.0", "jest-config": "24.8.0", "npm-run-all": "4.1.5", "prettier": "1.18.2", "rollup": "1.16.7", "rollup-plugin-commonjs": "10.0.1", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-sourcemaps": "0.4.2", "rollup-plugin-typescript": "1.0.1", "ts-jest": "24.0.2", "ts-node": "8.3.0", "tslint": "5.18.0", "tslint-config-prettier": "1.18.0", "tslint-config-standard": "8.0.1", "typescript": "3.5.3"}, "dist": {"integrity": "sha512-9oLAnygRMi8Q5QkYEU4XWK04B+nuoXoxjRvRxgjuChkLZFBja0YPSgdZ7dZtwhncLBcQe/I/E+fLuk5qxcYVJA==", "shasum": "3fdf5798f0b49e90155896f6291df186eac06c83", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-1.0.0.tgz", "fileCount": 10, "unpackedSize": 48078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdK95tCRA9TVsSAnZWagAAvIAQAIABq26oWhpSNBR0H5wx\nG7+79MyqnPxuLr/agXR1otqgHhQoClmX0yPsE1HtqDvNdkTHWVbBaW6CBvKy\nkfMdH+I0jyRm20rVe01SGwdvoDhA+SdWj6IgPSgNDtKwsEZ+BRT2UQGGdUXQ\nQd5OsomhjuaI07DMZNDxfb3Yg1mMxhdWuzS2voKhOUSy4rBbiXEOXbvRf9zs\ncz6wyP2P6tzjdhR2IC8dFpCQL3X4Dn09CEeUYtooz6Lae037gVBrQkxcgTrw\n3AFP9J96IyAc1FYdJDoZmqwugNZMj3hBolzBBiPQGM6FdsdiD2cc0uJhhj7f\n0VOxviGIhi06+vekFtomB0yHUz1vqcf9Hd3E7RjRhrk7rQvhqW1DSOSh8g7F\nfiPk71NMAcRiOb/wIaQQBIKynOzi3OcmkqkeTv+dEv+97SonEq6z1pyWDrbr\nkvQmswQi9CaW5squYJ/MsNuvwX84z1Y7esADS50HTTmyXnee2xliEJkwYYNp\njPhwRhEi1506D28JGD5V2toFozG+kx6JIRj9OvD5hR8m6BaqWZjVIj255CB5\nDgl+2UAeUTykONNX8AYaVZ71Koc2xBhhwzd2wgVqvT/AtXooKtA7JJubAYfJ\nkmmslPd4AhV5JaOePy5GqLbizNyxo4D4AxYTfeHnDr4pLxWAbdq59Xh5L1Qo\nut7g\r\n=eafC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDa2yQkHxietJuIn2LaPGS7hlHT8h8Jv5HaQaZH4eUM8AIhAJu/hmQP+QFl7vdhkdrJpegs1zY2SuIhQwHzSXNqARrp"}]}, "engines": {"node": ">=6.0.0"}}, "2.0.0": {"name": "@jridgewell/resolve-uri", "version": "2.0.0", "devDependencies": {"@rollup/plugin-typescript": "8.1.0", "@types/jest": "26.0.19", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "eslint": "7.15.0", "eslint-config-prettier": "7.0.0", "jest": "26.6.3", "jest-config": "26.6.3", "npm-run-all": "4.1.5", "prettier": "2.2.1", "rollup": "2.35.1", "ts-jest": "26.4.4", "tslib": "2.0.3", "typescript": "4.1.3"}, "dist": {"integrity": "sha512-A6MWt3DUVI+IuB3SMQRXtHYui/7dy0AOGlbe60Q7mDfFbTM/iu7CZLpCwWC41YMfAZ295tTD2tUq7z8/1VabZg==", "shasum": "310ae0d5a1df29fcebbf83f88e01490e226003f7", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-2.0.0.tgz", "fileCount": 8, "unpackedSize": 36964, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWa9GCRA9TVsSAnZWagAAa+IP/2AGBB/I01xEuy2G18nx\ncKTdEjLHIkVdjcsoABLpJFW63X43McDjUvqxsxsAKLHcFUs4CwjiEqKidOlB\nyAoHLHdQsu7TOGRyDX4mNzGEt46wKrMV0n95pMe2M7LSu9wbe2hZAtypW4M6\nTHMv/Ozl+sQhRW65r4iFn+/8IR+7nVWbvhh/IanK3w+m3A7zhEBn4y5owu/N\npyH7dFWIMwtVIshr7jYRnhxbrZ4E5Un3QHS7yYWiAz/mi5AcVaQ0WrlAD78T\n2ELakRtgrRch7dMOWintqqKUqAvBU0rBp+pTq5tdvr50dXN+YOuIapsVilDx\nmUV/htOEGUPP8voXC15bkuR6JuLYxwydLWLslS26tzl/5iZbgoGgl5TKfdLM\nM/n7XlGG4vPfapZ1jHpUecroewV52SFm2DDsdOxaCvXXoraq4ObnF9y+xn9z\nQftXS7TuNbrTVuvklzd4DUlWOMfZdjCJv18EZqjsXb1kS0XQ7VSZfomlmI/R\nkCuxnuFEmJfWSIlZWcrHCQ+T2EAl4uinmE81vxsYo2QFNVU7dgRf/8Id1ZT+\nlsGRIF/BfyXJDUW18dkrArEs3phFOc+LWNqULGYug1gBIU2x8g1UT03DJLAb\n7mGcjF7LCCCEuEkDpjCLhML0o/5VKmdw1qI39F0EpxFxHek+DLqZd1Axjbf6\nTaLo\r\n=aExN\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDA1FhRqn5hE9SRwh/d8osjzEKjCVrw5XzKN8bGhzrxEQIhAMoJu4826YOEv4bU3ObXkGVTbgbrJDmfK4ecoYYLy4gC"}]}, "engines": {"node": ">=10.0.0"}}, "3.0.0": {"name": "@jridgewell/resolve-uri", "version": "3.0.0", "devDependencies": {"@rollup/plugin-typescript": "8.1.0", "@types/jest": "26.0.19", "@typescript-eslint/eslint-plugin": "4.10.0", "@typescript-eslint/parser": "4.10.0", "eslint": "7.15.0", "eslint-config-prettier": "7.0.0", "jest": "26.6.3", "jest-config": "26.6.3", "npm-run-all": "4.1.5", "prettier": "2.2.1", "rollup": "2.35.1", "ts-jest": "26.4.4", "tslib": "2.0.3", "typescript": "4.1.3"}, "dist": {"integrity": "sha512-wTGFMzHNNloYt3ONin9SDE7pQsfz97Lfxw067NbgWS3XjFl7fJ7JTXpTR5N18z2YFOo4WHtp0GoG0toZFBW7+A==", "shasum": "4b6fad80a6c18a461ffeda14a93ca0aebe0fa2c6", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.0.tgz", "fileCount": 8, "unpackedSize": 40965, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6+y6CRA9TVsSAnZWagAAemoQAKJMbQN/rrPeXDPFxgvL\nMcJL5vIobouscJsUMqQao4f0gMe8opVvFW2ujqdVgRT6Z3BJOLm/CKbiL+s/\ni9coQvZXIxm4wXCXGt277IT32Vl2q2ps560KPLRH/uSzeezaowpPafkLDmNf\niOZFVMiB7yFE0Y6r0UbqnBqwJJ3BbWaKD+nLfDwKTpXAvJPFTotkkXjSWnuz\nv6HPAIZrY94Yzg7V7QzojMo+9fvTxLg3Jtez+aWqZQ6L+elbgD3GVgWQqWvx\nHQEl6+OPJmwdJZ3XzobxneUNXwz33RunB2+hUBhpboNULnfsShr0vTbGZoQC\njOxDkcjb4rRn4Ad3F+fqkZf64ZbhlrtSCDvR0vE/B9PR3dQTrrp3wmar1xT8\n275OXbenB36Vmbp4R31ztHIzMaDrbxfqh9aWGSJowRnUb9qlSx6znzRfnTRG\niBDkX6iwREmnM9bzfGm44NVH0/hJ32L3NjxT2xHw+LXeEtZpplS7mXCBv4IR\nenKcjJsySqjzYausv0cxAKDOJSZ/aeRNhsaz6pLy0qpKuFr2a2SrRyoLgcZ2\nTk+G4SKfhWtZwjaqt8mUT2mhHQzcsoQxHHnt8uj/0Jib/UtIMBzSpoZZQXgq\n9eRSmvpQknCJJYVDol2M26F7eIn9nuInXz5aW0XOdnMN8N88bFwJtCdCzlQQ\n5P4k\r\n=m2EB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFDza1U3xwQUmS6s95bjIJn6MI1e0m6UO2b9G8D30OGTAiEAl3ljml31tfXyd8d16Mw3S4chpUF4jypOX6kvPk3HRQA="}]}, "engines": {"node": ">=10.0.0"}}, "3.0.1": {"name": "@jridgewell/resolve-uri", "version": "3.0.1", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "ava": "4.0.1", "c8": "7.11.0", "esbuild-node-loader": "0.6.4", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-Ot9ZsmO9mutJrw9bkk8l0K3aYbY15Kw+Cz41vWfCf/b9otT/meH2BZek3NigNGQE8PC32TjY1ckEsloqYVR2Uw==", "shasum": "e3d19db6e7233e4a1e70f469f074a2b73037a158", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.1.tgz", "fileCount": 8, "unpackedSize": 41959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7OGuCRA9TVsSAnZWagAALQwP/3pqHSs/SiQAt4PKiKCD\nkPS6gNqI9pJzeISBj45JIwAjkGwgNlw5UJtpOq1IdZlXe/rszaHzu9fUpheM\n5PDq1pFMiJcieXF11HdxHmdLIpADveaLLWlHqcM83WdKJSH3K8gWf6LI44Sm\nPL8qtTsQws4sgNQeTZ93gvznEqAq7EI+7ys7YIoQiEOJ1lQxP831JkYnZaDh\nnjHK+zvbAj2WXBGlrBUoyOwFJgDdJ3xQXXGREfeHvy691YHopg0wP4Oh6mgY\n9boA9YMXLraZkV5Gn+/UCoVrt8pGBjqu0JmwpHPQiIzzGyWGz2VP9mKmRK4g\nnn4IpnGyLTaboZTdvSChwYuw0PSLBicdffMp0MVFRVxMog5LdovmF1TyzaOx\nRimQwxUXK7KM/I39CoPc17r7QV6qli7TQDP/04+fFBoB+Y0gQOR2Dr210SrD\nXva9J/sJKpDdNSyAg8C+vHnn2wSiL5D8SeY7j3bdggmDGI4ltHaFdj5Dgr7Q\ngfUs9H0fbAfyG9mvBYOWA5IdBOUCHnKOjKorrd8Ic1eGQQwh4oldVLay1E7X\non6QSPrdcm9V+Z8yNMR8K4y/v4E9hrOkMesB0XzKCfhG3wm3fnDEe7Di99Nh\nQdGyiij7MHgAT/m4VoGGd9uuNYMAf1+HI8/SjGDzTh9e3AuMQQMhs4PI+tQI\nRZsO\r\n=A5dd\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEavJ3LNqJFNenj4SNqBbnaw9r5kWejTfb0S+FP3uX9xAiEAmnlBQUnKsMrsBKdmeK4TYWfm9ziGvyHZ6rBFyUTuVLo="}]}, "engines": {"node": ">=10.0.0"}}, "3.0.2": {"name": "@jridgewell/resolve-uri", "version": "3.0.2", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "ava": "4.0.1", "c8": "7.11.0", "esbuild-node-loader": "0.6.4", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-SkydGJCi9+iwFxEOdvVEAb8/io5mH9F/HTaeesu1eSKWPyVImT5aL0dGZi0HwwbvWNqVNulDz49QY3hXAddhzg==", "shasum": "83fc9d126d306e5088d96f8db64668e32cfccd5f", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.2.tgz", "fileCount": 8, "unpackedSize": 42251, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7QY/CRA9TVsSAnZWagAAQbMP/ibT4ziKC5Euw/qBLeBY\nHJacd0o+U32Jj+D/qbvoJ2uIW49vAl7mxKPuri3YRggGTctvSGdmfI+0/Zl6\n08NQnE9OjMZxdRlAKxChH8y88nSY/qCdaTpx94JSRQabdCNCvPCBzMsBsrFN\n8O+dzK7J4EYYK3aNrvkzuvCNPZrxYOf0+/afcwJWoXHcdFzEdQI/fCMGqsfV\nj6yure1zBXrS517gA2iid4zfMzWkAipFW/9tMD/Zdtjjfiu5ZeKfE/WOMNpX\nKSF/is7rAQDW847RIOsG+PFm40o88FLPbRX1cyLLmI2Yy3fsCKj17jNDSgqX\nLesBNQBNwJECIg+42znmaLxZxAsrdSe+9Pa4HpS83GU0rVVVayvDdEH8gh5c\n7yL5bkm0R05AICHdncrv4EhEvjKcbtE3Xfl8pYSV30IaATIPheLq1lMK3JON\ndMSfo7qrza5OYH6Gcal5TJI9F6Ziq97YIgZWpofoUjaOKKIz2BmSyxRcOg7G\nrvrjmd8mJMLv73arix3OMIUXj0OlUBDwzC2nBsKZASoLqXdqYmOxe+ZMo1ps\nFkPG/OcmDoBNhxgdDhg6c8ZbdepZGpuBzGvT71o+ldfWxbVL3mBc4W1zINvU\nHuCykf9K04wRLXBkuDmOZq9JLw8yCJij9TpfqiPIdyo2HIfnhLtC7Gz28XoT\nFQhx\r\n=2UU0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHRvptagh2ADh8DtlQQvttdpe0Kmv3BbJXAxqaReJrRsAiBOfK1LRZ5tMb/98xGwoHQdTR4gS+9IsAhyxlteviGwYg=="}]}, "engines": {"node": ">=10.0.0"}}, "3.0.3": {"name": "@jridgewell/resolve-uri", "version": "3.0.3", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "ava": "4.0.1", "c8": "7.11.0", "esbuild-node-loader": "0.6.4", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-fuIOnc81C5iRNevb/XPiM8Khp9bVjreydRQ37rt0C/dY0PAW1DRvEM3WrKX/5rStS5lbgwS0FCgqSndh9tvK5w==", "shasum": "b80093f4edbb5490c49746231513669c8f518acb", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.3.tgz", "fileCount": 8, "unpackedSize": 42533, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7QzxCRA9TVsSAnZWagAARtwP/3XHicboy8wBrJNUfumQ\n94kgRV9t7YDSoXb/7Gp3VHxFjntF2v3DRwLTAEUxwXOyKGs+Jp2pExwzmOIN\nrv3MmXyPbHWfeCn0eO41sxFaH5M5Qk7mvBqr2Z4ikJhm+/Z53/4ETPmzikcp\nAczx1CvhVVO94X4sXvAx8WM83jT7yR8QMxQSZ+AiNeWzlaqyJetTDj6u8S9M\na20yeedDBELVngAcAFl9CAD+Y/SVBjCMEAI8mkUFrkQtekAa82Qmx2ZClyIt\nCzmDcpUe7BiPU7Y1JNYxhHQIKMAnlqXzbZ+BqovQuHvatqTkW2Nzx5cQGgtP\njogpik2uGxj0AeBi3qHFmfP74OiY+tBqiS48o8L6bf7aWRu8Gd0/Zeq+xYhI\nHQXOSHlqvI7a2+vy1XQct3FOrkiUe/Cs83CO7jYWBpvvuitWU4mAknYmW0CO\nNWHGRwg1JYFVtYQRXb7NNkbA/gb4OzoW10APms28R5vMuG1VP6RDDWm86ESy\nyScPIQGxMqAzTkG9qa0se4k/9vJkYpQ5sVuv+gB3j3g1W9O+Jxg9zoXj2uft\nW+JGw0/IlXqbk/gGYIFKQgS3Fbk8Rf6Kq6auw8zOdL4CGJ23fN8rld6mLY8z\nnGdIyNeTfL2GnwGVXRYRTRJ/Bdgq23dVo87xJ0bWk/U++3IfXY60TWPWkeXH\ndrmC\r\n=LeG3\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCeM+kbiDldT75LMXrLzrXJMwl3EFRT2BrFncBN7oRiuQIgMDO+t3b3QghYK0DCpHLnj9OxW1KCVtsc5NAJ+d9iE28="}]}, "engines": {"node": ">=10.0.0"}}, "3.0.4": {"name": "@jridgewell/resolve-uri", "version": "3.0.4", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "ava": "4.0.1", "c8": "7.11.0", "esbuild-node-loader": "0.6.4", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-cz8HFjOFfUBtvN+NXYSFMHYRdxZMaEl0XypVrhzxBgadKIXhIkRd8aMeHhmF56Sl7SuS8OnUpQ73/k9LE4VnLg==", "shasum": "b876e3feefb9c8d3aa84014da28b5e52a0640d72", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.4.tgz", "fileCount": 8, "unpackedSize": 42532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+0n+CRA9TVsSAnZWagAALTkQAIz8M+Tg8yyNU64EhD3E\nHkzAGrhCn4m6GIZJdToLpDckwJkTD4/IJ2xKN2qfFYng9NjYX8q57BqdcR/s\nebZCdnAe9xCjEJAes2INUcwKuudtpfjrCN+UuJkM4u19t9bPBBHBNRPXz+2y\nyUCtaea7XdhcMjkkeXJ1IpeWY950n+eQ0/xUsKCXplwmpUjgISanY4eiPNbU\njnEp416r8pvTzh6Ri/PHXuRpwJBkcp4b8zx4Qjf1Y7s35tLme/APqIGCiiVS\nw2SEXndWqUTQbGrME/tljQOBgECSLQY4I5y8b0OBCL/qlcv6e2iCQswmBoVN\nUEnLqsljUmHvIB2oUgxf3fJouVwV7fgTS/Vr8snEY7+bF5hYSi0RxT858MQd\n7UlKy4iR4xj6+PLWuKsAMz95ywuNs/jIF889Osb5tuJ3VVzQ2ZMg7dmPK6CD\nM4sczqjwdzFJhwVDRIB0jxhPbEi72rEni0T85nQurMM/qHHqhhzCikUvzP9d\n3OcYK8qnqDt8NyPzMzw+cxjEcsxcfa60OGaAAEuS2/ax1aEL6GVgOjfwjg7h\n0Ds5BXgH0XZzWmODHPk4suoaUXnGheD3aL8pwG1l8w3qAaxBdVM27nVvjb2c\nZ/NUmTSbBFrTBHTJx94k3WhrAsyK5R0tOhrT1PTgOAPmcy2f3VCyZvxpob7p\nF4xR\r\n=umDO\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCvtXbBvDRbd0MDet4etBrjMofudXf3YwFcDyprb8ux7gIgFf0fhsupPcbKgukw4vpy/4qx0Q/aSAEo7Vw5oewubJQ="}]}, "engines": {"node": ">=6.0.0"}}, "3.0.5": {"name": "@jridgewell/resolve-uri", "version": "3.0.5", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-VPeQ7+wH0itvQxnG+lIzWgkysKIr3L9sslimFW55rHMdGu/qCQ5z5h9zq4gI8uBtqkpHhsF4Z/OwExufUCThew==", "shasum": "68eb521368db76d040a6315cdb24bf2483037b9c", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.5.tgz", "fileCount": 8, "unpackedSize": 42599, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiBfIgCRA9TVsSAnZWagAAF50P/jrZrZfRmnuOrJlALzO6\nP5V1rBdwpq62lFbuAIeKS2wCWn5uZntU1UFZyEmgISGSc/hS6+K8SaV46ZTp\naTJnFYGkhsefBtZel2pP0YSzI7PsuE7MeYmsgHqlmRoQENfvTjFGpNSwdOGd\nEOnsU8owtEYvKMHwvRTl3GXao/pRks7/9Srw9GMc8aiHeUzI7ZgrEJhEpmiU\nQilb8zC+FWW/aqSAqEPThsQhKUUABe24Q4acfzou1rcyl0xdgpl5mG1b2nLZ\nEWe1lAxvll1JYmjor2TaaLU0Qbo1XVGFt6fFIwqh9m/6M58A4kfE5gFmtSqm\nMaFTipzN4uhZ+XIODNFJ7BqMFdFDnpdPF0psktZ4UUMpbsxGPkp7z93tp3bK\nUO5g1bimj0pPk6wvH8p/mOlEja8wZIdNjdFMtbthXmCq7dPsKHRZ6eYP4WDj\n1YfKDhHTWkVHyjHOr9Nj1R1a4/g6eK3jsNswUP3Hp4Rrud3uTuQD2OcFOFaF\nXCT/uOfpedBkWni5+t3Ki0PejaLaTwEMkDpsmkOfdiAYPHnUVYvuGpWhp+Vb\nuvi+1ulbDUhxgQfH/ox5Bd4Pu3ns6lZCwMpg5bQhn+45sV3c7kgKF9R0Jx1I\nuHfvc1PdGVpDr+MVm76qgfaj0scE81LGxSAU+bGlujQteFMFlBz6cuCAq8Vq\nhucD\r\n=Vsqv\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFFCKFSoQFOb4d6FvKCQgIFy8VXN/EvdU4l8UGsS75+QAiEAjEci5vB39/5lzDetxsQ5wZzlgqD0MTURJCZNzlq/cZQ="}]}, "engines": {"node": ">=6.0.0"}}, "3.0.6": {"name": "@jridgewell/resolve-uri", "version": "3.0.6", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-R7xHtBSNm+9SyvpJkdQl+qrM3Hm2fea3Ef197M3mUug+v+yR+Rhfbs7PBtcBUVnIWJ4JcAdjvij+c8hXS9p5aw==", "shasum": "4ac237f4dabc8dd93330386907b97591801f7352", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.6.tgz", "fileCount": 8, "unpackedSize": 46667, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQC3WK5kQ2MVlLpmmCcWaqaJozrUP2nGJXoCyhMPvGKZ1wIhAOwA5RlhGyYOdJ64akP6DTRZZdBlHI0oa3u+2mwdDBM7"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiYyygACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqQBg/+Iz9YRdXx/rdy6YivJsA/ffy/CS8OfodQk7EkCK6rPoXxZ6wJ\r\nLvSLJadcD9R+QAWXOVt2TGfneCn1DMFKzZzh2AjYwpNe+hcd5FY27nkctGkf\r\nfR+0+ewqzBtQAn66VOEuvteQ6h8De3zeXJysPRiTTg5lKjIZbFB0nba8FCRa\r\n8b4Q8mfY9xDEZoIkqsu/84IzHQaKczLZjjs5DqixFUXntzmQnctSDpF+eS/g\r\nnAQI44MUoE+jAXa/QOcUxsoLeiCcZO0doAtyiOH7SZ//MzZV1+M/SO4wfIrQ\r\n3qATQmzrlqLC9tx0Oa3RTcqxHT74+e/NcMPJJSZFJVpmBflmpRAIs9pTjcDL\r\nQxw/w5t6rpEJdTMG17tPRA3zKj2vuSkNyV7iTD4NptH3SXQzZXxgTyuw0TPv\r\nlxETkQvIurJ+32J57Wu/RfQHEQJmJq/BjL+RKVuzT55OKqTXZd53ZBB3jtIB\r\nAKJsqmD3fWnN1LR3HjOMkHz4ruRgDniFUGxYLR8IDlMCuBieZo3qajLjwGAZ\r\nXK2GMJO77YKdKrkGrydhnprUvKQk8G65/CNpSmzHZR5V7Pwk6OqksMGcvgne\r\nW4kDf6E5azuzuCvtA4ftL5ft9hLU+5Mfv/UgnTip7dPpC89vWJwBvTTyzQod\r\n1jYNSkYuuqUbREY4eD8GB/qHNVc1mogMPTQ=\r\n=LhfI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "3.0.7": {"name": "@jridgewell/resolve-uri", "version": "3.0.7", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-8cXDaBBHOr2pQ7j77Y6Vp5VDT2sIqWyWQ56TjEq4ih/a4iST3dItRe8Q9fp0rrIl9DoKhWQtUQz/YpOxLkXbNA==", "shasum": "30cd49820a962aff48c8fffc5cd760151fca61fe", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.7.tgz", "fileCount": 8, "unpackedSize": 46715, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCn+1Jv99Tf42f68yJA72rFH5scLfUvzTLhoXMjFdSB+QIhAPSCQAbEmimbHs4nJnxUUYKAVv+hyiZzsTUAhs5MqXoH"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidEg/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpgSg//Y8Obm6koG80p9v0RPPlXfnmetozYrxxl3WuajBh+lnIYTO0m\r\nG9uJfcumWoCrj9n7tSzUZ9ri6ebwqTNC7fSZn40RjZhoJYYJNzsw4BQhmYUz\r\ntAmxSvnOkbav/PK46GxrTo5q0C9IO0CJbyP2FqWNBB1ck7UTiQPLDxf+fY60\r\nasJ0Up1ewDag1EcDac99ZZhmOvgLqDeVidHP9qLwazkSHJ3Yv0fgStpUjgry\r\nsjuIf/CsrCTgDjs7OQxOxDsTWqMd46LENp5+3t7q7pLsd73FmGZVlVJQRr83\r\nIrltmH2MXJAZCaE/79eQedjfeYLUbG16eH1Jr6+3G+pys2AIOX6sStBzdRjw\r\n5gUMIjGb9kONM59Mjl/BSPHt0rSi7K984zp2XR0N3sPpTZCNLxGM87n/F5xJ\r\nvr3Ucfbtrr2zhZVjkEuHjJJkwlDPvzmBkCCtL6G3Y2X2Lq/qX2ajkpi6A6DB\r\nZD8RSdlQG3esBl8C4bdBEQVCRHR2PYhtuRUvQ+LVaW51TXtZJZp1Xe+FtYtX\r\nV7qmhEYs7uB1OuxW9oIZ1KvX6UcqDhCvb+Y7BDgKHma01xHzBzUotH7d0hDd\r\nDp+4MiJ3vKcPFkYnLsb6/zlWcFxHsFpW2K9Jf/wynz6QCb3ZYayazzHuVnP2\r\n3xtPfpIHaEeBgzZWqvL++2d0Yn7vOYlg7Mg=\r\n=efAw\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "3.0.8": {"name": "@jridgewell/resolve-uri", "version": "3.0.8", "devDependencies": {"@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-YK5G9LaddzGbcucK4c8h5tWFmMPBvRZ/uyWmN1/SbBdIvqGUdWGkJ5BAaccgs6XbzVLsqbPJrBSFwKv3kT9i7w==", "shasum": "687cc2bbf243f4e9a868ecf2262318e2658873a1", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.0.8.tgz", "fileCount": 9, "unpackedSize": 54045, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF0/kPI4fh+VfDIt0kCqk97y2zpmtiVzl5ixG3tdsyABAiEA9OiqOA2eNDojy7G/kFQyCjvW0dVW1TntP3QHx3KJxQo="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiuH3QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo69w//UTOGEcMdzdT0uRfAS3gppWQ0cq3lxAqok8q06pDgQ/NpWb9I\r\nQd4HRdf3yHu2HaqG6j9w3aqbGcRpW+wc0MudZjfQNMabem27SlRHCgL58HJw\r\nCw4JxHGJzlTmzJvrDJdgbvOl+N534LjrhWycsAGCqYwygMFxSbdlJPVt/3r1\r\nZpbYWtQickXaNY8keMuyN1JU/L9A5VvTBPVXJTG1OK7qz8vb/5TBxkJFmCGB\r\np50JOiklY5aM3l0N4P3kaMKzjF7cda+mloiMmygPdYAHEDoXoP8dtWPrsBE3\r\nhDe8NCixoYWhMTlrEdNbYiqQ/dCRH3f3dsfa/SMgpW1NPSx31/RO26l3kah6\r\nLD5iVyjRMhbxIlxUaGXOatAfwSzYbBIW6v9Op/zwfzSigY0xB1byDsesz9Vb\r\nGQryAhijTrgN03ggFPjqcxEJh2Kv9ohZaph59rD9aabp52bfoYML7+5qmuyU\r\n13zaU8bmnbENZ86O9t+F4MaO4P2fHObpzDYQNZEGasR9sUDEptpDH2la86Yh\r\nG4ewWevjKWCX09NKG84cSCjwFUxccoywn05TjrCQMjFV0pcSgvSaleXbzXpq\r\nSBWFFP2vbtIJFNc2oTmGZdrnINHzg+76di58xAW42/ZX5pRBaSCCwxIQ3sLb\r\nr5FMRpJH3F/5SM9eRX1OtSJKrMfYc1mBZTo=\r\n=eILs\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "3.1.0": {"name": "@jridgewell/resolve-uri", "version": "3.1.0", "devDependencies": {"@jridgewell/resolve-uri-latest": "npm:@j<PERSON><PERSON>/resolve-uri@*", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-F2msla3tad+Mfht5cJq7LSXcdudKTWCVYUgw6pLFOOHSTtZlj6SWNYAp+AhuqLmWdBO2X5hPrLcu8cVP8fy28w==", "shasum": "2203b118c157721addfe69d47b70465463066d78", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.0.tgz", "fileCount": 8, "unpackedSize": 55156, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCU2p3qpbVNicpVq/ewxp+6j4dgzknuYR7g1sTR1gMhGwIgP0aMRrSQs3dhjbGw+o/9Aa4yD7opF2ILQwxCugHuzXs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixMwBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmoa4xAAlIq0FjMf7X/CbJnfvLjz9YugB4m3odf3Ue9qdezLtEthhQNO\r\nbreqY2Zx0Emk09AHISJkK7NbJdUAIXHIufsIvm9u6pSmA/REt/ks2shBzHRh\r\nFpAKJYTlvWg+LsuNwPtqycMydfnOMqhZN7wDEqGz3fOgE7N6LZAlbcmojNDv\r\nkLgxc/76TmSvdPXnnHIOjAdTPNaQPk4Lxmxtj310iIEkKZZBp9z3pDyx7N1z\r\nc0i3Cb/mp8WI/FqNOw6bXPWVWzXzgdEY0oPFrfPvRhUruJfYB7fg3MwTJFe3\r\n3heR5afcuY5/r1Ew9RsvmC7I2G5C2MGiE7O+cdsfvnzrQA3LalvyOUml9zNv\r\nRCR2FO8/0wLfNxAHnkZy5h1a+uB3FYJfYaGqqU2XGxMEVTaOcSosC6fm+o3L\r\nlImFzO+XKxXH/yILRDn0AUuTxGqarKFwcfXwzHDHSWEN3Xuw0kPp09VkViML\r\n2qQtXT53IqDnDFOO1Ua4+tzSr3H+BVrQZOwIgWPz+uaVGEt0HlpHnUI24WjB\r\nkI3eP/6jbsJpC0BVwvOHyps2S6Stdg2uKN7BRR/FRSWILuxfEUJALdHmef/o\r\nz5aK3XilwSr5AwqCwyjnCFHF7R1/LSFW7edSsh6qk8jOBtIG+LapV6+eSj9A\r\nd+/aDvY6yO+CFlXGkgjpd6Extrrll383qOA=\r\n=AYd2\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "3.1.1": {"name": "@jridgewell/resolve-uri", "version": "3.1.1", "devDependencies": {"@jridgewell/resolve-uri-latest": "npm:@j<PERSON><PERSON>/resolve-uri@*", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-dSYZh7HhCDtCKm4QakX0xFpsRDqjjtZf/kjI/v3T3Nwt5r8/qz/M19F9ySyOqU94SXBmeG9ttTul+YnR4LOxFA==", "shasum": "c08679063f279615a3326583ba3a90d1d82cc721", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.1.tgz", "fileCount": 8, "unpackedSize": 55154, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFc/Vrb7O8cBDx9mIM72L7ZIIUh2wDvGkJRo8V36p3fnAiEA302+MLSKoPoq/44lzEKRKOq2USs8eJ3k5/drCC9kaJ0="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkL4NAACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqVvw/+Ljvf/Bkmwsu/mmZcrmXfRFPpqwDCl2eKnHU3WWGpuNEAnN3a\r\nHfdPKAK2M0ubrstUWfwS+OYG2P21wg0brsc6ViiJh1aOXasEXl1be/keRyi8\r\nE5EG5f2pl3+DDCuJNl/cAyA58DT6g40G8+V13Vzb3sCW+ypX/o7pdDN7Pu6D\r\n9PHt6zKDXbWbXCwan50jDoOTg2qEWuJu3dW0j8icZXH90wC3K0LjcORAI29+\r\nD3GmCKZ9Do7/dBY5IHLzF8U7tSfbSDw8l3d4OwdLpcAVdX8KgIoHpPZzfg+2\r\nNMjjDHcEBtCtEs4aDEzVj5nCX+a9pFl2aWRLdmMNwEDNeWLAUDunOna79cB7\r\nVZSkxZvPpP6RvdSqnzaFExgOc6czmJZzzbf7qWe+tCMzCXKJjXe+axryhkVj\r\npJjdMjEoroTx8d7pikY48WNL0S1G3vIhf1o7LrltUkzSzhTj99ERNPTH01rt\r\nNmARyqeO3YAEGgIp3/JA2VxO5n1C+aclQnra45raO/0uW5gW5KpSLemzTicC\r\n5QswY++F4jEPOyS/HL3au9WNq9wRmLDCz1JUzvJv7ZK8eokqqi/lB8S2tvva\r\n+okGPf13W9DqPOcqxeNXb79vNuXj4ipRq2RQTUojMcg/rQdKHtwL9XBxq7na\r\nBF2uqKDOADXXTY8rYYwddS55PKi0kAhmffg=\r\n=ZTOq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=6.0.0"}}, "3.1.2": {"name": "@jridgewell/resolve-uri", "version": "3.1.2", "devDependencies": {"@jridgewell/resolve-uri-latest": "npm:@j<PERSON><PERSON>/resolve-uri@*", "@rollup/plugin-typescript": "8.3.0", "@typescript-eslint/eslint-plugin": "5.10.0", "@typescript-eslint/parser": "5.10.0", "c8": "7.11.0", "eslint": "8.7.0", "eslint-config-prettier": "8.3.0", "mocha": "9.2.0", "npm-run-all": "4.1.5", "prettier": "2.5.1", "rollup": "2.66.0", "typescript": "4.5.5"}, "dist": {"integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "shasum": "7a0ee601f60f99a20c7c7c5ff0c80388c1189bd6", "tarball": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "fileCount": 8, "unpackedSize": 53153, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEXppH+0BsrbWtra1kPPaPPmx9gUaehh6s6zaKjIHAFlAiBmQfkNGAwgSsvu0SsXAVHZi7isPYQkKaLUvLVGXwxCaw=="}]}, "engines": {"node": ">=6.0.0"}}}, "modified": "2024-02-14T19:32:38.290Z", "cachedAt": 1747660591827}