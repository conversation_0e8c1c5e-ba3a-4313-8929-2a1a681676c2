{"name": "sucrase", "dist-tags": {"latest": "3.35.0"}, "versions": {"1.0.0": {"name": "sucrase", "version": "1.0.0", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "1255ceabd5b037dd6a87cd233dcf64e2ef6ae6fc", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.0.0.tgz", "integrity": "sha512-+DQXIgjdW2do0ZBFLQMj/ccek0j52BgIKjCk5AeGSsf7xlDMdPhZMl6uMA4TAr5kCnZhMPgC3jZBWI3T+u9nFg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDTUiUzNkuDxRuxpEjCfiL/7J8orwvAeSNLk9Zhq2aQ4QIhAKXrtlF8dW0HerLMURm9UfUHFFLscNwriuaJZOLVQ0xW"}]}}, "1.0.1": {"name": "sucrase", "version": "1.0.1", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "0deaf22edd377087a145748959bb77cb2da03532", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.0.1.tgz", "integrity": "sha512-vM05zW3YPG8Tv891ZIT4WIdNkMPrboVM+O+/1/ZU6qNQyd7zyK35vKTGuCt+PvGk15RbaSM38Df0L3zdO8lS6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD4PAuEc67+ne5a2pDQ88zKkXXoIGdBS8JCyCJQiG9SpgIhAOMwsCtUp5RBxZ9oBD8QStHznJRpdqS566oWpCPXDp4H"}]}}, "1.1.0": {"name": "sucrase", "version": "1.1.0", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "648cf14abdd4cbb2629386ac0a3b6e946c5dd0c3", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.1.0.tgz", "integrity": "sha512-VSsjImjwmNXEhB+zm/DPtcZfpyg53dWUAhZ3iuuxYO745WMJjWsnqaVnnMRYqbcXJ4mq5+pEvaddQNKHkm6tAw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDZPI1FCdCbsEydCFGQNg0EOOlN+QgY+ZiJ1rPCjZVt/AIhANS+dAfNJwCyByDL5vq/PU2naSqkPwGA2kQJSXXa71A6"}]}}, "1.1.1": {"name": "sucrase", "version": "1.1.1", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "b9b5bdce5f5f7b30ceefacebb43bc12d180b3ada", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.1.1.tgz", "integrity": "sha512-xFjwGc48t89F4w7N1RNMQyyyTdgi5bXr4/Em3BRcNGdXeZd4xR5MHdbfB//8Hgk1lBIVtx0f3caGmOIiwceiqg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDOqtHWc59RwyDY/uPcfMol/qNCtytfJc5s/P0Nyni84gIhAP2e61UzLjIJQiwsEsVJYh5da5WCmkDsRDodrufBYCGr"}]}}, "1.1.2": {"name": "sucrase", "version": "1.1.2", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "6381726a04c4a5c563302db7408e0a2ce16e3ac0", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.1.2.tgz", "integrity": "sha512-K5IIXBsNwE+zmXyblIHddUVdDS5xsMZsKIS6v36oi77N2c3U7hhV/OHe2jRWIqPJ2C/b2QMHMFbkEF4k2JnIBw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD7s9VFuxsZVgtQ3Dqc+EL7r6PXDGhx3dYtCEUA8kPqrAIgHORZ9ft3JKyNYM9e3k7CJjcKs6TKnsyKyC0N71ytP0g="}]}}, "1.1.3": {"name": "sucrase", "version": "1.1.3", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "d17ec4c27352c6a59426f4b9864587559eda2fa5", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.1.3.tgz", "integrity": "sha512-qBlHgbAV9dFbEffZFh9nsnHbFnGT7Cy0RIqHEL6qauBdt45+MnRFoFWGdjmqWYgbs2stkAZXtGm+UqBJETuE5w==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEx0j41nUqkxBExUJzolw407TW/5bJdVMj4zJW0/nigjAiEAvZEBX99eNb2QHq2O4ZJnYktHTt/3/Ygsov0Q1TSs9eg="}]}}, "1.2.0": {"name": "sucrase", "version": "1.2.0", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "03b9ecde6d9a432702ef98201ee33cf22f91eb52", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.2.0.tgz", "integrity": "sha512-xirVTuAExvt7NF5d+Wk74fhkYThBjWyCcNbuQxPRU6jEzw/oYRzKA7vBnbvSoPBtSZbbOz7M3o2qYoB2XErFiw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQD7mOyyNfZnT1XRqE5pU4r7HeDw23LyvppNAxuUxpILYAIhAITgfPxPeCIPk2dBBloVl6df0mugNpgw8XJRuUVUf4Ft"}]}}, "1.3.0": {"name": "sucrase", "version": "1.3.0", "dependencies": {"@types/node": "^8.0.31", "babylon": "^7.0.0-beta.27", "tslib": "^1.7.1"}, "devDependencies": {"@types/babel-core": "^6.25.2", "@types/mocha": "^2.2.43", "babel-core": "^6.26.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "babel-preset-react": "^6.24.1", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^3.3.0", "typescript": "^2.5.3"}, "dist": {"shasum": "183a2c01c58ce407ba81fa8e6ddb583bc0859529", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.3.0.tgz", "integrity": "sha512-HOLjrOcPrSNdk3zryhHIX/ZTTXuMV5mU/3UJExYJiVXtwTxY/37/gLEOeDhCbBr4zSqLHnY/BuNN3m4qGzwiDg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHpdUY4WC5E8RVxng3RqJ3NAwJAHsVAtDGg8Q2gAECsWAiEAhmcWPtPGpZdJT04omGn6Ml7qhUk0ehIFS85CGnk51F0="}]}}, "1.4.0": {"name": "sucrase", "version": "1.4.0", "dependencies": {"charcodes": "0.0.10", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/preset-flow": "7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "babel-plugin-transform-charcodes": "0.0.10", "buble": "^0.16.0", "mocha": "^3.5.3", "ts-node": "^4.0.2", "typescript": "^2.6.2"}, "dist": {"shasum": "c381e7fcd348859de52b6e8b7ba1d57af5b48461", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.4.0.tgz", "integrity": "sha512-q/Ary0cpCL040JMPOT42YzLQnulVtZtdcUYp0TsJg2PasXjwc1IjwjLiuVdO3odxddMhZ7PvNctJkXIYj2/alw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHy+S6K5lzo0IIJjM/eiM+mgPqaC78HJHNJIRIlzreSEAiBwD+CuglNsImxnMzjgdlj/2Bftmf9rpyxceCUxqxQvxQ=="}]}, "engines": {"node": ">=8"}}, "1.5.0": {"name": "sucrase", "version": "1.5.0", "dependencies": {"charcodes": "0.0.10", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "babel-plugin-transform-charcodes": "0.0.10", "buble": "^0.16.0", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "dist": {"shasum": "a7b6cacbaa6c8d544dcb5ea79b81e162991e9687", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.5.0.tgz", "integrity": "sha512-wZccQEks8mvME/z/DgWBqWlG+mIYqVrScEjTZ47PV7vZvZ60i1DoINyEC8C38DrNLoUOJOVYnwg6GFOSlk0tMg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEmO5qEBVtQe/TctUghHHqBaI6d8ZsGuqCZMdc9eV3fKAiEAhOG+MV1GYeoHI052K6Gib93yLhUUX5XeIWAz3afXCkg="}]}, "engines": {"node": ">=8"}}, "1.6.0": {"name": "sucrase", "version": "1.6.0", "dependencies": {"@types/mz": "^0.0.32", "charcodes": "0.0.10", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "babel-plugin-transform-charcodes": "0.0.10", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"bulk-decaffeinate": "./bin/bulk-decaffeinate"}, "dist": {"shasum": "5c1743e5b7748cec66d615604af206c17d97d3ef", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.6.0.tgz", "integrity": "sha512-UJdzmV0jRu+L/3wlY0+AEkJ25vvSXJZ+SPXvJfYnxwDz6ERRpmO+B0x8taoy443XVJdut2Ngz9JoGU87ymh0gA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDFZwsRh7DT3MkxSwhRvv5vTyNHI7jvs/qQD7L+PGJ4/AiAwmpqpbBT8NVxgSX2HDyqvHq3deKFDQQNGRmBFM+Sp7Q=="}]}, "engines": {"node": ">=8"}}, "1.7.0": {"name": "sucrase", "version": "1.7.0", "dependencies": {"@types/mz": "^0.0.32", "charcodes": "0.0.10", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "babel-plugin-transform-charcodes": "0.0.10", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"bulk-decaffeinate": "./bin/bulk-decaffeinate"}, "dist": {"shasum": "c4e824cbd4123122d5613fa4935feacb8a903e2e", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.7.0.tgz", "integrity": "sha512-RbAD5a+HHLMP7JoUSan/VUna5AmUFCvJ4Mc8+iit8mtc27OjIXuZS9pY6PjNgDkI2RllkhBwatn7qQMFfYdM3A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHSDC0e+MOU7K/9hrHhDi87QSajTyn/U/Q+L06Zapr5DAiAyi3G5tgD7NeD8fTACjj7Rd/9vZuccnm7INUTJCXjnhw=="}]}, "engines": {"node": ">=8"}}, "1.8.0": {"name": "sucrase", "version": "1.8.0", "dependencies": {"@types/mz": "^0.0.32", "charcodes": "0.0.10", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "babel-plugin-transform-charcodes": "0.0.10", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"bulk-decaffeinate": "./bin/bulk-decaffeinate"}, "dist": {"shasum": "b657cdc924068d20a26a2eae7ae73ba1d8b69022", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.8.0.tgz", "integrity": "sha512-maVoybY/MJtX9asWOEly93ZsbnhIb0zKwN+HiIHPxXxPf41mEzNENtv3hXzy6RRgXMKzR3VtHhmN977yURnnmg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICXcxZuzP/XE2OYh4w3jsArDAE6MkaFSqRZAJ/rrQVtBAiBjSDYDOt88h6CLPaAOLebHsckrK66jASNkGbnPZlpACQ=="}]}, "engines": {"node": ">=8"}}, "1.8.1": {"name": "sucrase", "version": "1.8.1", "dependencies": {"@types/mz": "^0.0.32", "charcodes": "0.0.10", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "babel-plugin-transform-charcodes": "0.0.10", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "9caa8cc08832c44e225ac2affcea9468038886bd", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.8.1.tgz", "integrity": "sha512-DTR9UST9SMq70FwrGoKrxn5fylGE1aHctlYxoP3WcPCh20S34aM+eY3iJZnl5YBhRKKPMJxAOe3oRToosr/lCg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIDGeCiMHjuCfl0iocv590HVNTFPbdrY38XACZjWlarrpAiEA/yc1z19xB975dMm1e74vXkYwEQIYqR+l0F86IwTZgH4="}]}, "engines": {"node": ">=8"}}, "1.9.0": {"name": "sucrase", "version": "1.9.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "tslint-language-service": "^0.9.7", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "5078661ebc4a9fed2a8fda055745b9fcdc074c38", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.9.0.tgz", "integrity": "sha512-2746xeQPLiDmbttAb5Qi2U5fEdEsxyN91c2R9piKC+DO3yKVRviQUe0i+DjkMtJqU66VaMBVET9oIh2Xn+L74g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAK5iqnBaAIMx90AbitxbmDasw60dd9Mj51qiWRZm/CLAiEA7Qjb8ynBnVbYFCJu8ip0Rk47HSOtwa3mGx1pgEBTlyU="}]}, "engines": {"node": ">=8"}}, "1.10.0": {"name": "sucrase", "version": "1.10.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "tslint-language-service": "^0.9.7", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "7cd0926f5804a15569279ca59ae905403f10acec", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.10.0.tgz", "integrity": "sha512-9YC19NiXjfPj4Ah3ztya4MMkcBPgsLvpXeVgM2K/BHQ3M5cC+Ypy3uV+HSP4EtMLOXAKGO6xT1WL994IX9SCNQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAXdJNMHh/nuxcKLjMDJENQLInEgHNKig9dkj0idKiAZAiB5p90u91suLD5CJIgXwMe6AxNbm5t4wsXavZIJNO2ZHQ=="}]}, "engines": {"node": ">=8"}}, "1.11.0": {"name": "sucrase", "version": "1.11.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "tslint-language-service": "^0.9.7", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "acf39b4991fe4f00db79d246eb32f8ded70dff71", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.11.0.tgz", "integrity": "sha512-zShm0Ccffq0mseWam6UfOz6fg1JZu7J3vF9gwWfVA3fff1JHwu4tUtV/vs9O4KtaLCCoZIMFCypq8QoAvVAWlA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCyMsQTAahWlX/kT99hLJX57DsR7Lt2ahviVo+9ClE1KAIhAO7Vply7LEqt/BpPqt6MR3OoWXpKurf86Z9YhIAIEXKL"}]}, "engines": {"node": ">=8"}}, "1.12.0": {"name": "sucrase", "version": "1.12.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "tslint-language-service": "^0.9.7", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "174dfb15dbee6abed0d6d586ba5acbe6003ebca4", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.12.0.tgz", "integrity": "sha512-Yey5MaW6CJPX9wKh9oz1Sfksnt5T+oUs/07ZHK3HZffqY6uYdTSusdO0mqQRffUfG3MYd1iLb1GmftMAwGGwrQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAMuTFGQgviB4nrF8aUlpKLNm9pm4ld4xj12rFWBhAdsAiEA3ByGCWfl8G9+mhAmFZ4GV18B6zVPt/M/jlyfaxg+1wA="}]}, "engines": {"node": ">=8"}}, "1.12.1": {"name": "sucrase", "version": "1.12.1", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "tslint-language-service": "^0.9.7", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "97a92597432d2af03887bfab11614da5efef0998", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.12.1.tgz", "integrity": "sha512-wpq3t8NwnBPTrYLrinVhtm185LSoGk3CwoyRd+CBVFxZ5nYRw+dBVyjF2COzag+qAW99DC6cm30sh66+XUL1Nw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCXozSZ1NRhB5SLhuh2uATBXPXIHYW5tlRxK1QgrEGljAIgawUAbOanwdArDp0aFRXGP1UNd1w4KtgdQWNXnbWHBX8="}]}, "engines": {"node": ">=8"}}, "1.13.0": {"name": "sucrase", "version": "1.13.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.7.1"}, "devDependencies": {"@babel/cli": "7.0.0-beta.35", "@babel/core": "7.0.0-beta.35", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-react": "7.0.0-beta.35", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.9.2", "ts-node": "^4.0.2", "tslint": "^5.8.0", "tslint-language-service": "^0.9.7", "typescript": "^2.6.2", "typescript-eslint-parser": "^11.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "9fa81ea8a5203827cac01f0bc199b039988cc6a8", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.13.0.tgz", "integrity": "sha512-1SXNMKbGoRINpv3o1nqBo8VOuKX20Uwwri+fWs3imEfOYwyUTF/YQ/doBr558/HNXBlKSpq47s+JG9OP2VddyQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAIERodANxvLy43IdYIEziW5UxLL2jva0q8yGXOejUUPAiEAnnFtiMqPiZhFsmTRdBZgeIVTW0436Vb3qcmHQkzUmaY="}]}, "engines": {"node": ">=8"}}, "1.14.0": {"name": "sucrase", "version": "1.14.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.9.0"}, "devDependencies": {"@babel/cli": "7.0.0-beta.39", "@babel/core": "7.0.0-beta.39", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.39", "@babel/plugin-proposal-object-rest-spread": "^7.0.0-beta.39", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "^7.0.0-beta.39", "@babel/preset-react": "7.0.0-beta.39", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.12.1", "ts-node": "^4.0.2", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.8.3", "typescript-eslint-parser": "^15.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "385e718bdb902ffdfc39cead551952997286f7e4", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-1.14.0.tgz", "fileCount": 189, "unpackedSize": 742984, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa3NGKCRA9TVsSAnZWagAA3YAP+QDJkXJKFTX1eoMJuROd\nbNUnZ+D92G+Oy5CjK9S2omb6FBIMYQbw0IB1x9Okxx8Ss34gM1Cb172wHS/q\nyghHVIc+qPrtUt0OwUk+dYSaHtvF8XYgxEGOyzSCkxtq0TEtoQPwhQOZLzMY\nxPoryeUfpgUMx6squQCTEuhq5GyeUidL85PfppkHrLhVgq5tCBfwDGs7ZE8M\nsEODTPwyrZKn5nIMY2YjS4NfzRu/b9f0OswuQpD0se/5DZFsmZ1nt856FZoy\nEl<PERSON>WovHLDB/bVH/ZxXJzykyctO3iDELIiXQGUaiPr3dWQv5pCPWx9w1CngPa\nfpuFc3JvNZj5yoreVKJbvFMdJhkWzFq4D86MOXAn7ftP3Z5PlP832L0TLKFG\nLkgmHHqY9pTq6uXonW6Ubs0CRU46v12O2rk+guT7vmhZkZVh0grLcVMjDCDZ\nKk8o1+rarEWUfBzD5VAmUwbi2s7Mku8pyO0rsTdBZwwM3yzc8hsjK82mHyOJ\n2K7CJTY7vVOgtBLYbM0tV9yxuG2GRlktew3Khx+qHJTRoRtYCH8Ts7O0WwzA\ngHpxg11rqRp/tb3X1WIakisfCRIbUP8hmE1lx7anBj34KOvmdJKFRXErGzmm\nPvYdivpKs59EoIwpkZukZvGUNF7mbGl+4IzIZ0mY6Nwwtu7Wd+0KTYuMb0K9\nM46T\r\n=KhXS\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-Vj4Oh11YfPrfuTdkvXzY/o0uchoHJfmxvd9ucPSaozsZAfimhIEqh5I3459zcCXePJWWCECWCdTTOr1PNlFg6Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIBGWgT+KtB/9DPeWjbBZrvzcFSYwzH6WgJsEgpRDiU8BAiANG9AUHJuN18Cjo5DypfPekWrb07Zri/K6V5zpH1DQOg=="}]}, "engines": {"node": ">=8"}}, "2.0.0": {"name": "sucrase", "version": "2.0.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.9.0"}, "devDependencies": {"@babel/cli": "7.0.0-beta.39", "@babel/core": "7.0.0-beta.39", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.39", "@babel/plugin-proposal-object-rest-spread": "^7.0.0-beta.39", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "^7.0.0-beta.39", "@babel/preset-react": "7.0.0-beta.39", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.12.1", "ts-node": "^4.0.2", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.8.3", "typescript-eslint-parser": "^15.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "46df7a4c5bc7dea76a624c69f960eebd3bb8f279", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-2.0.0.tgz", "fileCount": 177, "unpackedSize": 749007, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5G7VCRA9TVsSAnZWagAAs6UP/38fYWg+gU1In5eQ/1Jp\nY32AtG8cpCxdeA6uHZGvT49NUpjy6AzT/KCV84i8zRQpUxomhIx6HjUdQLEs\nDFcCe0C8j+XqbDDo3A60lBGssCFRTZr50/CgsdErd5WhYWuEfgM9sb924viI\nxLxZnqbFxDDGubK5sPdJtsLVK+lDk1vr6mCIimJ8Td55S7bhjBGTLUzfTBny\n78NF16UCu7fTZX7kltX5gJCT2iZ/JYUVbsOuHJvaEJfMpkJTOCkJhdSUFw0p\ng6+vU8ViGZqPJ4gyXVZn5l/PND15VoVrZ8Zafho8UsVqcY/ybhqS8cnvPv7P\ngyz9VYxNNIjumQxFvPNyNZWaE+6nOmyp9FCg14cGFTaSkgrv3tKJLwqHlD+P\nZIb+oSB6cj4MVP36+w/pMGgXnHSk6LMZv0c7PrPR113lvwiSz1h5gHZX4gm9\nnq8QeA7XPpzhRdv+MQW6pZWEEljRi9fK5NNA3A5EiQ9e8v9/xblg3Nmq4BZa\nZuIP4Sgv3AeldR4icZua/1s5m0s8K8X7/Qryrqeb2Q64yhcJaHf3G78dIrE5\nFhmczG3JmEo5MOe1aGc4As/AdcwHENuVt/9B8oR4hRLbQBOzoGdAVHAtZz/j\no7/6MFYqq0FKtRCnrmXNxaZ+JGq8Owyic/zChdW1hMAehQHVjqQVzpvmRjL4\n0P5A\r\n=K0aI\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-GnXMjMfAPaznwMF9yTsL81XTVHBNoNW+dgZ50FKscuZ1wCV5Of1yZ2atJH8/WwgPuV9xxK+e9HcwA5kK/sU93A==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDeWuXOP8AaxswbPX4ajSIG8h7IQPV95gwxXR2lrplFjgIhANg/GJz0ut+4H2pUw3o54Mi/UXIUTEuNEh4YyeYesEqm"}]}, "engines": {"node": ">=8"}}, "2.1.0": {"name": "sucrase", "version": "2.1.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.9.0"}, "devDependencies": {"@babel/cli": "7.0.0-beta.39", "@babel/core": "7.0.0-beta.39", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.39", "@babel/plugin-proposal-object-rest-spread": "^7.0.0-beta.39", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "^7.0.0-beta.39", "@babel/preset-react": "7.0.0-beta.39", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.12.1", "ts-node": "^4.0.2", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.8.3", "typescript-eslint-parser": "^15.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "bba856d9da60410ce94c97c6a1ba3ad341ff4bf2", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-2.1.0.tgz", "fileCount": 189, "unpackedSize": 843940, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa5qy5CRA9TVsSAnZWagAARhAP/RF1PWkhzbW/ewjP5dOa\nHNg4G+dVwh9LGlVlWm31akkLFJc7wpCUzv0EeQjsw/cewDBbUiWkGQn9opmV\nh3sH+8jxu9Yct4oiJ6+FWHg3RASdQBMSAuV8qxN/PK4I3JgeAqAQOPdjUYqZ\nql6alcq+i0aFDIEx0fGT5YjJ1lIgvqg6GDM4zuNYWmAlBgJ6WtQ+DCCUmBxQ\nd2t/KRAGMXJvrEFAu40OMO6YYFKaV1AA9KNK3MDunWwZV8oDX803vjA2EzUt\nLmE/FUv/Taae3CPEEmnhoY+DbWN4AzZsMfhQHp8yjO5TtJvpRX+SvK37Hl1t\n+RNbXGPqebw1nlLSdRv1tp1gGISspJazJku5HkMj4aJxqHcu5djAb+NtRjNJ\nXrWwSAqoAA7rfnqnHh2qJbAJgxxKPjgfeiWwwN5tQJ7cmqsJ5Sg7zVAxvPBi\nn4BzeqMUW2o3ohrqV8r5kI3wsAq8pT0RKFtcCHqSiKCnm6imAcI8mFem1FSE\nAdPaAGgafpIHqrZ/oh53rO53kydIwhFnNE0rov6UYVpogjms/jBAs+MDIiFw\nruJhNElWOEa1pG8NW715HSKpuhzP40IHgJMXbsjnlt/6ovTq9SX2wQUhdXz4\nyY+cp2fhFjofIkSKd184UNOWspjQHLwpoVA+Kap29m7FTLNl2QBUMe0cT9zT\n7tDO\r\n=nd7W\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-3GA6Y6fkiob+1t8P0kkkb26ErdnyvoAwafWbuCaPLv75rB2OJaOT1FNNuSypnQXrOaC0gCJTtAT9Nm5uZzXejg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHcTBgqiSFORhSD6sIdKgS8xhN0xIVKUeapkAdvVLnhUAiEA2w9H/8I6CcN7Xwhn/vmCD5sEAq2AGlvneSrAl74PqY0="}]}, "engines": {"node": ">=8"}}, "2.2.0": {"name": "sucrase", "version": "2.2.0", "dependencies": {"@types/mz": "^0.0.32", "commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "tslib": "^1.9.0"}, "devDependencies": {"@babel/cli": "7.0.0-beta.39", "@babel/core": "7.0.0-beta.39", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.39", "@babel/plugin-proposal-object-rest-spread": "^7.0.0-beta.39", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "^7.0.0-beta.39", "@babel/preset-react": "7.0.0-beta.39", "@babel/preset-typescript": "^7.0.0-beta.36", "@babel/register": "^7.0.0-beta.35", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-tslint": "^2.1.0", "eslint-plugin-typescript": "^0.8.1", "mocha": "^3.5.3", "prettier": "^1.12.1", "ts-node": "^4.0.2", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.8.3", "typescript-eslint-parser": "^15.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "a860f428e3df57af3cdb490f065ff1360776b2d7", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-2.2.0.tgz", "fileCount": 189, "unpackedSize": 847955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbAMeNCRA9TVsSAnZWagAA1eAQAIYMAIzEQ4FNwwcMgnZN\niyyRdonsQ3pPYGZACgpvBvTo7Fjt90ruCkkkffbdUkGvdGFiBcn0ymdWaTqU\nQRwO0TAImtijsgr+KL1XSzsq5Aya1hkShrjf5YrcYG32Ohg+1pB/7eAVlKTc\nfCSEOneEQ3xYBneE4UtG0vzH4oi/6q5ARets8K3JIU23Oxo42jLc69KB9P2K\n5FwhJlDbqNN9shfCnHg7B2jbh3NJGEw7eL6C1iCLR1Vj6zKGj2eepV4ihAmW\nxWk/WH3XMN4sGR5nH/GUBbl0UI+iK2GLPrwx3mSYgjSxNSdyOdr0m3+VMleW\nPwkv1woUcNzHPS2Mu+KHDPqKMBwXNTFVLJFWTMpF9z8sdGYNLcJvoSsj3FDg\nLE/biy0H7CpgU16po8mwcM/0WRUpctZOZXdskrDSFBNNflysWTo47Ul2S0XV\nGcOEyzMxvilClgxCjfK3DMH7ljZN5kZYavTBvg1suOcG9xxyGoKPnLnZb4jc\nOMuDgpoUAEz5Hdv/gNauqHDpnk30E0wFoItsw6B1PSxfLR0AyxNzQ76V+lk7\nMfw+CiirKg66xdDY7mGGFSGXf87bxyNMDiQmfgpgfZA6aMXmhaUlYwFIyeKu\nD/i2a/M5wz5e6tKXsNhitcJfU5NTv+ChMzgeg/5dbavZJCJA9tUG0yIpPAYL\nlze4\r\n=CBPK\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-3jm7t0VadW7eUzz5RBe9tG7k3N4dirHdLFVfekyfHMCxRD/R+y45qnSjbF+zgoU+ZE116+T9jVPPw/akD3j/gQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCB9zMh6jw5ulxYkqkN35vD6nH1vuan0RkObMP/jv7kUAIgT9dY7Lf3Isu5zJQPpopUV7tGZvLTbIgbIN7g/xa3Htc="}]}, "engines": {"node": ">=8"}}, "3.0.0": {"name": "sucrase", "version": "3.0.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.39", "@babel/core": "7.0.0-beta.39", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.39", "@babel/plugin-proposal-object-rest-spread": "^7.0.0-beta.39", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "^7.0.0-beta.39", "@babel/preset-react": "7.0.0-beta.39", "@babel/preset-typescript": "^7.0.0-beta.36", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "prettier": "^1.12.1", "sucrase": "^2.2.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "e593605170ea4424f625178d4761c634295b0f14", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.0.0.tgz", "fileCount": 144, "unpackedSize": 794026, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHgATCRA9TVsSAnZWagAAIqYP/jxUndnQJQNOGRfYSC5F\niA99XYBhj0pTUpBk11oBwmrgOGsNy5pKUleSmYgcO8YoxTQcJHiZske/mi5W\nbyQvAcNlHPJe8VKF8/watPVJFpe+5re339WfTtUdcxDeKtEOcbCzuInhMX7j\n+HmSpyI3wmAQy5EgYr87K5k58g3nPH9od1ujoV35WwvKR9yKjG18Jmh4GOn4\nb0D5UIEa6W3zqAGL3UUF+h4qoiUn/xF0N+sJhEEv8g+Wope1w1P5rcSpbRey\n/hGX+qR+WCMHPBWV4ot8eagpUP2jkTyV0XMrfuwsI9KYlQExYeI5GxJRM4Pe\nnNXTo+aIOkz5r4e+fA3KBduW2ZLD21xhQmDD9qYl0hkQkCSULrsqQcFLxa93\nYwFgytfei3gWbT5dbxfI7okPunWy74lgmVWIaEfZbodVPZpSqIbICTZlvyP9\nr4B7sNI+NbmmuYXtCazD0hMHbeMlrssFNeOhXM+ah98tCVRALezRpYzLR1WE\n4K09K+xfXS2llkMzaAdICctSe3NYrqjzPj7at4dIRWs0vdB+1RDzGxh1ijul\nbgwPUxz9T5jkuYzwAhzzRzIz0alHoeWAccZ97yoIAj9/KU6Rb11CX1y5AAyH\nb+tu+KD8Op40TYG0T2dKSA0/IcWxTiXOdSGGn5LowGX+rgUrzFxpewOt+0s5\n6Jov\r\n=v+s+\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-TsvBeJCAfLbpUMd2euEr6PPsXxD+izZ2RbkJTOUBnlBSQL1q4f8cPOhAJ44kD517IiCuDPG4ozbpSw46OahQhQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC75sbaYxEq2wrNO8MQGrSImuIXQGQwuNxi+5mXcMZUNAIgSp8Gm33WpnutFRca3AD3j1tGpLPmeSmmyvHiUvYmnvc="}]}, "engines": {"node": ">=8"}}, "3.0.1": {"name": "sucrase", "version": "3.0.1", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.39", "@babel/core": "7.0.0-beta.39", "@babel/plugin-proposal-class-properties": "^7.0.0-beta.39", "@babel/plugin-proposal-object-rest-spread": "^7.0.0-beta.39", "@babel/plugin-transform-modules-commonjs": "^7.0.0-beta.35", "@babel/preset-flow": "^7.0.0-beta.39", "@babel/preset-react": "7.0.0-beta.39", "@babel/preset-typescript": "^7.0.0-beta.36", "@types/babel-core": "^6.25.3", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "prettier": "^1.12.1", "sucrase": "^2.2.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "23cee1a81f9eaab2ced4f392a6e4a524f2548c71", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.0.1.tgz", "fileCount": 144, "unpackedSize": 794072, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbHo8oCRA9TVsSAnZWagAACOUP/3I3cymlqNrif9sp8cWl\ntPCCX3+Ea9ZKX4qDwX8i0zhE+gOIy7p+Ux7KszIzq6w/nuMU4TcxH2lrH+gC\nUbqP3auEB7wwqDo36eEHsbaHhK2fFhIF3pbjyXGZWd8q+MB/sooe6FLu/vgA\nN3PSyMVK3pwfCcPtTHz/wl3lNu53OO41SFCYpuPnwwoHlWuAy1PjFw+aN+QX\n7TlPb9VxpgZCuxuZ+lri577t7TTRqeToCNs/5RIGRf/Oe9tyySQBcoQKznWH\nm8AhYUnT0ECyvjTmkg0VisrcAEV1qVh8UFheSLLPPlNFPQWcKB774+WtlYPk\ncIskMCwolmRnuCbtEb/2hoeaIVlUSWO80vjulr1FivoOaK6VKdUJ6bcQpHTm\nhUI2xAIfRxlra+dIQIKEKDmkJWECPbPIdVre4Ywq3ROGDlHX2CbaSC9ulQUT\nJXiV1H4wKGYXL+DHwfX8CCsFyVFHq5ml4/U7nyszMIkCniutxK0G2/+IEka2\nWS2UryPHnxoY3WWskzswUxbTZRk6j+F5bejqSnz7NjTsx6iZn6sTCEo2daNo\nrotg8+UDlC8tRN6tB7ZAHqNqo6E4N2874IZ3Hfdg8LH8Rt+9YgGtFhK+LJ3V\nG7X/NZxuB4LIQace3JfZfDcYQSiI3WC/08H5+b4etWDDQNjHrRCczJsse/CG\nBvou\r\n=939u\r\n-----END PGP SIGNATURE-----\r\n", "integrity": "sha512-nS6+YM71WARCU+8bKsNSYBhSmgoHwuEP93Ld8gipHZ96zdAyRYzOvmttByufxZSQrjY00E3aTLt/G/RgbGB3Tg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIG6GNcnk3KlDbkseizSqvySYsLvAROZ/1yFg7tWHatQoAiEAjqXkqEDRKfQhydZh/9zW6XFab8XztGe3x1BqBPXcSzo="}]}, "engines": {"node": ">=8"}}, "3.1.0": {"name": "sucrase", "version": "3.1.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "source-map": "^0.7.3"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^2.2.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "2474547a70d8131bcc6799fac6a14817640c3737", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.1.0.tgz", "fileCount": 147, "unpackedSize": 800512, "integrity": "sha512-6y4psQiqCjuJJBamoGdCQREsRHvfaiMGYbaF+dMA//LbjGrfJpnqEE/6sXEs3jPcRkMBuX4OgoJGsfjFiCyDYQ==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIB4uJspkovOpYvguWrT0ciLMYvH0FJJ+cMY3/3/kjrZKAiEA5yry5V5nj+fIx1ckCaXpywhHfTg06njRSocPx3AoCg4="}]}, "engines": {"node": ">=8"}}, "3.2.0": {"name": "sucrase", "version": "3.2.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2", "source-map": "^0.7.3"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.1.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "8f0c28320d987d97fcac920e67eb4ba99d1d54a9", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.2.0.tgz", "fileCount": 147, "unpackedSize": 789254, "integrity": "sha512-eEsysJZMV4p66wskWK/pYzq4u132zfGCoRvgOtZlNFPjjJF8+Nim4rHiE7zZlxRpQtkxh1S8egY1bPeS0RAHMw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDL7pVqCbrgcqdsPQKqJPpqndxyXBx2GaeuNp09kYcNXgIgXESWkKJ+tg7Edm9ZHk6v23x4tOC8X/nHA64x0ELm7mA="}]}, "engines": {"node": ">=8"}}, "3.2.1": {"name": "sucrase", "version": "3.2.1", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.2.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "dab6bdf86a9630517102355ef0555cfce150de11", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.2.1.tgz", "fileCount": 147, "unpackedSize": 791857, "integrity": "sha512-Us2Khl8tl2jX1/w7SWkIlbzqGizJJVbQUQGntf+4BC4D+B56P3gvFvL+mWU1/Dh+oDpKhFcO4JhoHM1Dmlxl0g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCdPM39wHSIUhX8UA0bMuxSaM8Cx0a2S/MwWSv3g4SB7gIgT0h4oBlD/9ohjPQaU8x0LO05JYlAKHamBL+tpkpay/A="}]}, "engines": {"node": ">=8"}}, "3.3.0": {"name": "sucrase", "version": "3.3.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.2.1", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase"}, "dist": {"shasum": "d8c24726c27be599a34251163f14e545a1bb63df", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.3.0.tgz", "fileCount": 147, "unpackedSize": 791049, "integrity": "sha512-Qnx/RXVjPNsELPhXpm6CxYKg1Mqyw4WF5yenXqQMBSmD0GGEU5hwwLxgUTZnDRsU+pYEx3dkCQxdlZW751Aapg==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD+wpeAdb7TVDbJaL+vwqvZcVbG0i9Kv5Stg1U0hso+bwIgF8IVz2gp5Iilb9oxEE9BSgNjUEqdHebnHW+qk5hH/KM="}]}, "engines": {"node": ">=8"}}, "3.4.0": {"name": "sucrase", "version": "3.4.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.3.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"shasum": "1f8f88ceed35112e6affc814b03bd98e91685f5c", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.4.0.tgz", "fileCount": 148, "unpackedSize": 792280, "integrity": "sha512-WL72lC48TWbgLsTF6TPlBns1FoHTFC5jWe/8u9VDO3HL1GCNbCpG7zgY6B1TYXqsmSS4H5BX48P5fejbxAyZ9Q==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIESTq2HdEawpOly1SwbZIiDshOHu4uwis9PpdtRQdglUAiEA4/fpp/QhEKcrtnXNoAgu2nTnFDXe3vLBLNAUcfwkoj0="}]}, "engines": {"node": ">=8"}}, "3.4.1": {"name": "sucrase", "version": "3.4.1", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.4.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"shasum": "f381197e50eae0d807a7f8a4211bd3c81bdd4d5c", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.4.1.tgz", "fileCount": 148, "unpackedSize": 791712, "integrity": "sha512-fj6cXGeXqmIRT9d0Q7PhAuhoXWM9pyQPo5HwvSFduvY3BHf6arfe0H1vO1WL9Wax+YmYC9d6P3fGqnCfqfSU2g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4zASaag4NG63Gi1Bgh5B/0wwuROtckB3nSdbTgGod3wIgQIQsjCc2N5pCqLOTul2Rc53pegfFqX9xifmAeVvGMTA="}]}, "engines": {"node": ">=8"}}, "3.4.2": {"name": "sucrase", "version": "3.4.2", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.4.1", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-gNDeMr4iiQ3GUBFvoK5k/KzuaiitnRcgMyW3NXxLT48GI2u4srVp5sKKwLAEGdcp3FyjT5/GVQA2wCRGq2xP0A==", "shasum": "9506ee279a92bf8ef0687f03add01370ea7c07c9", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.4.2.tgz", "fileCount": 136, "unpackedSize": 806805, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbhBP5CRA9TVsSAnZWagAAAl8P+wX24UrJUtk4ZNK5mNgN\ndpCAX1u/3Gy3YMttRjXsJjZT9N4c626LJGLcNaiRXM0nBJZVfwkzS6nA6SwC\nkicPgDRdlrxStMkId6O1iYbOnHW5VahsOd2H6yV3sSHsj8tTECOcq2F2YbIm\nirefVFk4ljUK24zEK/MmuuHrDvF5GDYSKMnIM7Z+XIrhjmIIGzT0QetUDfgM\n1jPAxiRUEg+sSb/iU3DG1C5OqtgUyY8LVamRLo3NmACabYaRSi9IBoworHrq\nyvWC3j3+apz85e9zKcKayIlhb1NxjcSxUsvWCJ8mz9t7EPl8U1wIOeE5XGAA\nuYjmCLquzMf6h+bk+JfXov/1ad5chzN0Y2urvpYFLZdO4rcXIpmEm+f42sjC\n61QuiVEkJZgHwg0xwnAOkFy8Zfjl8ihb6FHr1EdX/6mKLU89wOTU9L32J2Jg\nKB2NIdFEmERaVs4vc0S3Whbvg66z4EB7yCp6biPAruPwkvFXc1+67NofiAc2\nehm40wj7tCDI6O89sLXu5m9unZYmZRHGhZhJsNqbfrGEJy4pzq5aDJQJZWJg\nYaUOiZU0hwLP+IGl5UB019LoU7WweeavN8564er60HlFeb8vseBR4y8y0MFo\ntte/bXkldRde8B3AGoGTvKHrqjllHD4NKJOLAlbLGR5MiWZy1c/BKt9bhu8a\n5/cC\r\n=ivSP\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDqAzcx37d1ZBwe995uB//0q67CdPr1d1WBOLQ5uiX5NQIgIwjTCuKnGmloOxAPrzT/yNLIdU6xSD3YJHnZh2pyFiU="}]}, "engines": {"node": ">=8"}}, "3.5.0": {"name": "sucrase", "version": "3.5.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.4.2", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-3U9jSm5T24tL51KPxUo0eq5V325+g4eFCwXO8Lhdwnk0szD5OhBmmr8cn92yJ51NXcM73yZnkvSF2KRaUxIJpw==", "shasum": "229581ac4c65dfb44d07fa2fd25e47515a73917c", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.5.0.tgz", "fileCount": 136, "unpackedSize": 822560, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJbsW+aCRA9TVsSAnZWagAANZsP/0WYz+0bpGc5TpTKGcJa\nl7AFAGIOcq+QREqFyJrOjRWa1xCuUxwiQYDN+bXjgevf5Fpg6xGmtFzXni3E\nEO6D18bKsdgLhb9Xgm1TneiGUnEVqGrlXYRFDHlCeEbT4ulW/zi6jpGuhZzw\nmg5lnDx8RCprQajfVB8EvhiNCa5hNzwc6M1Omw01J33q37MQDF6aQJBXi5iy\nRo8nHHC8ukj1XxZ+7FPUvUa977iN1adMHFQEEDbkfXOnwErqHrXeGct9ptSa\ntmc4dX/v2SH4d6wBlVTanRQMZ4rzhXVHoG7tmVGQFO3zEPIEjmCmxH/xNazO\nTQ+M6ITMLWBBb9WVG9uxtXbUuT2f665FpC+VYPaHVibyGhZbWk4iqq5psAD1\nIG3q4u8hJI+oXEX5nbNEgFYQt5igDGcfEBBEFER9nngEgf+za2p8ebx8/llZ\neBHFoND1VdsJCoYPZOjElGYrrE/UKS4dLJ1dSJVKlhg00U+2wawpCf2ovgvj\ngp4bd33T6fF/IXiGPYr5CCnGvD8py2EbiKAriuxWj2ZAe6eJRZZiFfaCk1x6\n/q4dhS9oh0vsZ/MRh04q0PrxHTMWFrvwsmd0Nfv4qbtM8ydRCAa9ZHZTATE5\nNjJJc0ooZz0/lRpnbHaQquxvkPP+LM/1A5LSgMyP7ZdCwrqdk9/0sTQ9FBkw\n4lpU\r\n=HjNh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDDWdyaSYY5V9vAbQofUAP3LTpgaYyAHYzaPBsLT67OZwIgd1ZbaQZu/4Ih1KMBo7YWrR08SH3MhS1jFrkQBMAIvAw="}]}, "engines": {"node": ">=8"}}, "3.6.0": {"name": "sucrase", "version": "3.6.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "codecov": "^3.1.0", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "source-map-support": "^0.5.9", "sucrase": "^3.5.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-64aFtWO8ISQH/ZDfJ9KxuaGe30vo6uArQtceHfqcadfMW9rrlYVM7ec43vP6/c7y/cKaafcanavyeH6ig6nx2w==", "shasum": "2b3cf5d39bb2da58efc171b90b3cd367e85b3c04", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.6.0.tgz", "fileCount": 136, "unpackedSize": 823851, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb127/CRA9TVsSAnZWagAAXjIP+gLFjsL7wcqXRsyVhad5\neDDtW7fG1yUfO6LzrZXeVYrkuqhJecWgT0uf5uFzSecokQBvDX0wmRS6SkRX\n1hmmxRBMQ8E7P3snTgHFV+QwIk50EWTL9bBf7STugUHB6MkjqnnFMH5PeA5d\n+RqY2EjaxxFbHCoaVsSNm0wdthjwBxXtCbJtmNOo4btmZg8rcdF0yPCSuRxY\nedeaTRTx34lEw/JBTuQ7ur2wZTgFHNDSawUkz1c8sbfaDuaduoI/bMUqiG1M\nS5EDYwqRzc+7VJoaWFRKnlKtrilKkikGPP3lMEmJ6gyxA9j94bilC1jMXN0x\ntP/16uXZieRwdqW/t4FFF2IDxsyHkzwH6Obv9Bg0prMpttpbodTXrb0vnUYx\nxmJr1DfN4KxGHrl4rTxbsbzEtpAoD2rhnBiqpu2Ui23vvHl2Zb/czgUiekC9\nCJ4xHRaF/AmLyZ6Iw1M0ysEP0+w1j6TNzmLZORwNUpucPNjWWClplYzYQgze\n7IcOn3tZbEtWaDnNq/mfPmC4Yx9Xl5FzvAnT5Kl7XxZSvHHc08D/8Mhu1WS4\nar/drjqkO7n2eRvfEFor+KyGFK0GG1oJFqrZ/WPnysunmffK2BEyfDVlDDTw\nl9BYQfF6WzCy0vVWuWIOoIKkh2fbCed//4vuFT6G/r4QCXdAl9DW77I6Qknr\n9I1P\r\n=o/Me\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHRMFdt6m2cqqJTRPLt2Jwv56vA54XEZ8pmEuj6FoJvIAiAYjOG2ly4o5BrPdsnxDCEYLdOiGSQyxSqR6Y0uPWxUiQ=="}]}, "engines": {"node": ">=8"}}, "3.7.0": {"name": "sucrase", "version": "3.7.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^8.0.31", "codecov": "^3.1.0", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.6.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-VFKLwscIt5Y8wtjpiYOHtdrMZz1VmUNNVSpXmHVDHPi7eRW8mkP9ISlQjv/X8ranYmvvPqt5hvLHKymG7o4ikQ==", "shasum": "d167b54498b8278f7cd874037b25b5489b6671a2", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.7.0.tgz", "fileCount": 136, "unpackedSize": 826889, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb6NbyCRA9TVsSAnZWagAAogQP/jnoGwOsidrMA+cEgGHl\naWEBFN8ReUU2FYEq7gmrJE4v3ZYLEg8NF0TD9UfZb7sTPs+smWTygJnFHF4G\nkAS8HRCr/6VuUROeouHmr5cENJiqFSMzG41eDBvVN1ZnDXpRKkQiikfe7qqn\ndj2eUCuHSPyRYCCVHBB+4QTEkZIuGgaevNFaeXW2iv2O0NvBEKlz1ZtCKc6C\nk4sRpdcsPQ07SpfEeM+Xo1E59cvpP0LWNsmQxbJiYOi6PRAOX4i39MauXIKt\nbL4G1pJyeQdw1K4XS5eb/XodRuT9n7Gh88K8Rod6HHNjcCvHBoo7IqBoCB6T\nUM5xNFh1yn3Zm1tkPC1J+aHAcK9NC03N9Q2k9YnTraLQEgDaKVOkIgJZ/PwW\n6cEKHYulaawIbKu2SmWHVcXIJDfh/IMHa3akZoGlXbRtxk+C9JfBwq0nhDSL\nWiIsrXBcGfEW00a/ql4H6tNivtBVxHmea7vzzD0AdYy0PX2/gK9T+wLdU2X7\n3FjLPdVqwmqvtmC2NndwNsWRg5fjJikZkEEFbeTdQfV+XR9vdzfFZITFYaPd\nJoVPxKZHg9vDu/uvG+VXx9FHasO59V49Xj+jJKtAWaZBMjLzuAaygWH/hiSn\nSqw0735DN6t8+7tGAsGC473Agyq7kCiYOUKL/lvEcqlRQaIZ5VNZwVeZ2s6r\nOBod\r\n=3hIr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCstVXjWSgwg8bSakLr/Vlo20lkhL/xg6IN3BPVZgT9RwIhAJ7WKn3VCMwfwQCqjZsem8iuoZlYG3ftvPBYl0HQZjAX"}]}, "engines": {"node": ">=8"}}, "3.7.1": {"name": "sucrase", "version": "3.7.1", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^10.12.9", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.7.0", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0", "yargs-parser": "^11.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-gpxb4FC53QHaFw8YaiIDIfmCD/QK1cSll9dn85O/il3Pq0C2EkBWRcvcon6yhQ3YB/nx9fiLVSAlpMqJMUOdpQ==", "shasum": "b1fc4d75c0cc92ab61e91fc1ef8f070ca274e29a", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.7.1.tgz", "fileCount": 139, "unpackedSize": 846402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb8i1uCRA9TVsSAnZWagAAjbEP/3kdX9paL3pkNUT/IzaI\nrxl6UCEv8jmQs5fy8ax2uJ68o0kvQJQakksscfgAG/qaN0+qlD1InIq9IT0/\nFKFqq3T8S4etMAaluSjQxKwMRQe3y5M1FTqmhCvQlOep5dfBvLXfWAikt9Zj\n+zq6J6FYPBV11Xm/1c8B52KrCjc2IN/n+G3anB1YfdyYvocQCDMGYxdBFFgh\n0whZWto1KJIwZ0pfVfpo1iy/XgmKWPU++m5bY3us/HuWqIeb+D7uSi0FDU0+\nFbhConmwegDFWyzfhxa48xmr4sRaVEcwikcp57YzRJfB3KAvhoMg/c6i+2ys\n7nHe6n+z3vlI6jAzn/5kV81cU2qW8NAAPZwM1Lkj8zYFAt+4Vo6GWF0cpjzd\nILKKvlMKRq90KF/53hZX9JcdpiEOcOsbc4X7DCaMIfOJYkpejXaBJpJ0BNgU\nEla6BFWAadmDYn/vaTwajjimXGNYAAXJUb+76tgCNYK6GM6utOq45Z2EAbS/\n2cjx86URSt+5bd0wMCzram+KkTYFhTUvFj9odW+GLeHHA2oKbeHEV2LAA79S\nABGpnEGHfouYWjvTX26V/U3xkYUwHLvlA8dybnCpb672E9vks9onmKBG+YGR\ngogu19hvpk0HiWATU3FUckpjc0xu/6CfHfjUqemFzAglS6HBB6AuTV6IWc+s\nt8jT\r\n=KtpI\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDCuKekE/PGlX9ZhrVPHf+UeuOQGl2OoxpX2BgYjqcKIAiBDAFjA7xBp3s37gz5MfOOhABKqsP7RLMFI03ogPvC2xQ=="}]}, "engines": {"node": ">=8"}}, "3.8.0": {"name": "sucrase", "version": "3.8.0", "dependencies": {"commander": "^2.12.2", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^3.0.2"}, "devDependencies": {"@babel/cli": "7.0.0-beta.51", "@babel/core": "7.0.0-beta.51", "@babel/plugin-proposal-class-properties": "7.0.0-beta.51", "@babel/plugin-proposal-object-rest-spread": "7.0.0-beta.51", "@babel/plugin-transform-modules-commonjs": "7.0.0-beta.51", "@babel/preset-flow": "7.0.0-beta.51", "@babel/preset-react": "7.0.0-beta.51", "@babel/preset-typescript": "7.0.0-beta.51", "@types/mocha": "^2.2.43", "@types/mz": "^0.0.32", "@types/node": "^10.12.9", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^4.13.1", "eslint-config-airbnb-base": "^12.1.0", "eslint-config-prettier": "^2.9.0", "eslint-plugin-import": "^2.8.0", "eslint-plugin-prettier": "^2.4.0", "eslint-plugin-typescript": "^0.12.0", "mocha": "^3.5.3", "nyc": "^12.0.2", "prettier": "^1.12.1", "sucrase": "^3.7.1", "tslint": "^5.9.1", "tslint-language-service": "^0.9.9", "typescript": "^2.9.1", "typescript-eslint-parser": "^16.0.0", "yargs-parser": "^11.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-aNxW8rxAIOqBpkfxT4tFHe1lvMnwWaPhtwN/auX3laOxEG/0qeVtpriGPGzpwhlSxib4678jsSWQHFFy/vizgA==", "shasum": "daa0c88d090e3621d54be1a476767c792a41bd70", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.8.0.tgz", "fileCount": 145, "unpackedSize": 808416, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb+xlhCRA9TVsSAnZWagAAwH0P/2e8KbRMKqz5RcC067tc\nCqTJsKUU/KEMcKMWzDnB1JW8pYo232f5S7QCg8+D44dZYvm3eszjDwYTtxvj\n1PukFaEF+j9r0rUiRFt85+xGMokEOB6xgh2cSYHLulLs9DGjETsf4QjFf8Tw\nZiLeCmk5P7TKTmlSZJMzCrKKgVuZI2cnD8F3GMlIG9rUR5D2GSXYcJdGorwp\nmeU2n7i6yzCJ1nk5GKu2dYHihuurAfSUN5D840JlO3/8JYFZU4hNo1qbe5D+\nNdDPUq4bwh6vF4shwUyTI+L1a8yWiJy7wGrxxo2n8KJdKbQzvPRcHavlEU8d\njAxppvaRbdTO1KuuQoNy8+cb8NZyrxzCyaNhVBdLyjFig0pX4ZQtRmIU5eqQ\nFWCcryJTogN19Du/Nr0OBE5HC/WpJ1KwYtG/uvFwYsqMUBOtWJ9mPWRbkKg8\nkKZCD3LGZsrTOLuKzF97tgJnUemiSmcJAcQuyQGurdvkHs+bZ2TRyVFBTapw\nup0lw5BYvN0nEmxpbI6OhnFg0xHlWkZh7NOCyAwX8d87HcpUJ+7hHDGBiCG3\nrM4d0YNVpW1yFWs4zUpPVkJk6ZDAgeYGVWXEp1g+JY0pvz8NK+5HhV5HtX7t\nLu2M5l+i69i1BJ94iNtHevBNtG/PZiIKX50qJD6ClHpObBFkLIiUvP0iNmur\nDehk\r\n=D25s\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIAbJLZYckfpEYhfeUsP+rFgfjaKVsgJzlLxp0roi3bXvAiEApkw+hgvNP8+v2jvkOfWy8ZIvVr3lnKesqlv2lLdg2Rw="}]}, "engines": {"node": ">=8"}}, "3.8.1": {"name": "sucrase", "version": "3.8.1", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.9.0", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.0", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.8.0", "tslint": "^5.9.1", "typescript": "3.1.6", "typescript-eslint-parser": "^21.0.1", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-Mvgu45uZxl4We1X1KqEuJDHoTz/exvKdQyaSQqAZR8O64txJSvTjTj58L+0aqTeygWwhynD3GnInPleX9f08Cg==", "shasum": "62015b5c730c49b809bc0cd301c9374a129c42e1", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.8.1.tgz", "fileCount": 145, "unpackedSize": 808794, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcBWO0CRA9TVsSAnZWagAAOzgQAIm/js5IyPfwcCN8JhiW\n3AWV71pfxlDPuYZvn8UVpJHrM3dGFDzfppB6M6v3wDy/vZ+77T+4Kuv4W9Uo\nzIS8sETNvwgC84x+WbOstJcVeEmdpKXTdOcmtrrmYLOMp43faHjWwo59/JGz\nlHbk42F3yOrvisyGEcB6uTJweRqVKJFO2IkUsY1NWTb0YmbmK/Dz2St5LMFt\nBmFQ6viOzVXJDhFnMkgwSAkMgNmg9JSVVHNtXslsIkIZT7ksmGsWjiko2rpR\nT5bffnis8/kEoT1/F3XZRgSNKymPt9NBmshbdl5QFmCiYXRfJODub0EPhGLH\ndQY05tgoAhVNJ/JfvlfdmBgyhCc7AJO4+68queuxQTgE+nflDa/giSUuessm\nsmHforsvSWdHzytmd39JQHfSeaBtylBqfdlZzpeQ9q6PriVFvYlQV9FNKuDf\nOMhL/A1DGyY5DJgqY3ccnKSev1A+R1F7DpXjM0mpFfdAOf3DKOJvrLME0/kH\nZNOEuvDsjSDb8xVHEOoe+PK8b+UdsBiayRyVWgSFzopRp3amnZZIONN4WApC\nGsq+ijM1nLyyiIANVvgx4pQJZOowNydm6U7yfdd/YKAaT7S1M3zVQ922XmuO\ndsr2O9j985FidLQERDNhU2VSRKsIWXmamkRRmWhuJqC9gjW840+J7VgMxiLy\nzAg5\r\n=mXyn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQD5D7asq5Xw8IlTqsm6cjNYRqI290Mnt+x+ZgidGuY93AIgB5/9ERCOUcsQ0NeNfkRZWvdQk84PCcOFKQd1P+IGGUM="}]}, "engines": {"node": ">=8"}}, "3.9.0": {"name": "sucrase", "version": "3.9.0", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.8.1", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-hVruqlQhvX1g1m28TpvGF+l6jMa2Scyd+qBpskd4PKOKyexXE0GIkxaFxgPDnDgmDYYMJwKrQiCzwCHuLQIxzA==", "shasum": "e6ec1dfb0a0b95e48bb542756de4086a8c929d34", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.9.0.tgz", "fileCount": 148, "unpackedSize": 828464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcKUvkCRA9TVsSAnZWagAAYbIQAJTfSyitpCIRTY9pbpsZ\n9j1hxXFI94AwDP/k+Nv7h14BtjeUw3keMW/pgfW5wbPg/ksW6H7IiBivtvOU\nHb/Moh52DLyfbJ1qyPKSeThIs6l3rloALzPIRy2uGrXA+Zuy/r5xsto2Xi/y\nOSYpIuycMR2zkeZ39GtlqVHblIPJISktCi2ErgJsLZ7q78b11W88iRGx6Nv8\nF4UvtijNjIjtWkMG86wIQJUaNuF3jBvBdzI8kvZHpNzeQMKYJCdSlqatx0nJ\nHQ4O6BZsjxZmd9LkLniULAogLgNXuur85QMNNVz/DKGRvKSiDKGHMorUBp/T\nkPUTSnKujsQixyqIWTBNZ/6mDP/aPncZVpfM0yeGtJiF60DjYna8atlQGEU6\nQ6qLlPioab/Gs1ipz25+y/XOiYjBYMULIpq+PAkeLAF/odacRRRC1jhDlyEq\nFYKJGfM1Zf0e4IZo/TD/QoNLpto6jxn80THMlfx70b2m/ZkWI70RukC7GHCO\nqoVD9aSkkTbLHOKpXWq2zC5oGkf9uh7yh6gGsuhuiL1qYY2D/f4Mbmm4dxFl\nl3w26NIN0UT/FkRpQmB21RufPinXtsiuEhopEBR9lypARH8uJg8Z+k92t2Kk\nHSy8js6t5JM/qMy3hFQUz6dtwONhmP2if1mi6tlcLj5g9rC2OHM5bRc+biVd\nujAC\r\n=SkHq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC35LVMwb49BqAV/5nkfg3QLVoiMtPjywZJZE1xtqsXOwIgEy7RT9dmHsOcL8yxjQBdmPR4kW1si8GA4JtX7yCT6Ps="}]}, "engines": {"node": ">=8"}}, "3.9.1": {"name": "sucrase", "version": "3.9.1", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.9.0", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-im4FoCSqTnEN8VXNZflTioAIvCZ4CkiXP1/ZQhJHFD7d5EZM64Frl+dMrwm8PzM1CU0peRS9qXK1Kgem7UBUQg==", "shasum": "95efeda8b727197a8eb1f65b539969b0c41f77a2", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.9.1.tgz", "fileCount": 148, "unpackedSize": 828762, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcKmULCRA9TVsSAnZWagAApG8QAJoE2kUMk3PPuQcORZD5\nbKunNyFSCRqBYEoiR2AQutBnJox8JCSy6hEe8J75s9/CwaEFnye7NIck2ilt\nGGfWZcypZsRkoD+BaStrOHEF2lX/s2rwNCoEZOoB+UtuzChfjGYWai80ljEM\n7GyGs8nMS60ddgOi8oJXJCce7fcfIUcn1djMCHagetM6i/46mkpWkmUMcwzR\n3yt0uFkspO5E0wyYOLNBPa9k8TQAngh1CMyYF7wXq3iCYsCmGavhADlRBX+V\n9oPk5qUSDIvouzTmVwPzpw12LDHNrb7oFbbNYtHjcD/SLSWUTaSYEzc4OkeP\nq6yNXyj5uRpjPU3huT45GjltEFe48hADvRk6/CUDClT9vkOux4G1qICXwdOw\nXkyYpbSwEkF+7o3H5k5+0+3YrKpOI3rhXt/qLaOJal0k27otu3iIsxVRxOEN\njtlckL585eyTKzz7lI5kZtmeWyvQwM8XbaTGvNqBz5gMQ0wZp4lzyWNqA9OO\nsJ2LR5AOUdjcdsOVQIWUlLOOdN8mVAHMEwYGT0VZCcPioDmZPlDIOgsQta6E\nuAyzVbXzU9McAreHm5qcqe6oZ4nS1KgMs6iM1cLt+ZTpdfhy+VqIFRF4kWCZ\na0GT0H2j1j8WdE2S+XQnGgdZySTi4vNjpSb59jX2KuYtF8jQ+qqNpb+ZDZd9\n3Wot\r\n=A3+P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDk/p/A0fuXfvMPJr8w6j6js/w/tGroEB45NGJQpNJJZAIgW5ReUkGXSqjTnlJGREW1btxNzj347qd9/3m5yl1nyYY="}]}, "engines": {"node": ">=8"}}, "3.9.2": {"name": "sucrase", "version": "3.9.2", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.9.1", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-/bSiKKJoeJPHGsGokAeCAGUKPwubvHrlmk4v9zJWOeIQUixYVgBjD7tcR7yr1Mus9eC9xr/SzGYDzwbILCE4Fw==", "shasum": "20195d7f6ec38dc92464c57b3e5d86ca2a05f90f", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.9.2.tgz", "fileCount": 148, "unpackedSize": 829464, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcLa6ICRA9TVsSAnZWagAAayEP/3UZkM2YDEtZQcG9Q+sR\nto7yK9BPvYWO6WKs4FraLwREz1P09/Q7aBei9QW3KitWnrlqgVh6qzWjjNJF\ngoCWSNqeSM7dvHZg6jRPabF2FJOz+LY33aJrMX/OqrVXJPAh0f5sGSihODVv\nNBeSASagVC6meVlwXikFPNPpHDg2+mM+V01pMBHW2h1mLq+OvS1cBAJlyRFg\n/vYlXSpI09n2do6uk+3zjWKOd2vZ5KrzUwolR9N/GKxsOJCxx+VcTJM302WR\n5hZUowwMFZMle8Q8gAFGrIfW7iSwfkxH+cdnrqcSAV0ruCQzU8b11sN0mPxz\nRkmzDyrZfisDnQs2Sgs/Nmpw3512c4egZyISvqDF7Vb4H+ZG7GB/MMCb4NNp\nXO9r+uDgze826+UZKLi6CHcXpxAaX8RgKFa5hzkaiFi9kjdjxdJSiux0Qpj2\nyKcGXYuCTGNaLOLBfcnrvi1PFXCLXnBTh160e7UYHJ+F3DGMbfviKdcAYVB2\nhB/w377uhiCIlb6kmAqUZmBFRjbI8lD5yldpN3RXTZkDrjAtpTgCSEPq2iu9\nxufEq3p9OEYD8uNGFPOofXkJSwIUPLgwQ28fO24E1yACyESd5oHjYZEMepQZ\n7APbz4qEUAXNb9/FJTlvL6OYpUkxBDqqXy1jAVZ+yQk8oz44YpMO7D4AbdRF\ni11W\r\n=hW9v\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDIIRRSCaUIcrxX/ZLOZgq0VpRXePWgT2OwmU8Fh7KPMgIgMNBeqqkn8FRpvy/WlNKp0Ociwr8IivRk61/J9BvZHKc="}]}, "engines": {"node": ">=8"}}, "3.9.3": {"name": "sucrase", "version": "3.9.3", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.9.2", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-rhpOwqdtPZJAbr4GESqacd9u+ZBRf2CfiGDbRJYNGhFuPjJD0Nz/zSCNfYifTbt2yYPGeXE4nZyT5EL3zYomsA==", "shasum": "95a6ad5a0317372d054715fd225ba8b357138226", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.9.3.tgz", "fileCount": 148, "unpackedSize": 832389, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcMkE/CRA9TVsSAnZWagAACZsP/2L/k1K5/1uMhZ8oGRJH\nW9NJtY435mSCx00ING9XtXGRWSeLJ7lHCLdCtpRYFwywpRo/tFB10qv6wxpv\n3DyyWlOn8/Yb7iKNwXTdbDZYLgjZoIjtcpkTRMjvCaToU5khRVRivAXs53IF\nJQe8GuMX41NSqtM7W1ae5V7XkJdbkIF0Fs8xwMWu7kbvRQGXIsOX88/8ahuD\n2SesSwsrSdFgoN9NnG5ABy8jtMrYblK2BNqLZyNAES6kvNFRALOpr1vumbAQ\nTW31NMBpYIuHUmOBRGIToUrCBLQLArSqekVIy3li1AyOPq5B3Gx8hJiZ79pA\nCaZaxnKaZWqutJ6biNIX8RvTjvUWRxxq8nQJzF3B2teIQDwCH5IwoL+fjL6W\nKpdpDSYZKJiNr5GKuj14sL/wrqhlf+gD18WkdCy8UamVrf5iFjTJmH4naJDk\nr+DZblm8qPYplLxIqdyDbgFcweeIpxJ+a8G9ZbYu6vOvfnVWC7Ehr0ksbo/b\n0y9odYUAqQqgZYQEuDn6fEO1mKC1K4SwfoAQdLrALCLMzKa7fdW3V5OVtrNQ\n5g34iG/ZLeAun/YKwad8oJtadlVgVyC9vJw6EZ9lcQ0ecGEVK25rpy/1orom\n5tIWbQUV24ciF3k+tX13oPoMXx9X2qOYjmTHzK4LaGFSS2kQdhTy7pSwrXjW\nwuEw\r\n=dJ1P\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCAmOhNVQ1DsAx5GT4H2HTkibBB6+xmYXu/FvX9Yi+NQwIhAPTfuo3Rf5rhPq4y7OfyE4JsQON2nvESWX0mUmHRZ4Yf"}]}, "engines": {"node": ">=8"}}, "3.9.4": {"name": "sucrase", "version": "3.9.4", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.9.3", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-Uz4gRkJtWgFMpnn5R/nj+/RfIXVGz7nsQ4/bqy8jQvMP6mWwrcViriZZHW1C7TtH//EeujStyZ0EL327TDMuDg==", "shasum": "ad18d270123e7412d0f94fd3aa0a0859d7d8abca", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.9.4.tgz", "fileCount": 148, "unpackedSize": 833252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcNCkNCRA9TVsSAnZWagAAcMUP/inG0Ikx6cFOX7LS5S6n\nZBVNYgBA1JCqGf4kY2/M/tvum53ArZatdauIi3ybzaPLGbWoScWHsgVzGaRa\nk+9yRKCnyb2ytGxxekoBdh8myzQLbzbX5VlysdneiB4u9bcpqwS0T9+QZAr3\nV9mULyp+cMCHgjPQqqcBW4QS14eAgCsl5jwgDYrQokLggOmEVXz+pkQ7obeu\nzoZ2D2wQUeGL+O3GmW8pTYvOHJmCqbgRInpvEgDkeIAvgmL7rVPKtZReEe+u\najLPtFIExzDe9rqG/HrurI8TUZ722YgZ1qLW/CGKg/pMnvNQfeNWAB1ylJgn\nvlVjde00KT5Gm9kFbm/1JA2a+l4G9xPvYCeTilBRZzUfP1ZMjfcoqh+iwkhL\n/SyAURhe/1BlT7VdogHozdwVTuT88L7BRSCnemcVMD7SbX/Td8gUs41m4EPR\n0vSESBBgG0OxwXAnNKwCgJ/qMPqiISluctZdkySkrDAK+nLRTpoBcPENhK7H\nHxzGLIOBKkNGqMpcTvMJ7YZYvg+VNN/7spYAW/vGCWd19OhfCQK6bDo8Jdr3\nyTIvvUOml08FwW8iwSG3tXuOPobPKG6NmfdpXqTtW1R0wIwIC4RiE9JfvXZJ\nt+1/uitHfgpEVu78z6uQGaVCL0lzz+kMN7chUodUTR93iyIVst1iZAdFUSUn\nuTaK\r\n=RSXA\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCDonYC7kgpwDLtetn/tVNKAK5cJLqevC73tXU+bqcuqwIgOpJAfyFmBp6pIBL4eIXUtsfLrqp2szrSWzENWFtzi4w="}]}, "engines": {"node": ">=8"}}, "3.9.5": {"name": "sucrase", "version": "3.9.5", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.9.4", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-gvNjd3PJ0WAXxWUAQzCw/nlG4J5a2j7q8jN+sX8Fwe3ryIsl9UrEcWPxWoEzBPrj3Fias4U05iN84m4/IFQLdw==", "shasum": "0db86a1b0b14dcafa98e1c644e164caa37c78747", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.9.5.tgz", "fileCount": 151, "unpackedSize": 838118, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcPDVfCRA9TVsSAnZWagAAdnIP/AoXUGvx2VHmCZXWKlqD\n4SqC3BXbmzBUbfq8FdUu6ZMhAfbc6qECDjaSqh25mx21zPQ0gdajrUayTD4a\nIyJGNO1Sataa8xUN+CQjkxrqHUgJ/AV3d2TXBKSgZZn71TGTOB7JfcT1cJoE\nYytU7SoyjhzNTn0/ryQbgGb1E3jzASHRETXLOkEyTxck6qC5y8KWZRAIS1a8\neDn31ycuD5IWEV5aaM1aR3tHpd6Byg+zGCusCg0zrIP/wYLXVbXXy5NJFfax\nf70ZRVMUDKpW7nbszOZqND7aZR3JB43A3F/au6B5RQgIXT+6K/Vwdi4wTt18\nAq2zj40IKbs0U+yNNQdZTEfxWKPFzdbgMztIaJq1w+51RzpbPmE8CxZeOipa\nnZIKzA9Ljb2lwKKfO4QVa3NCos0E34CACarojQ45h1B7LBzneDfTJoEE4Nn6\nBXVkrRhP9jgRtIodGQPtKVeR1fo6UY+6MyB9nJfCu7MauLnHq02s7AJeNWNH\nJKLEDa0Es4n8MruVwJw5+58T/K9/gxxFoHFWq6xb/EQ0RhsBTGJZweMzqesV\navq1HcLWJpHvSTfKPiq21FsXbg9FIm1vlvvkYxFbJ38Zcmgq6HWf17V+8c/9\n1U+8z5hOi19UGAuc9HnhyYhkgfnIYVtTnfoljx5k8qA930NL41JwbxWeb0GD\n8kCC\r\n=stUl\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA9hsRRYsf+ZGBNhU1ZVH3eBrRy9uLUHt48zPtloWZnkAiBlnAxDseoNJvRNT/gE4dTP46Xoo4gCboSiPOoTIMQ+SA=="}]}, "engines": {"node": ">=8"}}, "3.9.6": {"name": "sucrase", "version": "3.9.6", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.9.5", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-Z36EUbmeZtdV4c92bCJc+6lcU1P+96NDNtmIjHyOhXr7uBqT0L5aCmeKN9poYvdRKG8kvhr59xPqAdV3v6a6aw==", "shasum": "e5d80d5bef9ee4c31704762e075f543a7875739f", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.9.6.tgz", "fileCount": 154, "unpackedSize": 851647, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJceVVZCRA9TVsSAnZWagAAOwEQAJZ4Slr7Z8rto366yZuK\nw4PTZVPDPp1Prx9gpV9JPAb1MEZVlaZJQKEm21Cx5R2j25bQ5k3TolRuhzXR\nLkiJwhip5H7U64+QXMTcX7WHRASEQ9pFbmr9tQSxSOL9X787Y/ui4C97gQtR\nkyYBapRYundfzAsUcP59zQVfTn2Mn1YHeZKFCd7VjBgzZDBlycSvQ9YSt9d6\nBJ3MSvDnzYLDGl5mbEZnLgFtA2+zxqK9YEqvAl4KN8LyM7KdgTC0fgN8INB/\nHgblGBuB1QKB8RvS2cOpBeKB/ctlaX9yodShxg7ytEx1t8mPX4tuNw0WzI1K\nxAT3eUIgJnrdrMr/uCUWrqyRe1BM8kNuk0EnoYxUmeK2/JW0ZvSOXfw8MGIt\nTY94JtoPGnRxboSJwoog0Yfe6tFmUavFwlZ/5QOzxIVShbdCz8aA9LVB/kHM\n/XJtE4IDEcQj0tD5mEzQWMMqxC8aEExwZX1u7uHisrZzdAaT0zfue/2/j6b7\nU0pometVQ0wAYQCCpaY/sZGxrZQl/+dl0yPYNXbjSYGflIiCA0McQT9lIERS\n0s3Uc4eB5obG03EQDeBpY4sY50rB2m5cNtlF/c/r2dIu+AFGYd/3iZGZucYu\njurjj54fHGZQpcad6R/gS1DwoYA2AQmysBZo7MXq8QAq3rhqOYU8iNE4lXGz\npgLu\r\n=9/h6\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCGAYyVliNZDmONcGLUEL8Kj9Py9QFgy3M8Ni4tJWAYYgIhANC0yasRiuKqvcUd0rWp0CUQpDIuw9F+7odnflsafbcx"}]}, "engines": {"node": ">=8"}}, "3.10.0": {"name": "sucrase", "version": "3.10.0", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.9.6", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-bWXuX3F+P8V7npz1sGuNc3X+MNuUNDy/STNyQI1qN1AVb+0yTKbriTKb/rfXqNVEcRlh5xVE2+Pj4fyzJRhOxA==", "shasum": "e08f194f6c47576eb759ca9d0a82e32271abbca1", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.10.0.tgz", "fileCount": 160, "unpackedSize": 870809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchhLOCRA9TVsSAnZWagAAIbkP/RByilX1kZEodmfWKu9K\nwkBmQYsy9bKtbALLEoEcqQ63SltM1Bc9u8lrC3KkoyqpxFf8FcqxXcWu31xL\nQEvClf5Pw9kRtUvbKb6WlmVyoLPmDwaJIu24LZYNiGW4QxBZs+rs/DSD6b5k\nYV5/jskC0GoNC7z8J6UeG+wpQYM6lvEi68bhbZL9pLpJaZViSoeb5SywsYxW\nXfZevbHvE36kDJ7h3O7o00HgBDdRjZrorMktQA/MH07NeUZd6e0zAseL9kIq\n9Gy6kYkfuiGXU60s/JnpNWE4urtjhcbuGpIcXjJ3LTUo3X1ev9OQTh5n8DTv\n3I2C2Z9/jzxQk3tleuU+P4Mgun8f4R/0GgB0vPKr3rHo4m0mEy7wM1CynsLo\nVlOVgEAVByX7LN5xGU0xWcNPsgVWS/PoDahF3AylTIMlFV3mXI5/6A9ipmzq\n+qemSV1f0Gmt4QffWHHo9bI1xIYTEusUNzhbYWPmQpC7kMZJqPMnIF5UGQ9E\na0zAm6jvzFteV3v9UcrnGJ53MCm24w1e0GlJGcfSk1O/4CHCmvMyfyS61ZoC\nRDGu06WTAXhisws4bal55Jh/nT29qjhmC/TZcDb+xpxczKnsqdQH+o+SGLf5\nkQ6cQco45N+cU8fvudLOcb5qTtYagZK4Z5qQiyMRHyXUHb7FkxXNmWLt8sPy\nAwk2\r\n=pAB0\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIEIO6VmPtZ/R1v4IoLJiZSYeNxrb3BUUsXxCIwbFzH3SAiAcj5d89L2Px6tIlavxd3Hyg2Avhej03VQRzT0JoURk9w=="}]}, "engines": {"node": ">=8"}}, "3.10.1": {"name": "sucrase", "version": "3.10.1", "dependencies": {"commander": "^2.19.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.0"}, "devDependencies": {"@babel/cli": "^7.1.5", "@babel/core": "^7.1.6", "@babel/plugin-proposal-class-properties": "^7.1.0", "@babel/plugin-proposal-object-rest-spread": "^7.0.0", "@babel/plugin-transform-modules-commonjs": "^7.1.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.0.0", "@babel/preset-typescript": "^7.1.0", "@types/mocha": "^5.2.5", "@types/mz": "^0.0.32", "@types/node": "^10.12.10", "@types/yargs-parser": "^11.0.0", "codecov": "^3.1.0", "eslint": "^5.11.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.3.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^5.2.0", "nyc": "^13.1.0", "prettier": "^1.15.2", "sucrase": "^3.10.0", "tslint": "^5.9.1", "typescript": "^3.2.2", "typescript-eslint-parser": "^21.0.2", "typescript-tslint-plugin": "^0.1.2", "yargs-parser": "^11.1.1"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-nMOs6rFWwkYRxcKHHDjyQmC5CmLbHN2LwRyWF1n2i0kb/pq0xcB9M19TdY5Ivfcj1BsWfs+az9Ga5B0tFdE5ww==", "shasum": "70ce0bad0e4c8fbc3c3184dbd1797e82990d0602", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.10.1.tgz", "fileCount": 163, "unpackedSize": 878228, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcobFZCRA9TVsSAnZWagAA6v8P/1178qMMhHYa3fHWUqVK\n2ykNPzGR7Rm9VAIRq5L/76U2sitHSuhvc9+OSpSAndxXW0cs6xEuFmT6CqTw\nmuGis1Qagjz/xFQ/Ckye9o3FjehQ16Jc7B8PS2JKRNwDbpu8ALJvQ/gj+mAD\nUpJ+ki4iWU7+jVXmm5oSkv6QXW8MhrOqBVM9N298F10I6PaC4Db7X6fHG65L\nBAe0aaLYNnFbZzjhwgDd9hH9a9+gED9zLl6Drf4DnnJrMTgHQSMCg1rEEsWr\nVMS1/RdK3llxIEY+nAUB9n95gfcK3fgtWMqBSqZYw55UXydfjnc6N41GZzbg\niNptZZlMC4nYQVxVG6b9HCsfjOyqHwrc4XroInNSAsUcp2HrITv9llZ6h8Zt\nOCEjlBYddtFFGdJgPOa1/Yu0aFZsVhHkP5mbyoH7u08EZPwS4WT/XWXl3zRR\nOlka6oy/ZRNoehB4p4t7hbeei8mtXKkaObKJC/SjV2YhIwyoVkkpUhQiTjlf\nAtv5sRScNqki1BEV6jfIqBcorWVIRVysv99fXFehn/cUjdGAgWzd5e2PRFRp\nModi9T3GtZX2FmYNG7qbhLkaTts2GUXqsACL+drMg6vnFCHgahG+kuRPvs4i\nHB4wOFhYemqkTY+Tgg+oJPNSEKGD4kkhqm20dLzUNfsA8r1+BM+ff6u1cDer\nA1li\r\n=ygBm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBJFsBjLAq4A7Hui+0uRKsO7o1PFN/8k0PbW45rUIApHAiEAs3bJbqp41EO1c6Xx3iP/YB+0elcMVG+OHilyhohvl+0="}]}, "engines": {"node": ">=8"}}, "3.11.0": {"name": "sucrase", "version": "3.11.0", "dependencies": {"commander": "^4.0.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@types/yargs-parser": "^13.1.0", "@typescript-eslint/parser": "^2.6.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^6.2.2", "nyc": "^14.1.1", "prettier": "^1.19.1", "sucrase": "^3.10.1", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^16.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-vzaNQWMZGNNIOONwBGkwoVPk/haP8TLK0SRTxL/FvuIoiHzDgySBTReTLaOYLAcv4OXvyU3KddLreDvR/bABLA==", "shasum": "8d5eeeb38af5c376de906065fa145ba786af5bb8", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.11.0.tgz", "fileCount": 169, "unpackedSize": 882682, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeAE/2CRA9TVsSAnZWagAATxAP/R7JlpwGN2AHpgb2QfSO\npTESfk4A62Cy+XAbxZp4n6MXp1Zh3gHMKGxEZ6/6IpG78+djajmT54MvRHPY\nVFk19DGtu+eUbvnjSGZnNGMLCoUejV1PTj64oeTszCKYROz0Whl/kLfwoeHT\nFm3xxjOupoXt+JdyyS2sH3UMmKdPnWrsn1OehDvGijVCkc5LL/yY9M6JQfe9\nX0rRM9y53YdWoaGVaoPExhuV9YxtKDhkY3CAUPL0byErvinUkDxT43g39S1j\n8AWsmEMxqUWXdDe3s5jQjD9kMDKmINcbKscqh97We8r/hR71J50oOM1fr4pE\nZi95QmXvHu57S2T5Wl8Kv7hwcAE/mfy6wD7UpcbfIrEdRMbxbAO369MZ5Fpu\nv4y/ki4q679Gu26EIS0m3gv7paf7jsBCq4GoGgqpj9KhkcJqAt6P7iyIGkaP\noG9d2NpiT3R5mgSrBVuBF1tPo1LpLzug7WooPUp7kVWrS9gA5P5DDtDomJvX\n8QfoAHAoCnCJ2YBDVLHjjgb4huKrFv7ao1ohNfB1W76UWRRCeOyEAO7nyliW\nkA219V6fibd80xGJ9FD4oztPgE+gxhRWqJtbGLYx4B5uIMYBrVSijx/gXRdE\n2qBZwDlEnvedjR/LJoAmXnMfhMBZI+uSVgoFM+/uo81rDbbjpl/NPctISAew\no+xS\r\n=NrPR\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBjfsQfL90ZSsXPk7c6vdrJ972lh9V417PrCAUAg7KIQAiEAiCdUhOrnq/5EnPdICjTUV4gL34h8srB3DEkxILe4jys="}]}, "engines": {"node": ">=8"}}, "3.12.0": {"name": "sucrase", "version": "3.12.0", "dependencies": {"commander": "^4.0.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@types/yargs-parser": "^13.1.0", "@typescript-eslint/parser": "^2.6.1", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^6.2.2", "nyc": "^14.1.1", "prettier": "^1.19.1", "sucrase": "^3.11.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^16.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-GHttbEnUlGt3O7oQ7NHV7NCrAmGnAsDb5ar+FUi7hb6oivoF20qokQ1jw2Nv2tCG+VqxF7P+hPavjMXv61oKdA==", "shasum": "1983c8fdf0ac901e8db68917fe76860cce7c1b97", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.12.0.tgz", "fileCount": 178, "unpackedSize": 922991, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeDXyjCRA9TVsSAnZWagAALIcP/2aC5GptlJfy/a4KMSjG\nNValmRPUKq1ikN0bYv0bfkkoY5T/ZFt+Eg5818URg+0Ln8JxxhnGd3qJbdwh\naTtC3lHKncjk7takMRBVwtyWWjI0tsw8fX6Gng1n/ny+v8jBTqlhdE5mPwWv\nJihstDsOcZY7ODyp7kGwWU+DkEqDPnpSZxeH8y2OftCVdgQRaUbSD7JDkvXV\nvhbnlQ3bl+uGr8Cb8Gyvu/CtorSwebjl0caSS1zgOAqnKmSONRAevsTWWbLl\nF/qHuQIWffgMFzE7IzKeKPvUqukNDHzrF9TI9x0un6M4bbQSTCq+XtY5jYkG\nMa+hvQh6xf4RjNRv8xgm440HJYmSeO05OjSFrWJmTxys2Hos15x3cWjO9luW\ncQHnSUTzRUCZuZi4Pd/JI+tVvZB2bzmNYA1POMg9BO301JAoxpJlYAukKe+F\nLjBRtc7VoNHb5mnVJxxhOg0HKNLRB9UsJbvPaP+LR8EXcfiN9m4+v2yVIGCE\nKXs3UGB6R/aIg0WZpQ2wJQQDY8HNk2a+Wgh+ughizNmWwke1HACHX5fZLWb5\nWD5k1rPe9x2/+NYiYYIA1RqwiAnY0zIgATBEyXDg1UxzRjcT1B1LNChvNwNX\nwMSi5LRAeiTL7MhfHIUswl54cETvpXQ90z8L6kdbuIE/VdUpBh0EOkg+Ws4g\nhPi1\r\n=yCwm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCUjvb3hK0gl0mMS0H9FPJ3eV7gl8l5YSd0Ch9EJeLpdQIhAMZl4GKKHvcT6f982gx6+VdY/MRHU57zwxhv1l8/TnC1"}]}, "engines": {"node": ">=8"}}, "3.12.1": {"name": "sucrase", "version": "3.12.1", "dependencies": {"commander": "^4.0.0", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@types/yargs-parser": "^13.1.0", "@typescript-eslint/parser": "^2.6.1", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^6.2.2", "nyc": "^14.1.1", "prettier": "^1.19.1", "sucrase": "^3.12.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^16.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-aYG1RVImoyczRm/puVkNjbWZFus2b/LJj58RWEF7oe4XcKu/a/rudq+R9OrO69juzVx6KnPGTvjWUbIGnXTeFA==", "shasum": "85888fb0c3c39e3723b720690b6db2bde7b34c5c", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.12.1.tgz", "fileCount": 178, "unpackedSize": 923949, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeHKCBCRA9TVsSAnZWagAAtqoQAImLtlZvaI0iikA4sTK9\nF6cXPy13E6qGgOZ0GE9BFQUbJ5sFAWHjLpLgLd8ytEi3YL8FmJQs20H5+kNO\nU/JIAkdMG54hc5heGRWndF51mFOvcOSa9CFXA0aydYfADCKjViha9+f6oIBY\nhw2TXWMnUnzTi0f1ASyghzQsgb7sIIvEw3Zm7ptJukYcmJs/qxrLMhm6kzPY\ngFMpuBxe2MQnQWj554Q64AmTeyyXPbKMY0G+vGHFWsphvBO3Gvo8MKr40ip3\nYSDb5cZLwILt26w+x6HqxdGnjEpt95wU/2JpV09T9AMsFMpVMub/tVEmRZkw\n0oW+Sd5XiK1fDB2vB9sWEamoUD1dXfYbLswIRn9s/0lFKt69guDkgUKne1ux\n9qYEIMKu5FjVlmAGwACutElgY9PSpsoXPnqTzp//NVPp/YDvPIytqQozrkIF\nAxXJ918cnHwbN5e0mKGUm+OGoB9HpUIrzmk2L1llTlkV8aqYAOcJS8hQ1vGA\nqDbSbHJUBPf5qhMWQ2DZgqQP8gXZpu3t2Vkk+vYblxStDkmhEkx+Tbg9pWpg\np7vLvL2R4yO7RMHjrvp8HNnjF77yhfA5I5nV6Si4yh8SZbxXUtHzXPrzPl/0\n/LrzSxr857EI5oXVOWrS8vvMaytNpYSSXAZs27RUIuj1osDkRu42mqt7kG6r\nuqgV\r\n=wE1r\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDsjpoEvbCiCgjFnMzxlH7OrUd3IL4GQtyUaCT3yLk2iwIhAJ0J/jCtE9WB22JLabRbW4VxXH9y5BZCPEqcr40BHOo2"}]}, "engines": {"node": ">=8"}}, "3.13.0": {"name": "sucrase", "version": "3.13.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@types/yargs-parser": "^13.1.0", "@typescript-eslint/parser": "^2.6.1", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^1.19.1", "sucrase": "^3.12.1", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^16.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-koXmWc8Iq8q7quNJ9v/TuDIRBeGul1D+QL36PnfzFvYFoQbWcYpSmpJElpSM+eCa0nFthyQqgCGrEKAepnFMtQ==", "shasum": "68eb000dea32dd2ec84b91de97c76dcb83a1a2ff", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.13.0.tgz", "fileCount": 178, "unpackedSize": 934535, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJef9OGCRA9TVsSAnZWagAAw48P/jAL3TvzQrFEDvMQ+Lyd\n1o1/obJciB/1dlMiFkWC8p1Ev4YMcTZadQYdv+/woZF4qdSB4Go8tbbqMqDQ\nv4opq6RpVS64ur14GnxbWbx61HZq1HJY+HrgXntNlKGuK09o/71UbHVKvPyg\nnW5Ck0ydpwunUNbuq07IY0PjgJ/GX77xTEThI+GlcAZ4rNVDB0erToqx5sMk\ntF0280WA+4GX0eTeCXYOlQAe1QBBzAjjtdB9PBvzn1Z5RSF5OlYc1Zb/rmGJ\nnAk5Yr6XJtt0bXJD2tGSx2YqzFURkSJIkPMMq9D1use4TGekrLUKFGV9WYka\n0eXnjFiDLqFoK8VOSPvikBZAe9WSR/mlP6fFxI5DXJaXf0SaoBKVZWdgBW4Y\nXPuSOBsZlN6c0KNam4TxYGyfhTTzflQUxIsYCks5UbYSAdyk9klnbRGY8yFm\nALG0ERD59mpAxvCjNUwtzzUO8QI5WztmcB5ARzIcP3bd0oHm5Mn2WpKMqjJ4\nYIaFUJiK4pMgtpBW1tRVFYZdCsjB1a5+E+VyhBm4V+Ptk77AHyfsdBVFCb6m\nhQ2P5+Yn90hdoJfnPYzHCuAF8SahxWoAPg2/7kxOomg048rzdUyBremqKP1B\nJhtH3Yh0LsiDmbjnAG06PBGyhKHbNjI+yDXPOIG0XWDST110AiatS14JCxL5\nhOtE\r\n=Y6R8\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHZ+6AYLynqTHTMyKnAtmHlJGtzwBCYi9Uj1HiePlHHnAiEA8GL4vVcm6AbgRzHIeBfwvR1E8ISrCCofEBCrGzPTGHI="}]}, "engines": {"node": ">=8"}}, "3.14.0": {"name": "sucrase", "version": "3.14.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@types/yargs-parser": "^13.1.0", "@typescript-eslint/parser": "^2.6.1", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^1.19.1", "sucrase": "^3.13.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^16.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-sWyDtHMD0Q1wv4GpL3Jp10Pxi8ht4qrYeo1tAtHJ21BaMjl3PCrIM22FudoKRVY90r+lj3ytvIcf6WkYqt7TJg==", "shasum": "4364f4da5d57465acba364ef65b14ca78672d500", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.14.0.tgz", "fileCount": 178, "unpackedSize": 938427, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeuN9MCRA9TVsSAnZWagAAt7sP/0k8o1YcgOvMzsHiNVMn\nD9/2ltYkHna0x1FNdkOoa7Sq8evT1RhL/gw8hPJoSAzQypP+soZbFfNDeZhH\nbIGh+eEUv+KUyHSh/WGp0N1b8TmaefYKdgdRt3uHnEzGaazZ1PrgE7zTTG0n\npg0C9D/CaVs8HCjqvsmEOqdmhuLohQVR9ZNojM64SQrYNGMNBVG1GrOc5oqL\nhNETmbi2eFQW0sGfD/KG+Re9QMlM+1p/rB9DmnoE4yYCpulESVgyJcGVK/yH\nM9CYIMvZy9XnUet9VuWiohNso93xAlEhioR/Km9pGHtZdKMcn0V2bhkyyGj+\nV2E6EYAQTa0uQfivFRjhT+1+4e9Zu69uCSHlaCvPrwRBR587Wj6K97hKH/pn\nU7xRn2q712MbaxEEVk4ygND63lNrh/ECIajIYr3XDEGhiMAnOzUz9Sva53Ib\n0DniG5Uu1IbtUE4uhAga2xTSa6fwd3FLQAkmGR0QcWJfAlBA1LdftCJ6ybAy\ntzYibVdGrxYf6QsoLfoUmq4UXX2gKPEY2aRcdX+yYzxcrpQF7XfZFyHGphi1\n2O4MXb79FBvapsOkfGBK3oasJwPvHYGKgh73Wyu2QNN4Fj2KtzTqTUtqhwxH\ntFeUW9zwcM5uLDpcDmjxVG5qopm81GTAWvu0ysREItmEyxFPNumuBeVKx2oD\ncS2e\r\n=acQC\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGIoeB6wqOHSZ9pp7NvyIofirQX8ir/z8IIDkgFiBxVMAiEAiGNqWZw7rVHm37VeJkfJcRLkhFtbxJXPok8MwkdQI9Y="}]}, "engines": {"node": ">=8"}}, "3.14.1": {"name": "sucrase", "version": "3.14.1", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@types/yargs-parser": "^13.1.0", "@typescript-eslint/parser": "^2.6.1", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^1.19.1", "sucrase": "^3.14.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^16.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-f6aomLv8u9kBfJvO06a93kYBvdYg0WxlrrbOCN31FI/hOzAvZMS9WFPj77hT2EMWsrPyxE1TdepkKiOarRlX/g==", "shasum": "ae948ced2887696637606f8d8f405e9ac9b6936c", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.14.1.tgz", "fileCount": 178, "unpackedSize": 939817, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewcAFCRA9TVsSAnZWagAAt74P+gKxiNFfAgo3L2qTkhO0\nwWv5zd5kTVXH2ecHdnzGn7w1ZvLwy+MkoGXvdvv9xMG2e16BR4pO49ape3oA\ncEy4SpgrKybp1qIlKsyUTbGUy2+jYa+ZLdzKlnVAyR4aAFLRU2CTpPoaDAkx\nmi/aeTglHTnUzPxyFPeqZhZM27cm1EEj5fjgivHl8BvdGxFnZujygVs1PZRZ\n2frDUT+COp4Fi3kuOknUNwQfBZoD/k6I33BhzvZI9OIkZzGtISRCx9dd3ZZe\nJfDM0kIRDv2qdfBOPi0Jn73wZLEEVBLpLgQ2gwexNHMqDXTWsImTGHVPIYMD\nIJihTPjw30DfyNm6TOmaFngeOujOgODp6EI2NkZdNEMLLKtuVq/KsuCbGJ2y\nZVp243sU1JNeWDx389yVJFryPAcodyAcEW+cc2OxklxUupiaxOFx7MEBfoTq\n0mXuwRg9HI3qeOSix1wZi8V+zQEy6KkBufsAWRxNJk2lJbFRhXZcZx27CE92\n5BRNgFxJQki3xicRbfy6l9mdqZP0UtjT5zLtssX1snTwkltAMY4nW57cH1xB\nHUSQq9SCk53zLP2DZTyzn5+gmBWV3AvICaOSTa1sKOogRQZHTDWeSaEIEM5t\niTgE+vT0ifDSRcdGkYvq+g+Nf2z1e9dPUcuXcWwpDkAKgR6li5J1rAju+7Nk\n6G3T\r\n=xVkz\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHAjx8ZOb1jstRZwGnavZs6ZLR1T+3R63ZMP7LCo1GOQAiEAhi4DI7DwvDvKeThzuQXisdIqzi1ueNDb+XLATw1bNsU="}]}, "engines": {"node": ">=8"}}, "3.15.0": {"name": "sucrase", "version": "3.15.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@types/yargs-parser": "^13.1.0", "@typescript-eslint/parser": "^2.33.0", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.14.1", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^16.1.0"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-05TJOUfMgckH7wKqfk/1p4G6q16nIeW/GHQwD44vkT0mQMqqzgfHCwkX3whNmwyOo7nVF0jDLwVu/qOBTtsscw==", "shasum": "78596a78be7264a65b52ed8d873883413ef0220c", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.15.0.tgz", "fileCount": 178, "unpackedSize": 939803, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJewqFlCRA9TVsSAnZWagAA4KwQAJ0kxL9Xad6Vv7fnVd7Z\nPELVjEFRnlBKNgbJ+akAjPQzX92U5aLRox9Ju9X78khzJzKlsCkhbqIV5SiQ\nu5P4NmTJmuVUUp1gLf4t3ZuJjzJfyDY76WIafLE3oAKbg/D9oBa7vygdxJVG\nmho0AjAQ8Hmwh/4XrAo2tE/2BqMJ0b25qsQdWJ2NQBbPykuPNlvOvM4yQZGe\neOSBRf2lznFFQYHhh7Ij3iONpQotC+Vj0kpK74DAN1uZs95Ec65gmRgfRCnJ\n0kSycTaTnZqOzRD+jWvZlFyDsvXTaOos0mgNOGgcgXHOe8VKwomiGkU711kJ\n7ewh3Q2wYTiZnhB2Yak17wHAkVV6vv1ADetFQXMgfwRvQrNUYIuAGh1qW9XI\n+p/Z1HiesbiKYyb7tHZ56+MXyZvUJr5jWYQje2MSF9ucX1h7GBmr3GRA35pA\n2boZIT8oJSoJoq6yNpIav2aIm543QFcNpJ4zwwGWam5hiRs/oHMwTTaVUZxu\n+GlPQX8NjJ3kXMymrGsyX99tuhMQogBoUknDeLdVzdLGXDO3sMzdtj1ApfkF\n60ae79bFrbKyv1abpAL2dvW0jpykVqkExiAhYt1qVf7REDtv3+fHerr2irj6\nNvWlqC2CAG56wDTLRLx4QhIwIRSTd3JEXZL0GP9zp1vvyoiuizy+hpjLfn+Z\nPtaJ\r\n=Ky9h\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA09eMk/2zzcI5/mQz7Rbj/AwsGKNiGLNSxs0+1BAuhEAiA+X/ECmsme0ZguYQ7y7dGsIRghDTNYbXtCOaeITp9OOQ=="}]}, "engines": {"node": ">=8"}}, "3.16.0": {"name": "sucrase", "version": "3.16.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/parser": "^2.33.0", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.15.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^19.0.4"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-ovVuswxV5TayCPXfTk8bgBgk6uNRvsinIkEpq0J6zS1xXCx5N/LLGcbsKdRhqn/ToZylMX6+yXaR1LSn1I42Pg==", "shasum": "19b5b886ccca270dd5ca12ff060eeaf0b599735f", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.16.0.tgz", "fileCount": 178, "unpackedSize": 944793, "npm-signature": "-----BEG<PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfhRlrCRA9TVsSAnZWagAAiVIP/i4b84bM/Z2MsYgy8Sol\nBbgZadAvkv0s4+gKNCNEoX4bn1tFqxh6OzsNUaPwaLzmA7X0NthyEmBv3OGW\nAACfDfe7T4dwcKalD8+2dAxcNB3GFNdFsxmzKd1RM2YFZ7AYB5AhS+7Hr73q\nrEL8lMdGDKDx2C8U3rF+rxrxb9H5+1whYCBCcswFwUXH+wbMnk7ycHvllDKF\nKqZ3DM9g1JC9OXtMeZsDmyV5ri2rW7DKnTRpINh9T8wWQihxiEAZu0bexNaa\nxBkJqQqwhzDc9CEseevptDRv0hbXrD06QX5lLiCliB2abQWd2bBMFmRFbIx9\nvxuegJKKFOowzJOMKkUUulZp8xK+1nGRO5aFNOmivtTcMuiVk7sC8iAYUv5L\nm0EPe+ENxzOfD23OgGcyZNpRWcjUAFAjCcvI274Pmzlhqk/CgmC5VQhnX2ay\na47BWhyaoEaiXqLYMj0VuxBDVS+5Kz9qtiqsMSZ/ceomuKQkCGjFFuX+kb1w\n2JPCNIIuP4NYbbXoO3A6qDX6RKtDja1lv9JFND1DT7XEMFc9EiyJdSvkTajz\nPqkYZdAZUlnEsL7njtsy1K1dwfZbyEg4xvNLrl6eD/vk4ra+yel9omVJ3Xgi\n5WAj7eWCt5PY2PyWOnaDyTYKtS14qM6UD8bssra0E9hh4Jheg0qs/S+s3ZLa\n4mwv\r\n=j9Bm\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCICK1MIKaeBWlT/EPFTwEIKeKFt+U++PR3qaLI46wxarRAiAqBhu/48ptNxArSY+WABOhUCSixNf2FmqPYbMavJJmhQ=="}]}, "engines": {"node": ">=8"}}, "3.17.0": {"name": "sucrase", "version": "3.17.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/cli": "^7.7.0", "@babel/core": "^7.7.2", "@babel/plugin-proposal-class-properties": "^7.7.0", "@babel/plugin-proposal-object-rest-spread": "^7.6.2", "@babel/plugin-transform-modules-commonjs": "^7.7.0", "@babel/preset-flow": "^7.0.0", "@babel/preset-react": "^7.7.0", "@babel/preset-typescript": "^7.7.2", "@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/parser": "^2.33.0", "chalk": "2.4.1", "codecov": "^3.6.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-typescript": "^0.14.0", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.16.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "tslint": "^5.20.1", "typescript": "^3.7.2", "typescript-tslint-plugin": "^0.5.4", "yargs-parser": "^19.0.4"}, "bin": {"sucrase": "./bin/sucrase", "sucrase-node": "./bin/sucrase-node"}, "dist": {"integrity": "sha512-wtiqaokYRjFSSrv8fQu7pThKTIZSLwiffW+PHQG52hlI8eJO47v1tXbKt6fYb8Z1kCyuCkNH9etpTUebb7g+pA==", "shasum": "d9fe5d7e359d884cdb31130358fbdfc18bfb4c24", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.17.0.tgz", "fileCount": 178, "unpackedSize": 950573, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf7B06CRA9TVsSAnZWagAA4dYP/1Ia79U2bg1c8mQ2xptS\n+Iyrb8fKWE5KrSBDNpwhfis+ChSZqHEr87yCkaKhQpjakTyn5lFeKSe/Bc58\nM5aBfP1pWSHrGD7UPsfVKA1zujq/SVOmueJhMOb/Wa43DT837hWZ9lOjrxoL\nb2SWNNDfBeYi6W3ZtcTxA3H8P8fSh90TRpQM5EB5DNwa48/hfgyty0sZ90nd\nXEL8ps7P5kLM94ndbJ2bPA8JJXi8X3B32PuIxQBv0pMcbCIlxbkrwh9svYrC\n0zrfaAkNjYqVLCDuBmUP53vqzLiLWlLqNISMWDhS6Co70u2k8K5NkqKwossv\npAX/mWqVuxJh5A843G3r7BHGZvDr01L/oNMR0v6Y+H62tbS903VVV4S/jGpz\nokIkQ1DSpOullGpguXXcw4WkuTMaZELDNNH7/+egTatFgLRJiAk9IrbVh+vy\nFTvjuZCkVBGSDpAaQ2DadqegHWCa+e24YWoBbl7zotURKN9ZK/GYf3LN2voT\nXEIMqQtTQIackaYreCC6ykEFMcVhjRyzuMCducO5NfrDm2MRqiNPmKvQd33F\nIdadLwDxflA78XYweDu9dJ9rNZo8ozzRYMly80cOAS0jorFh1OcljxO4XdvZ\n23bjheEvHJRGPUDF0WVxMG3C1vqivKvSkJF7zulSEDosbxvW3gl6pZTXaQSB\ngaml\r\n=wqWr\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCn+R0fw9RDNmnSH9ZV+kuUFpv8RDE1n9BLa2cKfgu1VwIhAOxrvh8Uu2dmOnIMYxpT5qq9hj8qJ1/CQjSFcWW4XcMo"}]}, "engines": {"node": ">=8"}}, "3.17.1": {"name": "sucrase", "version": "3.17.1", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.17.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-04cNLFAhS4NBG2Z/MTkLY6HdoBsqErv3wCncymFlfFtnpMthurlWYML2RlID4M2BbiJSu1eZdQnE8Lcz4PCe2g==", "shasum": "b5e35ca7d99db2cc82b3e942934c3746b41ff8e2", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.17.1.tgz", "fileCount": 178, "unpackedSize": 950969, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgF0Q1CRA9TVsSAnZWagAAqFUP/3HHw4e5Yl66VkEr1pRK\nMgxSatZLbFx/ANKXW3vXFGY4MakQXlmQ4CVOHCWmXi2rSCIZHH5xV+oFeboB\niyYMwnBCsvwJanRQrqOxAhvOvDQzcEOfk3egaJWHFacUP85AipXXxX7b9OVU\nukqji07JpbU6oXRSSeRwMFL1HNfgjZEZhjdvo+jA+XoJuBN7qLvYm4may02i\nxzeo951noxmFbM0vFC9DZEywyEvRKt7qRVKZ4wQb3gRe2wyvzAIednJDnvL9\n9bdtIbA5ILVsJhZYi6Uj2UofXzEZ8YhTbnynvwZrytB94RKEpxCegfM3+KW1\n87qVoGSF2Q3xRTppqFy119PbUWmLmjsetz2vZJ5iWXlMiskLwqPlNjm18IRk\n1sTmW1W0zOpRQejb3r1mXtINTp8V06XTy++o48B7aRsfN6aaJBGSpMZUK0Cv\nrb7By7UcLV1kIKAmPVUWowanaEqBQ7KK7CuBiCwwGCllbeCB5dDJttjid8wQ\n3wjKmFWyG3R/pkGl5lG+Sqn953+h/cWiQh4hxP+lv/s7Mfsaq6uHf9taotbn\nfSl8AIdB1GdWFZsx//OsQ6sORcgEaHd4CBpRuTPCtE995I74OUWCS2fnRIDt\nI2J3Y64JMLLaLPt9TdE6OyZVxvsFSZzc+1OfP91Hjal4e0QjJBk1befgKKel\nOXBe\r\n=jDkS\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIHgrcw71w2QxWpxM45aoC3pPzKBlLnOMGwGekGKQrwDKAiAyTL2VQJ9U690Of3Xc1ohcuOoiESzHd1BM7Im9O8C1bQ=="}]}, "engines": {"node": ">=8"}}, "3.18.0": {"name": "sucrase", "version": "3.18.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.17.1", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-6R3iX5pwyAFMLsM2kU4Rhc7EwODCTee8ySWM9UwqARDClO/QdQDVAWaioElx0o7u9w/76Xulz5Q1jseBp+YuRw==", "shasum": "66b2481e0458ecb74a5a4a644f45fe1bb33235f0", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.18.0.tgz", "fileCount": 181, "unpackedSize": 965437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc32rCRA9TVsSAnZWagAADdcQAIhcqVTibKm6W7ibVFDv\n+EwNeGZ6rroxeXFruLbrA7igm2hIM+rSBqHtdfu5BjxmIFcAwqKwIrF8Lm0I\nihdV7HLk0a/Kr4JCgUNEBeUdRc7XCCrTTdNEEZY/vvWx9/6bo4R5kbPj9wAb\n16xStqPlzV6aKvfNDQqUcPlJGz9G9YQScDdaqEBD+oOOAwh3z4KrC3cDbYxF\nptEgm8ShAeoyLw4zfvIc8j3kNV9hlmftMCIICg7L8ygij1h+b6nQzuuioR/A\npSuQyhSkgrwAoMlJYFleL0cco0As1oyR2Xso3lugkeSW9LABHq+fk2tbu8mT\nqF7YiWyCeput3x/c74qEeGzEykes//HTb7VFQfiMTZnrHqAx+f73cHM01hEw\nTZanAqUu2rcDA30ZSM8HD57ovmtOiK1iravCyB2D+I24y2WE0OowpZfVWG4J\ni9HIT+y9bqgc6Ohu9uL+RWcdyc8nyKkKMZpOY2SBPQs2lfl2lG+1LxuFhmJu\nX2fLmC+MmUKyL6wCCpul+QHcJ6ubAVdjZ33vFk6Udctcs67a8nGiyO4YSicJ\n88z70LBiQidayXE5O98pyah15T7EsCQYoXBgRKQBfF7iwwFrOoGoujygRVdE\nwVGDrnh/B50te8bU+k+rsZoGIaGv1tz7Ct6Eju4c8v7uXh1qncR0oOa/ZngW\nksXj\r\n=bdjn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDwVRsf5BEB+HKCpBq/GuYwA1i86Py0IgNvllYbEYghaAIgEZ9VUHWR1YVHsZq5im1XZ04K2ch9FpIrbBjo+U9E6ss="}]}, "engines": {"node": ">=8"}}, "3.18.1": {"name": "sucrase", "version": "3.18.1", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.18.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-TRyO38wwOPhLLlM8QLOG3TgMj0FKk+arlTrS9pRAanF8cAcHvgRPKIYWGO25mPSp/Rj87zMMTjFfkqIZGI6ZdA==", "shasum": "7c699d5148734b1105542ca4ea2aa69bcab7f728", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.18.1.tgz", "fileCount": 181, "unpackedSize": 965388, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgc/EmCRA9TVsSAnZWagAA3pkP/3qRZwpsXodZ9RTe99KB\nn2tnCFulhcxCFLBEdZYua8QB0AT92FVzw+o5lXewAO9+McEUlIeklrxhoXZO\ny3QlMTzAOhs91OSMuhFFh2i4liSYA0E5QhL6RtZV5ksGf6URSo0I5DB4cDxB\neSI8rsxqf3atRuIHQhwchB3a8jHvYaS07xMvFTfdUBvbhLSYeeWr2szn4ZnE\n5g91/FtqvLW9XFl2Vy4MRagmHlnnTCNJ6by3I+MVFhVnzhsI+bIiu0LM3I+/\nADHlEoQ9gwRSov9Jp1GX/WH2xSOxTZxyzpXlgYGX6NUXxl4k3hUM8GQGrf/2\n3Z8/nHnlNHWAO9FLiexNvTlyikCfyznHpix+9GP5gfXmf0XYxMtwgMBExqhL\nUK5GFawmKi0JcpdIkXZjDIpkFoWoKXXv7CXLqj4DEt3MBoOhhNQtAXUrElaq\naPiKXlwILNgo1Fct698833QSvO/+dQVZLgUyjkgRF/1vXA7CPM8KZgj/qyN+\naXvDiuLaxFDunbpBy2dLZgrgyaIllQnWgjnemey+AsQUMXh79o5LfvsiyWqx\nvaBTnuExa28L/IXcI6zSOArh8mTK2ZQpWZDP43TpBXWFQzOqQdOSHwjbger2\nPXIqRBS/m8I22dEY3MQ0/6TOCHp43EFTYqXFKyieYqgrLF/hQvZhEEa91p6Z\ntVK5\r\n=Vzrq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQCpiidi/MgMSUGt7qs4LbMWHLMEirLQBU4sqFub8zi8LgIgDjydIo8hWGI30RfEqR+AD0awPMb06zAwB/2uKxxPqvo="}]}, "engines": {"node": ">=8"}}, "3.18.2": {"name": "sucrase", "version": "3.18.2", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.18.1", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-xCFP35OA6uAtBUVB8jPSftiR2Udjh0d9JkQnUOYppILpN4rBSk0yxiy67GVzD3XsFGIB6LlyIfhCABtwlopMSw==", "shasum": "d9f16f1dd4f91e0293ad6f692867772eda301e4b", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.18.2.tgz", "fileCount": 181, "unpackedSize": 980384, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgvkkoCRA9TVsSAnZWagAA9uUP/2vycyVcKmSvryV3eZrg\nPHZH4chCiiUBNs508sij38kg8wNNtTqUU2lxH+ctjro9X/qYfJLfyLspWkNc\nylrJZE4lvz1wLfFrIg3Ghq8ikr0qtqJwqkUektbX1t0uOb/cq3rwy5syq0MZ\n3dksVr2DIv9I3EJuqyGTx9qfY3izJiEIzQwu5CfARC4bkmKS5s3FWCE1LxJR\n/SQrLKecbhsh8X+Eu79xItybXPSGLfv/fEyGAap5EtopLczqNYSIeu298dce\n5BayWbJ0S0cf3Nxze5HJ6AsHAfylLUFv88IczDfPSg0CNkqYoaU6feCwse3T\nIdenAN+/uRtF0GnBlYggBOpzNHOqeDdaAmSbfFAIhrLffUV/Gr3keI0x9zd/\nyBSVvuQaIV9SgAb3iSIJT4vhJw2a9kV26y2RU6NyLylvlIC/TMDZUZjLYRTo\nm6C3AGxwpp+h17RFJIy+ATNWGVPOfFI3y/Zg42Ub0ZJM2Hn0nD2l5RVWWO2n\neqyfaS7iyTAntqwEFyLeZfiQpcJNzYlFXEBAwR4hoC3Rx900DM6jBK98l3IT\nl6jvCQak3fbaic1MWRzs9vR/Lt6rHq/bPeBoZWz/z7xaDVaMxcfL1JJZFujg\nWSwqTByvkCzH6ChS4Vxy+f1ZbdVUhtUMCrAHVGVVlPZbPIZAfuDrVct462Jc\nB2/1\r\n=Iunh\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBW0OjZ1eg2MF3otyFxZHbq4QipCFM1d71HLt8mSKWldAiEA76UNtrovrfo+aH/w5HHRJJdzN9WDsUVVmaRgakaCgCo="}]}, "engines": {"node": ">=8"}}, "3.19.0": {"name": "sucrase", "version": "3.19.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.18.2", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-FeMelydANPRMiOo/lxbf7NxN8bQmMVBQmKOa69BifwVhteMJzRoJNHaVBoCYmE/kpnx6VPg9ckaLumwtuAzmEA==", "shasum": "cc9a60f731e7497766a7b710d3362260a8f9ced5", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.19.0.tgz", "fileCount": 181, "unpackedSize": 985402, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg02bZCRA9TVsSAnZWagAARQoP/jfiZVmqwQea2h+7ldZ0\nbWZHEev6ykIqVBf7+KvWAVHh1t9dqeQu+VhmTjRP1BDanwnX/Vbizn+lNli9\nNjM0lfrwedDdwhdMzabpt71kpQmC5Q+beqoPgqzlqnIxXsWZp7xi6O9qkBNR\nQOmIb6yIpmbSIDcxhyPC+9lKgtUEX3qULgY1K5zAsplBZ75WgWNX2/wsiEH+\n2Mzkz6vWga4Wgu4gfaHxupS3SNQq/37XAcW38NLRPUjid1cyHG9zQ6x78Z/G\nGiQLDtcLiqFrW+ySeoP+r9Pzc7JSJJL+2nPS02T6pbZq3smQY1Ken067e89f\nA7+t9hg7VbWXP+nwLnifszbIfr77RfCBI0eqKldN4eiAuG5CO+konnMPqEgk\ndB/tiiZvJrCSF75dSkMhsULSgmtc0LmibLEnk4ALoPylUaUxvjxOYQxl3ZvZ\nkTXqoqE/z91CQnziKDLFEFpvNcTQhcEBWOPHFUsDtegE39eQ6nbc6TbLGqSl\nyFBVNrfCb18syMSa0onkUwJ93jOHVvgrwjocC2NkTqq7fdXyhGTpF+ppBSYl\nLqJmhaoXEQ3De7lrcc3AqtjZ+vxD18QL/of0netRviS4auMuz14kMjacvbHU\nywwcBtM4wFUrH7ggHT5tnooLjzMWc7WI9Xql8lVPoTxmmM8eCqQ7Nr2I4NXr\nHfZ4\r\n=xTxF\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC0LOxGBYPWcwXZcNUJll2z/wkbS3CpS4fMlG8UjArF+AIgO1olHM7jkl+iGAN3F+jr1sLHBWcJ4CeTkOoTdDNrprw="}]}, "engines": {"node": ">=8"}}, "3.20.0": {"name": "sucrase", "version": "3.20.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.19.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-Rsp+BX7DRuCleJvBAHN7gQ3ddk7U0rJev19XlIBF6dAq9vX4Tr5mHk4E7+ig/I7BM3DLYotCmm20lfBElT2XtQ==", "shasum": "a80e865830e27d66a912c938491d474164b06205", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.20.0.tgz", "fileCount": 180, "unpackedSize": 983463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5VteCRA9TVsSAnZWagAAejMP/3BkwecaPWoz1szOPSkg\nHtwe4TcfYdagUWi0aMap/2fIUucWpy/efLGbAgcRK4Rs+UXLeCgLvEw+/wfF\nxjm70Xdkpf80mVOQBN85V0Gva4D2ffXryj4RvcdJGjKAn29lP9M0hzyQy83U\nHJXOS9wqwuumKRRZqu0Cem1xGANxqlAsNAhfr19tfJYHADS1M/KtiZqI4ah4\nOhPIO72lopH+5FJ98+J2aSQzNC1h0fwo7LVrkFGAjOiL5HtsYv6DgOCgZ34w\nYDv59zocdJWKvwlcbWtmxU6Pf5mBImMHsTP453ZlOy9Zj80h/7yay/VyfiiT\n9XZCgaD3lcqNk21ZN8tt97AJDBzf+Ssk5ax6Y4Ydxlc5dw2/AfViMtJ6lW3T\nZ1iuazzq8SCYdsMF61LbU/Mnovf74w+SotK/dHf/GB0o0f1zBU5bRB/Tpwkn\nM+gU+J3SUzF9lUOSFgENOOSzfAJpZ2f0jQ348yRIXlp0BsOk8/ac2AReslYu\nbFaKzh9344jp1L0Bq9k4MsFHT7Ram5IFh/VrdrnhnSaeO3HEKc+ghiUhkKpQ\nrpUOp4ue0OJhRDucjZdrlVfrPovIRVm6cc/YjBIneNkRYKfznKYWtTNupnpl\nAGgOvDROhOtAdLpQLRuxlLB3x+iXtQ6dhn5y/svns8pjXCIBnsHcJN4UQ1GH\nmzAk\r\n=vAtL\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG+8N+Yj6ZEsgXslT95NgyLMx2DG3Ay6ekmJA01rsbVfAiAq+/9zarHKoCKme4T9OQWuPIEOZhZTMZwayWyIt2iwzQ=="}]}, "engines": {"node": ">=8"}}, "3.20.1": {"name": "sucrase", "version": "3.20.1", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.20.0", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-BIG59HaJOxNct9Va6KvT5yzBA/rcMGetzvZyTx0ZdCcspIbpJTPS64zuAfYlJuOj+3WaI5JOdA+F0bJQQi8ZiQ==", "shasum": "1c055e97d0fab2f9857f02461364075b3a4ab226", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.20.1.tgz", "fileCount": 180, "unpackedSize": 983649, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhEOnACRA9TVsSAnZWagAAaSwQAJYbR63+AFQknhnNMkJg\nIxaR+18FDmVhH3jppXSADMAARUJPJL92P1wdL8ij2QHeLMEvx3Ek1k8+xhAq\nUH5TEH0pFf0XLkKIch/EAjU/u6he0xKZTEsB7An5aVTSVj6LyRU7rnnzgLjM\ng/CX088gW/IsEN+4Fq2NRJe5fpdp5xDajd1eDrGle7JgVrSdCk6uCIK9p3Pr\nshdkQi2J4U+1AT7wzKu2erXobhQZE2I05sgRsb4QWBvf6d+bjvQHSNMIY/m8\ndby3WkeiXo+q0DMGimmOZq4x2J9zkE13Cs4PR8ocTUuoGLB7850DZtPmfygt\nlVcKkSMoyM7r66wMxL6iMHdQPtoz3cgmz5OoY3lMuVgStA/sniBkoSr8RkDB\nqvOZoQNe01gGxTNb0sdQ2ilxjlPwVgXaAmkDiy6+IG134dYnoeK4Nf0DmrbI\nqT23+J4ey/ww8A53ch4T24A6zFmzt7Qk8aFevos9RGSSImLP/sBqh4VCfDQs\nPymQsme6zXg4hIcfmO1bVBfAClVsq/fHFlZAI/1IuAjbrOle/U7IJPjeRGZJ\nQaZ1d5NRK1NRJCl5lssRtrspAYshD9k+Az6T1xtDaSmfU0oXvac1u0PJdmo8\nDNRAKiQeoaMyhlTewAyZMEzyEyYxaKuUPJhW5sUOocu+vAsrs0Qbqtbu7jn/\nbvav\r\n=4Tc5\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIGZX/Rxo+nu5NJbLxjkdTEgdcI81+gp540YoAr29bVDlAiEAuTiEec81YlXa89/U/SuXhy84NZvRQkexl7Q89tcf7Bc="}]}, "engines": {"node": ">=8"}}, "3.20.2": {"name": "sucrase", "version": "3.20.2", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.20.1", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-EdJ5M6VEvToIZwIWiZ71cxe4CklDRG8PdSjUSst+BZCUGlaEhnrdQo/LOXsuq3MjWRbfepg1XTffClK0Tmo0HQ==", "shasum": "28a28dc58a55be0d6916d5c9b2440d203e9ffe62", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.20.2.tgz", "fileCount": 180, "unpackedSize": 984225, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCm8IG4yvpA1ooQ3uAjQFILLftn57u2DfxdTbAAT1OyBwIhALjLrgYC4L03Lf6RBQ2QVVofYmsNOEG7LE6vF+m8lMsU"}]}, "engines": {"node": ">=8"}}, "3.20.3": {"name": "sucrase", "version": "3.20.3", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.20.2", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-azqwq0/Bs6RzLAdb4dXxsCgMtAaD2hzmUr4UhSfsxO46JFPAwMnnb441B/qsudZiS6Ylea3JXZe3Q497lsgXzQ==", "shasum": "424f1e75b77f955724b06060f1ae708f5f0935cf", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.20.3.tgz", "fileCount": 180, "unpackedSize": 985440, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh28V5CRA9TVsSAnZWagAAMyIP/izT1csoGy48xqSegahC\n52bHd/+2K5ulNPEd+pyT9w1Kf+H3qNm6cwdGS4SHZsOiWMvZivQKoRb5yZKS\n0mXk8SRK8/o/Zw9R4ocCFeguFIjnoq6lI8lqzY7Kw+Tyk5YBlCePVKbLPJDV\nWeLkNEDGqH0TGTzSw5hy9L6eGyQpQ77DxhtJH2+JlXy2TvbNN9a1R/O86P7Y\nRSPZQSILlRCsde9oEALsc0cswOfLvUym4z+IL6Mu6c7QjBESOWB/wwzjTNMG\nGzLP8WMMoqc4+/R5QzhE7gqzID0jHRjQCYH/T4Rh0xVmf6svTcdYrhgSZKS+\n+y0QFYnyhg3OBjAaJp+qzeVfGONHr0Fjyk1sbcxWuBQoGZ4405o8mbT5MqeK\nbZC5Wkbr5bmIp3dcCk14ZLK0Dgoq6IgJWmwQf7+RfKvD4TVQeYbLcN16TdUJ\n/HAxNu9+SJ9SvDQif+lTPDwUgXjii3+Ukoj6izChKF/prXHwLtpfnM/Wzusb\n1I3G+ZD+Ojo3J//Z2dQC5JAhFG+rqeHJSqd7TVxvIoCzQd/b3nfn1o6ZNeO0\n2alZYcYqp7mr1d6ajmSE3yHcvqHUNFIguhaQdTqREI1CBMOyigBryZyF9yQU\nQ60Ud9qE91nGUTQ3OC/5X6LP1UhsuelNqEloDwsEpliBvTKlCgpeC1qUAbW2\nBXcX\r\n=Lhjf\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQC4n1xdfSyQhmuancEaoryFkOacM+4Xpi0bZfAPbQPNtQIgV9NwW7uRMQyh1N6oT7QYIn/VwE8rhdoaouv5fpGj2i8="}]}, "engines": {"node": ">=8"}}, "3.21.0": {"name": "sucrase", "version": "3.21.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "7.1.1", "@types/mocha": "^5.2.7", "@types/mz": "^0.0.32", "@types/node": "^12.12.7", "@typescript-eslint/eslint-plugin": "^4.11.1", "@typescript-eslint/parser": "^4.11.1", "chalk": "2.4.1", "codecov": "^3.8.1", "eslint": "^6.6.0", "eslint-config-airbnb-base": "^14.0.0", "eslint-config-prettier": "^6.5.0", "eslint-plugin-import": "^2.22.1", "eslint-plugin-prettier": "^3.1.1", "mocha": "^7.1.1", "nyc": "^14.1.1", "prettier": "^2.0.5", "sucrase": "^3.20.3", "test262-harness": "^6.5.0", "ts-interface-builder": "^0.2.1", "typescript": "^4.1.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-FjAhMJjDcifARI7bZej0Bi1yekjWQHoEvWIXhLPwDhC6O4iZ5PtGb86WV56riW87hzpgB13wwBKO9vKAiWu5VQ==", "shasum": "6a5affdbe716b22e4dc99c57d366ad0d216444b9", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.21.0.tgz", "fileCount": 180, "unpackedSize": 985367, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIHVMBiIZWjqO6BbNApN6nqFh+HIsUVrmRMHxlMRrB86zAiEA1/o79t0zBVUO4exlNuRolf9cBeO0jud9M3aW/HUfOiw="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiTb1rACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqZ8RAAlJamN7zzZSCsTRn33HMAc95hniInpW9ATDoWEkhpF+z2yxSQ\r\ni0cR8VZaV57eNkXySqUd1+8TgVbbLH/ITm+gEDiC0+RPvP0TeqoXKYb/0f31\r\na/g5XROnR9uW01Uar9oWu3GXm//VFntV7k5wjC0HTEq6IN3M3OvHfPM2Jfyv\r\nikBn3q449T+ihA9XdI7X1Or16C6Igh5IfBSd6vmYJY6rM7YgR61EJoN318Z5\r\n+CFoMZ4Ox/gxr+7BdcSL6rDNMe16zMPX3EoGGpBgv439J2OPkrPm+9zUHWD5\r\n2gNtMzxVWnX5pyZsu48muxQ0NxAi03v3VTWGX8wkOtMBu7XbKDR7Jl24jpPX\r\nWHBgS4aAPLmvWgbAyuwqV6v2lIXh+5QuIYB1DgUG9opEFEGxmDdCgvf1IGWV\r\nNbYDq4ErnISKNOWCCuI5a9YQZXlF4sWeFg5o+8R2bt6fpIrmeZZNAAwkJdqo\r\nlHxkGtDDdQXGk+YO4yHae8mXtEF67gU0aaoCjSyNQiR0ODjUzyqjOmFz4Q8E\r\nBSmkQgg8z4EOQDccLrkbmXupI7bMLjWrWxmSc26ysN69cOscSX1KWs9GHw85\r\nb8GV7In2oodk/+IvIxHe61ZmVjaCS/QlCCUdjQ8EbkNeOjvWUg2qY7xglxaT\r\n3a2zjWI7sEpZkj+xZma9t/J7c6Nazbmm08k=\r\n=AGkB\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.21.1": {"name": "sucrase", "version": "3.21.1", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.21.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-kxXnC9yZEav5USAu8gooZID9Ph3xqwdJxZoh+WbOWQZHTB7CHj3ANwENVMZ6mAZ9k7UtJtFxvQD9R03q3yU2YQ==", "shasum": "7e29ddaca012764cf843280b00e74a843bdf790f", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.21.1.tgz", "fileCount": 180, "unpackedSize": 985573, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFSIK/jUZGmFVzcUXX0QdSKEb/1gEVfJSOsDQkGgGvsgAiBavFEEcirRsdscShCJ6Njq2LqsnVF46+MKv+dvSK43AA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisldkACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrG0Q/7B/68C81Z1UxLSYctoji7h1WeEEyj9IhknRTS6K4PzRaP5El1\r\nkRREcKCL22RdEyOZMMHR69vDwyhlcwuoLuM1rUItFGwAsS45Nsdq4vDTPZEl\r\n1Mg+VeH1OczIeSJ7LkDYAQeiLHEEppyKY+xim7D2yPtCCbGyHFwRBddoLY/1\r\nt1hfB7zt/UU/Ee3yN+OH7aw8Q/UMY6PAt3yuWDRTa4gGtC3wOkKc5ZI7VAK4\r\nvWRYpggMHK3MmWtOWsEHSUnR5B3uG5HxDE/xr+wNNTVOZe36vySQywziDBv2\r\n2MXxVIXxnxiEzpVXSJc9miDGAAatZqVxhYPv/MAfI5lYsAV330sKKjI6qCuz\r\nzGBbVn/W7b7cuGZfbCF7JCUxHdsXdb9MPlahhOxXGFmV1tVO7XIF5frCSExY\r\nqee6QTDxM9spHrFJiveRDwbndbx1sZLYIysFehEZfNHZwetns724tjE4DaYy\r\neDZc4fxfWw/Cyl5f1Lb6haEslJeoysYD+0NwoYHMAzW80++35Y2K6llXZVJU\r\n0gKrB+c7kn8cXye4/EbNCWbOojWTricSv+pttZrcd0OGGrnKLuENsfYsfBgG\r\nC4AiN1boof3f92f5WLQT6EAdoUjrPi3+4QBifLAKoVOrQK97EfV7Ro1lyEtQ\r\nMuBF6VbnditRkioWAAqPIl6+6OHUCcHcXNk=\r\n=vQ1j\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.22.0": {"name": "sucrase", "version": "3.22.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.21.1", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-RZeE0UPxCjf99p4c9Sb27qRbsuZBd7TViR/q1P6TsUPYa/H2LIkaCPpio6F1nQksrynYA78KEBUnpxswZiPYcg==", "shasum": "9fd6d12b93c758a65c3b99cc3e4436274ea79d55", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.22.0.tgz", "fileCount": 180, "unpackedSize": 1006331, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIDMRpSzPAQIvvowAbxAl5j638Ptr2EMMaECZqfQ2IGKCAiApt1KZTtGJr0fUWBpF8kLAFT150nc6ysFUOflsRSJzmQ=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiug9oACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpi0RAAig3UJxZ5pMhr1iylY6VkPHOrvYk3+EZ16mDvLZsbabKBDl8S\r\n5zI6FWN4iKr+sHdfh0WpIcuT34OgeoKtOIKytGVNz2bkhZa8h3DtEGK441s+\r\nTeghY7zhWdMT/TIcZt7gtohTCcGmTOGcsSXn7KwsQ58THXDYAU41FEyWRMB+\r\ne50z2B4yNh4z9BUxS9d6Tnj1vL0iPBGWAXZ++oSe56RDxedZhwC40GWesXsD\r\n4SbZRzoOakbAqbMagyYhAMSKf6cvA8x0F0Db2irLrrxLXTEtCjUtI+vOBvx0\r\nJB+9+hQYZtws9Ea66tv1Zo4eBDcXJMfrursW3QS3ZMXiR1bEbkGiYrfa8uXY\r\nB5pzxvze8LwIfwvegpLl9vGQdDTo6FPuFDP2i9XeXS2gqRTzanZoTLB8AN9M\r\n+y2ZFP+sVTsGUq8rkk4NF3pM9GvsTmUYzUYVPCJCD1KHJNm0mbIjFO9WJTKV\r\nFEw/Hc3bQwKcufb5mQUgNAx817h0SxBpiEkKktuOSiSnGo6b8F18oPvQCLmE\r\nW9CU0EXIP5ZkshPVzd6qJ3P8BY8SJ7AFC0f42FDwEgazYWsE8dP7GljEkhps\r\nza9JKEiIMimpE4naexPaai+5flY+edF5uojHfEjT6+6q3hcLScHjBefmw8Pt\r\nWUsQWdsxVoik9O7JDvyBiwWUFqx5t45vJHo=\r\n=4QOQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.23.0": {"name": "sucrase", "version": "3.23.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.22.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-xgC1xboStzGhCnRywlBf/DLmkC+SkdAKqrNCDsxGrzM0phR5oUxoFKiQNrsc2D8wDdAm03iLbSZqjHDddo3IzQ==", "shasum": "2a7fa80a04f055fb2e95d2aead03fec1dba52838", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.23.0.tgz", "fileCount": 183, "unpackedSize": 1015985, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA0OJFGtoGgjg2qkXotvPz7earr1kebvwpcjyxXLMT9cAiAq1nGQc3sVdWOD3nsu3t14yrunrGnwZ7XMkY+wRgupSg=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiv0eEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq6SQ/+Oz6+pQX8P7Z+QxiD22g1tV0+S6HWg0W7YRoIJbHwxR9WBVvT\r\nirmWxaD13NAR0Rhq7IVEOr1L31SLHXkQw0PYyz7nhwGS6+IqX1FZZBZpJCb5\r\nZ5fiNSOB4zxbZ+1DjhTDpoyoQ2aOhuKwopnE5112DHA4ZSqj//EFMe6CdUHa\r\ng1OdA3w4V1HD2fWTA33bU4+6SgkEhSOmkY9zkKgZXP1yJrdpYMuiFrxKTRus\r\nhddzKQ1/bXW+KbOW00+ww5pYwaiRAt2ZhOMDdkBb9YV8Kdzh9HweB52ZsXRF\r\neHILWccaebWMUKXeEV5vo+doYCyHlgvAp2+pYBiPtf4uXHMQlSlqO4zdspBG\r\nN9Pka5M2fZ538/OVRTtUKY6eDAOR8XZ8Ksd04Sbdg7YodUB+9jBTXhzxFmFU\r\nOwj2Kr9osA9qsVtwECe/zjtbTSfzZZ7XjprV5YCu0fKQaTdMZSxY8tgzrNw3\r\neYbEHZtglQZZbBw8GJDW35Ws+m1IZCRb3cYAgziKkRwUrUSFb41kKeJT1CaE\r\nVDE6XPK0g4kup/yFewW90Um0UEh12M862FhnNWUvr6UqWHjGKE/J1Q2yEzmE\r\n5+R2ANurs2t7hqIcU1vUyJpRGYA77ickNq5rrBq4IpPWFP/DdNDpuon/3J9c\r\ngivR6d5HGhddQjG0ckUKTfJo6+SB+uTgvxw=\r\n=M7ta\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.24.0": {"name": "sucrase", "version": "3.24.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.23.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-SevqflhW356TKEyWjFHg2e5f3eH+5rzmsMJxrVMDvZIEHh/goYrpzDGA6APEj4ME9MdGm8oNgIzi1eF3c3dDQA==", "shasum": "66a8f2cc845bc441706ce5f3056de283289067b6", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.24.0.tgz", "fileCount": 183, "unpackedSize": 1035350, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCID9VE5HYEob6fRQvjsuDJiiZxoKPmGfoFWP/HdvK06kfAiAgagaoT7CpW55r7h6cPJzxaUyonAPrHhdfbnkuOWg7Lw=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi0H/XACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrHYA/+LWT3wrQixdMQku2lTnjLB+tf3tkU5VG4rCDaLSAoFto/lotP\r\nuwmyEfo0cD0dy3MnTfzMtapSeHGKmUQiE6u4O3aGiJ6o3s1DY7RVkwyE8G/e\r\nPKxhS7NVI5KO8xt03e7JEiU3y3RHN/YlALpScl3IBIEpMOcv/ttIh/fYDIai\r\nGKMv/NZGrvB4TGAqtkV5VAoHxcVzuTvih5Th7CYjGkPHue9PblOYG7Mqq16t\r\nthfw6dpJYHnKoDGjhvrnbDPl1FSOvdpm+6hPnH2g8Re7tPQxgTNUJ6RoeVp8\r\n0L/IhUS1bBguIR4vt/AsceDLq48YSePoFHXzCp7KS5hep1WfrC7id+zMR2A1\r\nqOctYno89hEMZiIytIo+5e/iEeX2MhnRwL/unjdVaaYvqK6wAjqXcVMt/XiZ\r\nQ0e0PKfXdsu18deCi/ENwBfrngdt/n+c5jx4ZnQjKepQgVRwPLiFzG9Mmjw+\r\n5tdi62lXU4/JH6flGfowp82B/xanUWG4ERpWjwzTy+YbK6U8SC4iP3X76ECg\r\nyVZjLdS9N2XeCOrRB/M1wAg74urneLlMR0AmYKJuxk9utKgvMIIOE70b3xSK\r\nIXxskxCrmo65raHSW343kyg9exQ4Jxrwu0bulnFdYNu+Wkzi/0d9IYgVTtGV\r\nWg6L2ditF5sWPT+CZ5dgdvhdamjx4gfAv/8=\r\n=xfrq\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.25.0": {"name": "sucrase", "version": "3.25.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.24.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-WxTtwEYXSmZArPGStGBicyRsg5TBEFhT5b7N+tF+zauImP0Acy+CoUK0/byJ8JNPK/5lbpWIVuFagI4+0l85QQ==", "shasum": "6dffa34e614b3347877507a4380cc4f022b7b7aa", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.25.0.tgz", "fileCount": 183, "unpackedSize": 1040194, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIFbeviqeroj3k7tHP6lD8IU7ouQTEiAlThhEVlJ29Q4CAiAZ+xlYarrvCEAXAyFcbdugiDopSnD8n8+uxsZyk2AxxA=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi6H4xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmokkA/+IIEbUTjHy6efABoa0Q+RzUaXb7UA/HXDUIpfhv5CxGhhNZjO\r\nx7Y89K/dZ94rnz/QJeDVpHxAjFbN7EF5r/EyKg8Xvogujs/sa83mW/PLp7Jh\r\nf8wkE9EO7O2L2n8mBd0BHI2lyiEpVI0iLc5bZPbNq2Cx8eEzXsnV6EQ0w/g8\r\ndYxJ8xknp6wPM/xBNWhO5ycK6jVyx1bfbOeYEbalAG5fpIZMdWlH8z2jqpGe\r\nTNDdR8NGLd884teBC/aeDJj3DWoY/WFjyyeNpix/WMB+XFoh3/UorteSBAvx\r\nOlu++u6zM3RkNVcpnHiZTajkNEJFkFu+TqcKGc66qvFpK+EcP8DrSAooc/GH\r\nwy/SUIjw3MxESzVGeRZZBAjsl+74wKMKFf8996xbypgIz3xCcWwShn63JBeF\r\n6J00tpBDWCQ4IFysAFQW7oT49j5AtSRqCoQywkwZPCp4cxovZ4k2BZZBr/LR\r\nIxbbB567FsO5HLllaGl89t0c2FyxOIhMBlJ1Len016eGDNRzmVTTPgPLJNh/\r\nAaO3/KF+6RCT1CWPkcfc1g2Ul35PgnOCH5uysIKipZQmHK+xqvBhlLRDU6bw\r\nI0PP+2PITBal5PxdK86TIyKKUwnaaU2tAJYzch4vydXB5Ir3g95FBHBHUYIQ\r\nXX4HUp1EJfJvkO4U2OSC9QANMNeY0syAAgk=\r\n=G1GR\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.26.0": {"name": "sucrase", "version": "3.26.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.25.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-iWWppLcRrEwHaHefYJaJP9XQdRJO+tZfy/kDZizar5Ur1IK8XN48nwMFnDupXw2uvNtjWd8I58vVH42inBT/2Q==", "shasum": "a895fb26861dab2b5582d6235ced052d4f27cb68", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.26.0.tgz", "fileCount": 183, "unpackedSize": 1082144, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDADaf0co/2DBWNXXeeQUfwe19riLDLBQCUGwtsskrfqAIhAI4PbpDVjDuOAqY2acFBkNIAx92eckv+i7Lha9YJNfUy"}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIBp9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpK7g/9EhhL6TKgxHXiMkMs7co5Q4heWEDXYxp8W5lANQ64Hs4slG5k\r\nWnn1eAEsTzpm/T/+Ds3qcfNFsb7POJvR63Zpyeqxdw5qArXoQESGqi0ZWoLO\r\nxIxyEoR911Wl1z22NK1/pbXX2h1Nw8Dy/m7+5Yeao83LgUk6Gje9VUwbJv6B\r\npHUFGemAoZETHt0WU9tZBbNSStzBdj01FU0YD0r0Sym/CYes9/tmJia8G0lp\r\noVPheGIfDGYk33vrwcir+kHJ2wUrxYlLEWJ6rvWVHZDCNDx+JryuLkKccmKY\r\nf3Fx7LTxu7cD7zm9TYdReCT6R+JWHKLdpq8LPxMyvIwvtRipdPxGyKGmRglM\r\nL0eXcYBxqXuygI5L9rxriEhYUKqMRC202pguyJyFGZlwjsf7Blr1QgfYXOYu\r\n3L2tfAUStlJhM0/5I7NzwYjFYHVHCbdT/XUn03tZWPVQa9kzYtuyQtpK7QwG\r\nn3ZDQgbMiPoR4PQZ5GNeAt54cxricW3AHQ61bHi8ZyczY6ahROA8ZYb3NbK5\r\nI2FcdCOqBNqHoiMChlDE4IBNfoGchQKstGbnKktjpe+Wj6qwKphKYWq3bqm6\r\n7jmHW3lF1a/JSPnkZXdFGxQBWMGaOs+8xk7R99YvypB6XZlS4lsMIex69jBU\r\nkQpyTdtaSCKRqMVEkXTTDXtBlWUe165n1FI=\r\n=AUOD\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.27.0": {"name": "sucrase", "version": "3.27.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.26.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-IjpEeFzOWCGrB/e2DnPawkFajW6ONFFgs+lQT1+Ts5Z5ZM9gPnxpDh0q8tu7HVLt6IfRiUTbSsjfhqjHOP/cwQ==", "shasum": "32b8e8735ae0e78c6e1e917d2dd61ecad69e5604", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.27.0.tgz", "fileCount": 186, "unpackedSize": 1086945, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDdxZZPdDz4syNiYP5dbxBmnxlgoleI5srCMp3/S078JwIgdYSTNycVuJD05eZScJJ730ezpkPIcZYsKkyk51XDM1w="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjIzfKACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoHaRAAmwI6v16OZXgyybE4PNZVubaorwF00hcKTVClXzrHLJmFkFL3\r\nugSGqDFjI1Xck0EqYqqIZbOjXRx0NB2u55OcbEu5zHqNo9iryQqo2NaxuKEp\r\nda1UpEsbfMwYRt/LUFgR1mZO9z0HjQIC5nYswkLBTLd5daJVZFhABYGBIBTq\r\nXdnl62cE+5c4QdNq2jBRGaqkGufHFFdFaRYqt6kXRu28J5R3c/IFMVFsgosM\r\nfjFHNuvQ0V+PTL4E4WZLjep/6610bilIC97HRHT7nanQeaur5RRCPQtrBkSB\r\nFybgPZoXA33wKlWrA6QxfzLDmfYoF491peKmUkEKgKeQeK43Gll+pfDwdePz\r\ncna+w84xfECTO7YpZqZ0lfwGZmg15NvigAUWRkVbSryPg9GJY40xJctDd8zp\r\nEXna1kf0GXWFJWi3NcYirKb2W7CV6iVIWd5ADkNu8shL1plmm7v30SokClrk\r\naBDYngHZ4AhbuKAAmHLt87VI7aLrR3r4y6FL2ov/eq0cvoRd//ZWYhTqwtwh\r\nAmcmHR1ahZnHlojtBGE48ICgeLNo1IDkMhTljSK5K5WgKrGaKphmr+H1a+6j\r\nFxOyELqaPRzwc2oYf64uQNrElZmYnx2qZhcKlfsrYMOT2P/iQk5tPyAGzqr1\r\nxY3lKcC1GskIy1+of3G3+fZhwhrKs7eluP0=\r\n=/f8G\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.28.0": {"name": "sucrase", "version": "3.28.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.27.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-TK9600YInjuiIhVM3729rH4ZKPOsGeyXUwY+Ugu9eilNbdTFyHr6XcAGYbRVZPDgWj6tgI7bx95aaJjHnbffag==", "shasum": "7fd8b3118d2155fcdf291088ab77fa6eefd63c4c", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.28.0.tgz", "fileCount": 187, "unpackedSize": 1090628, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIGuXH/NgStMBnAInud+8Dqe9YUSkgBkwwMtCUdT7OL0pAiB9cDGHY6RrJFr2OntNP4B+vO/IEBalHVWNML1fr3ar8g=="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjPS5zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqxTBAAmFCgHxQwBJT8K4h8gMQrMfaTzxO0KJNwksW1EP14iUuVNqg5\r\nYntru+uNQd90Wt+nudF3gG9iUVJxSQ/GG9OlLkiDDAZJF/juwfXpRcqp88Z6\r\nd2PxJEtrsa9OcM0sqnLI9JL2R7Qvalxm1wzEERll7n6pTw/wI8dPSbqUChLt\r\n/wiopm1ZQxrr/UaO820cqudVvkCQ+a2rOEhOOgi8vI5Rdg7Nbq6+V5AIDShF\r\nRod+dHoF1/sjYfl10+jOzoccNp+InYIHQOCN3qrDjNz8GKXZbk2ROIH3DzM8\r\nKl6LrjhwXugDOnWBY8Yzm/AQmuzb3PWIF5q85OVTdB3Fz8nA87VnzrmxgXEY\r\njPyrOPXqhIgePdJ8Dhur243U4957ca8Lrm8K+0FfNIe/kf755pVjJvAHZTJA\r\n74X1Lv2GV2Mvc9euF1wt7qhf88Cwf3HiM3WpsdASXTSssE9bLc9Qy1I3bpsD\r\nkKHWNDkYsuxCCCUrYLL4GbY2ynpsggm0M5bzjSDRArq4fm88dpC1bWHxIYL/\r\n2tKlfcjgD5rj74Gd7mB8hsz00SV9bTNiwFPNbLr8tyfIcGXkYv2nDNSmCwWs\r\netX/oispiyoHgo9mQV95KacFp4UfmdhVki5WlEQauvNWIQeZv5T3VEPJWS6e\r\nCMLm3vZqCIm2KX0SBegslk+1yHjCuIIL1/M=\r\n=IxCP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.29.0": {"name": "sucrase", "version": "3.29.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.28.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "^4.7.3"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-bZPAuGA5SdFHuzqIhTAqt9fvNEo9rESqXIG3oiKdF8K4UmkQxC4KlNL3lVyAErXp+mPvUqZ5l13qx6TrDIGf3A==", "shasum": "3207c5bc1b980fdae1e539df3f8a8a518236da7d", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.29.0.tgz", "fileCount": 187, "unpackedSize": 1093033, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIEtDYoN8C6lEV6jpyzkd1cBmrZ1R7sHt76symCZYgWviAiEA8cRSFl/VZsbSiN6YSROQ6DdMp0lk7g99WFr0W84Bgag="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjdKLsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKqg/9GEQtLd6ainV1+tfT1kkdhfmcqxKEIk5bQmgtr6BfJxJZJauQ\r\nQ2JQGzIsqOC03XBfv3/5H6Qluds5AVOum9Z4YVLqIVTbSOaoHPj0OLbQpVLE\r\nxwedLPouoEuVxLVYWqGBAtB17wRkvPEkp9uyEAk8TVo0Cm6Fovv+fFZfirZP\r\nhJm+6CsOmTysqzYQ8/OdO8WErKF7BKp2lpMYySZTc3wrF0uGMH9faKAYDSv+\r\n7xFxbhOGE1Uob+KQQmNHNsC5+UQrqXmL9Xoqln78NKK8hz25LB7WMvqAzU26\r\nHFpzBwHTcgWZs0Iq4V287adgOU1X6fMZeG0aB5OcmA92Ory3B9Qml7IN+OLQ\r\nD+8TYG4XXAOdLaDFhAYB/VoBQ+cnjyN9z8D1WBvUs2EPk4xx/05uT2ObaEbD\r\nQN3MSRJ6HiOCSpvJhs2dAE2yU3Gdkd2INZ88wKH/GtY6QfYhdpqvwe6fzHqn\r\nwJNfCjltRh7n7K0CGBVOdwyshg1QFEFHRhbn989K0jxnUBynDwUmXOjQs8T2\r\nr96CW/WlkHgEACyHySsymbmGMFL1Upz11w3egn6QUKlA6bEAr4b2nvCwBsCV\r\nVikThFfZFVr0p3g48c79kGFOi3FNstHbNoeCZrjQnok3fnCbbxOEqJSYJN/T\r\nQZADJp2DyOqinbPw9uRq1BalemyJkroLH/U=\r\n=R2ty\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.30.0": {"name": "sucrase", "version": "3.30.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "~2.26", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.29.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "~4.7"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-7d37d3vLF0IeH2dzvHpzDNDxUqpbDHJXTJOAnQ8jvMW04o2Czps6mxtaSnKWpE+hUS/eczqfWPUgQTrazKZPnQ==", "shasum": "ce8f49152913c70945d65d9e5405d66a399cca8a", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.30.0.tgz", "fileCount": 187, "unpackedSize": 1105581, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBE05c3bncTU7vJL8aG5beEtf17pLg++2gxfPDYqVnm4AiEAm8ToANnfOaqfgX9cR56Mj1AsF/x3saIyIZCImeZDrdg="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkGBTPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqMrA//RTJmz1d38UNYvEOOti4f8qXLgA4PVFKv+d3Q46Mgn4T8KS+0\r\nnttnXBxRKrGFICjpthRKL1Psbv734moEyxK/PyHPWGNh0zGzBBygbrrFLT6P\r\n4h5Y0ebGDAWiiSgpUPKhPHZbmKambghOvPoEop0g/qvD+Rh6fe2rKO4wOs1t\r\nipXPv9NGaQAFRSy5IdBSltN9nSam1PL359gUE4IxGpWWgMgOWoSDmy9KDtAj\r\nLVm1jReC8tH4z0SbM9X96VXATxyFv727SbTrtuQqjPqzL1bvpYs3XDMsEjUq\r\nOJr96n5SMWy5esrOYF7yfpfvjqVcEXoyPMXqD/e0ARrYgu5+Ho8YXQ+JJQTO\r\nga/Djn2AqN2dniVZQIPgqKuo14VCWYGiHH02N3EpxWJXvsBKFvg0006rtplP\r\nY526GGZkoviolysVdiz3O5r7ll01qcAGX7UzjOlxc0KdPoisuqtZD7igxgn0\r\nh+1ggy9nPlVep/44yiuRtfcWpXGNvx0Bj4qYPESVrUl1ReXFCPQ9v86ZPMYC\r\ndmGdJBGAYKS0A2l9d/wet6Xnc3xwZrSlOmMobtYSiyObVZhDRrH7O1bRC676\r\nwo9GA9vNb1W0g2RycNjK9ujvsWD89QsocnxHnB/ldXMbvy4/Yw95kOfSRlQ6\r\nSjwtE8g/n/yhiYisqiz0KHI/g4XtWYUFUw4=\r\n=rD71\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.31.0": {"name": "sucrase", "version": "3.31.0", "dependencies": {"commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "~2.26", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.30.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "~4.7"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-6QsHnkqyVEzYcaiHsOKkzOtOgdJcb8i54x6AV2hDwyZcY9ZyykGZVw6L/YN98xC0evwTP6utsWWrKRaa8QlfEQ==", "shasum": "daae4fd458167c5d4ba1cce6aef57b988b417b33", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.31.0.tgz", "fileCount": 187, "unpackedSize": 1108307, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDBQUwRu/qzEoYPy9dz7yA0nAoV0YICJA6Om8cCBXTO5wIgCUBWMjrNpjW0cxLmgzSrmkQSsQWMQdteemEOYBo2mYs="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkISBjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrS+w/+PT9k5YBQTyHbJlxSauUzla6P6lvYiWdtFmPDZDv/h27NHqsF\r\nlWzpcCQ7RmBV9ehB1XImJLLqfD934+SrQIeAy+NM/mBFt3ssa8tagokr4t++\r\nqEaJiepkb/z7EEW1wbSqpapH44EGZdkL6Qnywr1N/CKN1IcoTJauRbLhtDCS\r\nzIvGM5FXGVoCt6E6WWRD2IQjPNIGI3vgOHkJr+rY8u1zIvBgMXc8Prg7crJY\r\nkvMHr+A8ZR6bPqVKXt5MeoRIimFN/hU9+7/bVXyc0aHN2Cf6bKRcgrjBlc40\r\n93K1SCj+jqmOqsxHmIqLA9l2LsxXtkl8GVPoa/QIxIKLTofqrOjlSTM6hbJ6\r\nJfucMpPmFa35b6fmknuUL10hVCKrQCTb19U3L7lLl25bG28RpBnnvX5Eis1H\r\nnfU/UeOw5mh7o/AlTfL4ZAPtq+iUbPfmbgybW7JAXsoJ1yVqjcNFDqZ4OO8D\r\nk/FYk2GfbzVetfr1qNyJYz0ifdu3jDZwIk0wVAwDUy7sUeZAwX6aHjfGqBSc\r\ngzYA9Jlw8j0hyUx+RcKSKyjoBoJnYxtrc5SS28eIsg6uiBMiZYEH84xcEC5H\r\nz7gp1QCxW+099XyocUtKMQEDPlqyp1P+l6J7MroZRPz+Ghl/5vieX4aQKoA9\r\nbkQIOVmoNpOkn5gWGCo+Qe1QFFxVezvAGCE=\r\n=Qb8D\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.32.0": {"name": "sucrase", "version": "3.32.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.18.6", "@jridgewell/trace-mapping": "^0.3.18", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^17.0.41", "@typescript-eslint/eslint-plugin": "^5.27.1", "@typescript-eslint/parser": "^5.27.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.17.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-import": "~2.26", "eslint-plugin-prettier": "^4.0.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "prettier": "^2.6.2", "sucrase": "^3.31.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "~4.7"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-ydQOU34rpSyj2TGyz4D2p8rbktIOZ8QY9s+DGLvFU1i5pWJE8vkpruCjGCMHsdXwnD7JDcS+noSwM/a7zyNFDQ==", "shasum": "c4a95e0f1e18b6847127258a75cf360bc568d4a7", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.32.0.tgz", "fileCount": 187, "unpackedSize": 1115940, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIQDU+4+3xjG2gmH6QeWQQpH04DvtvAm0WEJSnWmCjoYwXwIgSFqboNcKL9BJdJoZS+lLg2MKBNSV7ELbC6txIfreQNE="}], "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkMlYCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQ4xAAi65PSF1aemTKuSoFktUlrA60qEEL91qnxi2KS+spYMwcZ8Wa\r\nzLF5KSNpSCBD/XtGYLEdZAef4NvJC9ryj6FpBVFUS/6UjnjVLyHGal+TSHbu\r\nSlMXQ6MsChtjKGe9ngBD/i+j5o9XE94DvE/+jy82q6fTxYXYZ85Mg8vlSagt\r\np9BY3P3P55WJ/zbSAliRyW/s0UpmG03N8CsYvigRHB2Sxc9lyWZea7HqKJ5L\r\nqXFqi/Ft1udWrN8cuUpK9ty5hvYQxUcrKzNEQFMWpK7yiwjGsx0eHUjeaqSa\r\nq/hfNKMCDE/UqIeWpIJIvztMNUtFOzjqGc3l1QgAJzMGHY4r/mop9PS6KJgH\r\nkEgT5Ewrbx6EpebmUrixxdQ0jaJgHZl4SIDltkCrp5Q5kCpNCdiQKjiSM9KT\r\nOCv6cHOfHpmWNndkDkhSGNiSRNP4j6bkiDYJSHmfXT2p2po3nForuDBwjsVa\r\n/8kZRNUklPE/kq2BkcrbMk0lMa+UHhadjQIvCea5skPIuGvw/2Ev0YOfBdYD\r\nraWUo0rbiXjW479NhWxucR+OaoCXvVs2ysd1ZCrbhDeWB603KdavQEKpJ9Pf\r\ntQcP6qzpny4mbzRkCWD1HLaKb28ErzL5TIOgUVJ0Y7jTBL86a9bbrW0T5o7u\r\nH1+qEXIRsmH/BDL06A/xYZEKnJWb+NZ9E4I=\r\n=qa1S\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.33.0": {"name": "sucrase", "version": "3.33.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.22.5", "@jridgewell/trace-mapping": "^0.3.18", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^20.3.2", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.43.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "~2.26", "eslint-plugin-prettier": "^4.2.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^2.8.8", "sucrase": "^3.32.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "~5.0"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-ARGC7vbufOHfpvyGcZZXFaXCMZ9A4fffOGC5ucOW7+WHDGlAe8LJdf3Jts1sWhDeiI1RSWrKy5Hodl+JWGdW2A==", "shasum": "092c8d2f99a191f2cd9f1fdd52113772f4241f6e", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.33.0.tgz", "fileCount": 190, "unpackedSize": 1134333, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIF3KRi8JR3+gD57/91WnjknYwJ3rWfOcKfY+0je5XfFfAiEAjUL+j3DxOTzR+hJHva2Ml9QEFg8lihmNuRsA8tPsr9w="}]}, "engines": {"node": ">=8"}}, "3.34.0": {"name": "sucrase", "version": "3.34.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "7.1.6", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.22.5", "@jridgewell/trace-mapping": "^0.3.18", "@types/glob": "^7", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^20.3.2", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.43.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "~2.26", "eslint-plugin-prettier": "^4.2.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^2.8.8", "sucrase": "^3.33.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "~5.0"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-70/LQEZ07TEcxiU2dz51FKaE6hCTWC6vr7FOk3Gr0U60C3shtAN+H+BFr9XlYe5xqf3RA8nrc+VIwzCfnxuXJw==", "shasum": "1e0e2d8fcf07f8b9c3569067d92fbd8690fb576f", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.34.0.tgz", "fileCount": 190, "unpackedSize": 1137174, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQCseWsEOMosJ3nr+Ng8cISYq/ylURRsG0IdSXQVBKiYxgIhAMusJVYqPvcl1x7c5PvVCdHueT2qspEB4HDbLqK1elQs"}]}, "engines": {"node": ">=8"}}, "3.35.0": {"name": "sucrase", "version": "3.35.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.2", "commander": "^4.0.0", "glob": "^10.3.10", "lines-and-columns": "^1.1.6", "mz": "^2.7.0", "pirates": "^4.0.1", "ts-interface-checker": "^0.1.9"}, "devDependencies": {"@babel/core": "^7.22.5", "@jridgewell/trace-mapping": "^0.3.18", "@types/mocha": "^9.1.1", "@types/mz": "^2.7.4", "@types/node": "^20.3.2", "@typescript-eslint/eslint-plugin": "^5.60.1", "@typescript-eslint/parser": "^5.60.1", "chalk": "^4", "codecov": "^3.8.3", "eslint": "^8.43.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "~2.26", "eslint-plugin-prettier": "^4.2.1", "mocha": "^10.2.0", "nyc": "^15.1.0", "prettier": "^2.8.8", "sucrase": "^3.34.0", "test262-harness": "^10.0.0", "ts-interface-builder": "^0.3.3", "typescript": "~5.0"}, "bin": {"sucrase": "bin/sucrase", "sucrase-node": "bin/sucrase-node"}, "dist": {"integrity": "sha512-8EbVDiu9iN/nESwxeSxDKe0dunta1GOlHufmSSXxMD2z2/tMZpDMpvXQGsc+ajGo8y2uYUmixaSRUc/QPoQ0GA==", "shasum": "57f17a3d7e19b36d8995f06679d121be914ae263", "tarball": "https://registry.npmjs.org/sucrase/-/sucrase-3.35.0.tgz", "fileCount": 190, "unpackedSize": 1136988, "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDIQAYCwBmoesTO+349wiJaVC51UsFp3RIFTviLYj5wTgIhAL+HW+1Jn4iuOVaM2B0+mdEeJQPPhRpUlvd4Z9Bo6FrI"}]}, "engines": {"node": ">=16 || 14 >=14.17"}}}, "modified": "2024-01-03T17:43:16.346Z", "cachedAt": 1747660589170}