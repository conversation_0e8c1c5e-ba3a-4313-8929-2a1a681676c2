{"name": "strip-json-comments", "dist-tags": {"latest": "5.0.2"}, "versions": {"0.1.0": {"name": "strip-json-comments", "version": "0.1.0", "devDependencies": {"mocha": "~1.14.0"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "ec01ab67f10b79258dcbcaa03ba82485acb589d9", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.0.tgz", "integrity": "sha512-QFN1P8dzwpB1nC6jQnCnjptXM/AO9DGps7c6HFieDIf0NxShzy4C89/pASyH4ZeYFOo2psQ67HI0iL4b2875zA==", "signatures": [{"sig": "MEYCIQCCWwetYF/Tg683CPuswtNEulOUukPRDfCIvc3BhDiD6gIhAJor94Xvq7+BsHVz7dJPQT5QSOwGfaOgSpd6oXLm1oev", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.1": {"name": "strip-json-comments", "version": "0.1.1", "devDependencies": {"mocha": "~1.14.0"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "eb5a750bd4e8dc82817295a115dc11b63f01d4b0", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.1.tgz", "integrity": "sha512-M60kC/sJQwhrFzgS9r/X011ikc7xsvnmXC/h3m8a0UF1NJBPoj89NoJpernXhJqAjN6sLfGllU74qym9fSHaRA==", "signatures": [{"sig": "MEQCIGb05Av4UhjnUgH1VqUJRYvzz2NgDEWa3iiUvRE9cFlbAiBhOabz1OwlVCly7E5tz7SxUhCir3l/Pwc44f42Tw4HiA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.2": {"name": "strip-json-comments", "version": "0.1.2", "devDependencies": {"mocha": "*"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "a5f9cc48c488c9b8cf1303f880cd0959923a9ee1", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.2.tgz", "integrity": "sha512-rSiJWCy2RKuHrt8/VLwtNn7ksshM53wosSSJI5zWSgC3YuN9BBf1FuYn0f/y/QeRsxnWmhNzgbRV9FgXwiVdIA==", "signatures": [{"sig": "MEUCIQC2Y51Nw8edA4yYwP+Kwnw9LYOo579S5IePqO0R632i4gIgcOtpeTDUzn5eG0WGh0Dgbldmd0QPoepgO+xtyu19DQA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "0.1.3": {"name": "strip-json-comments", "version": "0.1.3", "devDependencies": {"mocha": "*"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "164c64e370a8a3cc00c9e01b539e569823f0ee54", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-0.1.3.tgz", "integrity": "sha512-d2RPtrkLs8TurFFAIhW8IdM0+cOq+QFETWBGKHO+93eZ4Zt4P1CeJB5LHKW4EfEwabEpPL8/UTO3QX94+lqxwQ==", "signatures": [{"sig": "MEUCIQD5kPjpLpeviZt/WIjkVmmC2VvnLK5cDs2etXJS4uWIQgIgf9YcWtb8pwOGMLynk8hWC8XqPUXFpg5TpBNzT9JLGvg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.0": {"name": "strip-json-comments", "version": "1.0.0", "devDependencies": {"mocha": "*"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "5e284febd8826287c8266f224ed2b1fdc7220e9f", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.0.tgz", "integrity": "sha512-XnFRxmff7W8ynHhw5B2uti0LAqXVm5K+7mFr65/3EKAw2j9tFev2FmgBfctiEXYQKnniY7x4mgkHnoaEk8kTrw==", "signatures": [{"sig": "MEYCIQDtyavtBsnqvUYfsEWjkDK9vxFxHzci/pXP8JhYkQ4qIwIhAPR4cX30FumVe62DqLU6aHNtusADqQl875QyIVq+e1d5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.1": {"name": "strip-json-comments", "version": "1.0.1", "devDependencies": {"mocha": "*"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "a91252d95ddee4ff38a66135cd6c78de5709f374", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.1.tgz", "integrity": "sha512-rAMmGzKuo6VWsB6mHVUa7DHUbUxmddSv3HZ7GZ23qEl4XqFsJvbpD7xabc6LEW1Fa3gGi+L3ayWTAnYAnfWDaQ==", "signatures": [{"sig": "MEUCIGmc6udJHiglZX22s3tpb9mFuMuazrNIu1zKvXts8mo1AiEA+BB9Uoip06WK+3Hgs91UjEmOImaoBL1ivN3yXEvsDwQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.2": {"name": "strip-json-comments", "version": "1.0.2", "devDependencies": {"mocha": "*"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "5a48ab96023dbac1b7b8d0ffabf6f63f1677be9f", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.2.tgz", "integrity": "sha512-zRzQSDu8YVDoLt1cVFfznCS6Y0wxWphJiG6+2d9YLZQJKooieRoVa518RFGK/P7m9ZsNyDEiknOJkdPQu7exVw==", "signatures": [{"sig": "MEYCIQDNRiMEUhXbihlUDLU7izwDJNV6YRpMb2mCa7MVG2YXUAIhAOyk/oU5PGkf71yBwJfqHojSTr5SAu1j+uYggafmx1CM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.3": {"name": "strip-json-comments", "version": "1.0.3", "devDependencies": {"mocha": "*"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "c250d2646a24fc5603e27043005dd97dfb409fc4", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.3.tgz", "integrity": "sha512-S6nXPW6pGGX6okQDMaxGLyXhbwsrmYp8GF0IrPomr1rjURI2QtnGQrg6Kc5zlk03f9dIJCaKIkjdM4L/+qLqUQ==", "signatures": [{"sig": "MEUCIQDIJJ+CMvq/o1+o6jq2pIW1DMgUlK3oc3wD8O9mAR/h4gIgPuHP9RgfiksVbp8qjd161/QUQeRsYTKqu8IOY6bG524=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "1.0.4": {"name": "strip-json-comments", "version": "1.0.4", "devDependencies": {"mocha": "*"}, "bin": {"strip-json-comments": "cli.js"}, "dist": {"shasum": "1e15fbcac97d3ee99bf2d73b4c656b082bbafb91", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-1.0.4.tgz", "integrity": "sha512-AOPG8EBc5wAikaG1/7uFCNFJwnKOuQwFTpYBdTW6OvWHeZBQBrAA/amefHGrEiOnCPcLFZK6FUPtWVKpQVIRgg==", "signatures": [{"sig": "MEUCID1wVLqluPc7gzNB16J8OlOmTdolhl6l7GEpq1NvdpicAiEAvCKLoWcdHyDj5uC3I7jXBy0dMx66OgItygHn2XMdFO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.8.0"}}, "2.0.0": {"name": "strip-json-comments", "version": "2.0.0", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "413fc5a34ef34a29e4376c7eb632c884d2e97f95", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.0.tgz", "integrity": "sha512-Dt1tiQ53ogvscvhfuFzRzD245iTm+eS7BMicLBzmZDTm05kB56Y0pfmU9bTZH8+sITZXukohx7nElw91WmtlRQ==", "signatures": [{"sig": "MEUCIBvQhES4zVt6VcjIox4c/vj1FuFA83ydiECcvYAdnEZRAiEAzVXEo2rtMPBf1JEWaehV//57FXFcjWzCoNvFx1EWU4o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "2.0.1": {"name": "strip-json-comments", "version": "2.0.1", "devDependencies": {"xo": "*", "ava": "*"}, "dist": {"shasum": "3c531942e908c2697c0ec344858c286c7ca0a60a", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-2.0.1.tgz", "integrity": "sha512-4gB8na07fecVVkOI6Rs4e7T6NOTki5EmL7TUduTs6bu3EdnSycntVJ4re8kgZA+wx9IueI2Y11bfbgwtzuE0KQ==", "signatures": [{"sig": "MEQCH0CCCa3qkaq75YUmE7ZN4C+dLkGr4SEbkjI08ACExooCIQCav4TQvZefgBbxcpxeHODw/cEHgJF7pts4shhMhPO3Hg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "engines": {"node": ">=0.10.0"}}, "3.0.0": {"name": "strip-json-comments", "version": "3.0.0", "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "dist": {"shasum": "86456f81e4bd10d314576c1adf7719ed0894ce3b", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.0.0.tgz", "fileCount": 5, "integrity": "sha512-+yvKOYsd+oXGSoNMD0PgjwWn4MrgXTK/t/Fh54NXLf1cOOf3ceP/JvP5JRDP8jeKBsmtXS9PeoOr2eh1RKWSqg==", "signatures": [{"sig": "MEQCIDf7Z7koNzcqdPIVs1eyGvckSW02BZjqORoKrOI9l1TKAiARJCtjhYaqx4nmqXxalploS/gTQquucM2U02+WhyXkDQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyD4qCRA9TVsSAnZWagAA2R8QAJUeXxjRJnF3A6Lz9ipv\nH+q/1iMewZ5bU1BgtPLNaF6e/r5H36/N3TWsYZ1btNtkSBNiOSjoy7Jc/yp1\nkUOoA89QwE5z8TER7V4rHwksFezpBuxjcBuDxMpSSSatrheI63nfqhza+LiT\nxIUbbTc6JVZGf9tzWmDrsNqS7m+cuVljTHG+dBPfGZx8iCmHVy2gBz/98DuX\nRNIp4HjfibeY79DAMRf6jV7+Jtp2xG0d/L7cMUQN0TGOdrWKNt3vIcuRh/Ub\nEQ2quEi2NXF2bguPQ+O+w6/VzaILxrL7+GmapeZak25d/p7hG0MQqi6+rFC3\n9pZmcQH8UUEqvKpeBTe+aRg7bzSQ9qIK9Tf3OTBc3mp/dxS6IsXzjT9vs7NL\nABYXaBQfXVaYE1uAzz9x7dniHdHXmfvCJ3H0SORubQW2P+lXzjm8V4dhLi7U\nK6TThhODPodo88M9QliOHZbTge1s44dFHDyMRpxCXy5mj+fd/603GuMPKsuT\njkECgdhSttUyJ3Kw2fZ1vV/kos1w+OgBq3mkVDVZXSoFTo0jBZg8CSOZK6Xh\n7khUgJ7sLSlvwTMdCfo2nKYX+cVgnUq+nODouNmIG9fPhaaW3cMRByz3AjE0\nmVYFVGzMTlIanZERd97CgQOVUxZlSICaJJnDGwNGX/5NL5zxek55UJ5NuGZH\nwU5k\r\n=Hpj3\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.0.1": {"name": "strip-json-comments", "version": "3.0.1", "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "dist": {"shasum": "85713975a91fb87bf1b305cca77395e40d2a64a7", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.0.1.tgz", "fileCount": 5, "integrity": "sha512-VTyMAUfdm047mwKl+u79WIdrZxtFtn+nBxHeb844XBQ9uMNTuTHdx2hc5RiAJYqwTj3wc/xe5HLSdJSkJ+WfZw==", "signatures": [{"sig": "MEUCIDeLmpK7GNq2pj0npyXcauTPv6+IE+wzOLhPOTjWlqFwAiEAz3stw4y4ajY9M+qAHUTzOqQv0DtHN84oIevtpzvyd/U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6334, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcyIG3CRA9TVsSAnZWagAAoFkP/2Fg4JABehZLiTFnJiUw\n5ToGpDbTz2vg7WeG8U78uyTYEVQUfnEBnu/kpi9jzM+RWGR1JS/DBvq300kf\n2baOxBKgprjew2H+rMnuPj3eBJ0OXP+hNnzWxHQnlwmBDDHA/0CjnKdxD75z\nJV3X2KtIX/0/oK+c9rgVvcc+TUwAWMnu8J2HraEVQnUCXH0c/9foWBvKkbai\nzLyirUpRon01FzXgx7U/CII49KPFIUVALX+ME8PuAqUP4/iJQDbp8tIXSMeV\nCgcctZv26GgLXV9gyrKN+cRE4hcs2an8Ex0TVJigbd1V6mJWwsu2I8koVxSv\nPBGBDxw7jGaAad7uC0sAbp6q4kUC9Vo3WsRrL70khnfqcINhYDF9oc8xhNj5\nbAIYtp2oe3KR9vmNmKmv/wX9OhlT0fRJFmaNvRL+N0KqVQW0xYzdK+hoyCJe\nEq27Ng+ke/IXerDVv5Z9zaFDxb7Z5h+mC5FCSjlcHYb+eiESRfNzXUQuRVa5\n9Miedd+tcsoWe9EVts715PtArAlUxyy2I4qvdjERIYpG4LPnX0Rjcj6ZreI3\nE97wwEenIIVHhN8HSrCPRaI4FK9kmE3jsm1qc3T5Yy0meBrjiLTp3/6mmI8N\n0XV84neY0vC/0arYRytSZUBITysBU3KWSWzMGwMqf2IioV3hGtwySzDcylRH\n5rpw\r\n=e0gI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.1.0": {"name": "strip-json-comments", "version": "3.1.0", "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "dist": {"shasum": "7638d31422129ecf4457440009fba03f9f9ac180", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.0.tgz", "fileCount": 5, "integrity": "sha512-e6/d0eBu7gHtdCqFt0xJr642LdToM5/cN4Qb9DbHjVx1CP5RyeM+zH7pbecEmDv/lBqb0QH+6Uqq75rxFPkM0w==", "signatures": [{"sig": "MEUCIB5cYYsPmNk7rEOtf85v4UCZnE226GAzpmC06V7B5wIkAiEAhYhks/kSsyTMQ6AWSKAQ0/bptQ5dXZPfLSkd+MRGomw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6878, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJeiuE4CRA9TVsSAnZWagAAyacP/0muJ+pdHtzbxZVVwJWo\npgWHr1x+KEIY5H7ng1Nj3FfPGYjkwftLig8zQhInGuseV9iwFJf8Vj3pRLRQ\na0xNU+H8y6R1qy/awHHtSyAQz7T0tu9nSWUN9w8rsf7x0yLqsVKrl31e0WmQ\nirfvXPxZBH80ts7yaKCJoiCWi4bkfpqMG2HCnPW0MGxFZIZ0zErPK2uN+hOt\nHS80PkJJX4WMSaZVLJi2MyzopVryh7R+Rui14++H9Vi72g6cdnxqCRiTl866\n23MGLEeKRXC5Zuob17ZjXlRzPe/s3gP72cct1DLCMExN0A/dLDrsONFhUok6\niVVqNyx0P5/jWv2ziu/l/rZSu0RTX1qasTGS6+jIOwnKmx2IUNDNAeZMlQmy\nEiYbhlQ/Ss39MG5nIV6wqraCwY8Wcd1GAuaeBrjWnQ4xoaF/IVW9+dko0IiG\nY4kJM4CIpybyF918G19J2Ul7fEsFUn7Gg9LJiSEgHaAqypNNkIiOKEaRsO2m\nEq1f+Hqh5NtFwOQ9s7REHex7si1S6x3PyuMiVG5xl+59x8L2OeGj0WCllf5I\ni5csgxHOto/fCwmc4QjPDE94bMvyjnupqiFZD6A99G8DTRDKg/Yqvjhzv5Z4\nw2byr2Nwfn6pdTHWR7vnWU4BmXUlQKY8JBHlTFrvgHm1VeEyond1G+3BFZAO\nRGQ5\r\n=ycK0\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}}, "3.1.1": {"name": "strip-json-comments", "version": "3.1.1", "devDependencies": {"xo": "^0.24.0", "ava": "^1.4.1", "tsd": "^0.7.2", "matcha": "^0.7.0"}, "dist": {"shasum": "31f1281b3832630434831c310c01cccda8cbe006", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "fileCount": 5, "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "signatures": [{"sig": "MEUCIQDEFfhulC6y1p9gNW/jxefxWYUggxPZSjv94NI8zWE/ewIgUZDs7mGYPrXV44hHUWraHUlY1mZYV3iPANgcELxoggg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6959, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfCyHoCRA9TVsSAnZWagAA2l4P/igAOF4E3r0qoWCs4i37\npaZvhlr1axuDaxKmyT15bAp2TGYcIl0TAGm6+uubqlKU2YW8wAM2h94YzWNT\nXOHyml2tvKA/MtAQylUMXekaY+GEAoDVCyFRGHnD6lZpxcmPOAtcnEKBk4NO\n53O8kl/+LdnvfMwx500L2bFRPZWESIjVLWlw6YIUTOTEgYlOvy4kz3gC3ajG\nUH4ZP2nOKe0v5QOKPsZEXnOjTka97jHowxICgO+v14Z0OrduCOwPzyzpX635\nT7TGPRyKpe5atz1J/p5aCMNPEoQl3F6A1NEglefVINDsOZvV/DPItEsEr53K\nVhuYh8JtZ0WZ4TT1Zj7rrq/kNU0wkqdhwfYHofXqRdWxwIbUvK54E0dAZpaJ\nLBGysB4/fSHSlpHIJTNiNOxlBmGk6dY3ammlyjnO9qeVFbk3i+lH5cYH1/1M\ne3lRFpYvGklgYRPDRZi6UOfJ6715HYRwWm4twWC/ytvCLWxa3QNDNGBXG+Gy\nkfbym+fsXNMkcTweVf26+tBQt0vY8PkeA0ahY3GmUxD/0nNidtZtIFlfNaeZ\npR4JBABun7xbjQtU2tQyEs6NQJz6SK5rBc70HXHB2/cFTPimZWFX3JgGOykZ\n+x4j/E6DceZkAVt4qutuUt3Tv4QrF3EgOGW/jq9JJkfOKfCHpyLDG9VwDMzo\nxCZG\r\n=XcoI\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=8"}, "funding": "https://github.com/sponsors/sindresorhus"}, "4.0.0": {"name": "strip-json-comments", "version": "4.0.0", "devDependencies": {"xo": "^0.44.0", "ava": "^3.15.0", "tsd": "^0.17.0", "matcha": "^0.7.0"}, "dist": {"shasum": "6fd3a79f1b956905483769b0bf66598b8f87da50", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-4.0.0.tgz", "fileCount": 5, "integrity": "sha512-LzWcbfMbAsEDTRmhjWIioe8GcDRl0fa35YMXFoJKDdiD/quGFmjJjdgPjFJJNwCMaLyQqFIDqCdHD2V4HfLgYA==", "signatures": [{"sig": "MEUCIQCq/nwqSCvAaccFwocVlmbhpslvsb21DZCPS2IX5/om1wIgPmrXA2umKwQWdTkxg7Auz/ZJafuGLWto168OQ8Od+TQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 7023, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhHp6ICRA9TVsSAnZWagAAbFAP/19k3xc4mnD1TJy1na5a\nXc0mFghpbOzKD+R/Vz7JdLfKj2pTcmTqa1cy33qPxcpLwDwiwuFJhmYDP5gm\nJjebjGETZlv/hjWZrVlhet1D3Z5WUiRPI+qL4Wusbc6unnV7d7cIjzuRdpyD\nbaA+fYJDmtNdRU4NhMoIxKZwW+XzSsJT1aWAkb7wzJS1MG+4Qm3KCD8iUCKg\ndDrBzI2mzOx2V21lmwQl4iK5ieTHPun8Ajf+BbZpfJg4v7wvrUNMtyDmKRQC\nvN08m317YiGlPbOxwV1cpWBtZYyxw6PEuaMEkkVEO5XK3q4H+HhsHRFOTIWO\nIOeD4LUQrzRS3qo8fjW8TuzLwwdk416idTkRt44Sd7loocJoqd3w7rfwjQ7C\nD6r0r7fqSCKwur8PxYy7ultCE2MplVB9mSLcsnvV2m1Cef+kHvAex/dqcL0+\nvvJMvkOzXSOGZursVPWKKdZcdAecj+7B9X8LuChc6sR7ZDEmVhyXoTqIPkcD\nF4Z2pjEj6opsIUQUh7B+DDkgNRrb82br7/vLmQLM8adZVugWBJbN1ULfqJKI\nDLHV2pDVYHaW2EVYQVbEdBXDh9UaVX29xFliU0+vkTRVYybNF+jO3QzmGWbl\nvx7DEgf67hjMuPlZbGHUryMSXPvGGFqENE6qiR98vhpSQ2fl1ULNHLfJUH2k\n7o7j\r\n=qugP\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": "https://github.com/sponsors/sindresorhus"}, "5.0.0": {"name": "strip-json-comments", "version": "5.0.0", "devDependencies": {"xo": "^0.51.0", "ava": "^4.3.1", "tsd": "^0.22.0", "matcha": "^0.7.0"}, "dist": {"shasum": "ec101b766476a703031bc607e3c712569de2aa06", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-5.0.0.tgz", "fileCount": 5, "integrity": "sha512-V1LGY4UUo0jgwC+ELQ2BNWfPa17TIuwBLg+j1AA/9RPzKINl1lhxVEu2r+ZTTO8aetIsUzE5Qj6LMSBkoGYKKw==", "signatures": [{"sig": "MEUCID/8xYhw0rKNw4jhVY44Z4pPPaTSWpyw1O61ErCWEydZAiEAmBAGpM0FKVEk4TrEsd2AEBayaiL5bTspynT9U2F7Jis=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2nJdACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqNog//bW/1811nVrwhwYN/wL0Xf+ht4OiLzA0cjiEMquNI8e5uXJgY\r\nkxh9QwDMuzKgCIHtD6+veA/i5eXbh4ACLAjAvigVpemggVxqhYU3P/dUxw7F\r\nqn+mR4sGpmPgfoMUUVdUytcvpheSig6Gmc1Kx/1KeXHX7aUoeP6aN6qHKSOb\r\nVAKbbs0c/2D9T4KSKPLedFqyRIMA16PgKrNflRzwY2NT7SiWIWIGy1KZhe5G\r\nidH19mT/mIpJhLKEu7a9rP+d6fuJhxpbrdIxEnF/CaC1h0HDR+0OcblYKhRm\r\nFzkEIoJIfdwJtl5+O7YhWdLMMRPGok2kqmJyEAnTt4rLgF/IBdAvaqfBceEK\r\nfjmIVHYsbt4rtzmPmhotNZhwWVXasWncn7apgV8veSlFgkSkLFdVRtT2QK1I\r\npDit7SdxVFjznla63W8xAjW2OqhueIT3mscsvd47SdWbIxaJK5XUUUD/OWGw\r\nlEPfcyIjIHm2D/TE9chWVMgwk/Fy3MdisbyVmZYqvWmZDbmak/ex6TfFtYH2\r\nn7dOnidX+8GNNRqL1TvxXfAg9TN4VHquW7lma5vxOhAaoGC8bOl+/97qI9eO\r\nFubLQJMaFT0sWG1AiwQkq1VVyS2RVfh5pUdxDgWN/KHjnwKPPPsB15ioS6G+\r\nBmg9fOEPxbiW6dVEZAL6CZe4nx86pDq6y+s=\r\n=5eOQ\r\n-----END PGP SIGNATURE-----\r\n"}, "engines": {"node": ">=14.16"}, "funding": "https://github.com/sponsors/sindresorhus"}, "5.0.1": {"name": "strip-json-comments", "version": "5.0.1", "devDependencies": {"xo": "^0.54.2", "ava": "^4.3.1", "tsd": "^0.22.0", "matcha": "^0.7.0"}, "dist": {"shasum": "0d8b7d01b23848ed7dbdf4baaaa31a8250d8cfa0", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-5.0.1.tgz", "fileCount": 5, "integrity": "sha512-0fk9zBqO67Nq5M/m45qHCJxylV/DhBlIOVExqgOMiCCrzrhU6tCibRXNqE3jwJLftzE9SNuZtYbpzcO+i9FiKw==", "signatures": [{"sig": "MEUCIQDhcInXvpnuH/rxnhx2+i2ntYCwcpRQLX/n3/G5B7ExGwIgTK3ETqlDJK9XLjF1c160HE80398JdRCpRkflJUT12wQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8003}, "engines": {"node": ">=14.16"}, "funding": "https://github.com/sponsors/sindresorhus"}, "5.0.2": {"name": "strip-json-comments", "version": "5.0.2", "devDependencies": {"ava": "^4.3.1", "matcha": "^0.7.0", "tsd": "^0.22.0", "xo": "^0.54.2"}, "dist": {"integrity": "sha512-4X2FR3UwhNUE9G49aIsJW5hRRR3GXGTBTZRMfv568O60ojM8HcWjV/VxAxCDW3SUND33O6ZY66ZuRcdkj73q2g==", "shasum": "14a76abd63b84a6d2419d14f26a0281d0cf6ea46", "tarball": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-5.0.2.tgz", "fileCount": 5, "unpackedSize": 8153, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCk7tU66Y9u4PZNoL7BzZQs70gA9VSBw2777h8si8Pf/gIhAOpdRaRhje9tlvoWNxgzQanJZxnJHD8ky3t6Zr6yQMK3"}]}, "engines": {"node": ">=14.16"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2025-05-16T20:47:21.899Z", "cachedAt": 1747660590238}