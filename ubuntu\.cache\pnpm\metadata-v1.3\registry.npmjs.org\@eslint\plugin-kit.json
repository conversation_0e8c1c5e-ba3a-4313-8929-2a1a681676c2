{"name": "@eslint/plugin-kit", "dist-tags": {"latest": "0.3.1"}, "versions": {"0.1.0": {"name": "@eslint/plugin-kit", "version": "0.1.0", "dependencies": {"levn": "^0.4.1"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.5.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "809b95a0227ee79c3195adfb562eb94352e77974", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.1.0.tgz", "fileCount": 10, "integrity": "sha512-autAXT203ixhqei9xt+qkYOvY8l6LAFIdT2UXc/RPNeUVfqRF1BV94GTJyVPFKT8nFM6MyVJhjLj9E8JWvf5zQ==", "signatures": [{"sig": "MEUCIHKS+NbhHUUkBmRCVYuiYo0feYcEXPR7LhCTxTxY1lp0AiEA6RJPgxvyDFlMkBgORtsv0qlqFrvNmyWJUoMU6++Mej0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.1.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 69652}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.0": {"name": "@eslint/plugin-kit", "version": "0.2.0", "dependencies": {"levn": "^0.4.1"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.6.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "8712dccae365d24e9eeecb7b346f85e750ba343d", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.0.tgz", "fileCount": 10, "integrity": "sha512-vH9PiIMMwvhCx31Af3HiGzsVNULDbyVkHXwlemn/B0TFj/00ho3y55efXrUZTfQipxoHC5u4xq6zblww1zm1Ig==", "signatures": [{"sig": "MEUCIHgfWPu8QJU6mh8kjcBGXfGdX9LfABko9FVCrSzghJrLAiEAwtJeKMeMWKIK8b4DzhfsSOlKqzcAzRubvVHSmReYO78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76668}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.1": {"name": "@eslint/plugin-kit", "version": "0.2.1", "dependencies": {"levn": "^0.4.1"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.7.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "cd14fe2db79fa639839dfef4105e83bad1814482", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.1.tgz", "fileCount": 10, "integrity": "sha512-HFZ4Mp26nbWk9d/BpvP0YNL6W4UoZF0VFcTw/aPPA8RpOxeFQgK+ClABGgAUXs9Y/RGX/l1vOmrqz1MQt9MNuw==", "signatures": [{"sig": "MEYCIQDKovgpG4xeNJadZSua1z09X0KHAoHQzXaYbYkhHqEJIgIhAK9Ok7lBCaiQZaVafDxeXSR0QfO3prvbua6tP1MOm5Ve", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76672}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.2": {"name": "@eslint/plugin-kit", "version": "0.2.2", "dependencies": {"levn": "^0.4.1"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.8.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "5eff371953bc13e3f4d88150e2c53959f64f74f6", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.2.tgz", "fileCount": 10, "integrity": "sha512-CXtq5nR4Su+2I47WPOlWud98Y5Lv8Kyxp2ukhgFx/eW6Blm18VXJO5WuQylPugRo8nbluoi6GvvxBLqHcvqUUw==", "signatures": [{"sig": "MEYCIQCgn8paWiDoiPtg/PpIpu9IlvlDDRB//z5xPX5xsmtXmgIhAP7stDkTzLLdh7YqHxGLUAPYbIuKTc0WRsavBDO4/h6J", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76529}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.3": {"name": "@eslint/plugin-kit", "version": "0.2.3", "dependencies": {"levn": "^0.4.1"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@eslint/core": "^0.9.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "812980a6a41ecf3a8341719f92a6d1e784a2e0e8", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.3.tgz", "fileCount": 10, "integrity": "sha512-2b/g5hRmpbb1o4GnTZax9N9m0FXzz9OV42ZzI4rDDMDuHUqigAiQCEWChBWCY4ztAGVRjoWT19v0yMmc5/L5kA==", "signatures": [{"sig": "MEQCH3HdYJmY7M9P0AAcsu1Gnq2mOOaOAJfL/LTi26BraR0CIQDnfetk0JT1HroDc9WK33i0sFPmcbrqFBtjxopTjE7CvA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.3", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76956}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.4": {"name": "@eslint/plugin-kit", "version": "0.2.4", "dependencies": {"levn": "^0.4.1"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "@eslint/core": "^0.9.1", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "2b78e7bb3755784bb13faa8932a1d994d6537792", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.4.tgz", "fileCount": 10, "integrity": "sha512-zSkKow6H5Kdm0ZUQUB2kV5JIXqoG0+uH5YADhaEHswm664N9Db8dXSi0nMJpacpMf+MyyglF1vnZohpEg5yUtg==", "signatures": [{"sig": "MEUCIEaUYGTzvztI1Ol+Q12g/xSQwCkUxnsmsod+E3gmtUnsAiEAmqahROKXkbqzZ8x8TC5gZAQJhNSP645vxrSXqg+Yx4w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.4", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77087}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.5": {"name": "@eslint/plugin-kit", "version": "0.2.5", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.10.0"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "ee07372035539e7847ef834e3f5e7b79f09e3a81", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.5.tgz", "fileCount": 10, "integrity": "sha512-lB05FkqEdUg2AA0xEbUz0SnkXT1LcCTa438W4IWTUh4hdOnVbQyOJ81OrDXsJk/LSiJHubgGEFoR5EHq1NsH1A==", "signatures": [{"sig": "MEUCID8+wDz5jFDOXi+KbLywMFstfgXpyDhjaNjay0UAf9xkAiEA9mbXFrGhyRoFRHn01lzqr/bUF9Qe+kLBBRpnv4DsyrU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 76754}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.6": {"name": "@eslint/plugin-kit", "version": "0.2.6", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.11.0"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "a30084164a4ced1efb6ec31d3d04f581cb8929c0", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.6.tgz", "fileCount": 10, "integrity": "sha512-+0TjwR1eAUdZtvv/ir1mGX+v0tUoR3VEPB8Up0LLJC+whRW0GgBBtpbOkg/a/U4Dxa6l5a3l9AJ1aWIQVyoWJA==", "signatures": [{"sig": "MEUCIATUXwx5DDu8h8n0MpK018Fnq5SFKey2ibB0fnN3MVidAiEAxwRBqw/n0ZpfARZshazyj7m0n8nkFdOAZuSUD63vbME=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.6", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77161}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.7": {"name": "@eslint/plugin-kit", "version": "0.2.7", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.12.0"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "9901d52c136fb8f375906a73dcc382646c3b6a27", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.7.tgz", "fileCount": 10, "integrity": "sha512-JubJ5B2pJ4k4yGxaNLdbjrnk9d/iDz6/q8wOilpIowd6PJPgaxCuHBnBszq7Ce2TyMrywm5r4PnKm6V3iiZF+g==", "signatures": [{"sig": "MEUCICKjgU6UmkgL8qgy9e1nA5qYYwzV8+fY7sG4Pu0JjGgJAiEAof8qZ0wnMFpHk6bRS7HFUi67UGwrpf+jEYIfCmPCuGU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.7", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 77293}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.2.8": {"name": "@eslint/plugin-kit", "version": "0.2.8", "dependencies": {"levn": "^0.4.1", "@eslint/core": "^0.13.0"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/levn": "^0.4.0", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "47488d8f8171b5d4613e833313f3ce708e3525f8", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.2.8.tgz", "fileCount": 10, "integrity": "sha512-ZAoA40rNMPwSm+AeHpCq8STiNAwzWLJuP8Xv4CHIc9wv/PSuExjMrmjfYNj682vW0OOiZ1HKxzvjQr9XZIisQA==", "signatures": [{"sig": "MEUCIFHXN0YekCR13a444ijTmwM26+dPnFnqdzIxiAYPLQEfAiEAqZHmP2Qa0bsfl3FLz1QQdmmYejbs8/HVDvzRExKTAzg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.2.8", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 76842}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.3.1": {"name": "@eslint/plugin-kit", "version": "0.3.1", "dependencies": {"@eslint/core": "^0.14.0", "levn": "^0.4.1"}, "devDependencies": {"@types/levn": "^0.4.0", "c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "rollup-plugin-copy": "^3.5.0", "typescript": "^5.4.5"}, "dist": {"integrity": "sha512-0J+zgWxHN+xXONWIyPWKFMgVuJoZuGiIFu8yxk7RJjxkzpGmyja5wRFqZIVtjDVOQpV+Rw0iOAjYPE2eQyjr0w==", "shasum": "b71b037b2d4d68396df04a8c35a49481e5593067", "tarball": "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.1.tgz", "fileCount": 10, "unpackedSize": 80311, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fplugin-kit@0.3.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIG+ltl5FJFwVwxfPelXOXQsCaegaNJNJSBjUPMLkf9CCAiEAye80AHJkKK0MzrsxwmRmJsh8svVzj6ByKC69WCBwma4="}]}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}}, "modified": "2025-05-01T15:02:42.006Z", "cachedAt": 1747660588851}