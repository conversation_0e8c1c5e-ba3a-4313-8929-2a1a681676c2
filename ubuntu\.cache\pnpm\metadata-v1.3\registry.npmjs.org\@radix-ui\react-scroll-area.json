{"name": "@radix-ui/react-scroll-area", "dist-tags": {"next": "1.2.9-rc.1746560904918", "latest": "1.2.8"}, "versions": {"0.0.1": {"name": "@radix-ui/react-scroll-area", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1", "@stitches/react": "canary"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ae6386a1595d3dc193aa091e7cffe89e1fa7f7fb", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-pfXtfMA15C3Ctp1t6daI+SVF6GPfftnY4964uyfk5bQfUNXkTrwPwVegFX7xxEtFdmrAmVAIuJHpSqnwp0SyXw==", "signatures": [{"sig": "MEUCIEIio6nlq7ao5aVML7EREvI3SSoFVyCxGK5AuawO0phMAiEA7RMx3zay6U3OynyTsZvlz7P1akgdjUBOd6BIW/E54U4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 354601, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbXCRA9TVsSAnZWagAAgOQP/0UL/NaNatmEz+30qUtL\nEVWtQZ+F84hOwGnCtrZ/y0VZe6RdQMPVFQwMR5TE5Qcv9RPveoCA4xWuU1dy\n6CtFyiyayNvJf3jfselYr8MsLzKCnproWqw2P/7kt4q7UIRZ9xHhQVGymRSJ\nSBQ2P/e5O6jtxJOxocfBFDdAWKW8oPE+3ELoJQCOe7a/ONDR8xN86x5o+uxp\nnf8R9houzZLmCq9GzX5DzIa9WHISzuQxs2poIovOmiM9bj8Aoirorv7k/2s4\nrdTzMtQbpbuLCVxtI6H+yc3srs/UPOEQlHyqP8NdWJgTZ8BlbMjxG9qozgWL\n0cmU5tq08+W9z+dFU/MuNWD6ouYkgXSgVodOhFS5NCN7xgR7E87oSlu9TH3F\nC1YRApAx42i19n7aiUU4C3HcVKUSyzkGQrH0JvS7ODFvuFyItjr57AEI2nEU\nhK4UoHakSfQnHrl/R8QwV3AyXgmLBKuXgrM1ZbVEahfLDPc/epm1hQE53gtE\nEC0JzdHDFqu14CMYmUiGGG5qgrXqYxUV3zWMmWdmo/9TNGvnQ60Ftgy4AVMS\nSKcoGtW/sfdEfOtr9lbbexwlnG1Xt2cWkkwHDZZmnz0+jojyAf7eOiGJZLa/\nr98pHIvGmf8qx81vJGR/9Itsff4lle9DkKe/JAHkOkHsiFSSmdMdYt5YldvW\nGNCy\r\n=j07E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-scroll-area", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "devDependencies": {"@stitches/react": "canary"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fb883a6f1390c180f4798e1e587de9befbf6ceaa", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-MHPxpn/5c6PoV7OQtM44/2piF2IxLmwBS/gyDd6t9zcSJf4cuITH0Btgs768WwazocV+Vq00FPrgMLqdwXX62A==", "signatures": [{"sig": "MEUCIFavtagVrsSNJ+NoXLKP6TkJvoTEjpOakwBSO+8q1NvGAiEA5x+4tUlBAXKwv8+RCSqcQ9Fql266ERKQ8eKbg2cf8r0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 359532, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvbCRA9TVsSAnZWagAAKiAQAJtLERq+qtNOy+SWBhCM\ntv9NB/EGWEYvaqLO+5o/cY0DmbUW0mbS2iqdurfzA59FDmfyiQQjq0XNwYlF\nVC80tQnbShcoSwTIJKUiPIU0P80F+4y/SUhOED8ZJXrxtP69CcOh7CLs0Siw\nloMcqmAmfigbQTjqLKhcUmKRkfMzODN0OrXcLifNnqzXxQCYNpLe+kOtYiOn\nvQMRKKuJNmi+NUupKHyliGlS8ikuT4eFGEX7SGUClc977vyn9QwDf17Y+B8c\nO/8HlV0cWFPiln8AGIvuiK6wlx8kecSnbhod1qkxBk41gsNvBF7I46HRGfuA\n9VR7UhADWM17d6qzwjBt+V9v2oZgJKwnQjCehHKQl157pd3ZTfHsV7hpoBXc\n2Jo9a5m8mYh15CiSQXhpXWs8nBTrL6aaQ63zRiZ669VHkOa3UmrCmoj+Y1DB\nwEsLj5xqQ+enuckKJcRsIX7g+5VM1cVvtcmsNP8e4vy03e4y6lYNjU7vl9g1\nOSQTMg5ueo9PlOgdN19q2Dhg+W5yfVNbfHzdkADrF558STyRlEkPewDmmyNC\nq9CW58dNoGxX3n1ZL+KwlftCms7mdNXs16RWMiZmohS88ZPkZHX1L/uWP40S\npMRMVCcjVyRqtYSbelOcrjM1Uc66XhVKdZ7ip4Un6FrBvo2jGFpcPNGPJJiC\np9KS\r\n=yGp0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-scroll-area", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "devDependencies": {"@stitches/react": "canary"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "29690f6791b00cdb8ca22f3f577c02ef8cb57b40", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-UpiTIzm7CTKtOa6I1DXgbBBeNC9zDgKEp839jXv1/5mx0dGNc/LRJDUMl7/9P4Yrw4aZue9iNO3oubbXvirj1A==", "signatures": [{"sig": "MEQCIDZbEhPOmEhBPemBVuDtHYyfjFpmB4VgWQNCEIaeKQ4hAiBbAE4FFb787gBUKSbBLrpqAvGGK3O/9wER3tm4TRi8pw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366778, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtbCRA9TVsSAnZWagAAdIgQAJMmv+v6Ikw0XRUtpj1j\n9WVZm3pKetMZzCOQPTuDwDqggIbKpvpsFiD85qnf018Gn7rftcQaB4k4gSH3\nx2UsoV0aN36jnt0ownjKNlOz5yadSOKLqVilcMlGXawzzdNez76e9b7UqwK8\nIIAXKjwWur/FT8+TTt5meGYXRUzxOeNRLXK+uR2Sy73po3VCapQGMr3uyO98\ny6oGkHBzxJlXuEDJjTVv2yYjrNSKBPuwHjgd7/1PDJ/3CzXA0d3HvEihbJQ3\nJnArgKsHiY3A+/lhUy4qVnLFiShiaP9IWsXH4tSc79/aZ1j1RB1ae10hvVLe\nP9Uuu6V83jGMCgVKcNcHVFE/RTfQgRxG12fK9EIvdRYJ8+D53AMvIMJ9AbNX\ncaW59qLROBE4Y3Z/IhOCrTbxzvppYorVK77Qf2CQ8VUcGtHwrIxU+rf6o2Zc\nLk5VRhPqYHlyKWkMjaLPN8SDMmkFs4e7EtTjO1yYFe8fxvyf5G9fzwfBGpVx\nMXVgVRI38623It5b8WdyfOZKE1mIybPUo9SsAiIhgUFJso0P8gsIwDP5sLWB\nn3HLYuVYzTxp6zqgv2pAalu2X0R+diMul635Fmk99Mc+l/B+5ggTvPb+nqEr\ntwu7vCKexYdfXIuE3bRY35vSrYoGEQGEPMvNbiEnQuN6dwcbx1kpWmyWhSdU\nl6LT\r\n=/fXx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-scroll-area", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "devDependencies": {"@stitches/react": "canary"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3b10cc38c98c7ddccf3bb940db09cf8b0108dcd5", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-cw3VKlpsqpodfPeDFt2MGCxNrwgQL8c1mGPvrM2MaZvgZJ0IsNzqnvAf6xyFi1rtomUyAXdzzmJS4HgOO4tD6A==", "signatures": [{"sig": "MEUCIQDxf5IrLTn+xoyto9b7464JU7yKHw8cWJzoQa9KkDk1IQIgMSQITYNSUf/fDVhYaUI0OUCAptnv7zUEGt6PcPvOIg4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFDAVCRA9TVsSAnZWagAA5lMP/jEQqIqt+qDPc0Mbm/+u\n+nYQNHvcyfEdRBXLBYwR7b7LYzrWwH8KwBDkqzIrrklA5AMgymMt3srH2Zta\nLj/fo68sXaeOEg1kkig0FwGKjnDFLRZeeYd9kCXEj0pEL9vlKP0nIik7xvdd\n71tHQQITbpvubHM/4iIS/jtVaGVkzOLbMhGbB4EMWkJRMkqJPr3ZppbN4CYF\nbQ0D78SAXtXkoxKYJv4u9Nm7oov6OsOwIdqN8BwjlbP+2GdHh1lQ70VoOXzd\nkhGGfYPVMPlX/knXoigTLkFGlSR26lclpZfWfIxMO1gxi4VcVMaSbJaQ+odn\nMfG5bhZElSox/R1FhOrj81jQuVIz+VbbcekBhTvcbBC0q2KqWRI/Q6T67Qyb\ndUVMhh/ZdekiWETLi91cjr/zOiTIOWx82e9GrYKeLq0Z2AZGujVkVARq/UHe\nXb22iM59iA5I8yH/88jFlo5uIjKC4IqvdtZWhHfce2hy2U58PlRcGzwjElCv\nzB/lFZB5UPYMzP+l2psV+2iUMAfUWd7An952cSn1YIMe6ZEotxvs6Im4QBba\n1fdVe/nQBtGG11UFaNxDP/XJrgJq6PBphaqxoKaoiTPuk9ty8Fn1m/oTjQGV\n7W/lteQ5B//iikA82MEVcGwLo+SdDqvw11yvNdlumHa6VrAptcEHdQC2SBFx\n8N06\r\n=r+BU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-scroll-area", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "devDependencies": {"@stitches/react": "canary"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0f2ff607e93a51ee30ba21ed141fe5b14576c5e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-PQSh/Bixa36+/TVTHNbrzFQ70l1Uy68cxvK8JmmmSi6cJJ6JU5E2FH9rJiCJA+wvQvXa5B+sTUlmFnQKrosIPg==", "signatures": [{"sig": "MEYCIQCgqoQxyvtEMdwLsn2rThgbCanHwillt1sUlWMiJ6giIAIhAI4yl0asm86Tfte1em0apb0O1odKTJv7eIvlcIwtvJVs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 366022, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/V9CRA9TVsSAnZWagAAZBoP/0vs4ajuQAkKaXcJAQbI\nuU+KeKSB9MroXL2QMff2/lhA03mMTOwWm0vmb9yasYRpU599uZ6x/REO/SJs\nmgvH6WiA/C2nEQTbUjHDL5CSMWUDVfXKrN56KwoKXrzTUxI5p5JikaljudHV\nd9D7Ufp8oRgBJ3VWqeAqs4l32MYir08WQBWX/4oLGa0qeS3Eue1q5MK34cV9\nWVKwXWjPU3yGm8Sh8x3o7JzzVwFIoVE4yg3ansf2dbHGD4mQWOlRpcyO8t+z\nlPLYnJQCUdJHhDRATbT0iXxVP34dvM5KFJCLpdkaO3exkFrAsm/llRWhl5gv\n2aPX7siw74qm2EoprFdivkKckA/cUey+q9/0/DfhGwKpkv+8ycSCTQtR8xOd\nM/rhMKgzVeRAV2M+Bo+k/IQ5raAQ/qkgFPz7jDN6CjfBD0YASGwMzUP9L8pZ\nNr2oI365taE51Odc6Pk6sZwsdYc7CORJR9zP4kLqSMYfDeRWXT4PaROF0S2e\n1ff1VwYSmJjtHoxfnu1d+T0mvRtBsLmxOIIIThGACaJyWU7ifqz03RqOWO9I\nAfkxVlHopq3kYQE//2hCm/R9CeTGzJlcHTrNCricPP70B3mEl2iTFXjLkQom\nvGkHXLi1HWNpOhPBtUlKsqMwEF77i2cxNWys8rwrY0pZParqNPIJ6YefEGJe\nphXW\r\n=gyJo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-scroll-area", "version": "0.0.6", "dependencies": {"@radix-ui/number": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-callback-ref": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.1"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "fdb1b16f9b319f52c083382751b1f2a716ea2571", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-g86qXJ0a26WCKX+CNcRgrZiViuubrCMvfvTe1EnvS9d85gmv6qLxf/LjNsTis5IzW5smMNQJODB45akoB/vyZA==", "signatures": [{"sig": "MEQCID0HfH3K+StPJIR+FRF8n/KgoD2rUPhf6TgVJrjhWP2yAiAyP+D1EeJaiA70rUB34KvmMp475e3JkRqUZc5aGiOMDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 362000, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VYCRA9TVsSAnZWagAAci0P/0NVI3FKIUdb0KkV/1Q2\nplh3zdEbXcU2O5uKDmel2LGtYtqAgwPmEHeBVhuPbJ4RcobbryZ25N78eB2g\nRL6+ouKputz146Pc+NT5kkSkB1Q3ASyo2+k8ouLcHAIgyctcg6v8NzzrzkJ7\nf33gklhzyIm6yrMTEiilxdkKl+XkIKchg07Q1en0jK95GnpDp67QTXSbMhdz\nVjSMUD5tIfOwfyCNm4OQ++wCeuSyR4iejIozBIZHup2b69/E2Vs/3IVr+Hkd\n34c72KSw6u36e4yl+sOwLpFBhcJKo/XIfjz6TakCt1H1kEkRE6+Hho7/7QoM\nZ7ZkwrMqnx0En5lRwR6dauZpbPBAz9CFUeb+IASnKiAtAsChWjLi32eN+dcL\nbBFN2NG+qe0K1tpdKjiKg/ZpWzxe1PZjVsd9x4vYlSIwaQ+e6T8ezc5uD441\ndESzsGXGbOMAbeNCdIQe5mIOiFgus72/hKV3E5dZLb4bZRY4n3Vm6jCsDmd0\nH2wrUJUPx6CcC/jLElt74GjxxT7xKTJPulATrfRdy1cVVY4VtAGhEChyrabZ\nRIMc7jDlnTkNXSxCn/8q/iSkwvwaNLdyYAQBQU+G5J68mKBG4TR2PC/YITEW\nHnIUe3eUdaSGE+rZs2i90aH9WKOUOqwjNSM6SS4Lhky9vDHPej0RFLQPXzpg\nLQin\r\n=AibB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-scroll-area", "version": "0.0.7", "dependencies": {"@radix-ui/number": "0.0.2", "@radix-ui/primitive": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "18cca7d72ebfdd79d6e7dffca7b9c0da3e8e1db7", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-wbqpw64z09vpK1jC8IP2HuCAyZ9Dj596nKZRMKo+ywKvlePbLMaxB5V19UHzScoxYiNGOrzetJfIy7B24dGJJg==", "signatures": [{"sig": "MEUCIQCt0O64PBTouAl0VvFj8hhUTB8slG3BboA4U47Ldb2+YAIgInpzQ5XXDvyJIKss/Oc61zfRhaXFN9XGbQrkSqoIEVQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 369733, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmPBCRA9TVsSAnZWagAApnYQAInHCfrd40mYbgtdPh3j\nPO4MBg4oiHsIseL3+ZXmX/iLCvOUFM0xmU0yhLQrtC3bfm894tzkFqypWVfQ\nsAS7keqS6F3W7dgCs++hTW615cQArOo8TcJR4swIS9wNbGPsyQzCqNiS70km\nOipOUvbbHAjBLI4tZHpSHL//V9pHvQmuenrp7SX33gjr+THw7nX7xwIB8CZf\ndtihMk095G+ROH0gfLwCzk5DYgdSLxZBB8+frV2BQlxSwgdjxiDesfDX6gIh\nedVNDWFNS4mknFai1/12yITS8tOnDUT7iTnpuoptcHXqKbs/Lkg1ubLno4su\no8i/Zcje0eCycU2vQU+rHIVPKqsFucVkYSZT4T+09kvH8Tq8r1BAjUo0rdzk\n8M+90ylV1LAPf/X1MvhWScyRmiV70eHkuJR7++d3dWGA8dDYv2maWgTfAjcd\na10N9tFATHM9QUDQ5Otv/fQzTLGOVeAg2hXvVT5pCm6MgVOS5fuTHC9uinK4\nTNS31OyMy94sEY+XpWCCLcS12wtcvcO5HuuzHG46SUNNUGFc8ocSjBkgeRrS\nJZ34SeThmdo1yJY+3P67QHsuJ7T4x0Fo4z67h9MaHexveXGKPEFcgAPt/2Ft\nl++2Z995TjBVJOMBDlhKLke+hBC2BkDMruonVIQbqDYu89QPTb0DChvFvd51\nG0+F\r\n=2iS2\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.8": {"name": "@radix-ui/react-scroll-area", "version": "0.0.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.2", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-callback-ref": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8b82daca3fbecca43c14b5426d6b812c8cbc9464", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-Sff9Czc/vp8IUtBgBQtERFvzwDC21jEwkD0T1GF2YKiDYHQ77PPBafdyPHRXq0LxIpG4quVDTo6x/+wvalfNpA==", "signatures": [{"sig": "MEUCIHmIycqoHxClIwvFewHSNEQQZAOXLX+3AwagqNHGT6d6AiEAg590BR0ITetmv2ygnXjDDxXRIvvpC7sUzaPDR/KhTpw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0g3CRA9TVsSAnZWagAAsGUP/RV4sySS352kvIcjubIR\nTFB1Nadtb1Tpzj/YuxqjZ9G9Ib7O4/XvL7CR5n2DuAwIeqisyJdUCkqjPGLX\ngz7g613C/hn1hg12X+G0dDDND9rj6yx25x77z1ag4YTmiMANZ04v8A08nNAi\nXhkzNkoZCLm5SdWOxml3XfF8kB2og4niXlIYooxwEAK3vGwkvd6i3bHMyHy0\nYXnEvLeQ8Ji+fnJ++yH392lDqRN866qLMfwpeCyyAqI68OiVufAnwLUZTLkD\nsIU5Qw+j46ozzCCyulZUdFPlD+/k4akIuvzkUojMkEF3mWlx/WklrTyB2JUa\ncf750UDRwmsDAeCgpCndU0DVObwYr8Hq3aT8+KwW8wqquQZhRVUhumAEUOcT\nu8CYten6mRjYQBg3ZA0HCgzBSlGc/26YIzzjIgCh685WRbfAGDN0OkORQ5uM\nq3HBagLHMnKjFPeIXu7IFuY9fyULXVVm2qcnscBxvdDWfC83bq8y2tzAMsKz\ndVZ0ekCzXnATh8Y5QsrI7KzsmGcy1pHrBLP2pMAHXm0AkmjLif018hkocE1E\n4gsoh+DDKdLwatt4BsK+dm6WlHH2lDHIFN0EdextKKjixpxSxj1OjYTjrJYi\nJ+g1UiulllG6s9jjmCHKU78IhnX7hT4VI57oFgTPTp2ZvGP74wFqaunOwvvT\nrrXl\r\n=rlIb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-scroll-area", "version": "0.0.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.3", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3", "@radix-ui/react-use-callback-ref": "0.0.3", "@radix-ui/react-use-layout-effect": "0.0.3"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "550971e809f3e7df0b637e3cb428fc48346815c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-goUF0Fe/GgJtYZQ53T31uGWmM11UsVudjoWaDMgOjzAMFCgzmotzaXXwhkoFWmUJMO4m340CDTQGQkKl1LmCIg==", "signatures": [{"sig": "MEUCIQCNc6iWr66T9o1202TQkWanLKDIcvRE6+1Yjoh+aK9KgwIgMd4KLgKpZd0W/fBk9wbo9H1XVmbx6FSL1utTVkaVWJ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350901, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1H+CRA9TVsSAnZWagAAcIAP/0bYHkHKLuz0l60NEBPP\nbLdKs+NR7/oCp27cCnwepgJdH5egqXcsfHwrUh2I3gbpAMcDqa0b8qlZXDFT\n59xfxdLi4q1sWO0hU7/ZqYTB5W585/ncGDB/JlqPp3BPvE9kB+oALrymnkC0\nsrMNGd0VcSzmPCBuoqhNrzta/1VWfmrW8bk9zSzQuWwj5c+409vFGMD6149a\n27P/pneVtcT0DGi2vQQYOIWOpvdcAmtNua17l+Fi3uzbeWVnnnYDN0gZ2vd3\nD3tV5JvUBRlRrHo7LNHViW7GEmjLf3Dal4oGJjldWI7ZFfqoWQs68+XHxUW9\nHP61M1vfit1M9PrB5QV4ftbu7tWygP296KROJLad0HlGfQCoa+nztUCJgv3d\nugKCn3MxwUHO4IVh/3+MwKM3ndq68O7/yfYyBWjXsSGMHqM19n1qkxm2mS+A\nSHpB6VxTMQuDKH/iBkEw3/4LlRGEzYU/T56bSjr7ooiulFYhqEpaR34jA+OT\n6y9HBIpXHMXxfCQN3FcTvN0x9ePSjxS88B2TdXp8wOtKNlqgbp/kjOgHaBOd\nVY25jbO1AK0ftCHHgTlIv75/ysL6nADFUrIC/UqOjJVjdG6tUVy0iLBQynqJ\n+iirP0wxAK7Yp1Dpu3rjWHiLXuS43CzRv0c8ji/Kh1gW36nDFAxQxibCadow\n47EV\r\n=Cm5H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.10": {"name": "@radix-ui/react-scroll-area", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.4", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4", "@radix-ui/react-use-callback-ref": "0.0.4", "@radix-ui/react-use-layout-effect": "0.0.4"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eff195ce5f6dc0b0079d36f183589b5180829182", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-pV7Hzae1pCVvliw4zuozIb6UeMKDmv4DLhhx4uVHbEZAPTINREQpSqncek8fU6BKAFhBGYnPrB9O6KEh6bVQxQ==", "signatures": [{"sig": "MEQCIFkiBMbFq23wt9753xA2pH75ycpofxsHdbOsz8T9sWd0AiByFqJkKOJN+8oMJPXRTj5wZq4YVtz1QvyXuj+DbNhJ/A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 349945, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3v8CRA9TVsSAnZWagAAfGYP/jAKNHq3LjW/gqvqPyV4\nqU2KDpaAzGn2SbF1U4zEk5sX20AKogyp84Ujc8CMt9XNXE2gvv9AslumXDha\nIa5ysorCbkdjpjK3Ht7LzWGrnsieNZ7LOzySDsWv6OEyg/VD7h5tV/jrfdqD\n1tfeO5dyU8tBOnsA1fiicw1SVbYF3KwUN2RoAyubKdOG+S9LM6C4OnH9IIio\nTYlMBUp+FKDkHs/l7qMg0oExFIw2ZPcoLuTHju6ADTbMv2MKztvaKWRGKcOH\n9gG01uLU1fU4p2u7xzmfw0n8lro5HZLu1A4CR+cw3hsu6q7iMP7Qf33FYwK7\nQCauAAKwpggJyIbQ4EtYNhojqpK84QSqtWypT+Kt/QXXV6Egc8bP65hO6+Ee\nLz0A2VOvZ+E8q564Jil40676T+fFUyjJ/VFXqU3KlIK3nGOqRYAQjZMPKRCy\nuhldzT/dFJAeOjYvE+yOULQKFquZ2cA4KPULyjTl6/xRp6YU3G6Q52DBmjjF\nxqsWFTgHGYYsOKgPVGQDn0nHcWnYTxIboaW1L8VYWTnzU7PlXUJ54Uq//2Hw\ngtIq4mCWvSXwIIjZHtwB5hjqKDA+ThaoBRK3DsfZHNSxmhPjw1TR40lTV5Yh\nfp/ytL7CI/x1vnNYrH70jvJnQO+t+G1LCLih8phuGDWXrjenpL4LSPYzO+Ok\nMaj0\r\n=OBmY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-scroll-area", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.5", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6ece3eb094cc1a954f81dc3f6215136dd11b85af", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-/Uv15PZvQ8ZrtlF03RVQ8m/S4+TLxZm+W99IDKaBkKTuKkKCGvTkyuu8LiK72thTppBA2Q8cmUkCsGfzZmEPEA==", "signatures": [{"sig": "MEUCIQCGPAGjGeZLoLDer+64WZqjkV/jXOkSarSGO/CbLY6QQAIgFFyYafaQSIUOrhyhzjkrxPH0k3GI8KXQ1KMo6e507QE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmvCRA9TVsSAnZWagAAdVUP/jj16siWbQUVsswzReQ4\nzoI8VqdUmtpccYDYfLovSW+MeVunpymYXcs8iyRv4XTgXk/MSqN5Cn2jn7Ql\n7Ltp7jh4pifdqeWZbnrHUPnR/6xQDtot63T/QLo4JZf8dfMJ9B4b+Jh++VVz\n4Sye6Vh+qz6Spup/L9RhHJAptMvo1g4BSMR7cVNpIkTMC9EJgPJMkkKS7y2I\nFu1ZnmMdk7omwvmxTbJJrgrwlNMf7hPjY1JTzY/mUduFj2DJpSsDfOxCobIz\nvDFfuyTgz6U0HLM0GsRvw5Fbgo23IiWbbtcB5uTaV0dHe5jzIQ6jK5neqGzY\nqQDi8FQv0E9NWD2dYEIxVd7d4m8B2Bcq1aY4gIjE/NFevOg84RyDSmFC+5tz\noITahUaVjp2002Tnioql/GkYEcHrd4oxREgKGcAx9ehKi0SHSthLgTrupaSr\navqjJSL04bUnepVV6IhJzwAJZyGL0yIDzb9pRLyi69Z6CORkUbBsUh56GHne\nEIurSF4QLXTPcEY5lBTHb754nffYmWNfDNgV3W92Ndik2Aagp+vlVTBSHyqu\nZEzw2ogBfcZ8/Qh4AD97JU1yrw34S46HnCIlVeSK9IlR3Czwt02zo9IsdYRu\npULvPr87/SHkYrvVqVZH5/NqOwfUzgZBrjslsvMY+JUFbq1d4JnWEZk7Ykbi\nttzC\r\n=vgyw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-scroll-area", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "944fa58c97b1f04f27090926e5d35f0292e0036f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-4JnATKuFauFbVyMLNHSDjCtrzrX0RUafeFKoxuHZLYLO0w8mIJJfwKHeBMWBGpWju0CtjEX6SAjIYa7thOpYdw==", "signatures": [{"sig": "MEUCIFFeE/lHvqroxzG8IkIVxLgWlQzGIZwFvQ2EvuTVyDE7AiEA2im1dHd3olp7qDVPS85ynQWbWm8ezfU/MDa/Wfyi/Yo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350168, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7YCRA9TVsSAnZWagAAwQwP/1A1+pjA8NKXvLo9o+2I\nOI/HF2BfwQjDlmV1tCSUBXOS76vH0UxWzsc82aHBNEW34CKSGMX/EfK71WPv\nt1u13s6y7bmW39CpBxDBOyMqcHlnMb0/sAFe7jh4dTzwOV6h+3TR5+696riY\n/v7hlBzUEyo65U9MCx7elN1rjesFb0VbTOijJXlc/aAIoQtGJSmtQsrStZzN\nOmeDraB4Pom9C+nI8mQjstROa4CSXjHTuAbjNDD4/HDuqrC+Erb2rX8/Pgvd\nUgkZcO/OEsVgZOXLhR8wQSHJDinXc2vYt47EdkOCAQYnXojkQ49YbYJVw6mn\nzuutgYQEZy9OA8iZNknikodUCz3SROrUC4jriTNDamCKhaHS0JahnmYJExFd\nW52hMbFC5wtX//wyW9gu2R82NDS1+B2fNUnA/S5dhgURDiXy8pB7MPnytHki\nw/CdHMfnnm1SNgKSrarvVdhM3+/bfJJPOp5ekvg8XTaqbIQNTxSnODTYDGar\n2Ry678NwXINMYzxrorRQlJJVeD1Q6i7AmdqCjKKW7MApR0JGZ6waRw7onEqM\nl4LFwbSclbxWxG8UyOuSyThi3CzKwxDzIzqTdjbAnR13VSt4Bx3lMnv4Tml6\noLfZOIY50FLfEkoN+hNVCw2hEpHBVOiHFJBSxrWpD1FOl8GD4IOpbB1y4EPj\nxPzQ\r\n=+sst\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-scroll-area", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "devDependencies": {"@stitches/react": "canary", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9565a43e85690458337f1e565ed4261dbc4c51de", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-qblvAuG0qxQf8yLAh8l4RvN1+X2XMZRsuE+SnMRQ2/PbCjbPTR7QS4R/7DyJbSvNJksKpaC9qvbawjg8b+/B+w==", "signatures": [{"sig": "MEYCIQDOpcQup4kuqvTNDbN9NzHRRsi2KLIrMxPpURe1yxGVSgIhAI9+JE4sqV8HUEbGY5e+ROKyem7lYdW6Yt+PmYOeP5wu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 350829, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlYFCRA9TVsSAnZWagAAaWgP/0gq2eb/wOdpP8udzBmi\nnlSZ4dNC4e8rjpN0T3PW2xQgm1TS3OumsmxKj/C0WZ9sB5x4jNvITiCDkc6I\nYIAalbOGDdaDWgbi8obwsM/FODau2f6q1ZAyRknwRWvGG9vAf8fp/e8SZfip\nuaAp4QObYMpkTBidF8fz98+M6pf4Jyi8v6Oy6ATAIECxf9YykKzgLegBW9Md\nbWPJYjOhFzqQAdEXX8iwPdLIeaqZ6I0cRUzJrcnHJXEEFVV+ZBl02m38Zi+S\nON6S7R/9liyrxjPt1nIHhk0jyLt+7xCaVgJz0nh6fOnBiULAtiEdWef/yvZI\n2BI90iPZ+X230jERe7xTLeXI9XEgXRnYQ+A91j1UoSLxxkj6G7twJ6LySENR\nR4TLnSQtCvJtSL2T5MGCkN4vYvzp8cSwzcAc0QQy0DDE6Tb1l6Ewl04sPySc\nkUU9lfo3iWYGZ6trAH+sa9L8TT8CcfQt9waq9/cKrGmw4UQqY0/n5nsTHRtN\n9IZODuFP3AUxyZlYHkqjEL0FuHmdhsojtfaQ9Z1VEXVtiClJY8b+I86zSn0i\nAeHSLS/V7UZO+Wq6Uy1gXWJMH1VmbuS9azqV6z+vIvhUcEIED7b98qr+8/bB\nWOMHDd4qrfG9LYBEUCk/xGo8wz9P4PfQA4FLVL39HmJ0qaT1jM4FuAQxRWEQ\nKGD4\r\n=FMDX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-scroll-area", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.14", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-direction": "0.0.1", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c81b3e7229b8035382aa8d6bc8185e34cfb1ce27", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-R1UQWj49aWt/Hw79Yzx3EVTP+AWOkadbJHgPaDpjxHqb+kZpQFTWfrJ8LPe6qy5FcU/x8thEEl8FFMR5W55DdA==", "signatures": [{"sig": "MEUCIQDW8EuW09ucPyIJ3zht8YEBh18k+mzS/YC5dtAcXt7/1gIgOjhafLUiv9sYK+GN3ayCKgP1HFgJh1oewdJibP8dg5U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 157927, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9zCRA9TVsSAnZWagAAjrwP/2EQG+37eKiWTMVrWtYv\nI6tQ6UuOWrJy6+JJbYJfIWg6+8zqeji9vASGreJSIADGkVOpD/fm3l+fVSc5\nr+wTZzokI5dMjz3G5Td0njsCudtAlQ++giKDX/RTqGIeWV7Zg8atK8L4d77q\ngQGRy0Nz08WT5VZtqMT1e5lDbtjkjOsOiehUmvkFS8SxtjVKa59s4GyuDlg3\n73i+KPKo4kmpCmHmf+Kd57Z+80bHHxibGA88RwsXqQGtSjmyqBmBZdWTCqT6\nswIl5fswwqmGpkMe0lnCIBbt4qitC5H3/Metjojfrs3A6VHImf0pZ5ZdkCsC\ngHYGtx//vcQOJkaTlsc35J1sfRkzfIw2cM0qRKXam1LnysZMDMthXTC7Vawx\njZCOKq01Nl0TA26nweP6WpX/vHHWgvwBb/puO52iCKalXkfjlAF+0AXPNHbe\nYSw2PGsz/aRPAzbusDEdK/8OqJZmdEexZ0Sg+PmC8qE64O5T3fs0QflUvtmR\nTR+N0Z8Ii5Oof03KlFaGAdDDX8KPjP74jfe3+WYmrYVr7QErObtTbrVRO02a\nBfkrYnMCUAYHFp17HLHauPuJISreqMzQScrjboGr3cbGbMmB6hqI2iUFYPTZ\naobv6IERF62cV5LzChmIHrAr5YNSbTnRTiRUMreJ9gt6YIFEa5GY1aHkS97U\nHBtW\r\n=pUOm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-scroll-area", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.14", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-direction": "0.0.1", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "67c68b729b627c1e395d1d892aed983755a7a120", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-MA/S7pdhs53F1isdos8zT3M1QLIEaOer3D7wQev4DmLlynG7XWNdjeIQ1WIos/jiipeJH0KFlSjsEy+T1si4Bg==", "signatures": [{"sig": "MEUCIQCupa28e69ndeuQilUxsc8vu6FtteyM2h773qZSF1ButwIgY8eAGzAjqHcrBArgh60iOmfO3hQA4kbSXCDiZajQQhs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 155809, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg1GU7CRA9TVsSAnZWagAAwxwP/RAccBSmheXc1AZ8cJNn\ntq3UpIF+hfcOqq8oa4oynIj6KJnWjvsc5u4+cxpcvk99ffdNFIdCPv8s/O3T\n79gbaAnOH9oQ4XmQ+nABpkeWBPrSy75sSwWfVCvm0QX1fNVdK+rb6bk4d+sy\nDSSeSPYTjMCjGqy9sUnu76itQRGMRX75NOlSLIDmlMHLtxU1ER3FIVPEEguI\nDgHXL4kzUt0YW3T8FX6cJC/h5RJjDgdsmgi9B5p+9UxnzdlOcjrmdrIgsYPb\nMfByCOSpjblAo9JLbpwXiuMoUgNaH9JdFwMT+N6YqA7nqSKw7IzAM+JQTtnG\n8Co5uWHLtkYartdgmvx7tNWsEEkAC+IscDmC9dRHcD5L39hUXbEAnDr5HTnj\n3WI45NHBwT2J1Kk9vXDQvpbbULnV2YWuCEtgCGUG8I7Onyl81F4Mqz0Jz7t9\nUs1BqR6ogpunOqjhlmWzHxOVKML+BY6yo9k+0HsQn5u6Z9NSMMtnmvRmkv++\nk6H+F23qbV3LkkqaVoxt1kk/Ahne98W3/2hTIM2dlzTZEDWs2Ns511Y7E0d9\nUo/MxXFE0Ew7Vl0J5uuKko/6OQcJPY3ykpdqnJB6rDdwAxURXrw2C7fF7XNl\neVN9oagbflfg08RD3ATEppATqAWa1q7NQRQVFudQISeJ2KEB24qpRkDOJNmL\nBA99\r\n=G/RG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-scroll-area", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.15", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-direction": "0.0.1", "@radix-ui/react-use-callback-ref": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "21aa93288acc3a888545d09bf3b2c6f25682acdf", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.16.tgz", "fileCount": 8, "integrity": "sha512-GUVT1DtYhQEryMpNFdap40S6pQLmYMN9avDUFZy74zyz3gEy1y7hx0Dp/WBINPy/pLmzTaBqJajzVnmOJjzzrA==", "signatures": [{"sig": "MEUCIEySGYfSTEPgOqMl6vvr53G9AvRy1Q4hJDVA/b4Kfk2UAiEAp/S++53MV7ExNb1meKV3laLciUi9fOSuvY5iYxRzZFE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 156633, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTxCRA9TVsSAnZWagAADecP/AxiKNJUtnCl7fj47RKV\n0nDGxebMxUOeNr20kYtD6nwJEII1r2UwMaxN0pSP8xBRP1hEUfj8gfZmfNsG\nywfpibHAyBdtgJwKILbrnxg3hW2btXrp7slPmgDO1RAx3KRPKO2DcSNdXq2/\n5bqLeUdLmT0NXWEBiBfrEBn3rHR0nIUSA+/+UWAvy6Nqvb7bI4xolpC/XPQF\nFVxHAu2Q8ifAw3b1BnxsPdmEqtrklib5R6IsJK5+YnI7fxzCLkJ6KbGPepNI\nbSWSrMLuLQu5HlgCjO8y+g+wWqZjz820IlKPemnJK/YYlKK7XeXFmowfjHPQ\nkxg9xW3OCo4y2LUfqo+hteClgV3BJb+1nijg1ZVB9eiBA9UWJXf7NZoO39yU\nZONANFHqYY6ox4/trHkfDolhdbIH1q2eqAC19wQ9HsAHGErngyKnwFKhpTGh\nb5SY9wTPB3eLWxXFCFZqW8XAGbwyB3XKxtVTkcjJoxpo2eT0xRifVDaRWKxY\nJwCKwMhEHw4uAgYfu1r0cmSvEmrD/KdqJyZuTZ9Af2lo9bV5JzcRfNKEaR/M\ne9CxL0pynY6bdsauGAvE406DspYrl0oHnDzG5L4ukuyhLscGtDpmxy/rkT0G\ns4HwppsEE5mWEOQ1w35NTXMEGFVi1Pq7sfV+NhLfvxU9Ph8UG6l45VUS81j8\nkapm\r\n=y+9Z\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0-rc.1", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-presence": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1", "@radix-ui/react-use-direction": "0.1.0-rc.1", "@radix-ui/react-use-callback-ref": "0.1.0-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0-rc.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b58e47e34598ef123eb9b60b4f3be250516440c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-z0+YxRQe/exe+5PzQsk2cVsQOMNf87yCrYBrbnIbnpankwn4TnEH8zzmAzU4ZRj//3yir4nJTAkSHWnzj80XFQ==", "signatures": [{"sig": "MEUCIQD77eXfKUNNTZ3Xqe9VEhxGCKVzJ4VaHRPzzfaI3z9cIQIgMHMtRUKikHYLgZnGYQDV2AFXLtC/FY48gAwMrnyK32g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1490, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpoCRA9TVsSAnZWagAA1MQP/jyCYcoVA41LIVWlq+Tl\n0ZAd29ybr2la2Qxj1ITdbWilkNGmmVdnYR7KJ6hoXIjx+cZd9yjTQrEFAp9k\n2i+5tu7xAT5E+JKaJqyLMY3Tv2KfV4aXL+9jmCDFSGyhJCAhJTmASKfgv/fU\nkQSlGbEvGe4pl5QZ504y+yVw0GbtUdq4j1OllXeK9LCXBw3V8F+LnBQc6/TP\n1MG+OKzP06NXN8kC+Na8NeNhElcrlPzapb5COe9E5iEZawL77d0vhLZo0Jeh\n/eGrmXfnr4MZXfjFXow/bV68+NlQ1sSwPHtaY4z7vnKIIFIeQuy9+uF/bJQK\nm9N4CdzBXEt61NWsCBJPZojSdbhb5sDgY3SmXvcaaJ9OIVy1wPt8Fu8YsDjh\nJbUusNCX2PpTFy/9iAVNlN0P3PbQwJU/Juk4mu2MhbvpV6z4JKR8ZRP1guPC\nesvVyjgdr7gFiuc6QV3K/nekOS/eBQ4eNAtwC9X/C3CYc+zG+Zo/ltNuPFCM\n6gFbw9+i6n3NmHUIyyh0FqHeoqR/RwBP1ieYB0gvpMuqvBMhWxpn0ExuISTg\nVaxQKooPoeWAog+/QmDlUHGc8+y8yZkd4tDmVFpVMd1hapho0+Ml8+RNjD1M\ns/RXIMcX1NPNF9lEVk2m4SXVPHyyZ9EksEd4QLnSeOVEXhyjvqRHuN0LIZuk\nkQxL\r\n=xLLE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0-rc.2", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-presence": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2", "@radix-ui/react-use-direction": "0.1.0-rc.2", "@radix-ui/react-use-callback-ref": "0.1.0-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0-rc.2"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "cb4d39fa1ffeda4134f5c65b126e86b07b53bea5", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-fzFb+HNoSHURgpANlkTiHYnk6ZCOOq45kS2cQWKT6FBaNmR/bAKUTk2l4JL+U2/+i75jpiaqHaS8z1q/DjUKhw==", "signatures": [{"sig": "MEQCIH8Hv7QggZvWNfVxQMb/FYqhBJdFc5ZzbvrJDvYECJ+MAiBuMY6PERCwfjpAbb9dbwzj4jBe4zS6sJwa/Z4/mhViGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153788, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhydCRA9TVsSAnZWagAACD8P/iKjdhjdJofhTROBHtfb\n3DR8+nWvDLXDO+roU5CjH14mnLpgFqGbbfzxingpcPXBm4B/mvx7pqU8wyko\nw77T+MmPPK554vNrVsm85AtTE8zEdxof9TdqFNwZ7jrbKb57DO7AGEJ6kjIU\nOCiH7zKnz5js2BeRTYpIitxy6GeJ7b2rDfaIp1L+hExEE8nuPgTvAIxOQSWn\ntRFwSReXMV938aly7VkGTgutmpCQ2fKK7Wl6glZV1eD7CHMugeE2C8Zj0eJn\nvsZg9Di29sVG5DJGEwOSTeCJqvsQA1ZQRV5bKysjXiKlE0wFvpAXa6tYffLM\nHOpAiLrJyG/DITBeK4TmQFoOUQnS4AkEcTUxgG93pTcNjM8KYjMzNFYMUWnb\n12SrtcrKI4DGbwSPZaWf1Qo7boOM3q1HdvNFdI6BGW0gZPcbK1Yhv2QxtKVn\n0BWXglXXPVDZzgD3nGsNfimTiWO0o9ax4lJTm7jZPw+ZrTs3JSAeZ+96x3W0\n8zPHMKg4ggI0JeBKZSOQVtx8fZcpsCW/1QgnMxFE7dNHDs+JjJmOOTj4VJ30\nmMcWeM8SDwkRmX2s8lyeF61iu8fHNgu5lkDcsI1UyJYIUCL8Ts3RgELu8Sgh\nbrahCHuzH42IY9bIWyy3j5TgIrwQaXiBWkfBJNitxAY5fibngn2KzQc0BE//\nOsKR\r\n=mrD+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-scroll-area", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "70887848e9ea98f178e84227d1cca6bd455be14f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-yK+lqXGYCmRbYoI0npjMNQ8ah6Naz7SGfWokcQvO44Qh4dQeTshIYgSzewgFWCYMgZlSa1PiRHvJinJYUSlyaQ==", "signatures": [{"sig": "MEUCIGDxPpCk7HJ7enzH9wfaASns2xCH0Dq5eW8l2Ao//aYHAiEAoXb8JSNI3i68zcIulBxG6pfEGx7fzW/oJMy7m8bMT4c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153709, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmmCRA9TVsSAnZWagAAlgkP/2rKUBYEA88FwVLX+m01\nNWAUm75o6ib1iejm6CBW7qwsTxP4Aes7HBhjOVBuDR+4gPNU8xbG7XTYHeJa\nAjaytcX4Xi3OEBIQYPmGn+ApWjXtyYDscEm5Vf9zexqCkLFEIHgcP+f6XdyW\nb+5srqKEVK7A0nU4RsIEznxYuxC0nFsLynvOk6+qTyU5Sr43e+ySvSnpXLol\nhZd+gakmiCV+VVrCxuQ1BcudyipZ6//sCubk/Mh4Uf8oWYzTIxsA9PP4lxIW\nBSn0MQJumxJAupQKbyugw6qBOc3eALxtagVzquP6UNyjsTatYH6b075jWrb6\nqO4LmYt2NQSm+rFMMwksI+yAmCH9PFhz3PSpZwwzbBlQRSlaMO9lFPIhd7EF\nKsoANyyxoB47abfD1CuLYfEM3qh5IgD9PXSPLOPHotNB8NpBjUr8udXnSsh9\nM8g2posy+c3kDefldsiJXsMhodD3W8dGzMYMvVVDq+iHkTjudYl5S6bzXY62\nVrW19N6nLo/2lCoDJmMYHLMjgKUCkESbUwofVKiQi1SxmUxh4tEclZWLEJjp\nJiNafB7PMHbv9vBt98YL3T6QU9mOARyr/lgYaX+i8IeHR4rAj80xy7K9l5K2\nHDlevpPoz7weAetJ53edwvToFKD8DF9TV/JHzyEWXn4v/0SgemiKv/A9BtJx\nDHs8\r\n=Xs7y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c5e7b70870b72d614f5c023812fcee2af63a54ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-a/87mCF4MuE9nE8cBCIbUqqD2YcjjN9b0OTs5c11CxvBXCjgJw5UIvXfM6yMTKba8EwM73nSgw1Rown5ParO3A==", "signatures": [{"sig": "MEUCIQDklys8moWpBp/PRSTMUsIQ5x2CSdF7+WYxR71YDQlrrAIgYFQ0pBiiG+MUgnei8w67X2snc8Lpgk1rqwe/3Lm5INg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQInnCRA9TVsSAnZWagAAAdcQAJCbd53xckQ5fmTdOmTB\nBFKkMn6lPA72aaVXOCb52LGX0IwJdayIfN1wAaibIA5gyLoVJClLUbMK13bX\nifC0GXAZK/emzZ8KzGwVFSc2nK53i7p4JJaOhGnPh+uIAUTpI6HtL7eQlmiR\nFEvLS+ViBiA9Th8Xgb/+BzjIlIxHEuTrxLFksIWdRi/VDXIORWOxrtCBvFRg\nfnV30mroGppbOeR0bI8wtp1dF0sr/m93vHD3Nw30ipZapL9FL3fLgAhWOcBC\nJTSSM8HQR+81sSj1qLDAfxUxl2p/JmAqo4aISggExIXUmUjXIhelhIf5RwC1\nXwiiH8cl/F86nId9M22mEBKx5ZYQUQj/x3U7pqcVJrhzunUsMnGA1ppMZsod\nJfdvh84AjP2/IQ9mqGndNAoJK5eszvvo8HcfeLQ/kqHkh2hyPc1G1Yg12yj8\nlADFVaNi3qM6hvbuyTtZAvtumQl0yWJZfsI+w3d7IvTKDH1mVJBgEAvS9UZO\nHIyBNlH3WX6po1+iohXiS01hFKEMfiTBGA/DcBrg4eX9eKezv/CgEom067NN\nieN8e+0v8V+r0JGsfJ3X9OF6lDeZ+DPdybRIHeyMEfLHKpEOQK+98bx/ts0J\nMpegWX27NjBPtwQj9FmSX8SKpii77Dwx2zvYIZM3SMaiQNUlARvDH1lPKtIy\n4wmm\r\n=iwdv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "be640beaa0c139b4a80f8cdbd6e23088044c12e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-AYLtJqsxt677sdH6Ip1e6qmN97DXESdbYaHIJD164kI1/6nNESCV8XZjwEuvlSzQLoT3vQTJWFPRVY9h9suuQg==", "signatures": [{"sig": "MEUCIEo6lVVE/G4rbb8+eliYzW3lXM+EQCMAOWo6oGXBQA/3AiEAmxA01MNeDdsKuBbpUgOrXHn0BckO1slByWpobokTZTM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdwYCRA9TVsSAnZWagAACSgP/0K8s68Wh3SKB+csu2/p\njI1SRw6EnAzps4x1gugcfxW92eg8vAhwZL0yWxqSD71ELkSDConx7mSma3dp\n648rUkWPJFzxb8aK9VCWWIdCQBTn7iWhbq8fETDSZrU6b3pw5agh9wI8iFle\ne+GFzEexXEmAOhqJyeO9zzbGdafSIalSU5huaUn3ZdejjE3nL97x9MjiC3CX\nKblv4N0uXsgAVB6FejytyusrsNhIxT2S+peCflTtMF1yAPhTdPjHwpoFNqsN\n31cFrncWYQuZ86JxfoaxuWc0757YdDYGuUJeWp+Ss77mCv5bLd+54DixBm2Y\n6ZnUDoRWN/2iQ6KjzO44qNKV/GiiW1R89w1UzQlIJQ2QFXweYQ0X3AVzYXkU\nWaYZkwbJJvK8oV+HSlIUYqA3kcCP6sMK2EiG61uIJY+j57vNN4GCTxvc0N3W\nOEHI/a0UEkaeqD0owAT4nBM9Klel2QXBLrwXQ/gbP5TXLdZw4gQV3a6MbwU7\n+JsIZT8BBL+yoLJsqlv66ikChIlRQmJ8BBR+JMuAuW13t5rU+ywq994BtE5t\nUH4hCVruF1OLunZFIU9kglqKM6mwrb3dj2TR7pvKDlmJMCisxL1UXs6w3B+A\nvGCEoOLiS9+4YXMGNJBwj7fd7UQORqiIEY8d00AVjzIxcsqMctb1i6lLGN55\n8RnJ\r\n=+pT/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2b42b971e75fb6a0fc6f378ac9268d82b26216b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-b7GsSV3Z6bkdFQVcl8R5Jxem5zwsqCCB4VCZ08VuqC8f6er5OwE/sqVt4z/DE10ovocQHdvwjptURo80YP0aFg==", "signatures": [{"sig": "MEUCIQChhzIt7F/HuBtmcPgeTdViH70a8fKxU45ktHEmRwIaoQIgZLNCq5pOgepOEKBWc+5fR62Ydj/dH0NE843+8fcCUa8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0UYCRA9TVsSAnZWagAAGvsP/2DB2ZyYgSAEXCs3MyzG\nHlFj3Gdj0D473BkHw9xbusKAEkifXpx/u42iOgdjd3E/BuFS8yd7JqQd8TMV\n6sQJ0AyzAuIcCaQ50x+/62tJYQGNzzAz6sW5AK9FBm1y1zN/NKoxPPTqfQ/y\nfmmPVMiOxku6jFyJE1nHVCMWXp1HRKLzGXH87gcZgx7bxQBW3xm6uVqIePd3\nWUKRfgPszIj3XWxYU7dl+NDtdC4X7Dp2hCvjWcXXAo/y19gxF0ZUGlVnau7/\nI92pJyfe8Luz67lU/w4ftyxvpjZ9aeHWztfBOsD8gMmS+GbHzDk06Uvc65eT\n2hFRy+EkTqvHpfOvdoyuBeLpIHprKas+NgDRZS1BHZ7XZdYyZ5oX6ZZYdusz\n1SYnjyGCCPV41ZJ/LGsQqOh8Us6ow9VHWLwOvIGBfoOZQtKfmTo0rbk5vZqZ\n6EPbDRxofFEF6rxaAMfsK4UeMpD4dbyJbiY2pDz33aw2BuLPbK92unDduT2g\no+c+Wov/lPTQh3b8S+9X9y0WyW8HfDDvjluFe++FFRsTxgePdrUs2aNw3zUS\ne9D6NXI9qLMALpUoVdH8s3b3CKeUzTBDpHW/K6F8kOAlCkJO6o360uEhxHGy\n5cf6sCqL4Q/o6zfftKuqjcUi4laT+jLcsUI5b6rfixRHhbBLoR5M7yPZrrmU\nAWjs\r\n=G8cg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "391800b728ad0e85e6cdeab06f925a3c032bda64", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-TEFULRx46+sz9BxBulBXYQ8VeDzxsaFBLDYmRQIyXL5cGhBB0NNTGQZO2BIKxlv5y1LZ31FSMOynXzjDfHv/4Q==", "signatures": [{"sig": "MEUCIQC9XmZZYHBuqRWEyo+CbNCwCIDq8QYfnzi5qRmDBl/T2gIgHDgN1uLD55w76XUMOit7ptV4ZW/vn9Qx51SWb9yc6JA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ10NCRA9TVsSAnZWagAAi7MP/RdOvB9lp41TPzLu3uew\n3SHIUyPRzjCTtD3XP2bDONae26Nt1sf5Q5FE4MVJ82aC4/9j9izZe1hlt/fw\nobAwpz1vMJsPrHBH1h7lur/RHujgqJtBMtomXx+64jLt0GwXq1cC7p9Z66nm\n+Za8HGhJv+fDy5wLbRteFsUqCDXR6mlqDZ0fC4YgXnvr2t8Cr78b4+hBvQ4C\nj3RLgHAN1ZcKECGyGpTQANiuRegGnZqffxC4KIURllzQP3VJCdCHRY0aZSYm\nfQEmWAWuI1EdiU5Vg1DteacukHkjEQPCsFXPzxRhLVyrEpIZW+7NRF+PQ1mX\nH3wcYn/3KcIrKB75VC5TFJIezcXgPRUJnSW5SNeetqpU5bdSCGC358PA9kww\nOBi6Y2aVLCwdTcCwIltzu4y2Q74+noMh8lhLyOfgBB5J1p40dk9WKZZ7MKvr\nRnnuUL+cDUSllAppQVWsC8dWX72vT6zB4szD3zth3BJ7QTFgHMv6jDVRcFpX\nCwDOQDDBYmJEZX2/CvhAKvjrqLPQvI6TveAbQ2VM+/TjMwjsN5Vk9ySJH3IY\nvTijIJqOgSxEDG32BzP84qcs+uvzBMO/+zQCAykmNe9fqK/3+09XaJ8MNI1a\nR+ZQTgs9ZiIrVyd1eQQQ4nQsY3nxh6Sepbna2T/jINB0tRoWY336Kdm+xKQe\nXBZL\r\n=6kml\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0f6e2cc323c259781fe241e06825c0fbd50eed79", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-FQNVy5JOcJ8VC/LxP+PXqcI6RjZuF4TQLlmPaCJkt/9DH2OE8QrmOiIVQRGQ95OFavf9f+zJDrUtBJjaq+0E2w==", "signatures": [{"sig": "MEUCIQCbgICyRe94Hu6am4PFGV3lWiLYgVtGCxcG95hLtF9+GAIgNJoxpCYhWM5Q9Bs9pUDRuwgds78BRvCey8PR9Ffng5o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153752, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFkhCRA9TVsSAnZWagAA/vMP/iBBVzJ2L+SiMdoOiJNg\n96GPgjZDYWbFuqp/WifpMNMDlVUt8/fDW+bGK+D4bYQZH5KNEC6Xlr3Z1B/a\naiZo/kT0kDGl4OGhW0S3uMSld86RHFeTxvyerx7jNYU8u6AucYXxnYWvO4Fd\nP8jDbUcAW0OxK6VCKD56+j0piYwkXZijhx27jUHllUoX/kgpkwqOnWfUv3ST\n+xLsaOWmAlubu+uxGuFbipj5TQ9tExjGIvqAbzTofuGl0fn4M4ZIe2BU70qn\nEbt9jJsk5i98IfaBeV+Ii/5uGVAY4b3MjMpPgPjAthyfejxvA35uV2bslOyy\nD8sgxL/2JALiffQbjv4cWmLMjX98Fkb4ou8WtuOEZIdw3M98ZzidsCqS7nty\nPyDkim6vulFUAZW4sZS5XaQ/rEmc7dyWh9jGU49qmJP8gAFadRY4TYZQbOzN\n+PdQCMWt2fHuDGxjXGiJIqtrQcP/L/dmFSlv3zuxrCG51BaiJuJKir1Ups7i\nlwf0OlkafA8NKPe4IpP0lcOyM11ZTq7UfLjPHCWJjh77GhlBgf+RcHeZDfye\nK41wkTgzZZnOceNtZmgxcyYE8bj0egJJXgpkaDObK2q8nBcFYIjK3HwrJz08\njGVifgsXpiyck0P+j+j6jlq6fl6ubdiauPH7JN7+k3aNRWL6aTqMjgpMi5kx\nspIf\r\n=sf2b\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "14fda07d69a69be68c3dcec54d819e49f21413a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-OFcDeZhhK2mYRVHyf42qjKd1GHCSuypQuaYa7/sW9d7HO63IHD7o9zcQ3yV/jSECn14yl1oApHB5eCZz2TXIGQ==", "signatures": [{"sig": "MEYCIQC/oNKjvqwBnXahsA81Vurur9BIw+/21qcTl39sVKSy2AIhAMIRKcASL2ZnG5snHs2jIJwQBU+i6jACKQQseAmu8Dws", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153752}}, "0.1.1-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b1f22cd73235c3d4a2248782ff001303a1d97c96", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-GVMavyk07ydZLBPBfQmRaAY3epFhQ7+1ejZP7DZR0G25W1Fk1MHXhniYCKQwqhBfvbJXR7o/lq6+NSn/or4N/Q==", "signatures": [{"sig": "MEUCIQDKQTbdzI/ko8lGcDD06SLy6EbKsxvHqJsTzN9Og4AT8wIgajIwoDmgd1ta/tz5uSkq+2ushomywl1PtcQ1o60pYoo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153752}}, "0.1.1-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "26e9df80253cc8cbb1b46311deb3a3643c7cf11b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Rv2Xjivm5MM3xc0sLd+FJBjMxS97dPeC9dQRb/5mO42dHBpIJpx8gP9+mtf7dPrSAfQi2Ks+oVtUyDfN6wzVDQ==", "signatures": [{"sig": "MEQCIFSDx1vUnI/hJGvnmucrOWl+rPoivJgShQsVcYf8qkCDAiBZkZOxVt5QJffTUYBz4br76TCUJCdRJdnGCGVDXUJm0Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153752}}, "0.1.1-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0404a0e235bc1b2be2251afe2307bf2865d38068", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-y72RbxOo/TkQm4zLFUQv6zdHqxJyuxQJnz9WIEme5IPjw+MOfjXbU7D8VzPUR2c+kgf4jJGSCdzeDB7AAGr0uA==", "signatures": [{"sig": "MEQCIGvEAQhJ696o/u1p+YyRFNAbeujaUY7Qcx4+FvpdwlrWAiAvfDRhDZk2KacvRBQvoQa0HYf3LUOhvDV7BNP2gBUlGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153752}}, "0.1.1-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bf0061c678d33df83b195f065acac56a654b11b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-Fn1rQ1SjywCfxcwawAIVY2Cahfvs4vy3648BfrTKrX/Xe2L8n0hwrw7H6GKCSFmkm6f/oXY7AKKOTkt+SekICw==", "signatures": [{"sig": "MEUCIEkHU5W0wuw8GXpcfI1McQ9Cy/+TFAyhCKpArRNCs7uTAiEAsHHgXVk8B7jzsAkrgVN675tWLhyQ3wkY1F+6uWhpfig=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153754}}, "0.1.1-rc.11": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.8", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5c43b3e02112b90de35a493a2274190c235d2a92", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-WnyGfLQ/vgtKsfYagIzrfAJ9VSaxWtMtQIAGRrPYqNY+S8gDwknwOwIm1mM9Gb940njMM/P7MeyWM8oN9Wznsg==", "signatures": [{"sig": "MEUCIQD636Mh+N1UrHL8W1hhkahrtMhczpJ1HfRYSK5bEhSSowIgOYwAZFXpguHSoyDO1kSgi+iYFWq9HpXHxw+h7Cybf4M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153754}}, "0.1.1-rc.12": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.9", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eef12bc4cf222b28a009cb82a563772f70d3b853", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-yb/urd0sWpvO/Oi8g3javgFA3ARZsAai0DE/NuSdlPlFGbjBkQ70/9p898oJuzu1/QdHn6VZ1k3ea85yNCAjlA==", "signatures": [{"sig": "MEQCIHEXhJeQ0yHAK5t5VnheMTVSzqJBpNnmh94pk8yly24NAiBRMgkxhQtlBLw0MP1I7D0DkSWAF1IMXcl176pTJ+d6dQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153754}}, "0.1.1-rc.13": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.10", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "93e4d15bd9df653c7149b84d4a24fc310cf137d0", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-/d4CIPybjylzDe1WzF3yaqWZyE97r4QNU5FcG8rT79wXbWVBv4j1cLZ9ulqOjvyx5LequL7+aHVvuh0Dek9klw==", "signatures": [{"sig": "MEYCIQDb8MXTqO68WqCm41KFvJ0HCHxrCWjEH0jzWOE2IctCIAIhAOhjJRSbrhjZ6q6f14NQqa+zckYVZauYlzrggnq23ZTZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153755}}, "0.1.1-rc.14": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.11", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8d60dcff0970dadd651e79bd3fc5f7264196cdaf", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-UBYNfTw2aNai0P5LuEsT/3rUZp8KzAmHZcf3FkMssOSsZEG2+wE4DH5RjMhpyYHei7VmKnRR7HQ0wW+fo3/jYA==", "signatures": [{"sig": "MEYCIQCVm3Oz8IvEHC/Lxacu8ipW2A/D7qAymQcwR/Uge5ZmawIhAPCmzYf8NT9U150NQl2q+EyK6K7gxFLHxbebBjXyUKWk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153755}}, "0.1.1-rc.15": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.12", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c4a869c00b0b1b6b8a4f7a6a27c73605693e1843", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-DFYqiPAM9xmb7V9BMnOBX/wqSj7zvykdb0yqvb1P60taq5JKHeJQzc/AbiCo3vfF6TpzSTewfDXxAhehGUdlfQ==", "signatures": [{"sig": "MEUCIEFbvR1l8i0CxA7Kp6ITuoCyTr1opqKq+9zsZ4bxDzPeAiEAmk+EhnxNfs4+bikvAT3oJtOMcC68eWwKOQvOe4uPYvM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153755}}, "0.1.1-rc.16": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.13", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d3907584cf914fe955375ada125022d1e9831246", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-fsaaVQG1Xq6xheCYMH2PHadetGWSb9at2TVidnYzV36QOJ3egYlaNyzMUJfX8AdYjgsZgJIJa2XGtldF1EeKPA==", "signatures": [{"sig": "MEQCIBBfLhJD8YbijRmOb7KC/JJS60Q5b9MMaaY0oiPp1J+sAiBubcFquhf5qEoVK13VhBs4bTPZCkkE37v5UkT5SE9w3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 153755}}, "0.1.1-rc.17": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-presence": "0.1.1-rc.14", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2d7dd5b755704e5ef3e9625631d7ffaf194df1ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-O8guCb/DQ6PKWSCigYqb4f5ZcLevbkMQwxkJHhSt7sqKV5K5O8694/5iiMMMg9I6l3Kyn7CpQhVdaHRLMG3RlA==", "signatures": [{"sig": "MEQCIEAgbtnw2ZMJEDa5yClf0Qtj4lxFIqSFaesMfi76Gqm9AiAYY/txizAs/sYtZLELbBj6mbe9n1XzezPpXbO+uu4PGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158253}}, "0.1.1-rc.18": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-presence": "0.1.1-rc.15", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7ddb1892d5cd65c94fedd10dbc175f8bdc75a859", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-OWIx4s6H3MuwK1lG5JvGOyPhxImsTnldvAOctDyzNlyJUNOM9yiVF6wQ2yRp3maGc2uQ0FbQNdNnklGTq7QigQ==", "signatures": [{"sig": "MEYCIQDQcg1KYX2wDi3tsp5jlaJULLFShSXcxEdgDSeVkcNWPQIhAI7iu2bU9Aomg+T4Bw3oe4noHiyjv8/LoJnyCPrk0z/+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158253}}, "0.1.1-rc.19": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-presence": "0.1.1-rc.16", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7484635f726746e983adf8551c18f770f62f3170", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-28dhhw4GjjPj8VgoLfQAFq+K386aEUDhGGpIzE/QeaUCRjp0ZVZzzgzEpSw/0Z+DodcU++2K7S9xTq0/XSZSYQ==", "signatures": [{"sig": "MEUCIDEGXbeg9FEkTP8MT9VwmDQvD1Sj+75nL8/RVklKpSfTAiEA0qzBKgHiZMLfh41R7ywpygpLxZSLkdW4KIA7KB0uB1o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158253}}, "0.1.1": {"name": "@radix-ui/react-scroll-area", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a54bce0d61097dbcd9395d4a93fafe07e9acf847", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-MyDsb0zHaGaPThDJhsiYpbkMx9yUx3qegIoYodKLXufmd8RhZp0USRx4D+YjqI7GCdMzuV4JaD0l0+vMkE69Cw==", "signatures": [{"sig": "MEUCIGz3+yLLrVlcr9M4skKw5xFRPredJoKBOnOL1P2XyedJAiEAk25igicg2nfiIrV/ULp2kpArhyBShUvUT5cc7GpmP1Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158202}}, "0.1.2-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0c831624079195ce2e3a327bc8d16db06ed30a4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Y4GrPhBZPJ7HVK9I4sXZek3R1cJBCaF7DVRCLE8VmqivlSPK+XBc/vDjFd5EF5SVyLCQHVEBxuJKKwLxfGJg7w==", "signatures": [{"sig": "MEYCIQD/kUNBCvSM9NmwjBVK32kR46T0ZzLklSyrZVUSZIeggwIhAJJyYNny++FDE5H8SR9/w7cIlyiB8dD5ivwrJaqvPihS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiA1CRA9TVsSAnZWagAAnCMP/3PCqGvaHUwjn6I/9ur3\nm/IBwFIFxwXQZmvKVWUmLrdLH5yPeSUCFmsFD7u3IEcCc0acdZRKpMi12hph\nxWcG0h+P+pf2pztZ0fM4l0xx8kM1x83En2y01pluVVt8VtsNawo3nzWxHwM+\nTuamVTGdPSCqRPk0k//Evid3Pg8SJcP66COn/1dSqHBfECPrctvmavuKvRXq\n8hpppmaYnoZXRDegwQDjrylXM7eC9j3cf2L3GYXRHmUsWzfy5FlmqYGlpINK\nc48D6KL0tqivgzWshMnhA7srJQB+SgzBn4vQUvqML3wRja/chewB30ZNsbo/\nYV8vpBFcit5M8pw9jrr/Gw2e3Bcx8LBMINM9Zf7V4wUmw0LzABr0/+krg88Y\nkFXYtZhoLOffFvvXrqPH8tPDCxL72PsiPLVlfQ/QfNypM4gtrwFpxh/O8ugS\nH6Odrb+idUDqE8cAxOF5UMPORvyVVGVioy4uEu3zKqkQ9Xozcqqh1UhHNoJE\ndYTcw7+FgsmrzLXy9CzZOnpECdvkghKlA6gJQ1d57fqzXdaTBTn4RQ7Nzq7K\nhUq/kMK9imfi6yLvbdawpmafEONPqyiveGFtjCQwcwTodUv+jDhnJHaRmkKn\n0h6HBZMDcf6OM4B6R5d2vN/kz+hCUINv+CiO2ZXC0yHvG6CugYawrH++7nnC\nMXza\r\n=L3iy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b36535ce4aec60a81be9e46c59235fa02b8d0cef", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-nLMqIEm40+l+XjdFm+Nu0UgSJ//iVBxZ1wjVRbxY7oMFhH4Fgz5R2hVRtXTyqbMMf52ZIsPul/0Nk0XaA9QI5Q==", "signatures": [{"sig": "MEYCIQDCWJBIcsrIraAv7u2zVv+fC3WSjjsNMhMsAgCRaM/CpQIhALbe2QjklNCqkjQP/poU3iyUgVVGbznP3BbfeH2leM54", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiOjCRA9TVsSAnZWagAAdF8P/3go0tiOf+8wGoh27O7L\niwZ8KZAS4FHA5Ahb43vw+wpRHfq5XKyUTThhAPt1P6UH4npcfsthchrpYOt3\nWfa9fsUZm+bgrToMTQzQwjM3xes/OHWaWTAsnfEZyu45+5WxLBdNAukErEXn\npeG4ul7+l6XKjLxYnawY8fw0dlGVGWTIYyJ1bDOFkQc3uVrlm6ZdIXWrmW3Q\njAJZvSG6x8fyJehhE6k4zrsIof6DfRmRBdD+kOv12DT9io6uGTpCIBWBmYVH\nI13c+7swoIl6S+pRq9KX0AzwMAY7TAt2HfzEbep3ElPKTH0qK+IXAvjwQfLu\nCLsQfbb+eNGmx4m4CCEd3XSzqLvrZFAvtwdyPYceWA7Wrmj0VMqaZH5aF+Tc\nH8n9kOtNs61YuavyK/+Ecqj+/Y1Z1bpQFZMDAW8fwB8NoBj3320m7nI35+jh\nlez6hJh0UDgAjMynTXZlMGop3Ks9OFtqcteUv+TrpRcdSbdiEPuu9mLHQ+sm\ntX0aumo1bkhwMlSKYJgGBUTN7fYmEnPTqKRUMypDcuYqZQ011n+2GM1JigjM\nJ9u1TStGsQwWnIxW2CwbPlqOGjGre93b1JOueinz5MYVUb9IvrmPF7gmrS3i\nJPiEhQGsdPqHvERVs7EqItn6pKxgBrxaPGGhL4BnB2iBu9lvz8xpgTyXq4gu\nuR2F\r\n=YRuR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5d2d669067ba3ca497a94e29451fe6d450663ae1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-+waFkdJIY7ohvXyKPDpBEU6uoThOxj2XooKWUEWwwjF4qLeENHkOXHUWroxts14+HHSivNgIl5QI6pAQPknMnA==", "signatures": [{"sig": "MEUCIEy22CRNP3+mcwf/oyoA2elkU2Pozd9bmG1jVMvyYY8yAiEAywWt9y6pexz33YPdLDYcZVDVSqDLlXXSZxf8SHVOjB8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryjkCRA9TVsSAnZWagAAxHcP/jNsMXCcQRXKmcufOclX\nffWk5hwJkQHsal48BP78UimKpoMELG2/xki0DAJzDdA6kT9s5pDWxCYtJsI3\neY6eXrL3pgGc/lXyFA8FLI54Ffefew+CZ8E6kgwiZFmQb+O4FgPHs8lhC0rk\n8OhzXK1JoDbXZuHanjYymNYYejkuGDU+L47704u9VW4bZR0WY/eZQC5pwZCP\nDSOeBVKpwATiminrwLeI4QRaPx+peK9YOt0swYCFFNOdyxa+OsyqwDnGHkSg\nuxj6h8UnIJt0vMEqx6HGyvv7iuvtF+L3+MNAD6YGc81ZoRVB/v35JKoSDsoI\nxho4k802aTySptHvTTxyq8SfbUhocJmmK7BLHtA0zBEfq6+ApcD6WXiO9K9Q\nuQNCy4B8E9m37l5B/qSO5upi+eRivWrouCJusUQeyAx3Dn1Vni1gaHcU9/SZ\niU7WrX8gH60NTHuP7+6zTHznqYkuYL/HXjfXhGEj8Lfk4lIwCB/hszKczoge\na6VspByCRDBpnR6XNSWqk5X9mXxwJ6JleRAT1VAuqWO0FYTAe9UA5+c8Vunq\nq5c3PkQk92hqVJsM4SPWkpfCGelsBYrZwIuxT4CwtybKKK2hNyMBUTXefSqa\nvgyrjXHBjf+YTmwzi56UIZN+1Q/z6GTpP4ziOzypsXWDKkbr8NlMbpgYBCWP\ngLWi\r\n=aH4+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ad02ca8ddf76949502cdc7120bbd4095b43698a1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-Izb2PpA/eQ/Dp2QjD6Bj0VS5hoScCB6FktYsEmOffPpZj6XtBxVxuIR4SHx+L21F+dMLzYP9NvJozy8S5zTLtg==", "signatures": [{"sig": "MEUCIHZv9UEnG95n6etUVueSM+UkTy1GddPsBiEEjom7fURTAiEAoxd/HC1UjJZWnIafN30B5fgQ/Ek04qoBcGeu0amdaTc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzRrCRA9TVsSAnZWagAAQQUP/RSG4nS9BcE7SVPT4431\nvLPgC4pkd95ZUHqipv1P5ocOmCCvj7+z1ocF00IeMTZRhD4A4MMWVU4aJFzy\nXY/BNKEBKqhpBtYy6hzO90ztaMn84srxJl8MJDFBoIdKn2hyCZOp3XEaG6S+\nGY9jVIjGTAqDwJFZvgFpUfhyeEQuktOfb0Z4EQNU7hhhq320phMOFTdz51S8\n+7wINlAo4+R/3i6tnLynIEPrqrScR6+dxY3rEk6Y9pBVuA8S0KRywE1x3uJK\niBuQCQbQ5LIhmwx62AMgXtwGdss3liLlY23AsviVEgoYnsyXfnRiziC6bM2e\nCA2GVw8lXJj9OwS2TW9bx0zyPB2ZI8X5AzdQG8nAN6fRMxrstKZIl092tqct\nQU5j/+lLz0P4M8w2DUd2Up/WbtGonmk37U45vffKNpa4RtKgYfAH2jT8gFDm\nkrMQBXFx1HPPPCLY3miQFZ5+EpmV5VveDla3xVg623bxwsEY6U30BaRANmry\nW67hQbwPdcSNSHtbelcCWhSwBNxV6wBeByQsEfjAf4NkZVQMPe9gBXGbjTMl\nDD9H+f9ymBycPBUB6hdoPO/v+EMu1EC9ppOQLeh75EJsrj6ZZGrnkNcF9/5s\nCcREJ8BthRmw4Rfoiz3lr7r3acK4g+2TUg51X94drGpR8yHxpQHMVGHRZn3N\ncbtJ\r\n=myte\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "44b8b7dcc9ed649d506d47f2b45f93d4240d8b68", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-hRNrRpcgt4foBBkffUeCglMI/S94ofaSxjKZuQcdve4Ab3ai1onHCaSso2EHCFC7LU649x/AarcQIlUM+4UAzw==", "signatures": [{"sig": "MEYCIQCo/+a/c9QZyOVB56x39fusDh2W3xVqkQvHrE8l7/2YTgIhAPf870yESW1PcqFp7VbuhbzTS+nKak4rm+t6AYO3yLs7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr42+CRA9TVsSAnZWagAAIF8P/3R3223QgQ+dsqsjtlzu\nXhFV9/ayZhOmp9no9mg/fL8Z1okZTRRxtrO0RVeMNF4sddtzF3httfc80MAi\nwORBgaABr8rpxbcsgeHew+8kdBnjjiJ/9Zpsmp5HuAov0VCNyhTVRrp2NFZ0\nREJSQglNRMT9V++atGoYVEO6zgZ9bMrN/Av/+aV/YmYTBkbAExGOfK+zFxLm\nGA94GaR1Djc5YORMtFJeHBEJm0YiJGAeYY9g2vW8nRXk797XrEhj7fysHd4Q\n0+hPeaLjaW2rBYNDVOT45EEfF80e5ZEAAbc5YAmF1qTs8S7hqU+6M2m8r1+g\nYzD8b+oq8VVP5nT40qNjPHqYeWf8MeK2tTjsSNAntQvFIGKTpsEGl02oYwnl\nD02QhFTU0thx9a22yiqsIsy8IxfNug2KJNp6pjEaHXOj8gxEYKYp11g4UNDE\nAnfTpnymcb+BY9KRLc6f415Rv7DHKS5hpEek/KhwQd7cN6kpLt1/9vL527V/\nVAe4qlz9ymvyVhT75gTy+bondGIb18IMYXTliebFHTAVL77vHYebteBMNGdE\n5wEcu7wXX2xI7kXvd2B6PRYsHuQ77B/kzRkI4LjzeJazIkk9SdM7PhGsSkDn\n+TNyJqvURGh5E1sTRbQdWyqL2qF+y4SmJJXfONmjlD/mq7RfhogpbTWF+Zal\n+YlM\r\n=d0KN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-scroll-area", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3b0af7ddeeb5486f34288f9c5da1193d424d494c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-HE57MrMUWw7UCgniGNKiguDuFVXqrtvgsj4BTTXLKOPozUMjNEOxBQgkRb43MKUueXCbDN85xsUmHYJowfhGfg==", "signatures": [{"sig": "MEUCIQCkvvwdhy4fDC3QcnewMIMuX6CpjtkxUAnjFGgU/9RYRwIgPwiRNgh4bsGVtPC6V54J9B4mNUoWxA9DnJkj6m4PWxM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshD2CRA9TVsSAnZWagAA7UwP/14efOPY1wPCDSj1oIwd\nbf9GeI9EQEgbC3gJmPVHp3JVEwK9lC85Z2mMBcJHV2wRhAs3ImoBp6yspPYu\nVnebv04VzU27SeR8QLS4lX6Y3acqmJPMtO/hkJn8TzrLS1V0OYxYZnZ05mCT\newu11vyjvAUu/ElRI/PoamnqDPU4ZpK9y+JErx9jNPZmAnHSISO2Wl3ySxZV\nE0pezD+CICbMegYPX0A+pyd+TqJmPKjb+OpRslfTAaAY0y5ywEYrnwLjdX7z\nSxt2y2Be+ZcAqXfYAA/WYtjV7AgxMJqcJQWEYmi+m9yacBhvk9X1qWN2TfVN\n3SYoGBAp9Wc39W4EIomB6QRWVA8olssKjZpnHkWy4NhHkN8JEmFKDX2ueBwp\nl2c69iryHJDXOpYskiPaoTVDo5C+ub71ZOXU6dI/H7zVIwPTcuB+Zw4PTkID\ncpjV7gzoC6rzwnKXDh+QTRC82UtHgZNTGzHZJQyT1Y5jQ+bTGLsRrXE8tYQP\npU3Lx6p4GzP8/Fk4FzxaNAWB9/NpP8NOQ/J5QeK2LiVBmA38Qh5dhDVsHSL/\nHugvZ+yUg+YEvJ0FGrTNjgmWLULZ6w+zlyCCHWIm0ouAuoB6By0U2Nw9dGqQ\nWz6joOyXBdyuqdcySEsSpk+82B4kftu0m0V21YbsdK4l46lSJZLCbYeKvviC\nebTL\r\n=nIK2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-scroll-area", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d9ef1625d06346cda7eb71e21947fe726c800144", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-wn5FdX5pU8N6ygqIad4clrhyiLo3LWCdscIee2nKe2bu7AijtUs/VAqmLEoDLIi2gpkS5RclxHbiHRkaxVvccw==", "signatures": [{"sig": "MEUCIQC64rpXDYqezfj174tSBPJzIZvpLd2qQ7p1MLk2S+vCyQIgXzSM/SXq0EWaL7HbrhilaGMKGS9Ha4xmc59in9/Tx6U=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLipCRA9TVsSAnZWagAAJ0QQAJ+GNoleGuMELjbKuPHu\niY3HCKIqDWoNCI4tRkAgRiUxU1xyY8e3to4Rm35iBFkjVAgBRDfnwydfV7sW\ny041NS0kSVPwjHMPQLHy0Q/bBzAzsgh67vABjzCssO2Qeys2TAcIWqitLaaY\nCKiXppDcvfFBOs6oOTKMzMX39A/MbbIJ+Br/PcGj5ebrSSqNmGlTN+iWcAWd\ntccVLY5yLjmeQ/nZBoMkkfay5d7zWEsW6XbpwpR7osfgsX7fA40P5TKQB7Gn\nIasykok1MEr3LxYlw7jsXhXVMI5CAEGFnv9OsQdNqsIyMK9QZSKMacp5Z7rQ\nootyn2u+vLzhSqcpDut00xbElNS1U7Y3LYTiuE5irhPHwDpD+SrwMQqI19Ic\n2sc76R2nVpG21bzKsOaxSCPRStTnVqBQ8drB63A4qHUEG2i2wTaQZxsJSHYg\n2x1PzVgP5sY+VXP+yvBRSURCzBwHHef8Y3/g0UeTkCBNFMC46Uky43yHLNJu\n1IpNRcNsVGR1kVcwYpe9mfWyHiKXrbdGS6MmQrlUN2uKQa4JyMwf7NF0pQ4p\nvLeyYlVbfzEhF1AfdTHXLOkkGdUtHVyief5mbV1YwggaaYJUNoZYC5I02l+b\nnxXQuMtf/kLEpJYZd1XxrtOb5CoSf3lSuRvPrkV7Ps1Qpg3v1oRkLAdl89c8\nFqly\r\n=rCS7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0135906d047e3f56dc44d04acc09fda8db2836bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-CTm7uewo9ixoeh1QawkYs3Mw4v53AcfGh3hA15aMyVDzl0ukdY9QODSjymcWrIaST2lEKGl3wDsn1eg4zK9fFg==", "signatures": [{"sig": "MEYCIQC6ESR1MCJlyGD73XgVsDZFGqJzUQt767wjvtyntsCjBAIhAJqBjOIyNOpLb5u0LhgmUCCgakMfQoLSHRwhndrLXx5q", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLj3CRA9TVsSAnZWagAAcakP/RbfFGW3C/TkhJ6+aK8U\ncpR3tRCi4TQ7MEVNIUZCUr6LCKd1Z4sA3DIfbpULduBqjefciyy02AifWncP\nOiAPoMRCTw9HKJI1ZhIb1N+Uc+S+DMRLdkjCq3CKmQaiKBSruOp4dh1T24t5\nwVjWMutGimv/4dMTHKotVO0qwTKyER4CwE8Yyx+e5gq34zT8dOnGxlXlVIQk\nga1Xj/ibA3oNcLjnFQaz7V0VqfZokMvbga8xwKhO2TvULBEnb5w0DJMSDWZF\n6ACjQDFksmU1Cz8nhU6TPt4dsn7DTLKGDrK8pds5fpWdNQwij1oWSbWNkpal\nAMjtVpUSUvqBCcearEEKqnl9m+3QC7Uvyqk//ShvqMx5pCKjbjJOaQQauQHr\n6H0dppfSOWL/aLjdU0gWTldP2GaWW9kF2vupGb2b5SwfKhZesp9KMv54aVcw\nDh7GPCSjk0WEod+k0rB/gxwbR9w5M7GATnhgtGxssdpK4eM6KfSv+zhBNExu\nO1VfRnrV+0azWIvv8Miri/K1MEAfVUbsDYZa4FfmXo8ZV+ousDvhP2zmtXzm\nvViJniVfh7kzbXkw2AIim1rXLDerVl8LwugUgF0RXGgVn6czJweRLTG2xly/\ne33SRKgefSNqVwM9LLs6aKLhueDtluyRFenZr1YaXx+GzjtK+mPHOFod7LEz\nzuPc\r\n=OmQF\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8c204ef8bd8f87e713290e1a27a73b307543ef91", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-IgfwTCiuT2IxFsR00+975t1vNgF/p3fnoI2KYLHDJMvNcTROedw7yrpuP0IsqlmdLSLtHg5sKp9EhZhwd8vP0A==", "signatures": [{"sig": "MEUCICX/luvV2acwkf+wB4wcZ6eZkyTNs8M5PIYKFM614dDpAiEA+T+jHPV66DGVY9++KMzURpsx/x+70W2PvejP74epFEk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31rXCRA9TVsSAnZWagAAPlQP/RgnjUN0c91sEvtF1R3S\n19zKLAcTfUT+UlOfBwzVlDWnESmlcoLncq+NgRbVsnfNy1b8HF/KPA7QsNPg\nZ9LJ/CBqsEfRYIkYf2Jfv93nnZ8eExVXm9N1bNU9bsO7LnaJ6sqUOVnT2QYb\nwskGJyWELD6p4h3kT+/vYw2pfPZcHiPaukRo8mZ8lVBmkClhPwrobyBZBZfd\n7WNky/5jbLD8siVZLw+WhWpj4TKhSwQ7ZRfi/Il6YxR6OAVTFj0plBpERFan\nkg+arvFsGGW1O1IZE/wwG1TH8Uj9lJSSSQRjmGSLlhBMeZPT296QPbSBWABB\nkbeLsDiDHFRwZvWNCn/WklO2ui482F8dMvEYdVSFu8/7zJkZTwHGewf9pgoD\np/JPLM3MeY/fj3EJnx4wqgFr1RkoBy6EBpxnJTedXyxTgDR6ra9T0aJLGD9Y\noXDgxkjqSfVdVHargnq/R11wfo0h7iYFI4D+mhVoX2vC7R+wQ7neWUkmx8TJ\nOTiJEVQuJg9W7P/tVGI7fV+uOX9td/Xfh5nYCCO/NPync4VCWkoebUpwETmc\nxA6Te+JjKx2e9WSCAMk/mPYYKSCvsk6CMwb9DQ21cvm+qwRYVzJopuoNqvae\nL2VKOJBxX0e+QPdSlwSRzRTw+Y7PTpceaXDHsoDINo/HPb+qMZ4fosvOVkWB\nQs0m\r\n=zodb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5cf9383b0b2e656e03df0aca9e252d7a559d2691", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-42PQ6nJN+8FP+cM4yNw12MF+uYikpoWyvlgCs8EAFp+9gHvPdbC2BTzc497i7zxaJD/n5FHrOHDHtD3AwxZciQ==", "signatures": [{"sig": "MEYCIQCjw+kM28kCVWulxuCidKoKbiIrKhTpE6BiaGfSTJwA2QIhAMCWjfrnaBrdIC6jvG5ZAk6reM8F8pcZbKmMP2heVa5I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BERCRA9TVsSAnZWagAASDEP/RTwQpdeOVSypWrwGtJp\nY6a8lfHlhyRUGHqdfUiO3Dss2kB17nBiOr4Jr/yPWJWgSKwxoTMUc1Hcuujv\nZnCK0VZis9Tb/WGA34usJquJxLYch9r9zgufJt7BakeUSFiADMshkWKxmQW+\nCo2jTazRYygB0B/lcIZ+EjTaSjZDvQNODukcag5MWaQd0xLlXghE9ciB0jUj\ndO7+icGtJZCF5Xsne+9bnRpm9Mf501sD94QpvMWh981zhIMa2trTsuk6vCsI\nfgtgTyjwJnTAUIObYwBwAN8vwoVLReNDxu3eBHhHBjFUAV85EixTvahYdH8s\nico30b6JoNuGuTBjEMd5fv1RSvojcJR1qYg7OiK5k+Ii17oBZhKJfhfHTCSU\nsCasmYY1Vm6WpTUgDv2+D4NJj7D8L2gdsjgqH26f+XBtvAseJONcDRhf2LRm\n6CzsHS7WGfFvvMu57r4rUnbtvPoLS6o+i8MYyWUk1SsqGth6+I5soAoTU3WJ\neWiV9H75OgDbJup66EE0xwqrsK0X9PgG1GEpj25Q9VUlH7PEMjparEjS45xi\n3/vQ52P2hpQhuM3gjnF8CHPnsHwAWuPxoIOvar2fE47GQXwRb2gsc1XNHUYQ\npGeY0u96Qm8MyM2kKNd1UIQROK9Osa2hFZcU8PHaD2iA+BkrwtaP0Mtxfyma\nk6u7\r\n=hdPY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1ae07ac0149c621338b896a3f9a50a997ddbd88e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-bYy3KJorNzueyCyGswgUp2JNedoLM331XD7MrEmFrI/Rhbw5p3oPfap6atJER/6VeNRDrXeTbc5eCW9fMYzzwQ==", "signatures": [{"sig": "MEUCIQDkTEifZbpHYNgqgLcz6myvZMqvKAdP+mby3/4Pf39u+wIgMoWo/g/gV0KhwJS5lJ2fsAyfKymGbKyRX9q1n5kPdh8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4Cm2CRA9TVsSAnZWagAAF78P/RxClO8LE5t1mYgwCRcb\nJpajwzimOzG5GbOGriFaEGWWC6TJYXYSYTr7gpNhIS0QW74ChYc4ho3mj/7h\nyt6O7+SbinR5E1kCulRP31tbH78oPNm431liWdPUx4HkENcYYmQW5bKwBwdB\nKoz7485WVLCB4lj1g2Ne2dGtnS2r8uXTO45T4d91HzDrZvrPbawuZlEJp9bj\nP87Gp2OqiRkUG+g08aEapQsXn4fvkJdoHM1WgAGQ7jsL5A85lb7T1kBPNIlg\nCgqsZHsWSkJz81cBl0+q66T3A4/uKINgLym8PGbtscXTc686aLbw2csQZN7n\nuXiVOMP2gewnkKBuM6e09RrMFKdzrSUn09X+NCDJCD7tHymqPmLrk8UpQ2Bb\na4wnPcAJSlQx/OeC0w9jeERBZmPUGkfjqLxN5jvwMiPW209wcfCttQ4jVp0l\n89OVfDXGlcYPi1nOWnC0/OeXSbB14wo27g643wE98eozDyq0/ggO8b4AOGcD\nl+jasbo93yg3uoWwkXIq2mTCS25QIx8Sl96UBfnJQKXyeH30xjds3cgEmZBX\nb5WIz77Jo9mqjBN6cOHgMj7ogHRFIofZlRAgPVmAivYYXnI4KHykF3C2A7dO\nDjhTwilx/RTkDdppe0vD98hQX1fAk8VN3Fe95lmmWIGGJZDnogU7jZmPe+px\nLoNh\r\n=1HwV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9a8c5831f0de5dae788a6db41febc6de2c1c85a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-K5aYgsUK9mLMxNQAFCQrCnqtFb5JEjbZFmJS12378ngOxRchNAGn9Id/4v/pzVKZP1ge4Wnn9jKBWb2eYj61EA==", "signatures": [{"sig": "MEYCIQCgZ7cUyPr/vU5o00CqtICuhjbG6HtCWFuySrcBaYuy3AIhAJJkZwZPfASrO2y6eLgixUxu3zxTz0N7Wch/Q1U/wNFT", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GqyCRA9TVsSAnZWagAAUawP/3dBa6sEUzfJ7IklqTq9\nLiLKSr1s/FHIHKOhA69B5/1S+DDt97VVQ0zzvKdtSJlmfSgHh7ePZSyu41Gs\n5oIwzhVIsagdQGB8U6MvVa3uCywK99sJFo+MAIA2SsAqZzVx0eBz4gjd7INe\n6uqXaCj9fK1O9HuJ4b3eReji/lYaDoAYAa8GNTGewCGt992DEkMy7n/H8txY\naO0vftVwPZMPmVHGzkCH0XCP2FIh7B+Ilxv5rb3JS4ru0VAI4DaVXPTeBf3Q\nbX3d5YT6uTbFdLR4tF1apq7lu4klZGXspd+V4AqAkq+qUD4fktFSqqsZAgAF\nJZ8R+XOEbl/qmUALMe+b13vVuAI1+MrBaCPJpzMchk3+qhO/2AUbpyrpRwTd\n3Nb1fM6ezYnA1PjmOTeUNqrZRLMyzcCBWzuFTXJS+scCyWil/C5ny7w8wsTf\nK3Aa4NyEtX67Khw4kxtBBCIVUx7+oJYpPPJPx3DCBAInTJFUzKS9lkO6mOx4\n1Om4efMrJEVGCLzKoJbw9EQG3ylvlIe71bqHsD9KBleNBC+vXVMEghq1ca80\nkwdR4mSN7DOHMlwigA9Dq4loRwTNtKxSs/PHjFr0y3cDcvW8Zoi31WtDdDyI\neTtOMKTpO0hRdCTvXuCS1sM2NtikpJZC/T88T4PTGFCScYDACXr+a+5Ukm8Y\nznJa\r\n=P6kh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b52b62c0cbfdae6b425c49c6b56a560b199ac82f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-LEQ0YFsXybbDmEuY12Ed9oKtnXLfC4Fqz+4cKhzPqPDYrth2phO9ca/LSCOiiW2RD8LV16ctauHQuiVM7bpyKg==", "signatures": [{"sig": "MEQCIDFpTteBye7fy0ty5Ut0B+p3EuBgYyd+cmd+RRCxUgkvAiAEBnu6caI2ZzZqq12U9M8GGQL42R5qPxfskAS57CIuvQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZcsCRA9TVsSAnZWagAA+jcP/jvwkeXqDEmZJQjfWp7Q\naV4x9iCfTJHYyyJ2nwLg0ccnKHmKOfXvkmqzqCYSr3kiBNCrUisAfuLYG5BQ\nNU+EB2OWBNu0BFUCWlLi5K2mk9GTSiuIC68g+YJTjVYKJcOT7WAVz0oRyhnM\nfpB+Kwv2LcmMPOfPhYA8GHFnXEDpd+VDn+kxj+HW0ZYkNDXq9jhkQRnX5/aj\nd8WZMANz0/qdxbwm62envj3p5fCTKKC2C7O3KnIVTD7yfVQPGjzJ4+nW6R8E\nPIK6o7G1MR2qMrVP3OetylQ1aPk+9Ad3FamKUVBtPVIsknTpExlrtOoAo4ma\n25Kog78kSujrpIMyXeViVvgCUyEWJVFzk0+GP5M5ZujeBKDuIFFiEKTfeQtn\nasmBxXPUJZdCOwgFWMiSoiYD+z05MyNI66ML5mel40nEfKPw7WIwZe8/wp1R\naGCjKkmHelgalTvPOadnIEL1BHfu4VwE/CvJKeABb2UJACd3pU70T+Fu0K5H\n8cffPG/gwvECwbVUxxRAbFi8x16qk1KrV5WVLp4P42eW0S8fALAMg8GBSRyW\nPcNmoJ7trRnKFR4V+7J/zATZDhTWCh99Pmr/KhObZXnOQvJziMGIaCFnSfCn\nSux/NKbtkryubae/dMw4jdbXNm+bnfW9L42fMhk3ZfqD/zm/hL48BOQH/kMf\nBBYI\r\n=K6N8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b32403329c9e7e15984149c5a025bcee180fce21", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-E8IxSRpQZ4OPhXrnhpu8b9WdTzT65H7GPsNy6OrdJbGT7gzk0zHV/aiGfEixnRURXYJt8Mv8X9CqBUiNWr0lFA==", "signatures": [{"sig": "MEUCIDquJl2aE4ZdsC4dDmffeT0GT72VXDYcnCQbaX3h8jniAiEA2MZPS5x9s2XxoEJIJ8AGka+wVg4bidUdaVsjCALqp3A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6YtICRA9TVsSAnZWagAAhQcP/11kV5Vq7lroVUPMRPPJ\nHncMNnD7D5w8wdJv3yJdKuj3Yuh+dngXRrJCTRJ6VRxDi5VDNfbaB8KC3jXx\nZV4tTKDTWzuOucYbUOHNc5CcjChnOsU9yPx6zdTXG4tt2wFijiCuIJoiMjxz\nx5cVThbV/7n0WhfvWAV4AWpuoOq9iy1cQfxpROET1h+0dW/K4ASSy5r8oKqy\nDx4dkKT5AgfuyYfkOZTn1O1gG98EFBkh93sG3i373P1nJ2sFxVZ4fDhW8OeM\noMoxaoksRE7/az2OEaTTTifIcvbziW7ptS1MWUzoX6vYvecNf9nGT+17vXVo\nrvnQJZIdG+bHdsYRsfCQ6nUC82rGV3zdz8ejSjNBNWKgl5NGRfmHIbwqkljr\nce8staWW8EKPVkbKdcppF0qb9mOsSUJrecblPkYVM/a+Hx74qiyc1Ju10xRo\nKtduHZBo2D9QJHZsgE7pPciEHwx18Nb6uJsaCKdwipBsQy0q8f9lriEWUTxN\nQc5DtF2tEkLZJfqEPStJi8QnXryYIZ+J2ZkMw7KhaQhWEEHa0LEs9S8maWmz\ncFq9tALPprJPBqg5LkoT/fHoU1LRm7M2VRUpmGp1FcK2O6oe+tQLCxBKgXKP\nJ/G/EA8nXS9Znq/HSHYr3oC/lMv+RvmVTPtfXeNi/+vRL6Ma/djr7LaBRddA\n8udw\r\n=eMEc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "88c45d681c7695a524adaad7c65dfae4cb0da096", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-Cr406MLXCX7U5iHXql+ayjzP0kb4EE1t+g+MVgVtg0Fc7izmqh2PWkKeVTNT5Ugkg5vy92IUW4h8xuFTNP4Q5Q==", "signatures": [{"sig": "MEUCIAJBCfzkFNVxWfQeWuGxdsHyirsAyG1U/uRS+X7WxyYKAiEAk6jtMHSYJAl5k/nchx4t7ny93uUO1aCeDSeRmWLTuII=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6sdiCRA9TVsSAnZWagAARBEP/0PH0TYWmhLKy+Q4u0Eq\n9rj3BpfzAVUbqSwk4stmkqlLgQCgfdH93RHwUPRUuva9cU2curLJN/ikPuPo\nZntGg8dASTCV5CdJ532vMa7dV5GhZ4HLKp24KWH4c56rAbuXmpUKPPvEclKA\nlQSHMcxgFEOp8mvYjc1E9rDEIQUd+Iymm3v1C+M8p4Fk1bBPNK+AJwBr2Lgh\nif5bbE1mVrwhmHgGS8asFuGrlqYpgTvJ7jqP5QExuR04ASDgeMkmwJgGw0FI\nfi8XlnnSQfb2tqcETM6V/fh22U60Lfe4nDveOqdr4TdvopylDdqhz4wgLT5J\n66mg41UaSIixQy13eXqSC/0oMNAOaNXq6w3HPWcivnv2L/XxQarVhXnDVvNx\nWnVpMMnMuhcl8xsSAhl6I7ZnRJPDhH4TNdDapTYjPF0QuWvXu03ZUfR90E8N\nSAvs+HtymMXgOmmE5ee3kOM3Y7B64QocBVukwfSlV780EXcwG9eA50CyVKfN\nykwKoxmD8QmUa/z6NSXb0PTQhtjFKCE2C1G+5YOEbZKYkjScPXusRYk6/W0y\n3+dIAum9nsdUvuaUbh8ZPOvBiZfvjg0yyl2cDs9rwB5QNOWY2KmbziofaEvn\nnEJwm0SBSs//pKavQkG6n+iV0sLebrths8KhyiEYeYc9YfSVTfW7ihaii3K7\nmfDA\r\n=kleJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "62d13a3d320ef9d2e7b48abc039d8efde69cb341", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-jYIrV3zyhrhfgs5dFcpHA3idazGxCP0d4Hn+0ueKevNbCahpxUXkr5btMPwBghh77N1OEjOCwPHQjyPBNkOoQg==", "signatures": [{"sig": "MEQCIHlgDr0M6SrQzCAJkjETkBPzJsNTCwEyWo35tqpU26dZAiBzEaOjqw5LheFpA7H3J2jhiJrZELaKSTKPk1v5IXdQnA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158240, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xDiCRA9TVsSAnZWagAAuU8P/jxSXTFZOgx2mYQt1Cth\nqpRYGzp9zoeHyi9EriHkKPFIX7TW52a8jcIKODZkCwUmv0oN92X0+62EE2qh\nW9cU8n6s1EaR8/5YFiAv3y/BRXalTEVYFMqvdK3jS1IzCWjILTq8GbuqDUIa\nRYXKx1t9XCoX/+KchA6vPU5PJLSF27af6z0yBpkXvRK95pqI1CUbSy3zWWwT\n9uGT3Bouf1LXyNJRpL5dSZ6SrWCaQvebBoVDd8I+l6Z9TO3WCIO1JbaSDQW2\ndEJVi/qEF8uvjbUOoGNedFPUmnUQNJ9BXhMVFzmYF713mrDfPL4yZBedOa8j\nFY4NEnZ4YT31m289XrSuKyh4CyQEJTWGYc81dTpaE10DiCKIFE29gzKJBIhf\nqnppqPuepSMYWmSkz6IzWD88HdSqndbxLrWo6NXeelMvS0S6c5rYeAEJMOIj\nFLx8SmT+SyXLx+KlR2WcoPUyEEePADMeiIwD0++Pnwb+gXw4+10i42oc3Mo6\nre6MmVfIkJhQKwm1nZta58Jnde97mzkvmAB1ntcLv5DSHP8sPYzADWFxnFe8\nnMN31ESAZoe8QYKz5JhkBtiCKIOrKFXkYVxQTLARCXIc8IeXtgalmWs94eNc\nxjpvfvT3AQ6WDdk4ZV8vC53v/DwlajwGOnG1moiMFVo+3ZXIH/GrnCoYwzK5\n9DMO\r\n=prf0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1afdf901b979277479809a468d1c2b7fdf48fd4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-fQo5tInVxWeoiohc313o7cSyyx+CohSeDDHADh3SutAlc1dYbx/OmrZqz2k14xim2JTy2sL/7BP9SFk0H7QARg==", "signatures": [{"sig": "MEUCIBB1HZJc3YfafcTirM5ncR0a244ZyHcy0BmoeZmSYqtrAiEA/mrE7L65/ijJzDE4HYkB0PasvDwnESElSg4oLgFtmEA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158245, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xLiCRA9TVsSAnZWagAAOwYP/RuiGn0X7l8Jk93Vfe8o\nczyGymN3GNN265vyfCNAvFsbdP4iT73zrS+wu4fx8aBYODkoUlr6lYITlNIe\nuO0WEKUvwWmrK6qYEXmsF0f6KkN4SGlls9UF/Ehyhz8RqiSlkUZlVvoHB9VP\nlQIIkyYceOFzCpVSkOEmYL4w9uMERSR4RxkXQzQauaFhQVFtBRwrSAsll0UF\nXkhbagmZaedwRaC0OCkmI+If1zfYcLFE5/Cu8l1MskEl31Re66rSfzsvPaIo\n0D8cR7p0dEDBbRPBzlevU27alPB6YiHE9yAvxnvtyb1YDekg25AfRsv2g3AP\nhLDVPRep3EVnbpFYWBKY3s9cSKFu5mzKnO7bsDLBcLiXddgddVN9X9KD8ORe\nXjyJLoKaGEyyTczUpD6iGOsWhbpSPVe37WIqt9SH3KuMHkgkc0I+4MKXDIT2\nsRn7gAScgcImcJGf44ZF9wvYMy0uZK4p1V+jnsfHJp2leVc0PHy8hU+KwWGY\nH5eAdhH1+NQaWCuZk/GI3Z904AjhddBKg5tbn3TOJ6Cng3v58T/v419/fGy7\nKIxK/38pCQUy8br0GNagEFxn6tFvZgVd+Nol8exLa6iZORKUTJu0FNQp9Z7Z\nxpsfVoXUfSH9WrRE1aF65HOXR3CBn8LwBTwM9jBr/UGKgWZgH5HW1+X0E9yO\nOGzF\r\n=kcew\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "08bbbf7296f214ad97af892dfaf4fa9a6e13c650", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-yWMk+T6ZvAbsDwU3cs6DWi9jtD/Dycjkm1eufsxiQNa7Kft/DEKoRYkM40onJ2iZeVjylkxwC1mabYQDbUwHSg==", "signatures": [{"sig": "MEQCIDJjcrxHnPGnTEOp39skuV51p7ZZflIy5Ym541EuxDhZAiBSGXs6Lb5mIzMmWiSbuO3edRFI1RLtjzF+L2tism7azQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8D0iCRA9TVsSAnZWagAAGA4P/0Q8bpReI5tDCXY+8nQF\nwRZlDAGI1dGEo+8Arhx5rJiQykx4382vTFWP8wrbZm45Iyhv0B9KAz8bmfLW\nMLgA855W/dLm5JCvnVgsN8kKWgRUaeD4jt0VCEUwLPtOd1PkRq9QBbmR9+lk\nP2b95YjNryGsA0O1aO9D12nGvsc88VxX+o40cE9t9rWWwxoGGuUvrAK0BZMQ\nmESbvLgNsfjdoXfiQcn27u3aWhesrpONFIljkPeWQX3dtK+ysEySYB1xuy/Q\ny+drgykcSuavtnVgE51Wt7YKP+td/ybe/rxvfpG0rGMJ6JjUfy2S1F7OVMdn\n9JNn1sV7y4KbCCq61EhcviYGxJ4JkI0fQK75JTMTKTasYgUrJbQOwXpnnCbk\nnb952z3GUGNx1q09xnNYRuBlSMgH+oJdTIWND7Y1xDMm/0TSwWadQoQ01IMe\nqyC2K1nn9GRIDB3V3LV04i4SUDulgWAcfksqLK1NIDa1MTB5PpYFrG8d9A+c\njpVadZ0QUHx7wypPle8EkuYBqnTo/edKg8o5RKRGmJxLcuR4Ycdz49i8FP4G\nAezoV/v5vAHjJtB1P0KIsNSOUH/oL9C2HXKgTnK1qX4hF5lVCZTXlYz04WL/\n8hcjUIWQfzxoRXkHTiVIutcw98L0L6UdKhYu+2e88KVnXODe6BCfAusjAA+p\nUv57\r\n=EWDV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.11": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e983bd0cbaad9254e87b4434319ab8347748218e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.11.tgz", "fileCount": 8, "integrity": "sha512-8j5pfppdNA29l59HCFBfTLKzWfiphUaSbnZha+pjYkOc8Oxc6j4HFptmB4PsV5beAprHndUhmkexw2F2N4BlNw==", "signatures": [{"sig": "MEQCIF6ThXnygEjLKtRikHf31klbek1R3ZgFERWYYyJdy1pGAiAtCI2VbzQVt5oOeNRhr4NrpU/twHuPvWCDExII/2+xRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8STPCRA9TVsSAnZWagAA3TYQAIXUDJbprEsAFBeR73dV\nqal5Wx/eUrUutYH8KU7ax0fI89OWppn7UKoTdVLy5ZDIegajPyYeSK3hrpks\nDRQ0y4teRnEoNeIaqYAlpeiu90z9TMHjrVFbJmTaPXCZJLZQl/v4cPdB4rCT\nWR81e6qtKSk2ccE+4pCpkB6ks7k0fJjKhCYUjAQpGoZd9pGSlBYgVdPxyxaz\nF2HxVpUJgW/BNtyzuUXk+UUYRkdmDwMWORd4z8wVs+e053SaVfx6jpWHWIDo\nWXs1lvfgOIvqapAaYRqHkHlGptplTzZYPoU7i+0x+nGDumvDnTJndHyBg1tF\nlLcZDPd5snKLMEi1OKQQXYn3+e7sPy3/L6v04hWO9bPPZrza61+PWgUCUX6A\na+LfHwyK5SfHgbTmdPVtTw56yTZNOSsAOe0DO8Iyi5H2UcnhToSAcuh8T1ON\nDw862GazV6Y0h5fZwKitqxDMLV4exZoHzqoYRBqDLTvtdKObsX1XqrcQ5Mlp\nn8FG8D+W82WdS6zqo9vtiyncTrom1b2e8OSdzbgROVpOF8smVdImM9LxAAWn\ndLKRWAIP1Iyyn3LGJVinmLQcCuZCcCGV0MMawY0OgCypCCDtMaKkGiQCOXNI\n7Hil9T6G57uHmVuI0IFIUWHqbuTJxzeZh3/cxV4f6kuIk43TQmZTJElhX3Us\n1Yoi\r\n=tiNy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.12": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "307e37c93fe466ac0fbbf503172b764bcf1a2b42", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.12.tgz", "fileCount": 8, "integrity": "sha512-FU5BjeSmB5jGcSoLbMeOwSqylV93SUkNKyzdM/COww3EvUX/c1FsM1DPrg7Fq7ak6TDUsLEID+mSnU+Zi5urtg==", "signatures": [{"sig": "MEYCIQCncN1PpiwKtGKx6Zs3ilwjL9JKP7hiJ+8k/lZk7xg0IwIhAOyyJW3aEu7bwoD1zv7WZdZXvypfDNQ7eSwoNLN4w9Qg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DaKCRA9TVsSAnZWagAA3c0QAIJwDIU817SjmZzVoel3\nerYVPRKJV5GJ+pjLB1ihBl9DUoLfFSTJXv3Ut2sXwH4RmxxTkbDHn/heV/bC\ns02oqLPjwHhBnyB8GVHN7PCUYh8FBGICITyNX+onhBBIvceIph8PAQKiNgR/\nSIuTOFdMk8oiTM0BASRzPRD6I3fxjl780yvYKoFHFFAWKK1zSfWIjqMsrJ9O\n1JHEeC8uayBV3w7qfa1kFrXvDPKTaITU5hXUwO7LeF57rSGW/X4WVw9kXAx1\n5ra95xxCJ12zAx0qKM5/RX9li8bE56E2s6xuUmn/4ynbvuIpiUGPgubsV8ZX\nw6n24WGb6+CVH6915xcMoDm21+9G94vDhkQvoWemeqikZB6io6Tv/KhvQ8D7\nPBzXf/UOs5YORRTskyOo0HGZ4zW0QJbqyKmyguCcb0NWbPyPVAPVPdnvHao7\ncHdFawthnqe3pEYKOXGaZDPMUHFvHVz5tK3MVCSmD1bc9bMvUKGdpVM+hpVl\nRMiClWMJ+O0RmhNAf+yq7/w2rq8/Et/BgBemQiyvcYH520IB7LI0CO4FEf+4\nIcNBxN9SG/spzjEI3lDqyu/siZyYhkjPgF5YSPP+Yw1L/rLw98Hd5/1xE3yy\n/NDg6C4pufor9y4KAVnn7hoRR+v8S0N4QpGEKgHHuzXpB0AWkpTGixygXe87\nACv6\r\n=CXDS\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.13": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "659472e348e4fc02fa07453b82b2ed2961b08689", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.13.tgz", "fileCount": 8, "integrity": "sha512-M71+l5PCmjFnng7EjTnO17Dfjxv2Te0+UA5G/h3ZrlrnKYESmGfWKDf8SYb3LvK8U2yocksJJg/XAl64b6NvhA==", "signatures": [{"sig": "MEYCIQCZOjavDiPr/zOu3Zh67wMoIfaINBqJu84Mv6MRRZ/inAIhAO5kHmptkN1fOwEz4dXtJYbsEgfNBlbYBmPBCeY7LF7E", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WoVCRA9TVsSAnZWagAAwDYP/2/2BtBfnMVY0WlZxM/b\n5mfw2nhIKtYJyeeZcQBzTVwDRc11kTqrsmkPcTQOvcQUJI/ti4KjvC1x3oD2\n1cP59LW4Z/rAT/FjS/3Wj7QQ3Efd4WXIw7FvDWq3BhOgXqSAZOxw9hYb76V4\nd88xDKz0pkEZsiVPSr7Z6zAC5UiT2ENWz97JLwRuFFVYUA+5WKbxRpPsfABc\nlPPipgAU/oGi+RXQ+9KQbLdIVTxBPJmthiw1l5dBFFG3efFmbLauecKeyI5B\n465+2il73+sd8hnfvY5cprZo6sXhbvBnU3R9CtaXH1cqRJLMgvqNaoY8oOjv\nmL0jrVejHqNJvm3V6SDIk5AjXPIIpzBx8j9QZ8g3S8q+YByB3iB/Ld38gDU3\nBnorUQQLmb9ADFKAjs383lbZvCb9ELLVtibx1l5LsaQNDV3hMYTIe7Ubhz4K\nOtO2htUXLxDi8gylTbK/Nd6ujLIk3RYlXr/Qp7bnnh5f4RstgSFQFmkpSPYP\nVqDC1+u1RsxzCXlA0BO5bwc8yMage6/3Q4jkQylY/wIMV0dI8PWeEv42mcJ2\nQjDdPX0PbnM6k4gAcbJjJZhnSrv695FRhPce3CXEx10LuWotF2+OsESwhU0V\n63YvW5LD2qDfiEaT5vQCYR+2bTfz3KKFYRX6/e1OPLyz1FC7ti+fK6x0s4VC\ntTya\r\n=LKOf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.14": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "558d3d45ccef6d59cac3bb9e25ec5fba00e6d21e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.14.tgz", "fileCount": 8, "integrity": "sha512-1Mmr0KoK8O60PBqlcF7kiabkWvKDPnqCpg0G8SCy/KNK9Z4KNJACAkCufuroIRVeGdXqI/eOu1w0F5h0olJFNA==", "signatures": [{"sig": "MEQCICwl4SbHyQ35SGr1gvYmbQSxFpbtYVc9wDszEAuSFWShAiAYCjdhB+otbezP+2HG/orOJvdvCpVcs0sI96z9NgTXiQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rUvCRA9TVsSAnZWagAAghEP/1jlUMhSXu1KVevDP0Cr\nsdhC9KDf9VlkZV8RgCRnp0GpQgp6UAjFbXedAgHUcPUMkV89/J84Xc88+EGC\nrgzz+e2eXxbefBtG3sqPdnKpCWPi9O005tzDsjVkPC5lQSbeDRpD1iTUMRjw\nz7W+DeZUIfAZp5nG3kAj+njSN9PXiyawqsxzUVNoPumeNrytJJeNS7sH0Yyh\nsxoVLHlB8gfehhZfsHMeI0wBl5vIqDS2KKZq21BGYbzxIoGrXn3gl2XrLG7j\nW2QOjOmywosHoSe3sk+aLwqaLaMnJmoQ8m06f2wvkN2JLB+RTVp5CVlg7UtC\n3IW6W36Up1H67Fxndrqv7s9s+Zz8GRFUDC9ASBvgCvwuPRO1sIW0NbdjjQbs\nIRQ+mmOPrQNaXtUson46AFwzdZ7KlSoXYi4Ameih1JREA5DUChHeRBCRazyL\nPNOaMXodfPL1oau67fWDAurw04kEH2WwfBI58RI3eeb33qczym2dt6vOJhxW\nlbvp7WaBYkHcPnJj81fx56Z18LEDD5gP/REWDZqsfAKIxE15VJeBmRS3sgA3\n/JxH5MvKEVhpZbahSlxoVClljX+RE8NTRVHsrZCgB1RnE8ugkywtZA0gjhGs\nuJMucX2rE7cOZ3ByirMX6LFB8BVOdMB2MVbXirG5x8KROiZSfYShpvmEY5Mi\ndKXL\r\n=pNQ4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.15": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "54a17ecb61c5cffff1a091317998a045df2cbefd", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.15.tgz", "fileCount": 8, "integrity": "sha512-fFRmqOE+F2fapWsqsUHD+bAs62z9H7xUHyrAmVaKlLB1Xs6Tg/qibimQa8K0nySO+vK2rs3RfVksx1iJl8QNXg==", "signatures": [{"sig": "MEYCIQCJQC7v0rT1XOZ7mlPELTvK1DoemRpb12tNNJoT1cYngQIhALad4MryzjmpC0qjeQyg6dfcL5bAp6y1Ojl532qFfa5w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/nyCRA9TVsSAnZWagAA5iMP/AjDN/NYB8RkzpAqc7VH\nNeoB1mLK9hqt2Jq5ZjqjuYXDYLvLWuiqvg7wZmzj39L2yTA9nW5uY0ZMg9+c\n4KF4eQg375CJ86l++3Xh7V1kVdZLHZ5GfiMNZgEpvpZDgED+SuFRUR2Y85Kv\npLTSuMIwmAuIBCmEfx1bTvvLNHEq6F8XOb2z4PykS0+UJszcdcgMD/fNbtG5\nzMqf+QTnrErwRMVwVZCHo/vC4boavTt6R0l2tHMbqOpmnzqYeIfh+o1WTuHj\nGh9gGF3qxTcAwNVFU510Bwz1zTsYiSpwnYHvfgy2yekZFGwZXQZZQiBOPlK6\n1qhrJqZwW0XLdyw8zubaBPLNF4fB6zYplA635WxsTfngoe/bTu58TQqbvAOE\nEE5Fw5VhUHtfJq31zXWRTjRw0b5A8bFXi7VMbxmyv8iaM0jD8bDTC/vCrEvL\nrB/w+shkr3LPmLml4WZRioz1QajtvqDpCjuYqszPbZdXQ/m8wohxrzGmDZKP\n7dYtK6aDHzUNRyy0rEcYdHbnpvR18UpQkjFfMX3xx18mPwpXJHada5olc8H1\nmaD9BDfw62UmYMuQ6nv65XZpQAbPpzqKra46xLec6McUXJrqH9n/GZA9PBn3\nOpruRbhMXMDmdioK68Q/wkgYkYBKZ9xAKLFTcLSDJv1uZI84b85GZ50YqX56\nNBbx\r\n=gMGP\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.16": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bea15df44919e5902fbbd8b71eccf19cffac8b82", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.16.tgz", "fileCount": 8, "integrity": "sha512-JUuIUeFvMrwD1qyFZMClmw6BBvB7zdn5OtfzLBLqhDwZfOt1DFUV4ylefJFJdNgId9CIbbStleJJaAhj5Lmsjg==", "signatures": [{"sig": "MEUCIEcqXLk6AFJ31yo/yXwARFsMN+TB0NqsrJMfYAmvb/CRAiEA6PUGWli4qo1cNZzVtfTmjzT23qdavjFFBB658IzwBCk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBIPCRA9TVsSAnZWagAAIAsP/RISFEV260N5HBaAlFNv\nL0rc7v1iHJjfV6jMAK5GETGeLdAefx2fKyH1w1PEn4iaVNX91aGXrrTKzaH0\nFM4R6CGRzOV8zN+UC3zFaFDCSQPgrqDH8zZ60z99tRJ+bDBtNge4IVxwnsID\n8qGvv+IYthlvRV+Fjp45Y4mv+3/NMglkifnxZbhLuG1RjK0uYKkq835N0wQI\n+zIxm9k7JqoWatYlhpMd+ML7NTEc9evVPG+vstNhrO/5uq24gTHIaEy0ifbD\narayifVPrJxMtELevPOZLoukQH9ZwP9hTW2ONzY84yP/W0rIhmd01LXPU90w\nRMAmETByZxgBXDjfuokWSRfnCwMt84EPkRBfOsKwzhyhyUQKbqbqmdvn7XLn\n4dg01ZkXlHuy1Q66Yv3eJsScVWmbfxljkMheuLOhOrkOf1bEaMCQ1rDeXdHb\nf53i1BrKcmL4ZrOLZNzUKWKnGbYGhCPgkZdh3kQKFH7lVKbcROHboYdOnnu4\ny66dOyT1CZgAjV2oHZ1OxyPE/ZoHTpeuEDTJ2IwILOnGGkFU7l1pDuEq5IDE\nS1NdP4yGl/Oc1TB7xTKlthmC9ptv0wc/LzJsu/qt6s9lTkN/wnrClsEaaNPA\nXDqdisBo6paoSXnwjCcPTlZqAp15tpjJpUUcUGC2nthaD3XheY1ba0PI/2xc\nhKtj\r\n=oa0O\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.17": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9ecacb7790134ce965fa06cf7e55341b3480c7ec", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.17.tgz", "fileCount": 8, "integrity": "sha512-3VcWqLrQZFwkVj34h7zzUVLkBPJ1N8PEkC/O2aJRD808s3WMUn6SPB8VjFT9LZTi1/19OoCnxsh0laJhED54mA==", "signatures": [{"sig": "MEUCIExMYXN94Qx+TFmu/ZQK8tRRIZ/isWatigxveeSEUisMAiEAz/CTqDm8efU28dLReyqxBYhb4QLp+fb4OSh3dV0Sh3Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158247, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBYcCRA9TVsSAnZWagAA1/QP+QAJVONUXI2jJjrAS4qE\n44oqYxqf7bxlYUJQG3MTGD3TJbIy5WpYuEBhqejEfx+R9D+rnoile7FNT4qP\nmhz2SwMBm1BGqc+2Ham5DeIb5BqL7rWyr38XO4rk+N/P9m2m/YyIXxzVVTzD\nBh3hqNrtVt1Q3sRIMkUHoE9uYuEgmdi1/vG3pwe3nUs52QFAShRv0iI7Ai3n\nBIT8bilB2Q/iksBtPaccDAgX+N+aKuYrPPgg6nyUjMUMVi6N2ZSty3VW6x90\n7hg77Ag0po+uUhp/nuGfDcojR5fcgRPMeuPFbTDlQ4NGXr3hcRvdV1B0r3vA\n+Kzsw1whSzVp+NlRYM/RtWwUxSKyYH7s5gJTC/M9eKGW1uEU7wx8SaumBxqo\nSSxIe0418sVtSe3+LapPBLwO3h20F7G4QGvcaY7VMMlSQWyovTEuB0PUt4Dz\nOvHLaRDQg+KOi0tdwNOudJtC99ezzWGA1S4vDrY+h1+O0gOHBLk/sZhHCMe/\nKIGiJdVWL9Gt5cXw7PqncooU2OGz3JzfRVbRfu585YxIrAUvVTNsJr7IToUI\n8IyH2J3/h3NrIEz1nqvP3vjy0YGxJDpHmKckPB4CxaH8eq2aZCk9kfqfNBTq\nhPZ1eIRVwKbKzSPpmU60Ucw4pyJnaYsR7X25qMJOkR3C4jxAGaIuJExP2ZTU\nURq+\r\n=g7SL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.18": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ec322f6341006643175a5ad477e7d5486466384c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.18.tgz", "fileCount": 8, "integrity": "sha512-U/T0vaUlXYvRwLd2Zdt+Lv6iNKVx1tpQgw+794KYW9sCl+xI88+HSiPjU5xO4GkHbVUbbvqwCbWI9mxaY/mw0Q==", "signatures": [{"sig": "MEYCIQCbtlqBLqde3QmF5kKCnqkdi9QD6yar9HBrAXTrCmHqbAIhAP0VQ35GUFsbxpnVQlkC3fQOXJvzxS+aDSV0czRlsGJ5", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlliACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrqcg//elMHeZJzHa0vW/Xum29edSzbU2vJD7WPru5i9XIGlOS4s1W8\r\nehogyMU47TTGkT7w70GmXqRKfi2PrDR+4aG0/LCrp29iZYjoVFVJwuYT9utE\r\nusiS5MJTtIWS0bVCZNnHHAGtZANf2o7CX/agMSspKyyx6sJkQKOmK4MaQDJF\r\no4KlpICTX7cWBteliqvZF0CD8w6M25ZG/0fCqZmKYjPGd8KkOsKT9Yu3pkFb\r\nySOgrwrWLawTySbNgBbKX+t7OAUzrFaHK8I3kqKKQdBldEiOM1gvi+ZtkN8T\r\n1psvEmN9mCjhUC1M/XSw594N3j2SXjyWroZWB4E3d633PGLLxh/9KFYaMT3e\r\nFwAcObMp1YRBMD228cGcOMThdJHfbXBnN9EKjdnWSkNc0nJ6L8+rMPX1lOWe\r\n/FP9uz6vuSPXQNf6rQnCvW6+EfcQNEXtawoYZ+u8juJAGRMYHfyMabwKmN6K\r\nNow3DapLITdEeVucUdrY3T5npmdFkxVDpVh6K9X4+mX6q2GzZQINU59zCWzM\r\nWPc6BGqRVtgx84EdGdOXXTvflU7ZuYS+i2VtJhADoegeJ0v2P66usG0isNE1\r\nQWSl6AFI9LLtTrjuvGy5t5e1GSSXcd/P5+8V45OeZv2E55yV1D4+z5dM8amI\r\n3XdE763kEuixM8+5PA/R2RVL/oXCX5Sn8zk=\r\n=O3zq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.19": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8aed70b6e7c903a5bd31da9d32491337cdfb9f8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.19.tgz", "fileCount": 8, "integrity": "sha512-Qp34WkD8V/FTaoO+oYgrBmUmmDseec0iPEJ1cmACcJ7Rn+heaujUlr+fs+dC5bnpFYulEG0s2aNgQnoEi0hVkQ==", "signatures": [{"sig": "MEQCIFQBnL0v8UO1U0t7JiNkZqwiUBA9nk5eSGWCfdpviiVQAiBsdxwG07/56/zlUHNP+QWqIHLD6smQ/CyW4EZRtXOGGQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkVFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrmaw/+MzKZraXra1ddYgPGZVfUA3F75kGevYfGkTBJpXHp7ukgtzKs\r\nsBwOe+lTbgdzTjmQWKXCOqVbrv0L7p5knWO7KelqjzQGkoSliPsO6R0FL0Bd\r\n+El9zF98r0DX8UEoZv0WKXIGJtJqQ+w/unTPF0csTcUki5tSTPBg7R+bOhFy\r\nFp6U3muwpVPNHIvEcx+QFeqFwfrQsQ3jQOanxXjf8c9DTBkED673TgCGP96A\r\nYwCdn3Sqi2Qqa0vU5lC8JT9p6xBgoK6C9MpEm/1m0OrYd3ot/B4nmY7ClyI2\r\nLs6xtNJFQUHx2IPMl/RP/IZsxqKGcuVLOe64Grok3/wOlnSmLwpCYYE+Z/i/\r\nrpNsE6TvFTR/KRvaM42fRStiZ04Ypkh6n5eSuEn7l2VGvaXNH0vCzZnhBkbC\r\nhltd/YWzXCKUsle1fEiNK0jPHeEsbwMTxdGVZZo391Dh4RoWviMAzJxHqmfV\r\nUJSE5EgjUwRy+uPDJK6qct7cd6MPYLxH4o9NYKbFrRZaWzOCBF6WQW31Hmpw\r\n5zX5FV4utN+D72rR946lgmpYenlxR+x/LVmMK3OjNfTV07Og25lFxeWireHO\r\nHfHPEDEPt1aCS0kliVa//zeN+bKA0DFeFLXgNRObVqqVesNo2Csgiz4TNzAE\r\ndqQRsKu/ho0jheoJdPQQZ+FtoiDQ9mA+CXg=\r\n=1zwQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.20": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ac4cf5e50c71e973d11f936840ec5e78e611a9ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.20.tgz", "fileCount": 8, "integrity": "sha512-Tl2SfZ4OSVRVcFahvAqfdl21DrqFveqd7YgGsKW6hK3aCE8zBBG6jj9GGEkPEf14rEubpF3FxsDaF2jcoOD5Iw==", "signatures": [{"sig": "MEYCIQDFfYEvkyV7RoqJpieFDAO5zU6ArE0jAMb1Kxzsi0gHigIhAKwqTNCUjXcOTq2VzoffGsu8umYF0JKG/KteUny3k8XV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkdUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo5Tg/+N71K9BD6gTIvnJfNCvKDFDl/BUkYvk37epPe3tEc7MxOFnWd\r\n7edfHiQArnEP3gTjG/gzxyVOlhSwbIx6RlCG1egWWoOauUPSvfRJHevYt4zy\r\n7WAlMhN7Hj6bLSagIFUQgryh3s4yKzwGpLPQO4m6lzk6WdSRQEjed/+KbPI6\r\niPbLqNTWYWULW5DVYSSnacJROgrH7sI4VRqKa7wkNlxF6ss6e59dlt6F9Rb7\r\nH4T0Mp4mGXAu1uoVO1yUwETLebFHLlPRmTF70T1YX/Rfiya4kw/CbTj9cQC/\r\n2Ju6RVafHPG6pVUcEO4TeZtG2rflSe3crAi3+ktnZ3e2gpMwVETjONgVu8yY\r\n+v65pGO4dR+XdUyNX7UUMIjBmTo5um19pCBEnmCvcEqCigcGr+diBd3xSojr\r\npU/MlIGc2fdeBqpS3HF2z4OVDhLNwTHsgqkMgBOxmQFnymOHn5A5jvpjuwY0\r\nUYdQuYpS6Jn6Wza5HOlYwWbNQ2LOMKRjmo9xHzcOqz7VQwcnVLRCJOjjRAsQ\r\nY7e5B3hEGwdGvLN5n4Aj8qr+Y5bEkSEdZ7v7yiMiY+Em+a0mPMo9/gIXs+Ge\r\ngN3SN6kmlY8ZB9bYb4G78/PXNy49m1ZK9ExJ2dVC2Tu1KJEAkqh07hYvObK/\r\nYcOmXBGYkQ80la0mlVbtn5whuk4CzfIAxsc=\r\n=lpaj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.21": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d83f19736678a94046122f41d1329f4ff31b0fec", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.21.tgz", "fileCount": 8, "integrity": "sha512-Kp8X71Gzei429MM/UPv1IQttH926j8F5V8DBf1hDv8UFbPxJOmZrIC0n8KZbHfkeqGNtHzE68Us9f9YyUvEYFQ==", "signatures": [{"sig": "MEUCIEwLgo71sy0oZgFyVQmZF1o22gbc7ASzdfoVnSzmQm/tAiEAwexp5o5HvrvUJdoJaQ6JrrttZvgctH09djFvk+9mBSA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFky3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPTxAAnb+TH9nM6SxcSHlbKyTbjgVxMl3HOplR7E/FYFrAg2mB499z\r\n4bUIKuSH9c+L8OHaeA3kpJr+U1Y4U+w4GXkdelcL4dO+X6iNO6/qHofJL9Ol\r\n+V8+uzj6Cjx9KxhJfZpnvteKUtLhehxsCrdERct4cW7gsH0v0cLBEY3i2qdO\r\nLx9yEcNiV9uh8P88FTb9nN+cU9uK3cDFukzfkkko3sAB3mhctFzy4qPDlqV6\r\nVeb9cPoyNL083OJR3XwhzK1rTWyDJt1cFUxRHkkikUAWWtdc1utHCux2ixk2\r\nwQ8ECG9NadGW32zx+ypxbtCKERC9n2Fluz4VklZVadUQmbIiRVF9/1GI4hFz\r\n3gGF0GZZXN2RegSYXytHijFEHheY0QGpN4Q8upDWHaKSr0t3sfZrPxo0aBJU\r\n/ifI3jyPmfuTbgj0Y7rGmkeIbfYgXEye2xCfyBlvXG/EoHcrdIWJpZ5Nzuh6\r\nbCqqARUzGNZSlCCAJgj/aY1uVlNt1dWlwDvCEj3rLhEj62crJWVnJo5mZVrE\r\nRZEQOHC337kJHb1VrWtGSyiGhnj4nMcB26XM/yVkh+Ln0AoYB5FIxEbpcCiP\r\nyrvOiLIvo7RYpFDmxhxN7pFOnNrbT6dH9zQXFVoM0IPI9IbtnSuWZ8qJKGUJ\r\nv6whZTo7lgvGe9Pth79iXI0/S632mvkVKTc=\r\n=Fw0k\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.22": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "71cd46b1b6011e6bf3f2a130f782a0a98d8256f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.22.tgz", "fileCount": 8, "integrity": "sha512-dPZGVYFc+Cat69395ZUEtxiuEkg8SKdy1XpaOvPsoYSq7ovJ0Klm49Cp/D73jlAXnqleyUj4ZJrJeVFcdYDwww==", "signatures": [{"sig": "MEUCIBnGmWosEAsaXXP28Fj+QaVMale8bBG3Flo89EGxMGuAAiEAwaGaXUiWF8BRJQggaUwjc3uPI4RpVp+Opb5paJlz41M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlOEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqvrhAAjJvuLBGmIo7/ExsM1Pu+bkskWKySFztz7gh1oVEmztYNXWmc\r\n9Y4CO0xac96HeN2eaM+J6L/4Q4Cmcu769V6NX1Dfj930AabE8HLK3JyuppAA\r\nnbuu3figfGvRhGb0tktlToIBIqFSo3GzHSUyh9MUhi6PgTLxxcUbOpUMmuom\r\nu/PZw78YlgUeFoMZ0PATylUT70RiT+41EDWIbBurNQ3HyDIyWbFa/DgN7PeP\r\nmhPy/53074i6bXxzM0wR9Sj0b+5iSd9eHRVzRGrLTYlt3KPDTzZs+XB4ZcXt\r\nBQY2QdFHDIB5Kg/307lXdzJwNs6Vvpsg+QMaKRdMQNZLwIv4JEb4INoCJ3vQ\r\nYDnfvATWP2oRsLfFWRcE6Oq9syIzfdgTGiBAxK8spygeN2s7xkEU2EvIh+Jt\r\nJO8k9TSY25rtcyawXYsJANmBAsEeRye31MgJWENbM3Y66Mcvv9TxAPe1giYW\r\niJWhyxdmWnTEQxfkA/BQ4EGwqOZ5XHwEqzSjzpiuFXOmCYNObF9fc988sity\r\nmrkBU62Ti2gjWJGqpxncxglp5f/WMW3ji0Jn6Vf6OnWDgUZ4gE0ziIPv6Qtd\r\nvkiuf/ugEBUiHp3bKyhlFBEtPRAOAUerzFRrEJQ0GUs553C3TJ2uiQv8UmFB\r\nwdWvua8vCLgKsToH02xQQ73CexbeqmmyHmU=\r\n=waA5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.23": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1102ff26025c06da9c32ea58d1b9ddaf7f3fa51f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.23.tgz", "fileCount": 8, "integrity": "sha512-FUmQ9hM8woZ/Dsg13QqQoru4qJeYgSgpYpjcDmDrUePqZo8wTYjqu0t0pxVJOUex5nIB6inhbif0v62Q5kOhpw==", "signatures": [{"sig": "MEQCIBTMSODhYwVvdaxvu9uip3wYinwVtKnrfkANZ9nuFX0/AiAiVccVDhtqxlJt/6f7gQ/q8gg5cithMS5h0dvXxdilcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpD5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKXA//Spdj9RVYaLx++6oyAx2mrrUOjLSfG65jzhQx19h/8KKG/E8y\r\nJGcJKaOzT+7HXg4ix5m0LSw4HiyNPTPMknH7BmGGJeWV3acWZcw1zrmA9m0+\r\nB83ZP89/uenxL0rKoyIgGPa1lfQcC3KArSOGD7z8BCMAg3X4BXgXD0GFPCI9\r\nvG9Ip2Qs9IRBfCZZOAN9SV+bwgtfAqmCVtPiJAhZF9v2XN+WQ0IIiPplspru\r\nWdIKqP6pPh/tSW8CL+Gsp9bOPP/SqufCdFlxZe15FUEJzI7oQTY50Uo646C4\r\n5ptN0RAqORUQ6gFzLSLxEdEeZnB3vnRyyScV+1r1+PsggRyhoAJO8SanW+wn\r\nBTVTlZG7JkS0xEO4EDmJdwqOkS+LiW1ww4ddmqqOeOMbBc/3V2Ef22rV2bMd\r\nDuUbLQyFGnEZcKFMmSROiVve4SseOdr6uPvvpTJ6ultzXQks44wt6KnFgVqa\r\n7rMuqXbF4a9Yd1ZEXygyTnzWQE5aeW9enijIuet5V1DQyXoa34d5wfPWluqK\r\nnpnCGifxlu9iDTYTkkzzTNxdgEH8fs0OtPNuFmrPkbOdydlTZ5ML1J2p2DRn\r\nzdbDzvFXxE0PB0Drnv4QeDnnjIYUmjb5HDsLHmxkIt2WkNHWFqq9WLO0xvMY\r\nnFn3K3r/VTA7GA7/msNVW9pjz59O/Nahyq0=\r\n=1aku\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.24": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6730063e830ee02aab692f0ea7a52043ef8e024d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.24.tgz", "fileCount": 8, "integrity": "sha512-q7vFKh88ideZMspuKkS4w4m9VAX4oULAm3Kv6qM7S42HIkv1ncb2EiWMfNf50WTAe30RPfRM6Au1zBy9BocJhg==", "signatures": [{"sig": "MEUCIQCYoPMph3wxrM06zjghtGkHmGl+5FHblYDpBtTdV4oJaAIgfEzUFh3LVdvBge/zOsA+i5ebHnCxM9oqFDDzA+mwkSs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF31ZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfNw/9Fh29O2lsn44LangOQmioB6Wv/xuRh0lgoOJboNq0mJZfzNX1\r\nMbYT4O1s+cj9FzokSdDKDOzX6huvg2M5zxKdP8sOZna5h0/q7E3/kNJvXlkC\r\nM8mFbG5T/LT8HzwbPop505sz19ZHtWgnGXpFRacXId1xUDcKL6/HWHojQk38\r\nHmMkgYAmkREo6zqFvKfoJ+xI60MQDhKuoxMASQXoFRFmrCVNA6jf1UIDI9e7\r\nXhHaPOoz2IHpcU0jrRLo1aigAIS4LPRzxPRqdXi1s1+19IcVfnX099OOYXsu\r\nUyeOX/4YU3YQBNmZiT7POQSZB/ljFaAGaLha8/8tOMX5MXEw8IZ/4r4N+4ZZ\r\nVcTbRC2UD0P2Mea/4vDbaJ5yi3+QuU+iU+aZySPxaBSTmj30Nilg+FK0exB3\r\nWwqvXGJSKOV48YKT3UMQ5z0YrmksPhg2TvLu/3H+kFJWvfZ24JzVoVRjYlY3\r\nACaP7J3Qd31ht5fvJBiAyU3GEvDBOVLgc6HN4tuKq7d7hVdGcB77augtGc7f\r\nBZspyMp1nzXt1CkJYtWiRSD9niOmn3FaW6UhG3IFd/xxMicBEWSBST4/rkEZ\r\naDDULLBBKGEKpTgIUHStrIY91ISVH5bBycoixyAR0qGSwZGqRglkIWV0u/+u\r\n1JHrCU6rKFWrl7Qd7oGZpy+MbPNiO3hQeis=\r\n=Wzts\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.25": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "20fe6e4641ba284de04dc11be854b66cd9638df0", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.25.tgz", "fileCount": 8, "integrity": "sha512-BMzdgOPzPI5aZydIX0wknAcgdtrOyY4L9vJvgU+vY8TadwwwlydEQXzG60x6oCzjYK4UE5f9qlfUiKCU/XQv3g==", "signatures": [{"sig": "MEUCICEfOK7M7dKCKm0qppwzhGkhkPvl1juFLxygQbeznG7AAiEA7beFmAwPOyBtzehmgm0erUWO9C7k9Bnuu8vkXlqNjuo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4YDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoeXxAApMCsKFpcUmdOXPHTzZzlVtxbdPtMAABXXoGT2P6+z0HqTzv/\r\nvFK8dHmPfbLDeQ0KkD0qSPEJ702V+yXuCo+V1U8KfPiURxhr2GSDUU3c8bZ5\r\nmbI3s26HXb/8kQ+cfAzBFLgGAkZkOGSOdiNd6udFEyfPR1hFKta5uzKtmcR5\r\nQgP7IINTU73gTNU8i8GXvmZZC0/eBFl0X0hN8KiJ6QFp1O3F7n8dlc2pNQO+\r\n36I7EifL/8sgLFCqhbC+ftbC0da6KGVohMONyxxUfNALdcUMXfo0shqqIlI5\r\nn6NtBDBPyl077EeUVM4AGtyYfglTFdHX5eUpa31vZpEjS3ihedd9vNTreo09\r\nbNsUBrUoTi8GU0Es5iF15grmrJ8ckumu5mRz/lsu9fikjgfH3o7OC3fdQ2VW\r\n2Udy2VitHqtrz9+n0JALrmVi7PFe59JtcnZ/w1P1kRv1SiPMyzEEwX3QgbDK\r\nJIwnNZT4fegrvN0s7qdpTC165RCz6rb+De2Kfg7yRVNBjGqdlzqtAeG/AxMK\r\n42b67cmL2KzFz7A9WShi5V9wpVIj8vPJqXqyjgvucOCEuRIgeTePpFnaa/0Y\r\nvLjIaEQrLcIkhFNiNTi4vDLdZ503BZjIr1F4y7eGvlXCMvmBjsxR+q7u4wdk\r\nkV8WzQDcGTNo81SpxCQ7dgcO5Ry3yqxY5e0=\r\n=OPNH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.26": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e06d258c92c415331023c288c0ca5b2e10b6752e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4-rc.26.tgz", "fileCount": 8, "integrity": "sha512-qBdBd38VyUcWnXLeqAVCNCQ9xuf748rtr6a4iGrlkQ8VkP+a0kvI3B1JiWbkRfW2dz6/kLHTPqeSiZYpWMb7TQ==", "signatures": [{"sig": "MEQCIFCSrXuYpcFzwAWDvuUCxK5lIlcLO3VvpJ3uqpMokWeGAiAKPZXRqcgyNAIky5KV+U9lZVFKHdjKp5hny6oxEWc9NQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158248, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOcRAAntbEtQYKtLAQcWQJZ/4AthDY4naLh6ibgd47OyyRkCOyG6SA\r\nI9nvF19/gsDPnUNnJ4bO5YJAaKRmrHXNmovfpZ4jJ/i82FuZAy50HJ8085E1\r\nsVbvWJAqL1fv6Q4/bT3bSn4jAUtSCzixXfxzeQbFSokxRguTgJcN0KGmj/Jx\r\nQGq2PFZlFb+j/sm/rMDC0nIdPf85yqgiADO28A+HbG18GLfcVQ4VX1YC6KKC\r\nWxeO4CWrACEYIW7zg4BI9Tsjc0fSJEMu4OeE4g9e1zdV2MdY+2zY14dzhHh3\r\nBHBiCGdCIGfHIX0S9cIv7mxgR5XG7dfUAvlI5QMfAJ0Gpo6Q9YM/cgE5lT7K\r\n0VvC/FFPMiV0GWb9Z1q6cQNp+2DyrzPJmtacBwIwg61FoosybABM70TOCvVE\r\nTi1C8pOW7a8RcY6S4PUzF8tIWX2+HIOknxr5Z039HC8RwYhk1jI6FhJaHSAD\r\nC0IdEK7NSzgRoWwb0eiUo2EanT/M/0t9G4mMNBqpPhvjivSiriIm1TqzWzA1\r\nXs4ZyqZfBm+UnQ5zoKO5/efmJIhmbWO1oJEHhO+PTNRPAEWX9kFyQV6s+3oK\r\n5MHPgIk0C5GKMX5Z42YGthnw5JuYQzqEmmV1/svibxUk8FE9O9hnM5OqwZYf\r\niiZ6r+O9VBq1rwI6JVDlyXIK9nnc/Ueg6oM=\r\n=jkdI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-scroll-area", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-direction": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "be1d32c113ee9f64e3d2e7ee3983d98f00b42038", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-QHxRsjy+hsHwQYJ9cCNgSJ5+6ioZu1KhwD1UOXoHNciuFGMX08v+uJPKXIz+ySv03Rx6cOz6f/Fk5aPHRMFi/A==", "signatures": [{"sig": "MEQCIDGHQIkh1tjNMEt+UCxdugXT5qtvre5u4MJ4+G3JyeESAiAKTdiEfz7Xzht+CYd1/0PR7WoqXGTIodBk/vlqvCZShg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158202, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kXACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrSpw/8CleY5zoz8P0hqMHuBaZAoIrIkU6UzUEMVEJIhcmNPZ7o6FEM\r\n0Fmzf/cESpID9L2ob/cdj8LtlcFwKyX884vrRyPLzcz2oqx2gJvlrA271kIw\r\nPzfJtsDqZjcMZ18PvvRASkCeHBVsbUgS6jDsgCkcKOGlEzMXzi8J+Efui30h\r\njqRVtK8lspCAYMawqG6JwLoQdBbmVPT1lKdZiHApsEYJy0Rc/37HoyaAlm39\r\nQG7SocbKF7IlfuFYSsZkwRKWE9Cvdnkc6Uv9O+aP20K+CxrY8j5X4Yp9DqT+\r\noLa8mBFKzRzuqXSznBf4vH71ki9gqKjogr7uvn4mvOcDbb5akTsoh+6vp5GB\r\nnKs3SGtCr5kp+A1EXnjQ5l0zpKm/KgBOOkGIsaTTRHIZseJD/mQUIMtHBaQt\r\n7Zr9vhsBjtkBuZ2vgAvUmubJTfbIpsVJretkWqtgbse1vOxTCUHTPTuqEo+B\r\namfWvNUXSPK0h+/V8+GimFpXkuGn0YpXPTjG0nVjqPsX1kIVo0InPy9zF1Ao\r\n13JlKO6NjD94mVNNm5ojpzwXJhojht/QNgIgVh1TOPCLp0EwX12NWbspPMoL\r\nQFH5/DjhqIYU5WEFS55jDODgsCCl+fYqnAbDPALpNYZ++SaHBt9wFY9A4ZDg\r\nvk93/cCISEeUJKdBt8M6VKfOLEj5ns8WE6U=\r\n=2XMB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-direction": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ba08259cc8e45d619cbd3a40bcbd05cf9629ad9e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-7u/tTzDomTMuUs489JxRdiScdaNV8QeZ1FLuq6L1Jk6SRPHYy+uxZiIgaTZH33SDdRlmZWfcIHdv6xqwirLf8Q==", "signatures": [{"sig": "MEYCIQCEGMFNwXaBfLsuSrs/Xf2gZOz0mpqeBHbzvc4OyJVThQIhAKADJzq4kKZctD1k9sqJtAbJCfbrG6aRzmmrR97du6Ke", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158280, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVD7EACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmplWg/+OsJFJkjMnS6fGt1jlMg2avC90ZwjXI89l0fH2xSw4QN/zPXf\r\nBZwIW92ZkhRmoCvD776pAn6e1tG+Hx8QE/4NmGaxaI+P1Ess3qA8OCH6zmXQ\r\nNjvm7T1VZIrh+70sxMA2VUlkxVAAw06Zb9SnBIJ4fwJeAlGpEoxYH8ENZQha\r\nGxk34nYe9f2ydrQfWIpLv+YpTzCL6oas86zGkgZ0XRx6r97e72hTMN15qcKR\r\n/uQ9Wer/XJtShmtWnfmin4NIKbfBduxOhjcBqsQicxUnOC1wBHT74L4gdo4R\r\nr1ywNRHtB4KIaDFz+d1CZ5wG/M3nKjFJ59pYwbf5NK5YDeABvdmk3hwOuiTn\r\nOtyU/j2waCx5kseU6q6vxXBef39i8gg0H0wdJmuS4QiqS/Zr0dSQduBALdEN\r\nqm+p4qzKuCKR7z41dY9N9vcbXBSogHfRw6MXDxVQmtHF/oL/Fq33cSBz9/cd\r\nfVc/ui1G9sEOznUpO39l2rkx82YcZdUUPzJcJIKX5cOh8tXvqm9LIpaGJWhs\r\nhc2LL7ALCu4hl476ndrdkuQDRltLOroeeNTVEP/+NGiNqShbvJMFphbjBC/s\r\niQFQDnrhutniXblrOcPg+liJnwBoqanRA8QJSTqEHEtEk5KPTYBN6SeOcBpX\r\n13e0+nEmGCt+KbNA4drWiAv03J6RBLvu0rc=\r\n=C0I9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-direction": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "45899d8f13fe420124bc10246d7d53d449224f6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Q3DrihkP8ujmSH+akO1AVRgy8gZ6LG0OuzbzrpQkxuM34YGQvuht09utUyMgl/RftrR4xt6EY34FrnGLN2quFw==", "signatures": [{"sig": "MEYCIQCVIxkkPS+fRyTlrXiryzfgWt6dK3cLkoeU4n4mX7xKIgIhANeo2VSTtkSeJH0nD11bky84zrTfsg+ec1xDQ9KgEfK/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVEIJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7sxAAoS628P6b5xqxGNDU2fxuBaM2MYldKRjIBBqGm3TQHAZHqeAv\r\n+pJAHC+at7DsGR63xYu/9l7GAeOuIs8mJVkBI3xdKfQJVGHqVYrGivDHhlzN\r\nc3cRHD2RiE9rm+45cTiiY8vMdKg2DzH0Z/Fn9l+QgGE0lB3TxBdVYUEdUfDK\r\ntUsX4xkeVtm2TmnoXkn4Ntlc3VJkc6PrX1ODw1h2Zm7U69MQ/BSXFMxJHL2r\r\nGMF8PTMD3xP2ipzOiRw2BbVFG2931zdmTDGD+T/WVae+KOTsEYoosjIrizKa\r\n87c5Eb8YCwcCeNx92FS848LzpRBVbkXiXg8R9hd5SiuRE9FX58nzDPle16tR\r\nesmCfmTKvfRrrtDwpvxEhUsA/mYJWP4bqyERkwJNmZdcmevaISXs26mhGdVZ\r\nkIwrpgMmUNkKbNVHaxRSEm0XJZ+fKlG57yXLQlghOJVF/XhaGvQkW83NQhGL\r\nRURA0PUr5xdz2Xh4+Ho7SUNAWmYNwFBXVKa6RcB7thLFbB04nC5z6sVDGxcb\r\nzlXtEf3aW8D8f9xIMfAUzyjLp6bh+t18A+xmrrcbsK6BTMNWasN9iQZAsj5x\r\njKlQ+bf37byWrwF/SrlAtmZPsR5iv4F33w3hvY46W1xywPxZSthMguhF7qwm\r\nDVkHHmI1hlTT2cYmvbUAyLZdDIsO6suRZEo=\r\n=Oqpf\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-direction": "0.1.0-rc.3", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-callback-ref": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b70e4174efdc6890e9ce4f197822804221c95188", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-MalZ7Sob0gQRe9+gcbTSZBAHGEecjF8UJpU1hmFrez4hlRAb2scqR8AIMu86CsI+dEZNNWCm2DRDjoIv3AOPdw==", "signatures": [{"sig": "MEQCIG/nFz0mhaamz02lDerCjGMm+2CxTeMV+5dlYYcLNPy+AiBKpaNH6wC+ZWscnryvYTRBj//FZqZaejDJBINqWBkydg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158446, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiVUTpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3iA//dTcS4wJruLsmZRQ3pD84JhQQDKeYn8LV+m/pSV7xzFHuAeBD\r\nP3n4J6ONvwvIoZ/bvtG/yvGOtLrv+ydAW/hZFUY+KhkR+7TXN4+gFUdmhlsD\r\no3NTXQUnWFNv18R0PtXOyijbuv5MDt4GK7WQo6S6zOwX1zlT3fAbUh2BT2xU\r\nfBy6spayYRfkbHsQerO6hPPSixKZoVIOW4rHLap9VXRiTmmFN8ehV+7KKNO+\r\naEYuMnXnCdURhO+CCOuUKoNyBTD4ddw7CRpUZBjg4/2DvlE40Q1e0W6ZFxVK\r\nVEbb6uD8GuplmFi1cIeqaH7URXDxoG2vY95DnPSk3UwCnLL8yPTg/7Nf/3nY\r\nfCuFjTJz9cvEahw+NqimvTb2/luSEIGoV3V8jz9IcsKNflP1OiNno1zyR9Uo\r\nLezsXmLX9aBbzR70WlV7DMYg/ucqiED3CIFeqJ5l+xlD1GOZK+EvQvHJll5M\r\nzEmQvPH47MhF+JkqgsODw3lnCEm5sCPa8YjW55YhqRZJA5MDq7F6NOc4iL3L\r\nvhLtQJNL/YwRTgfv5mgposs3XoSbS/LoeaT4iqvYDKkiLjcVrKd1/JFftQxW\r\nd3z+FCa7Maq/lpZuQ55HxwHRRPVTs9rJXicr5ktX4mjSHbe+JGSCU/a94x+T\r\nOaN1okuF+WfGWZilOpu80xaEM07Vc+9/Y3E=\r\n=97TE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-presence": "0.1.3-rc.1", "@radix-ui/react-direction": "0.1.0-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-callback-ref": "0.1.1-rc.1", "@radix-ui/react-use-layout-effect": "0.1.1-rc.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3fbe4b2f563dcfab02109f6d1d4063c3fcbbb78a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-rw8MmXcvt2JHeKhmNmYw/DxRQN65t0klsIC3eSp534V9FzzuN2VQpgknfQkvnoSS7yM17+qkrp0qcWDiiShuFw==", "signatures": [{"sig": "MEUCIEUS09B9g/dkdY87kUTpEmIzOqoUPMBFnlRnYOjv6ck3AiEA2nTQx64+9Y8vKwOSn7KAuepnVSNzGRzRE05ptkOxxrY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWARuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqaGBAAjV+J9G0Rhk0IgN8W+FrE/8J5NbmEvQzBqztS4vKAUxBHDifJ\r\nuV1p+do9nj7t1yUTl03vbxYWmhhsk6yAcgiDzsudwOBKpSdZf/rdSygsT3x3\r\n8GUNpn2/L++QDzM70q+dL0s903YVTI6RT4kw5IMuOxxKXfOFc3jgoQBj+Olz\r\n6bPpX6OzLpiSpf/rXeCOabCBqnBbsNnDAw6gPurxa8+X7Sx4GYX9cV3sRoL+\r\nYAQkklUTozQgTNMwaWUJK54BLKmJUSHR5r6IJqTy6R2yPhN4BQFLZyRQBlfe\r\nuGkWMpv7jcF8BCXi33stDOd0EZqtl+y80drIn0jbWX0QKYzM93NJuqHpIPEP\r\nyJFmKdDLXHxMzqvPk2pJ+YG1PbmWvsoJb9ZX5tG+pJrvVd327Rz/Np0b+1YF\r\nRSPuOOmb9nH694QyPNiQOyXxeRbEuxdSSjTenN5abFDlNSyHwNcwX8SedP+7\r\n9uHS1SOhhFBBKZf4B/enYPC/gasz0sdOMRRvQkFPa9eQ11Xg9KiGM2PzjM7j\r\nHXFaBGA3dCR+FJYGUGfbTjc9l1OmNJuz0+B4zHG/6o0l5ftZsmZbV3SIO4a5\r\nBZghpZ24OWDHgvKpEsIbWKC2/+qDFbYBFRM9X71gCCpdf3bpaXmNFqfJrMR3\r\nX6f4j0BXDr9FcCNVxF2IJdSHHoJD+PbOe2k=\r\n=f/pK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-presence": "0.1.3-rc.2", "@radix-ui/react-direction": "0.1.0-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-callback-ref": "0.1.1-rc.2", "@radix-ui/react-use-layout-effect": "0.1.1-rc.2"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cc40e68279a37382030b172e897f337b6350f0da", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-jRjczPa2bdcUUTaIwr2uYdpdaAXVK1VFmx0GopBuaj5sXY7m5PtG7PtewLTUhV7fb5l8Ulv8sa2p54HTxJN3Xw==", "signatures": [{"sig": "MEQCIHaaEiBUtjzV34KEBsHMwEfEBmaumXHCJvgoQ2IdTyRNAiAgxqg/spCWDkj2B3Hc9HFe+Kbcd1M8D0+vICR+8YaJZA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 158485, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCPRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoklA/9HzRjie3JbYCnn922nbkA0urmeoUQeMtlJOb5JQv3UO4xhOQp\r\nhLQQEgjoBOBWXmGJAcjuzl0IiqE8AvaaiOF0QeSG48099NcZxv6V8FttCfko\r\nyeJH+hW5TZhKPxJj389BRqIEoeRxaQMLllME7QAY0rqeslFV0JIXn5yQ4Zo1\r\n6bA4nUzKbiIfjPc36N22Ei15P+QDDaxwCMpJeg7UsoQJn9jwgCOVmv2vfkct\r\n+wTHAAbamPfuSh2lJBHQAlgcnNp8+DEme+shcYu4GYL1RJTWSGhJVf6S7yqw\r\nzrVjRcJ1zkPvBCsVO4Rq6D/o53w9DvmvBhpPaRCVQwc158t3rtiFb+Ca9kYZ\r\nWbRoCVprMzygzHotvbeCr1hWaxh+AMCAGOddMH8hm/KgullbQv1djxfvmAwZ\r\ndk28Q9glMjtpqN9mcVJLQJiF7mZTDoGljyhMUh4oZImleVfAkSrsmB7Kkhn5\r\n5F1cajAO3wGVzQeP2cDHMXZIpgrBuYQl3bznD8bzTj2qLfjlVkts5hWzQWJK\r\nLqY8/u77+m2gySAX3dYRARBMHgDnsXOb7mGq6IfjX7bESHanyp1GE4gXit7A\r\nQU7Q51sN7Uf1Pz2bV98jS9KH0oUr/WcCB9LgUxifZ5oszi9eeEZ+bUlBCxeQ\r\nzZghYxTXtlxCpj+y7goOODZf72415TEX9CQ=\r\n=igQm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-presence": "0.1.3-rc.3", "@radix-ui/react-direction": "0.1.0-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-callback-ref": "0.1.1-rc.3", "@radix-ui/react-use-layout-effect": "0.1.1-rc.3"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9be218b509adf44f36fed106a385b21fc47f85d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-VUvDgOJo1e/HCqTkKE0R4Rhie+TQA8w0hx3SsKXAa5oIO40MJmuiXygjlloFWyJ0zsEjJczYENVMPEiv4EBu4A==", "signatures": [{"sig": "MEQCIB4CNxRPOr/2LvHqKFwe2cscgIE1yyP+F1CrcnoCuP3QAiAfx1idFq6V7mWAFvA2a9SjP1N+7ykuEM9D5jVRxQXOEA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245861, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDTWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqKMg/9F2izSQ6piEr8HgOsRW8bky3/IVxN8fU+QH7DKCir428DEF05\r\nVCtZuuWojUnUlOJ6zxqpgsa5uEUFls4mVrt09+/An1kvjOwHQeDVQKf7KJpg\r\nF3a0IoeXQUliYxXNwD1e5Stsb/IXwOj3jyyTlGmrmD0cjV+wllQ7i/fgxUuF\r\n67YFm45tHsl8JsieIXxr5oy8Z01hAC6CUWyc3gkFBCx83seB3GwbyC6IkQHn\r\nss5NPDiMBxzT+dwkz+4cWPjXXvunYFHGLzp7I9yV08KJA7qK/+OYvM9n4Sow\r\nCbS7wlEG9agPkqN1Pj8/zd6jE1TaBWjJedQOoz29oSPHAdJmKJZAbW9wu2iW\r\nBoIaxcGLSWG/XmiXGJos7MHvimxq/MABG271cow36VFRddkWhSJ3VLEp4d4W\r\nUqP6o96vbJlC5yX8YqOdlTksDm/hSzNv1JltkQ+b2ZqAiGjhlSSl74BvruIo\r\n8KQr+zDlujPnvm21tQ5ly3W8Ed0YTEYRMu0zjGcNvAB8BPtR4mUDgtljBWyg\r\noXvaGqua74ogKZ1tLEg8dU52AbfCB2EJkbJr0E2vaIcYJIaw8p80JnD4Wptp\r\nsSAn9ACQlsf6BQAIzueSNt0S47U/3edl2Qz24kdKF0hV3WNIrbgGXgpze7ch\r\nWYjRZ+Su36ZF1sVk2Kiwigr+NEGY3VXJQzI=\r\n=jFXJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-presence": "0.1.3-rc.4", "@radix-ui/react-direction": "0.1.0-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-callback-ref": "0.1.1-rc.4", "@radix-ui/react-use-layout-effect": "0.1.1-rc.4"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c038fd6a30eeeec76fc5774a3cf699606f59ddd5", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-iQCHCNlS8VKPd1R7S9G7OjHQ0cAuWMZ0aGEshPPCwSS7aQW6RWjRK+KHYCYZ+kro10hPxeOnASgG+4v9ZrZ0OQ==", "signatures": [{"sig": "MEYCIQDuivrr094FGd78plOTRnYvKsP8MWa+/rq2gZ5S+xT0AgIhANPj69YJTHgqvp7r1z+4PWyfEGHrqY14o1h5tDhLd4v4", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRr6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr5cBAAhcgWswhrCpzZ7k2w4qKgKhuj56DU8jMycTD5kIqmj1o4Sms1\r\nLROGsGsohmwOjXSNlygzuUFJ8aEy3OHaDaWwx0FPnnPUqxdw7q1hfB0B/0WV\r\n5+vARfALECmq6b9QFm9Da5+zfktNzpt7cFpEqvfjlTe1/l9LgTxscMCWl0EJ\r\npmoU1nwJ076TpRtOU4K0x7rD65/QYNORv3BHKtdy8vpj5rwu2VWL8kEfAWWi\r\nmZ+35oS780hNYa6izFTFpWMwss4wpIscz/JMX9HtKcNpuoQzFjQoL3RPLWZv\r\nGVUmBYa/g9nhqAAXbzpovffAb69bT3uSuYNQigIK1GVztu0H46cWGOByPZOt\r\nzhKcBLM8qtufCFLggIg1vRIKWtcuHw2j7HgH5ELUJLT3ghTnfOrB78wW/LRS\r\nXuQcGVFN69L56Gl3z0OGau+ZJoYVhsJufUPyAlMnFYwDc/9/EZEuB5cwMLBV\r\nU2SW9F//8Ujmf5E5l0ls0y1SAN5kqSOAjkV/mR7xXKrEVdScHEzuiRfCzIdg\r\nR/9XyTc+o/PaCU01emo1fFHUwO7kbie2P/HxtsQf6JoKaYtp3w6e1SighWSk\r\nAamq2J9iWOcepBqEmHIBBXVGWR/0EysViA/HVXhVURoSUskD08QlzABYDv6v\r\ng8FL4nl6CrXD8WfPsdYGMDWZVqq+m2rfe1g=\r\n=7fY9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-presence": "0.1.3-rc.5", "@radix-ui/react-direction": "0.1.0-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-callback-ref": "0.1.1-rc.5", "@radix-ui/react-use-layout-effect": "0.1.1-rc.5"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "688d7eefe316170b615adc4de4abc599d53f7994", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-68tv12GVW6sERchbRv4RCrDpf1+d88l5TlBxRNET6kr3lYmFI4aPhQcTqE+XvYVjOhiQwbM0115KRczTpWdLUw==", "signatures": [{"sig": "MEQCIGjBVdkMzL2dbzSV070xhJhkPp8+BdXSCjD3bz6lgs5wAiAIJdOhxHPBD1e2wcs044DAN787ICj4HKXKYJIwvkIfKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrlQQ/8D83K2avUTj9wSQ1ilBnqETHb9NSeYgvwQVbiadNaGHYepkLW\r\niJhIrWIKDAONUyyrSj/1CY5S5TE3jGnCmNOoYD6RK98AeEL+5gGGpuCcTzr5\r\nMGtcfzEVy65WWOacI+S3IxReufgYJFC+28L5emar3nHCDT6KTW9Pr3qlEr3N\r\n43k7vXrcUte6Jtt3CI4KRYFntuaxYPmj04f+yEaSoSQJGlq/XMbFMGhBwdZU\r\n57jRbWbMcURLNt3Beq1KyDCGfPP1vEO5oIJzgtpKGJHNwdJMPcWnsfoK/AVF\r\nYleNuI6E2tDRYVOhUVLUnM0ZVkpm/HGaljcTMxbwICHqMO9WpR/MKhQfiMd9\r\nUwtRL3lE0U7gC7jk1dhbfViTVwwyZX5kKtuGZtsVEMR+2bcTKzqEggKHRBSZ\r\nlN06+SLm/176m0PbKU2+bhHeQmdUdSi6P4u8nrjLLgxxeOQlwazYYYIy3yOH\r\nWjaD3V8DJbxs6RsN2NxCjs8rktzDFkGQdb/oZAnq9GiaJhcQBGgXNMXM6gvz\r\nO0LJZVA7bEEzEay7tXf8i7m7ZyZYyyYunXL/DRX6VTHjVyXJiG/adprzlYJU\r\nAAWy1Pw64FRkUpIRp5kn2PrLGFGdJjwXF7JirjKK/FVMpHuqoCfhQaCmhTVL\r\nk7WWou6XtSFL97/BEJWFXWkp5WB+BAIUO18=\r\n=0yaM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-presence": "0.1.3-rc.6", "@radix-ui/react-direction": "0.1.0-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-callback-ref": "0.1.1-rc.6", "@radix-ui/react-use-layout-effect": "0.1.1-rc.6"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b31bf8af3bdfe48ef214919dc45a23dc419ae4ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-zqgmWpmecH6iqAZALfAwayWIy0T7X9n6RWqoxlKOta7l4T33O8fr7jBJLoShc+gXIosPeopDI0xrVCCBRjf6jw==", "signatures": [{"sig": "MEUCIQDPaNgXQ8wRExb5Kae8fTGn0FTYFUko3Z/+wZjAzf6K4AIgT1fig6W1+PZjo2PNSauI93q2wdZ2bblsxmirZs/iWZ8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245905, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8yGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoFNg/+LVdDQiaExKhwTdIVeo6WWrlMFlztRUMYdP1BttkwZp2WZv9U\r\nnyumB50+KQYEFlE5w5XWxCU7UsXJKOfqdgh1lHXnkFtHKy9ZZvusiR2cBzi4\r\ngqaWaCcbFZFHmPAOQ/jCerm8T8ARbH6IdIo+uiyJulqwUtYdeRUApX8D3D7M\r\nBUQCglK+gXwn6ZdwPjGqT+2TVyrKPqu61LWVDRcQrbSyUzszWy7UV/x2oG3q\r\nvnYj6cRXsoyV1JKqa1mIbBOX0rlDrlDV+DD8rpqkkNgyKXarG6Ml/Ro3HlAj\r\nMkyTGRoDQay8kigjjcC6fri8We5hHEfnpDwcZzUmPhQbJgJ3CeA4oIXqa6qd\r\nli/EHXVQi4h18JzYa2ROSZKIz5Y/RIGtKO6kQzZLlb1z1kGLg0o1SAic0KfX\r\nka9TavrwaSTpnm4XK9EC3elRD2VlJTJQJUv9KHZbLplubSdIQPXxmAuR/nn8\r\nLQL2AfikRK0crlmkN3Xy78tLY0oEzXDH19RkYFp4V/4XqIQYHz5HJK/yE6WR\r\nIelEouxu08l2Jz9X3QmUcMhy4lLVzVqPQPZuGn1Y2DldO/EfMH4uE12VTImj\r\np3RPaHUhQymZRAWmN0UhrHsVSYnedyV7l5PKPNBqYMWasw7XreL+B/LVHXEZ\r\nj2lmkKaqubz8+x83osrsbjsT3AzY9XoxglU=\r\n=7p0q\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-presence": "0.1.3-rc.7", "@radix-ui/react-direction": "0.1.0-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-callback-ref": "0.1.1-rc.7", "@radix-ui/react-use-layout-effect": "0.1.1-rc.7"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f20260fbb58d9198d6b879065dd21ec20bf95684", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.10.tgz", "fileCount": 8, "integrity": "sha512-rOOYw7FPFYyUTuB+pGKymql6BmzofAQCFKfvLnVIdURjRyHpVAR6VUZwO/sclIHkEERZiybMD711JoWrWkECoQ==", "signatures": [{"sig": "MEYCIQCeHDFTGiJpwm183Gg2N9ctJpWpEJmAnjc3/V4vOomO8gIhAJ9/3rMqCcS5hUjQ6m4s7HZ/BnuRbBFxk6r7+YGcrbSF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmol6hAAhr9HtmGLq23tP5ZlojV/ZGQgLQnaswJUnC3GnWPs1jrQfrH0\r\nVn7Pq2WQyTWjNdwsv4v9fF4yBP2C9iSXiI9NfRGvu0DNS02mJ/vNbUCFA4yj\r\nAQvR7ltBsHF8XE44d0pvig3EZN1Ghvq3LWk+pmRxTSRy26gSrvpNX7UGuTMZ\r\nhTrVE9h8GKRi2srHigkEXFxDlCp0h5TtTrz5oOy2kQClmhuce6F+NCvEhH4b\r\ndfKX74ZEINg0sR/pBYQeVnbe2+uyv3uVcCDETpfRgZIa1YxXU0oi4kk8ICYC\r\nJ68/nXuCjdT7GZnoY4AE8NTEgPxJYpS2TzmjhOQcF7/B4HDUur/z+YHL3IeG\r\njBQ7X1loQZ8UEHs9fh5UsaW3X5h2h7fMDXGsq0UUeZZFjrJmXFsWCJc4C3b2\r\n0cbZLc8Tp47/dHvIkkOIOXI3OGbIP0n93P2zQX7d1D1Fx861FjqxafNOv05O\r\nLFbTyqyI2GXPLdeC6TfikqLyS0/Xk0jxAJbdO/x3NbZZNj86W3PRsG4Lx0Pa\r\nL4PjFgViYQSKoNo8wr51/4EGtbdFwcN14e/i1WZamRxNApRL+lcooBZ7lyMc\r\nlR3Kmjqc25GpCzusJDr6PDyxHgyv4y1UIPDQC1q6d+hWonodyIi6f3Pba7os\r\nmSPey20Me5My5Lai2uAtCx6SY4sN5HH1D/Y=\r\n=lIqt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.11": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-presence": "0.1.3-rc.8", "@radix-ui/react-direction": "0.1.0-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-callback-ref": "0.1.1-rc.8", "@radix-ui/react-use-layout-effect": "0.1.1-rc.8"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "005d25a340b1219b787bc7439eede21a0df95a41", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.11.tgz", "fileCount": 8, "integrity": "sha512-ftP/lIcfAl7BheihNyBhtbDyumgQ73MpRSvU74vv7U0eR+3jlfPGXeWSbYAnQ+7gv/CRyemL5S7/xsGsQK0IHw==", "signatures": [{"sig": "MEYCIQCMdyi0QdKjXrK/vLp7qTf1aFaa6vH84rAmINYclwnysAIhAP3A+QVxUWX/idbz6v+8OvEyvZjIlxzl2KlfQFm48+FA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicViVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpC8Q/9HolQ8iU+qW5UM6R3euN4sASRYgSFLRteQMfs41V6cjjYIkW3\r\nb0AhfYXpAhWgh4ZoQNHR/daf5PYM36mlVnPJSaaMgN3X/NBB6DNkFkKjdBJr\r\nm5ebef0qWhvQZ9A8Oh6B/bycJ2WD1++BNdyll5Hvji3XI2i/uMGO7s+2ALAv\r\ntBvyrA6RmwpzcTX2QOMYumvI9h5zMXbVyjIO38fQt6VBUjGFktFh9Hg5lNlK\r\niOEWM/a8T7iD4k5abjiMgPzeMuuqzxlqVr6rGkoKFtec8web0KkIF004SasV\r\nIne+SB8ySbTH1jcpoDDs6MKQ2Q2b3dXGS0W9qwaaGJMhrRRuPXJa8AR5wYqs\r\nVX8AySTj5NV8xD4OnhoODEgVqTDWBBK3LukARPcxHLNyjlK3lhak4vNig0Oi\r\n5e76XCLpYaAuNrcqi1NyEPoWz35QiLJMyZwtfXDy3SbeLLompaCOb/jbOdr/\r\nloRq0WABZ87EztJiiHIBRVB/DE9avK381cB1A1dQhFKnXrlhBhG7Q23CvNu2\r\nnhgDOLW1z2Wdw8EBkeJAexoimmVbQL1t41kU5Tg+tOezCxwNFpA0OWm1fhK6\r\nkPIZ92VmPuqRu9yD91NdgxUTZuMSiC/ucGPMYWv52QAeY+eGz/NI4Oc3un3T\r\n7ETAW1k3Y+CgJNvpCC531Wb2wdUEGGO1xH8=\r\n=Q0U/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.12": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-presence": "0.1.3-rc.9", "@radix-ui/react-direction": "0.1.0-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-callback-ref": "0.1.1-rc.9", "@radix-ui/react-use-layout-effect": "0.1.1-rc.9"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "55692ee5a6e2e1a7288a00a58bbe55924256b08e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.12.tgz", "fileCount": 8, "integrity": "sha512-fhNBox2Fddsj0dUhhfPWFeovHQeo6lmnRd78LQ/7ip1GZnVi8MKixte6Pcu1+xcvZ4ilHuo8nJebbQ+7NlLOQg==", "signatures": [{"sig": "MEYCIQCqt2n6QF7m8yaCJpww7ng3AV68CTjJTUXXSdjqBii+XgIhAKJBVsjVbATC4bnUjFPknFjY/iwu5tR0hHL+c33YhVQo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNiEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpf7w//aGSN2voDu++CVrsgmixsH/BYB2kfHIMTfNzBt/xqDW7s1bfl\r\nHytNsAe2RGWTOVdoB/hMWK4AjdYk7DW+rBJGk5X9j2msAUjPrZ1anOyKle/r\r\nH5J5Qrf8ThSZw0eUA/Xl07K30p81bxaDFDYBwvtxK1BrOoBZPMXqyXyFWPxt\r\nt8f7yPlv/WIJ9WHM9gSMqVnbEiH+CZkqIeF1AdpfWX+bnSJIKpBo40nYWsE+\r\nChCW59ZgGrGiPthssCPqDppBpiT2AqA1nW34c2Fp1mWjPRU4/lwoBJ7zq4nB\r\nnjYoGi9P4ns9K3OxBKliAid5Vo/0wk8g7soU33WUD84OefNOo6NKBZTVnFAN\r\nvnDvC/bCEXGZ+1ltfgShA0pxSJYXzgS6HaVi8CDk6nT6TALkCQKn9x1yrNqr\r\nAcXitkCMsiLadOapr93jLm9PJ6Zk4uOBje3KmOILhkFw/6jlzRC0dKK/j7So\r\nGRK43RZTFqimdyyl1ch0wWQTHtPIMwU41yQzsEvQ2VqSQr1l4JAUjao+OgS0\r\nlGqCyHowHKWRJ0Pkmz2vLUpZVBS6cAgNIVz/07u6SEfrqswNIdYlk03SL6Tr\r\niexhoIzFOaT6oTCrerSz+39h+CNk4wY52C2Ii6hD4E4XZr9q0//Dg9+nhcXV\r\n7zYxeSaw1RK8yzeA7QRfruWUAo+BB7283bc=\r\n=DfS2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.13": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-presence": "0.1.3-rc.10", "@radix-ui/react-direction": "0.1.0-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-callback-ref": "0.1.1-rc.10", "@radix-ui/react-use-layout-effect": "0.1.1-rc.10"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "af0edb16979d4a607b92ee11b1c6357c976ccaac", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.13.tgz", "fileCount": 8, "integrity": "sha512-kFMLL2OwiBDGmtf5bUB+bkvbDGQBsilJTCpBzjmonEkNnor6Jj8EX/IOp+nGXwRGTJaUv4JdHW/5JAGbXS2DVg==", "signatures": [{"sig": "MEQCIAUTh9UOFgiN1jKJXFVGbzgiyOcA7ur6iZ6ehYO/GeSXAiA9q2T6Lws8DHS1G1KF7epVB/6TN8+sSqMembNuG6Opcw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN+xACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokrg//XefOId3LmpLXn/PM4v71jFQHDLtTfNH65kJPdFxtMXlOPl5F\r\nCXYJQ/TcoEcfh1B5ARWouiBwUThTG5BqbQfM/YrSH8lcw6z4W/7P8FUKIBcy\r\nRzcYlTlUygyrkHSOYt7/QwJMdseRYcToVdSfNEFX13hCFglroZ8AggQzP09c\r\ndr79U5cec6t01RDV/kTfVqqihGLrpIPITxlJdMupJvBu1HQrQOC2wlpOdfDq\r\nLfkiEwDnIVKDtzpMMtGHQtMUIk0ivlnvR88VwMJc1QEsRn8zbsbWEk61DvVz\r\nVTWSJ4nqBgRm6T0s0h+vMPi+oS/TfJ9mODae6ZRx6Z9XGbmOyFGzvtwvcR5Z\r\ncF5zyipYzRoMgJjWEuT5OIdtfpDeLq/dm8hMjr/qnswJrfN0+cectQfvI27N\r\n6+HFTVLnFE8Sflc1kELx2HxwtGRWLfGxjxBRLOBVdc7Bpak7E9ybb3asBq9P\r\nt+k0LGMgHNk+GDsc805xrEUD1RvRVtSEbl8C9FhA2V9yAFyCX33QVEz3T7oi\r\n88pw/wN/TrYeDRKHf8PrKUIhfLRuaa6rqk6/vVgq5UAxpqeMTuHaqPWszbM6\r\npb/svYwoNTpzeauO6N66ZWZ9NNOWcI/NTHXVv74WAxV79T4pGBbOOM+70G/P\r\nMFgxbkNGeyEMwAbqoYZAxDV+NgK9/sq9UMk=\r\n=Cs0/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.14": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-presence": "0.1.3-rc.11", "@radix-ui/react-direction": "0.1.0-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-callback-ref": "0.1.1-rc.11", "@radix-ui/react-use-layout-effect": "0.1.1-rc.11"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "349c85ecdf394cbe50ca7d87c2d5636e19f50a8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.14.tgz", "fileCount": 8, "integrity": "sha512-2glisk9vfJpHewSlBrLtt1zKwHC2gobPdll0VbzxpTdDcw8aMFoKJmyp32PTBOrdAoHpFmCXt5u9DssLXGg5Kg==", "signatures": [{"sig": "MEUCIA1NromRqkjiUKhi0rXuR33QSgiixYy+V731/wAdwr15AiEAm/TivAZ/MbUaK6qNN0sE94MrrzZJK3JSSh4rsGLjHj0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSljACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPpQ/+NehVrOW/GCUW48s8+k4/44aMWb4sdmpb8myEzR6rB/cy+VoK\r\nsg0vMD1DjL93DB39Jv53jb4MRKGCNqBWQZAS/bZG+pRHhlyGC2e3zZB/Ysz9\r\nMZhe0WHkxWCrXlg7BFMveZH61rLfGUqxuBvukcUivXef2hsl6M1/mc2OqP9w\r\nKgQ0qcMJKRK8YwScgL45cdbmbBX+GPziYELL5QqhFIGvOD0TfeLMT+QScAWH\r\n0IVbTJBuQQ/AP7rIudX7bKslFI5Sf7iwyrBBQ+Oar3ruT4iygCj8OAlqkvFa\r\njuWitwAyKYpy6kk/WhG1LyoZwUYVBWpwDE8+i4rZxYprU8nWt8S0ULkcj9yP\r\nHH0KYHsv92Vv1v7XdXhGoE3ohTjVzIEAEuGQBAfTDiOYBOUGZ79TBNXbftie\r\nHUR/hd/TFWojD67DifiiPfzhFeZ+863Jf3Ml8EI1Ev/FiczSlwcIsjWM+JwU\r\nG275bMBI3luap05amNvCgXxsbTYf91NVDa1IzGFRERkSS+mzeCgpZz2Kbtl/\r\nJZ1BL6ZnUHZppSTHudwBXlsqFZ/G0t6yJl5MlfCJpcHqsRBvnPpfUvRvRkWj\r\nYOt6F4g7tT8sdlV1VIz/gkn6TLmqqMVuyjbLiLXYoPx/v7tlu1SSdrBa9SKt\r\ny6SZK3tbrsuKWkhfcClcBl2a/IdVOHoTBSs=\r\n=Lfr8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.15": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-presence": "0.1.3-rc.12", "@radix-ui/react-direction": "0.1.0-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-callback-ref": "0.1.1-rc.12", "@radix-ui/react-use-layout-effect": "0.1.1-rc.12"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "111fd9b669c7c43b3fc1321875d77514b922f9c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.15.tgz", "fileCount": 8, "integrity": "sha512-dQ8kTqIunmQ9Q6g1vl1bzVafeVbImd2hw0eSetXY2QusPaQnNpLdi4gnhmjRccbkWGsqiwIOK5eqkOzfdW5lMQ==", "signatures": [{"sig": "MEYCIQCyFgFYBnIq8dBusOSsst+G6Pmj3F9rm2OTBbmyfkFvsAIhAMnaAmzTEIPQK8iPcxNuPKYTwNugwKEa7xF4ifjs+YEh", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieogRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqrew//T+Y2gmsKFlMa0Aa5T/wgxjlH/EZ2MF+c7kI5ntmyCGr4pS5U\r\nLyiVh2HXQ6FBgSSHt+JdIzvOE0gpwDpXNVwky9A5VXFLhm13rtB2w1PzCtNm\r\n7fvraHjwjTVu3xW/fwrTjPBfyC+QdYYibiMjhKYsLeURbiIRtSJlsvuxPsdm\r\nfQqr5WanpCe3vUn9Ty9Ak2L85RPMXY+RQMQxdUNOvyHckrabpzQRVlPPVoRG\r\nEZqlAc2fpiSu1Z+F6AvEd1bO6sUGKfs/S4gsiE4c8k7Z3QZ+m4miMhIOoK/p\r\ncw7hhAxDy16iPAKXQoSCBbCfwUlh8gek203oVRnsyqmp/6NIwh1HCKa0y8nj\r\n5bxyjl0IuQfDWuYSYOrSUuIqS84ZFWvcEzlmH1ELIPOMC7dxay7b6XL+UJ42\r\no+mmuOn+5FLoaEXnCDX/GlLNTZgRk4wEv7hQ5+Hw9tkR9RVIwhwpkje2msnN\r\nsCJPmVc2AcprGvbAjDA4Z3j10zaKfu+ESnyOJhGf3nOtiAOxfeA5+19xGnvE\r\nHdW9jyc3i+qqkFDjHgcVIvRLGKLnSQCCTD2BNh10XKHgbbJjByOHfdHkJbIg\r\nl1iFQpHbUpPc2Qk6twmfUTiXwP2fWkrzN2NCAdZslANwTIWoRNDQSiZGGJOy\r\nKM6BfCrXHIsVpD+vn9NgHaCN6NlyKLbV4bk=\r\n=edjn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.16": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-presence": "0.1.3-rc.13", "@radix-ui/react-direction": "0.1.0-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-callback-ref": "0.1.1-rc.13", "@radix-ui/react-use-layout-effect": "0.1.1-rc.13"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a83c07ca0ff3b52eb69e8b47745cd80e15f9cd7f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.16.tgz", "fileCount": 8, "integrity": "sha512-+Vxh9etrBUhLudVPaxsCVWmoAK0El/r49EJu8711Jy3wr+8s5eg66OwGvObvMp7sq/0zneedRi9xNOhBnSD33Q==", "signatures": [{"sig": "MEQCIDvmEh+Ut/fy50+EW1wLb0gJE6n6f6JJDecZr29qcBzvAiBuy0eqtA/ZKTc0L+zwflX4Ixg0UlBPNGgtF++2Cp5dzw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepJmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpIdQ//XxGKX6xzM7AUJAXBWwTDRGlT/xGqNcdzJbxZU0FXovraGwOh\r\nHigTKRmuIRbOMYfcfKKatT8uZqbL93gdZzf4wxopsm5TJP4QKzKlmkgGtS1I\r\ndOXgQalq6ogGkI7F4eGsFxJGAQGYGN2wgUm3N1oXIxG8uxzbSfY3sBAY9zJk\r\nMI6ro8v2ux/Wa5jZTft7MBa4ua/jRTBkx1v1hh3O+OnoSKQQD1k9ocyP7cbN\r\nBVPkk7BkvfP3yRd/SVQbHHWV0M5pRKfQuKIE62QQX6J2MF16gw/ktkDyP8KO\r\nkTCzaGKWuqAVxxnVQIwnkSPH0ToJZv+JYfnskrq1xuun0gnUnhY0IYC0aYgO\r\nGSHX2f3RKSp0JGjJYPE56hzEtboGXz768EWFQfNpDRU7MzsjYWUJYBHqPa3n\r\n3wiZCxgU8cpoLx3Jy95wMdOzWWtVMm5Pq6mdnHoUatq8nMcyHcNDIUTNurd4\r\nyJcFFYVJP8ma6soU5ksUqmN2LNi4a3yvo7VkqYZ+S4HJRsQH6NOFqNgvO/TA\r\nRNlD4WsdAqwRlbMnoBD9HpFwGJ2dE6m+H33u2F54gBfffagN4Qa5OLUUntYw\r\nsTVmKxDw6Nqb6ST1/qS8+ahPj7O7y2F/n03SnqtvhZth2mc32klwGDDbt1nE\r\nyd9D1I2hwZUSFbpMM3EFH8Hla5su8jdh4qg=\r\n=oaEV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.17": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-presence": "0.1.3-rc.14", "@radix-ui/react-direction": "0.1.0-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-callback-ref": "0.1.1-rc.14", "@radix-ui/react-use-layout-effect": "0.1.1-rc.14"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b4efa16e9fe88db4dd3a5f418f6e3c8fc432db8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.17.tgz", "fileCount": 8, "integrity": "sha512-bgF6toxJvXrgKuRkLif+i5IwOyG5oyPcDqdOswgK5V8FKKF9XoqXUk47uN3BkTabEOTxltSzd7mF6EqIEAfPgg==", "signatures": [{"sig": "MEQCIHxiNXYEx4bqDy19pIeOsaNHar2vLYP1QE3FlEJMZrnAAiB4f3kBWOMtUmo4aLYwKrrSWQFIyHO+G3ZMceeR9+pZPg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8p3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpjKA/+NTeOcGSWBDVmXMd6HIVfSOMeU0NWcBIzC7XNc3Qpfxn86eiN\r\n+Gtez/9BxsODRIt/s5aKDne00MxObDVbijElX4OcqyDROtlYC8GxXD9QleUB\r\nxkrf6G8/br3/u5XeeoDht6HA9i0PdkAAHQXEHv619gbyeidsABVMNj7AF+Tb\r\nZxX8zn4tOleaC8xXIzaJDpltkWfsGTEbKYQIULQ4AAG+tQl1+Qt0jh76rmGk\r\n0paxBBNv0T4pUEUYxpPygo97d6f0l+YM+v5txUKRqi5lW7DztS/j5/+CyrJT\r\nZ5YWwRHYedqvQxv3hBQQluM12uBk6S3nvhuY/F7FCPurDjO5xp7aQQV3dYwu\r\nKR77qmCU9LKf0Xr/3d7G4dvfBFK/QN4i1dfiFrH2UDEdU+GJONVBgMUhB6yT\r\nzov1RhDVIDkN3IQtha7ub3alRndyU2aRhDmGoo41wLh688fJUTutdS/xlSaq\r\n/65ADFu/sftUPE9BSf7kIuXOCMB/Q/0ITQcXhty8Vn28vpnKlYNnWGmuCXWZ\r\nXNRXdqRjj0E0BuDkWdHJq58ZXh/zGRXuWSeUE1llcvNSLPCYf3oTqctGNlbj\r\nG6WrGQgYtpI2CvlUxdabVErGwR3x247krM4ZBsf397xojV7Ozpg77GwcQgDK\r\nMCGBLP/N/9mrhzvG2ERrYWLIFQUds4ruIyE=\r\n=1Nml\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.18": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-presence": "0.1.3-rc.15", "@radix-ui/react-direction": "0.1.0-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-callback-ref": "0.1.1-rc.15", "@radix-ui/react-use-layout-effect": "0.1.1-rc.15"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5945417954d11525246abae9c5248328b2255f82", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.18.tgz", "fileCount": 8, "integrity": "sha512-ze6YwlSGAAd9Gi7KRjYp9RY/klTTurl3RWphuqjE8OJSyiE6ng1U10wOEH9YvtZfLSqAWF2VjcdCJNXQ5F5DcA==", "signatures": [{"sig": "MEQCIC/cwWe46e0wQJX2nxImA9tF9KGjdvsS/ohnRDPOkzEKAiBmZ+1h70Bn4CfYEDNq/+g+VpbAzbdPm97oWuZYjVliLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifA0tACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmravQ//dSl2qJLkN6gyjxorDBikHMgujiY2tPskLNhizBRBIirdal1Z\r\nJa1sJW99FYHO4LqPLSUgpST3iWWCoE+0P+URNCZgxppTkeW4/+RLc+XFnscb\r\nDOUSuB1bK2RDPu2Zuf3vQZl928pBL53Liq6snX/VspTZ0x4zyna9Jf74ShUS\r\n0E7xqNQpkERoWhy8E6pp4C5QCMoRWc0bZ8Ep79ifqlHERnrO5ALDcsz+ehoj\r\nP5KHZ+FGfJ8/ixwZ23UMs/hjyc5SmliAchZlzyW7SL9XI2ffZjEOvtNAO6rR\r\nVIvpiQ741TtgrHQnNGbbQZW3jyektPB1TBtZ3ShALwtIkh3FDC8hSVawjUAu\r\nz5Xxl/sooK7f0IlSFTUJmZ9HzPYPwPqCKss8sZ6aZ3uj6z2utS6gDWhPqKXq\r\nC40K3GcC7RT6shGyPhbXlK+csS0dp37hizRZxu9E7RKcvaKFBSl3jQ2K44dI\r\nCMMk/OhvElB7z4MMnqZqMgbFj1YDitxe7xG5bLuLx/9529oZMaBcN/TSvjy+\r\nL/7K01VsxC9sOi71nat6u4ZBMmJhTbV1VQEStQ1qo9ouqORIVEoaIzlSVWID\r\nbv95Y5RyY3cKWr9VuIjenWpz1Snm8ayyjtYOEiBp4Z8dgN/vrTmMcC1rbzW0\r\nQ82KEwMgxZl0lXgWWfqwWtEjSFjDTzxx7j4=\r\n=OyVe\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.19": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-presence": "0.1.3-rc.16", "@radix-ui/react-direction": "0.1.0-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-callback-ref": "0.1.1-rc.16", "@radix-ui/react-use-layout-effect": "0.1.1-rc.16"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2b18c4700e04fa069b7691193225dec62437dfda", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.19.tgz", "fileCount": 8, "integrity": "sha512-p4wY6pbBp67b3VZEhXKEeY81o1/R6uQldkbQQaFBMvZaZ1xknFvDQmolo9cGa0jGMGrKip4dzGPsbPKMLcn9XA==", "signatures": [{"sig": "MEUCIQDzakD00K/ZxSpXpZ/64k3xdCjHNaDKxg7tE3LV+y0V9AIgSYfLqSZnpvb4trxzZUCZa0/0NGlfG4JtJvwBRwSso48=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 245913, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTsOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq67w/+KjXnkMl/j2qy3Sy9baBa6JmLcpzJSOEIpJX3iRosahcWBY/7\r\n8kiIVgBCRhdALY02QEgZAh0KTlF9mqmUN45VxjL8urtoDhdZ94G7tiNsnV4G\r\nyFjhZrMA1Q9uxrp9DdYR3ulGeNbL19+flTf92j9oY6BeRDm8ySk+sMw6w7I2\r\n4MPehtuJgsBHZKuW1lwK4iE929oQQg6tLZ2V00LNkAmjZ1e0mumZZ09rgofB\r\nsYkaXljYciAK5D7FCS++nK2k3ZQZq9HnLuuGoNuXCtEmU8Knel8CQhwdPbUr\r\njJDHpec6zkTp5a5S4U8PvNHLCNvSChnPlEeJUbyajhhiq1nhYXxB/tRtlxRx\r\n0cJ4pd7hgJ8/uXRJe/x7XOYkImegxocZWE6KgTXjTcjChyQYuOY6JMk/hcP1\r\neNV8LkG/nQP2Hfw+yUuUEDH7fl8TQqaAmo4oG1D/oXg335Ab9pFdNOxIwclW\r\n3a4bMfnidFNX52RzjAhZaCzArswW7CUALtUKRsQmoWM0WTZKCpVn2fnPhX8K\r\nZtFIquyXTcYG0Eb3VbFqbMRUy63+G3ysmZCoP7/oiCrNdcclLeJD7CyDwpBd\r\ntktKN3/jtxGDvYkFurqy3iyAPw7VJDaT6QITHjLda1MJli9ZGrFQR6jJamG8\r\nzvzJcHBmqazdDoBgbhUE/K2aEvDcwihY+P4=\r\n=rrdg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.20": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-presence": "0.1.3-rc.17", "@radix-ui/react-direction": "0.1.0-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-callback-ref": "0.1.1-rc.17", "@radix-ui/react-use-layout-effect": "0.1.1-rc.17"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dcf77b2ca4fa5327fb73aeb72ef0d50506ae832d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.20.tgz", "fileCount": 8, "integrity": "sha512-DFkBqYBMqSF0GyHNsIgyEfyIOIU+JCLbMJMIeKP27aPPMkQJnlrC+WX7u2Ms3FNW/3SHRAhIsH94i9zzRwUR5w==", "signatures": [{"sig": "MEUCIEr4tgfss6lkXmh6HzuqW2oZBSkR7KEi6AwKlIytvX8NAiEApb/UqCYMlSmzKgaCY3VEKspR0OW/y3PcwlfMm8qY/1w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 247487, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo9hw/+J3DmYCFk0jScvQvpTkyAbrFWbQ6LNKmuduH0eJLWOeG70Vn9\r\nAvOG1KH3Ml6Mqgz58shARrhzh1u5eK+pa9aKnq4XS3bnQbGUXtr4wWOAgF3j\r\nBxdOJR8dGnCP8Opoju8an3oiGJELv1Myy1ZQs38VpDeLXSAhUhs33bDYwlfQ\r\n5P+I4kIGPVcj25CJOTtGnMI9XY0FJlNqwy+II9cAGf/eXsy2CQV+90q8lgv+\r\nbFL6J2eEd4RmisJIzG1yfGPFYXpjuwkuWz0PNSzgTyWQwsg3jnynsuD+RjeU\r\nKpo5VIPjrOPVvFPHpBfAzAnrJQRSkKWKPa3Bdd4r1FGmMU1PUKOZ770F1Fhe\r\nx05EMOy4UdxaAnQ7RcU9j0nBj4JD5UGDzVko8CuoXZheHn7P6rdDBaS/aM2W\r\nwMGn8JDBgIMqXQUVZtIF5Sa/RZswEjLa8CLHwxWQxiwKdNyJW8cUTg86h50Q\r\nyEaWnuOl6AmXdrfSO30Tb0J7esTh9EFgyIMSfScfUSz+QlMyVNKdZaaZetGI\r\nxBWZFqejhJ5IT7wwoXpiorlLttACf55KE+6nmRPaVQkknKeXWAmzSE+GwsqY\r\ntZn+zn1Kp/40zz0bkr4rL/IAcPR1W8qYuyIJqNEEcb8tKrD/ZRiDSdKFO1HR\r\nmpfUzzufSASV4fj4fGBo1Vj32Avn7Y30tOg=\r\n=napo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.21": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-presence": "0.1.3-rc.18", "@radix-ui/react-direction": "0.1.0-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-callback-ref": "0.1.1-rc.18", "@radix-ui/react-use-layout-effect": "0.1.1-rc.18"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "803b1725643301e024c9b967fd20249ec66ae4b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.21.tgz", "fileCount": 8, "integrity": "sha512-y62fnG6ZFfeL0Quvfla/J5BXe0gJFjjL3BqhDfFuXsW6C68UK1xoybsaTW9xnn/rAekm9PXvEv4RCn1d/nkKfw==", "signatures": [{"sig": "MEUCIBvELMzSmG807W85GfFAfWIb5Q7N5C4ncYK+8blpRJ3hAiEAhP1Hix2xfAElrKIAT72uNFZbqsVyWE2+Zui6VkxyyoE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQ0gACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpN6RAAmyFNY2dgZFeiEQhQK19nwG7MNUcfDpgcej+TAmBB5vpTRZyC\r\ng+1UA+U/vT6Bo8fTXAyoTj3xpjVl01Cw3JjrQzZuHsa+e5AaCmVSQB2f/sSR\r\ny6p8PrGZEm4MkBfkUEUZLzz2ioXTuK6fgX0moCEFPGvwJ6XqX2FHDKF7lVel\r\ntHx4KLw2jgWXsB1EiuykIsZ3v9nv0XdV7qGCYjozYeHQk+mcCuIo41OHsZsd\r\nU10a4FOQNYwedN5o1GkA72DC2uFdd0/oniYmBXLFtIj+AgUCyGtdM/muyLWp\r\n6pvM0rqgDieSDyHekJjHHdXMxuEXpsXkqTlZN6T5nQuKXHBNMRLxlXpPhL8z\r\nxMTeNvh2RdGlzVojMrBNYOnP0uVOYtdoPtUXeYcDQ7cbR8fLHMMrM3E7B6T9\r\n24u+mwthGAvk8LuavJbGINpSvpzqzZ2JCh0rX8fsvCIs01Ld6LG8hnBJXXeR\r\nA2pFex1yYgpPhDibCO5rh5YjaNiKra+VR8S7K9+9YTg7951ToMxwJmdXYFs6\r\n7OLd3wR3dImKH9YIXbZIV73wywETrhFo4ZsokPk9ttVjqZ1Flhbvnejh0jcE\r\ng72+BZJBf0JaawCc+H7vOi0d92qSb5gNRNd12f3VHwFRwMQTd2LkdXFSVyYg\r\nLF9qmoNqrUY/T7CWPsGdbeprXxerTXjRC4o=\r\n=8TZ0\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.22": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-presence": "0.1.3-rc.19", "@radix-ui/react-direction": "0.1.0-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-callback-ref": "0.1.1-rc.19", "@radix-ui/react-use-layout-effect": "0.1.1-rc.19"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "519d7450754ac9e628f6ff3d43e0a8a193330321", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.22.tgz", "fileCount": 8, "integrity": "sha512-O6veaBMYLHp9oKu0sZro85MhhoA514nLo6W4sMQzxTnhTXfCzmEn/S60ZUOdmQxpAN6cvA0YnCCg0KcLY5xIeA==", "signatures": [{"sig": "MEYCIQDdbGmiQVdfSdNbmJhT8pKx4bDKR26jLUUze0Em20R16AIhAJbIP3VN9FfBdg80VW+RFnyCI4wy4OtHwVL0B/LEiu0f", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2W4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbmA//XcdJBRgOTiWAC7wyFsN+wE21mpfsDjO3XkEC61JP9To1D4fn\r\neeMTITm1auMVVFNotfS+fu/l+5wJ82F3LCpeGgr9enKUS/pgqxT+bi4t8own\r\n9JTokiZJ+oL0Zc/a12F/w6ECcrGmY8iydh9A8d8A9JR061yaFVE+Ovtwkp2O\r\nXaDamYujD2BvTAYsiYXtppraF88Hjp8eSCiJdloAF4be0cx6NhYO2xl3oQt2\r\n0E1vAnPEdSwC+753/SZ79T8tgDPHeEx9EVxY5uYLcSKfcIZ2xs8GYwvltXxm\r\ngspmS5uJ+kjZeQuvB4rYttZ/RL1RPXKgcLZB8hrTjm4jaKrk5Pi+ETYsrOS9\r\n+w7p3fZzQ1vucD7myR58OCmv95D5f/CX+lhKw68ynXpu+n3gUixagt14/Z7B\r\nYSR9QxyuI4Kba7ZEPofPrUbfzEimPZ2Km4iMNxohsDe2OU5lPQCfFubAybPT\r\n5fRPD0YvdBXIhl8vhz9rWOo13flcGS1PZT7pHuFWxLSwnqx/I1hFw27bX+6+\r\nJdQP+kUDWBO+zOA7hH97QCMhaArqiHfBcsNU9MigrD7UQyEUK6r/D9Zepq4F\r\nL2X/jId+YH3wFwRmkbStKkSHgKTDjx2ySEX+uVLXkvN4LvafMEVCH8UAdcwz\r\nb6/YD96U3+lEqudgCBbWmXfNOnAS0WnLNcA=\r\n=9b+/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.23": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-presence": "0.1.3-rc.20", "@radix-ui/react-direction": "0.1.0-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-callback-ref": "0.1.1-rc.20", "@radix-ui/react-use-layout-effect": "0.1.1-rc.20"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ddae93628823420dafa9f24be5121fa51ada7e70", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.23.tgz", "fileCount": 8, "integrity": "sha512-8QuMbsVrGLoDpvfDNNubaoUuCmAOHjeqSfqMB090hwj/MCfWT0MNI5W92LGqxwtZQf2DCkpDT7UnmeobP6LIOg==", "signatures": [{"sig": "MEYCIQC0y1IWEt7Zgs0G3SSNrB/bkbs2N9k9PZB0isJv1sLzvwIhALYrMfE+nf4mBamBNJ59qVWhsi14tMu2jWT6zLrxpYx0", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpNMQ/+N000c3qrQUcPuaj2JF6fjFq8WjJPtYYcuz13p1DgMq7JTsXu\r\nWg1cNBS7pFDVFKfUm+pGQWuutKujyfrR2Bi7i8WGHqCEcKREiz51BltXM6M5\r\ncwyVfCxwWV0em1lnP4FHTz5mhS48kJyYQMxQrFUfiGrfyDYOzkN4u5cBpTMV\r\nkkHHpFZLWUl1X5KR7088VRNyYCG9xP6Crmree9Uf6yyHolA4D0G+0LecU0lK\r\nBefGqnQDZdcUI3i8zMBvL75P8vxzf0ltHQ1jCFyw76Sttrqo51m2cMzqbMhl\r\ncMtYNSa/fIgV5uVFtFEdMwWetbCuwBPTQeAOiZdO7zSWpYcS7SmXcU3APVvD\r\n8jN4qOuqm9gEapx3uwmQmm0BidaXeWuQB5G5m0uvUgUn65gvfM8nYmQQaOjA\r\nNzQkdGFQZoizrrBa4Zk/NeRRhooneZMIxyfzdRAGQEHRRDHyzVF/cPoL/vgO\r\nEM9KIR6VhZhfcsBulsW3kDq4Cu3HkIgULpXBh3x0xTa3p5i7x0UfyPIYGhGV\r\nKbnzS0tUntIC/FI6xkOrlC9SG2pb2V+Irbwa6LmnXWP3uX8ONlOPUDsyhfMX\r\ntETqT+tqYhV8f9au02vGvAY9ghhaIT4wBp0NcnHkmb8/sd4/uhQ/OsfYTjPj\r\nqCPIecuvJpXZfYV6b/2C985EBf0j8yaW0l8=\r\n=QAXl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.24": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-presence": "0.1.3-rc.21", "@radix-ui/react-direction": "0.1.0-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-callback-ref": "0.1.1-rc.21", "@radix-ui/react-use-layout-effect": "0.1.1-rc.21"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dcfa3c834298c9c0aed3370c8e3c0b257620cf59", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.24.tgz", "fileCount": 8, "integrity": "sha512-kz9vl+YLmAwXbBoJg+c0bDBXpiNtY4nJHGuKnwg0Gmhl2QpuTNLktIM84Gmj1F89iQPRo1ezf4JcWp5OTqUVUg==", "signatures": [{"sig": "MEUCIQCzyV0zcpb/TQISVGvNU7wDrR0QKewYu+y3jkv2K1hz0QIgCdzuuYS7KSpLppHw0XjowZvv8DnRC5nLKKCUbt5gqC8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih5+MACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeSw//YXNOSSQsDxvwfJ/NzybFGtkBdFBLcvM/3EVMs9u2Mad7WEmV\r\n9skV6O/+E5JnlWK5K980ceiEVM4sAc34Q5/btdinVcWoVNRDMCH4igDU3efd\r\n09eAfKm0pkIZ9qMQvu0DVBmSwe2dd4PxngkwgfxW9E0SWEMViBDj6Et/ONDI\r\nX/rd9zU3C4RfEz8/Oq3LXOkkQXFzBTmI7ca8UX4VpbpudEqdndM6S73dfqBt\r\nJVrx2CqnYCLal2rq7FAvus17sXSlwd4eSh3QnTr5I5FiVdQAWHTiDPAUdKmO\r\n+/Rwfgc3lhNSx+RxrpOaZxGCrgWOk88DbsilRiGElEzONhjUYEpDWEKYgFUc\r\nwp5AaZaWkQ43+jhpqGF2iR7E8TRa7SSmsx9mdlMpuB3BsfQhSUW5648X1WQ1\r\nQB+3GBuJBQ9ltNZkfO/nRo2yw5BKPmpJgkoMKwtk3WBXaqlBbDNqf2+AIfiZ\r\nBgCBeO+m74HwjsUx/EHwM7LR8ZDASZkyGlxFynT30w43DpYzqyDW3UsAjbyn\r\nnGY0xBJ1l0Lw6KipT7Hjd94gczYOqyWiAwGM2/bmdh/TIYMLomaioDKf1Icr\r\n06GkJyfIUDW+UCZIaUorFOvmqK47/0+sJBhvF/Vd35YYvlv95/F+8aTVxecz\r\nXBQBQ9bCNI8m0w76llqa6inzntv3dEVeM5Y=\r\n=o4oB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.25": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-presence": "0.1.3-rc.22", "@radix-ui/react-direction": "0.1.0-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-callback-ref": "0.1.1-rc.22", "@radix-ui/react-use-layout-effect": "0.1.1-rc.22"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "12cf4a31bf5b23965345b93a2666d58e71e5f1f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.25.tgz", "fileCount": 8, "integrity": "sha512-7Yq0579b7HLUMMmg7Rdjjc7tp/bwiK03bKxsFVaMu5UKD2VAGXTxPGHgoxuRUjtEgJMmYfCTHJj6po6xTKJ0bA==", "signatures": [{"sig": "MEUCIQCpbHcjpbArj3KHtIsJNtvg/O+WdAVu+orwm9NVQe6+nQIgTddScpjnjxZgik5DGU16TmwznM09vP6QyA+Ex/Ir4KU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii0+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJqBAAghtFOzsRg7AYDA1WYoBcRgp+gokdihNIHDs3KGXZPc+P7A2S\r\nPWpZOKFI4Le9Z9iZUwAtqwC3FpGVnGv6kVk5/vNIMNPvWusMcFbVpR0Kkukn\r\ngXLZdUm/nlHK+zKkSSsFDwGQv3TnJWvpzreEUSyn4QtzaOUuFCe2V5qdrOMo\r\ni8GhDA8gnqs+lQ9FKb75ht97C12n1fXNaxrDAOQxWDwehKhvtItaQIVpjjLu\r\nrAZuWe30O5QTXvm1RYA7M9MXFdAhrlRGy4n3HkInnmKnS2FlJ927+oIumqir\r\nbFLd4QMaPNd7opNY7ORx7HioO7ZMzMId4rhqpGac8EqEpGGPzCQn6gzmbRpN\r\nap0l4YJcrymkN/fxxSNb7QeL+Up2nAd5GQQ7pognSzZhrddFuN6tl7dLI+JZ\r\n47gVDM/bxcF/Zw3bEVOR3QSkAIPSDitjE05UYb8ZjpY2rMjcWMR2iKZFUSzc\r\nELkUBTN30Ja3Lt2MAQ/Hj1S167lF5xgXiVnZ48v6tSj7Ugkk12ydlR6AMfpZ\r\nyBePNHZ2N1NEoIabWX2PV6V/AMvc7r6mpvUf9qmEDrKSfzWrthzpmw+EI+Ey\r\nxnX0y/6rhRHZ9PK7i5Xy2J9r7PSNNaOrinx+Efd7vaB8mBoGJcXkchoOocsi\r\nJKey/KJ5xg6hCnD6buY4rssb7azOibPqFVg=\r\n=rRkc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.26": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-presence": "0.1.3-rc.23", "@radix-ui/react-direction": "0.1.0-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-callback-ref": "0.1.1-rc.23", "@radix-ui/react-use-layout-effect": "0.1.1-rc.23"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "63d46d80949023ed3270216748dcd5d42044f333", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.26.tgz", "fileCount": 8, "integrity": "sha512-7o5PYeoImaw3VMiJl76SICb/5p3yO9r6qqW6M1CilsgMfsqLcVdGpeBppcUMVJg1KzcmnP3EUo+fApl5EFQWrQ==", "signatures": [{"sig": "MEUCIDqcx2sdFVettBMyW0T0YhrN8OA+WEU2DV3v7PQO1Uf+AiEA4S7FJFBJK8GqHZpPFEB9rOYDCZH/lEenfcHLp6qkLyU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKHcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrygQ/5ATibNSlbNieOGwThRlgwLimm9WmOU/ccwQmReV2hp7wRsCwE\r\nTl9vNyLMwVwCvg2FmMs2C+Hgh7lY1sc6F56UvWLF7XK4JxSNj6FtgvwM9wQb\r\n3Spa/P6ia4oOfFAcEy0r8DscMLqMVOibCkWEU7lwBlF9mXbNqWLUCGjmheoR\r\nDjcwFHLoGm2V6TaWkLmk+Dv2hrtR7UEhraWq7DquZhRLppHeq3oSZnnpoYAG\r\n3Ky1rfeu5OAzwri4KcYmKoF9ntQItBV7XY13ma7lUt9ZCd8PNnjV0tuW0jsP\r\nUQuGHxZ/MTkuKV/K55AIsdFXr9iQBXWwM0xa7oynCgOxwLN2wYbjKGueURpy\r\n2GUcKWwvIuicTCuknDTYk+XL7NTBQMr0rexi51aC1mgCgHEIUuvXKxlw7L1e\r\nYc4obAP5YOxyuzTa4u1ljhs4mco764EWHDOjFf0/ZlCzj+pE816xU5lRc9oL\r\nd8BODk0sjOzaoTW6xnr3rxzEIB0CeakdL0CGIIFQ1VO2deruKo42ACV8TyAI\r\nc02q78zaLbtjCI6wCT6fZPznB35d2SNxU9NKY9JyvaKMVrVX8/qmj53mKTpz\r\niyaC+hk70BkpyOFHP4ZPTrzyIPHB52vlADX+ImNl1Wy8MbVDt3gmBJQDhH7i\r\n4SJbNLFqgdwkc4m0KAIobNyBCdFYdlYKv6c=\r\n=woR4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.27": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-presence": "0.1.3-rc.24", "@radix-ui/react-direction": "0.1.0-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-callback-ref": "0.1.1-rc.24", "@radix-ui/react-use-layout-effect": "0.1.1-rc.24"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "65b3b1cb6d5be022596577e765f00033576b2bed", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.27.tgz", "fileCount": 8, "integrity": "sha512-ZvV4GPLtOvtO91M7rwXi7KbJ7aC1p4A1MrOY5cgPnJ50OyfXZJ5+bo12EMox79T62chRq/R8CVLflU9SORlFgw==", "signatures": [{"sig": "MEYCIQCl/GmVAG9S0fNaki7M5u71DEPWwGS14VuEg70FFB7grwIhAMsDPBhAOwSAC2k8YsuTEfQk44yS/9IFr2LhhkITosld", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrV4hAAh7v+Hz5jsZf2+qNEVB9tWbwqKrKyRz3jSh/Ca8g6ipgzRCYF\r\nm5MjcNpDPTf9lOxs2IqqtcxaEAtcS2saZ0rWpu2a7CbDOZPG2c278rjD/EfB\r\nWBumDDLii8ijAKYEKj4KZWi/BWa7zARdJajEPU/0YiAINcsjuB8ZWDTGqBss\r\nanKbaHVeRBhv79DiGa2Mp3O+ZnRKtGl4xWRB6Sh8d9wrBIyiCxixWbqtPP9E\r\nXuDcUrakLCfYE9cs+7Jxs92rnZt+y0MrFb8xo78EdZq7YqbZeo4T5eoga2IQ\r\n9W+ftZlZDMkshzMx99c/a+n9kdYD+F/IKh0RZSotA3hI6A2A0oo2TClUBlFX\r\n5dvURyUCnqCqsW1aqmHlHdtvHfXKQ9i+DzbESj2Yo7Ne74QyuC55BwCsopzQ\r\nkvlB5Blx4Gc7vJ3kuWcQr/OxQyIb+3oA6rkcxIbKdlg4NqetdmRLhB9uTDjV\r\nLy7lOB2sVMyPOWMjjMConZNPExi2jAyTfUAMEoQu/HtLgwqlg3+kh866txyT\r\n1AWzEqrVvMxNjCdj5q3wbzTQl8JMQRjzdfZlYdBgh92FyELDGQBx93HnIEUV\r\nY5dwyuNh+qJ7MsSZEnBdwJYTPnGo4FQYTsDENBwb41WYrMKhtvivkBaMTh3Z\r\noVSSK05NInT5YPV4zj/84CKV5iYHQt1lQjQ=\r\n=I38H\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.28": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-presence": "0.1.3-rc.25", "@radix-ui/react-direction": "0.1.0-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-callback-ref": "0.1.1-rc.25", "@radix-ui/react-use-layout-effect": "0.1.1-rc.25"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a6140b3f2addde81a205afb1a68882d85d7b2b7c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.28.tgz", "fileCount": 8, "integrity": "sha512-hnfdMtICgrTsnTeVtev4BF4+BjgUp3XvjeFT1v9I3l+hPX8jxYBKMsmwdTExhophZ4YfIfMPj0vhSjRLy1nffg==", "signatures": [{"sig": "MEUCIDdqAzNs5T80nIRTt8PKWzgka5/AkFECKtDUipjdK1DcAiEAnlBk8gb3bvOm1BV66fjUFyuQCTOS7RrW7vQoxJyQKCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj4TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoWxA//X3vhp9hLltA7i9MvhmiSrT9/0Uotkq/uVBuLhPMJhPRjUg1l\r\nK8R0ZEFiOjWr9NK6/RyCMJ+2Ku8G/jYB+oUKmsEE/gDzIlOSDYb3LrEF8Lft\r\n70kDti8NzJOlaq8RNI9PLYaDtrG/lfu98FcFat7y41J5RzScpfZcEHiXMP1F\r\njgOZYXAx0F3crhyrXIUMzzNNZuIZ6Oxod56+WCnzBSTCwbXM5lYTPz9BRyep\r\nWaKJuXQE86h6crrBCHYBM23xGI6Tjf8amiu+NBRqczoTK90cPP2368aK+BeA\r\nVgDweydR6wlHejuhHtWep+6Ca7x/uYBtlucqRAFwJmCNhrjdN8Dnu9jfzjUH\r\nHV2uNCOML5dCfKj+O5U8nah/niLTKtiaaJ2hRa/kg/nom86MnjXinKvs3RjU\r\nNIWwiwZJxsVyMchzOxArSw8mQmfrgDvMSRW1LhL3hcXgAhY9R2dUcGOiRi/f\r\nLKT25FRRb0T/6O56l+BbtFcRVW95bDLTLU9+rjH1lxX6mSOlbhAH6WvsaWqk\r\n2akpu/CWSOZ8dB5VCmdSPv2J9sUem/evBP7GkX9kBB5ze6HiRx0tyPuhhXRL\r\n6fgJ6v5HGV8L3u3mb5diu9LOxHCfGU1W8juJk+RBMCT4YuX5OlzDFTMhu/Y1\r\ndqbt1193hIg4Zmd9vPeuYbrV3OzeaAcV+yA=\r\n=oNf/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.29": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-presence": "0.1.3-rc.26", "@radix-ui/react-direction": "0.1.0-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-callback-ref": "0.1.1-rc.26", "@radix-ui/react-use-layout-effect": "0.1.1-rc.26"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "156ef8153fe2d4d391997ebe32402ab58ae4c0ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.29.tgz", "fileCount": 8, "integrity": "sha512-iuzB76flBixcgX8HlOcdJSlwWVL9m5IKLZqxl9C2KccRU/GNb1H4/Oi6jFK6WYZTbjNf7h3+aUWXPjHYa1iXVA==", "signatures": [{"sig": "MEYCIQCArFhFUScR4uAO15ksjrqzSL0KSSrrdtOVIfbJBm/A+wIhAK92PlpLusMEOX9km/DIm7UjsF+dPcvXxfru5axboCJL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl1eACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoxIg/9F6N489ZB0qAf4iPhAZn+UMj/+50O5xHVvE1ydzc8jp/tM0ZT\r\nB7wGfy15jk2LznOi8u7O7mC+6K/JfsjsQXo7ECBlQcS/hVySVSqcifW2Fomx\r\n3H+3rcCrDnllXgjIHXeOl0RW95sq5khOXNcdY55W1Kx/MeQPP27BKS9n1s23\r\nzV30SueenJH2B9r+ERq8tHgeqnGUG6t6SelyHkiUWyQvARY8kwWyZ4XDIdk0\r\nYMnSv3NanZ1e4bGZXgD1dES5RRXD1EFtE8CyjicEh4CKff20mhfjcfOJOXf2\r\nf5abS/nKP9ufpePZKhQEtpQ1E5B71v2bO8kyds7oKlXg0U75fgXVoQl46ZvJ\r\nLG1bgZtc5H/pB5PUHdRdiJCZdit+57thiwp0MpOnrNPK+0hR1hn0T5FIu2oe\r\nt5b5wZDTA1Ih04xaHIE50JsYY/1xJCuLzNgMYKsH0IdBIp7g3WzhL/VJ4KU6\r\niAfLov+bPLEKBo5Ut8A+pn8hyE2p5p36PzrZ3DHqu2OngSorU3ozV4T9fbK0\r\nuyRrvQQoOUcodpdoK+V7uuDHlyT8LAUWGZbDi/Bf5Qkannud6rghb99SPuf0\r\nnlKJ9FH+t0wj57EPmrS9GqHPPM7Hq9H4W2yzHavZvFsyrv7ighjXPWcZldKo\r\nz9wSMMi9mIGMaq0v42Nk41qiw5BTWQqiDEA=\r\n=yHLW\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.30": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-presence": "0.1.3-rc.27", "@radix-ui/react-direction": "0.1.0-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-callback-ref": "0.1.1-rc.27", "@radix-ui/react-use-layout-effect": "0.1.1-rc.27"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "331d3e4885b6b6ed3e5e5c69550cd5fb266a2f6d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.30.tgz", "fileCount": 8, "integrity": "sha512-jSruZxpLySAut+BYlp7TbtsOMyQEOOeQf26kS1a3ANEfsR/9oD8quzhHs1BDXBYV7ekEXr/h6SEFCV2sL8/rFg==", "signatures": [{"sig": "MEUCIQDcs9YWVE5Np/OK5Dn80Cj1rg9nIxGZkvt8xNnEJxBwBgIgHScaAGlCfjVytZG51HNKVWJolE6gdgUxOEN8hPZYQUs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ17ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp8cg//eNocL7pjOyMPJipXvjHvtXg/vxHGpij7msw0wiD9+vRpU1qh\r\n5PWcHmJWjIT9z8WNws1H+fRZth5U5ae7ZpvMr9EzNx4JkKB4/kHNJ4jb3VUa\r\n4PFqUxzW2//x4EeM868uAEJ5aia0Nj8b9LoqZY6fBX1Hx8zCwN1upSiHZbpr\r\nEitPXakmgasrz5Ul1BHiwgF7xIxBQO+kTQGsiG/z0c82hRRWy057M1LbZNnL\r\nWluwR7iTJo/LjVkKE4NNQ6oJX4veSF5KMl8bAhj0KW+geu54EeDiE9cOp1VV\r\n7X/lQBudEYTIQ61GlK4iCWIJY/iVQG+88YgeRCv3o+vYOQRDAN+AESzZkh6J\r\nQ+kldRKxK3Lqa8RlZfKkuEmXtk23R6ohr60PfbTgz0/e/tvDBWnvXCG1AwWe\r\npg3RuTxR2HVtrDkjw4LMbi8cS5qbZY6dda/1xBp7PFfBRkx7nJyx9RYnA2uQ\r\nARGRIUXMbB5uSB/K+vDrBvDaYFMC9I0Ky/0B+YUPpAxgYFHl418elmhfxDIQ\r\nwyCpZDL85tUIvh4bAMFzuOicntKQ0elGSM0Mz4SDZEgBOv15kj/oNrtoqCih\r\neopcg6KVXljaSyHU29WB2SuAW5TTfUvoeXG3187pFqplVgdkt5H++h0MF2qH\r\nCe91eKrpxXM/V4gm5TNqd1rERdDrT+PmeFA=\r\n=qsoX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.31": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-presence": "0.1.3-rc.28", "@radix-ui/react-direction": "0.1.0-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-callback-ref": "0.1.1-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cc6641781222bbef20926983a310ad66d9d3995e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.31.tgz", "fileCount": 8, "integrity": "sha512-G/fd/9+H3vKIFSXt9m2cxiA0g71TRJzwyo28IFzB/Y2SQFGMUvO552HKAsQxofDh8OjKK9dF3CmoZGghspDxDw==", "signatures": [{"sig": "MEQCID4c8z9P5guN+NfRFXO9dc2cvloy4kCY6B9AxWFvQd2EAiB2A0hhmM/zlPcj5w/2rfIc1sqvQBezCZJ3KEdHPTkvSw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildNrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/IA//ZsMdhaHXvSZhSVEuwrIHqT10cuM4ob7dFOjNxAOcXXocSgxc\r\n2EJIXGj08TSiBBZ2sEyEK6jpfXn1Y8DlZNJoIIFeB9azInoNFZFALUasu3MN\r\np87m1stApTtQ7gniCqmaj7FTZNBmL6xg/YEMad8hR1BYaf66a4YffU2iryA0\r\nb5xVC98vYfXOCG+4CQOam477aveFftKKQqTE8PWD7MJJKBHZ7qg8II1Wq+Pb\r\nSiANrd8ofMOlEOY1FPZfV3eAiLfibfXfqIZS3hR/Bx5E33oAbKNSKhwW6Iv+\r\nwZB08TpdEAia38N7GGL7Se06pveqyrKOskBTRiEWkvKV51X0+fthQkirYXxG\r\n+gaxilrdUpROQQ3oPtdOFcyVddiFf//9SrB18Y27RNgaoIAYghXT8jwRS/ge\r\n3kWiZkjkIytvwNCYvFEs/vLA0L7uYqcNQBZleQ8CCIv+HMNUL5GDtyXQOCpu\r\n10mqGfx9iSzt/uCZjf7PXhcvv0aJlXC2iRdK0U0PA7DNKqC6mELOvHu2HDH7\r\nbErh/ODrJO5bZ7w99v85UFpqsdcRWCjpSl9nkffivKm21pmqcFmLX/7YCfst\r\ngoa/Bicgt/wpYttkstxhEEvMBKnJLq8RhCnYI+P8/J/Dp88e88/qqxsUAxbt\r\nJGKWsGWhLN8OiHuLjeO/vsuHL8tOZPr1a5Y=\r\n=EbRb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.32": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-presence": "0.1.3-rc.29", "@radix-ui/react-direction": "0.1.0-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-callback-ref": "0.1.1-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d4a79a833d46707761afebee290cac9827997b07", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.32.tgz", "fileCount": 8, "integrity": "sha512-ZXEfRXWLl7599/IOflBms9MJ7LfhqXAO9p7CvJqy0K06TCfPJK5Il3IsTsq9RqF0QWj05ka8XhyBxrPsGeStgg==", "signatures": [{"sig": "MEUCIQDzQA95z0tQ+ecYY9KbHZ6N01XEdmFJntKVv+vvPB2uiwIgHrbvGvK4ko/yYYg5jHnKW5qHJ9YnEtR4oJNUdEaCglE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildrpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpKmg/9HUsoTvv6II7wvO19VmMUTL7mHPxPHVb373Agz4WXpQ9NIPh3\r\nvpWgGEQb1M2Yl4ILi6aiKaLARsXQ8RBQBZnOR/byRTnfx7KDdnjGuZ6qi7NT\r\nIV8bvkWM8XWxNeBjaVumyknQBuJcSWy/D7awYBMc4Hfr6XOFGUn4XFhcrqXp\r\nUF3FVqQqcizjodxhQXfPojVv/5iiknIimho/OWvyT8X6QMy8GelUTXe37pJ1\r\nhPiIND5kohfabUNdGnwuMcUJXI0nu/tRGzV5Jh3vWL1jkzBI8OQ5EIxY5X7j\r\neMfPr+iIatRBchkeQnZlWE7frV3T+m+OxVqNsSC81lvgZh0UPNqDlSXF43Yj\r\nr6SxLca7pfBkheuZRNK3duTYu07/WePg2m17fenuDt3S/1AkC2l3XuzgtF/h\r\nIZB+G+z0yHj0IdbGMeC2OExMTP00JeSr6xn/QAwUqTozCyYudAYh5LAMFaiz\r\nj29qFBNsa23rr0ikIc02IlQcB9c5jiVlgp77vwen7WnyAw+YfiBeSXAcZTdd\r\n2aGIflBeV8p/fj0vLwbr8W9PLR4ILTqCKkqCY00zcVX31G5YVf/iMC9/+y5+\r\nTQMfFuhkof7iAOm7KpEy0kvnR9FvhYlHJ50MNei+GSYwnxCzMIs/dlOzYm7K\r\nWc5sYpazh86bZp8ssO437B1xKMNchg9+DXQ=\r\n=aaHo\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.33": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-presence": "0.1.3-rc.30", "@radix-ui/react-direction": "0.1.0-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-callback-ref": "0.1.1-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa422365fd388589ca4ac6977daf731d4348a9b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.33.tgz", "fileCount": 8, "integrity": "sha512-yyNfLFasSfBHeIoQZmOsuXKT3F6VTGtj+5xCcSwVwsEkpqF+/cCY6CmQ+5E5ynjpo9lykJKlQpXN3G3P06yDoQ==", "signatures": [{"sig": "MEYCIQCGxXXzHTribtVLh1LcgpL0Wpyr3nWFty4VcwBlzvbHHwIhAO/OVYp43LRLxHxFSndMKCxhweiQa5jiV/QuyAszm811", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile2cACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr7og/+MbfnNgW62yDocuOXDIfP3IS1xPlVmmFZnHQ8hs6HQ1ypDIMZ\r\nArV6d10lNYEjfyXPtXy2A30JY1qYHapCmihlO9a1L46dsZfPedFXqqW1c1p7\r\nNBfKHXhkb7AGAlc+lw4STXUWS2mkeEObU98zXE0uefo/Hjt8zIDivDraajIP\r\nxbhcFAqkXSQwrL5RN2irIWMhCRZQenCbYBjE5OB233prT8CwrxcvJno+XOU2\r\nElb/YjlSk0GGKrEZY/szY9Y5q4XEoYpMno/KHpvQWaxQpG/HHp93prEqGNBh\r\n5MbQEGlR6gnnCS3ubl03TqP+OnTyPYSm35SBLCTWgAF8MjaeWuWmm/1ukAIK\r\nvJdUH4iiH13NI5pUhwswZ88hVMBn49yu+BzqnKoF0fhc7GGj+LZAH5t2G5Y5\r\n33ZeCetTSR22yENomDgRMIgHPV3qKYt4yWosZ9sZP/bnW/StY1/kZx4yAhDy\r\nd12FrBgunkre828Y9XNbtv8b32T/n84bJh3kwsvkz0XIGCcZo718sbVEEA6W\r\nwrg0dqDs7y5D66xDMZ4QzbW5NWV0R2q1j8iP0bcwd6cVA+U0641XI0Bbb5PU\r\nUNzaAQorMYygQQMY0+gRrPtt8PgaQYTo9BSo8OwcKdJxqUGniPjYlMoPZBlE\r\nzMOgydUATemiFYw0npBxqNLii5IxtTNXk1E=\r\n=Lfvx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.34": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-presence": "0.1.3-rc.31", "@radix-ui/react-direction": "0.1.0-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-callback-ref": "0.1.1-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "258f005edb63b83141664d1a302459d456259ac6", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.34.tgz", "fileCount": 8, "integrity": "sha512-hpRYidSnBQK1jz/CAMRPIaXKdXcU0BWwb7VZm8W0r6apHauMTo6yfkfLqtLrRByuH5fE5EisQHpp5UxxokPMqg==", "signatures": [{"sig": "MEQCIBs2/jckVHQ/dZPulQ1TwuK0E+IhhBfR4sIXvsHTkLwMAiBMQ0EfebW6wPRSaSps9KC4XUWLeHpd+7rRxsn8S/2eDg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3X6ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoKZg//bPXlQHA0KRsMJOZSV1hq0UqgJfujg9idrfSyEbJtuF1V7edf\r\nsnVtpwCkd+5wsm+7kdEmdRjTh9g7knm8N2f2Swt7uDmYKzhn6YTJNoSQsJTA\r\nDhLzx4wKXq7+Y5uEXU9338BPxh32fbYeBkJYnpNXia2CH6sIN9BS7uWBUhyD\r\ne7bzWFsH4MIaMBwWUPDPZ/iO2/IIr2noJ/w3xl0lnZAtlbEzdt8C5eKg4hP3\r\n3fMD5vtegg19tQFbsDe55bZlwrwZ4179u19byvcVeDPcWRqSqd8Lgo+j1d+B\r\naKcfn4vSFB6SwbG+kx5AkWNtvD9Bd16uuYZcMJWOw0Hph4Tj10tOb8Ckb5cT\r\nKpkkxZqbtEMu2Z8Aq/bT3x+iXPH4n1+5W+o1k25XvXmeM/wyxfqYuccVSb+e\r\nVoqKQGHsGKuitZ2hbYNwgoKaJXVsG6Hr1pxAKEGJ1ESRK6Mny6EzSjzQJGtr\r\nQRbSpQ/kWnUkVMiYRQdn6iserLEYP//9Mn7ElTlhe1urx0r1Bv0QfHZy2LFU\r\ne0C0dtqzOqkDO9DUpzT28TILcKgfBT3rJxvExe0dakat40UR87ZKtr+ouDch\r\nEen5Duzaf+/RsdxrOS6P1xG3/4cDX/z9o50K67o4wXhYHRVvyE9+4X0iv58Y\r\nlEJ+R1PqRLqZI0KsAYwlsa4UUOpuD0OXRRo=\r\n=oSac\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.35": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-presence": "0.1.3-rc.32", "@radix-ui/react-direction": "0.1.0-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-callback-ref": "0.1.1-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5190421fae5560fc8c097d054a2e86c282ed4638", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.35.tgz", "fileCount": 8, "integrity": "sha512-CfAsGvx28MRT9Xml8hp9U+54/fl+MbnaeE7kieApgNzTnnF/DQJq0lf8VvqJeiOfBtjP5bussupc8lfCeVvn7A==", "signatures": [{"sig": "MEUCIQC4L9SGuprpVnRAlPSkbru3rprzC8Lgk83Ww3TPsaVsLwIgRhsMlrkbRTLYVfvUm0aA/lNwTY8Xg9jBbJNUoDgm3Us=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniR9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpf3Q/8DJXgKp4U6FbJDvEUgrtMvCrtZeeCu4vDwgIw5WVIjiEtu42u\r\nVnRWh8+9iDujBITGGqHZ3ij8h7WTaZD35lqTpcLam0M2xKsvOi/YgYzpn1lG\r\nS0F0WhF9rwjcHEHSQot8P2GxXlXwYPTeCH6oBSCAOshowainGoNtu1fwX+NI\r\nyxL0gwhsY37pNqlJIqNklBP+/P3jjiFLH34qcJGmfhKRb4cVIV7dddwBAb2L\r\nlX4OZ0AzJwvW8FFyVjFuXITGDjytq3xtXCPIN3zcDXW2a4/anrYTp63nnYAO\r\nTQau0GcCWx3OWD5zSLlsTU0ux1KR1Cv/VB2LgP5LPy7UQ4T9J9R8JzbYK5ie\r\nKxK9/96KflNFLmk3Aqs4ijNXknnNKHOOab8CdEAVE54O/avYzxnMfdvXiDEG\r\nQmmK9UTvxDVAkRllnNH7hrYkh+vLF9KdUc1OmuytLAI+5d0A33pjsaxJCBZC\r\nkEWUUtkhdUDK4mlX/PrTAlLJK8MqnZ/ZAvWRwG3wMTDeTvZ2JBoXeaHSpvoy\r\n8H5gcsHnpsaR7Q4WnYWylpsJtKdmfzzH8yWId73ikrBqGjxLaDzRXNcA5YWM\r\n1hoOemgcJZ1rALgdjlLWbwHSj0YYPqWznZSmy9vO5PgXO3rReHshMUf15dXg\r\n4OvmSM2h1Cvy9d2PD9Qssa4YRVbY84chsUM=\r\n=XHEQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.36": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-presence": "0.1.3-rc.33", "@radix-ui/react-direction": "0.1.0-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-callback-ref": "0.1.1-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ff1ae0002ea06c923f2b5048eb0cd115a5240018", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.36.tgz", "fileCount": 8, "integrity": "sha512-WlQBynJz2nUsEaKgLCUZZLZN/oovUevvjnpMySijTSnO1G8tTHkEGdt4ENZLDqjKr2ZagWhfcKghhm4f3fZ4cg==", "signatures": [{"sig": "MEUCIGypr5dyGW2wqxZq8snrnUAL81x7Yf3ve05xE9JUK7CWAiEAiSYHcsJbz+Cn5y1c5H8Q9W9cj54Sfp7Y1Vr+db853Dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHcgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp3Sg//Z6akyECusUpnllR4VrULz53DAJMI7mlSG3cA1XTvx1PBrg1R\r\nsDXZ2Gv1lOCAUJgcOXIxR1Bv0r3ghwwcqURn2wU0ttLpP7gF5S765ruEEPj6\r\n7jiL2KhfdDcq6pc28Pc/HrRKN/IQppQUK0Z7I4gkGal+Zl4LN2vSSEl1eFE2\r\nNhgEHPmUI6814ASppDD9uFVaTa5RUFOft2D4x5T7pTKbzJefniQFAuCRye6H\r\nK6L4Bewjs2p8Z72mKLHmTnKjwfnLuZqzlduuZG5t9dhHgJ6DIxnX+Im7j77H\r\n1EcSSCXhSPV9rgehUezthYAeytMtffT+UzIx7UO5mt7LOYZ3+9KhPivUEyd6\r\n5RBbGpYZ2Rx8VZKZmZp2tuIOZP3aIjIzVM9V8b+L7uwI/+gJboIr9hTsWmOM\r\nl6zubIcY39k5YloHviMganHF4IHfmU8KNaQKZzCJ3RJ+9oyLuw9i6xDEgdc6\r\nZD8gA5qRek20NNX+k7f5nBcmVVgU9X+j069K8G3bOy70eJlRN5WiAZbsdlmV\r\nYXw1aJ4gWm8hYZM8Ya1cNwEmV8LjrstM8+AZGQdZeyx/+cyHfumEP6se6w8y\r\n4i9xFfL2J3s2UV6ar3fms4/sDnMJCEq/tcdpJ8NDwoFCT1mOMMx1JCSWCOMn\r\n7U0zCdUsv7+2kTIOHlSJy3RdJxtTHQTwNPQ=\r\n=6zwM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.37": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-presence": "0.1.3-rc.34", "@radix-ui/react-direction": "0.1.0-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-callback-ref": "0.1.1-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa0af4be21dac26d7b782ea5f129c37754089590", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.37.tgz", "fileCount": 8, "integrity": "sha512-<PERSON>jWtmuCs8qHyLLRNd3DwKBC8WeSZ1Z1rJGi+NejV7A4ub9sysCrrMb1mQj/8HJss7CgTqFmBdMRHWSl37ifZ5Q==", "signatures": [{"sig": "MEUCIBlXxFuzKGl2SEIe5qWyx5x8R6l33BMDYK38ylUXVxMbAiEA2uupxtV3RaANal7b4TzieJ59nUo3rUEDA09KfzwfC/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH+SACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo7YxAAoQTQ8QeVLkN0VJRaGHu2U1vFLk6egGXBKE64BFZKrOdtkQJX\r\n48fxb1R7A+E+TaYSBJCf7dcSF42tDHcIdzEzSiVE33AT+ddHTAyUwOqUzzvb\r\nm11tlOaC/wB7E7CegWJKxTshub0IoB0Ymuidr4B1gP8t5UpifMqnjtOEgdg7\r\nF2s7aymT437yRrG2GMPQOzA/+xNVHcTOGxeUp9ZDTUs3k3xQYr9Dy2VRBntE\r\n/cIB6yH2ewtLTyz40UcotWfKzlu57zsCY5JfbEN2FYCMx33ZKC90VasIIaSK\r\nSD39ceoVz8QJoZqqNACh8cLtNyJALm+RlysO904DouGethpu5575xuOeyaky\r\nzk4QsYuVN6FbOiAzTi5j9gYmUPuwJBOavS/ETAkX47qPGa/WRnAcZwbHPGa2\r\nEuq8lUadKWtZ4+8Hl+OjN+QlyLmUYDtH1TxwHoOr8ZFSE1qwAOwVonPxM943\r\n3uKO5kGXDUbsmeLit7a+ZWtCCPnv5oE3mRM3UDIccCrBxkF7pVwwmO2osbxu\r\nFyI+UvqcXTHbJrvv/n3niVNIaal12ls70jgLGSrzMM/CvJPE2kD0Jcs6Up3B\r\nMKPAtGHnEPzGcxr7vKE5ENrwxW0AY4SSzQh8WOYwxTlZwgigmQlQdd2a11X/\r\nsJf2O9P6fDXgwA2yw/J+VAJR9dthjxZfkZ4=\r\n=q+W3\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.38": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-presence": "0.1.3-rc.35", "@radix-ui/react-direction": "0.1.0-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-callback-ref": "0.1.1-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c854d4fe78feb51192da29de8fad11a88ffd6e68", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.38.tgz", "fileCount": 8, "integrity": "sha512-WrvYBz9100XAkltImutief/hvg7UCDU3Ft+KaONf6Vbk2p9uf7UXvjst8RxLqPQUmpJj2qTK0bGx8vR5PQlPpQ==", "signatures": [{"sig": "MEYCIQCV14j6KYhVKtUJOuIjVIB6Jijyw8F6Bdptc6G8hguMWwIhAMqvGrPkBdwREd+aesDQtFI53DS2gcp6bAmqhwTih3pM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOZEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqzJA/9FH59jylYFA5m/YDPhVp7zx4TxpBr8heR6ZdnBZggMGo4ZAyo\r\nGErt4aPpnxkH8Jek48W9yHDSSwc0dHVYaugm55TMylQqpSyaTXcrkgzHx9WR\r\ndZZnVLphFPVTxc92KWi31MRRlb74kdClmw5Z07X1nwG8g0XKvTKZGMVH4alW\r\nyq8KypoK++B7wGKhg+bkCpugayJXxFeUOsC3mIk4muCJoNs/L2mZXByjuaqa\r\nZEctcia3bK4citJik2U/Aq6P5P+VPfPtBAAePBiNlMBPQuH0ayPm/SbxrvLV\r\njcQRvazmuApZVN7UwTyrFUmV5vmfBn9VjBfsyFOb1wSwejCl+8b2wBnwF83u\r\nEEj17W/gmwXNft2S+FVcqHzMJ+ltdCzSRw+Mu4Ak8Jm3t/5f2hMdrExHY0EZ\r\nbITMEYV+EwoANZGF5s6LzRkZ7LzHTBqTanYWBpFptCAs7FfvBI9uxxonIuTc\r\n0vl6/Fef9B/L335am5JQeSD55r/Rghb75mcprgHRUbn3NJda9dTkSmqYUR5y\r\ngsGj/Zmge6q7FBkjSXL9A3Ou8vh1c2/Z8t2lBZoEFH0TvXQRCd2UxbT0I8gY\r\nyRHULzmAuNC76IigJNZ94ldx8Ew/O6JBI0r/daEs0gDNQ0+IBxgs9vP9G23U\r\ndfhU+FLfqgyu0WgpQU6P47QB7GNSohgj/KU=\r\n=XNtq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.39": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-presence": "0.1.3-rc.36", "@radix-ui/react-direction": "0.1.0-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-callback-ref": "0.1.1-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a320856621e57a61275a13131196a081769eee5c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.39.tgz", "fileCount": 8, "integrity": "sha512-/L9j0fGnjfphzgHJ4Xzi0npAdsv1kbMNhySUmNzn6wxs797WmPOtwj6oJ5zlxZBFUWclQv/v4bDBvKrTNfwmHA==", "signatures": [{"sig": "MEYCIQD543ydZAW5UiTbLnr74xOkSCQuzB9UdsifqGviFZ0FwQIhALTRTTFDBNOLQwMz51h5CxB11PSG4I5yL7l5T3Kf1XjI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IzACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEEhAAmQxYNBxnXiKVJDirCpdEtkC8iDhIkbcNdIokIAv/MJ9ywzKF\r\n+o5EC2oaybqAo6TS65TBqBmBVVjFhjQneG1P9l74zHkzhfnUCGOgYGar5X18\r\nWbm0emlEToy4FZQOLH9G/H2Kmi4ls2vPoZo/O8o8sLzXp6ZylP2PkZLTZNPY\r\niJGIbImtiwhFLI5GeXHlmrybSiDSX6YPW1ZUz+OkKFfNXOcMLQcGVr4Kg6RT\r\nuK6tIO3tB4bT8lHBMwN89kh9dDEAujFFBbKhY9Cok2Rjz3C2v0P2XjwqDw+A\r\n5QLAefkZxibI8jjBoCmrYeJkrcriT0u/5srQpgC3B3RhPpdX77IZ+oz+MVzi\r\nXpkvl9jKHko8YxSG+FiydaWIYrTaq2OjiVoPc9i7fnBNM8rwOorV4j5uFQuq\r\n6rwmp2f6RKx0apvDShLtEP/XvauAJxnt6/sv8KDBzs1rCvo6ou8A0eLr/VhG\r\nI4ovn2Zek64HtY5Vtu6hnRhwAoZLO2lk7rHS6VPM5xnHrRHUM8SEsojKpZO8\r\nrwBNhjoGBxJAh7jdrKa4VNXa+RVkRI1ZnSffIzNvkHiU+adv8g2Ez1Li66CB\r\ntmomk2bbJpz62/4oO/VKzXliEVOYwtukXUYqR6JWSyFoV8XxBmJj53SmuHsM\r\noHATJwh0SGaqEusobJlgmgdMTGmHBQNsu7E=\r\n=2W5i\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.40": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-presence": "0.1.3-rc.37", "@radix-ui/react-direction": "0.1.0-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-callback-ref": "0.1.1-rc.37", "@radix-ui/react-use-layout-effect": "0.1.1-rc.37"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "19b678655491c8b8e6e149e0b04831ad4b2c0710", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.40.tgz", "fileCount": 8, "integrity": "sha512-nsTwbgtTn3KzTZlMHBQdTx4G/zLEM4z5MQ2k+mKrTbdYvcX9e/Fnm+LJVWKjBMRLcf4i+AB+gsjhm4tvlF/u7g==", "signatures": [{"sig": "MEUCIQCWS8HaUBnKVMO7dDJzeGujiJVVmChtG+b4p6d9hFrmHQIgRBZclLevAYnc2RCCtSnGsoigZrK4JVOdpg2oCmh24ZE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0oHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoA8w/9HiSK7F99NDHH01FCqSyaRZ9DAo4CLXiNv3abHY7ig4Aam8mN\r\nAAkz/gDXcvNdj8+u6B2r67d1ofFiKVmmoq1RdozbTmd/1Zwh6ZM3J3KmLu0F\r\nqpVseJ5bwmpZKXwJf+ySV9RTdv3+hy+ZbW8Ehtb7g50A2PrR5jXS9Nwaebe4\r\no5eDRDqDbyMEQ2eYosGco66ECcI9I3s5/BZ3bTaXKMcBDJs70MeOJZWI+9Vn\r\n3pB5Sha+VZ9QRUN84yN7tiZPvRxep6pJgH4RMDp3Opy5ocSmrT/z7oXGmZTj\r\nBFhOMy9d3OrwKmlgE7sfd3eZnT80I/nZ7seClCmQ/3jrGjyjUaiKE86n9Owu\r\nIXZisrDpo6vDx4pol32CVqzIBbADvKd+iJEGrARm08cs+qBoi7y8wnjMqhBJ\r\ntmRYNOx3UOUH0EetHf9/Zlla3xXqlH4BHqffELezRzNlZn70SjAaXwTo13or\r\nOSOqsoxcOFVbXaHZD7I91Pr2RBYkNnyOvlSpn/LPKPPF0xxg54QH9tB+8Y4y\r\nBiysAfXec0G2Mxv6hWEu2iR6sQDBHAE21EWbp8fx2X/QzOG0JYP6s9/C+HzD\r\nQCG+uYpji+Tn/J1uv60TPD0sAQDQV+w79/fxCdibsgC10+96aIcKpcSjfSDx\r\nkccbDIvh5/6n4GOmvS8e0rVc6IhQ11MWxNo=\r\n=l8Wp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.41": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-presence": "0.1.3-rc.38", "@radix-ui/react-direction": "0.1.0-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-callback-ref": "0.1.1-rc.38", "@radix-ui/react-use-layout-effect": "0.1.1-rc.38"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3862a7f667111227b66bd331814108d85c33fceb", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.41.tgz", "fileCount": 8, "integrity": "sha512-KBY3lDrACNFLxJJbXsl21YqTIdroyBQTerry1KKc/rr1ywaA9ANIE15Jru/zX7XEOWrCG5aNY7VmthrJgXLDGw==", "signatures": [{"sig": "MEUCIB+GQtwxv4EyZF3gLLhQHbnOcvNFz/h9BNM0dCP94UqDAiEAoL6Y4sFem8e6vVyx3i6pkXETVOC5A43JjNDxrsDPmQ0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzqIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqo6Q/+MF5G31Ya2UGFf79aLhybzesSFWIBLnrwW3WuW4Sp4xVrNk5+\r\nQIfV2VQTtiJTLykwhXBf8oSdi6UypqPlARvPFBgrl9eCPAHjkhQQSF79O/RF\r\nGRn/BYyRQTbJiEy5sgAALcw4zLApT7VeXyPTjh53LcWMEGPJBfVWNG+zutKP\r\ncNO7QV5QxmZyRyRUssJbsUDD6VhJvbOlQHl5zVrFFydYJQVGwGgn5IFj0FPO\r\nn+0H5mBjiANlDlSI2HMzKbyOcEt2Lxbp3nOapS15ZHyG5mFhPj3NUBAJhXTh\r\n4HQjFd2ZH2Lh41iYY31ZmC0yZb4SMjHGBiNvsVb/v0waDbcSmL+bAddJk4rW\r\nDNeqaqwymR+ndzthEIB5z9HWvAG5Q6ZyAzRVmK6gbdcCpjh8mDbOVA1egE0L\r\n0g9JmA4RBq9GfikKAQ1arl4xl6Yivm25DyEsrw4/axeRT41V6L9Dt0jZrsa8\r\nLgWyCm0jG1kZJx5BK/DYQhS/9HWdec/cf7s91mlB48ODlVERIDjFPj/5/4gI\r\npzIgYCRa9vqALUguLckf88DaNC/wj1pl7/PtJTudD6QRqtkMcv2PI5Y9kK2Z\r\nCg0pswrUd97gUjSYEOFXu5+bAPfiXFGzKclvsF0VJsO8Htb5nvLmFF9EegzA\r\nW53R/sS3Lgtb+bnklSHBbMCBuZwXYFMBPx4=\r\n=J/3c\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.42": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-presence": "0.1.3-rc.39", "@radix-ui/react-direction": "0.1.0-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-callback-ref": "0.1.1-rc.39", "@radix-ui/react-use-layout-effect": "0.1.1-rc.39"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d051692277d4c417f28e1d21f0dabd92c9b6214e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.42.tgz", "fileCount": 8, "integrity": "sha512-KEAN5z4GfcsiVWF6ZDTInhtR9RpDctU0X9PCbwam69XNXnVZstIHnSqXFXi4exeqN+lnNkL7u7mx/PKl+1bdBg==", "signatures": [{"sig": "MEUCIALrLyeqRy0EE/sJ22GE9NN+zY1ZCOh4pWG9FVYhEscQAiEArXtx2gqnjkZEQ1+ilRFTvNqslc0rpZLDy3wZvDC7uPI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz+CACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZxQ/9HPzMYN2bVSuZZyqd96KHErD245f/hNuQUXrQO/Nrlrgs9WHZ\r\nX+3X+L230Wd8JIKSi+OThxPHy5hQ90vGNaLTgpYtD7i0qgw7pmt0p3PftM2D\r\n0Qacjgnr3MS0i8TmzqZ2T4kQJxsowXOU/BrpOEgcKhOAJ8BKyV2/qgHsSAQO\r\nI3qEnMh+1SgeoedmpHDEehWpBBx7Nmt7knMUm/8LE5EEPdj8L61xGDrER9p7\r\ns80pgVwx4Tya2XicH9slOIGbYQusZwAAIcD87qVyg6R/cmFEdoBIe9Vb+W7e\r\nAB9z7NYQmHEpwNuAaEUFfX9L9Mj/OdrIeZJPVW9z6xyMHLuQCsEAT2ne0b2J\r\n7IxOcjIvkKrV5xEnQSDRSqjv/dIKy+Twc51sHjBlAdRleOhmueOwqUEAp3JG\r\neUG84AkJPCaWCsWwZK32vtq06Rr4gveMa96lYIYC+L2j9nO1qNhqRCklv26b\r\njKZlQcba7+IVrL8HhqSDoWbfyPQ7bnBJS0Y7QAXsiqdyrewF6OYWr6O9T0kO\r\nqTvC0kZmLt93aiHVKwXNcpRTOC2MvIR03W2JZJnnOUiB+WEBwt50le2X0e4x\r\n/kG08SYEcK0oO0MCUhn1NGak6J0QtDoqKIjaKGgyjRXk6MRdcNSi3HBJxekI\r\nVoKNdFob4Yz3Q/ZISC6eBwbrrT9zzF0gEE4=\r\n=RoDr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.43": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-presence": "0.1.3-rc.40", "@radix-ui/react-direction": "0.1.0-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-callback-ref": "0.1.1-rc.40", "@radix-ui/react-use-layout-effect": "0.1.1-rc.40"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "594511ed0c36f98e8a0a18cf488d289ac6b6e8ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.43.tgz", "fileCount": 8, "integrity": "sha512-OZRsqwAuUYd8J/pmtpgjCddO4bQPngwVNp7G0w9uWZwYDUFfAkVktjDLzCNypoPbmgfNpFWvUfx4DfJa7xAwAQ==", "signatures": [{"sig": "MEQCIBhTm9nPkkFG2zV9iFJa94GwmHrrlu0ECvKk7PriYCG8AiB99SR8pew/YwasRNrrpRwW6WaYJF7TbxyngNfy2nymLg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 250337, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0WRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYyw//SUHJQWStxFH6UKBRUYTmwD4mceF07a9hRq6Bu003tEHXGQGZ\r\n4LlBipbnDO6yejGQ5ZlzcWpRthLPb8xjC9COD0Re9ZBcEIuiWG4LF6gvMDy3\r\n1dWYK/pUt5qy9wKhHhn/sG5YiiPFqOtjjbHqB6oQSDBlch2xQDwbR5awp7ZP\r\n0fiUucpsu+7tm6Zvo8luEoC82RRdYCYKJD8B9nIWrpQsqbgUdLkRhxJ0Jre5\r\n8oE/ROYVfhBJv2NhRXSRROKEv64lqyra14UgGwgEempFKOkXpRKN0ByO6Pqp\r\ncNHV3RksLm5mi1hA9uk7c9uYHnzw3as+hI0I9FfgiM+wTBQIr0KruLDa48od\r\n6SGWiv3dWte0dk+2FwxwgGKk/u1TEOwUUYU905GC9QdS10HPrjYi5kbQzKzy\r\nx87yQfRT0PnCDLBei2QOz6w3fElZYWuLpzhJEZOK/H1MYBVgKV9X9tdVpkug\r\nhYB2SSjURQgTYNgwtc/bTAL726G4rmjHby+BYKI2/PulK92iqUqE3nVqhS4b\r\nNWP+VdGc+yVimhbFKA9G8/0IHARN1srTp1mBGl2tTBGEdv6K8Pw46KR9BsEp\r\nkSQ2CQzwLt1zZ6nwanBPPsSMMDTZnneLp5q2Iw+g0Nk6uDon897qSEr+k7+I\r\nQqKKdBYDXmOvQM9rLbpWaJW43UPHTazHum8=\r\n=WSQk\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.44": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.1", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-presence": "0.1.3-rc.41", "@radix-ui/react-direction": "0.1.0-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-callback-ref": "0.1.1-rc.41", "@radix-ui/react-use-layout-effect": "0.1.1-rc.41"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1a1047e02d9551cf99d555a03b6d53969244a730", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.44.tgz", "fileCount": 8, "integrity": "sha512-GHvPinsBRakTNFiA2HSvV4iHB8r6F7Z+0Le5gLoGH7Do8WbCHV9srVj8aRDipaHkPIgOE80oovhOxprR9Ue8xQ==", "signatures": [{"sig": "MEUCIDk2tzO0576z8N+l7syaS8LZN7L8Iipsl0L8Ului+fN1AiEAkdgXzo0WakjGCdQM6n5gvPd5dNzPEDoIcJKMtytfwCA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaZiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIoQ/6AhMxXSwaq+GHCy4JqUyGbVOs663a91/Hz/oX/v2GJXLTSkgZ\r\ntwfAooEwGVjcka+ybXbgSyfMucpk9PRjcjt4C1QISgT55GhV/dRusUessy7K\r\nCMFE5TaM9us5sWcmEH1LIIFFvCCUqSbIN+yLUnAW8KiNTzEkSSeDl0JBAHaW\r\n/1Zn5rvjU9Bg2JbzYFDhpkgM2R+M5H9Cg8mh/pb4iLcgiwwvz10UekjTj2oV\r\ne1tT3N1Fe7ofN11AoEYaG1OHI5Z/ka93rX3QQ75am7/NcpEQdDblxIXsJWMI\r\njp+1z4s8xVZxqJqjobdXaLNmDis11cKOiv9e1x/AxU537iEgWtORIC28WyyB\r\n+wifizqBwEK/uJp7O7wq0aS+acyxP8hhofM6HyELPHvBh0LOjpJpCUBH+uIO\r\nikv7Iyg/EjqC2GSDGDE+dJPYCyrwLgUA8w6p0NllRVCd8yc2mKDkTH6aAs0z\r\nOAoWHfzJHF8hw9AdWz/zFhBqc/BXSQKE+ZJvCS8lPdnriwB3ZHCrZgoVtvnV\r\nsXQlyfTEpVXqRNgWvZ9kWeQ+8YGivlI/gy4ePbfu6IgsYIZ8I4OtzlBzOBx3\r\ndl5OLzOeljG0O2BrwQfN/wzhCUUv/6AeTC5uAAUNHZ7RfL6IKVz4oPiouKAI\r\nfXGP+gEKSN62COhULs9YIUexS2ilBvw/LRs=\r\n=M9ia\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.45": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.2", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-presence": "0.1.3-rc.42", "@radix-ui/react-direction": "0.1.0-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-callback-ref": "0.1.1-rc.42", "@radix-ui/react-use-layout-effect": "0.1.1-rc.42"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "da6286709cdb6652149b9f16412bd1cf9b022d15", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.45.tgz", "fileCount": 8, "integrity": "sha512-5Q+YTyLtPprpxSVf5W3EVw2lGcTmbZEUZAT+NokMu4zxej7m+Y89M8/NiOPiqSzjoSN3A3KK16sE5S7Z/8ZHuA==", "signatures": [{"sig": "MEUCICqHmpiWsinKW7TjDL/GJ/yYcYpmV8xpTAmOcgTl3S3FAiEA/dwkKyXSyeNLQPSU1Uys3p97gCjcyjPIXE82Wpr9slk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixveFACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3LQ/+Jhh7ss+eAc5zuEBQHkmTppp/Ck3PTIwzscwTvUsem7NQGY7q\r\ndC5nM0+FMbrc90qaahFMjJFDmRZTo5lt1PPpXezspfAJiGESU2xl/wPy8LPy\r\nHLQV4w1m3n6SspBpySFG+bCEdM5O//orCf9tf0OGbvG1iaEz280sSeFwv1us\r\nKt1TGa3RLLyIUECOgjGXayVDjz/lEGNE7T63U6MoIKxfKhNm2AUPTAhvRmWg\r\nBCZVZ9fD0q81TWI4AoRvMDkxSbYsyx29yUHWIZ1fMMOOh8bPAuuQbylqB4kh\r\nz6KE/3jfuhL+vzUTECa9vU0PEE+d8Oqmb1hHOehNcj5v9bqG4xBUC/ry5hgS\r\niM7jNXbKwANKfH5+EKIwdftysOsP6kGOIOYHI06aht3LBxQfhWN1X2F12sA4\r\nhM0F3UWUbkZ6qpolToFEWLbE7OeiiLugEjtF1uvTlku2m+AH/XlU9DBZ6mve\r\nDb80QbdtAOzXq14MiixjU46HzJuaZRIKmBBm5jtv/O2YT/n0xTYk+GtLUGMz\r\nkhG8g+vgYMTtlb4awfMrZGxwosZUU4dB1HSsxnxC5VxsQUE9+OAbsmARyZVJ\r\nLvww/ZLu0uwXATICkisFFJm+ot1DRatlJ1rzlyBRS3jUGmym4MgD6l68q0J3\r\naKEz/yQCr4BZYWzYK7YJtbFUqJIRPhwZK5o=\r\n=IT+F\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.46": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.3", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-presence": "0.1.3-rc.43", "@radix-ui/react-direction": "0.1.0-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-callback-ref": "0.1.1-rc.43", "@radix-ui/react-use-layout-effect": "0.1.1-rc.43"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aaa41b6396de950f72ef478fafc0ffc2821d4edf", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.46.tgz", "fileCount": 8, "integrity": "sha512-D2NBzXN3kYeFuku6TQZnuWZw1M6LgGnR3T9pNMlQt8iipnP+LpLnfYQNlcOTjxHBOAKzsTYxXX0Iwz9wrooztA==", "signatures": [{"sig": "MEQCIGwJW0xMmVrVQZyfZp3bGFhxob93QvK7Jzoq8d/X/7e9AiBJnGuQ+UdeHPcZYiLP6sbJeS82jByAVgbL/vPZJ7ch/g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvsfACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqtqxAAm/UUOzp2wt+mSYrFSvEC+mfabOTaZTZ0Ph4bTDS4tIzh6Yy3\r\nCzNWqZ+iYwSV0JG3w6XXibsmMdRVqnrFnV5hkUMZlDBf+XrJR+hyUqTqdmNH\r\nmJKpcZhez/ZjXtmvgty6b3uFE7fb5IoDe9nKmPQJx7/Sjn58YGSPW30ILVfr\r\nvRCCcdVP6qf10Zcvbsa4UGXD8HxGEWqknVouWc6jDQkKCyMqohIexsGOvC9S\r\nKtmm0vXhh1Bbt8vMTXWmvOJVryObcYQkNR63FMQGZvrjVXK0bqU8mRABVl1r\r\n08XM2GMtCP/24YC9XMmhasJp6lQwVXPrUd7KgIGHf7y+a94rcd+tCkWnv80K\r\nAzdTbl529Xw7inj+R0+9bUyS1qiK0fbGDwPrAHfIjnP04O0x1A2HWw0svZoU\r\n8N2So0PtUmchkHoicjzsMcX2rlgggGlot3uGPloePOL3uTJ82JlcDXYB6OB8\r\nfWQFLyebUSBHF4uV41UGJYslvWtPHmVFJGwdPdMBLk2vX8V1gNzaeWaZL6He\r\ne2OjGCudCT+dy61v7X6PASF/90q94j/7c67QOVIRO03lHRkQh5dVe5xzw9P7\r\nl2Mio3PPpv6UQqmwpvusFMrD+SV9gM6wSUhK+i7pBEBSiQDAMufrU4bm6fcB\r\njUqUSt14x+qfk7dx7iciob8M0m3UPVojX7w=\r\n=pzZm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.47": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.4", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-presence": "0.1.3-rc.44", "@radix-ui/react-direction": "0.1.0-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-callback-ref": "0.1.1-rc.44", "@radix-ui/react-use-layout-effect": "0.1.1-rc.44"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9c20c3febdd553819daa1d2b255b382d1faa861a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.47.tgz", "fileCount": 8, "integrity": "sha512-kYRmxJaPaYNT6eY2RnL78WG6CNgruDVZPjlQEOP+Ky2WIxcniD6dTHLLsF0NhC9mdvf2/ggDWi4F9pAyU/4VjA==", "signatures": [{"sig": "MEQCIGUctrVDBIwy8i6UOcFa3XG41AfL0rMVpAKgUJXOKc6OAiBN5yoWBHRJ7TspKRHhBq+6P9Rhkm7Zyobq8Rt2tCfoeQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrrlxAAhA/byDZumBKzo+GPvgDtrs5MvoEnPjao507DULyPqvUO763a\r\n1gnIv2vz82caQxGUQvs9epDERs9CQG5pvsdRWjeU96I1fjcYlcoWmJ8zJdLW\r\nFvsjrlhHN8rHzBxG4Vgw2vgvpCJXE5jFkh1FnSRENmUgtSz+OQYgppIWggdm\r\nvsoz6OM2Rg8we+zPeC8s6+Edfsz/ywaLOCKu0sSQ/ILPlnkbQkBtMZFDxhpR\r\nAHPxQlQ06kjp4A60Tclt/YaJpP2aIWB0ZB8h9iJbLpf/LcYrnsyV7KD3BzUe\r\nSCfL7XiTjgJa9J2fZHIxbGingYI1dRq9bom/R6DZqyn4mKV1+oiGdhd+fNHZ\r\nAJRW/tqjarfN1FlnW9DD6Z3vdiFaBhQ9Md3ZLlog3AMhKOOWU/EyBg8v6h+2\r\nSzHL4PN8s7QFxbYkzxLatz9DX1mbv2mg0fLYxN4YEfW1RPCBH8C36u3l739T\r\n/YtnwL+Fps4wYqTbh23RkSWtPU5LQxdB1ts1YF5lAc5p/QyweMmqyk6c03bN\r\nVenVVIOuJij4YHmpmIHrMM9/TYxTy4mXv25Qomg9llBpXIO2fFVL6rkffZZ/\r\n6zAGxWBAj9h4Y0YOq0YWVwRiDRFShh+Ez1h9hWpp7jv7WoFDh6FK37od4iXV\r\ncUjB+PHP799Twz3LklU9bZevwwnp4yafuzI=\r\n=R5zG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.48": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.48", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.5", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-presence": "0.1.3-rc.45", "@radix-ui/react-direction": "0.1.0-rc.48", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-callback-ref": "0.1.1-rc.45", "@radix-ui/react-use-layout-effect": "0.1.1-rc.45"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9443ebfe9d519d5190b08f3f21e9d989307e89bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.48.tgz", "fileCount": 8, "integrity": "sha512-pJu6VcJVcpmco/39vN5zdD97IKStBlL8cs7Tr1dRH7QEJQcbGHZOn/Cjo6LTtPZ5M4LdTlP+7qfNhSHoYWNQCw==", "signatures": [{"sig": "MEUCICL3lgYzHFlQsZI3GKAy233EEr98DNWI0VkZuKeoHOHbAiEAssnD6EM0TGx+7jXHjiVC6vWmNmriurK4QSFCCe9QhxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wWYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqalRAAhNXXkItEircftM17MlGKw3Qx6mRnFoRAkmF/Sys4r/klrVoF\r\n6rFJ0RxdnHL5kkj1teuKcf39SeIiDdfE+1+iIBqKpEra6tU8HLNflkOEB9k0\r\n2X9/LI/ys6PiQynZYp537qltEUqEBSbiz4J2Jhl1hWwSF0l7Hmu7Ko7uWMXP\r\n1X8kI2Acft5pwyhCRNHKYQ1Lhxx7Ivlcz/mMZzu6LEPBcgBRBjQnZAH87pnw\r\n/yMLetKEXkMqS+4pyAlpRlm7qPPsoj6EoPk1XB5+ZURHVawfdIrkGC18bhp8\r\nQsR416VJLZgYFl0AsloRx24Fj/OzMYGbl4twZxJlCfuceVOslYxB2+/JupHb\r\nfA2VgqdkIxHzYe+PhW98GrGzcoHIhtc1DeLf1U9jWg33P1/2OG4lSPDhgcZR\r\nUFWPtK6TiAoEdfOUXnB4D3mJCvq2dZ2sJCuisbjyK5eyaXGcfSWSFNgUugSy\r\n3UcbSL2HLObDJ5s4vYOZhbwfpyB649uY2HJv0Du3FgxSy2juR4O307Sf+RJA\r\n/4v3L7js7T7jOF9bnGZExBLLBgnSByFQXaf1gFM/mpzSSzyox2LTBJfj6mkb\r\np10RVYCYOv1plhf95ZC7NnlYqDfUs+YRpaWr73BduVNzbJ3tcuhwId/0+P46\r\n8yec3R5Qe+Yc7ojJBRlbEqn2z8Vee/13uCo=\r\n=Yx9b\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.49": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.49", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.6", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-presence": "0.1.3-rc.46", "@radix-ui/react-direction": "0.1.0-rc.49", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-callback-ref": "0.1.1-rc.46", "@radix-ui/react-use-layout-effect": "0.1.1-rc.46"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e27bcac5a40f76168dd73f4c384b7c5cc3f6eac4", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.49.tgz", "fileCount": 8, "integrity": "sha512-aEkS/V/9daiA7XPPAy+RNmTW6Z4akUoy3F5bPYafq9TujyAh5fSGheCVNCkX25DPMYQOfH1VzJ/N4h9LHXAq5g==", "signatures": [{"sig": "MEQCIB08RR3VSx+UivtOl6/dpK20+3SKH5KZQQXVDLHtnKh7AiA77eyDP61Z1WVwMaFsQ4znWhbhbEbzCmodtE1Gjhj5mg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1976ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqknw//TBKkMEKwNxi8WOOKfnf7UGPlOntLI4O0Ybga1Wyq14PJbvOz\r\nalziIsa/8CV7xQ0jpV/pM9OXvSUgoaPxwxwwITA+XfEKJWUD8XRuVh3G2kOW\r\nGflU2scRjvEidPQ5nfqmr6d3ilWVK2xlY2a0IYC541KiGrZJ6xAxFsbTb+jc\r\ncDAj9wUxfrNzDr8WE4AZZ+HydB1pOtfQZtjPwp23/Q2iMkq9p96uRNIIMpl9\r\nXmxYTPEsRefJA81AbQidf9Qnvvzd7G4cGyFXJ5pZl7wlBYf9c+Blpsh8i8wK\r\nL4iWDe7Nw4AAKn5/TRgQ7dwfxl6zCH4+4U6K2/iIilqq+m5nR26aGVbNwFJw\r\n0sswXdTrKSXJ+LeFWEdyH8spEQqaDzn3NatZh4muDCRnuEj0KjmCY9qDsAeI\r\nG6ppLcS5NEu5x4Xb6dhrcVsxpFVAHXjp+ypRdqfXM6nBqjsCjcCIols20Jce\r\nhKISQo9wH3qIeOEpfPXNkQapuL1GE7qF+8ldFfCRqPgyuJUKwkbY+JkYJUma\r\nZA/U9fg7GIrmKSucK7Ogy43ArhbfT64RCiU6deDLASAupyv4PAYg/TlhlbIu\r\n4IQeIIjwiuukSZuM6zuYXOr0TDyi3enuN7SMyJlUo+FqsRKjC0wJSG+9yn0X\r\nxeZHcZY04LKdf81RtP+UTZtTur3Hr8Ovfbo=\r\n=vfKV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.50": {"name": "@radix-ui/react-scroll-area", "version": "0.1.5-rc.50", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "0.1.1-rc.7", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-presence": "0.1.3-rc.47", "@radix-ui/react-direction": "0.1.0-rc.50", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-callback-ref": "0.1.1-rc.47", "@radix-ui/react-use-layout-effect": "0.1.1-rc.47"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "058b480a338c6577411d97510d77edf9b83989c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.1.5-rc.50.tgz", "fileCount": 8, "integrity": "sha512-h8lrDtF9wvbdOfLMCnjBNY8iGor6CVEAeOBPs37JjFHkJ1y7Ei3XBjYjykwl8nZt1KmDg84/oYih+0Gn/EKWRA==", "signatures": [{"sig": "MEYCIQDlzmuaZNLq9+S7Hxmv4Jsv8j+X8T6O0Pk/rC3oUsL7sQIhANqXGeHg2JUUyd0o0VGiDr+sMrZAEC77aXXWa1mY4etX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249471, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CEtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqMRAAlO0nKKRNnV14T3NuxOfmTN5QnmO688f+TTOBsBuG1ZNha/su\r\nzgg7E8IEFuwl+vev/hDuU5jikLAoslDx3FGHDSXQ/qe0Lf6vP+1dkO6hZLrs\r\n7O1AtTefFgmF4Gt5FJ44t8buFMsEN6/vYF0eKEj6G2RN8zpUCjn4PtSP0zxw\r\nNF891xAHLMT75RmaAME981Gxe3ro2R39GFIywGr1A7QQ802V+JxdQgenwUNQ\r\ntsMzfNLgJ92GEAXwgOuP4tQjmqYTMhjlMvCZshxXYgDZSlkYZEPV5r0Zl2YQ\r\nuWbEyUJBrEpFTGCRenbCXkrziHo92ZJOkynGitcs30b85JhaWAn7tcxbNjRp\r\nQInR/heP3R+cyT/ZST5dp8hjm37Q3w30TsUOxIwJMZJbQcCloY5E6NovoHjk\r\ns87yCm49k19Rnxd6zgee05Z/4jrX93FwD77kohuoqpUppDXuunRiGDUCOgAG\r\n1NmlRr5NG0M4kFj4CbAxFfMOcyr6opHpM/n/G3GGcMRciyKPa0D777CYqYA3\r\nI4HPyU0sLoEuELULZMVEuxHDQephwAHIV5z7qTuILRgkwqnVcFLcuSF/lXv5\r\nJQKAkY4xoNuE+ujquO2DDKGHzXyBmtIrA7F3I7k6rrcbR9+VZ5S5lobsGqnv\r\no6kbPSywd6WbmJdTXmoFbpqeYaxmMm4DJ60=\r\n=Q1Q9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0-rc.1", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-presence": "1.0.0-rc.1", "@radix-ui/react-direction": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-callback-ref": "1.0.0-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0-rc.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e5df770b2529f26bad863921749eea2065ed0d5c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-iI6DemFARtTQGOn7lOmk2ea6fX61K2n+qyosHrcdI8fzzBvk9JWJ2OKJiGG7859vpMLYgvvAZ94qDgr6VvT3FQ==", "signatures": [{"sig": "MEQCIBhIf2PowT/lgtcBWqAWVSgpo8dxJB0qT7jTrnw9eQorAiAvfdWuApFokE9jlVEYMpu4WaMEl5kRUpyKy/Rmltq92Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249463, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2EvoACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrapQ//TJFTwPaEmPj5O7fBRY6EkaXARyRQe5UW9jAknv8WgH6IatZq\r\nRLfiDjBq2zrHsWEltlcaLsQN+7RFaocaMNWt5uTFrSmXPtt3rCp8mtBgBwvP\r\niaXnJn8i36OTPSqfiaNVIjncwTLtNQ2ireTX4choKT9l+KPRBtXRl8y0r14s\r\nMczYW4vYuZONUipel2amTWf4KXZo/8N/hPrg4V4Xww/rhF65UCJznYNnCbB1\r\nZEcSejOta9OJ1w5bfGL6ReFOC5RY+Sj4IdZmP5J+V11OUv+PR+sjrOj4XTBg\r\nmlIfoBIsWJumV03d0YzcCAMeC2G4vOC1jINESAMGjKhWxS79sbJ+DP2T7ssp\r\n7JtUeOeuSzJw/B7JQlpLbyXM9j5jW51UxXOXh4svzV0xL3oQoJlExYuxukSW\r\n75zuHlf/2r3PPHYzIKQpuAe0TcGiDP2lKFVMEHhDzDoy8QjDqmWIHKGLxoLR\r\nPZHnsuMyU5n2F30VGEu81MydVbPkKXX9hwJRPsWCSu9rS9Zosai6ZytvYhE7\r\nKgaDS7QDHMGq21rnJ0eQ0RnZ7RB6wb7dp/UA9rvej2CHS+8yqi0A9Elt9vw+\r\nACl2WOVKJ4kw8JlGP6v+DI4SZNqLazKXz2iPC+GYtpG30wAzyBkn2tzm5U/U\r\nR0hicui1yAVgun5wXAKi3iNPoZzkBNy8gjQ=\r\n=np03\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-scroll-area", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "10d0262a52266af528798f36947145f7e3a3a52c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-3SNFukAjS5remgtpAVR9m3Zgo23ZojBZ8V3TCyR3A+56x2mtVqKlPV4+e8rScZUFMuvtbjIdQCmsJBFBazKZig==", "signatures": [{"sig": "MEYCIQD19eFoynxTbI8K048J6mMc4dS+gTzQ6zogPThUee6hqQIhAMTzs2Yl1p2cUA13t6TyIAihOU8TASFe7wWDMDCAi7pl", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr52Q/6AsJ3DOW8NTg7q0Fj+2QNpGrQ6B4WFbZbnX7+ky8p395h3Cbh\r\nM+HeazttgizjlOxk9m8ufDNY4LhSR7jlfGB5IPvBTaMN5WO3C0LmhL3D38Rh\r\n96kPDJUK/98aH0/d76TniLoCO0m17rgZVA7+gH2xglJzgWVsNzdcfuoyGkj/\r\nV4tJ717/yQooTIkvHAy7DqN3NrCG1tmzGVVoCGltFo/3iCLbE89cF/u7i5L9\r\n8FTacGXyZFmsb2D9a6BKay3hM6HdFlIZujbX1EKfbTLOkviCAEibh0YzZf8K\r\nbSFcvwF11CQJHYjBIaXkG2STKzzWq2w9kdLTP1YouhsuM2oUE42HDjeyWuYa\r\nWuzqxKYvnon7ELeQ49qJI64XBIIvH/49UA1UTTSqR+NJ1PMpkueonirbOCgX\r\nomBfiR3C9aaWFoSeWw1HFTojLT6ajlkqXqLnGz2KRPjHW8EYoFDAyyq27fAw\r\nsembK3B3/q0n6w2kQLE0vPdyF471BcRTWzivg/20Teh9lb1z6NBga8bg+VXZ\r\n1y0NSJJyfV0re2ACF68gm8bMrfa1JZL+d0QqArNqJHv3Eqt7C3DsYbpeui/E\r\nwZQp903tiKM2J2BPD6n/ac5qBe4T608NZHIouaNZ4VQzY82xtvv2XBiJMaXb\r\nOCt+sdcqZ74I8A1PGMeBEyjwKJ3y9/+Ogg4=\r\n=r+J3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1e97316e938de31c1bf44644aa574dcd745dba38", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4x+fC6i4COSwbzcEPaU3Lw9Pfpg98yKQ7Bh4dL6d7IQIAERJL84VKf1qDNs/pF7Wmme+bRIdwgEaokLhUCDptg==", "signatures": [{"sig": "MEQCID0Y007MGRgGW1fxxpc995H1aMRSBGVH/3sQ25MW2HMNAiBlkvrF6DqWS67GVqjDvxKf1ZIIetunRJn4CYXgYMoEkg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbtUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptAw/9EQrbm4DsY29cITcR1oKnCS0CjR7dm/2qeGjabWViAAg219zF\r\nQYuwicHI6dspUM4bVgVazsBPUrWc546RcE1dVA+IOfqNIj7SeKSDgV6J1345\r\nj2XvRlU8FqldPc7p4y6yca55DsVuqJNdct8g+/yrHa9I6pWQKqyDvIS4BtoJ\r\njfM0VY2XpCSd8/Oi2Tkef162ffQ95Zx1rNzwXVi0s81i9OGLbFgNh2cXU7tW\r\njMhSv5drF9dTGuIlBo5xJFbJWY96dC/KFlz8bShoCQZvRrEdoin3H/RlcSXq\r\nNF7fluGZ3fi/JF7quq2oCpX/j8ZlM04nepOhR/tpa66ZREEjXrYvjstS3vEA\r\n9RSn3WYxO93bh6NPdG0vEuTJGGLmpg06dkDOMPilPE1dfb1/5NVXDZk5UcKf\r\n55YXf9Zb+gPuD2rqxs8abHfz8DtdFn83bAWBLD1Hh0/+jBy+aC3TCJwuOYMg\r\nnIWJwhoM5SGz6zOYCMC8WicdgpmPuc+NmhHCwz3q82woh1da18sZLgj1HIC8\r\nPSH9nZfdO+WoZ1QdywPw8zhkNAQ9sEH0BwgPyiJxnMcq1lBHaOETi7EmRJ+w\r\nElsQZmikEqq6ZbW/QrX+PwQ+kicpgaFvGOkpK6WKUyFWzPIQqmCcImpXLRD8\r\nDp+GtqzW11E6IBZMNJHThgCE/2oss1dhk/0=\r\n=LHYb\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "96a5a57231a508558bbb6b38bc12e116b9b54375", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-FMq/3KijXCLyOiYwMKyOjxoBWrmTJHsJ+O5tf8DSqrz9vNKFesPAf0z7ol+1I5Af3lJFSkZv/w4oskT9hnQouw==", "signatures": [{"sig": "MEUCIQD7RP/Pp5dyBN8DDYIGVG4WeeP5Lef1g8P5+Z31299jTAIgRP7sQoJBFSUchadIdU3/8btLOAGsJnMXIB+CjHnrsa4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKzlACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqgtA/+L5fLs9LzkBykfplhQD4T9wC8NV2kSitzpwZ/6N7Clz4YSDuv\r\n3eQycL8puGwdFAxklDBSSDyUAMNCi5Bae3ANz+N8nRTLH1fPEkQrPKLAEST0\r\nBT3D6kJ/+a+VhrhTBRhMOvHGFyqec7RzkyHFkphE5K2k5PoqJORRPVpIDkWt\r\nRID4ZWlfVTub5ifnny23HG6Rc445kdB6Wx0KK3PsHF2qg+qnv3V33JnKPt8H\r\nyRYXvakkYg+Xgk3RVrIR3RRvVSUArIjZHETExhWoBM/nuDdScGlJcNlT3XcJ\r\nff4rMkR/z+akqYK0GoVMwfXQg1BolmL35jffoBjb8Gi76FUsZSRGK8VQ4qNp\r\n+tWxWY7mA82T4FF0ctAdLHKQWvbBsfE/qEMDaOdgTAW2OUx1uduQPv0LafXG\r\n3kUdJJA/Pim2c4yhWBBkN8umSC+FDq/PfXo5sbssZwrxvWLQc8j/HhVJAOKQ\r\nynMGDaVXKKQrDYQRG/nrLYZELHuDcNZMnx+D+yGm8uRyMQ+s3Um/2hkBo13z\r\nkZrc/TKWrSZeUyaYjQ6eUzKpe+h9gOGuwsXAAgJabUhgUa2J2YkGH+DEqzXL\r\nKiJDuroqgrjavlm04aw8G2RSz3jSahQ790+FSYQAj5tZHGnZRECrA1j1tm4V\r\nvlT3Q//jYdw0scMq2N9GE2+tMZFL/YShlgc=\r\n=7wOC\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5e27cc66d76c48ce45250f01aab5398f842bf4f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-qAWI9yhL75l1jxvwD/DlPTD0cq+Yq1WQfYmAB1IqTQd0fBUo66cFKuwabIb2MVyRwfsldH1yKkv/Odiwk5ZGsw==", "signatures": [{"sig": "MEUCIQCcf/2U3dV3xLzrssusuAGur0rUFm4x6p5GWCO8CchnpAIgX9SYVmWacTvftC8Cz8kffkRUoatwhsLdIT/oq3MKSGw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdceACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqCiA/+IUFPs/5UrFyYg9CjFYLCllfiyrVkEHItHZXSRehgBnbcB0J/\r\n739eXiYFvHGw3HtHG4+/f/8Sjj++dZlbHIKITqTttBG164USqi0i3GdiMJIZ\r\nD7D6QHU4VUkNGUtJFIjU4ycHqMaWQ1q0CuJItW1Yym+pSwQ7yFMzcTtrl/Ah\r\nmifaGMoXJADrX66Eqj1vYdPIfQ0bKI88CZ8RgbVs561+IktplnwFObceSMAB\r\n0WL+H3H1JBZPwgNTIO7lbji3muE37lFn7az1xy7Q9xNEnF476cO05SM/xoOe\r\nXwHHs2XQiFe9AOQ/b3jW2+YFs2BjkXOoxIBeUUDMgQKhpRVs9Rl1yYKCUrlu\r\nSsTI3yWSlRZUnaTZbFwAsY+GnkUPGVRY0N7JtyM7s1klm7njdCGE7EfIaesB\r\nmcIFvwkxuE/TU7Ch+Mwn/+ASolEWnCvg/OY6sh2lKXEOv/ZmV1gAr9LRiQYV\r\nXYu+129XdoJx1Am6HEgKZHcLDE1GpLUFmdnvbc9FiWNypFdQgtowNA7s/zl6\r\nfzG76oyQjhU/9QRs4rNNOooRUG+9mu2jJMUSWmhPuI950V3Ww7x4OeqK4cZB\r\nUtkCkLb5S+bzSjAZBNbr1jS+aiWaktjHKbLMr07L3E1DA8Z5kAxzwamQwTbt\r\nNVjWySMIKp6min16BJB091xWp3BFk9fmCN0=\r\n=psn3\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a80853cfa66b01976c4cd5e1032f2b8312cfb0fd", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-OZPIcKd27031XYgHlxjgTQ5u2AkooPBaCNHDWsdeuX3U2EPx2Ko2DkbNNVcNgaovqLFl3Rpeo6VECQeTUp1RmQ==", "signatures": [{"sig": "MEUCIG8lAXGqMOuiRCtUk3bglWRQG4wqBVu1gZ1Lg2wSdDQeAiEAw3fxvNfyHOwMg9wismLCoZdPQGBgVAhrxU/2odBQBQ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfBZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwHw/7BIQr38NTA20jJy1UiFP8p82oPLLg+XO7vUPiEdZCcBWWR1nc\r\nDG67b+QKP6M5JXFFaZ7LYNNk2U90X54Ie7uZF+Z4dzIBI8hhObv93gSiZC4P\r\nH9HR3u/0L9AB1fEUWhfNy9hyUZZL8gDCl6pPDZFKEh4OY+AG8ee9gO/4l9eJ\r\nl7ciFPqk+EnXASkZUd7yjmoMiG/VypIB2jP7vweNYufPlj697OCwA3HOT0Dk\r\n+fhlGEeFAxOoAK5jaMHWStn+x/W1tDu/huGgYDYVZUXn/+GBG9M+Dz/jBITI\r\nQRiAEDD8qUwcRmZd6j1D8exhuP0tD8j1qqD9ahmrvVTGI2vJoPiWX2/ljjiM\r\nfDK8XD/xCaI4BCJo8seevgjqcjJMK5Urq4KUIneVUxB9aGTQFEa0RJE9lxb8\r\ncgrJEEKe/ce7tdL4PZ4A/zBCfIqOANoTSO9eGQ42gAoTkWmi2SWuwFtNzN7B\r\nwyKKE1QQNFEPun29uzVd/S+tlEKVOz6V7k5Mzs5K2loy642vPOhJHr7ceJME\r\nkAWjcElsV0zObUeyVNwbDIYqyFa8ea5APQG0QFooBK3VhQUeIdiLfYlIrAlX\r\npVDvQHI2wPkqi6LvhY8YT5O0vjX5OrBAGsHRh5tW+hCzBUkUutUJiYHLoQS9\r\nFLtrKiyyBjJ96U6NX21oh9TYp5SfnW54Xuo=\r\n=kLTy\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "45ef0b117c1c074d3aa0c7dff17bee493f56254a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-8GeY94YvILt/ytH2lUekmj6Sx9nYgCN5wCmGlVhMpUm+GWFRYRAPkzGAcSFAO6TnC++632Z98Gia6hNODAfLqw==", "signatures": [{"sig": "MEUCIQCT9GErNLPiQjNaH0S0oyt7ZK3Yiuoh3ZOQDeN77deTogIgZgtXKkxaIMA40kVY42zgW8V3oKtqVGRjlvetjkFgsAQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr2fACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqJKg//ft9NWTvbz1euOaEugHqlTpnduf9gR/RTqlkOcxwgNzV4Dgi6\r\nDL+uaIro75w13L24TJhRnzerK4nA+YS3fd19nm6NibKrVl0tMRqkOc/sQHyM\r\nX/8GEktaaebKMapOWmOjsgkHWUYIK1hj4ev/dsFE7nx0gHV3LO4ECn9FbCmY\r\n0rp7K/foL3NDd7R8cO7VqqQnfRHaBvgY0FJaTuG0zrnnIMq/fLTEWoIjtQs3\r\n4/BGD+fvQ3AgkHHyS0AGmsAbvT+2T5lBYYdIC8HWFSmW/mVMx0Fl1ATgfLrT\r\nea2WFcDWehJ0ASNdTMPiNGU3BmpEfGGqmZ6oCZXSAR4bfC9WbS3FbPHjbgOH\r\nxxdC4AEMWB/Ke3gRQ36Fp/gAj94gdZFXfwbg/ecNpQINtZey3nRa5R+wgREA\r\nvcDSb/jUJ/fP8vjY3F9KSOdGw7hfOu7P1p7hr5IU31HmR92i/vIVa4cKuQnz\r\nbLgaeDWJvPi4cUaYawFl4h4Xgf6AQGSs8UcxJvXcYo/JwAiY7O58fb189m4q\r\n8B6iqNr0Aozv7O10aMmTYtEB9x72UMZJ8fl+zNvzA8b97B3JiDOAoLr+6jmo\r\njxFyX+LcLodBBJ5qxN299cpj4QvXIhXDGYfn5vX4lnHegGXYirWXkTs+o3uP\r\n1tPQW2XlPUC2rliwqHkdGPhbymZ/gj9jVlY=\r\n=vGqD\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cbc02e92e289f1a9e7d13acd8ca5fd4bdd150bad", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-ob+XR+iKCk3+G9ZJGMXt7U0asAP9DwiGHyJCfwXHSVqli0mgCx9a1rrWSVZeR/8n0uF9VhnL7rXjSfqsOO+XeA==", "signatures": [{"sig": "MEUCID++Vv09Wr1CzZhmgaY307YcLCdyWkSbAHBFuTJ1/6aaAiEA3A4fSwr7EpdyOaUKuloh/Ya2UB23hdRj9xwC41EdFRg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwPqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpi5A/9Gxa16qd7A/lQzhR6bOQ6QupRjhkZHXZGAIBK29X8awQmdi1v\r\nl9/f4orcft3UegDR7+2vsaixFNS9muQBXSeQjc9QPF8vCzU4xZQ3+7Bvrk4l\r\nXuC3gefmtn+HuCiDWvuq62FRhWFhpb/Za/v+9mRjdPmdVT3NACcZ6nkRCsFj\r\n/7x3d0hN+lJzAGxZ+DnqtYOyTxaGmdpTVSyMKneBO8FOvdsWC9tuBIo8T1fO\r\nkrwkoDpdkd/f//7GoZkTDBicwYLrMwHSR1EekgPtZj3IXMUXrHngFPl0dHcb\r\nJVuPSjNPvTndd3CkWL0u92GSYOQRDRscHBEOcNbTLGgHlsveogIPxX6pp9rp\r\nCiCUZ/ygs3w3OrZXJ8bKg+ULMGjyQ4bld8ZRHrrCG7jp8MJ88ae/9lAmF3tM\r\nesCXmH8bDX+ISRWXVP53SYbGI4J+uDZUCbuRsIFAF0cZU6tPuw6PopeXuK/H\r\nJWv6a0wXilG/m/lYlKNnFgGPQoDnSUsSkZLRNyoV6Fw4CJQWv/UM+Azgy8I4\r\n/cMFVSRvVfhG2QgbUlkImoQgzi/XRN10Q6zjnYkh0ISXACT4EADsDVAZQg9z\r\nCvHns9EWk9EJznd1Q9tGrwseRgPhx5b5FrCVo838DTc5a6tN9nwfv5limzYa\r\neMYDhOBzi+8PfhziVFaINLbAQ9yyGNLWTnE=\r\n=/WNo\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fa433dbabd673d5b0ad817c90b71dd04feab5c6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-HYcU6EqUW1k1Xd/PxFo1skEQt5JrzOHaYGTB59wAZcfUEIKxkJ4EvHcIAhjAsQBZ9sv+A/VCZbcgM9KB5y3bJA==", "signatures": [{"sig": "MEUCIQD1Rft4Wp26zB/aLxWLz6qR4L+8Nog/zMDgBTGT2Zx1zgIgMJIv+SDsMKg2XlZSDcG2Oq4fPAOcLdO5+w5/jNEOUAo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwxYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmor4w/+JmOvZoWDoZRUz9M1W9O3ycN3ChF0977K/ne5TqByr08FUtWW\r\nMbOt4UAagUTyfsMY7TkPmclEQUIV6+cNMQzDbg2m1JgqTpFXiH3S6EJsam2/\r\nm46xVGoCs5/DxsRhdwMVYqQErK3lFKx7iG9upTNLY7Y+3k2lP+bwX+6oYSda\r\nPAjsd1j7oRY7qklL6x9oI85Yas8bGWr8qE6dp2jxRb6tCmfCYvumXeUk4Ojy\r\ndpyd6/bVJiD47FlEPuVFHl0Sel626eI99IFeO7rJa/deA3jNMH12nE2AZ7j6\r\n/tFRRpupPJuaQyYTyceqZgabEjTgabRY2X3AVuLyikb4sn952aUOhVq2Mtfs\r\naQDs7mWsm9kbAlLFcOcuiNix9yqt4JgGV+p+x07n31swCTiUJ3TRZj6+XTez\r\ni+3doTYNUv/5//+whg5+llxRwDmDRqtFiWu/p32qZM9JmJdkech6HgntQ21l\r\nbK0cp195bly1h0aJCpdjVpT3YASdaiRvOLaZA42pn78+ufXPqZmUig5WCS0u\r\n8BHviUcc6U3s8rVI4fbYPf3MJ3OVbp4JZLfM/ihl7V1DiiopFf1Y3drNVv44\r\n4P/IguMDa1xVD4WCzyttCt/TfTeQ+c6nlRuKQAPM5W+cfeZkfo84kGQad8Z6\r\nhVn+JR2IOVSKvEnJo4eKliPL8BK4y/W9uEc=\r\n=3QO8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "383a5a7603462633b6498d7354a70ee640bc9b6f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-0mU0vj4V1bfMxCPFGX5vExA9cDCpjtqKaIdyRyqSRUeWGw2xr3gCXCePikoruqo73LDs0u6MkkyUNaGO4PQYlQ==", "signatures": [{"sig": "MEUCIDRYjofdcp2XrLrvHitmj3kUAEQUeh2iIRmfSpk3Y3yuAiEAus6iVBmg5JILR8d0olBShAqpOjBz8tW+u+rN4bUIUZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqOfw//WfwMMqlssxFekjKMfOkk1Vn21Md3vq1kxzaVfWL4p0PTXTGw\r\nRkRyvb9kxvgLJOe+uAqQ/WZd8ntdQUVHGoVyQHA0zm/PMEmXGB67x6DoPe9M\r\n3OYGs4gM+z04MoXH/SBtiQ/23xxJsx36+E4Eo677aInjff3fkl4AnsD5IvjF\r\nXY/GPrfQ9F6uN1v69BZawR5nu7T8Php5OQBRP+F4ZsoMYQUsRpzWsltscYHg\r\nTveujnrf6XHHnx46iZHpfFCC98Mu5ID+eFku7eoTR1K90jfJ2/1mJBlQbkXP\r\nfQtYdYtvDq/sWEm8K0DSa2amaglV+T6lzpE7xnEATXJ4S4ckxPHRc8jURCxS\r\n/pyvzOD7aj0zURURpX5W8SsZNtNzqKKUNZAztv5hBh4lOXDaIgLpOmBCvdrj\r\nXLKTb3S7EuS7+JGzEpQhbFmGLJF8UY1RIUTFNf87g/sHCijG7zlc5ZcNzp/B\r\nWJ4hcmRsWp6l78Yv3VaMO0k5V3W9QFN3sL+FYkjEgi6y7CYrrpnPW0VnRxDE\r\nw2fwdNNEv/MKP/zqIn3g60nJjJIASWfm7qkLDGk5EOjxwaBaaem73gPONElz\r\nlJhXyEioG03SuEWZjolYzTLm7J5XKO8bgHv68dnpucch33c7spmgwgpWUPrw\r\n3F9P3syTYVo9PDkf65jOCdIqe51j3yBW1bE=\r\n=sS7B\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "737ce64d966790c2cb56f56e5d9767540a320a31", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-2tDVdl3T/6o+/QvVzR/SfXZybKnvGmM1mAuRpCEFAS4e5AQGmmeQIgKdkoPYG6v85a0iWN+8pmWSp2X+C0Hphw==", "signatures": [{"sig": "MEUCIHXsDfXQYuu7MtBxoc1IPaV19CilKIT+zNe5Y2chCPd/AiEAtzl9z8Y98O3YRVTgeUSV1k0NsHVqH/xyZ1w3tnayw+E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249423, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/bbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqeHQ//T4OR9dYS/jO4bBPeQe6rECNI6mqYCBIcy/iTsHv/nlXRIuWf\r\nmA6jycWmnD7OypjzYGO4lfF8LM5Q7KyvjCgZURQGwjuYJpZFyAxjpkinTPHB\r\njzICZh8SImg9UEBoZiuKv/AC4VzNcQGoWgatxbVV/kpSzMKb5uNHqnSACKm9\r\n4+DVWPlbDPGZ2T+A5NNbicsXKdR/XeQWvcohuRac52Sjjpkr4B79aCQCGDGc\r\n4vLEorI/GqrhRmkNbCVkH4pY7AFOEsZ2ShmcImTJY7DbY2KWDemGzpPmk4F1\r\nWNRgWsyOo6LRznIWk8Z/n4JnpmEcWqlXdpP4G+NqJ28o7BaSW2sTeaSrhn91\r\nybFtmpyGbEkXAx+1e7yS3Ppfmg6QB38wkgc1kb1Bhl+ZwMKKZ4/q5LLBv0EJ\r\nT85qCrnPVtOO57otwAmxp1gjBl8rJcSiFz6as/ZS7JHI4wpOJvJ9a2aBkwrQ\r\nldlDeK/dQg420GZCDaePbej9I01Hrdy4z1SdV3LT256J23bBw/Mi4mUqyYaX\r\n2um5Wdy3qOGxQvnkRvLpjxyM+Hy10Vato0r8JMXPpV+EXk86+UgxQleUS4nr\r\n0VGnKK3Zyf9MqZDsjjO7vA3MnskNW4Kp8z3L/B+w5XzhBNTs18kc9OV47hp5\r\nzDxUoNtL5Ludj8tzMQrIRboIMTwgSbKa/x4=\r\n=zVmu\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7710960e3ade538d0072a6f246dd0110c7b29d42", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-lTP3j6JyPmnRHP+srO3vMchRMHeFyjl7Q7aqc8JNdAbLaEnV38uzu8ATEh1sYAjaFg/NG0V8Mv5e07nbPsOAjQ==", "signatures": [{"sig": "MEQCIENWVsd6LaBPUcwzBE0rZTmotY0alywnjDk51NzDZEZ5AiAP/X1BFvQthC2OvEd/PmLTFwRllRXvTsmOKEk0Cr3zSQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRACHACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpaEw/9GPe01VbMMFqFqHhvXQ9JkaQiGI0W6PYeD466I699srM60qbC\r\n5muR0rUTzU9xbGfWSmqibD1hpPiBdndm2j6WcIg06aljOy5LScGtw8bDSEkd\r\nwquE3XBW+i5u3KPVt9jeJGJBTpIaNpU4ZsRBgw4IYA9JDnBJw8d8NmcQx77z\r\nUOp4KzLfDWQ5JKAbfM/+bZnv+AFaOgGP7L21pWqcfEmlrNq8clOOYJe2vjmq\r\ndYDPxZ2vk8hVG89Nb1xihVIC42z51UzwsDXVFe++O0ruB35KCn6Tt+RUFOXa\r\nKqc6zBC9xh0c8XxKm3JJoVZrn8fYdQJLxBw65GMFvIUOVMX6RLgL8mtPTfpt\r\nu09LPNybBzbLwNvJIWyDc+b4UfuXpdOf+qRTxDzyjEU4JCiAQxDLNWmDTBGP\r\nfNntSTAaoPI6dxqtG1NiX93fGcpaTiME2ufShEfFN3Ph45CavxPS13t0OuDf\r\np6QVj+ojOpUzMXhP1Yw5lRPWOB2vd+OaJDVbPyklgihLltcS1U0ds3gAM/gC\r\ne+F4SiiKb0p6najL3e14zvO7eYFlOpUBtSQEQGvJn9mgRWRBZ8CxwOYRc5Na\r\nZ1LWfirul87PwPVNDuFa65Ac/OWbZW4V4SjygkS4yAjAN0cmttmux1TNaZJG\r\ngUu2gfkeaqOKS0SwYi5o7zEky+Wf2j4B2m0=\r\n=YMTh\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ca8caceeea4f1a85b152f0e10e660d7f00f8128b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-iIayVlnd9mHaMVnQwNVjLZI7QIMm4gILfzxKDm/ZLZRR+2No8ek5noU7T5b8o26gTAtwxPzitMsZya83D2tILA==", "signatures": [{"sig": "MEQCIBSGpIeRtp8YVjhhYTbUiAgHiEOc0WxwLOYnOuIIkoSCAiBULGTQS2COdLQ/Lgaqm3hGk18VhowuU6BBgVlGYBRVAA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249425, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqAGhAAl8tmunsk0sYtNkWKAjKina5LAe4XUxPqUq9GAMVIBkOy3Vkn\r\nZFXgWd4jwWeZINYCDeFGqYyGe1kbYP5alael3xPyovqnWkzMZc1bEYxMBxQl\r\nD1IyLG2wQrb6eV4G1kJZ5IVqIXFWGovJaRe0xWd4fegznEYas14rVP40n896\r\nYtx/+YPm7CINDfT6VmD8jO34K5+x7kLUmzG8MMw6E3xfvmf9VL4MvqsgTVSZ\r\nIpxldCmTHSMeA2RONSgLNqmhEN8E98TWEgSi6br/O5QbyouwgDPDpzfDqVYD\r\nd42tSEtB6OI9TKaSJmLh6TReWK4/VfwX/QzfTkT58LnCO2I2bsTC+IjHBrVB\r\niPxQIKoG/8d/36jv3CgVOSKIt7r02ROPS07nDZAKRqyLN9fovVEiswWuw3gB\r\nsmfdP8AXj2cNNIjV5xxVr7LVNgDjUQUjTg9rVcU36I6kVQ/u7dsNrThwI9Zk\r\nlvBWmht0SodPxUtiBM3j7nIQC+C/Pno/i8bRKN2zUtAsIp88i3YxARgkaREF\r\nOKzHIrXCEOjuXiN5bFfnQYvVTFfW5sSFEpdBCRxa8oUZcm1IOCeCReEvpjho\r\nPAKWqZwrP7aMe8vGSnQVmRCQ/whj1To6ZN/MwHzbHiLdEC+RBlptQyv9SNC+\r\nEh5ox311U/L2oiF4uFkOeVua7adW82hn7nI=\r\n=n9Xx\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b9ec08259e3de36433da1c3868ceaebe3513fb43", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-2LNysDfczMaLLpj+qq4bH9I4NCr6Xr6rgJs3A9RQyHklqjFxKrWwlLOUV3fJ+HunKWdQ/TVS5o0BzUB6bhWf8Q==", "signatures": [{"sig": "MEUCIQD9ERNKDlreFoQisiHCztiYcWJJGKeSf/2/cWXC8rZS7QIganKkefNtHIIRa5muf1j89De611jyHQfuEZPKpWnc4vw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVMeACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoS3BAAhtETMf/rBKrEadhFeJ9ziFRRsZIVHnUjCv/KoVwW7P+/WEu2\r\nRRDXU4Vx5HSWa01mXbV2auXy/DxcCIWi0t2PPN7LbqWxmNQDqh/Hryx/Hg3Q\r\nnUN8u2MMtmFLR5DFLqrjf3ZXnzdvTOuKewKcDcePes7yGwVw97jo8OlE2NRM\r\ncVHYQkc+Oza8bkCXrTbCcTk66vpMgsbTiBEX1jkqBrtIS7O7GbPYlfOZTbZN\r\nXx6ebcN3ZKZgo0Acwg212oKnQaiKTEptGHofYfIIK5RigRsVaOIcfOUa6Zs7\r\nn/bo3XxrVKFgY1+qXZFNUN4F/e9+YSIPhXJwx8JTVMvgIivdkwm56mFgOfw7\r\nZ2fJ5ucpgZISgSbw2rDYl+lHrUPs83yEBmCmAfBfFmL5ELpmhfxp6xsn/J1l\r\n4J+S7wB+JQT9SxG8jmo48FRPhUDkdcOBXzHn14EmL4RUvfirvOQe3LNZJsBE\r\nflHLatcY/qIFIctp1aBiKBiIjd8/yjXyc/d+NG3ed+uGNjZtgMrXwqmZgsyQ\r\nsDOmligpfbtsumXtTrq4ovFZaNtDkLNAgQ2CIYfpq0NdrpjbBvzSX6ztdu0p\r\ni6+rWkSa9RRgaM6LrV4DIsqO2nzdoskDLwDm7UqZCOakjFtyy+5DZzSQV5O9\r\nrtlxC62iuyXErP8yIhoDv3d7ISV9VChm9DM=\r\n=x+Be\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fdf33f3eee5bd94f9baf10abaff2a87538a3ce52", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-HV56D5xyhuEcjQ+4GDBqEnLnN8Vo2+yf2dqMH9zB8Fn5IumplldSR4Y/hE/6YuFTf5FfIEOTUS7rV+4O5Hubpw==", "signatures": [{"sig": "MEQCIFl2iRVrO2mLWRREWZ9mrvzkotNyDoWpZ1VUNLGJp5I/AiAxTy77N4wv+y6QAFGeqG6SSoCVaOa7vSs4CCd6XDnWGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrFOQ/+JdpTvieRAacmauUi7hQLLEmSLMfuYxp0XXicCz1Pqg+guFxO\r\nDymozF2uXIry6+PeMNaVcyQn/0UNeUChe9iqr2aAmcFyUv8GeTABbp6zYH9P\r\nwR6mmzRAxH183cfNYQDq5FtQszNEyx75CRlm3Q4GfPkgW81GSTdaVj7ng5mD\r\nn+w5u47YpogCfceGFTp/fkyELvqOZU+JsqmxYeWYP4THhEifkLskbiUO0UMd\r\nTGH+Jn/CWggDsBjt7QdNesa25e/L7GtJ+ohbF9sk4mlxGuyPaojIuxDU0YOR\r\nMzxtZJwYIbOJS9hxymHbLGok5oiY7V5Bgjzq1BQn8TviKCzacIWh5ww33RA/\r\nPFdFTSsIg3dRIOpX3zZ7RmBXANCQ9kB8mBjPVEkvF8xF4GL+jA5oL/PszN7h\r\nnAtesQvEGTjYUlWr+K1+ITQ4+CboXElMPtuss7qoRPbKSm7CzCykGDtPEZbV\r\n1E/4oIrt82lkVUpulyoMZoKk4bfgk1UyLC+O/GFuek+phw6CLTWFZIGyboqH\r\ng6GTuLRQoTsp2DOV6x9sU5/swCevMhl0GWEHEm7NC0USSZFlXHRipF9EJ7By\r\neDy9Vjiki36xrAcV54ZvmQnLSYhFfWNTjWB7jhrQh833RpY+iuUnLFhefgR2\r\nyjqTYYwDUC/Najukct9+4dP9xnIaAIVjIzI=\r\n=cn14\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "058329ec287fecec6d5de782770bb2b06859180e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-GjmgieoXOtDxO1yiWVQVwATwj4cbaQuv18a0KIUhQkg//imvU8p9nTfYhJ3a4N6TIXgRAH7yQP4N1e7aRvvaGg==", "signatures": [{"sig": "MEYCIQD+u6Z2/vfrGbjB6FK0RMdOxHfbTY8GOMmSXHVOZvljTgIhAI1Fh5L0+G2lf1yMPBdnzhFAgHd2Q8cJ1gv+xg5jPegg", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqxSACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpvtRAAhP94BmTUTC1X2vFQushjRzGHsPnAdcdD4iZDGWqaRT/GmUcG\r\nO8XeFYEoXMROFdiaW1AsbkMXKaBUNCjUq+Pfo4lnTXjlgRy9ja3pK+EF447l\r\nuj/ggCHS43qM2fvo9SC6GmhZEDm+wAYT1E+v4pN4kiW0YoEp4s/4+ALCjcO1\r\nZqWaAVUDUjMNYGrscMwUgtmxlTSnk1lr+90ZwcrsO/AGHZpj5dbbbkgM3a7R\r\nzDNA11l6zILeTcUshciDGjpZMM18VVt2o0GhEfW4sSZB18hw/6ksL7/bHwbo\r\n8x3R6JltJjtausjisrxmvkaM196uw0iRa2krjJM7hRLXcbil9zQIYv9bjRpo\r\n4yvh9070NG20pO6sgGOeT5g4V0M/qMgC33fG2vnhVHLQYtdOlUN4Tt51PcTH\r\nCj45yB/tPlogCjr1LY/Y7KqD1idCK7ZYrUIKFlLOMKPAtE+eZdGwEtgpDlpR\r\nT62z1OksEA+JnJCF5dGpcUgIKKQc6uzbp1+0eW6twAZo3eYEhWZfBtpToo2Q\r\nHp2BpUVWaB9MHIjw2e1TWMonl8SZEBjlv7zKWrDC4D7FvB/pyxD56kMeY6jA\r\nbJdJeH+WCmhCwtG4JiPx8PIZwFe4ojpMGckkcFjAk0UejXRu6DIWySbuWpcj\r\nNJPucRUeNj+RMnqRVHrAxaSlI6tQpquPsnA=\r\n=nQUG\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c658093ab127b2ca53e1a8966b405bdf5a6515c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-Ir4ZUpUo2gaGyTMVq07QiIL8ZlPiuC5eP7Tm8RVOAtAayM5IImGmY0p/95xcXBdAUNVxaKF6CosBMHDy9TVO0g==", "signatures": [{"sig": "MEYCIQD0B0o/WuAORsWQOoZHhxI90QopVhoCrwrrTyDCufgBggIhAIC0pL2CvjI1XGvirzW/rAU9PL0SELJrB0eshEUBtdWF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUKmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrwXw//RLAjdhstAx7XdtaGqqX0gUa8JBz53hFxJR2AHTOr9JQBCoX3\r\nlpQvf6MRiAG4aw9jfxlEgE1fFvRaZSgEORLChBWLGkW7nu6/JejAlyH/IN3Y\r\nmWc3KdzgQZLVJf3vlpUXfEomv1mrqNowbPDUSQingqeBjRqRguwjRKTfqLQZ\r\n66SsmIb/Js0TTPU/KLBYRXDCeFhO9tB0q9G324zPiADouMEz3GXbyTUK5rYA\r\nsTHFsEt12mboW5+6cR4IlPrzySYrX0WJHPB9lBRxN+madLxU8VAveRU1qC0k\r\n7WTTVRjLvOSdZQbQ5vHa3E59/HvEgiTWzZTG1hU5QKwG93w/jFl2jceXWOGF\r\nC6c56Y9imLM2CVmkOwbDaW4Te93fYrFuRB50BFjuXMY3Mi+cEznVG+m/ESmn\r\nAyi4uGeTSWqhERJoZDHeTs1nI7D+FSlm8jf83n46YXGH4eYxr+pitTq6nRom\r\nmtURAq3DbeJ9FN3z/W4aiUCKxnPdbwVoY7LF705jTJoYtKz8NNXx13fr8A7y\r\ng2+a2/wmO0cKwkGxnIoBYsj1whpTiFb9JV6e4sd2PMZ8Qj/84pgCAMLVdnlb\r\nDbg9p/JR7ti/KHtADVs5/70fS2b0uE7aCbUuCg46vQ5Vx/QxG28QEhTk/72n\r\nliP1BfpHRCVhqvEpHzvIgNHl8VDR0P3y4Lo=\r\n=homK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a75596d053529cb54ab2a8f2471e05917e8418d1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-zhIujOPn8irel8jJEt/SetqCoU8NgVBqaJspkQav9PcbiYDADKRwvw/iLNmE6kZSbqNOgkJzCidNnE7BjLiS/g==", "signatures": [{"sig": "MEYCIQCsTfFX/PErrbnYvVBy270TlL2H+HwMIvfHel4krRyiUQIhAKU5qmiUtpIicplCEHjo9WEkzgGIVGLCVFzdQzrdfFlc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249425, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRfJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqY3A//UTIv5PVngYF6T2/sf1xGwWHAHngfVrAHdLEp9+mQ7RaqQwEd\r\njFFdZiEz6w2Z0TSlXjcmRYx0Op37NQ1Q/DZ4O9oN0nL+doVShVwMuM7mv85H\r\nutQFQXB+jSmIBxEwQVzJG8EP8nnONtiHK7KU8H7MZsJwqfTInUNayV2Q09SV\r\n/0SD+mb1ty4XTbcnBd/++hXJzs6V3fZL6yiElAkT0PwtnN9hiCjdInA0jIBs\r\n9sm+vI27htt93AzQsowxzi28fljXqt43q5tu5f9u28zddgrYwOBEVGwTUt+g\r\nseQ4XaGV3P5kzAX+ZoJLOSzSCqAMyaB4XQ0ODpchbbwDEsOlKxEikI0XWr0Y\r\n7pXwzwi9CqraSdGyCtMIxcpec1hBtks+UMCbf/Ei4o+Eep3Y8gsUYHAC6GHa\r\nCDCz4wl+yXK8IQF9Yrl0m7+9hwp2D0eNSAug2QyAYYMbinAUNjMcpopjkYx1\r\nBSt/vD7XkH/QPLn6KCLxOg8tsAOIJDjI42/X9pO7kQKAtactNY3r+o5C6tAM\r\nAHUC+fxjfpcH8aeari9KF6RQSoGLlUIvQnYp7pY98AZwW00qMzD9hpvMmeym\r\nj2t/kfoG9o3bYQENPgrAP6IQa1zkLFmfavujH+/0fh4eDb2S5wV5YbhG5sYH\r\n/CUPCIkOmaWGz6TXc4WZF2dAL7ZFJXgkzac=\r\n=Sio0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "085cbc590a6d45a41dae3ad28187e59e537f0806", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-5OxAnuLUnrvJ183kVPAQUozWPbqufGP3l9SEnbicVnUniWkc5KQH4NFY4UBu1OTY9o3uZR97oBVSm7NgFOZvng==", "signatures": [{"sig": "MEUCIQCYxaHBOF1xG8XBQbLeerkz7+PHxeYLgKmiWKekywPxowIgMVN4a4NDtEw2zHQlU2Ceo+iyJzGzzKE+KhynmL4/Ryc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249385, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSVEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+Ug/5AT48JYGzhopbL0qRYdVqJjrFCHte3xF0GieRPYrYkspgKvQ1\r\nrdPwpTDMyKamIdVAW0KzbEoJC+LW4SK48UAEch/umNFLwUD+RiRgfo+TQO4g\r\nApzPvrBB8TfDXcqcwODdAn4yO1/ePBQs8PQVqogaWwkYpPiUHo+RkEeqepH0\r\nTzyfc0FWNVJ4VREBfU91Py7zfq7m1D5avHnSuIdJu4QbDDK2p3wMr/EyuHQO\r\n4l1exQrScpB3CDDQPxZ/QOPzEUQfV+ujgvNssZKsnKoaQSJ7gl9qwhWWN1JD\r\nvLUdMqE7op4eKGvZ0awaS3XEgE0Ve9odcz0DV/uUdudM/oSPhCgCPJ6uCrQw\r\n4NiDaJueC/TYK3kNXco2aYw//4M8/WEvrYM0vyeJRRLuebO9ARsHESFL9qfL\r\nD/0L9zvW17EMVZEfEumGLML6O2LDGX96cHnKHw6Mn1r6X5zyeG6m6a9jjXou\r\nb+31LXuvfmfz3WTb2HzRf+gDA0LBMlNN3TV3/v9xV4AAQIcV0QNC7vBHqhg3\r\nmex6n0sYuyKPgIqqBkzSsxdBBLBy91jJ8yI3Z2d6AtCbew8YJQXegAk1Mqz1\r\nOz06Tq+cDzbZnDkkIdLP097LaTFyxXWPFFDWn+heXeReEPShZw8cnENcp8O4\r\n2+g7iKuDWwfKmkt35WMwRzb/vE2bjN3kWnw=\r\n=GrLf\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "41fdd7a785a56459fa59c009b9b80ac82770113c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-087q<PERSON>wewolRLQ9ckXxCKJNanvvwZR9mL+nsa6OTy1VtPUDx1YwZ1Ub5gJNbCyYVgTP8oiVVGnE/vORfKMQg1EQ==", "signatures": [{"sig": "MEUCIQC/N1s9veDHXgX8OeBOBUvAZX2e3Mx7jlYKFn4egXjZFQIgFvmmo2fA/0O7lqVyUP4xJcJc6QJebgA9R88KL4y8w6w=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249826, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjY8G5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr9kQ/+O6gSDQ27EwfYVEj6cdLMjqpjC955XTaDN53I8kGb567Sg7P6\r\nMElcInowppR/s4HOeANdJOA/8k41TXc3x0HCi0ScLXrleO7+lwcsAr4A05BB\r\nMnmyWwUr0nyAwAqQBcgwl0dvfUQYQJXYeASo9K22+yQwBlgkH1UOic1MH+tE\r\nVXCyKlk1+jx1SP7zHBB2ljhw55rZYqM3r8AkzesGK94+SX+PRL5wPrQtZnst\r\nE2R7LqBIGzE8VLn6pDCdfDiLiU+x3p+Md4CkSeKL5/D8VxQc/KIP3s5rd0zD\r\n8vd5RrgPGibAM2xA0PWweyP8eOZ7724c7XS6UYCCnRN0U9xKZGkCBBBwlo2H\r\nWQK8pVS5PL+YmjG70W9LbbnwofnXJgYCDFJ3PuduLvRDahV1+Z7+jsmhJWV1\r\n4an7kslwRttbp3Y0OuBvr9rAaOwKgrSOKhpbBnikjETy2P9Dgsorv1vFd1m6\r\nt95Xrba9CmPR2Rg0yfQXg4C+BLRhBhxT4+M+TY4hO0K0fdKb1E/Dz2ROzXJg\r\nzahZtMa8f9s+WjXMX5RTD1ZIa/jZjlWyySNScpThtwWC4fUuPbZc6KxiYomy\r\nd8ARdHgZ4ItHAyp5zYBmMDsM1q/su8dqBrFiGrbqCt5aJ+n42vODRHRk1tr3\r\n7WEZVIC23ESmwkUc53BpSOxKZrBCqjPUOhA=\r\n=LDsw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-scroll-area", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "26c906d351b56835c0301126b24574c9e9c7b93b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-k8VseTxI26kcKJaX0HPwkvlNBPTs56JRdYzcZ/vzrNUkDlvXBy8sMc7WvCpYzZkHgb+hd72VW9MqkqecGtuNgg==", "signatures": [{"sig": "MEUCIQDBfgADSfOqc/+jLCFTnVVToOnXZUHBGuX43qQmHv51YQIgX0WC5Iqw4sU+MJexKRo7dpdNiRPzYckxRnVl5ekDOk8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjc3TvACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpJfQ/9GeX3zmUzfMnvhmPJYQozNmn15GvGs2z88ecnHN9CDLdFx4Mp\r\nOVNUql402uSJp/aRxNKNArqb400BS0IIj/7DEYWEcDnIGnOnNQp2toZEOCJp\r\nuR92XMOOZzxQheEze7VGpbVdqT6lADLmXTQcnpzsCYH5xc92Qi8mf8AFjw8S\r\n7Y87/SznK2KEZx8CAes8MeiON1U2Aidn5aqR6xHmbI1q0a6eWsgDPRuYxPMV\r\ngQqEMiaoaNrrsdQRXZzAayh5e2aqNByYuGCDGjWomQEVyefYn7GjFRoK8q9O\r\nsUXc46c6JtIWNz0YtL8VTzOpJDBrCYpcfYRiAHay2Uu96EtgpEtf0v2eNQzX\r\nVNoWNxxrb+M02tL7mFYJd8NXdBA/zNtTB23l+frTZMSOK452kFa09kA67ySC\r\neHd4LHCjHNePmUhIspEAgwflMC+8iIvKgYARbriQq1Cgrbb0PshuBon9ZiEa\r\n99ocGka4n1C8kERTGn91V5tm2ED+CfH0djIjwRguw45jsqcXub3RSeDcvYW+\r\nNs18hZ/UU8wd3YTMqo9H9aZnBbTpsffhJgryvW1olCzgIPTm27rTDl8mvXXb\r\nMiZ3XZx5xHErTh/rm9JtRp8tFgIMtPLUV5mCXzhTbj/9LT+7w2elSJCs11Jl\r\noD5QzKJuk3nnrMnDMYNIVwf+lRGAgKnTfWc=\r\n=e1Ap\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d16399e8eb71a3e5b1c871ad166c678a5aa4d1a3", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-KlYD9w3zhkjWiLdhsd0oNvAj0Euk3DjhmDORLsditVmNV81P22IX0p79rvIKU6PHxDP3YEmt8pI52d5VicC8bA==", "signatures": [{"sig": "MEQCICKhypkB1K0TD6Z750cD/ld912icKZcTBI+hf9a6vTCWAiByQo6QRX/DiEPFCs2LtU4Jc2pz/+qBsz6MU5cGdSWTpA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrL5A/+Jf8p4Zw7C2I2nNh2+b1GIUIgpSbiyVXPJe7q3/2eSuKqKWl+\r\n/aHdmlhR5BsKyLjcSFGX4qF3yINbs7xrDWI2qXmyco/3hKrDG4aa8re9vBHk\r\n6ILxlTp7m42xmoOYv4lIT5w6/tyx5z/9jleUQcxnTWW8ZnNhqlL24riHRDsj\r\n7Fw8acx++oLpL+YJA6i0rRrKARaoYeEccZX6gc8WRxoyU3dwrNEffcI8UXyc\r\nvy7vLX7qJVyDEkytA3xs22xwgsqefdzzRAhFoKuYnTKaxOFEoSgodn0lZ+xL\r\nkn530JL9cbu4uhdwFCsMDzhF5PHANswwexTw9fZ8yEeejBRK+wyWXUWFZ6He\r\nf4KGIXxlO+VqwpNbDgTo4b/IWWMMegEB1lbL+A4wQYZTpwOfcewA9jG/8ulX\r\n2Q1HyU/LUt70Np6shDZIl38eGkJPYJ8bppQDLjFE0ApS2bU6tclsAc7eEKaQ\r\ncisGGF64YCd8RHHRvPZHn8jPxCqpzd7PkdDBfFXPybBmkgIMcnNnDo9CpOjJ\r\nhnYM83oYSgkOx62RstyK0V6cWOgB+61ZhkynGwXUz5umeiuDt9OcYMKWrr70\r\n0gQugN8r75InuAUWhHkocD4+7Zy2CCHeoF9HK8N0xDSFMyd0tQ1glCGXiWqo\r\nmh34mqVJOHMyGPIlApiy0RjC7VTr7RfMl50=\r\n=u/N7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3": {"name": "@radix-ui/react-scroll-area", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "01bbc4df59a166e4a21051a40f017903a0ce7004", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.3.tgz", "fileCount": 8, "integrity": "sha512-sBX9j8Q+0/jReNObEAveKIGXJtk3xUoSIx4cMKygGtO128QJyVDn01XNOFsyvihKDCTcu7SINzQ2jPAZEhIQtw==", "signatures": [{"sig": "MEUCICSFhkVFvqxMXZg4XdKwbEjeyS2dFqNmhXZQdQ5nGs6qAiEAof1klsl8ux5g6xo+YFqbi8Gp3aTUU9vHWAR83hM4nlU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249793, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJa2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3IA//czoQ8RWcWnQ3/ZO3Vc0B8gLE2epnhjYZrX3do8dv6UrlxZ7c\r\nJ9DJjqyzvfEhFgYn+YDzwA0RGiSNwDCEJWeqSs1NIkLgtSrieEZ6VnyyaiZU\r\naF93/Fvr6m1It6/BuTrWR/gn8VbP8rqOo036ZEncldp6/BIWlisEnEiu3JkC\r\nX7f/9lv/74JJdnqOC32TkQQ7BT3ah+buisVauciH64+D6kuWcjc3xf14Qr0T\r\nR9pgReEpoG4U9YMYlSxBfnBLI8ZALebtEwXLkX0pBfRS5eTfisegyPBWMsJo\r\nYtB3A5pY7o3k7G/SeqJzdO6gRaK9Ne+nwh+0C94468PJVe8xWTp3RvFEM6yh\r\n+VF2adM/7gXYV9laupK7UoEqtnNtLyTw3BYcc1bdvXQY65BODa6ZYw0doPMy\r\numD0C+DdSzczlXguiWgWu13XlXozrLtoWhTuf7/0VlOC5HkgVs+DpT9+04Cd\r\nRni7leSVjdSsQlY6XY8IYCIdPb6PfnygtPVwIU8++BFWCNAPmJEpV5Li2S3G\r\nd1hkmzwk4ioFN1XmGnv5ebk5JQ2wN/7wHrbpk7K14OjaudfP+AcvhHGdAvwD\r\nktG3vikxPxjOvKMfo9U0ViHNr0zvCft+/K6rlAdKEtfvAqq2enJcEiLtVDQd\r\np9NTP83rFFXIxuBRbKwmqy7vVR3RerNddJo=\r\n=Xkes\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9006bd0007865c6c7875f15cb35fcce4ca9178b5", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Vsb6T/aJaOzstj8NNg4sCV0nU6rd6V+adBOCWj12A2WpHagJ1KBX0jNkoIAzH+/gep6/yZhxcD9t8/1felQ+tw==", "signatures": [{"sig": "MEYCIQD/C85YiSM3oPmuIbnJn2apxZMMZcGvV/SdAuY46aGYJwIhAN2PZrvyo2wETqZqIX5DLMssAad0IxUMYSH72SK3772x", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249831, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xaACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPwhAAmJ6DVWu4GD6/RW9zJqLqkRgJkNmTK8POKel46A1PWKgQRJBk\r\nBAQwRblqaNQbj39ug/kSxUFQxsWlTwzlV7AeiDvusKJnV3UAeaP8Sd1TNaBS\r\nQBqkq7gFoNjDbvlDDs74wjPzckEJ/YltMhNK3oMz6mgeoWeWeLxt/kE7cFeI\r\nkKNywC15M7Yg1pmyyhb9lJRTOI7sbyv92VP0uXzwygRvpE6N8xNbT/larZ+F\r\nGYMtu8dKmxuG5boRncJOnne07NIhIMJWVB6Z1OQ51AW82HMBQAKaBbt6LT0o\r\naOj4SGfWVhclte+YG3bhkO67yM9yI2VTvgWAxvhe5YLT4SFzZ7BMyJ5VliZq\r\nGboW1mcHYPqAV/qkVp/ZElms7O9+seVGlZB6OIytAs27805ppsb+7FibGdVE\r\nkqslRLlxUL5urGDooXJ2OlpfaQQDz5gfnIswnsAMOLtrZD5OwUUjjUXZqibS\r\ncbyfdbpoqdk6Byc8Hn5MGmWEi6D9TcH+m4OsI6xAPIHzbOZdAJ1cepP79u/w\r\nwo9VpeFescJvIi/XV6QECXA3sc/J7UOCwLOHZe+jfLq+tQsRFdzGbWppBDY8\r\nffwTRGcrb5QwoiFFUfoj6Fsnzqd+Sa0brdoS56/hBGu7dGvG85AWF+68xO3C\r\nJErdbKtnfErvKhiMg+RqBzo26F/xuaYHDd0=\r\n=gP4T\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.4-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "28b66b57f2098680a577c595a1acf9f66e22d6be", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-D83JBkS2fLmn/xkcUjRuF81ZcD+I0rriVwy8eF40l41Pu4PD89BZLVqzXdUUmkmQ70mreI6awYgETq45n3xmvQ==", "signatures": [{"sig": "MEQCIGQGcai2hQQ69SdjFLH7a7HB2niyR46w74kSaC8RoO+kAiBvNQYUn3kVRNqXWE3SMOIRkPAR2/bzfYPM/7FGVs3cwQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249831}}, "1.0.4-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d69896b96e8d7748631268c792be1b3f80ff9529", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-NPHiQgVandwfw2JrRO70ZmhRrKgqKiijjuOlbV2JFbOjjQ0YZhwCd72q1Cot1clFNGitp9xlIsHUrM4iGbzuBw==", "signatures": [{"sig": "MEUCICrWbv9aClhDReTQkJ6K9vbwMV9NDowtktCzz1eszwzTAiEA8ZqUn5EmgwQWaJrsyyGrka6M+PJgb11PjYSOd4mRN58=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249831}}, "1.0.4-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d909883a250607753328de524ad2914f821b6cef", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-X0azLI9sp2RrsVkKOF9cU8HZd7txkvf1oW6TDKW0AaxzJ6C9N6XWhVrMvk5dbm/QhiwEchSa9hCAsoGUdsFtLQ==", "signatures": [{"sig": "MEUCIQD0toZLApCFbVCT22TUUNDjzVbsOFv/FBENyt+SrXJb7QIgJNS4jxlIMwj7qRrkHvsddNRaLancLHf0SM5+b76Ptek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249831}}, "1.0.4-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-callback-ref": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f115cf99627f201d62510c90b0f5b2051668a3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-8YvNykLT3IXToIIpS7d4bQtqCh+IITqZJ9/Zyo/AEyd00oHumF2yTYgfTRFo/wNTeqpFrqOrCI8jeF22ni4f4w==", "signatures": [{"sig": "MEYCIQD1BnkH+/6TmwqQpGOJ7h+4v73Z/kXVIrRB+mGvelALLAIhAI17ruRFUsmroKBZ/BBa+0eC6qDwHfOt431CaGbBNjzq", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 249831}}, "1.0.4-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.1", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-presence": "1.0.1-rc.1", "@radix-ui/react-direction": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-callback-ref": "1.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1-rc.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "36b3261da9795aec4d24d14ba921af39017815a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-K+5JPMPhl1qzyiIU60C/V1r+rRuK638EBla2UxJ736/ybbAJPo9ZolIowj/+BxXKES6M4EEzEWVzlMdz5XF9LQ==", "signatures": [{"sig": "MEUCIQC9TzKe6TvNnct/CKrYTSuyBoW3Y/zy5soUzvqUU3ESrAIgZQ9cDk4/9v5n78OELB+mT+LALYV8KStu8v2jtpiuKuM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255784}}, "1.0.4-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.2", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-presence": "1.0.1-rc.2", "@radix-ui/react-direction": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-callback-ref": "1.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1-rc.2"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "fe5c73017a8e95f6629030b7d8a134724ed2b4e1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-IyosQg3zSghQCDv8ckQR9GVQq5ySat4apWO/K0oFp8pwxTNJE9K5BV2j2Upst5g+v+7IWSdcCYs917D47i25TQ==", "signatures": [{"sig": "MEUCIQDfP8264ZCNmjC8fHu9CXKwOMgGHjnoP0jDUb3e1RjVEQIgJUxPzDUAC3VKE++h7WdAhIrL1WRs4TS3K6RV5rNisiU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255784}}, "1.0.4-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.3", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-presence": "1.0.1-rc.3", "@radix-ui/react-direction": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-callback-ref": "1.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1-rc.3"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f5190034c35037d763152ff50560a180a68c7369", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-4lm7ugKGha2F/gsfh33buMm20kVrZXxg8eNSdKU4SbB4X4Og0PSFO+REojIb+fXOIfkn6ESR4zPWnVEPQdxtKw==", "signatures": [{"sig": "MEUCID5fu+1Yr+ZKN37wH2AYB+KgIdZOGLZh05vK3LcD615fAiEAsIxSycs0WP94cMqcsAygpJIGp4pg2VWG+pKAsn/J1zI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255978}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.4", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-presence": "1.0.1-rc.4", "@radix-ui/react-direction": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-callback-ref": "1.0.1-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1-rc.4"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "76c211eeeb565c9553a988a82e56e1b14b718281", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.9.tgz", "fileCount": 9, "integrity": "sha512-Rfrs4Shez5K9nu+ZZEKiGC8GLGSGjH8brD1AziewMvCj7tfI/FJj2Hs0vCRAFrm4N0djzh2z+1OP+ozBgZvcRw==", "signatures": [{"sig": "MEYCIQCGIG798LKQ7+1ahQkWoH5xOwZVQvezZ9Ow8RIT3mueEwIhAMxApVugMeGjyQrVv4ahrs/rf7DFmVIGTHMOfRDv+tbw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255978}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.5", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-presence": "1.0.1-rc.5", "@radix-ui/react-direction": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-callback-ref": "1.0.1-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1-rc.5"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9909325bf9447cf13b28707f4826a35c41a0d2e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.10.tgz", "fileCount": 9, "integrity": "sha512-LyX+xpzTbHra6fMFT5V0OuCkjGMzY/NqKtcnQiXRSA449zxIuAcrhJFx2+DCEZiMRMia8sfW5eI/m3IlAAKX7Q==", "signatures": [{"sig": "MEUCIQCeumPsOnXQ1/H+d4H+iPmJx4NSjIkobOeXk8oB4fJivQIgNynfVFbJLLTiYX4kTkMzQ2UZpbS9Z/0fA8KMR6+TdM0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255980}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.11": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1-rc.6", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-presence": "1.0.1-rc.6", "@radix-ui/react-direction": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-callback-ref": "1.0.1-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1-rc.6"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8825cd8844ea5a053df36f0c9a0d0f4ea7a76555", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4-rc.11.tgz", "fileCount": 9, "integrity": "sha512-go26BzFsRcv2TD+xqRdERrnAiLe9o+UqR50OGQCzKKE8TSmZivqzsBYUfnrGqFzh30R1y8dy0ybWstdYd59KWA==", "signatures": [{"sig": "MEUCIQCgzyZDpunnbtahgECIQJ+/fbodKqZr79+wOO81HW17/AIgRoq1tQ0GO+7uPgIOHZz/ogkM2Tg+xZrEvp3e8FTpZ/4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255980}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-scroll-area", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "13c36c453b2880aba57df67fb91a1d3f9b18998d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-OIClwBkwPG+FKvC4OMTRaa/3cfD069nkKFFL/TQzRzaO42Ce5ivKU9VMKgT7UU6UIkjcQqKBrDOIzWtPGw6e6w==", "signatures": [{"sig": "MEUCIQC/Rro3fK09tVxzJ3VOqZ+X6y2I7ZMqjfi6MhUS0h4yMAIgcsWXjcFnEXd1r6SZ4bUSoOQaPErs0V7xE1MSPGhvNe4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 255900}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "047ecda0dbe4c70ccff80e9b1e12e412435f72d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.1.tgz", "fileCount": 9, "integrity": "sha512-Glcj/koOcj11q03XJ6XE90qfNDhlLDWr64KYxVuxc7P2IuX8B4avY7atCFIK7YPdzzCAX0hs6lhYVgEHjAHSFw==", "signatures": [{"sig": "MEUCIQDtPFaQBD+yONIRiDSU0uQnxy1+kHrf/QQTier6yyXr6gIgYtIr4ckKlJuFpIJUVvIlcHpEUKbn3YjAqWvcVV5JbCg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "94b18e51de81afffae7ad4f4e82ec88dd99a14b2", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.2.tgz", "fileCount": 9, "integrity": "sha512-QMi5QLIoYPEEq6kBeegGxbuJD+bX79sTXx7d4BKZjacVt06fqdPoJM8yEVvuE+l0hVbi0WJI+buaHS3LYo+RCQ==", "signatures": [{"sig": "MEYCIQDp2GLndKjfYMkDPPkAjqv1Wk4ezXKksldHIX4iBvpLUQIhAL9eGbWHlCX6JG8xYb059zUXcrdBp+KB5y5dZtq1ZHp7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5ba8fd79becf60471633021aac8ef700fcca8826", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.3.tgz", "fileCount": 9, "integrity": "sha512-hVWktHXGWY0tNhXLAgK5cmxcsVzf71Ycv+1mLZBCFyadqAYe7ezK7wB6Ww51JECCHV8SpCLM57Bk7f3UVOEEwg==", "signatures": [{"sig": "MEQCIE8zBsTK7Pir9MoqX4EQfQpzPpo1P+ovPrMBlwVfUrNCAiAS3z/A3hG5NOHUXYhLxg14cfcmDUpzGFG8FkxwXHXGHw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "999fe5e15d9af43eb99a517d534c0fd23b14fda6", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.4.tgz", "fileCount": 9, "integrity": "sha512-ZipEds1a81bGkI4Tb2NG24JIMEoFcp0lOXKlHxqrTT7fheySvsxEXJKflQE9drd0hllJA5QyEFEpA4AD2dUgdA==", "signatures": [{"sig": "MEUCIQCgyIxtIksE5jX9b5qW67m/0WpAQoQhgAUoViPhTuVqagIgN5+8D3HnUM5+47QMI47oOWuf1wASt4SfY/1+sneKgkQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f581c03adb331a9f573aa020cffeefc24e073721", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.5.tgz", "fileCount": 9, "integrity": "sha512-tltpGcsEhmupcLtR8Px0ZTx8Z9XqKp6V7mmSoOBkRsZPsRMEUoPzm3B+nGqg5QkYQzUYeSe6E7cn1exxC8zvMg==", "signatures": [{"sig": "MEUCIQCtkQfPgSqs3xauFGWaRUKEXNOkCiMQenx1FtniAUv6IwIgXkyrJpr5Pr4PKgyYQk8a+mveyfYYsxsDJpIQr2LWHP4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d572e34f74c44fac1364c23a2ef48279730229b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.6.tgz", "fileCount": 9, "integrity": "sha512-lEcWG+1HRhFdAibYb2uYcb2pAA23Ap9VABpCWE3vYHzisspflq7gI9dZmREJistGRED9VImx56Ie2wLFy+P9iA==", "signatures": [{"sig": "MEUCIA/J11mwH4Cl5cTGR+w/RRfsgIO+KMvoYJWOVZ0oPAgJAiEAjvloy5wEU5TrZ7V9KPk/hRMubehbPs78SIceTQzU+pc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2173532335367f46b09063642377c57d039be78e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.7.tgz", "fileCount": 9, "integrity": "sha512-xDXesn0l2Vn3XLspTosE91qTwmA9K85ac7OjKleemVuXj/BbqFSbz/lZ2MN2iiMnh/jAwtGYrhiJpGZ6m4/F7Q==", "signatures": [{"sig": "MEUCIQDRI45N2lHYx+l3ahwGoWZfC58cqG0p815+PPN4Xf3YSwIgKO58C6j3Bi5NVKrPPg7KL/VILlAZKEgE81ChEw1iNOo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7953efb6ca73f2fdf78b6a408d5347cbb6f023a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.8.tgz", "fileCount": 9, "integrity": "sha512-E8qvFr16fIc89TJTVyKSM7lLQ1QMd4/b6O4bIMRFET6JNSiknSPocqDTqbxDuYZuFkVDSJriLAay29Jn8GyWug==", "signatures": [{"sig": "MEUCIQCD3sXCKbGQCZEUdeiU12FNF1P0PR7mCnIF34WCG/JeZwIgaKTFG2AT/ME/Vm3NkREq81/wmLkTjQrgY+TSkVRrMQQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1832ad7df1dbded6d7deb5c499b575b121d1c9a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.9.tgz", "fileCount": 9, "integrity": "sha512-kT7EpUSBeYFaQr6lF7NgvrFauDrY6jQdalGOYIHmGwpkimsnxN+z4XkdRSZp83uxTMyucr33PxuobQUL1wn+uw==", "signatures": [{"sig": "MEUCIDLw7a418ox3VaGZJnqEloGaGxY5/6anGPvdJ9u9Sz1oAiEA8T54o3MzLDd9m3q67maZHWeSci4TXQeWaKUSShvIuxY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257021}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a4b3de4caa40ce6f3e2ff5042f8a1d70d36a0e67", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5-rc.10.tgz", "fileCount": 9, "integrity": "sha512-C<PERSON>ickV9P47giQn0NfKtFT8+lQcet5ykAXL680HvKytgIesBHfYL2+vffir6QcbXSYQkMKOu1Hqc58ZQRjZ9hg==", "signatures": [{"sig": "MEUCIDDlgjyRylyqdKWsC/n/yj2VsyEdrmb+qiqsGsDDhVZJAiEAoulyYMO+MwGOhpzEfULHXg9oave7mQQS7FJVYxICd6I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257250}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5": {"name": "@radix-ui/react-scroll-area", "version": "1.0.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "01160c6893f24a2ddb5aa399ae5b3ba84ad4d3cc", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.5.tgz", "fileCount": 9, "integrity": "sha512-b6PAgH4GQf9QEn8zbT2XUHpW5z8BzqEc7Kl11TwDrvuTrxlkcjTD5qa/bxgKr+nmuXKu4L/W5UZ4mlP/VG/5Gw==", "signatures": [{"sig": "MEUCIQC+iAQzngVfyEyGp35xLWuiTm6uS8WbxcboUQQu8xqcnAIgBIvzEj5ZezdyO0ztrfNPkI3b10rq76ufpxFl4elK9yA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257216}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "218aec932dd0f482815c9f7dd59998e32ff7f7ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.1.tgz", "fileCount": 9, "integrity": "sha512-qL5jCj49qQzcav6mVDsGF1LTRfDPoAEw7WqMLDs+M+O17gXVyKa6FUks3I0fWDx+peZOBTEVrZ7xkHO/4kkKGA==", "signatures": [{"sig": "MEUCIQDJRLFpl5ZDfb+Kj1wOhm6aMXHwDf37Po+sfll8UlTbuQIgS8+P0tIskQfesEDHVoLdDaBHdfArSItVDKbBFbCYSGg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a354da5123e1f911eac596fa44cdf8dbbeaa5cc9", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.2.tgz", "fileCount": 9, "integrity": "sha512-TnFJX5lxczyFwRfnAjnl8fQt7C46bWTWpKl0raXy9aK2wCErR6/7lja6caaKeNZZFkABJSe0+k5HwkgJpSou6A==", "signatures": [{"sig": "MEQCIEudQqXWHpDj98eMTfS6sAZ+hoVVKmUy6xX4dyBpuQX7AiBzSgDlxraM0dp/iG5IfJBkMD303p8ZNFwYkx/KetB/hQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "52b193bd1bf5a4d45ee9c57f7b6e5a5ae9f7aaf6", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.3.tgz", "fileCount": 9, "integrity": "sha512-4IqEmm461iZleJYs72eIpYQxQ1XwoBs+kPOZJRr+WlarnJTGh2TsRxiYEHtUMazQpMq9ribFnKxYLxXEGkg40Q==", "signatures": [{"sig": "MEYCIQD8j/RbNkOOOKTg++FPbJpyYgJi/vtqoTXsQxNjvKUKMQIhAM5My5j7NbwD2Mlub6kd6OQgKWkPH30JFmINsB/GKvAi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c9d9243fa310a2b6537fb2203a42c78fd6540cfb", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.4.tgz", "fileCount": 9, "integrity": "sha512-3Uo+7b8vFLgLeF7diRoEMGikBUYi8ubMyBqR/dnv6jOod9gO4Jw/pQZy4khXd8MKOgbSgPKfJSL+FJ2JfFisoA==", "signatures": [{"sig": "MEQCIEKwZNedLhWge5gURhqnnNhh+np3HvEUC2EEfEJOx/XOAiBWOtq0XkJITbbBOpsR5UhUlPx8TzwtzLiLrpyzfTMX+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a2791ba062df7159da17798ce0f35515fd2223f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.5.tgz", "fileCount": 9, "integrity": "sha512-z8HQdxBwrgbzLbLbAAyfs+3Zugew7yMDdKswdKR+DuHkPQDS9C7B7L/caHIGFb4DNkaF4tqgnCzEFm8YZXgulA==", "signatures": [{"sig": "MEUCIEA/6kqPfm2eJxjIqJQghbGk1LFd0z4QPCby6qkvdlT8AiEAto7vD0DTbyGx2r/eYmQud4lmppDjVJdv+tyEUcBG8Fg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a4f99258ac0be34c039ba4e192533c0bc78db001", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.6.tgz", "fileCount": 9, "integrity": "sha512-/rJTO7Uxe5aQYb/84u2w/A1Mj7/E1ttpGzH78wGPA4vfhIq6oWoR9H5lKmJwZw4c82DS1VjGHuFhONQ0oQeOXA==", "signatures": [{"sig": "MEUCIBmHaf1dH7d8ublN/Twsfx1wH2bfkjDvHCHGyarPXUx3AiEAyn1xEQvhjOAp3QWyF6fNdwODI8l1ioaq95TNfeIppdU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5068f3d0ebdb17e73bda4d1818ecf1229117b5a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.7.tgz", "fileCount": 9, "integrity": "sha512-WTat5nIwpzcc3vlqIAkr0tU2kNu2i+JawS5CBItra/96qKZpE0jUNhqZIEhHt3WPNyYgYN6a5/FTTYuWSoq+HQ==", "signatures": [{"sig": "MEUCIB0RUMTm9H070/Pw+yBhizHbcdN2xLC7gAK3YfDWexFqAiEA4ckx4e11Ooluw2A2C8cY0EBqGpBqvvfVaBQjwdDUXoA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "56b032eb0bff5fa7443792ca106c9c6a5081bee2", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.8.tgz", "fileCount": 9, "integrity": "sha512-KHtNMg3e0nU043U0yo1KQNfrw+3Ey36elwdEuAP+PMRV1+smCTENZn5g5hWSEE+U4OxRZp1GjlIXHTE6f8x60Q==", "signatures": [{"sig": "MEUCIQDSM36e5svpQRs4iKCPAxI6ncV1JUgkVWdR1gJTcymBXgIgTBG/TF3Bb+YcxeuDi3fuayFORv1jvxC7hDmfo5XMkDQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "03698a31bb2397c973d0e6e6443ae316d8e761f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.9.tgz", "fileCount": 9, "integrity": "sha512-K6eoKeQf+dx3EadOScIzuJWQ44M2J6q+JMvcM3obHvl1OLR3bbu9FtmRjmOpHNQjAEg8vxGH9cLbhPPqzgSRXg==", "signatures": [{"sig": "MEQCIAbG589A5zuU/xhM+qbthvCogjsPV362G4JVSPsw5f/fAiBeErLXxEMMFbYsN5JiXu0JQ6oowFmEwBuSjzZq3prMoA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257594}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b0488869511dd36a605c7f0fa5497eedafba714d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.10.tgz", "fileCount": 9, "integrity": "sha512-dcNHJTdSOjODx5L8xFU8cSDD9aWfrFUKcxuHJrxDmeI7KwXW+rTQZkir6ALPRRnuxxFzaoWnZVYNhySVcQ0v4g==", "signatures": [{"sig": "MEYCIQCia5Xj9DnxFYOqcKHWxHi+DrvrjsEPPAJjsayKuhSQzwIhANWYY+xdW3Y98DeVuE8bIm51114QckGtW2OFaCYB30BH", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257595}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.11": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8ab14df928654ab2039a1349ec191d39a74c0216", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.11.tgz", "fileCount": 9, "integrity": "sha512-tBau7NadeHl0xMfX4VpxSUHjDA1pLkwQxzdwLoMGpRoJlpKiwXH0T503IQaY2AKh7rQsnZwsOgvLhL9AenG89Q==", "signatures": [{"sig": "MEUCIQD6uEHF4UxG79iU10dDkfW/5r4TA+nZ3hCuvbMIRlqR3gIgdtKzdlRF6SqFvWX2ZY7LgY/TAVqq4u61j3FavyjMoeU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257595}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.6-rc.12": {"name": "@radix-ui/react-scroll-area", "version": "1.0.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/number": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-callback-ref": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8c2d6d22fc793facee435ae44b8b425f8131be5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.0.6-rc.12.tgz", "fileCount": 9, "integrity": "sha512-vQ/6yNYc391Nmg7341PolC9zYR4qRzfgVs+OlV1GdC6gsZ+5kl9tjsNpEj8zxGpDRyKS+p2MaXSnYKkWW1Rg3Q==", "signatures": [{"sig": "MEUCIQC1LFOR0IrlxF9KY5IdnWOwUMJ5hNXSUABuTr3NF7CuggIgY6SDHbkwpTWKSkmh6LHcTp1CJti9gAJ5J4uNdWOjVvs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 257595}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/number": "1.1.0-rc.1", "@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-presence": "1.1.0-rc.1", "@radix-ui/react-direction": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0-rc.1"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f3dc2cdfb1c11d4c6879754800ae102ba542b7c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-WkbEoHYgH+kU8k2VhAFrZBH8BPQ9yJhBXR7kK2tD07zmw8CklTevTujmuianRPcvZkMn7Nl8da4Chdk+goaSwA==", "signatures": [{"sig": "MEUCIAORa5Mx4Yx+T9dkhzD0D2N+9bW2AZhSAeQfghJpftTwAiEA5PFr576buVq4Qb4J9qD021tRYBgSa4MBcmbRNByE34o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197672}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/number": "1.1.0-rc.2", "@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-presence": "1.1.0-rc.2", "@radix-ui/react-direction": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0-rc.2"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9a248c85ccaf2051a6dc5741914465dff8cd148d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-gb5KR4YVgHzkbA14ty8v+RD1axOGu3vRQ7Zi+rRgJvuE+4yvI96nAjJ/zlVDe2C8YCbQ6UwykdyvY0dBoxCC/w==", "signatures": [{"sig": "MEQCIDW3sjTYtnCG+ndTAFFQ7TzUuxPsEt0Yc89z771voZamAiAyI157MKcHbp/9pUrjHhz6HO/K61t+UsyaAZGauXEJQA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 197704}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/number": "1.1.0-rc.3", "@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-presence": "1.1.0-rc.3", "@radix-ui/react-direction": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0-rc.3"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f0324beadf07f09cab9f89ace33ce61f058aa14", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-z9YmEMxVkNxSs5qibTX71t4Py1rrUP1/MvvQUjzoa9YNBI2yxOhf6GTZhaKjP64+/mv+HFPfcNetIqGwhKKiHQ==", "signatures": [{"sig": "MEQCIFxLLivS7VCKzV0GZkU4IPfjQI1qwOg0m9aNndReg+ueAiAmkDuG9PnROfMgbkn6bEAtM8aWU7gXzF+X3eNyAvuIxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196905}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/number": "1.1.0-rc.4", "@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-presence": "1.1.0-rc.4", "@radix-ui/react-direction": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-callback-ref": "1.1.0-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0-rc.4"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "db31c0b6936545dab8ca6e3ee1eb26fa814c4535", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-3jQ+rMzVNVkmxxpw18/7ZF0YLfkQLkv/EEnv+lnlm1Z5yMrO2EjRfsvK7EzfF08WFcyWzDzCza0JVf4fOoZuiA==", "signatures": [{"sig": "MEYCIQCM4EdDjO4aTkME4G9EpixVXWUfslZDsJRZJ6uH/UcfSgIhAMmCVbzR4SS+14R06afE/nOfQvAE0p7UCKB73Ai6m5th", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196431}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/number": "1.1.0-rc.5", "@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-presence": "1.1.0-rc.5", "@radix-ui/react-direction": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-callback-ref": "1.1.0-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0-rc.5"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "562c8fbe42a395b047436885cbd91ea6f7156b08", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-V8RmfahHmCzN6xNTM/6/zjLXEA1lH0T4f8vj2e8eKOFDwJLjhHPAfl7zuZZ2l+m9sChSgseFIx2T69ilva9U8A==", "signatures": [{"sig": "MEQCIGdcuTOlNtHq9D6QrARHUqj4mVS6Pa1WWzdK24o8sfXQAiBEeRSHESewqmKPbF2Nb1AgWht0F3MF/J255534QIn0eQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196431}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/number": "1.1.0-rc.6", "@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-presence": "1.1.0-rc.6", "@radix-ui/react-direction": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-callback-ref": "1.1.0-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0-rc.6"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a3af08d0e438a4115650ed3a02efd534e7bd03ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-e8t9I50L0fulHQ6s2KJ64P/HuJVuEwnSAxCMNWOGjONW7f2pSzlZF8AepVuEQbpC6wJNhPmidOBqYWDHW6+3rA==", "signatures": [{"sig": "MEYCIQDkMm0Vgy20YbxsyrP+i+s/LqAozHduy2S8uvmrzeVEcwIhAMQHy8c/nZY7dAkbbbl3OkzB4jbPXfbWEnXlg8317/2X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196431}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/number": "1.1.0-rc.7", "@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-presence": "1.1.0-rc.7", "@radix-ui/react-direction": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-callback-ref": "1.1.0-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0-rc.7"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5be1df98d042452e264ac2d8c1572ba4043f2eb6", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-9Z93I/EiXkr8Mh8ck53DH3u5/gukpkem/XKUVuYLH71fIpj1xf+6Mw8xRu9SHOn1b80O4Q9BDp6ozy5ckXtP+Q==", "signatures": [{"sig": "MEUCIGuhO4BqDeE/XFZ6j4tI6uLhQ8uiSKitbOiSCrDiU/gzAiEA26rjN6Q3Cr2qa3uDLKoV8r0M4auRZXMAzvx7+9KHxgU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196459}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-scroll-area", "version": "1.1.0", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "50b24b0fc9ada151d176395bcf47b2ec68feada5", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-9ArIZ9HWhsrfqS765h+GZuLoxaRHD/j0ZWOWilsCvYTpYJp8XwCqNG7Dt9Nu/TItKOdgLGkOPCodQvDc+UMwYg==", "signatures": [{"sig": "MEUCIQDbECxg959vH+gVy5udeUZFs9b0SDjQAX66QB2FlUrbQwIgHhZ1LijBz8jibLRNXpfLSEYMDtnpVDoaWuXfoJe/l+M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196381}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e8cd0877f775065287dab2fc766aaa59c36c943a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-py2Djk75W3vrgsZnCSF1gtJG0gda80VOQTb7eWAaZZPo6WmWeucFRzN3QClylCNKyVtHni/LbQH3iewwHFWUbQ==", "signatures": [{"sig": "MEUCIQD/YExWwa2RToZF0b0uzpYKSxuMucx4jBP0qAe3EHuCvAIgB+3ocz1pP1t32pXlIkRhqKj30W3+3aPyWRNOFwpEgo4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.2", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b73bf31006bb8870b349b1f458e827e0ea1d8ccf", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-ShRKYNVdyhlGu0dc6WZ9XF2ykrcg0fn4S2RbmVC3X7Ek3OqfdeTHsXoPp/l3oFv4HyNSHS9uGYrBEDsm023ntw==", "signatures": [{"sig": "MEUCIEkcEresCA4aIL+1R/LkzHz8vgAysK96qv9ySRSXhRAmAiEAuVh7IMSFKple27gBd4B/bAaTSIMhi20kPz6x49gnXbk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.3", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e5e069be3d237c51e7d403fc31113b126d0d24e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-rZm4gH7aRSt0ioO+PTn38noGXHo74LgCKBTe5eHpvXzuvfUv9hn/plPqburtTAOFoaz8U9gdU+ulBcY1WpGimQ==", "signatures": [{"sig": "MEUCIQDs4qtA9vtGZRLXUI+xUgPpMIRph1/S6TketZ6OVQJn0QIgRBCEUJmgbzLryrUpj0Eh8JpVqBIFatLPddjGPovl5aw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.4", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5fc0ccc1a806709ac9d2a9222b0ed3bfe8b6eae0", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-4vZ3QSpYc/OLBp1b4gM1O6NDblMYVeb9NRsmiTvOycJ8rdMwzTE9XArb3R7r4Pla5jt6X8QmxRWNDPaVlbeI5g==", "signatures": [{"sig": "MEUCIHoE26NNUbaiEf7z2i9zEDD76v3oU1zGtUVZORL/2G6UAiEAvQiBdMxb03gU/btzAQN3Mmf09a+kFG2JqxWPX7Yr9Z8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.5", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7b3be1a77842dad903305271f42562e832aa5c3c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-7sRdc2KOWWkAWaVcSC+9dZ2hfP7FXpPmtVZpn/jAIhXvX5+zIK56Z87vUFLor6Ve2OzbeYhr/kX3ZCrXQ95d8Q==", "signatures": [{"sig": "MEQCIF6et3+HHSp81MLjictQIkEXS8cAWGQHYQK34rqvX0SoAiB7/E0RWfLCD04iWBfslzBIo17IqljVEHaVtfQf4YKY+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.6", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8f14017cbe724e69d389f5fb735c2f37b9401d08", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-DiXAiecNKAWoSF1a3KHWF/9v4ieM4lon3S9PvCdV/yFqA9z/mVUWzlwew5WR6yw2G/M6uttQqBOE8RWHWy+eiQ==", "signatures": [{"sig": "MEYCIQDgexTUYsBxghK5Mz1MGCnzY7CTxSBKSXDR6w6K6W4f0QIhAPSkxqeVnpybjbyb8ayrQcPbob0HZVi3Xzs0Z93YrNbJ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.7", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5cfc1807978caa8fdb3f79c0db22b722b668a375", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-uUTySL0lZ26t2YgJBs7Z5QLBg8Fbp05Nr0vA6b1Cd5PnfYcG9T62yafckr4t7GrPhQCrNm5deiHBKewEAY1qOQ==", "signatures": [{"sig": "MEUCIQD+f6zDzVN6O8zhNiLmo4/hFBh2L15AYIbLJkaiAkdRyQIgQDlTMG/a6kIfgdJ5CTTTs5Vl1p97tWvU3PzOcy9+5vI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.8", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5d4877a5da71d898a1f36715b998de7f38438950", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.8.tgz", "fileCount": 8, "integrity": "sha512-c14jhUzwu+aWEMB/2AmVOpYhZ5sXLfPaZ1KKmg1HRyVQ4ZkMi5wKTt86EKBebnVzlN5qj4A+o/KPRHvYHC21og==", "signatures": [{"sig": "MEQCIAWNWWve19W+kX02teNafADzhOf+ZFL9Umm0mwDqCdUpAiAKiCwiIY7N9AbJwl3G3fn9qxYcNHFUMDZdSao3y9xFsA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201325}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.9", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "22d549aece10c7669d172d7696140ba628ad2913", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.9.tgz", "fileCount": 8, "integrity": "sha512-bmTFO1ufTzBpb4VsXgZWmS5pB356oicadkDE56bKN+iI7qK4mU0vnSZUqi09ivTVarRv0WTvCg8dKe3/oAw11A==", "signatures": [{"sig": "MEYCIQDkh3kEaNWfvOC34B8Pyf0UolER9a0GNmv74HPKm0HfSQIhAN1kiHJp0y9uqMBm7kXzFx3rzs7ZQurLAUz/MkC8GHqO", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201325}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.10", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bcac66bde35acafdb0561be9721e4376bca50a5e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.10.tgz", "fileCount": 8, "integrity": "sha512-QMilBZt5xnFNxFjromhztz7tBiN48Irbm+LZa4B4FztcmUuP1CvxjC/IrMFViqKtOD2w5ws/uYhRxe7d5ffFHw==", "signatures": [{"sig": "MEUCIEsxnkw4wqj7aYkhnOOOgFhoZjylqFW9ZWurDDidTB/uAiEA6H0r1EQe4rsQt8ejmdYgkWNBG6lRI2DTbTtr5PF17Bc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201326}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.11": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.11", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.4", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "38dfa31696f3dfdeef205a5528e2b8b8e5efaf98", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.11.tgz", "fileCount": 8, "integrity": "sha512-sRdruuQPWa4oP1TdAiawcYZNIy5YBkqC0PPhkDC39eaQeHCpEXpBsOXRJ+2VXJL9ZAjRlTBmG8V8U1E7iLyvdw==", "signatures": [{"sig": "MEQCIEhzpnO580rAc/MLJQ8D/FpmBDQ2Tp51nJz6cv3UGnEvAiBQGggijYnd5hJF3U5HjJBwkFeki3DQbwHRJ0f4GdXUMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201326}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.12": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.12", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.5", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c05c4b3d6b2d9e832fdec3f28ac8f6c198e4209b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.12.tgz", "fileCount": 8, "integrity": "sha512-3M8id1HAZDnH9UJPg/0j7DZ6Fc/k4M62E2qzocC6kvailcYIVll53Lz3IvYCp2/4EaFaL/fHBW/YPGwFxCr70A==", "signatures": [{"sig": "MEYCIQDp7yMKO90JB4YmAeEdFqcMX8Qst0qAF7Mw5BmQHEZh1gIhAPxcAPhM6CbIjbfU95O9ke7TMYKsV2hba4zMEi6N4W6P", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201326}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.13": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.13", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.6", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bec5c3bb947e9caaf7c7d083e44c431d0857d06d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.13.tgz", "fileCount": 8, "integrity": "sha512-9GyfKFE6ByfVNNd35fdE4S+w4R26tENliZDPxjMz3jHE8HiDGiMTDFgviA0b5Q8lZy7yelKmpz5y9BJgDHXxSg==", "signatures": [{"sig": "MEQCIE8hcEKnypQVZpe0lUoK7bkhWZorPxKzfTVNme5OAuKFAiBgT2hFXSbp0vRjyCHPKS0102k9LM0oHofBRPr0Ebk4Sw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201326}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.14": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.14", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.7", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c523fb42312923203025801f2936456037ae2c1e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.14.tgz", "fileCount": 8, "integrity": "sha512-MWmVTJOnxeoKcaKOyQcA6f2pz2e0tafrxEHcju1ctB/RlwkTFi7sNIC+6H0CiESUlGFVYKGcNYFz0liLJAYTNg==", "signatures": [{"sig": "MEYCIQCvre/hGIeIzWAR0W/UpV9b1RJudwDd6/Cd/RuOyRjxoAIhAI0Qh72x1qsQdX/XG/RmlBbilV1lqZviWj1h4zlNkw1i", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201326}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.15": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.15", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.8", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1fb0a272c738479cb8ab3a04474f8390a7966c6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.15.tgz", "fileCount": 8, "integrity": "sha512-i632FWpKdFT40irhcIqd0BHgok3OveeFyMrPO2sooItL5jf4D+apmUUX93LQ2YjIgf6HsyWEXwLvAzGerVF2LA==", "signatures": [{"sig": "MEYCIQDTayhEvsMjqoAYcIkbsrywYHZ2R6aZlqsjMBH+BUSyAQIhAPyo7wsSuPhCQkthE7lAgWfvkEWHZdy35WonLT5d1KQn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201320}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.16": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.16", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-presence": "1.1.1-rc.9", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b2c924b5587e63768fcc8fe8e7ce384418123911", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.16.tgz", "fileCount": 8, "integrity": "sha512-OmT+a/Yj3LOYkKC/ZzLJKBXpHrThUy+9nbcNutrfbSlix4OnLNymla/+BguwuRFvYo9/GWSS2LQIVDGCns5P0Q==", "signatures": [{"sig": "MEUCIHiel5tKmYcS4GrLEwbVVkiI7+t1aYvB7uy3Q5r3ms1FAiEAveB1Hb45np/waD6pU/od1Bxtk/T22149tvatiGHULgQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201327}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.17": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.17", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-presence": "1.1.1-rc.10", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "337ab094a43597f76339dd76d8dfbb03f9f6b946", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.17.tgz", "fileCount": 8, "integrity": "sha512-1qWw2TLhF9Mkrqsr63s1UgtxaccSs/7ghPdwlPonWn36vHts6rLE28YI47IxC+lg0ReyhFcg1Z+UupeG/DbZIg==", "signatures": [{"sig": "MEQCIHhM/ecSwzIxMQ+KM5A14An8jVu33akxpUZSJsDwfADHAiAqmKTKNfzVEJ1TBWGcquMVZUazzjYtM0kKoQI/Ve8vgQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.18": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.18", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-presence": "1.1.1-rc.11", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "32b61d744a7e9c32463830054ef3d8b5c2414909", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.18.tgz", "fileCount": 8, "integrity": "sha512-S5AgQVvUo1fZErXWdU6PbejySNpOPHE7cezgyfysmCCwzgBIiPP0EhMX3XE/5T5MqBKVdht/HSqHwShFWSRDAA==", "signatures": [{"sig": "MEQCIEKCizTWxKoEB4cT++4xN+Hxxwv1ae0dMX8Kpb5fGIy5AiAzf5tIlCSBMJongWO7jjvfcwF8H5yqpv5Mh4WQAQZR5A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.19": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.19", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-presence": "1.1.1-rc.12", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "730f86738f42c4ead2b71be25d9a5bdfe70eed47", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.19.tgz", "fileCount": 8, "integrity": "sha512-5PBofu4MMD0hpMecUM+SS42zm+y3Rd9ryKsOeXErnU15TqylWzEBqs/Z9+CIv8DYErykkEqqG0mp3V1xyiacPg==", "signatures": [{"sig": "MEUCIEf4WCcmn3LxmW7x/jo9UbyosQfICyPH/tEBLpu6j70mAiEAiUm+Xt0ysM60jhGlf8i29SBht4IiJarmJi66PJYdCT4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.20": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.20", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-presence": "1.1.1-rc.13", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3c83200669d25b27c0d2307cf59d532163c96db4", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.20.tgz", "fileCount": 8, "integrity": "sha512-7c4P9RHbO6McWcjb3+q3BDSUbfT8LlNQvW0PQWo50LnyDE9p0NdoDLe3Rw8tt17+IxdU3cwzmw0+Dua6aTYmyA==", "signatures": [{"sig": "MEYCIQCmM++FSVIsLw92Eq1rIOp8Jb8LhmtKTREnSq+uRzwHFgIhANIfBYpNNXrdExOMOdVC+BApnVzQym3wPnwcA/J7/zC/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0-rc.21": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0-rc.21", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-presence": "1.1.1-rc.14", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c5bf9c932d661be0ff8c67c7026dbfa846458660", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0-rc.21.tgz", "fileCount": 8, "integrity": "sha512-0Xg3qqFgY+5VN5cJSSDE1ePz/Du881DyTcGUdAYUfb1XjQ7mNxZ6o/V6qM83Cfl8L9ctQ03LFR7r062Iy8aiqQ==", "signatures": [{"sig": "MEUCIQDMSoNkLxmBnqVs2yMLgtbRSHsZVZfBoPS22/FTHrakBAIgCw5mkJZynniKxTF2Zb/hpeL9T1bco6lv4atV43MzgXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201328}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.0": {"name": "@radix-ui/react-scroll-area", "version": "1.2.0", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d09fd693728b09c50145935bec6f91efc2661729", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.0.tgz", "fileCount": 8, "integrity": "sha512-q2jMBdsJ9zB7QG6ngQNzNwlvxLQqONyL58QbEGwuyRZZb/ARQwk3uQVbCF7GvQVOtV6EU/pDxAw3zRzJZI3rpQ==", "signatures": [{"sig": "MEQCICseiCXWb+6sHltZu2ZyXNPjvQZ3VbXIeKzVVz1DgqozAiAn6+aIULmgAozuMtsf0bgNkCk8CWi8Nn16NBahaCVZyw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 201283}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1": {"name": "@radix-ui/react-scroll-area", "version": "1.2.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "610c53e07d017e24b62bd73a0a6eb23fa7331b3b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.1.tgz", "fileCount": 8, "integrity": "sha512-FnM1fHfCtEZ1JkyfH/1oMiTcFBQvHKl4vD9WnpwkLgtF+UmnXMCad6ECPTaAjcDjam+ndOEJWgHyKDGNteWSHw==", "signatures": [{"sig": "MEQCIGcOg+P4XlG4VXTJ1NcxiElv93s+2/6mX19j1NWa6aXxAiA3AD5KdJTpbbSZqkgBUAdF01Xo+1cCKh6uyoxIuhCUqg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196369}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.1-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.2.1-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c1b4aa0a1b55e54e1dc096dca7a18dc6e35ba62f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-lazkX3rLvHPNxoB0xJthOLRcbX5T2eoq67Z9PCHj12adKT67nWIvrqG2L0FOOO9hQ/4O9nFhyF/XaITulFLKBA==", "signatures": [{"sig": "MEUCIF3QW3c5DfkdSt9Zy3jlweeKq2QzM53S1j6F406A7nJIAiEA3cKzc+1A5OrDqeTjGW0ruV2TmnCauEGtMEaTgzWc05M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196402}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.2.2-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "460d7e5970589dea88750d686735377721060889", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-DD9AHRqGnTle+/EXehEQ9ZwKAqwzD1tJYlfax/ruTzGL1V6ttxAxiBhxlu2DQM7xTfIXYTBsOfRAPxeiqfKvBQ==", "signatures": [{"sig": "MEUCIQCQWvynGroY5vQR2ntCHqdiwWF4AAz8FwqRAw9JNHo4UgIgdOUvIPpWWYO8xyDKyTrT4PMgzgB/Hbgsmn9LrHuoiZc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196192}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.2.2-rc.2", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b2c64a5110dfecbcb8a2d13a0606fe173cd99968", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-g9GoineSmi/FSjOPmPgf57Xyh8a8icsVShL7O394RO52d8ZcbTB2Yqv87yR+e8hpZ/8nYTYueP4Cj53vnfshBA==", "signatures": [{"sig": "MEUCIQD98JllDDYHlEMBzI6j6WzUEb/hTLdyciKtHMJeTenEUwIgS1J1W2+KwqV1B59KTSpg7PByktSnfGgea6jVjhuwFS8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196192}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.2.2-rc.3", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ec8e2681c908a0902a5938461464b5a2abc8b323", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-5SmoXgqNE17MVxPmUJu5RIGyj2ukjoGGaKjRrxY/XUPEedMdCE6iX/axwMhGCDYyoQxPN7Yjicg2XKOCnwkmZQ==", "signatures": [{"sig": "MEQCIHs4vPCwLACIJQqjmVmiL52rvGnPPRZRQ4r3l29d6d0HAiAKlkmF9c7DAissfNe6ax0uaKMh1rsx7NfDRqp0ZWM2rQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196192}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.2": {"name": "@radix-ui/react-scroll-area", "version": "1.2.2", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28e34fd4d83e9de5d987c5e8914a7bd8be9546a5", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.2.tgz", "fileCount": 8, "integrity": "sha512-EFI1N/S3YxZEW/lJ/H1jY3njlvTd8tBmgKEn4GHi51+aMm94i6NmAJstsm5cu3yJwYqYc93gpCPm21FeAbFk6g==", "signatures": [{"sig": "MEQCIHDvWL/FwU8ivwW4YduJ0+G5XJpQNAQqC3t9ZyCzjaRGAiAaQ69wX/UCMOKgIubymXvpSegnV1VuZahKF4EwvGGKCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196139}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-scroll-area", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/number": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-callback-ref": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88fc0591cc9fb576b2f04289a4e856d548bd6d78", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-M6Rgaia/5BH3dq2MTjQWuAko9ryzNPs4c0vCnWH2PFFEnw1s750iue6jfHC8vG7zIaDNDxI413CxT599Tk1KRw==", "signatures": [{"sig": "MEQCID7UfzeXQRvrcThuLaOkVc8hdnMjXF6MpIQAk5Bx0Lf7AiBNS4zASMg+Yu6ZjnP6ahOmWTuY4U2+aKamgSsLZmkqRQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196178}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-scroll-area", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/number": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-callback-ref": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1df00d10a214242c539c62c89fffc2c7fb92d41f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-4A58jZrroZI79lhOzlBmUxd2kDb+CwkWT9/QSd18adgvTLpWEaT3SiQA1md8KM7uWGHv/sd0P3cu8OtoeIs21Q==", "signatures": [{"sig": "MEUCIG6xYrWU5XbqDpBbYsE5KJorTzCxJK2GgtBzEDTDWsucAiEApG/Wxr/N0IdDa5sFnW4o+eZXlyRdtMqj2ss9nKbNEZY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196178}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-scroll-area", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9c4a5f58303508b6b93994970044c94f1f8d119c", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-2GtmsRx1dXJgvjv0D57NX+jCKU49KOrjj8nby/EFONKPVHflXUicUDzpj3brsn1RAXQJMQr1cuquKLGzIYV2UA==", "signatures": [{"sig": "MEUCIQD7lTJ16jnKQMhxeot7YSJBFRPT89b5BxCN/h04NO1IpwIgNgNFH5yZXE9zZZYeuYQTESSlejYZXE+7EdCMPRlPUYM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-scroll-area", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5946127fcd1c393ece78f785dd58950c96dad620", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-FIKURcQ/f2Fl/3lkMw8jsqEEbRuOIFNNknQG8B4Ja4YNEjx6Uwyh46d7eGLK4GnYsjUhSrUMmxMnpze/m7GK9A==", "signatures": [{"sig": "MEUCIQD+ke46PEFdlZZuLeqzMbZX1wGCThinR0vkDzKkTz/cgQIgTtCoUQQTb+g7W0xcclST6hf6km8kxV+XfwXfOuEC5aU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 196291}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.2.3-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d5b005ea29d09036b96465d6c6e4e0ae61c43ace", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-B9A6GNKwvCUh/iqZatS4jzf7kS6CsjqOFLtcG2s43aXVxz5B7jy3uDbclNQFInY2fq6/DGqLAuQek2H6tSJTuw==", "signatures": [{"sig": "MEUCIQCfoNWqs1h1teCgO6TpN3Ru5i/7ZD8ckS5aCP7zzXl3QwIgNwtQBWvV5LT85iuR66HwwFzFFWbnKNpj94Sksxu9yWg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196363}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.2.3-rc.2", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d92a0e7d006078f127feadff72fc9cc8a610b78d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-41MBgdTiz8X5HJfJP3yVCOQhs+pFuULPK7zjrV4BFNLBo+NWl8s5MqHMOrPzcOazfLb4WWUG8XX6tBR6kEBgSQ==", "signatures": [{"sig": "MEYCIQDAuf8NIqdtQ11LIVAahyTfh/hr0R8hHDVb7OLGFNHDtAIhAMn2lapo2mElx2fslFdpc/4qAy05XNMa6+GGlTttUIRR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196363}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.2.3-rc.3", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7370a405810ba38f678b83b6be91714aa29191e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-h8gt2G7udoLpVdOdtSKM+aPpNNN9WCdGkFZaPNHt8+I/hGkGWCqZDfbINrPHLT91OuIYlRZgcB8eZaPkThKtfQ==", "signatures": [{"sig": "MEYCIQCXQkLhFVVlWdXr72MBfid+cDMV5rXpAnJHHiGsaYDoigIhAOvD4XlteEDptt6jq4sqOVj+0jTrz4ww5XmJiIm+Ld91", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196467}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.2.3-rc.4", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d5996dbad434e6f858db650a4639cf6882499c53", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-RD1PodkLTIwyuvdH4DXtJTq1urgPLvUbxMDxV8Nj2CAaB9yniTeYTChmiQjloZ3vlhNpOFtbfUNWVykQjxSqQw==", "signatures": [{"sig": "MEUCIQDwqnTA73lAadJn41mVRwW430Qgywfdp4lI79Dy4x03BgIgMGR+jH6T0ZeB0A5AEIEszRkdNFevh3qkAA6XsbnTLeQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196487}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.3": {"name": "@radix-ui/react-scroll-area", "version": "1.2.3", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6a9a7897add739ce84b517796ee345d495893d3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.3.tgz", "fileCount": 8, "integrity": "sha512-l7+NNBfBYYJa9tNqVcP2AGvxdE3lmE6kFTBXdvHgUaZuy+4wGCL1Cl2AfaR7RKyimj7lZURGLwFO59k4eBnDJQ==", "signatures": [{"sig": "MEUCICxIEyUboHnFKapeIpOdksEq+mg7Xrn0gYq2ph1U1bOcAiEAnh7EaTd9btsO8HgcFzNnsKICV6aZhUP3nUmR/npn2oE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196449}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.1": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.1", "dependencies": {"@radix-ui/number": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.3-rc.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-callback-ref": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5a580418a5a1117d64fd53853eba0fe2253eab34", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-TXICYnwTCN/qm6b6iFQ++TMTF11f7WMEwmLsDgNffqAYAiaQoQ7dLWncRAv7EVKxRIFtr4TowUYjNRatY1Fqhw==", "signatures": [{"sig": "MEQCIBUG6cosL1wwToE9AVE2zfNXMFbYRfcXE1WPq+Apsgx/AiBG6B48q2eh7cdmHB7BN6nH4nphkP7+l+htPUvL3ODUJA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196487}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.2": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.2", "dependencies": {"@radix-ui/number": "1.1.1-rc.1", "@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-presence": "1.1.3-rc.2", "@radix-ui/react-direction": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-callback-ref": "1.1.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b4e76c07286283732d8873e059db12c15ed58b93", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-Tyn9ETKOy3c73r+MKvJWhKur8Y+dO1wMM8xnrXy/250O570WawoQ6TURv7KELuStYf6z0caWPjKKg62q4wzgww==", "signatures": [{"sig": "MEUCIQCeaCUugBHDOq7APUejQwIO33J3jMg9sdU1lXLHD0+pwgIgHfpI6mtRcWO4gIxdzSWjUCfdaS+/kNGDzOTJZ5L5Lyc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196533}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.3": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.3", "dependencies": {"@radix-ui/number": "1.1.1-rc.2", "@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-presence": "1.1.3-rc.3", "@radix-ui/react-direction": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-callback-ref": "1.1.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ec3a1cc5a69bd5795370d817af3ea2d5b0b446ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ftrIjxqwH4SU0jcn9lNUpp73/y194jkgajLUIub7bmjY1sJzkmpUiOdlSzcIA1tVABce4O3f3zRinWParP8jMA==", "signatures": [{"sig": "MEUCIQDtEjNXv6LUytgHczEvWqbRYCJue8HN8lsc8vWj6OZcCQIgEObzD6ul4gvTET7h5sJ1W7G1Fx0iJzldRYU4d7rCdnQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196533}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.4": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.4", "dependencies": {"@radix-ui/number": "1.1.1-rc.3", "@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-presence": "1.1.3-rc.4", "@radix-ui/react-direction": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-callback-ref": "1.1.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "707c29b6ecdcfdeca560d6381e1b13fb47a5d56f", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-dyrkMEJBvtG1ginUl9bkDJXee2flD/+Izbm+a/kDbH6iwk/2w8sGj+nenf1pl7zoRj1tALcxMOd7Gabjmtr8nQ==", "signatures": [{"sig": "MEQCIG3Pe5pusLlB0SGY9HO/nRsfkDXQPIRXqpyhc3lFftk7AiAEb07aY/U+TJcSKSU7lquVI7am9k+YdGxZbdC3BTHL8Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196533}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.5": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.5", "dependencies": {"@radix-ui/number": "1.1.1-rc.4", "@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-presence": "1.1.3-rc.5", "@radix-ui/react-direction": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-callback-ref": "1.1.1-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "88a66563d89119567ac780506239b77cba106963", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-knCCPAjEbpcZmArW0ueX7YjtgT+1Bvh5QobA/YDtIb2V/i5D/ph3Q+GcsYItCSDSgJ2j5unt0G3E4omfYq0PUg==", "signatures": [{"sig": "MEUCIQCixZfZd9c5y2n4yPP/guTmEx7F9Djtl+aPwAD/+fE0MgIgOHa6llSnmYR1Q6f97qAppD9BEXy8zSC79ZxOyGf9Brw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196533}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.6": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.6", "dependencies": {"@radix-ui/number": "1.1.1-rc.5", "@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-presence": "1.1.3-rc.6", "@radix-ui/react-direction": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-callback-ref": "1.1.1-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4b81b6562f65759b0767dd34103eaba49e0ea078", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-YTWklC8EpRgNvIUKnSiP6QkmpPDQVz+GQZLsDagP4HnEApohnpoP9RaaxNcDP+zqQFe1ZoCSe9KsvYJlC1//Sw==", "signatures": [{"sig": "MEQCIQCrpSSyAcwfKIcP5jhPhgBhlNB9ljMNTTJ5XFeO7TuyLgIfMNRR2gMOjJ7TAE3NgVOLomLfKmOvp+M+/98EpDjcgw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196533}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.7": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.7", "dependencies": {"@radix-ui/number": "1.1.1-rc.6", "@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-presence": "1.1.3-rc.7", "@radix-ui/react-direction": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-callback-ref": "1.1.1-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "66099f639d5e0722b99e41058beaa05eb2c400a9", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-oa8TKvmefWFJNlyguw5TvYleJVxEGtUjqP09K4bxJ60AZeNmB7v4/4phDHDrzBRZmqmo5n5BMIYAcv3jJYpCbA==", "signatures": [{"sig": "MEUCIQDYr7XRUBJnNje2k26GBliva1ShSxL2u1bq+huoZxw2vgIgDmkr5YWQSvNLVQzBDMKZPqADxDaf9Y3PqLrHhfpYrtk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196533}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.8": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.8", "dependencies": {"@radix-ui/number": "1.1.1-rc.7", "@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-presence": "1.1.3-rc.8", "@radix-ui/react-direction": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-callback-ref": "1.1.1-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "814f61c4e15c2726c6c391a4642d207305a1b02a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-qm6WCZRlW//LVpRVHrmFU/RKebr9E9/MXHEj5RAJz59hc4+sT88g736mncnnLU+z8ZtUyhdHlYbTbAW/Qxx0AQ==", "signatures": [{"sig": "MEYCIQCOVyVv9BACrGBEHdE3S/OzfJUf7jMhkdaoWdK9sa752QIhAITWAMbrGFnr7IJsC9D2uiDz6/QFZihZgdgIEHsC71tL", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196533}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.9": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.9", "dependencies": {"@radix-ui/number": "1.1.1-rc.8", "@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-presence": "1.1.3-rc.9", "@radix-ui/react-direction": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-callback-ref": "1.1.1-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7cd490300b5289280724d3b88b0217e877c89b98", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-jxFeh2jGmz2etDHVx8x6FVSziaccrVmH+9PDFi1FOhkoq06nFUDOOQY64XwDfNHqOy1/GdDcxMKA40t75oMayg==", "signatures": [{"sig": "MEUCIEAEdNJYU5+xdRMH3yV0eG+33uoInH6i9Y03r8PygGK4AiEAzCrPB0QwajoBmf8gVdzc/646RIwPlyAd0GxZi9kJDCg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196924}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4-rc.10": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4-rc.10", "dependencies": {"@radix-ui/number": "1.1.1-rc.9", "@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-presence": "1.1.3-rc.10", "@radix-ui/react-direction": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-callback-ref": "1.1.1-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "342cd43894fb2db34c4473570f6ad24819a6e8a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-im7AFoxOJrE5fZ8GvDe2H3xAdqfiQkFtKxCKqwXGmzQsliufiPNdpfONKLL84tKnVxzHMkh5Rf/KJ3Iehpjz+A==", "signatures": [{"sig": "MEUCIQCH+9Rfqvf6GPN9XntshVbk4jtBic6+XOmWSW+uJT0TzgIgfsvoQkNbM1pZrZcmE8/ATdAX5xpDGzjenO6dVGwPLKo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196926}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.4": {"name": "@radix-ui/react-scroll-area", "version": "1.2.4", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0", "@types/resize-observer-browser": "^0.1.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4b6fd6fccaae26dbe73700fe8c5be5313984d8f4", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.4.tgz", "fileCount": 8, "integrity": "sha512-G9rdWTQjOR4sk76HwSdROhPU0jZWpfozn9skU1v4N0/g9k7TmswrJn8W8WMU+aYktnLLpk5LX6fofj2bGe5NFQ==", "signatures": [{"sig": "MEQCIF/H3RGr9wGz3HSJ5TdDyZwejFMkkToML/9ZcTq4PAS+AiBfr2GFYVmylCoTzb+BixnAsE6LRfDlO4+BBbXVMKG0Qw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 196846}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744311029001": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744311029001", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6bb8d0091f62f6404226e239c8cdd2bb4264b4e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-GzsJXW+tRExoMeAJ9WjLTJU3bhXljaA+8Qt1ENxwMFbkmeejp5CmMGqNJzKwafLGYkGkMcmKjsj6mUbKoNDXZg==", "signatures": [{"sig": "MEUCICaJ0Sq7EfDswhm8kqUblYoryqA+1leZvwIdzwEWAQIvAiEAy9jHGgPVaB/313KQCWocSDsmPOSP/pcdeU0922wqU60=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744416976900": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744416976900", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3f07a319a7df316ec5bdc34aba75b370e32d03ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-Wfs0WLbApICbImH+n0PX4FzispK59Pj7u6E/qqJ32kWiok+aTjJSbr9v+IGzp4yvMMsNzKJB5322F4qhG1fTIw==", "signatures": [{"sig": "MEUCIQCKctdTpCFwXDVsAjsfsjnVy9EWnbbCKZTGrRcBqSvF2AIgTKjLKF9I1N+hwiI1Ef9x68Byd/2dqxOGDPC2pBvDBA4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744502104733": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744502104733", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0b34e70f0b2b47688e92509d8c6b03f158ebbbd9", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-L/T9Ee5rjez+GvcFdsob3KIA/M5ZF3la4Qt2Yrn3KN78HYTSkNAt43LBM8eygZ9PWPbnAZBYvcEDC0jxM2DCqg==", "signatures": [{"sig": "MEUCIEjHgCJ1pRkFOTPnr3zv0KGZPE5+co2rIrDlL51uJjlkAiEA7YZCwVLyC6//gwlxmyMdwqj7x1/kresg6mh361hpD2o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744518250005": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744518250005", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ad51ff5aaec0405ccb52c1ed2b0b066d2a5fc013", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-s3PqEu2idzYGHmytIn+pI4xsO1gWXq0gjACYQ5Cz53JpFQz6UfwAhsMTrhs9YPeL0g2YTWnq7HdpRFayMUzGZQ==", "signatures": [{"sig": "MEUCIQDmgWM4FuMNjnuOOOoBT2jwDUIMotvmbU2lTGqy0VwDKQIgNczcvJrOv2ql9WlId8ks19yMjU/ehheNg9bAV2CVtQA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744519235198": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744519235198", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c7d4cb642680805a0c292edc92d6406b79660b04", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-16Yt5wAT8FTjZO4JfPmxXcGE1l62vTJH02kmc/MnzHl/raR8sRX0Ce6oQhMMwCVsbc2VCIgIIM3vmTQXrYEIKQ==", "signatures": [{"sig": "MEYCIQDqax654KxOe/H6mIv4gvCbw2AHC+NdJ7zJzmHF6q9FiwIhAKg1MIJa5HOvBc33ajjGoPak/BQe1esY/B+5gBKZlWjV", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744574857111": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744574857111", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "366acb847647f0d0b0d2d4c2131457f70d8d1b8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-ptlLkatPB3zs9MjACBchxN4Ax4XcQomUPsT2tG4ikxK8PPqy6RlyuP2iadYU0HsAZIUANg0h6Xcy/Rgfyyx4dQ==", "signatures": [{"sig": "MEQCIEv1X4RW1ZucmOKQHk9D9LwxqrhZWAs16Dp4X2MGaSAIAiAavPI+7rZ9zZuK05/c00o7S3NIoXsiCByeotLH6UVf2A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744660991666": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744660991666", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "481c1b8e4e662124301d1d87372dcfa4d902d9ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-6FH9xIDLPu5Fq6IFcfJ42uBJJZb0thjN8nqAB2Vl191xdKIa0alCAO7F9N0H+/vGdxVADcEnFoFxpvWhhLRR+w==", "signatures": [{"sig": "MEUCIQD+A2Hyw31Xcgzvxp9o6PRBEDecjvdflnMvy0iRora8jgIgI2Wd8pWu+xRnWJbI4egedxaM+R+qTpOmAG5Thj9edWg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744661316162": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744661316162", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "63615181b209d7399ae28e07fc6dbb6f53363c6e", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-7GtGFGzBt9oma2kkIk1DmEHoqyHYS6r4Bi5bNBeztms0XDk1/Bhe1uQxWDEvO8As6mJ09SxSJf6WTQ+Lo0Uoig==", "signatures": [{"sig": "MEUCIQDPs4JnGAAe8FjqWv4gAOwKsTHPc3+vtAa5mY47cPHbdgIgUBAlGroAkrU2kX9SzQ6KECsuk1cw2+6m6+c3vvexgpM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744830756566": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744830756566", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5d8350419fed12b6c89c03f6efe0b520c059b933", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-lWmW7dvsaueefHd2QrYtmcAWkJ0Joa5izNjqHUnjDv2pJbxOCIT4IHYpy95axJyfPGqKJb+FDrOLrlBCkOBDEA==", "signatures": [{"sig": "MEUCIQC8+2p/LzeB8Oj9TKShX0yeVoZ8KOwW6rxuzNTTIYYPQgIgYm8gCV1QcjTqR2omGe2o8j4VJ6C9hmZcvuMOelPo4Vg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744831331200": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744831331200", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b5dcdef6030aae56d31aa1d4da61b458c2afa2c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-bzByiiVAelFk5R0cwD2oWT9DkR5e1piQIgpeFKcQtMJ4ykkcJpQ6eg+pccGJxA8XZzL+pWOGPWbLTLXPkdR5cg==", "signatures": [{"sig": "MEUCIQDato3HXFBj+d4eDhRSeQu8tqZyftbPj5FgcmKkHDrEaAIgJLxt3vaF+LszZDk1Qwec+PqXN36dGdB3hpuvQ8BnU/Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744836032308": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744836032308", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43a923700bec874bdb38443510d3f97df26dba8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-MuxvnadAEXJW7Q3aeYdAEMxoeH/Rwa8VEV4lrhIuPC40A0uhJllpo5DZhfUaTpddraFlV0v7pnx8MzbKu91e1A==", "signatures": [{"sig": "MEQCICw+hxPKLucoLKAvoJU/uLcnsrTMQtzutfq6oE0sXbiRAiBA4SU/+VZuw2G8DdopWWtRFI866C1amqR5cdqDWMx+/A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744897529216": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744897529216", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ede69afb62217f2386e06387b1409b0d61e489ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-wavVACA8THlulA8wx/uv7j8t5iy2TOEoNpheVwim51Gl7LXnJQ4uCXGzm0TcLwmGKk5Jj27aHZFkFi0wjlTnkg==", "signatures": [{"sig": "MEYCIQCM27l6+PfDtyIbyOtMxYDNJuOYLQmoTfNggbqC3sPFbQIhAMaRGbzZuvwNVBwTG6qODxZIGO8oEPkOQptw8xN6dbOQ", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744898528774": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744898528774", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "262e7575cae3c5864d547a96b277d32a021536c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-ynr2/+eIEuU+7TJiXKtwGIJWhtgLGBsKhSpYOEFL47VCNrVIyeIytVWTzAl+tw2OW+PR97FtPGyFKn57khXZ7w==", "signatures": [{"sig": "MEUCIET7MCkKHaaOSvebdHaDIQXfMZoTziDraST0rz7xI5K9AiEAzc3V6KHXB8ydVe66QlvsYgZU5qDh+Upd4m7L30VgJhg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744905634543": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744905634543", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c055ceba42ec4ee12b37abca10e6999508f927d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-u9b45T8jMKDemndMdnldY6KRxzoGbuPptVbZ3ll7OK/xFbYpxYrPm2iVNNmFgJvyG/wtr4we3AbqL64cfh7m/A==", "signatures": [{"sig": "MEUCIQDCWFz5HBqIG5Ghvgfzw+NtaGr+Tsi3VtQ0pWmXn4XIGgIgT6UESKw5erHKrmXYlgs/oaln0gVmysdj3/Ix7nl3eIU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5-rc.1744910682821": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5-rc.1744910682821", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7928f07bc02fe74e0957225d88a6f6216b66742d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-WKs4fuQgpmRtFQbgYWqN4VCa0BOc6LYYXmIR1ObKwGPXxiGCUT+rcEq+NrW2rqz5ogSeeoI2GwsFv8/vc6MmlQ==", "signatures": [{"sig": "MEQCIBp9hjKRLeiEks+CRIwCveDGxYHzCHwp2SkCDnlwgWPTAiAgtP20InU3HZiiAtNTohXMz+JVJz8Ol2r60igXxeh0Bg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.5": {"name": "@radix-ui/react-scroll-area", "version": "1.2.5", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "97cc082ef8172897b5b62c7fa8aab0e2c1445b8b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.5.tgz", "fileCount": 9, "integrity": "sha512-VyLjxI8/gXYn+Wij1FLpXjZp6Z/uNklUFQQ75tOpJNESeNaZ2kCRfjiEDmHgWmLeUPeJGwrqbgRmcdFjtYEkMA==", "signatures": [{"sig": "MEUCIQD3w5GJTjFny1FGrGREHYcNj3bUQsAs5pUUez2aB4rJTQIgK7F8V0qfYkEtCA9IqueqfZLjED53i4DXzkczOtCgA6A=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197107}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.*************": {"name": "@radix-ui/react-scroll-area", "version": "1.2.6-rc.*************", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.*************", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "069ff5216818f418a1e45a07532e167b14c1737b", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.6-rc.*************.tgz", "fileCount": 9, "integrity": "sha512-F0JCEmfo34OxqkDiwoh91U0grlaRQ2ccqYEanwBAnkc/2jJ89g/e2FiQE33CQ6ZnzA3xUuO5x5SGxPdxhiumCA==", "signatures": [{"sig": "MEQCIF8FgF4IjVd0SSRGwja++AFoxEw8g/v3khbRNX3rt6J+AiBb29JnbxWP65V+R8Zf1jFD+gf6my1jKKRHyT1Xpaf+PA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6-rc.*************": {"name": "@radix-ui/react-scroll-area", "version": "1.2.6-rc.*************", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.*************", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d69cd485298deec890c018ee3d5435dab8cd47a", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.6-rc.*************.tgz", "fileCount": 9, "integrity": "sha512-VXw3wJ36eBzhbUzqxcZZcothphTpbXb1JlkVaYKKw2Ao1rStEZsem1DP5sZufjoARdZ0Tnxu1T/gIRry6z+QDg==", "signatures": [{"sig": "MEUCIDkqBawzea4VT0jZ6uL0BaAjMmPz6/Yp11pvC88LGheCAiEAy7z+H9klShIPYFSA+DzJH4ggGwz0suQM73bRAXE/LLA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.6": {"name": "@radix-ui/react-scroll-area", "version": "1.2.6", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cd4b113e812e92f63ef6959f609ac7e8eaadb1aa", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.6.tgz", "fileCount": 9, "integrity": "sha512-lj8OMlpPERXrQIHlEQdlXHJoRT52AMpBrgyPYylOhXYq5e/glsEdtOc/kCQlsTdtgN5U0iDbrrolDadvektJGQ==", "signatures": [{"sig": "MEUCIQDfR8UiN2n0flpg7oDktRHn+DVLpBJOyXMqZnWZXx/hbQIgdDlcrfn16hPkwqhNVrHkVwEnCFA+iYl6IOQp4Dmx2BM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197107}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1745345395380": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7-rc.1745345395380", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "16e48bf444f4c31ca732ec6712cd0321fc402beb", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-V/8k30YTJCl1Qr8ArW1+DFsUtkKVtqJ6U+sZnadFtaSM9QU9jn/rzuIoVhNd90QOnoRrW2cemEMTGgYC0y+pGw==", "signatures": [{"sig": "MEUCIHzJdsay3Pb6xx/K47rAAEnWY+2zArolwSZI5ceuoZ/zAiEAjlYJg5/IPeT0snymE7K32rjVDGJDtae2s2gKtEEOz8I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1745439717073": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7-rc.1745439717073", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "89396b946a81faef07db589ec13088b077200063", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-mPI6rgkx1WeZfxBnECT95lWmGatyjtv9SaH/8+eckNeWXWEo4PzxO61iEsM70NO7QTf7nq/cu+yp6i1eNf7Jxg==", "signatures": [{"sig": "MEUCIQCykvjhdxHk5JAEBF5dy9B43sNgRpWyhJ/T5SkRXkmDaAIgJNqN7dSxLC29PrKIp9LoHFU5J0IAp1DK6ulNdIzJtpE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1745972185559": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7-rc.1745972185559", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "278d5850b30ab7440c6473d8f9dfcb3b1d425b29", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-efQUJBbsW/T0VdoWE6JTL4gl2Dolf2y8CDZyDRNAxp2D5AoixN0727eRBqfzYZYxd7dwrlBfI7v0v8klLbOa0Q==", "signatures": [{"sig": "MEUCICma+4qAEIh0sSC0Uff46DjaXhz2+0w74EAOu4NchtrbAiEAhwzN9F6bhSmTLdoP5n561u7wcfdxzQ3J/Ur2oEE6xPg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1746044551800": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7-rc.1746044551800", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c45d18c4a159e2c6f7e727ce9c2719c9fb6b1279", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-HgcUqQEmAyZrz2zXHv4X44tK5JtmjJjxLP9NAu7s8vJNiakgeJz9Qer+hqLd5PZStzZuKGAgODF1S6CR5NyeKg==", "signatures": [{"sig": "MEQCIFDfxrVA2VkUwx9HINjUp8ggdXxXNZNEHcJfau5wjEgDAiALwu2OSp1yCxwgx2gsFgOQld0/2raJVj/vMXZGlILInw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1746053194630": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7-rc.1746053194630", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "078bc77a67684b7c59f9775aa5fb027d6955f7c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-+M3bO8C07fueuUPwvRoher7AtGV+bwbiYEBbzUp1mEj/IleIEbX6RhOtjUZDvNuNXkpdv0BPb8hMkL2Tk95eJg==", "signatures": [{"sig": "MEUCIDkbsmx/igeDIU6zTYT58g01xWgq+c8euRxR01EDo9JpAiEA9DcPcRrnGLztkafpz/gBJsXyDUHXT2cF93DV7XRGHAM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1746075822931": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7-rc.1746075822931", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b80ea99b21577017010204cd4a7f7dfc49e7f0e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-p8oKs5jyALPNmZ2uNUKftYrXWINPAIAb8u6z/v68xpRNRW6AnN1VBB0iNcPPObSUqOzuFhHSCgWzBjq89b8sQA==", "signatures": [{"sig": "MEUCIDd2ss859HBMtP+P+0mmyMzsgbHjugiU+Pl0ZZBt1WNjAiEAs3hR/IGvwSHsb3vfnXcbVi9DpZSocx/QH3w7Pf2WvfI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7-rc.1746466567086": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7-rc.1746466567086", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a54b4874dd85d24c4bfcd71624b07016bb2cc9d", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-jiRXYqwrq9e73dRPeG5XQa0MmTNBzZ7ClYxMguY8sNFuFZAqIhSTuzuV71YQF2Go3UB2r0f0WvA4Or8puJgmJA==", "signatures": [{"sig": "MEUCIQDCLm4ENJ4IEDgTu+7X1HUd3a5+yrDDG5UVYm+UZgiE0AIgf9RKZdgaZulrFoUQZ3D89rgq2pmagTjghGHfdiMUtl4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197141}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.7": {"name": "@radix-ui/react-scroll-area", "version": "1.2.7", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1111357d99ad2b80c6a7ce778e2395a4e182a383", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.7.tgz", "fileCount": 9, "integrity": "sha512-zIzD5hZg/4xsGG30rb/OnffHbuCo1V1pL5ja+VECaXsDVeV+AQmRd9M2zgBGOcrf5Th+kpManU3S2VHCs7xgKw==", "signatures": [{"sig": "MEUCIH6udechszxAG2NiFV6MVt27Xfp5ghMeiswtoVKFJrsNAiEA9qo8jrb/2N1bXCIczvMQ8JlsBTyCi9fpHLNhPhdjsLw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197107}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.8": {"name": "@radix-ui/react-scroll-area", "version": "1.2.8", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "98a11fe9ca086e403d5c8b5b4c8f781439b45e68", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.8.tgz", "fileCount": 9, "integrity": "sha512-K5h1RkYA6M0Sn61BV5LQs686zqBsSC0sGzL4/Gw4mNnjzrQcGSc6YXfC6CRFNaGydSdv5+M8cb0eNsOGo0OXtQ==", "signatures": [{"sig": "MEUCIDGDciGidsdBDhAab/gR2K5YZ1+w0iro2pMtGFsGoz2oAiEA4K8YJZD/9jx70K3J5SgAxNj1EKXY6SivpU0MtX8bdEM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 197107}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.2.9-rc.1746560904918": {"name": "@radix-ui/react-scroll-area", "version": "1.2.9-rc.1746560904918", "dependencies": {"@radix-ui/number": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-callback-ref": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.1"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-7IPDm69MGh0MUsHAZA7ocaifDMCBRy/TerWh59lVwm3fr00XDaty07qcc3MsIeHOJtFIyGltsaanH+LxGoYfAQ==", "shasum": "2004433b244ae9b41d0ae81250b14e40c71d5d62", "tarball": "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.9-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 197177, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIFnqxNBaZGoayfomoobO/fscnvOT9I7QWNqoKD2IRXCfAiEAuHZQo76pSy9h97RZULtODQUffcsFmBcvnHbF5sklA+0="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:49:05.680Z", "cachedAt": 1747660587813}