{"name": "@eslint/config-array", "dist-tags": {"latest": "0.20.0"}, "versions": {"0.15.1": {"name": "@eslint/config-array", "version": "0.15.1", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@eslint/object-schema": "^2.1.3"}, "devDependencies": {"mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "1fa78b422d98f4e7979f2211a1fde137e26c7d61", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.15.1.tgz", "fileCount": 10, "integrity": "sha512-K4gzNq+yymn/EVsXYmf+SBcBro8MTf+aXJZUphM96CdzUEr+ClGDvAbpmaEK+cGVigVXIgs9gNmvHAlrzzY5JQ==", "signatures": [{"sig": "MEUCIQDMHRFnSwleof0p2zfC5atuSbYffjyddRr0FqRuk8UWwAIgVce4FFfE3zg9+45WT+9VwmRf1OBzlGaNgAUJkijirKI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112367}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.16.0": {"name": "@eslint/config-array", "version": "0.16.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.0.5", "@eslint/object-schema": "^2.1.4"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "bb3364fc39ee84ec3a62abdc4b8d988d99dfd706", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.16.0.tgz", "fileCount": 10, "integrity": "sha512-/jmuSd74i4Czf1XXn7wGRWZCuyaUZ330NH1Bek0Pplatt4Sy1S5haN21SCLLdbeKslQ+S0wEJ+++v5YibSi+Lg==", "signatures": [{"sig": "MEUCIQCNmn+2ietwbYcusG8A2/CMeFoi2bgNJfWp3dXrwPIKrQIgG3sQORKC2TapxkKwnHk9m7OdjAtNCveNueVaLSzKwlA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112055}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.17.0": {"name": "@eslint/config-array", "version": "0.17.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "ff305e1ee618a00e6e5d0485454c8d92d94a860d", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.17.0.tgz", "fileCount": 10, "integrity": "sha512-A68TBu6/1mHHuc5YJL0U0VVeGNiklLAL6rRmhTCP2B5XjWLMnrX+HkO+IAXyHvks5cyyY1jjK5ITPQ1HGS2EVA==", "signatures": [{"sig": "MEQCIH/auYEggLWMW+5BeCQ9bcSAUtYnq2rep4qbi+CQRNunAiBYUdv3+wU4+3VN3D/Q0iPAxg45Tl0J/GcpYur00U3d9A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 112870}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.17.1": {"name": "@eslint/config-array", "version": "0.17.1", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "d9b8b8b6b946f47388f32bedfd3adf29ca8f8910", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.17.1.tgz", "fileCount": 10, "integrity": "sha512-BlYOpej8AQ8Ev9xVqroV7a02JK3SkBAaN9GfMMH9W6Ch8FlQlkjGw4Ir7+FgYwfirivAf4t+GtzuAxqfukmISA==", "signatures": [{"sig": "MEUCIDzeQ34kP/bL9JJY9Wh90PUfCG0OW4EO2LoVg+PWbLRBAiEAqewhVL/AkjWAC2uf9qPA3LPLu/EC9A9ZdHTv/86+hNE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.17.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 113080}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.18.0": {"name": "@eslint/config-array", "version": "0.18.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "37d8fe656e0d5e3dbaea7758ea56540867fd074d", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.18.0.tgz", "fileCount": 10, "integrity": "sha512-fTxvnS1sRMu3+JjXwJG0j/i4RT9u4qJ+lqS/yCGap4lH4zZGzQ7tu+xZqQmcMZq5OBZDL4QRxQzRjkWcGt8IVw==", "signatures": [{"sig": "MEYCIQC8eWna0dDtJiQAHI7UMTA0vAwW49VVFo+mQtgtZPDeNgIhAMxcC/ptYaExYXaeI+qZmzEDeH/YKTHKeKSfK6ggEEOe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.18.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 113218}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.19.0": {"name": "@eslint/config-array", "version": "0.19.0", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.4"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "3251a528998de914d59bb21ba4c11767cf1b3519", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.0.tgz", "fileCount": 14, "integrity": "sha512-zdHg2FPIFNKPdcHWtiNT+jEFCHYVplAXRDlQDyqy0zGx/q2parwh7brGJSiTxRk/TSMkbM//zt/f5CHgyTyaSQ==", "signatures": [{"sig": "MEYCIQDlnEidVGZxdAgf15xfrlsAbTglTC6+A13/mdrSSY0I5QIhAJtdNTS8bzEeh+93sLwLbq+wA3O3gFfP3nZzlPeK/WVZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.19.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 314034}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.19.1": {"name": "@eslint/config-array", "version": "0.19.1", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.5"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "734aaea2c40be22bbb1f2a9dac687c57a6a4c984", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.1.tgz", "fileCount": 14, "integrity": "sha512-fo6Mtm5mWyKjA/Chy1BYTdn5mGJoDNjC7C64ug20ADsRDGrA85bN3uK3MaKbeRkRuuIEAR5N33Jr1pbm411/PA==", "signatures": [{"sig": "MEUCIQDpuxI8zljn1dpm6q4PMeoTXcr8BVn+mJkB/PG5AxZO5QIgFVPempAaHV/TlO804BGCp4zVDPZbqKgSp8+6HbXczvY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.19.1", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 314613}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.19.2": {"name": "@eslint/config-array", "version": "0.19.2", "dependencies": {"debug": "^4.3.1", "minimatch": "^3.1.2", "@eslint/object-schema": "^2.1.6"}, "devDependencies": {"c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "typescript": "^5.4.5", "@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "rollup-plugin-copy": "^3.5.0"}, "dist": {"shasum": "3060b809e111abfc97adb0bb1172778b90cb46aa", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.19.2.tgz", "fileCount": 14, "integrity": "sha512-GNKqxfHG2ySmJOBSHg7LxeUx4xpuCoFjacmlCoYWEbaPXLwvfIjixRI12xCQZeULksQb23uiA8F40w5TojpV7w==", "signatures": [{"sig": "MEQCIA75hiqW/TWB/v+DUrOGchIBgdnfmT6TUMkmgOLu0XpQAiBwTExgHxGPsQeLIpOaNLOQvTdjAdzLBg6BQCMoWPYSDQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.19.2", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "unpackedSize": 314532}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}, "0.20.0": {"name": "@eslint/config-array", "version": "0.20.0", "dependencies": {"@eslint/object-schema": "^2.1.6", "debug": "^4.3.1", "minimatch": "^3.1.2"}, "devDependencies": {"@jsr/std__path": "^1.0.4", "@types/minimatch": "^3.0.5", "c8": "^9.1.0", "mocha": "^10.4.0", "rollup": "^4.16.2", "rollup-plugin-copy": "^3.5.0", "typescript": "^5.4.5"}, "dist": {"integrity": "sha512-fxlS1kkIjx8+vy2SjuCB94q3htSNrufYTXubwiBFeaQHbH6Ipi43gFJq2zCMt6PHhImH3Xmr0NksKDvchWlpQQ==", "shasum": "7a1232e82376712d3340012a2f561a2764d1988f", "tarball": "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.20.0.tgz", "fileCount": 14, "unpackedSize": 318595, "attestations": {"url": "https://registry.npmjs.org/-/npm/v1/attestations/@eslint%2fconfig-array@0.20.0", "provenance": {"predicateType": "https://slsa.dev/provenance/v1"}}, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCIQCbeUDn4xFiXlEcaX5aRoGolFRzdCtHTJUrF04o2O1MUgIgKZUI9Q1g/ceHcewk56YihTzb3ZDss3ApFA9ObqNL4dY="}]}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=21.1.0"}}}, "modified": "2025-03-25T14:22:23.075Z", "cachedAt": 1747660588849}