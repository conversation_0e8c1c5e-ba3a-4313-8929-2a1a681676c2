{"name": "@radix-ui/react-menubar", "dist-tags": {"next": "1.1.15-rc.1746560904918", "latest": "1.1.14"}, "versions": {"1.0.0-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.2-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "90fb4cdf37682e6e05122a2547648a2cb4f48c70", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Q7XMzok1vZG3dUP1hbga42Eb/+f9Yv4Hr6Goit8z+YgATDH+PNx9UW5s7mQJJfvvLogYX9FZD1XKpc5bh7mtEw==", "signatures": [{"sig": "MEUCIB5K0dQzFbVjUx64qoq3r1KA4hEe4ZBRPuQVPJYJ+OK+AiEAk0F5orAXiueZJe8Q2kbbmyk2VcP53ABwhyWzhIkF9/Q=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxW8vACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpbPBAAgwLQb9SDbyzWIAnB8mR6102gkJlLqJHKyg5aHPYcdDiUleY5\r\ntE4J0fHWlK5sLqMilLOSxROa2zWBjdmjsVHzlKddZ1JztdZbNMmt+kajrBkm\r\nOkr9vtFAu2TCZKWj4LwqQcoZ/A+t3NX43spEyWuJxkHN3sEo7cnZwIJbT34+\r\n7Q1ZHgeLxPhCcIo5RQdtC0IW0VG6huebRxE7SqLcBkV5KDDZrRoTlkYAay1O\r\nGdWe1Y3wVfwo545QDHWF9vld2iN44LpRbObgVwz4c2da4xa5hYZ3DQTphyZG\r\numvMTvzl6YdKtQeQFRtRAhVLpedNqP7JF37i2ZFvFGAfHOrTFQhVMlc73E0w\r\na213sNIRfQT1Wur/RtG+OYlMJmZV6OkAC9+x3VJaoL/W+dI9nCZXwK7sr0Lh\r\n7E2kJ5H/xPWpdzS+LAGKlLSboi1vlerGyKIaAfOuAiNx6Ih0HdohnNEL7KDP\r\nTnBez/kajTe0oX+eZQEnYyEi2e2Rb9wMfvMYARShW7lsA50LPGZ/+SDF1+4f\r\nrqm7Z84ADxi23GwmGHE6LVhzZVbXIOsPE3FAU9SRmTvnngG4nGzadfro5Txe\r\nlz10OsnV1jYJ5vxjIfEvrfUbnzZBDYI8a9yEn9wHtFmP764PKMMZjEhJlPPl\r\nO9Gq2ovi7HgKANlx5Qj45V4Fmm36xna8eJk=\r\n=28rN\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.0.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.2-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1bfcd27d22187b0ff7729d23c73c5610680537f9", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-jEWzl0jPtQ0z+z6LAmnXEB8rD+Mk4jt3hlMA0C0CcW4rK0PR9EN69b2rlf1SU2H6/KoPKdgSeF1UP9ta/CCl5Q==", "signatures": [{"sig": "MEYCIQCk2tssDydVDSn/RKs3ND6xSFZwaqfrGi2xUT48oWaiUAIhANSNv2uoeXIwuVuIIO2YoQou7sM9cOSQr+2us317ed3e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180335, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxp2TACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrh9A/6Ap0X80oCxDR6cB2cT8o4u3wblUhOvRhDfhrRQ0HYMkdWKnPJ\r\nITDkQWPMWBrUbC4l0cFPaEkVPwkbYAQDIEq8m2BhCYx37krGfQVP+UnX4P9V\r\n5H7aDuRV/7UH5qVeN8kjI5fI5AKeouHgdpcqtFgBymtVrUuTQq8L6Z+hwuW8\r\n0sMLAOBU9mO1rofWXBfKZVvlRPtf0su395KSExmGDgpUNNOOPBek1u1MV7xX\r\na6M5btYEc2j+paXln/swf1YauajXMmkTDyON0OPt53u9Mk6Yoi6Qmtg0LWQ6\r\nZ4QHR4QrM31Lt69rDC3c0oPXMkd/4y985f/aBpZ0No03Nw6LoqtRJMtANe+I\r\nJ7JlchodjdHvrswT1OuWBUX030TnePSsHqAnJMZx6V/9VpdjZRhN6SCpB5E7\r\ngiZ0pJTCwVk58geD4Dkzf5UDZSHZesUSMnARV8Qg5dTyl18tfkMMo8BpE7FF\r\nWK2IjMWOGZ0TXlzeoS+XdXjhTpJFkOXNe8frNkeGqQba2zDq0/5CWK+TLKnZ\r\n5c3RA0lobwmBadoUpMUee7kYl1AnMj/Z8/1HdWQgJvDMa0n3frQ14aB9vG1b\r\nGvYI2Pi9oTTdsDvJTyRK7suwPkQfa5rT5RKFevlGipZOkF+SAB64RF9lCbJ5\r\nfOp7YgZMBkdPZr7jwA+beLTLzVSORzElPHc=\r\n=SDOo\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.0.0-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.2-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ba27227753ddd7f0b23d637cc8f11ad639323bb6", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-Wn/sRL6SCHasVBrrsgShA0JrNyBLtpdXYdCjDFylB0jz2T0F12q6u1hSJHM5aOXH8m0abJly6fuFPBGJ9f+GqA==", "signatures": [{"sig": "MEUCIEFoqI9vmQuvHVbF1WByuEkn+85JOAxvRxT34UexciyAAiEA19gfcV1R6rcpTS+621LfT7XNaikxhcjZaZp+Nv76hCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180907, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqEwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpm+w/9GMQv1FPHRbU+8Q8AmaCXAwHat2FqyNe1KgDskb/buEStWD85\r\n8jdCM6TkNPCaaMjC5H55kO0haIuPIG2DdS794tpnA/5usZ2APa02IGNOxQn7\r\nl90VJxTa0mX5T6UlmhNqT8oOa+O06Y9KeGFzIbrJeDCcUqxfi7o20HMSdtRe\r\nUalv/ZpkIzb5cQz5wq2Hz+iGEgPutaKbmIv4VLbiGXXVZkELLhKnExD5jAfM\r\n0Ui5hwwwoLwZ/y6T+xEIRgo+geF2zwkVd+86hBKPIv9Yv8XmwrvjKBhNlzdd\r\nBAIzrHX/Y0/UqpPUlUYa+JmVc9kd16GzJFw/GS8hDs+suBCIP/QUnf91LYx4\r\njILTXMb4c0lwWQvcMahZTJTujfYmgYCPEBEwX1vkPT9L3qwdYX+RS6B564g6\r\nBgEC4/iVnJouJH4ig8tjpFz0uRVYnf/vwlUa1my31gU49K31HaaG9MgLPgD5\r\n27Lzy7S0BpCFlIdxoOb5ZsnuCaIfHFFF0o+cmPI9kiKBcVO4nv284+4GQhrj\r\nPQvweiWHHOzXgjPj0KsJuF6WTsxu+TSClKo0WQv0jZecpyKxXpqHqn+hYWBB\r\nMKP3xc7JGrp+pDoCOZdMgixlYPQTxKFz7eP3vvYTftZjgo1ZwZjsy7Kcnzdp\r\njIoeQ2Ho8VMS9N0twprKDO8Lv0Nl+TV4MCU=\r\n=x4Nk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-menubar", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cb3843c9dfdcc808c5ef19eebfa68aed77e273eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-l3pWX1Xg4xUVE8M+bxNl2FxfmntfMjW5vgVNkmXqyyiWipmayOEJUs75jFKwtZvYI9o/S5S+20u+N8+h4O2OyQ==", "signatures": [{"sig": "MEYCIQCHht6yynuycFhiHjUcj7VKbYB1C3//tMbiNleSDFoQjwIhAJoBOz0ksz32ygHbAJIir6/nGGUelpNCjT3v4ZHfUC9F", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 180864, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjxqVZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr0Jw//be6mFix8q3Equuhn8bnLACQtNxA7OrCOCDWKAS1l4DQJYhcE\r\nicvQgngCP99ThcZVDBColswAyfpJkj2FVlIW8E0/1qcu2R4Y9XhasD/aFYq5\r\nxwr7ep3udE07THIZsV/rFADwn9FzomXijocdKHmAJHJGOkJTiWjJi6DQ4vtB\r\nvPW2rQudpGy74NwPbClZKPPJDsk6C97L2pO3uReSvg/Jjj11mdBmlIGoWh0i\r\n06OlNDI91SM1I8/2p/ujdXRX1oKL7LDDB4mQWVEG+yaLxvKIgED/Iqxu0H8F\r\nkxvyoFmCgV4ggTXd1UuGKMKX5z8LcTFTV05zU/qt5o6dMw7R7WhmMglmOi5a\r\ncAaGbfn8E38fekECliWcIgP2jwuckwp8dnc4RzzQmM4j+++n/PjvUNnaLEDy\r\nl6nunCwabuGGRsvS5M5Q7BAH1piwfpsQ44l43lj98b0PGW9TZHiAx+uShEK1\r\nAH+DU5G6V9zd88OyVkG7iGPArFaVTDA+XEI3wYynSRRlLFpcHBkN4iNWQimc\r\nr+8Ms6+vIRRkN142zwvSIbYSNW1pVzIwmWO8h+3Se6dIILFvUo7Ntmq3N7cQ\r\nOQTr8OquRS/6b3rbDSfQPdkfF9OzezMW5/7D1iZt7AmtNKJQ9Dflr8j9pPt5\r\n2mRnKiCZ68ECbaS0aEFHXrOGoDBoePIX05o=\r\n=B/mR\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3a3728fdc00d86d189de9203f6951f9066a43e86", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-8MfHPgy8yAXW3rmpE+pJ07NvffqR6rClpkXCHcDNchXL6jkA7TD/PLMp7nF8ETEGV+wE/nQU4ab4wa0OOCJGEA==", "signatures": [{"sig": "MEQCICFlLd/xMl7k3XbNn/1YBwHYm1ZwkpxyKDrV6aBHglkoAiAAoJXpe5mdmfn4Zrh11rr/e+wvk7TpMAgIJIKc4sWdLA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183853, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj468GACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmosnQ//apLDHAO2yBUaQL/CdfFC1ukd65LJDWDNL/79VD/yaUHyVWZZ\r\nN9iMICXe9BM9a9GpNTN16TqszJzKJ7PMgnFaESBHGvVgsw/NW6heE1XrbCIw\r\nX3KpB6f/cxLfKPVxPWQDHUGtfmIxmUPLjP8n3Rc+dzC4e59tGP46dnAs4cJ0\r\nl3lMhC8QnIkMf+lumd++w6LT4vFACqu/BjKAjP77rDCTBzQCC4JDjZ7ZDqhr\r\na9xU3wdC3Q1vb0/pI/N4vJi6iS6i8ivyliVDT8k+NX+OwcwkkC+XtGZRFyjq\r\nerL9kYD37jeQ7Q8sR4CdKKxC+m5UFGSHLcM6NtyiLdjSdBsx0jefTwGItNkW\r\njmJRIZc4BInTSJekbVrZjwXRmvRYr18fsh6wXiw7mtSRT5cf/bRf00XVAlLv\r\neNjb8WXG6Iakdj8v9rjRe7lUqQL7OHOxgckh8sB54bMs1pCQpaNVODhbyHHz\r\n3Z9y9y6gO+CLyxLl3TL93IL6wumB/jlg7CvRFo498hyDJlRiUMhZLBneKW37\r\nhJ4YBRfL+/DxM2fAreC4W51YbtmMgbCHrWs2fDaz88kNTQu9nLHAardO4cMb\r\n63f5FHCXi09ALuNITiN78IiJZOifq0Yfi6ZnzmtYE5mZ2sQzqMAkcBkK1Vv9\r\nPrvZcT6/7CscWEVVcNIaBimDr+wcB6h6X1E=\r\n=6hfU\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1bd3e084907b52b09fc532924bc46201862a2be5", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-AO4B5rRrFSkZrqAib47rXFkkLwt6NFX+oajWbadIuGa+FVv/+WQO6WOott2tUgOKZ0eeX+SD6UEp5Ry6B/k5/Q==", "signatures": [{"sig": "MEYCIQDS4WIluHOo/Q44vw9p6NL0qbTS3jcXvakwKF4/JYXgHgIhAMawQT95iDFwWdJ/FByUGp+9OBBlD1Nou4ouvu4z6q+m", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj47I2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrWahAAhT7DWneovYlMJGRNPuEPfm5/qCkJ026tWcBfx7wtPLjJbz2J\r\noyWSvUHO81n/BL5wXaXUm4wz3IXPkHPg/tOUekUajlrd2pP7429SJAyl2a/b\r\nM5GvQyqPYiQnoSI2OTPwfm3A77F/x6E5RvXWS+e5Kc9WUeD8he9kigcZuW4m\r\n4qbZuK12vR+HU4c57TaqGpU5C3yo/D26faomG2xzI4CruUT2hgXqfiN9n9FW\r\nsZr+4V0p7a0EuZ/vt50n7ExnyzdLq3zaNXlj3dN2bqmNsXV09DApjX9v9B+4\r\nTGPMRR3UB0u+Xr8C8R4Bou8hHUv8wCYd8lwca+78Xqh+Iftz2Cj0z0ZhmRIe\r\nZuLwKFntWtA9qgOjr/no2+3PtIHBX9coaLjZOnUZ/rOHeK683fZx8uIdv2/y\r\nuSBKk3EUviHuqLuMpGpT90CObY0z7IEjQWIQYxAz+hw+gRAlgrvRqN0rmSDM\r\nBbJ4gWxzflclcMcXVusDeX4TILyajKPwv/MsdJmoRxNE3Qnm2tWN4RVzCV5M\r\naXx7fMLUKVX/yS+hd6L1vL8uQG91OCibXbUovfy5knvibhG/jHH6mek5db/f\r\nwjb8QleNqtfUoR+KDp1STZPZtv0cLoc+jmhQ9YAbZJhOtv1HEdQfWy71Woto\r\nV5//VuaSxGKpXK5T7w9P3ir7P7Calkpi+yQ=\r\n=0Iw2\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "981ff4b1786816389f4c04edff47e2459ff48881", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-jcL925wVJvcXcFvcQ+2MObzxOB0E/H5bdx1qCeBeFWY9DayNAk0jNxWZupQ8TzEKzwF1aBRYjGrQLy6mqYj/Qg==", "signatures": [{"sig": "MEQCIA4+N9yRSoS8kAEMiELdLw06JyXf3EZUKZM6wX46nFD3AiAXzdFWJHDNkmaTg80w/kHpo3rqROUXEg3PLf9M/f7sTw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5MAtACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpO2BAAkHFGGo8m0NFQA1RLp+HjCreeUgbn/Th7N7TTvRKXmzRNDd0U\r\nI4uPr46GZGoVpe1i0Uiwb5LF/PymTBJPzPPyNe7IkXRQH3tyLO70/R01gkLx\r\nJTlC1GM7cAb+m8WWq1tjXCcRVpkskC9RXm89vm3I7VSsvKKfQEsTegM2q7Va\r\nR1uo7/KhW7xOssO4NZA63VkFrqsKpEhwDvyvadO4Vv41TooKQC+Q2K7Mrz6S\r\nx6bYpzwniKsV1V06//TuhHzCl6Gqyq8Axu6nfsiYiFi5R9uY2tiP36Vgb6TJ\r\n1VkI+63HpU+NW/DoBTHyZrR7WGiCxTH9VwnaTemxQPx8/tSAR9aXu680Mw2T\r\n2Q7jBsDw+R9pAPHmuAi7PCVQJpbdgTm7fl1KjqNt5j4VA5G9Wme4EtIdehJh\r\n45KV2oYTJ7vTCTlrB/j26KH2KgJVBWipr7nCudePczvFztEO1nXqYeOIXX3q\r\nqMRqfUJSokVpSxmI0rMIeiR21e2Ln7ij5OqrFqQbaTMlMjo7KWhdF7yF5rNc\r\nYEPT/QehmirWOV86rfwVpvBi/q6o1sobf1sQcAJxp1HGIXP1pBTBzXMBYRKA\r\n3xQ4W2Ks3DoRj76jDv4XAXieLmPtxvNvvP0Ejmpii5GVRBOYm+eNM9VkPdbZ\r\n7Hs6TPXG0nHB+UCsLHciLq2R0LyZ4HTh6ao=\r\n=Rf13\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c2ae84b265d4935d1cd6ad1652426dad71dfba71", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-6+avLrW19LpQEf3vuwBKsHXxMvY74znV84XoShwRsdTX5eWN5wbr1qeRcGnnMOzMfYOPHawja1O7x8U0jq2Cfw==", "signatures": [{"sig": "MEUCIAlU+vMwijZvwXJyuqURWiSjdVqD3WOcZkHi6igZwlHwAiEAtQA/qberfFp0Gz0jE3I1OwGqv5gCeP00uX8RQYwSX4Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5OEbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpr8g/9GqNwsU+yCBR6Omu5FyX/j/nQCwYaXGAAA/St0z5Cd9dU6V2U\r\n5qJDl3MLnjsAX+JJiUm+t+meLG2su7/dq67FvRoJMBqti+DOtWw7z19c1bKC\r\nuhT0x4iLbXeEGdP+8H5xeWlO0XHxI9O6usd5ae5COyyCBCzro71n2mmeS4Wr\r\naD5XadgFIR7vZXoZi8e+OeWfFX2B/dzYYARHyxRd2ZrF/NTK3x11UtxHBG/Q\r\nipApMAncb9jK3a6aHvhwploREcjNku/vUUjH70NE2ObL0xMVIe78+2Kt2u2I\r\nQne+fTLflUmhe6UpdKxK4oMpMhROchy0MrxD/apprqrGQafwnMEKybDcJU3S\r\nlL/HTc1kJhgV7QQ3K5XF7/GlZlNw5PCromvfdq0ATFJNnahBNzySmNl2O+M1\r\ncP+DSB/5Oj0LjwAi/PdFci4STUSbnIfWoDUbLIzTq8fLvUzSYDyKYAFlPOZz\r\nxh4s/uSfjtrnSN+Gvu4a2sj63c+lkfDLE2i7mpptFQqSxOc4ESPIndqxRuUa\r\nMjCiiPywkNsc9BLBUdcWEjjhafiAifYeQrYH2z9ZwnA3Qv/PluXXZQuZz379\r\nai4I+vVruuwZqx3q/q1pvG+fjQ97FiCymywx7ScPObmZCwLEkswVvoplt++K\r\n9Ftapqf1Wx/qWoPWT98eqrKbAt6dtDoallU=\r\n=Ui2Q\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5cf86cf00ac452a1315a074cd710ae943bffbd59", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-Uo7wKZvt48Oa8yi7NAIkVLL/ISE2+jzbH5/Lio3gWFWeUe4WD3tZxUVdEHxarGMGJKBzy9c7MAMs1n/XyEbf2Q==", "signatures": [{"sig": "MEUCIQDLnFd4pIQmsQMwHdfuMJpoFnJGtJXCMlhOAT4XRx2rvwIgYzONtWFosG0oTeea2sSM8d9DzI7rMpKjvm+hsUiy8lI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj5XeRACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLuA//f/J6bqRJqFrid77oSAY4iCVU7c+Zf8dsPCWqnVQmUazhErE6\r\nnFrEbl3W77WR2XafcsXT9ssvrWWazKK0el9hMAF7qsSFYZ0bKE9Y1UZOrSGp\r\nAOgqhIEfupJRX3qGpzMmqtRLr+8bjBtdjh5Ru/eDpGccyJlcCsMJswEhBEWG\r\nrEhilKCi2QUIUX9jt6FAa2EL3YGo+pKQQeM6FFu3Lnci/iePKjODOk28CGUM\r\nJIvvIAafcrS5IMS+Ln34J2xzy19l6hkyq9n5ocnCk/mE7iAFyv7pY3IVo9wL\r\naGcONYOZVkSAYTnHsxFxknwPzgLi99AeEoX9JHNPNf7bvdexrxdI79EbtT7+\r\nhJJnXIGjZ6LUMnSVQYJ5Qg2awNe7qhnl1zH3TUecwFp19Ok0ojcskY//T5vz\r\nSnbUH7gc5MxbVo9L193wKdsHjsac/f3uytMBrfN14sEVE3jSihjAykrseBvh\r\nh9YFweh5Fb8OsoQP+INCi/+OFzp5O2DsNYOVaZk0+qGrnWEVAk6IFxHbejte\r\nmztMmaAFLYD0h1Vh1wXMNW1nIdz3QsCTvmyxhpmXkN8k4i6RK+h7SgPJZ+DT\r\noMYKQk253/0yaNluVUIOSlWqXsOyb9Dl8FPaVLkzaN9XmBg0205EYbN/VlDV\r\ndAb6OqlkXDWag05eFnmcMAGDa1ERCN31sBE=\r\n=puN1\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4bdae3e5a858c17472b6fb379f0709ebaae29c96", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-OYySFFAQzv0QnMwwgiVb5BBqb76dhcgqqB1qnNzIBAMHI6r6lEymjvkfgjThndf1w+rTk0VYYqQ0bQAif1ljTA==", "signatures": [{"sig": "MEQCICDpcxgYP919Hnb2IPVyV36jpMHRosuFht1hgamqNUcPAiACurjQhEPISJ4CRCT4LuAHUuHo5rsmSxfayYbGqxUQDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj76u8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqFg/9EKde+DLDoxk2yBZdVsIbE9Pxxqae89F0JP1O+F/Xm2AgYXYz\r\nNrVAUVr38oS/KULHfT+st9bWHb79cClR4ecmp8cEEcNXslEC+EkDyboiEAzt\r\nbJ6uJgyUzyybFf1waKMqmuPX/SI71YwvAYT/SEz8wyVjaNby6QkNrQjmVFPY\r\nRT+0Kuh5mgCl6KDxk2DpttItsnG+DCe4yaCTIjXGLmxhE51QGdOt8CYKghPL\r\nifTY5fPpmhCI6HB2y3IS3+4Npp35vqOPWFoLmYCYcNIFleLO/IysvS+/D/yA\r\ne+NFe6BjqYgykL+49iihU454SthutJj19pxSJ8MUZXxgrF1fljaaQTxvtirw\r\nZnZ4UNz/BfzZ87h7F0JsJQdYYcjjDviM61i2cjD5C47AdtJExAMCV5e3ohe/\r\nVGibnTQzu2Kgd+U+d+Mlv34xNDSpHFkFuYEvuEy8z5YmcL06BC2Ih6VahuJq\r\ne5F57V/CSxGtI9ArLAYmQjoElVFK8i6CCzZf7xtB7WqFasqMr8sr6caqxLpN\r\nGzqfVuKa8w0NznPrUK3ULz9RiMO3f71pZ0ZEjXxNcMB+ej4xR8GHeaVxvxBY\r\n4Bw7or7jV2Til7+hnSHabh9wD5GLcXK+eDmFmtX1kvo56ZX6FEsgAlQ+E7Dl\r\n7/OQGjT9aPxT8A54Awo/RYKXFxjGKGvWCEQ=\r\n=ULBk\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.6", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "8aeddc22fa61ef055fa75f52548f6f996dfa6960", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-K10GxDFHb62onQ/IWPPvU8/+X13LJTTKBh4FcOgXzq5IFG+s9yQ8UNSY4N2G4tfIJjOrYZaEASi0PuLld24IbA==", "signatures": [{"sig": "MEQCID7pKUukZ9aq7Phf5M8KUK4Nw20rx0cUMeVC4U0R9uy9AiBrlxH5QHnfuvT+T1Axtny3Ag5bB+fD34NjGqJV/WVePA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9KvrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmooTBAAjYBfmnvUoMi3jxn8ff6yG9p6nkJSnRa2hSj2jQFUxbMDhYS4\r\nH4oUGDQWgU2PuKu2vxabLhE2kdDLpnLrrILSjGzcn8nRes6GO4+7IWnxvmXX\r\nXVjciJkU2taprO+tqrfaiHEpWkrAbDYhStcAWHSWYMavh/HgOhtSp/cE5hOS\r\nMvK7KSDkBpV9XIMYFQjeg8/hR8xevTB6B35huP7zVv/mEW+YfeDudguiGLsb\r\n5U09py89iWqS8t/KF+pjubAIoo17oHW9IoMyU8Ju+3kOk7A5yiEF69E414X5\r\nK7wBLH7JziHPMpSk66x0KuKunW84btzYdytqrgHOB5WrZ0YOpgIJ63dkdyZ3\r\nJK1xvskplxDbrdK+FcNUWYTu+BEAwMjkPENtcGd41Cv2U9Y4T2ovcIEcVQSj\r\nq/bDMeqU0s88iRK2rCIZXE9LDvOlW1uhjU+H9VIOGga7/3VFmDy+gA+RnwuA\r\nFuVJarQUn6DGtZvKrASHREa0YUEnsJudCcf2HjJw3nrJmNaHftc6zdEjAx++\r\nZYJ/1AJ+94aSJRxHP++W4wHcP1A2y5rnaMcuTFBIdih7mChEzKZYwC/j5Ujf\r\nrdTMk/wQ8l+ooK505h4TR9BpUBx9pH652HnEAN5FssFiFI4wpKw8g9gvYYpq\r\n8XphUZfes/4u8Z+JmKLVPit5ThkKJzKm+w8=\r\n=Sh6y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.7", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e75a26731fa3d716002b60c1d0051349b15a003e", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-IbtND9twGDxdYTZXAnJDw/l13yN3X4FQE33LtUe8gr0NozOV1hoWADytA4WJVwwvimCXmvFM6+5Bf9d+0eUZ4Q==", "signatures": [{"sig": "MEQCIEOFEWFQ+cqg+ydKCOVlzGhEH/lSrRmzJHxiLpWaYhC7AiBqyPXNmDAn4SMQVYHQVS9Q33UQHr8jEXKFc5r6i91jwg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9K/sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXwA//TVZ6GcAjow78VbEhy5qOu1RKg0R6hVefHY6vHYvOYVP6D8WR\r\nXNWHD45VDXzhiE7/TSYz02z2K5LBEjnClU05CGVhd/4cgnazaPj0Dru2B2JF\r\n7WJOl0Za59Gjhr12/5BD1H+WIoM9nDXYCdlli1ajvHQJUCeOocN95zliiSPs\r\nAphC7/IWw58HM47QMAda0K1vt/Bn2AQYWYSMYiH0lmdhqjysVAn2dDc0PUh1\r\n1xkBC4NwwWnMRxssXOQZIabh27wvbbUClcU+m1THve2sf8LKbRasTUuoTqh9\r\nVKsKuA+JcvTrtp73TAyhnU1ZkqJksaR39kGt25RHe53RPZakCDPCO7YZy53/\r\n3tVRJqGO39Kq29fCEgiKjK2Ala2/4vM0TwwgdavzhEazmTJn02dOH48L3WPx\r\nOqut5/QdOP+xHt6kIkvK4SapH6G8tLpBZZbiO9gp1CJK0bVPl+VOzGv6RBPx\r\nf3sJBrVZS1r2qxlILrLdm6cgI6zRaxVSqmkcJbV0cKCHE+QnDSVdEs8VvbPt\r\nfArNWeLWPJcqnhvJ50u1VJ0Lt8P//0JiVme2DCvQ5JR6L1JvXFc+8yEpGEL1\r\n7ANukgcKcxdWDBb7zBhpxyMqMuDxWr9b+vWEGkb7JZp776vXVKRdRDJu4n4m\r\nAfdnZ+px9+t7iLhAy4eLXeowWfuEqA0xY3Y=\r\n=9r9J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.8", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "4ebf5e25dc4a2e612eff3f5e59d146fc91cb5f62", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-fyk9SsgsUjwJuDGB7tTmu7RAFSHwFnrQg2jLVFDQBwucePEVd/Oe5igJZg0k3+Gt14IAcS1/mohQwfC+f/gmqw==", "signatures": [{"sig": "MEUCIQDGugd2N8xEfnYVbaP/m1CUa5rQz1HKqUBVhkhlNTwjGwIgPLGnBvp+G9TEeXzEnMfkSng3bmYr0KxEriCEMuL4J8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9fLYACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpT6hAAgGAafwaeQmMcksOZmyGPRa8Q8gcHCMCurxbvfgFXflLeOwdQ\r\nbn/NVkOcn0UilpZD6gfoCbLNrt4+a3qmA78lHHD6ydjvBO8XSXtObGnBil0K\r\nfmhxAgpf0BfTwjBv8lvdfFouxLNR69nOkJw0pvAqr3bvFe9DHh0ILM3jg54S\r\nwnYFKMLBErwD7LgNtVY5S5vSvDF0bhc+fO6uj0WMgBNDAw0I3dujFcS7kvxD\r\nxrXmW5luFEiCKbwbp/gMhSukSWMBmlnk9FKvWL6AwWc2bDuFhv/eIxOHQ5Ch\r\nwGtBSd0zAkohucmKgmn0hy9txObUqpALib5TGV+RnV97W6DigD2qPLaiy3TT\r\nZomrCycRPVfIZK17d8Q8W5h5Xm0jLGkv2IZoX47KOx4oY1oDo3r9Q/8Xz3u+\r\na3m4/UwvsORYcmkynhwDhBeJf9u+hgWEbQJ2DegnMZzf2o/J107HqU8Dmpkz\r\nrqBhN1/2UnaZCeZ/L5QoWMvNaMM3IXHhp80O3lk6jkTHfkwUdbCbjhZPY3TS\r\nKQ4zRgJZuEO2vJhhvPBy325zPYqAfhlYsW+jxxEdJ6r60XnMbKliRZTSus5r\r\nSM447IjtBJaur0JzJvgha+vqNWea/Wp5CxLeyyeUWIVuMkp41xkW8/jcWChh\r\neNQL1AE2rnSF/HvJO0UZgt33ZUqVjOfYD2c=\r\n=GHaE\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.9", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2b223e3de845a66b277f38223ade88a08eea6ec5", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-cUcEs5QVxGtx4kIgzXitB3r+gsYO/6pPM+BRC+US8E85e80peNph1JHiSF/ioSqmNCpw498Uy/e61xNYsXxeCw==", "signatures": [{"sig": "MEUCIByNmhAqaDTAdU8BckhcEzwjlaO98UDpEiqzU4tWUUteAiEAyqt6yl0aZ/CfhkeCR8PU07FDsrDZS1jqsixMMr1Gzeg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183859, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9foIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOlxAAjyNnanMNw4jb7KanH4sfCpKcpAL0PAllETpkkSrrIk2JHfqm\r\nL9mRtuLDiu8vb4mRGTf5qjw2ft52SdQbF+MsaNJrOLbpoUM7PNM6Q5T03AA8\r\nhGh0n9SMkVFQJ+Wovcd8ZJw29EjLpIOaacBTASurTzMBEfrHFM07uaxXOj28\r\nuTqqxLUtca2K+VmGy3fa5G7zAmbV/1w1Yx869EhINOpQEyswmqHHYsIgA5qY\r\nqxcvWPmGDvyQrjlk0cxZg61JYDUjzbe2LCd6pZFYqAflg8m4WoTDarRVcNgk\r\nSQEwoNx2gEHMdYws/zwhaINVQxDybGU9b5I02j5Rt66JHlb6JMjcDlHmSmpN\r\n09beqLROmZD2Z3x4UxdAtZ+UWByyLL0+90NfcvVnwjQEu7j5rG1uSgwaPGDY\r\nlZZjleqKkCOKLiQ1OPbRUf8t535zP94+7z+5YHx8pbURS+OtKtUd1Vn85u5b\r\nsxBYwd915z2iGPKXMa7/qODjhHTMJ08U3zNrqiEIvy1ao4WxM7tO14goj4VG\r\nYyczefFIpfIDu/2SAbKhkuOnKe0s3Lzp2OviyYWhg7FDB+kWjrpSioOrU/Ut\r\nF1qewGn6hM3Qj1wTa7LPUX4fE9U536G3mNGLIuCDEv9GdOgUnQ6ciJj7xFKY\r\n2K8Nfe+pUzK/xA+TjY0kvmvYRECIxsz5lxI=\r\n=Rv5h\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "95ec8cc8507f6ca4fa4bda859776eacbe8db89f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-54qlN/5Zwz65+ULpF6YC415TQr8WGrtQOYjfKsda/8hrAzeaW5rqJ6GdwoNCXK4a13SYgqiCQ3jmbnNk6aI0Fw==", "signatures": [{"sig": "MEUCIE+ascpqNv86DyqorBJn2GJWDErDPCLuASXaUg6j3v/TAiEA/HWjt2bVIsd+S9Pnfi/8zr4LOa1z5ddzYGcz3b7ooBc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9f1DACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrMfA//f/is4quOWj0KHEjzLWDiwgMe/WgdyE29d33YSbpEq8SXG/z0\r\n8LfvVwuzJC48WMLThkH0/LL7XH3PlGna18sMzCZVpmdgAYrXcE92UQ1kdgxm\r\n0UA9f9cVk0BQBkr/ZOp2vSe+tut1qryv80GZn1/xcpG+7JOKQM9pYJZcaqSl\r\nLmAMNIqye43yH7YLPs3/OdauuqNmOWM5HAURXtskthL76fLKcyZLwRB7RS2n\r\nZZXcf5tNT7OTxxLRdZkHdiAq08sWlx2v8Ud8S9Ph21rHb5pqtm/+5336bWYg\r\n7li52b8HAKZYqyoczVFtqTRKLPVkp/tmhrpyT+4l0J/wShUXaRbOKLmCaQuL\r\nLUCNcPvJZC4O7EDcIHK2QRTAbOZ23N3tXkXh7QpLIz0WfBL4ZxyLQ+QoD62m\r\no6TZm/qpbEv1ngPWeQucWE7Z0V8RBg+8fNiq5CiZfxkw9UWn91XdFzX8bOLc\r\nM95S3k4s+KVeZ7gYonoGTlfA5p+cdA4bDx58uYTTy9UwfmjnIEmLgZEMLa6H\r\nnN+oyReKNEni/2EOnC4IpgqF80ZUTDD3lvjSGjVvaNYGlmZViE4VnoPbyzA0\r\nRUK1RODAenoqQzYdj/efnwdzTNV2EKYFbKuOZPEDCJVgk9zIwvQb+C/fnrYF\r\nnUswRwsxIRsFaXM8KjJCd+Yjh7hTpW3IxxU=\r\n=GC+9\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.11", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "61ce463c61fc7214406756329965d9f0f73a4047", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-no04Ccak6pLi1GDuvkbUpsY2s1etrrd9Ux4Xp37ihzhhdMN+BlYKwe5kNaEhx5EX25SlJ7kilILF2IJGrc6jYw==", "signatures": [{"sig": "MEUCIB+lex4u89yqAuPsRHbYGk3O29RwjrAdpvpntwLRzh7xAiEA6ksA8/of4B+TL2lSZDKx5Y+/UA8k7E91/2MpUpcT/BQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj9hX4ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp7eA/6AiHQ7JmJpnnwF794uAlPAEOTc4pCo+0PzLcYwWnuTUwzUap3\r\n+iRgCjnw+kDWeEsdN6/yNXd+Pds/fTOgCU3fQG6YRgOIfMl0+PLiFaoHrp0h\r\n5boPj+N6Vt5vHw9jUdC/biSHVoXddgZencDcpl3eukJTmNfGxImMiAMUpd8f\r\ndkEXMCd72sjxVNG3Iyes/RZGEl2eaBD+ARJCI2IX+Vz+SogmaiQ+O0O5EKfL\r\nk8cMPjGipIj8ENL2xjqyr2wLVK51+SU0JxP5kmuUULxM+Pr5AndxD51F1lMw\r\nWtE2RMh5XIIhoMsck74LUoNQbvzA1khQp+/o+6NOqMIEeYMcjNWPcFcqvSB2\r\nmy6TwkBubIqXNH0+5ZySZVSaGHWF65Zr9LeENt92fjkdCQuXrKtJ5AtQoyST\r\nUHfok+iOEfbVfafexLRga7Av4QviyiuIS27fu7XC5q0ht/u9HGCBZ6Tj4scY\r\novOt9ExnSNs4LWz19LWeX5lWGipqAcpTM4iN072oWcTJMiuVfoeXSWzzBU1t\r\nipsV7v+P4Zq0zfZmOdq+NDXNUjN9CIa8VCYh1m4KXJb11TLSpT2OTCw+myIS\r\ntKPonV+uE/83VdzjMM3H1zQLfOt0aShJRqjVgGsZNWfoUxYtgP3zwq6oF5Yt\r\nJtYZ5kdX9lGWgxHK2Hx2aWKPzJjjOwbWYoY=\r\n=Cc5W\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-menubar", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3-rc.12", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f859c7c69f4236f87229931c0f7168f67742f3db", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-YouDF6awwEwXjf/HV7uoJ7ejBR+45PzfUWZxuVpfS2OnhDxKxlx5umGVWYU2ebY/3RyfXDIP2d/RFdrsm+nzJQ==", "signatures": [{"sig": "MEUCIF33gK6Rg3f7sGN2Z52Ibk3JYnO66O3gtNuhPEoPQ9eqAiEA1l5GiN3BJWXUMyqaHYsRSLD5Cqaviu1sDH88z2Ro3ak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183860, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+InpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoGqxAAl4hNSkHvyFy6E78lQdQb1jOkIVuyqdh+mCcKeRlmPecdb+fg\r\nRzLYXco4JAxTGS9QoEGVqD8AsqKmT5NyHHQ12HkykBuuhvusmqCXwzTsFViq\r\nCsb+5id6TWrhOJ3tQIy2y0ukGyc+hBYoYtghtj2KKZxK21LBUs/07HXnm6h0\r\nPf9kKaJ4nlDG/bgtsCiuCHdm/uaY0via39p/JVGvwNjxPDroDvMGibSTO31h\r\n9kvJxUHmGhrCnj6TpD7Ar9eyWPi/y+z/4+hjoZaxUZpUjyA6CgJ2KAu5B9hr\r\n30LrSdGUH3fGEV/lhXglxSi8meKxDahC2RluOtcQZVVOpJG6y1jRegGuMMO5\r\nPuKV9LT44tQeCHPggW7R0B7ioYsygPiFUpQia+tN4ol81l7RNTD5jAVQuBOo\r\nlyjV7pkimAIfUoGCOuSTr4gJfHXfmRTtdCb44VP+4DWuEITFeK6nJrW1/wTu\r\nss1COP+X8VpBO9l5K2n7klDFj009fkbTGWw1+VQmISdPxbv7D/cu2zwLjQcV\r\nbM3PSkrXruGDgsb3eS2ufcf5MRW3beNNm63+9wU+EiddG8yHp8rhvkx9RXsm\r\nJkRvxuD4EWaHvqOFq08fJt6oeHrbLGWqhK2XyJ+YljryiAMqZ2PMrMv9Noc7\r\nkSYjrdQSYyEftbtvPsYCgTSkS/w7Ea5DfLE=\r\n=AyYl\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-menubar", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-collection": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e5634c4b05d41735a58e4bbe99dbdeb8a3b74fb8", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-fqALml5BOsl4CYlaJ+VASQ0Tp1v644+8lS0WZZEuo2gQ+H35L8XT3dL2y5rV+amuxBWkMsSgeCVd602RzGfmIw==", "signatures": [{"sig": "MEUCIQCdPGTNo7rykDytgxGDkNhhd5I37KTurFVM6j3EHvVIZgIgMmtdO6KxlynApfEAU7YH/6iROA7sxES1aBfQR56ERec=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJj+LDQACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZwg//dQkjgjK3NC23FzkdzZUx517xOu28yPmBjgoAiX/3YmY3WB2D\r\njqENR1shBNOW6i+sMMaHR+hnzs/0DTw0RVjuLgZAU6jLLH98IqV2c2iMiyb+\r\nAJCMGcomnIkoDQbKg8GVwH7FRLkY+wwygMWkMsls5ABrsMGbkroR0IlU/wAZ\r\n8rJ67tuswdVXDhDcfmqGsWTyBfENwj4MQ70bo/P0gso+a4YP6SyJospE15dU\r\n1+HIyNprDaw3KlTe31E7D6vYTCPyzQSvG1vAUZRLWtKNcsP6QgjOV7BQmXG+\r\nJFlo5gPZ3RA8AgrdK3a6e7ScI6GvfzOe5FS4FWR9io4jEuvZ1THn/KpwOOUE\r\nY1ivn87SOZI5NHjosAZXwwq/Sf5gWW5cbonDmiKkMha+X6kzrzslsW8r7YIp\r\n/A0hnrCNB4yzUfYhEFFzWux9/pSIEWxBAzOCr3/d0lx39iQ8Eos/oxlCw+fN\r\nRNVQ4E1l3EBt02S06D05tZ+gE5z+7aXd7ZOenCRUNO88cdb4/lhf0c/+nuWS\r\nrChm7B6rY7G9QkmmfTqoFWNtFknCILwJ4sLaYvHXv0Uz0geUxcwfY6oQS1SX\r\nl7WtR54n5K5FOpY5d6/L4dcdvtOCXOh70FlnAoLTARKbSI5komPtTuZQxKLI\r\ni5gdQDfF8j1PnxmbEqIPKkhr7gjL7EdoqRM=\r\n=w+uK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.4-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-collection": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.3-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "50906f4244cc09b7e384fdf89a6a218998f1cd43", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-JFIHSfodjSVfXGypOhNJ4nkubhDgk/huch4PZtsYT5LF2IwQeyTtLtAYP1durhEdysqM/Kbdx92TMCP3Lv4KEA==", "signatures": [{"sig": "MEUCIG7CWXYqL5rZ+cztkIPlVnIMg4qyOHqpDn6nn+6XxbNnAiEAyA6Ag+jearmDDhJoAqm2Eeu6dQROmb/LETlyJit/esI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpVXg//ejVRwo5hIY7QLVqDqf+ZP0vQ2THlEYK3uD784WmAhgN22z5v\r\n218i9rlvX9+8JfN7IkajidCUEzu2QCpxPVibkZdiyF0eyutkEbli2PGTptgk\r\npbLIa5zEDKDxeatvapDy9iKLQEC1oJGNHKZFon2Rd8VVErHxD3TAZ9Et4e7G\r\nRkL9DrsEKiBrYJxKklR/OHtxMo+jQw+4G4YOFq3COJSAp4u9z6yLRvIBkFu+\r\nvMceAUiauIns49koCsFte2JBzWU6nX/8rSbkRVzugzqFmEyR3PjqoEGI31Kv\r\nCBLv4NbzObk5AlAel/mUS120xtyxhRnbB36DUiify3Mwu3d+JnLeMzLdaLuw\r\noLnwQLcguEl4HKzQLkwUyJ/v1/hKV1l7PcYSasprQ4KlinfgwxcpwhEx24GZ\r\nhVcdurkigoP7r6kCtTfZjgvnV8uJQNrenpU4qvDtP+MEw0A1xkne0sqwCz15\r\nOJPwdpO6cbqrOsAs/Zx8PaVo9uU0uADZczLft4mw9hASTzK+bANi6Z6GYYlq\r\nUFosZZVrLXCNX5bfNewKpFIDdUleFHMg0ACZiI/rrxRjFABu+mUWKl/AMtBo\r\nRWpdJLIWEz5lqleXgfLTj22hCftrrxCBp1PmqzpFXcUxT53ttD2tc478OS60\r\nUJ1818CY56jWQeGo85FOJLPp7Q9Y3gOy5Ms=\r\n=ci+A\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-menubar", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7eedcc905669e12f5fc0789bf28ab5c792ab44c0", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-woEg2ZODGoJHonr6ZwC01ZCpDifS6BapI5ythRfvWPBeL/80CX3u4sQKaF/58bbAZQsDnVwO5M9b0XVBN3jLhA==", "signatures": [{"sig": "MEQCIDuhx5ejj5yxPF4p3GVhKNYBS1HiuCA0MDZGK1lq7Z9jAiBWiZt+0SAy6ud/TMSD5IxMahGKrAdi8k3oIine/5bkWQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183820, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJawACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqUnw/+P7vWomY99xbQ/9x6n2KQFx58Nn2jLQVHDXKII1QelpvNNI4G\r\nIf50gLoobSL++Vu6P4BccnAQv3e3xSl/KEH0R6rLfgW2OdoWccRkgyzZJXCC\r\n5f7h2vQjLr69HYubz//HBH8ettk7B/QxU+CPp5/xI/GPU4Rcaev8UOQPDri7\r\nmcS4pzRW+PdvFY1HDprAr4pA4n2WuJzgkyrTSTSipUSdO2+Q0JYoOzluvxDO\r\nFLMymqVnTvMMDDPW+jcgJ6FgeJRTe9p8GXuI9RfvCnVCBvl5dPb5Gz1i8MCk\r\nOSf6Y0ql26Cazp+DzoQSTglzLARY38ptLuw6K+QSrCCG7grh61fnekwKemCj\r\n5usYvi6dETiwu3ZpsCzLmCL3qHXU62i49rmMDUgwLiIV+HZcxdBUzVpdKRMy\r\nwc3uvJ9zv5x/+twubrnKJcU8P7rg4Ppe60efgk/eOaw3GEIfzyJwNAPsrLzU\r\n7+Gh06u47/OjtTblEU2P9DlgBIRbm3EXFKZnmtiZ9D8Q4QDzyGIk/V8Ijhs9\r\nqREOtXifb5UvQOQwJICrfgpoQBUuA8xmgmQXZv0NXmu8OGbqpYW2CS5VEUlm\r\nKonlWCxUrz5psyR7Iox17Eg/BSVjQSyqPN4hm61wqQNDfOkbJNoLPNL+ldES\r\n3iYGMxpcY9B08IC+nzX+MI2Ey3p9GSPUj+w=\r\n=sO96\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.1", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "447b62c77e4baf5b3faf58c36ecfd620d4e296fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-yA8kU7nxi1mObyRRPiXIDynUT/rMR/gdchrxXyxUhLN2yQT9RbeH2WDgEwX0rJe4dDqIm5lz86T9NvdfARhcyg==", "signatures": [{"sig": "MEQCIHmQlWpHgmQ5hmsUO0Rq5vvWB8I0HXwgl9kcdB9QdqexAiBNAnlGA7XgnMWmXM7NoOZx+On9Z+cVnIagFMwSBiFzFg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkC0w7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpfVw/8D/dsFtY9woxRzd3kJORtJYgzJCjT7Mt1ACN5Krujxp8gjeOq\r\nA9/QT6RSSDOavqyUi1kkRx3G9UUMbKS8C7kNHeHDxcMg6fBmrovJR/7o2GuG\r\nx4N0D3Z8Fl48uK3aQK/hzyL6fWIDBTWfuRzG4SOtxI3JEHgvFR5Kc8wODXCd\r\nB8640oRHPQO9IbziIXs2eXKioHKocUtPQc4nbLcsrPtf+fRD0fZmorS9jcI+\r\n3CEyXxmi4dYYru9qVx2KzNIOKzfcHp2b2qxGrbdOSGXBqRo5TEcuC6WyQJP0\r\nHnCOzMvTv4jIq7O4RSVkxaU/ZgyOtZWWEavGhcy91PPzE5pZTvUvL6lypzXB\r\n/m0QDkDaPAXZgVkdPKi0rX1Ym2xJIqZ8653Lus1l8rnAEdr8FyQ1rCgAxomU\r\n/rRkGkBID/dKzZxaxKPG02p66pP/VbrwkDk+ZKY7ftn0LiK/XAk9UzTaTepx\r\nNog4uPmnuwRCJCrAYwz7XhKtjA4F2VXljKI8RVFDqjB2GpUc5pT9TOw1IInW\r\nKI+tOpDdUMPoR2c4knkYBd5DmIx4iEsg1Rz2ogUdxJo3qDYnlRt7Z7QiJRWf\r\nwI2G+aFq4g9OlBW8Q6j7gjBxWCQlIzmBaKmp+vH3sma7f9wxapgUFPhOEMrr\r\n5YcmhkJx1S8yZJSt+ImMB4kcTd9vNm88Z18=\r\n=ZpbZ\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.2", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "45c12e328f7b14a3a782e8867dc0242badd579c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-SF6iLXNb9+E0WEG129sSCVydsJpbqAmDhVDkw24sInfGvPGdX91l8Fg+hqSIpIWgSBYA96kjEjb1Xdt9KGO4Rg==", "signatures": [{"sig": "MEUCIQCSJN4WhRtY9LpxiAgIosp3puDU/+ylAkidvxgSPGiPEQIgEnmnTnQJKq64s6SAWb2fFckKZb82lSSivwi+Y+0/G8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkFHuIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpdMQ//SGqbiY7rGzy8Kzts5RfVv1PJYdAA/pv02KThd/0OwRKmPB6M\r\nKvgYoN0rRE0jy5e2ohS9KmJstdKtBq2Hj2W+QqfQ8xPRfYxJj2uwlqFUkE+x\r\nKMs2P2v6QBVPSv6upRiMkAvlIxOdrjnb2g9XznbrUzNGyM4PcTMDA5rphpHC\r\nteb1m3oFRa7LtFzeLqpU4PVtnvws+kFNrImKyMQX+n0Wr+bulaXoovLnmcwV\r\nr4xleTagrz57VF76KRusnmoc0PGzoq9XYqsUz5B+l1FSZCG6WY+9LM9RgZwM\r\nJNRb9bzvY2CRnJTQw3xKXQpby7u6vE2TQh//Pav4D8GWaoJGynlySqL5jMto\r\n6zznKbc8+6nieCUgftCyGLi0rahimOWHAFmb9beqe15kn8BqYOs6K13sTu7w\r\ndGzt524DVKebgI5RfqTsz7JQZh7ameewcFcUqYJe7Vqv79IciRfyPcynKMvx\r\noVoZZBZdn2Mq2DjE+YJBt397RMuL7OLT8ZDQJ7l0pvLdgAUmWfkY/o32weZr\r\nabkU928EfCFN3/yGGbNjj3jDoWnIzfpNN33HdYH0IhmrI40QwAbk27AHGnWj\r\ngc9bqJGuIcqWIkatAT68U6MSqh65cVqA9UzgdmmJU1AgghzumHuHGebjxtkE\r\na6m9FrYhOWlSpgyFKQ1mSbjFEPWmICx3Z1I=\r\n=lRRc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.3", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0ac80f1b8c074ff3ff3fdf1f76d5d81be74b8794", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-yHv3izKRz6Qq70xsT77ziaKEC4s/Tzkli3y1ayo/eSAtBjGp4frEc6FzarJCTsArSc9FTCcR0acHGsFzDMHx3g==", "signatures": [{"sig": "MEUCIQDoLBqIqC/dTUo92XUUsVQ37cw/bHYybtRrMOK714pmgAIgbl+Fb2rwsSbDXg8uYXXDeUjzWbGD7UwKKL3d2NuOuf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRnLcACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoWA//Rp5FFano83W8qGkldAIywoQXe/ZZDM/8rS982OY5x4rCewuR\r\nwvo8FmBkVbmNs8PjHoIQQCKlnr9QC2G9vn2epZlZi9LE5h/VYyxcqB4tmmS/\r\ndGvVcad6VB2tajFtxZWpLOadi0AlB3Uy+Q7lNyrB7ptAVG+O3Y6Sxn6Hwv/F\r\nLjPEqeuiixSsqyeOxkJAJEEZg6LcFS1vozB4Xe+UH5OFPeVhBeLKqFliwalk\r\nSDhhsTPpTqBooz5JXfKVozWofCGs1bVXF4te3OAfy0WX3xr+oKLZjJq+jh7q\r\nRk2e/mxoWhq/wIdAzCDHGPtyeCQD0Oh0mrHwzKPiUzRYmUjIwDArrLSyTwFh\r\nIqAymst3VYqr+6Qh7yVJTM5B04G+6/Hh064cwtceZgLUZkXXVK8mCHfMlQck\r\nYbvEHIAyJoqIojlQheWNBoChQVgbtS7/BHEvaSPxjLdRC/asKn0jCj92BxS7\r\nfteT41s6UjxncWx9ZNomOVZYiin4Qp9au1QHiJ9FB/+fsYLoyLMHic6c2M46\r\nH13T3UIltL3WbXolEr+71RNFeow3y0TI7zuAzLzNKtJUFhTsNo0GRyO0DCHh\r\nHOJK4T8BLFdRiNRCqOEMZnbv965y8zpKXpsElw/x8cRvbJ3nj4OkH/70tIMU\r\nCulHavkDNW5YZroIWWG83ANjGJg/rgdOvSI=\r\n=HCX6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.4", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b0ef92282c572caba5f323066612cd72d1336964", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-OXFGqsKKTEi80kdSduZ9xDopyZf91SbjTsSsyfOW+9RssiMQ3JHGfIe0FAwvvJz0lFoHQHdhAE50BylbqtmlLw==", "signatures": [{"sig": "MEUCIQCLkOiZUJxwiGlD0bvi+QrtcKRfBfBhHLeyiqOSOSSPPgIgfCsEFxTlBMVqdc45lU+jFBbqiAhEOIWuDyGlsfJgxWg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkRnnOACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowfA/9GepHLwF6nFMBd+UOD9Fj4zWpw7zjRTvjf1Swn1IMvEDptLv8\r\nBmX/+NMh7UI7E9q16StN01/NwtvLFAMy8wIdxA4fu48lE9VwD44ZWANfz/ro\r\ntDhnPuKVywwR5gMbXM7Ov66QpeJDav0+EGqFnUAh8HU8JUW3wYJX8pDGGUeM\r\nrS5EdbyllJeesesvxUV5IhGlv57XJBT++18QsSzuhSU2WzqJ0imHUCgxUG5D\r\n+FGHTfUi5N+41mrE7osJEXRAkkvofk8qvOPFCj4s1RbRcm2itBEmem15BtOp\r\ntVMW4sBTlE5Ziy73uonFah3gx+QQyFGh2oSN2XlvQlfXbLwrUVRJNtpZEDCD\r\nCmtKX5E/UronNnM+xEgQuaa1u7VUZA59dmFmGIrBt7xcz/uJJtVv4dcoDlnB\r\nSuZcQYsRI0hB/uVdWxxMBoOJkEfuLFQHooPdIor7axh5HK6sctTD1UfUJTOF\r\nldcX37OkSvBuvUjNZ9iGe6V6fz2Ez+Ox+Xo6rwMLhpzgEMNHRdVISZGJNXHr\r\nr5eGoaOUb4TbPFmqweCML3m0czsJ1XLafKTvjUaIxGZ7L3MgK2adAArouapi\r\nDSGC5rPVJHzvik3YdoG+FZf6oFlfyY7k8M1wDsbX2vc4wh1uoR3JiCtPWZUl\r\nY7SeEndwppdWRMolwXjMKAPsxMiu/hOaPIs=\r\n=chBS\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.5", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-collection": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "055744f9a94147d5260e47949028d917f2d3e35a", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-cryP6Jh46/knH8B9Mc2RgG3QV5uaNeHWvlBftOw9Mqoi2OgMrYB8dDipoIC/tkR0hp3wcxfMW+uCjS4dsTp+EA==", "signatures": [{"sig": "MEQCIBoyYPHwCtXUpYFJY0qLzqjI/3jLMWIe4T37nrnzyr9fAiAOVdBPhLy7VcGYh+lg5dpuTPYnfkZ8LfpYBXJoQsNpWA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183858, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkR/kJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpxeA//W1VueE2jpNbMoMcjLtPJd5luIGi7NOPT0iLMeKYYpbjEDtXi\r\nq/Xx5KFflyFEPNQrcmbk4y3/I4SyJqu0FkxIVJNGsb+s5M+r2h+1UjAsJVWy\r\nH1vpn5NAFKpcLi2G2HZsPLOTzvw8z9wYuHN5kMz05pjxHTqQN+B/LcJJ47jE\r\n5IDfG2PIUUuFtlCTX+IsZER69VLzW6GmQNdZ10lxBlKP1AVbweoy1zJm4wm2\r\nBIlL+UduRNoh4nQGaRbevsh8vI1QHmuLUgxDw8QyipKCTnLRnjm7UvSl0V6/\r\nKtU0Jx9qU8F/73SjUMS8w/JXracBGySmCclZEsayCScJPdYuxryd3HFPZsTC\r\nX+lZIB3yiuPzUzJYhd9M+AVlOHo4nlUx49vaJ4ul3g/5iKK3ND7zzLcV0HFD\r\nbYopEbQVqYBFG3eWONUjY5rz7Bt1KAb4WSrg1EvBj770h1dw1xHA4d8PRXLA\r\nn0+Cysk7SqUHz67C7kXt4iYdqmNz9KoBP0vr38i8oa3sp44UHb7+s5MqcE8d\r\nic5yHkJNBB/uDSiSXtiFH7JJuzs9fVqLn33XjX+cKHw+3LHqQU8LnczNhtUw\r\nnEhBKBqZdtir4BmYIA0OciCCvbCZDH25ZG1xHC0fl3RX2bE4zMd3bDwl7NBn\r\na426Rxuckj1XjwBHky50GNUij0kAwBzkUpM=\r\n=S97K\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.6", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-collection": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.4-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "80358602d2ca62a572ea30df9b051b090cd80278", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.6.tgz", "fileCount": 8, "integrity": "sha512-RA7rWTGTJYKlE720euczMdNkZ4gr7AakLk8lwrHX1GbZJI6tXyJ6rz8+A5OZzN1bso+q/BYjELcfyKHrPtBh9w==", "signatures": [{"sig": "MEYCIQCidsBrPo8XvsnUFv4XwB486IiTEOe7K3Mk1GpzV6LnxgIhALtPEZ3H3NCq2nLsF41DE480JX50VoeWlEl1GRO6Oofo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183873, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8xGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrLhxAAje50/4PyDBj3/oWfRBY6XD/EE7LKt35rPFBWTw9JjADZYAjx\r\nqn4eD2xcRgy4tnXdH5LSfBavbw7VGSGS7Avf3ZgMpCC/3Q2QzsRaf7LF182i\r\nWleDd9EtHF8QAsh8CakWJ7y9iEI66uF5tWwEHLW8FAMK26f4PMyCzhV8i4Vq\r\nfSN1J+JH8cY3SAvhIIaJHGO9f2/GeAarQkzwJ2jgD6sLkdOdLuBxLHGF8xV+\r\ntwbM+oYJk8W8CXVe6irnOsAfBEDCf93YmkK8tqRxZnWVO+JoSyj6DdGXtz4h\r\nRYrxgeLqofZsNmMk3d1BrRid+YjPQZQKCNC2wcxPKd6+5xLaL2VMejxZjbR7\r\nhC3GxkuzDeezD/EiZBDMnCqoxdD2FHFvryCKMKQUlfk8acji0xA2hH0aKvJV\r\nEvj6a3FVTvsVttcWaPrzRDSKrN2+/pu9hcSZdnTOb8xYmZwx5R8uG61uYmJ6\r\n24sZJ+Rivp1jBLdS8gsaTdQCDVLQuKLZ/+KmCUPRnmhqMFz3mq69cGdgyrSo\r\nE9/PiB25ezIpGPwRfPrap3j9Qriq/zR9y0ttUkx3N2U4ihjiOjioQxyZkhBJ\r\n57bikGrdfoCWcfaRV46qyxAeyZvJ7U2CHqM9zjjGv2e44kZxL+Ph83Mh3dHM\r\nZ3FJ4U/pZYK6fF5kLcUWwxKAo0lTq91Nnxc=\r\n=z2Zz\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.7", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-collection": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.4-rc.2", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c19933c4d5e0a59c0b9c6094cabea3c2682b5082", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.7.tgz", "fileCount": 8, "integrity": "sha512-UvHyuoxaCLxlpmCteJK9mHQXuM9/PQYXcEOWp4Nuq8py4sAw3l/K9jKopYNGKVyh5WIaQx1UqgIOvaeGirZmag==", "signatures": [{"sig": "MEQCIFanC2GS2vBGrnhGNuvFbfB9Tvwl+UN4DupR1fAlmUF7AiAlicKo0FpyjGKL+fvFoRBFsJk7FhiDMONhvg6aSvirNQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183873}}, "1.0.3-rc.8": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.8", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-collection": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.4-rc.3", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "2026a32842f147f0b12ab0b0fcc4cac014bc410a", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.8.tgz", "fileCount": 8, "integrity": "sha512-ENh93t9CIM3OVNWzSjkkn9uQ0m1rCZzqWOywDmjK2MFfxGVkJXiZzTiS3wt03k8zdVbc93N7kzcnlyldKROe6w==", "signatures": [{"sig": "MEUCIQD635fCtMANJ4ge18iMOUwPPT6l/eO9HHJzlGDBiKHOPQIgQT9aqb5ZDvhRJISfp8sKrhogDzQBQO6gssSCZIUTpE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183873}}, "1.0.3-rc.9": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.9", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-collection": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.4-rc.4", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "cbb5012312b8411348922f0d9fd0e2e9c3f2e1e2", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.9.tgz", "fileCount": 8, "integrity": "sha512-JW5Y5AgRWM/mgBI0WG/EaMu9erNmxSvMe0FzhlMPO0E+iMIJB1AJ67n60rFbNl4VHdWC4lanZafu5sZ8eDJFNg==", "signatures": [{"sig": "MEYCIQD+c70iRfOabdDaYE5Tuc3BZFb54CBfaCudjT6D4HA6wgIhAKFke/ZYF9s8AS2SPia6kkfLu9dirdlSdKvesdHtE4/X", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183873}}, "1.0.3-rc.10": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-menu": "2.0.5-rc.10", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-direction": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-collection": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-roving-focus": "1.0.4-rc.5", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b829e447765d450baac42bca5c28a40ecc2a275e", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.10.tgz", "fileCount": 8, "integrity": "sha512-XgrfKJ9G0J+5vOu6l9u7pB2iBz7RddZ13M64sW1TX/p0TsM31PeEIVP82sIh7e24WGG0wHmk5Noh0tby6V+HqQ==", "signatures": [{"sig": "MEYCIQDWMwpyrm9UZ+85NgSsIg62tB/HX47wd4779brtAA4QxgIhAKN2g6TmGjWnWn+LvMXI7gTETsODnGgFkI8peGATPzMA", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 183875}}, "1.0.3-rc.11": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.1", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-menu": "2.0.5-rc.11", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-direction": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-collection": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-roving-focus": "1.0.4-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ec56b77eaeaba4bf3a7adbaa4a48429c9ab2f495", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-Tg5RXZeYbxnEG0Sd2D4WajjdPNs223VDaojqwmGL1f7ufcMsp74d6z2xlmi+GK8RIrxErf8lk/WzHrI6vl7iFg==", "signatures": [{"sig": "MEUCIEGGFomRe44lDqfiDsJERYPD7Rs6ORqjvlJa+nHLcDfMAiEA8UWoH6aj8B5h+VPg1DT5eQv7Qssfw95ZBtwl/k4b4fI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191239}}, "1.0.3-rc.12": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.2", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-menu": "2.0.5-rc.12", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-direction": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-collection": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-roving-focus": "1.0.4-rc.7", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "76909bc130274ac49915314d5d33320019644460", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.12.tgz", "fileCount": 9, "integrity": "sha512-iZu1DEhYTOq2iMeSdTIj5YaKq/GRxQZ6Mt2U/HrfkRpCtyKZwdPqB9LtndMMsC9EZ9W5S/kgUWMiResr1F/YDw==", "signatures": [{"sig": "MEYCIQCmVn90Iv6xKKb2u/SogmaC/qvS3/6EBo93AuYm5tD/IAIhAITn+3muRXDQOqy3eZJNn6Onls6luFL8Ep2RXZ9h5q81", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191239}}, "1.0.3-rc.13": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.3", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-menu": "2.0.5-rc.13", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-direction": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-collection": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-roving-focus": "1.0.4-rc.8", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b226b27dddec05c78b4d784978c6dc2ba92f9daa", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.13.tgz", "fileCount": 9, "integrity": "sha512-zXO3kus0Fj394LKxAuT1op0It8u0cQ5Yx2skDPZYOoXbNEaHmVRGjxdbox6NTQxhC/Oq8gu6NgWgIs7vILgF3Q==", "signatures": [{"sig": "MEUCIDe9NruDin8SR3LJo2P2B2pZwkcPhZ2SwJy5e4rrKVYJAiEA32YTyBUIFSx9loHM49rwSEmUh0FLFZUdgQW02z9PvXs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191433}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.14": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.4", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-menu": "2.0.5-rc.14", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-direction": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-collection": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-roving-focus": "1.0.4-rc.9", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8db6b18f649a51df1995ccdf211ace9d54c82ffb", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.14.tgz", "fileCount": 9, "integrity": "sha512-7JptNA2FG8dNH+UZkuuU+fz7HX2ggIZJ+2xChJsNF8vwRc93dvC159VOeub844VPRW4zhLPHGwzNogmqabiEIg==", "signatures": [{"sig": "MEQCIHZ7fuotyKNT3oLDOvuLrp3D4BXGksmeTj8dflZ3B4UVAiAB6YOHK0FFLwT0O0573ib1qr1OyXpHe+gxlizvF/1Bow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191433}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.15": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.5", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-menu": "2.0.5-rc.15", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-direction": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-collection": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-roving-focus": "1.0.4-rc.10", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "633d7b93f9e2d9c4f05d45c572a2a44fc9ac87d6", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.15.tgz", "fileCount": 9, "integrity": "sha512-en43DvcVWW0BB2dmTX3m44pDp0OSRyALYrSIVe5FlDx89DjyOXnXk2vUb/eetyNH9OJ8IzhXuXmbxJQSjwHslg==", "signatures": [{"sig": "MEQCIBDpjQhjHFa4epZYJLF51tVY342RJGzH1Clu0/tz6FFPAiA2jWnrPtt8TUbwh37gH8F6aruPg5ug84qXE3pQUaks9g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.16": {"name": "@radix-ui/react-menubar", "version": "1.0.3-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.6", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-menu": "2.0.5-rc.16", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-direction": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-collection": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-roving-focus": "1.0.4-rc.11", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a9e0dbbd8f6dd4daf386f7e63383cc77becf3253", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3-rc.16.tgz", "fileCount": 9, "integrity": "sha512-11559AIX7/Kj7v305AhYTAx6lCM0oZaRDYXg/rLlbmm/bJEmHr0g6e826CxKVPh5JbwVkx64Lz3T2q+IIOBA1w==", "signatures": [{"sig": "MEUCIQCR505oci1XpRRO4UC8fr7Rn4I16NveWeCwntb36CvarwIgY/LsC5AUIudAyICrptkji6bbN0ORoA/j6Bakd/kQxak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191436}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-menubar", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.5", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b731f1cea1b4fc566272fc7f5b6bf2dd06c93894", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-GqjdxzYCjjKhcgEODDP8SrYfbWNh/Hm3lyuFkP5Q5IbX0QfXklLF1o1AqA3oTV2kulUgN/kOZVS92hIIShEgpA==", "signatures": [{"sig": "MEYCIQCbwaaSfouMBKZRqJ79YDMccjSVWTjK6Ox1dq0rNUMF1AIhAJ8JHoMvwmyY5XaYGs+VguhqpWTlcOB/cdkSer7bBuYk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191348}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6054d5bb1dea158526a0a40aba0897520fc3369f", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.1.tgz", "fileCount": 9, "integrity": "sha512-kLjiAgvBvvLRgKV8kB0HRsAN0KekvbU3DBhffTDF0pUSE73mDYUeiU5QDBherx1uPrLPoR5e3S0TPluB//G0VQ==", "signatures": [{"sig": "MEUCIQCBT3AX+br7lGff/U126p66ZM5qfoOiseZyKlZbP1TpewIgdIAwnra3z45587zItGvhPeL0Ur+txH+nXhCVUMEECxI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.2", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e8ae39624b487b3c90c03edf53ebea55d8518dc6", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.2.tgz", "fileCount": 9, "integrity": "sha512-Af8MLRAovbeAuIIVsM+cu/x4Lv/bBDhEJr6yzn4aBh/151aVytMRcIMx7aZYa9jE8GTLIz4psBf3Jk8Lxr9obw==", "signatures": [{"sig": "MEQCIHoJ7Aua0O/UFrP0aMZMObsqI5UYccfIu3updnFtwakAAiBlfzwesHL0vgl7IsNFaM7eMgxYyDj4IFdJDYGF8cX9Tg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b75995ea047bc6fd79826f2e18b58c66cbbb0032", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.3.tgz", "fileCount": 9, "integrity": "sha512-n9JfjYe17/DKrAp51K0vId7ktmQuVzI2o1ULa93qAJ1I+v0fuwHxfJC4ZCuCuk3aqjOaDF5eosGfsGJtT7YfwA==", "signatures": [{"sig": "MEUCIQD2ckYm3l3Scu+P+xKJG1CPwvoBxuKN+HChn8EK+fX87QIgdHLKCePqszoa6kZCHf39cUJcDCCMCUT8v6eyTFD14PA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.4", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eeaf4857a5de72d1cc305053c6a20039ab580421", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.4.tgz", "fileCount": 9, "integrity": "sha512-LEn8mKucCol+LxNQz5Q7fDofvBa0UmWo7euqKmx/iJ5sIYVCuPa3ruZ2s8KIUVqqFHQ6FY3mDl8z2R4wGEfi9A==", "signatures": [{"sig": "MEUCIC7QlhaoN/dqouuepvmv5HOaY/v3iPFhl41lKsjm53oMAiEAsJ10uHV8urLV4oIsddBy6zM1nUbTiU4c1LfZ1QwFm78=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.5", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ecd3f787a47474bff137eed360c1d0eadf4dd050", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.5.tgz", "fileCount": 9, "integrity": "sha512-9VFOIo3nUY6/JdSwstBrHwjbqeYq9JCyOUaIbK1pM5k2pwW37qD0akuSZq2XXJCxXgvEUDAHYuKXbPuOcg2z7Q==", "signatures": [{"sig": "MEQCIC5qV0AAqq2LanBkMC84X2ohnpy/Oxp/9+kINhoAwvZ3AiBiB/6rQbHm7TOmL6eLewjJ5HkRGrgsgolb6uM9JlnqRA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.6", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "298687af2790c37a6d20afae012c20fdb5fb033d", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.6.tgz", "fileCount": 9, "integrity": "sha512-X0xrssZs0nOLpMcpCmGgdATxgUxZJ4v9sLSCEUtRax3QZQwVfVjQ92Xp0MGnDusEJ6x/CjmfkRppyrxJhYWDHA==", "signatures": [{"sig": "MEUCIDGsKj2Z5AOPAHCOhliB1gSwFfFYthnE+1zCrDUbPG6nAiEAn8k91KKQJnbNigmPawUyU8D7yhIzOBDwjWYBSYPgPwA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.7", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7502030583cc40e7cedf13b8331047db43f48472", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.7.tgz", "fileCount": 9, "integrity": "sha512-BgzucYoLU6+qeQt/zlbXEIOmYuDO9OG5ttLkJDUu1XCWLjM3C5dt5J6RfqP7qetbl+ZEUBGPwYNJ0WdLl1JKAQ==", "signatures": [{"sig": "MEUCIB+PEm3oRA0eCFU3ivDwFjc6SFZtOx5VMZWQ3H1zx3jnAiEAvvQNeNBi+acjzYHNNXHHxWfYeke3O/tCTetbPzw02zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.8": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.8", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a16e9461fcd7e4246d9f3808c1f17f53ddf83d83", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.8.tgz", "fileCount": 9, "integrity": "sha512-4Gh9oRuguP12TK6QEQlGMWMC1y9WFOJCwJAZMB7sI4bQPS/F5wlJE7zZ/uLqrgpM1K1IoKhbcxiVlr793/Zupw==", "signatures": [{"sig": "MEYCIQDTJVuGIYBeFE3zXwDaXTWbYffFFODtHIDU7SRrMn5KKAIhANwcsXgZa3ubYO6xywon4DyTTh0yoy2xarkE818MTmm2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4-rc.9": {"name": "@radix-ui/react-menubar", "version": "1.0.4-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6-rc.9", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "005582b395f64113f9efbb52225c06e4925ff73d", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4-rc.9.tgz", "fileCount": 9, "integrity": "sha512-ZiNqW0tpkF3avR80hSlB6iI5BgD4ZdNZ1e0gXukvoQCrgT9z/hZJx6+Bp9H0SpCMfZ4jC/9SnOemTNFKLCxLXw==", "signatures": [{"sig": "MEUCIQCUnrtEWmyz4yZ8j0hvV003xmZD9LfJJGoB5+1XZG314AIgB4sINnQdQGa/7SYM8vDkHtcfM3yKLaMPdu3FyIigTMQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191386}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.4": {"name": "@radix-ui/react-menubar", "version": "1.0.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.6", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7d46ababfec63db3868d9ed79366686634c1201a", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.4.tgz", "fileCount": 9, "integrity": "sha512-bHgUo9gayKZfaQcWSSLr++LyS0rgh+MvD89DE4fJ6TkGHvjHgPaBZf44hdka7ogOxIOdj9163J+5xL2Dn4qzzg==", "signatures": [{"sig": "MEUCIGlM0rxM7LRAaqreef4MX24VkzqOM4p2hvnRifbrYsKCAiEA94F1tIHFfJMFnrjKWcHs9vleuu7/EniHiuZ/ONJEYU8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191348}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a63828840ec15c0c14d9a79287f894d434b5f42c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.1.tgz", "fileCount": 9, "integrity": "sha512-Y8hvSebHoWRR1Po86uaD9JFggK+fzlOFkKdIQRnGvVpvNWIbj5/xpdOXgsgcbSRBLIoUp2/HXVpaxwomaDLz8w==", "signatures": [{"sig": "MEUCIQDuJmKglX0N42fPI523ZBg7ym3LSQMczVWpdNBAFDAtLQIgClccXTYL7foIJQA7ilKGmSpiL8anv3WRJIPbfqqsuNo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.2", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d74c03585ec7c4f133f6e1af81c37c30d57e9051", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.2.tgz", "fileCount": 9, "integrity": "sha512-75uD+7tNYHCd7+oo2uhrC2DCyjCkcGQrenb00f6lgeH9Sz8O2fpAAR8AReBfP7zBtBz2f50fK2Qe6uk6n9Guyg==", "signatures": [{"sig": "MEYCIQDkmICBq0EqKfT2X5JGMzCq7ED2tHIQIugaYsWQJQjYMQIhAL+TuUddpZiHvXWR7LNn78Tf9zP8Hj0UJTEd8Q66daYs", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.3", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a620dc27cdf987d23013ba816066a4c066f1a412", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.3.tgz", "fileCount": 9, "integrity": "sha512-MafUAUkPVOivO+iiy09l102EP++AtczGmRGUVeS1HLApfpGwVOnbOpA0dECaoCdvRvNOV5Y603esLrg0jspd6Q==", "signatures": [{"sig": "MEQCIDyILd399EFA0+9L9/oqSlvdI3QuYtnb5scp2CA9do8XAiAqT677n+3BLbSYHgAZrqPf/pUzxohnAV9JqihYwDQ6wQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.4", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "179966cf9e406f09fc83a2eadc1976c2f2417804", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.4.tgz", "fileCount": 9, "integrity": "sha512-0umazjefx3YzxJw3bAlKkYJaJdODWl4xD9LJ0JbwZgi0MyIiKCm9OdV8avNjbI9xaERvYVEoYo7T3SJ08Qwjsg==", "signatures": [{"sig": "MEUCIADslX7uINi5BNNVIUC0esdps5qvpJ68Q2hMWjYyruFDAiEAvwSqjCiDoIDXKYGC/gzl0JbBVeUqLXXztRERMBPIQRc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.5", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fa7c46efdd71fca0ed190cc496a2adc7d7bf0962", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.5.tgz", "fileCount": 9, "integrity": "sha512-NICqVKYV3e3OUSJ3yYPwtguRVRBnuURgShhEzEGBhPtlfTywkRHRsi5kd9A+BgSQTxul31E+asGQi8CEytHWzg==", "signatures": [{"sig": "MEYCIQDamulOnNJgBGfnpiyVcSydV3DlzoI5DPNPF6XDXzkLdgIhAKRdopBcSXeRwEJEB59ZN74cu9JBv5x8/hujmgFsEWdj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.6", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d81f2446eb5d2082998d705b871093b03103464a", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.6.tgz", "fileCount": 9, "integrity": "sha512-OS2FNbzT3wxbWO5laJRFvSVZdJo0as2CCxAsAlJ0yTq8SxPzRehxZqRP0fsbpDGKEklgDWIpUWdn+riVmxv4sw==", "signatures": [{"sig": "MEYCIQDgqOVsPeh3Aeb0i67ZTNC5bRzseU7hJIt46tLXDWrApwIhALmGZRvuDY/fw8Hc157KNXEq+kvzz5iq4vHQI3tVdAm/", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.7", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.7", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "42a76cfb8c3d328c09de75f368bd027d880ef021", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.7.tgz", "fileCount": 9, "integrity": "sha512-8ok9qpa+sPR76WENUowdexX4V90T2KrNwuA7IEhOHTeHewE73wrc3CdBwFXbK3N6maY/LDenOKPfFIGRRMQqpw==", "signatures": [{"sig": "MEUCIBRAJQ3jp36jYPk9JHUoQnYu0k7gZJvGStVw4TZj2H1NAiEAhxq2sTgl/7SBy52SeZ6STxPQlxx+vYXpnlgV4amLcJU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.8": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.8", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.8", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9ede86d9d4d7fbc016f577191054123146be494e", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.8.tgz", "fileCount": 9, "integrity": "sha512-1kIJRmTooVjANQUZvp9G1Bx1gc3B/4ZilUW7tbvrhx6mXUtfbvsmeHyOCbjTeF85u+yZIuA0l078ef54AN/V1w==", "signatures": [{"sig": "MEYCIQC4na/1Lon+iCMqshKs1hkYfpg4ztdCTDO7HEGOcHcf5QIhALbFmraZAzdgMbLQgrjEAbIaXJtk88JCRXJee5G+jpDR", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.9": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.9", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.9", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "20751913293d8a93ef3af8ce0482ec14d5dceea2", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.9.tgz", "fileCount": 9, "integrity": "sha512-0H576mhZdSICP0agRzXSsJwVQxYSuNrxEDU3K+CW/YMaE+i9bZujnTm6PfQ8Ow6/0o8U0NEwUuoE2+GS6lkPOA==", "signatures": [{"sig": "MEUCICM4XrK2+XOeWHK8xICUhU3O+8Mj3AQAmlSrvBPYoUVxAiEAjVZj+umT/daXfjy7ALPnVtU+eQWGe+UeAgISjy+4vyk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191391}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.10": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.10", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.10", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0fa7b4639a7e803d94396e040e14e1954505f413", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.10.tgz", "fileCount": 9, "integrity": "sha512-6bjLGJjDuUuyWWXjrhzUSFWkB7Skl+4kl2jIJ1uuABtRpvz7O4DBI+8gTFAWCv4toLYVZHocKmFr7rOmdFeFTw==", "signatures": [{"sig": "MEYCIQCwG7JUgefPKUTvPJz2Sglcg1ap/Jjzh/PAvXlOUS8KfQIhALCQRfTpZ643CR/UiEGFf1InixZzhZGL0mrHsXXYBoFf", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191394}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.5-rc.11": {"name": "@radix-ui/react-menubar", "version": "1.0.5-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-menu": "2.0.7-rc.11", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-direction": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-collection": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-roving-focus": "1.0.5-rc.11", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e3585b499f5f5f4b6e370cebc3c25777a40f7a3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.0.5-rc.11.tgz", "fileCount": 9, "integrity": "sha512-SAPZDKoaeGO1ZYgSBSC8x/oCTtlkNoJlz/wsA/ieG5ms33kRX9X2di6ABwE8BJAPvr56Zh/UYVDyWZrRsQ/R/g==", "signatures": [{"sig": "MEYCIQCFxk3AoZGlAPUZpI3TX5GQgex/EltjhxHgSklzfze/PwIhAOvRMySl1/xG+mc/SwSFrh1OqGF2rvwdy3pS8LkPMVJV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 191394}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.1", "@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-menu": "2.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-direction": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-roving-focus": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fb945da30621faadb99fe8f70e3a550a95736db5", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ngb+Y5ZvY7lLXfa5Zsce3Jrii0eF+Df1N4AldYHNRDRXt0KMJoDa7rETWoiyJmiCUvUxzaqcYOol3vnAOwixTw==", "signatures": [{"sig": "MEQCIF7uQeviyL18kvu6KlhRyg/tg21v/UqlH9DL2+oHDY8uAiAkMj6OAHiN7VoJtm4d64dKk9f31/EUJr/5ut4ec50nCw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138213}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.2", "@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-menu": "2.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-direction": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-roving-focus": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b77c092639f7e385b2d8ebcd5996f8bba9879550", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-UpC9PsVAERpDw7XIXH5hITlnsUXVUpKxhKXO4RBCuCu3q3Cjr4JwiJrXyn1YMxn0k/DC4Vt+3R5+NpLCgBpSVw==", "signatures": [{"sig": "MEUCIQDJJ55VvOWBHszELACW7RuKDrpMFZw3+4ZP25faCdUXqQIgQa1SKW1rr3coOUHprL1LW9v123EGfmwqLJcxfLGitXw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 138245}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.3", "@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-menu": "2.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-direction": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-roving-focus": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a69b6c9a1dc2fce02e6f0123364e312274efeba9", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-OvLq9Q3WE7OLpNoLN1eOeRWE/KnUnXjXV8/Qg319ow2lPrIsc5ASqipfPIbCRrli0SbccM29VmsQ+Q7SryNTjw==", "signatures": [{"sig": "MEYCIQDS9zxG5wTNdVtIVQtmarD+mEbxLKzIcwnsi5WUsQH7LAIhAJy5WiymS/poACCfJB/Xh4q3xUMRwsPu1b6kXUSbb206", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139986}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.4", "@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-menu": "2.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-direction": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-collection": "1.1.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-roving-focus": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "96eed5de80e19819779a455ccf33e8fa9e50b1fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-VNfLmm2S5otgE1lCC57M6rFJBfEX3e74PRNUQy9lqI0D+tBmWhsvTe5IbbYxa3WZzNfhVL6dK9NiM/F+6gu9CQ==", "signatures": [{"sig": "MEYCIQCzhDXscx7TlpLXArCyYOQyTMKO2E+q1dOQHsKJzcGCggIhAI0ED9luPT2RsP3g0+7U8Reh0iBATZlByQwO87QUUJ8w", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139664}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.5", "@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-menu": "2.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-direction": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-collection": "1.1.0-rc.5", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-roving-focus": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1820a740721e2bd6d48965d36672d3737b970743", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-5esn6a9U5N0nclQNRXSB0Z77QDE6p+vr83+0e33Z8TvyFVFHHnNZi3jyUczkJ70OILzIhxln+t7TH9hwYTPFjA==", "signatures": [{"sig": "MEYCIQCo0KQM9sICaM1opGwbGh77POSidkzKrUdcG3kPNyOvcgIhANMbDNG9Pq40fXbOZkl3Iih1B6TbVc6e0JsxqBoDCj0e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139664}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.6", "@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-menu": "2.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-direction": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-collection": "1.1.0-rc.6", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-roving-focus": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8f8251336672620ec1a037a2b1956333bfe9ff1f", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-SwigKuWCWUczW7++eF+ru/L6uw39tggvELbWwfjzo01U1iHVMfIntTeWcmCTDhIoG8qdxiR8ZNe8acutVGr82A==", "signatures": [{"sig": "MEYCIQCV3wD+MfNo9upe7cp47jhbOPe+XD67HQv0uk7f6SRQPgIhAOfD+60Y+bWsM8ejm1jF3d46UDT5jM1KQhginiYLSAMX", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139664}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.7", "@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-menu": "2.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-direction": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-collection": "1.1.0-rc.7", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-roving-focus": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b748178ecbd79999be5034effea048ff7aec83cf", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-alO8ZRy2yZu4nFjrWe4S4CyF7zFpsgpXkKbT1elHvEnOZ0v++OtHT6SoHjodw0lnoy19EELdmLfiQVIKTkJy6g==", "signatures": [{"sig": "MEQCIDH9u0orowZJV+RXLibg2NxbaLqLEjsEqLSC0sixtyVAAiBGlqCl9GNbXlADxsW8m4M4kzXy7pSQe1m0MQl1FBvV7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139692}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-menubar", "version": "1.1.0", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "654bbd2e2f4d0a5c3c9117f3b6ffc5a8ca77b2ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-LCGG30vKYI3+79AqyKhgjfL8AVfA2a6XPKbD44noaQ4D0NmEhbVZwpLzvl6G8yAoIXXmC5EIwJg/oRe5O1qrbQ==", "signatures": [{"sig": "MEYCIQDQgmCZWvILxxx0XyMon9faA4+Y65wgSnIAcJaOb6a2fwIhAN+Lezdt4X003EV2hBGyLt8DZw7969NJ8iHenKrJKf10", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139609}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.1-rc.1", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "da03c44c984aa174f0fa0ca7f9c42454bdfd0cd8", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-jE04aLxZltES9TGoUx/NxeNDZgajh7NGhmuuRs99+h7b5skRHzibktG8bgF7Im6OOn5sq36d3hoBUSKFhPhv4A==", "signatures": [{"sig": "MEUCIAL7vWy1juEp8yaWVlOtkl7W/v/+zBbe5ruclkq2qCh1AiEA4ljY9UiaR04EG+Tuoage966/4uIVXUSC4hPddknJEF4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-menubar", "version": "1.1.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.1", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e126514cb1c46e0a4f9fba7d016e578cc4e41f22", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-V05Hryq/BE2m+rs8d5eLfrS0jmSWSDHEbG7jEyLA5D5J9jTvWj/o3v3xDN9YsOlH6QIkJgiaNDaP+S4T1rdykw==", "signatures": [{"sig": "MEYCIQDGVW/1MveRDoSjg/nQjI0PHBQ9I8W49HHGtRflnJ4TTAIhAP4mf6OkxOLynaRrjolCxHkcAoNQ83bLjAk0DZXnfo0V", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139609}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.1", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "468a752518be249c74e626133237152788df149c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-OW6YVx5krAcQlEgcp9rVbxZ2VVBCyohUvINsQHBSJGlw0sWEOdARaR6Pp8Ke8OCwb9MLfzZ3tGNZqGRRanvUEw==", "signatures": [{"sig": "MEUCIBSQAaBOPf4/mKvapENmRxlzL1jTMTgKAKZ/i/CJVFCbAiEAzRcy2DhFjZT4P08Q0l2i/7COt1e9bE4iWqgdYuAPsho=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.2", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "295ddf6088358399471cb803f2beb83660573083", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-LXCMcCgNxa74VPJZ0zapgjPAdwh7A+qUVucg75WrHDO7vVojyi9TxkIASEPUC5KmURMhO5KIZl4jSml/RC5EjA==", "signatures": [{"sig": "MEYCIQDdPQnCnDx/9v4HX5jVCaFHI72XUKLilhvRKbwvGPRI4QIhAIckqbKHFaBN9pDcnD9tvamsGmZrJth4fZhykBujGJRZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.3", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5ab213d8117060c0ef475b3b1dac01e208707371", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-U4+NIU05ddYA0B3ccbnuQcH6askhPxT2Q6h1vlXHtxNYsGK8Ffzz5FVK+Qiw+OgCm7LvT2SiE5jSawuSJwWvOQ==", "signatures": [{"sig": "MEUCICu30BkoKcohTni3HDHTWcEAsU5pmfVpXNzAzKKltJsUAiEAp+iU+ij/+Vzb9oRyzx84Hj74ca5NBW0sJWjgt7V34vA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.4", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f54284d1d64c261bf73bed086943a9531bb4bd17", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-t+ToeNUX6N9aTMNLHNI7BLcClLTU3SOsDaZI3pYw5a7zueStEMthGS0gdIrUvcLzxp3q2PZnQS7xnAwVuVoFuQ==", "signatures": [{"sig": "MEUCIQCocKURXUutTHiKADeE8JW3eCGnrIahTGKJmQ0kGTK6ewIgGH7J0Y7OZgaZPMqTJSvxqJxvsz1D6IJqO3ZyVBVkgrs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.5", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "009856da7ae98acdaa8033e83261e07659bdc62f", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-RcaNFn8h8XZSk4pNjFWqZgnO1WGFvSEtEw02Y/rhADwuQ1udbclbYZieGvHJs59qvQJ5SlzoeyLLhPeNX3J7ig==", "signatures": [{"sig": "MEUCIHqsoM+75JcX9nnWsgXIb7REnfVYb20UgcErKSzthhlfAiEAgFG8V7duGGn40YBlcCaLKO+RZA2RdScNeEx0IoQAapY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.6", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "15e47275dc598fc25705bb478fa256eb884fa7c8", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-y4qYxo6564c0itC0roVGbVkh5+13zPPl/S9tKsRgwOpoKYJVC2usiaSYn5ZZYE2cRFU+owK7SX5nB3pNAqzNfA==", "signatures": [{"sig": "MEUCIGHFMYsaKQ7rVq9T8BJqpeLXDuxfpPH3yjwI/8oegRaqAiEAwOk8zh+l5o+2RcoJ+Ip1582SpMOg9lph9jrswZHI5K0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.7", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7044c48ac82e128fd558e5cff9815678d7efe3b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-QPkJtqHneDFTPColERMYplE20Wr2SoFZP0KKtMmwk9tlB1LoBcD60nUvT7ut7TcIYrWR/3MpESRSPs3t4ioFDQ==", "signatures": [{"sig": "MEYCIQD931XSaniv0lJRCqroGMhDcaRaqWoUdzJ4gzNvnBB6PAIhANm+SgaG/5FnJVzyTVfwCTqgSTrvNlYJwEg++71AfoMw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.8": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.8", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fdc6b3b7008f1c32b3d619f11ac01435c117c5d3", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-uT1xywnEZgIHPhoXmyTxkHZ+dM7jbDJqzGU4bOy8+AWec3aX+MRc5HDXrV25dHTmkvV7221POHMfCuzAVEDvvw==", "signatures": [{"sig": "MEUCIB2Psxp7Wka8/oNq0d/QTC4+lAD7uAmi6YZ9eTjiqqlEAiEA00YSHil5wK9h7/Ot0b9eI3Amtd0mxpyz49FgX933llw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.9": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.9", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "84e9278138d93003334ba561e78a655bd6b31292", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-LdgXzwROuIzLNPBbyJR2TF6QUVXqj/33lCvIcPBg37t3jnrO4lqJGT6Qc/uQM3P1KDDKRFTk2DBgh+q5N2AETg==", "signatures": [{"sig": "MEUCIF5nxqaHai6yTIISELZs8PGli8oiBu0dqvIOr0Og1A8FAiEAj0Zehvr/QEQ54xTGaL/ndBHQT+Biod540h0COpmcGY0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139652}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.10": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.10", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bd349ae1722e7476cc43fb90ce41901d257611b6", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.10.tgz", "fileCount": 8, "integrity": "sha512-jTlN16b8j1j57iRD/Drs6kFz3PMLWHD0sXa3lDICcyADZMNlmhEoNTm/0de4hdtBm7SwMKbUA6MWn/B/PbiK1A==", "signatures": [{"sig": "MEUCIF0lalErsYfECiQgWTAK7HKC3XAc05MXqgCPoul91/ClAiEA9lyKrFkbJPbwcrVCv62DnsfhppurjtXcMT+r4FfYWnM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139654}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.11": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.11", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.11", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "56df296bf4408a0e7033e3cbe2e64123f923a20e", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.11.tgz", "fileCount": 8, "integrity": "sha512-rCLuHz5fj34jMnl4FIeST3vDVYVhPObte8CFYmjNhkVYr1nLjFs6Ki1AheGrzaI6qYN1ppA1qodWwKu82MvxPw==", "signatures": [{"sig": "MEUCIQCUhEMArnAjtpSF8JHQiELaGRnZ/4YrgBjEHXuY72i3YgIgfzsFsDFiZiTNRBVsbbkgv1Xapkp/0TEsI38NDESQ9dg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139654}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.12": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.12", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.12", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4c611427da1549ddce07b7d1aed3e3c5831c7022", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.12.tgz", "fileCount": 8, "integrity": "sha512-/1Wwx/c6zpL/mBh+lpPNS/5Yrknx5SDyfXcqDLUfbWDImFZziGILtB8clgnEAfnSh1WB/2dTSIeB7JFZm0axKQ==", "signatures": [{"sig": "MEUCIHvAD3+0sTDC7DqU71ZjrdNr3w048YAjUOfNTr7sEuy8AiEAhEsO+u9JB06xf0GijaikIZTO05fzji0U8yi0hE/uwHI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139654}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.13": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.13", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.13", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3aa58229164e2a1b8315c056798f743bfb448c56", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.13.tgz", "fileCount": 8, "integrity": "sha512-Tv2OnLi0XxUzf6HxNxN+p1CYG5pqySA/S4XZ4EwLYZ3eFZl3HyUzUVW4KeV78KsVfmz+RA3o4MalSUYPSKRkTA==", "signatures": [{"sig": "MEQCIAar2hZNIYFIMumx4J+Qvm6xPMYXsg/6Ene/X3oQDtBEAiBOS6LK4eo1bdB57GOG4KwFexhvBrCXqVTZwt1MuSlYyQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139654}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.14": {"name": "@radix-ui/react-menubar", "version": "1.1.2-rc.14", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2-rc.14", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c033adc3fabd36ea1204220292d3fcfe0f8b853c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2-rc.14.tgz", "fileCount": 8, "integrity": "sha512-rGRxojta+T/7HL/CTqiDYxd3QFJeZ5q33iRFD3Mj+USEyoRbIPHHufJ/3idieMSG74P+nngWp8j9Z/ZFnSFnaA==", "signatures": [{"sig": "MEYCIQD0IB81xOnqoAZIiaik5XiCjYAYcuB5glGKg7Czbj9OdAIhAONbLyxWgi6Iq0R0MZ/pbSPP02zgqTTgjGujHB2fKVdS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139654}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-menubar", "version": "1.1.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-menu": "2.1.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-collection": "1.1.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-roving-focus": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5990e6bb1b03c3d78dc7dd3cf1527b013cabf12b", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-cKmj5Gte7LVyuz+8gXinxZAZECQU+N7aq5pw7kUPpx3xjnDXDbsdzHtCCD2W72bwzy74AvrqdYnKYS42ueskUQ==", "signatures": [{"sig": "MEQCIF0h1Pt9LiK3YxHvBKhBrA+pF3BGEFH/iCRzBMvTXNPtAiBonheFBup+WX1z6IC5dCyewOe+9I3/w11/U1JyWLMjuQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139609}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-menu": "2.1.3-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-collection": "1.1.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-roving-focus": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c2a150b1ad6bde3897fb27760db628f805c49901", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-yICdkU3GKFsKQLCQH3nGH7Ixf5+dwXOp74J4OSCtdwaj6VhBSs4ee3HQLXvM3zj/DUwOzbhLlmE6WHmzaYWamQ==", "signatures": [{"sig": "MEUCICtsNR1OZtYYafPh3a5oYTZ1rUBJgTmI9yL4gomlJDdiAiEAv/JLTfSwD2mTtYcCEXKxdK5gaBcvVtcEZRCNlTGpBOY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139390}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-menu": "2.1.3-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-collection": "1.1.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-roving-focus": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "160749699c9ba7d70de391eac8aa6e43c077c6bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-xMfMdHHI8z1o9H6EoFBa2Siwl/Ri5a2duWk5MB2N801RwjihYbpwP7c4PgjQqsOfJBXtsze2DuJuxUirOd5DFg==", "signatures": [{"sig": "MEYCIQDleRFy5WszMzMJeVHagLn1GWFzpM4armdOdwiUSm7uTgIhAKnaKr56OJY+zMPhN9sKHkm6k8gxG69TBK4aIlB4Uk4+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139390}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-menu": "2.1.3-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-collection": "1.1.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-roving-focus": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "010ed3ee24572b2d12a59966490b6ec017600f70", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-FQrwDfT1rQDsgmUBQmAACyaLkf5KcYW9nnHVAwhtRVU5kevNrOph/Z9JXwdX6DcoT1mgaaUrpC1SG+mqrCFiAw==", "signatures": [{"sig": "MEUCIGjHArY6qDgwERtq2Axp/o3A+H6MJsZzb0QYd6/+OspNAiEAsVA4AmtYma9FI1lQdQGDJU49lUIQVyAGCfITmSYTK4k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139390}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-menubar", "version": "1.1.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "981b0236c9dfd6fc931c0f77c7488f9441c2aca5", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-u4PF2TEFZlrXJVohn2pLlCEqw20rgG3M9yNlOkLTb8VSc5nuRdmfo47QeDrNyAbHUSXeWMJPDSPMamk2tvMVVg==", "signatures": [{"sig": "MEQCIAG+DSGLZ4Lr3d5jnVVO+of4Ci7hWSwDlIfoAD1uv3ICAiArO/1Y7Dt0wcoTGSQnAYUPV2VAcxWWuCW8XYkc2VpbNA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139327}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-menubar", "version": "1.1.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "917c49ba773094fa81579fa6c46567b562d208bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-+KMpi7VAZuB46+1LD7a30zb5IxyzLgC8m8j42gk3N4TUCcViNQdX8FhoH1HDvYiA8quuqcek4R4bYpPn/SY1GA==", "signatures": [{"sig": "MEYCIQD1QnXK2HF8PJ7rWLQgg+WyajYo7zChse4mjigImX2W4wIhALnfe6IhI2VniOt9jqZ3ex4ECcgiow7iC8Qtf/2x/Oab", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139327}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-menubar", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-menu": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-roving-focus": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ac4e6489f3abedaa80e98e668bcb07be0934e912", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-6OFcvcvbC/9nUJ0olccH+oqPmjLxiHG59nhPVuVHHxmTok2BV+vJpsy1dQl30/yBUfILawuO+y+zrMbvdcB7fw==", "signatures": [{"sig": "MEQCIEN21PeL/UPSgUjVw5SBuhOTBJDlspAG+H5WqFtkYW1oAiBP8nR49KNQMSZSiDL0xdSmXrSwNomStGEbrSt/gqWV7g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139372}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-menubar", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-menu": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-direction": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-collection": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-roving-focus": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7d3aac7c1aa21801b9cd43b2b667b207e68d2a5f", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-FjvIDElw+55rh6Q3FniWEEPMzzXO/uUGuWd3Tarn7p12ZZhIfZuf6++kF4s4VZAqASq5byNu7XhSrgbeeH+9sw==", "signatures": [{"sig": "MEYCIQD4HqQRA7KuOG7Ww95w3LeEaVxoyLhXrd8v91Pbl0imaAIhALFvoKf0j5V9LiPnogqCUvfOIi8oDEbIkbdk/z/q+xuy", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139372}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-menubar", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-menu": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "0.0.0-20250116193558", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "08b4aef61c47f3405644e4b26e3a2f5a6bbe143a", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-bak/T3B2Wg9xq1CmSMn8pz7KU12JR2FegtF3DKzXKpxqUXR87JFU3MoqSqVpUnEyfXT2B5BZISQQy3x9YA5ZLw==", "signatures": [{"sig": "MEQCIHv4jqTG63vEgyg3/HgAOgFH5buhe+Jna3YjOXDZQLDNAiBzvqmre0QMeor8J1mDCX+zSbReMXYNfY8rkM99z9OXcQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139605}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-menubar", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-menu": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "0.0.0-20250116194335", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "daa3927e5757e1de327c7f509d62f16e07ddd0d8", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-PLcWPOvZ2EHAq9HcYDpn8BDlYRXJhYl26TfU/yv8TxAXjkTKi9qckxJvpHxsnxOtdoi0OOJnJUUZGR6LlRortw==", "signatures": [{"sig": "MEYCIQCbISGLuKwzO3zZk7Bf0RoYYl0BLtO6yvGOFBbLTIsW5gIhAJp4A+jHwcbA9TK1s7IlVxaiq0BjWVHufI587mbGE/z6", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 139605}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "743cf4ac3cd4cd9b59c1a61af76fa5b17f1b42cc", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-E9sAnGlDZUiBF4GLaAGEhmN0keDoA3nZLbHbX0u9mWgnoprrVjYuzkrz5e3Ak4CQOzkzNBhkSM19LpT3jesDhw==", "signatures": [{"sig": "MEYCIQCCqGRBmyvhwcWLxd1bWfy8nSpv69PGITXrDckNuSv42wIhANrj8MUR7sqT9V8V3Ze47Za3KatHNL5akrytOfVVWBz7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4da708d709e8af4c620adb66581696b1f1bc406b", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.2.tgz", "fileCount": 8, "integrity": "sha512-3t6f/6LW788Z8TDe5CuSQ0WQVuvATz/eslWkB9Cn8EBPt9aKVZwfZgTz+XWUdJD234gpj2QWpTuMNfoRIRxRnw==", "signatures": [{"sig": "MEYCIQC5aQ5cI/HXRkvWE3Lt7Oh/LKCunQbnnswo1na0MsA82gIhAMmhK+7g71X2ZNbqdK1ukqFcHCIPN9URODeShYpjQBVx", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "00601e1db73fdefd4c57b89c7f6d1a35173f197f", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.3.tgz", "fileCount": 8, "integrity": "sha512-xN7lNUexQ1L4fw/MKMNcEEX3BusuOZPcxP5nG30tgXkqJcZCxiCFmrN4WnGN1+YA/DcTQkHwpWFibvKr/GE0yQ==", "signatures": [{"sig": "MEQCICIWSWvCsMArpXCyDqRQ+aHKQ+8CWjkzn3ezhGMGPMkFAiA5NtRjZT8Mxu+UnJjQwcffQTK9rHlTpj+GH1DlklXIMQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e817be295c100f8816bd2c5d9107d6bcf917a65e", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.4.tgz", "fileCount": 8, "integrity": "sha512-F/fQEpUYKpJAXuXk69rvtHsSxjRUKCihbcXGPUArbeTp7hZBeOuuqwYey1qV4AC+z5eLq9cQfH2CvUQ9KocBbQ==", "signatures": [{"sig": "MEYCIQD52FMi3gbzm7zzbr+QhgXjotlQ8V+iMLVlDQ3uvYR6qAIhAIGlOEsaB0qRBOkb9oEj+tqA3Gzf8PxlpxcamxTLMLvj", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.5", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "67922166e40c2f815bf7ace6be8b5a9c42ae2e8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.5.tgz", "fileCount": 8, "integrity": "sha512-zKHlP68JmT1Z8Qz71BkyrVZNunbm8R01RDp/RG72OKzObeO7oL2TPtx6ouHj/gM8AMEfxYyDHmIbJINCNQPSqw==", "signatures": [{"sig": "MEUCIFYewhIONulTt3jnGo3LLbP/+r7wQ7omzv5QdvoyPb05AiEAudRlVmudSjV5o8/4B+kpsiLoy+9hcqfkm5wsLQVN9pg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.6", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8a0617d6f571a8e3429caacc7a2dabf0ad5a54b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.6.tgz", "fileCount": 8, "integrity": "sha512-R2UxsVIlZddnffXIjyFvKflBYZq63bm6Fe7GypXeUO+qx640/IPITCzIbSzmOrH6dFaFVVwKqu60PdmyUr9ifw==", "signatures": [{"sig": "MEUCIEQFyFOa+cyNGmJ/shlInUjGpszylwJdMcoLnc6i4M7oAiEAig6qRnqPaTAjTI23zgVlM7GzCWvd/CoHM06IAUMRHm8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.7", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6b94edfef090faebd781af84f43a1613f29f543e", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.7.tgz", "fileCount": 8, "integrity": "sha512-T++VoZQp03X9PwIWbkG1q5p6hbfVcAmGy5o63Brif6+0NxjNlMkV+hLiE5xNphBQTBZ3GpvEgsvoGqR3ZnpYnw==", "signatures": [{"sig": "MEQCIGl87aMyi4+t/+zCQSAqt3dF7Q9YaoMf8qUlIbdu34LrAiAvAtrUPEjv/UqCMhiT1RXbgg2vQoKxZcnf8uUVDC3Z2Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.8": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.8", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "cb3d30b25ae7081ab2bd9c4d7200425f8995ecd4", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.8.tgz", "fileCount": 8, "integrity": "sha512-otXMhyhV84Rm7xf9MVQLHYcxHOsngbc/X5NARdT4fE9bTAPim0Cs2aKiz5TGcRQasOpzK4WDa2FmQBnlQwx1kw==", "signatures": [{"sig": "MEQCID5wLel807QSVDV7HeLwfMWmESOfgGIcdejDqKcTV1hLAiBtDAYNZXg1HjRtM4R4Eqax73VgKOXWlBfwBTZg5rDZhQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.9": {"name": "@radix-ui/react-menubar", "version": "1.1.5-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5-rc.9", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e22439d7fb55570b3e1b91a07a729d77e838bb7b", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5-rc.9.tgz", "fileCount": 8, "integrity": "sha512-ButTK52ztKNxQSftKuJ/dev8boWjHhqK+wEo8ShrafbW49COqUIFGhHpPj0Be7bPjWAQ40kNFM5KIdf8wyVBlA==", "signatures": [{"sig": "MEQCIEwaBMFoZdxiEg4eOSuRoaa5UYRt00jiNmoe893/Q6Q7AiBPauOybmHn+vJWga7z2ja8dZyyXUlEv8LBSXl5dsX8SQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-menubar", "version": "1.1.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.5", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "eaf7ffb507c27a6db8d5fc68d8dcc0e2e1013eb5", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.5.tgz", "fileCount": 8, "integrity": "sha512-Kzbpcf2bxUmI/G+949+LvSvGkyzIaY7ctb8loydt6YpJR8pQF+j4QbVhYvjs7qxaWK0DEJL3XbP2p46YPRkS3A==", "signatures": [{"sig": "MEUCICHjDOyfVm8UiqTqFEE8fYDghhPexoazPtr95cNbWPgBAiEAxBfqM5kAFpHZ8JStBOchK1JxOg5+pQ64hzBzuf3H23o=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139516}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.1.6-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.6-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-collection": "1.1.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c68f76bb33498198c37becf9b2845a7a1a1987b3", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-UrgoL7BV8MYsY53OMUtZNzkddEaITU6hGzVIHpjXPYd+tnO9SpWyeVN3jVqKMnWOUXdpOebMbE5RAD/b3khLUA==", "signatures": [{"sig": "MEQCIH6MDfzU1/8uGrtJAPqgPK/ltK1V1FGjlTMdYVn1fvgVAiAJiUEqlwIl3gVLnt8hz75nEiV5KL0hyJvhKvBETCnkvg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139554}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.1.6-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.6-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-collection": "1.1.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.2-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fe46d6dcddb16a7c1530c8737cf25ee2dbf56792", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-gnJhISY+VTktxU4/gK59u4Fxm29jRjF3y43vzAOkmcGIHWE4xkwynZ0r+UcaPRL6rhC0NQ8Na7/CI+ftyjVHng==", "signatures": [{"sig": "MEYCIQD0wjsOgAvnbWppJ5RY4dIlCmTrd8gVjVJ7ZQRRcsW7cAIhAO2XtEm+KBSJGrfHlbuiImtHdMEnVFyP9LW9bebR6tSd", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139569}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.1.6-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.6-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-collection": "1.1.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.2-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "de5662cdf022cf31b8d6072cb1c792892901df9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-yRK60I0poUW7uYtvOso9d1qpdvF0CSE/HcYqsWvk5ifVec2FW25KU53I2H/e5yrruHvHhz0Kzveb96KJIvOzQw==", "signatures": [{"sig": "MEYCIQCAgJLFyhoEZtJkqvQv6czezlMe5JdrsX4t8W03Ho31jAIhAJQi5t5OrPKzM7ULVHNW1o9eTFtgYco0oXc5wsR7+x1N", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139569}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.1.6-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.6-rc.4", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-collection": "1.1.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.2-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8df9cd7fae508b929013423c77907322acf27cda", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-E8CgcuK7qk3ipiJqjZqyu0KYn6ZfecGDcaHUcDK2G6xt0t5vu89CcWP23py6ctaJHRbWdoAPjVTx9dIXqrzH3Q==", "signatures": [{"sig": "MEUCIQDxPthVjjAMpPWj2LA6AzaCtIct4I7tGpVU4aLgwpempQIgG/iAV306TY5A20OxRKMjo3P4hN1oHmTLzg+Z8WyS5gM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139673}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.1.6-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.6-rc.5", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-collection": "1.1.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.2-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "74fc741554c315748515817573374ed144b36f61", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-7gWpzrslyTb+QjPuS6iEnuSXpT+B1CCnpgn3ROEgGKi0rkmOBkzZJTUcZ5RsH3X0SoQU7qhbPSUJTDRQwDoJTQ==", "signatures": [{"sig": "MEUCIGt4gpOqTIfQ4Gff6ptlomcbg2RRhDSIamE1bL9R7GU8AiEA2FAzS7uSUzZMDOrT/EZtoN7kXE2fBpAcWqu/1X+uQ20=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139673}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-menubar", "version": "1.1.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.6", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-collection": "1.1.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6f8009e7a3a22e5163350fe16e715d6241c99925", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.6.tgz", "fileCount": 8, "integrity": "sha512-FHq7+3DlXwh/7FOM4i0G4bC4vPjiq89VEEvNF4VMLchGnaUuUbE5uKXMUCjdKaOghEEMeiKa5XCa2Pk4kteWmg==", "signatures": [{"sig": "MEUCIQCx703TH+dVCdprXi3wej8bkTf5Y71pQt8ayxZD0dbQYgIgQ/BDKYTyPlANlEoly34OjBCzdKHAov1dP3MvttRlmac=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139620}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-menu": "2.1.7-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-direction": "1.1.0", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-collection": "1.1.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-roving-focus": "1.1.2", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c10da930dfacf7a33a1ba2048693211d44839297", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.1.tgz", "fileCount": 8, "integrity": "sha512-LJN0RUHdKQpaAbgEc0oVusUcXdS2tphYVS4TH8VFqoHSW8guExjwDBCF9NzMw0YnpOD77gaRd0HRFsXiAYowZA==", "signatures": [{"sig": "MEUCIGmx+GLBG7KGiMN50VAHojq+3dTfnFR6//NfZVaCyK19AiEAoQ3/AMOf/Ivx+NYX+51+qz0Hr9rfNH5bAaf+g5C+e+Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139658}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.2": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.1", "@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-menu": "2.1.7-rc.2", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-direction": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-collection": "1.1.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-roving-focus": "1.1.3-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "82d193bbb5af3ac2cb562dbd232035732e972c31", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.2.tgz", "fileCount": 8, "integrity": "sha512-atFdQSKqCUA4QtSRFtgOI4674LU9cOFGjsYzKAqXUYyIuXRWIuFk7wsnFfhrpjmvySVIGq6fA2wahlOXsKZnKQ==", "signatures": [{"sig": "MEUCIAJmyMaivfgqN/lBoH5CZdslRj85DKKmSXuGpkSh6RwdAiEAnlL41jhT5+HCaNl6wqFh+UEyFBavGHftKOEgGfrehQ4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139709}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.3": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.2", "@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-menu": "2.1.7-rc.3", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-direction": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-collection": "1.1.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-roving-focus": "1.1.3-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9e72c35c3780b586ddc605f09cb91d041f2b39ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.3.tgz", "fileCount": 8, "integrity": "sha512-aV3KEx1/YCWk0FmShX972VpwzeeI/+GOWRQ1Im54g3624aA9jJFuQcaY5Vk6aJAEu6ilnsp1dl9qkjA3p6ZUsw==", "signatures": [{"sig": "MEUCIBtTGy5hpO8OUPFzXfUce3wno0IiNA94WjezuFHYJY5VAiEA6iv9a3Ty6hbjr2Mjx01nt9J6ZAIAo8Vl9RLAHKNchkQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139709}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.4": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.3", "@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-menu": "2.1.7-rc.4", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-direction": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-collection": "1.1.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-roving-focus": "1.1.3-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1327f804c89b0900bedb5ca0118aef6495cc10f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.4.tgz", "fileCount": 8, "integrity": "sha512-VFXHGpXWovmffLCh4pXuZGK08dOyWXBVlQTti6GD4PcU78iCfaw+pMPq4D1cttAriaPrrnDdV9f8f77qSIoYYg==", "signatures": [{"sig": "MEUCIQD0W9K4EURC3ftERJfUlsz+IyttfGkjUPWTVboVqO2FwgIgUfyRDVgEiQk1FlnE5d3nGT1p+SMhDJW8DGMvI6B8tF0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139709}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.5": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.4", "@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-menu": "2.1.7-rc.5", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-direction": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-collection": "1.1.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-roving-focus": "1.1.3-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dc44464cf623a412727461cf7ec6e63b4e3f2885", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.5.tgz", "fileCount": 8, "integrity": "sha512-vQxNNsLjqyLEsMxSLzlmd7YrOzI8nYk0U3026Wbe7saAcnq59ntdoVyjhent4JvWQJBVHdeo7PrVwE8Phz7DOA==", "signatures": [{"sig": "MEUCIAiEcgbyKpFAsD48zRCsxFjQBM2RsL/6T50iuaWRUvwiAiEAguDmu2G46vgoOD4hDZw14A6mWCLML7VoLdl0wZ+50Lc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139709}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.6": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.5", "@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-menu": "2.1.7-rc.6", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-direction": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-collection": "1.1.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-roving-focus": "1.1.3-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "238860580ee4acb8e3ae467c34878fd79119ddfb", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.6.tgz", "fileCount": 8, "integrity": "sha512-h7yydiYIIn18yD/GXLPMmvHETLinvlb6FBxBSdJEi0unSEBF//ds78IpT2mMhZk4ZUp7s55oAam7SOzBkd7ZeA==", "signatures": [{"sig": "MEYCIQCBxBacQVkmzZVDib3sURnx3CU7Z3TTDaTn2thGK/YlsgIhAJLiB+5hotHiIzAFG47cYAtVTlhikYcPl23PJXQFhIiH", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139709}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.7": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.6", "@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-menu": "2.1.7-rc.7", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-direction": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-collection": "1.1.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-roving-focus": "1.1.3-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7b790eb9edf13699b759e8da973d2ad145042992", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.7.tgz", "fileCount": 8, "integrity": "sha512-FPRJGlksHw7jLL/DULGzpGGvKJn155Jjv3IN200ria4ZPPcKLGVxfpVjK3je5mr3KdGfcjwVcbV6Ya5zkmbWuA==", "signatures": [{"sig": "MEUCIBdaSL6qXhGODk/xd6dhIbm8d7WJlyx1MmUY+MntTAPqAiEA8bXBhcUJR2nh290fgGqI2rYr+UWbMrerJaFkj8Y1Ql8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139709}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.8": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.7", "@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-menu": "2.1.7-rc.8", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-direction": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-collection": "1.1.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-roving-focus": "1.1.3-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "db8424af8b43db5177e59bf9016d7645fbd0a362", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.8.tgz", "fileCount": 8, "integrity": "sha512-ks/kiJAOxNLMgl33zoMr8Q1pw0KFiWSGHKQ3ryQT71YOQoLCaGXsbWNeLegpRvNhQKU0qrey8/YfXbcCyhH3Aw==", "signatures": [{"sig": "MEYCIQC+pZcFM262GgnZ1clrsyVcQROaSmzXKHtkrRMw+HIDEwIhAN2Pqk74z9L5HrgdVs7WTsPM03Z1vYiT9CcVi/UKw5SB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 139709}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.9": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.8", "@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-menu": "2.1.7-rc.9", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-direction": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-collection": "1.1.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-roving-focus": "1.1.3-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e2a7f5f9481f9824b1d603b3c9a45a5dcdd71553", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.9.tgz", "fileCount": 8, "integrity": "sha512-8MxbT49Ze1+PHnZaUapTK0xAUIlFtH54RSiNNOiyQpywmOuKodpivNHoyYktIt5+vUqh9hCwchXqig7uDPiZxg==", "signatures": [{"sig": "MEUCIQDesTcXyoetyr6aFFa3njskh35JPjGumEmODG/guZn25QIgTduKnYzAS+nqPmzQoX0Z1BNDFuv/+DNHXRSPzc/BLJI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140100}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.10": {"name": "@radix-ui/react-menubar", "version": "1.1.7-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.9", "@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-menu": "2.1.7-rc.10", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-direction": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-collection": "1.1.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-roving-focus": "1.1.3-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "502a56a6dacd51a7ef28f07101f589d308e3cdec", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7-rc.10.tgz", "fileCount": 8, "integrity": "sha512-t314YMSGFjZhasPJOlNaWOTusiRg03NokOXzZfjCPqAckz3cK1Hh4c5RFEPF+yM8kN/H4dXYeftMlZf7fxeDzg==", "signatures": [{"sig": "MEYCIQC3vtgEwt52eplUVJYYtQrBZUOwWrO8MPOFyA5q41yKAwIhAJlQ68xrUgxcSptVUCOA+gdCXc24w7p/JLUWbQaEcz0C", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140102}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-menubar", "version": "1.1.7", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.7", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.3", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d6980295858134729d22fd3b6f9ca63fbebd5574", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.7.tgz", "fileCount": 8, "integrity": "sha512-YB2zFhGdZ5SWEgRS+PgrF7EkwpsjEHntIFB/LRbT49LJdnIeK/xQQyuwLiRcOCgTDN+ALlPXQ08f0P0+TfR41g==", "signatures": [{"sig": "MEYCIQDs1FY724MecaDoRGLJ2aVWSU8YFWn7XZKvldfgVwlX6AIhAOzevDTBEpmixnaOEjO56nI0A7j1pxPxT5yGQH1OahQz", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140017}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744259191780": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744259191780", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744259191780", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744259191780", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d1bf15e8264d83f52c73c3c44e889cacd90e8669", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-6K7dx7COsM+WQzu7b8rAocku9CKS29uW+qvitU4mWMWRUgFB5ZGvVyZ8BLBZc0yh0ixTrnb5i+yDoes4F/fTGw==", "signatures": [{"sig": "MEYCIQDrwcToPz3f//2DLxkW82dz2i7UfiyXSqioOXDWxTCt6AIhALwdNKZhIsgQIX/5sqM2dI7KOrYLMs6YkVLb80ikTCX6", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140920}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744259481941": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744259481941", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744259481941", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-collection": "1.1.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744259481941", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fc9f07cf9b83c141ff8ab2c363aa69a525aaa1fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-tJJz5OB7iqFCTENI45/m3epw8VX1906bsDs+ViFQ+6CC6XSAdd4tNLpW1GWJ//I5CA6Ur/kgypbpjxkbYfmYjA==", "signatures": [{"sig": "MEUCICHf6jSW5oh0T1NJpgBV/w5YzlfI1zBSPFGVnlHS69lSAiEAsQevGRngZ2oUj7zvcsLEnBqc542VWuOhq4xIcwGsXvs=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140920}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744311029001": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744311029001", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744311029001", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-collection": "1.1.4-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744311029001", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "488560facdab6872b9442fe1b3a33f60a91dc35c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-iuZ5bxt8BLIg8aYwPfYY/KfXDYz0bnLggjUeEzjHGyreht2HdXL/E/9yws5BVNGyAtUmHTF2XEMiK4zWlFdVmw==", "signatures": [{"sig": "MEUCIQDwWjUw4nDP9rjeMTqMYlvy26GioYkT6+EOg9ad26pr2AIgNpG+vxxfPCuwJzCBl9RIlaMpOEHUAjkrwaWQFfFK8Wc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744416976900": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744416976900", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744416976900", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-collection": "1.1.4-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744416976900", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8305c4a3b3532d94ad11da79d83ad1f2ba86b3bd", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-YSOpv13J5Lg1TwycqaaGhrK9pn0zRDTpEOPJKJ/YKM1ScrWADHOSXnrgKlVfKK9wqYgvhqJl9DY2QPvj1dnr5Q==", "signatures": [{"sig": "MEYCIQCYgqnLfS/9ogva2njGv1c97xY9vcvyEwxfqflptLG6HgIhAJeFdcjsa1doM6d5t6c1EhQaiuAyigHrS2QT7T9BWf3F", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744502104733": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744502104733", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744502104733", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-collection": "1.1.4-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744502104733", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c587df540230083ecd249e2557d41d50c331c8ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-Amcxeyy3NIaGPN6v3J49hr/usiHNkTz+dSeSNcLgf0ymjYLFesplhAZ5MBpARcNqYSHVoXk0sXYyRO+Q64iTlw==", "signatures": [{"sig": "MEYCIQDjUG1EFLeBxkLu6MnUSMosv9YUl4+pQWj66ekwQQqvPQIhAIJrOXZhNcF4Nw94e1W8hX2Z37XeYbeLbFmL2tq2saOI", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744518250005": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744518250005", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744518250005", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-collection": "1.1.4-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744518250005", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c6e3f690c007d108a2c3fcf5cd1d9614d89a7dc7", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-1AHsTZKOHSn4U7cx2fIwZBD+0KJ7IlmjhtYxkWHjX5hnx4glZVlE+04itCUpYlhIPDdkPupa2A4axkJas9rzjQ==", "signatures": [{"sig": "MEYCIQCf/2YZWGZuY/NKXX/pLcUAq+Uz6vEVOXetKg4gv/mBAQIhAJxPI8jlsMJ1hYTrWdac3+H85v5kS7Hv7y93zEG9cLbx", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744519235198": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744519235198", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744519235198", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-collection": "1.1.4-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744519235198", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "01a4990dad35915b314f3261cc5b99f349da2a78", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-vcwrLB1KQ/RIhHWo0yfPZ0dWnrC7EQGhh9rGoLqQC61x+lQBvtLEAIWg9NBD3XIpmvPRfrX3Auv86sKB0D7lpw==", "signatures": [{"sig": "MEUCICyntAJbu6Yq+uEOxO5+S6FSRqAm4sKUSEJuOMmKw9IdAiEA6Avv98pssvJnS/QnfucrEHQ7S2i/SEkEzsAR2qotxpU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744574857111": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744574857111", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744574857111", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-collection": "1.1.4-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744574857111", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d6c75d61e02a668726833ad9934008f16ea5ff3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-w4<PERSON>SK0SJQEG351LZaKKrh8S9y++BaIwNGf7JAKXO01BO2ODfh4H/5o3/fi4ODmHQp2Yaq8w0Jc57myiMzW9gg==", "signatures": [{"sig": "MEUCIQDiIuW8/MrTdVCIQBlqCefn2Wp/D7gsDm9wGuqBppW9ZAIgOy+DO4nKivgUxR1qGfUh73C719JwVS+12EjnUWkaKls=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744660991666": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744660991666", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744660991666", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-collection": "1.1.4-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744660991666", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f9b95f3460e0a6611a1a01438f028ecaa291ad44", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-1YhEP7Lpjb4SQLRnk3fMYj/0+u2nMv353j5qCLHL5Wkenv6o8UeS4ljXryTlsUyMrmrPnt7FbwtpXaFxJJa3KA==", "signatures": [{"sig": "MEUCIE1CPzqyeOoPym2oD+GeOBa8nNQIVqDso3PaQun9abgbAiEAwE+y3ZtNYDkPkWa0KkDYtKZIUNzkCWHtIXRNBJW57NM=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140674}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744661316162": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744661316162", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744661316162", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-collection": "1.1.4-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744661316162", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ababa6eba1e710ac4182cbf7512aa15abe3659d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-3mON9Ld6/y1ZvIicA03gjAtSiv5v/BzPlMkxhsjXQ+5IcuKjPk22E7+YgYO83h5Gtpy0TIu7aXYFL3e3761G7g==", "signatures": [{"sig": "MEYCIQDyrF4raQQypZz9/XvpiuNg4ztXUxzDzXtufnulb+KXzAIhAJb4wiPSDzMtasBP98I0c/v8+B8AcGAiMXmDtSQKjopq", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744830756566": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744830756566", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744830756566", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-collection": "1.1.4-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744830756566", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f6a309b721a83405903b9c82cef78099a1dfe7f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-QtXNGuoONq9nla4Wt247Pr4YHyk37h0fpJTK5woKPxZRMZtYhURdzvvXAG20mUIzJyP2UHkX02s/57AACXuvbQ==", "signatures": [{"sig": "MEUCIQCTurM9DHsKu31SGGge8gZCxyxkFnJQUe/gDb5ELtPXhQIgERrQxfuqZWiW4whXeeWX8ph2MWqP5/XZlzy+j0apVYA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744831331200": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744831331200", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744831331200", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-collection": "1.1.4-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744831331200", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ebe1203b9e59cafddc598fdfd618954ddefeff76", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-f17nhEii2eaK19qH1C4ZRYIrvQvAGCZcS3QoVEY3rmkYkhNEAsHc+C6Yv653v8DnJt1HSWEKA4vSBHH2bHQKnw==", "signatures": [{"sig": "MEYCIQCscEj8bs8rn5RA2TlXAF3s6ue2R/Ywp9aytvwAUhHiPQIhANRdfiKcWTxC833AAcF9TVLHTxJJcoByg0+tMZM6egEw", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744836032308": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744836032308", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744836032308", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-collection": "1.1.4-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744836032308", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e1d696f30f2db2720753b13c06d461234d152fff", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-zCTVO9SdIGr7uayTtJr+v1KZnJ9RoD1WKeQdKThKL6Q9Q/mbMpOQJUNuGaU2noG5OmBO6EbrrS6v9AyQDSoWTw==", "signatures": [{"sig": "MEUCIQDyl3QqyjvJzR7zldLmXEB6fUVKzftQenfKmmIqlXWTQgIgHOVtbfCZxBvwh2JWxnenMKMDpKeJKC9GhCmZi4ia8rQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744897529216": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744897529216", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744897529216", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-collection": "1.1.4-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744897529216", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "940c732a1ad97256c665a8017de10838e966a269", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-WW1dwHyUsHc5u5YPPxPJAWkIhB686emj2DoCf/zEP13OrpmeVM9u/TkRX7lEIaw4a8Kf7rcNHzzh/2xbAPH4zA==", "signatures": [{"sig": "MEYCIQDeWkAuPsulaBtKpd2vyyBe7CJqqnwT2G6Zy6yotXmthQIhAKzYTg38eEmfsKDV4s07UHo8Ns0tcaYywsvTQ3qMtHvs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744898528774": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744898528774", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744898528774", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-collection": "1.1.4-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744898528774", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d2d053e9b77c33c78c21c9e32d41975f8789eb8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-+fAnfLBcyKySacVwMUwF2m0HkPn4hDxV0F5+4imK8NLcjoDVcb45pCSdXv/TZ7+yOJCtLB2NOwnUcDA+xFsbOA==", "signatures": [{"sig": "MEQCIEc3r9Aqb0IvYAOlQH1HIu3Ksh4VIl0fjE8JaXYFVHDHAiAtpImZ2PUuR/xwx8jLvssPRMm6vM6BQxTibuy5pfvEdA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744905634543": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744905634543", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744905634543", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-collection": "1.1.4-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744905634543", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "53a888df75541ee35a50a40994f24240a8a2dd3e", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-wXbGWyrhp4MqiAWNRklcaSzvE5Wqy8GL55KlNFXcyGOUX5vpwie7Um19+92Ta6yAul2MZDE4XQIE4nL8/fu67g==", "signatures": [{"sig": "MEYCIQCCY2c2zVtA7tTJdrfet5snxTesSXeXXhMX3hRTdVb1CAIhAILi5aKQkBZkFLVTwAIkv/9pUKpZDtMUv2LjRZalZBGB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1744910682821": {"name": "@radix-ui/react-menubar", "version": "1.1.8-rc.1744910682821", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8-rc.1744910682821", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-collection": "1.1.4-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4-rc.1744910682821", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "54f2253fe8e63898d6adf2bf4fccc805eef7b440", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-KHuFHo+sikrJ4Z+RScIs5QtVs8nU5jwStMjn3bDqpqp3bYz6EBPO38AlCP7K6ShzWJsx1WvuROG6o1B2GNjYLA==", "signatures": [{"sig": "MEUCIHCzym7b1nwutF/a9mz7Gvn7ia7XOk615bRTWmKLc1PSAiEAwsbEGjFGe9CAN6B/mA6rCa97+zxOj6H+zzxiXmYDubc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140954}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-menubar", "version": "1.1.8", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.8", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.4", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "78f43f112ff670f1c2d888bc44ca68687f5e2d80", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-PTzbsSr0JvibUHcdiAZy1QzTu6zsWG0VKpzsjMT3hMGB9LG3N6WBafnLdFtu4LxdhBXIIBDK/M0C67Qwu7xE+w==", "signatures": [{"sig": "MEYCIQCZGytUfEVTs6BQDDkhNgET7xLV0u2jtSkaSd5tl/ys5wIhAJGZQsp4sDUvLd/KubkeepyMi2IRrDpXbOEh9cQQ1yfu", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140852}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1744998730501": {"name": "@radix-ui/react-menubar", "version": "1.1.9-rc.1744998730501", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.9-rc.1744998730501", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.5-rc.1744998730501", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "03b70cc1690449d051cb431a0ee9c76df4d08c42", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.9-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-2hhadP73cYdiz/NHswRSh0ZR0PsPncGl+lzeIM0PswQgjBKqNKBIr/Ecm+LSE5Fp3jjAu9WjJvveCrdmD2NG/Q==", "signatures": [{"sig": "MEYCIQDaCxmV+2mqOHiavCmgNfmLii2+GeDeEu1YBcmsxOEhjgIhAI/11TJp4j6cIAfwGV4FGLuylWoJHaNwGqGYm8662qgi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140920}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1744998943107": {"name": "@radix-ui/react-menubar", "version": "1.1.9-rc.1744998943107", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.9-rc.1744998943107", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.5-rc.1744998943107", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "54da84d279ffe2fee100b355c3e906d23dc43160", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.9-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-kz9W5GwZR/6ZgkfEKAEQxNqpYNah6Wq6uuCwyNwHJzpPBzYbD66BY8VUb/Sgy/OhPOiuymvEQ8dDYbPaCI+ZUg==", "signatures": [{"sig": "MEUCIQC5gizDqxwXU8QNQgCNp4fJsF5XY6AZDKlF9vBgEn4CCwIgP4aftdtGR5b6S/VHcTWxA1+ZqwSgXuCS871ZoJpSBQ0=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140920}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1744999865452": {"name": "@radix-ui/react-menubar", "version": "1.1.9-rc.1744999865452", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.9-rc.1744999865452", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.5-rc.1744999865452", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a3107b7514ccfa1a80e37cecb180a22f6050bb53", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.9-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-cCOUObfVklNq45kShCyPTzVbWFzDcPuY0nlCpnfZuxyk0cMP1tPzSUdoqaEKuJ3863sVSzT6Mdl342diGp6mLg==", "signatures": [{"sig": "MEQCIEHXnAIeTVFuJLF3Jee8/t8IgDaHIOvcTI8RYuLhFAVFAiAICNTQbGVwunkFszbEUVmtpL1qrcyKmtrLR1DPt2RTQg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140920}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9": {"name": "@radix-ui/react-menubar", "version": "1.1.9", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.9", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.5", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c0fc2c78cb5d086aee619d0887037b18e5d4cc6c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.9.tgz", "fileCount": 9, "integrity": "sha512-tS2KoCplSqiQLJRj3gVq50pFMycLmiN8TCBVCNQug0TZciLTIXLCt2gjjy+3e5pnt9JafDmvrl61P3xlyauikA==", "signatures": [{"sig": "MEUCIQD4sDsl3nSyDLXCQlOSFWPjQmWPYQ4BIkfKUhqXPWBAawIgARCYJmY+24f9jcN2IY2UofMQPc+vbTHHFNXL9R+kpjU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140852}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1745001912396": {"name": "@radix-ui/react-menubar", "version": "1.1.10-rc.1745001912396", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.10-rc.1745001912396", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.6-rc.1745001912396", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "43f84e2d1f259b4a650f7ce52753f730d4850b26", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.10-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-MsFtW6vgGYtybjaH/jR+HDY76O/4lMhUkz8Bua8z8lUUk5eJBGOb2mJRPJNiVysnjdtSaMO3M/yQubKACgQxeA==", "signatures": [{"sig": "MEYCIQCNiTSWRcP+nf6BZMXJ8cOgGvZ0MirQw6GxzwP0KU/JFAIhAKCmUqOe8/gP0+Fl3/VIJCSbzGQfVkdfm4Apa9Lia2B7", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140922}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10-rc.1745002236885": {"name": "@radix-ui/react-menubar", "version": "1.1.10-rc.1745002236885", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.10-rc.1745002236885", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.6-rc.1745002236885", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f669c35c5a10bf9013816326d1accbb7c7b272d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.10-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-P4mVRisCAiQ31H0IctEwBp03xBESOEMSZge9n5GrRAigYZzjfMq8oL5dh5DpibDGTh6RZisRZFoy9OBeifwPig==", "signatures": [{"sig": "MEYCIQCDcqZLuJB0XG+uDfNXP153JtNakwZZxd4XNBFFhKuVjwIhANe8tIxb25DDzAjrAeY7ksNrb18BsV4Kx+w207kE7ztT", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140922}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10": {"name": "@radix-ui/react-menubar", "version": "1.1.10", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.10", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.6", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aee3452ee1611d8ab8b1b24bb30f9c49fe62de47", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.10.tgz", "fileCount": 9, "integrity": "sha512-z6gfZ7CcqEvGTzd5zGmG+DqDFk31KS9QOBaITeMYt3xwFPRYmfNhLpKKeHLAWNrqQPbvb3RqldsBsDQOAjk96Q==", "signatures": [{"sig": "MEUCIBieZu6RgnZwE9eCHKsWdoBAu5pl+buHm7kpVUrQo/OOAiEAwrnEJ3wdxDZjQTowjnvnaN0LD6/PtKpPeaSrcZEAaRo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140854}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.11-rc.1745092579314": {"name": "@radix-ui/react-menubar", "version": "1.1.11-rc.1745092579314", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.11-rc.1745092579314", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.7-rc.1745092579314", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e4ebd8e906942c1fc992f0ecba5bb8b5ad032c77", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.11-rc.1745092579314.tgz", "fileCount": 9, "integrity": "sha512-J+yKwsAPmoEYQnAuZHgeBydB8mgcaWlW/wjbgWo74s23csVlmpbaUvG833/6eMesl2rUnHxird3IlJzJt/5JvQ==", "signatures": [{"sig": "MEQCID/Ihb1+POLutkfFKZxMeKPul52ZFJv60uDfsBmxujS1AiBo07NIq9dR2WawjvXfnu7N+ddJCitvsuZ2EDq7wMehgg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140905}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.11": {"name": "@radix-ui/react-menubar", "version": "1.1.11", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.11", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "691acf537c921cb0f55b05e2b6cd48f6e56929c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.11.tgz", "fileCount": 9, "integrity": "sha512-p+eVYsEiIyJOgVeUNqjKPSKWQAbRLQDWJHkAHODZu1HLFAAz1G/yFinEayprzJnmmH+FqUD/LjHzFO4qNj+GhQ==", "signatures": [{"sig": "MEYCIQDas1nZCHXWQp7+SVzXdku3j8sM3Z0tEKsg/6b01nsWzwIhAKYm0UmXBG8KZBDg3Zyej3CEMLUoKE4ppGuRKRIKUSDN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140854}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1745097595920": {"name": "@radix-ui/react-menubar", "version": "1.1.12-rc.1745097595920", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.12-rc.1745097595920", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "36726251d17472d250b37ca7e40d85faa4d4b5eb", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.12-rc.1745097595920.tgz", "fileCount": 9, "integrity": "sha512-F8hIqHpbqcfJ/+Kb2qACdTrY53d2PNNDbQ76xYgBlMgl9p6fQ1nIhJDeOlqT0VpcV54v2P8O2dtWGq9erhZ34g==", "signatures": [{"sig": "MEQCIGdlvBW9rAhx1UVBlr/nVPKpKroCGAHuF7hnxa93jXjWAiB0FngY8C7WkSYtLJ+BBtULzrJ2QH8IS6p0pyFezJQGtA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140888}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12-rc.1745339201309": {"name": "@radix-ui/react-menubar", "version": "1.1.12-rc.1745339201309", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.12-rc.1745339201309", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "11bd1584800dec417df148dbe1c06c74e165dd70", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.12-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-YA<PERSON><PERSON>Z4dYENhusfbctXKzkAAjOPHMkx9UiS22CL3VujGtH6DjMFN1VaEn4lK3pozySPO1xNKGwD+owLruYTqb+w==", "signatures": [{"sig": "MEUCIQCOEQsNkam1ymB7t44J35YX2dTBki5YQNGoSUicydIRzwIgcCCvCkLX6Fg/kscOfqsN16rCiyY7mZ1qqA2fhnY4SLA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140888}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.12": {"name": "@radix-ui/react-menubar", "version": "1.1.12", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.12", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-collection": "1.1.4", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.7", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dc6ea82bdb8dab80fbc46144eb3d757a6799676c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.12.tgz", "fileCount": 9, "integrity": "sha512-bM2vT5nxRqJH/d1vFQ9jLsW4qR70yFQw2ZD1TUPWUNskDsV0eYeMbbNJqxNjGMOVogEkOJaHtu11kzYdTJvVJg==", "signatures": [{"sig": "MEUCIQDRo2WBQvzLyOgFN8pnTe9iPPrGJkpc9b9RF38cBOYslAIgOhh9PnWsGsXqWYvnmkBUmtIwkXnFt5Knh0RcfeXlhAI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140854}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13-rc.1745345395380": {"name": "@radix-ui/react-menubar", "version": "1.1.13-rc.1745345395380", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13-rc.1745345395380", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-collection": "1.1.5-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8-rc.1745345395380", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "50d73d421f25f2b49c9f13b8d16870f5bea2fde2", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-jFh7hfuaBa5E8doJGZrDK5HzGAudyso+6IVdGOXavY+EcD0GzT3R/ObJDiGSBoL9/+aFJtSc/CU9j93WXx+l1Q==", "signatures": [{"sig": "MEUCIQCaqelSomNR9/KRYpJVfmu9JqcQZvdh0ulWWDQnXdsjngIgevu9POcXzl3GRmvvbJdQrSAWnvyZzBQA36bK9OzUCY8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140939}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13-rc.1745439717073": {"name": "@radix-ui/react-menubar", "version": "1.1.13-rc.1745439717073", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13-rc.1745439717073", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-collection": "1.1.5-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8-rc.1745439717073", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "109cf1ccb95794fb45488357c556b71ce212041c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-VxEUNUPKBoJI6Swn+iXf7Z2s5oRPZYhNMaKxHKCx/DR1gVVpji0U8Wrggh/QSL03rLdVD1J+NPxYAa2dvaOXEg==", "signatures": [{"sig": "MEQCIEGDWeL7S/GgEoD0gn+xpxcU4ax599dlYX4PiRFeOJNkAiA4wv02l9UEyYZrh61Y03G+EPAgufNZs7odCZItP3XGjw==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140939}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13-rc.1745972185559": {"name": "@radix-ui/react-menubar", "version": "1.1.13-rc.1745972185559", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13-rc.1745972185559", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-collection": "1.1.5-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8-rc.1745972185559", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "16f24e689b8e97a7d3edbb97c59afdfcde92534c", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-Zd/Dt96gr0kTD8Lf3lvzPMk4IOjiiWQVK3uSOkU4iRmPjZsxZVLUU7Xr5hUZh9AYS4hTXMwqwE+UbR9PKGxZrw==", "signatures": [{"sig": "MEQCIDHVrzzmy5PY0Xfb6/xfeD3+DFmWp7NQcw/6RzNElZdtAiB2d8Ls1NhRJ7FwJxizrghztgq0yQRFXNzfwz2nKPiY/Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140939}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13-rc.1746044551800": {"name": "@radix-ui/react-menubar", "version": "1.1.13-rc.1746044551800", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13-rc.1746044551800", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-collection": "1.1.5-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8-rc.1746044551800", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28b3009e09ddac23ae0b3e6233ba543fc1e6e267", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-WQL17uCOW9KrSTRX+iem3FV07wowEiQtBarlokNRNqPowTfENtNp3b+plvNRuhCVAyIntsWKAwKmlvD3+bLzYg==", "signatures": [{"sig": "MEYCIQCxjo2mHIm0uyjrBL/nY/g2+W6CRpyx11D/Cg0NAg0gZAIhAJV76lsfdX6VJi9GCP+6IH2g+rXUndAIjcDS53tPKexN", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140939}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13-rc.1746053194630": {"name": "@radix-ui/react-menubar", "version": "1.1.13-rc.1746053194630", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13-rc.1746053194630", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-collection": "1.1.5-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8-rc.1746053194630", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "599547cccefdbb165a3ba88d35843942d0a14789", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-9vowDYHyfyBAGixb+WSNBNCwIAvvFD08O8NrJJZq/l8vjzC2Se7p+4OCGDUzE1LcdiUk1W3kcjgFmJrsFEUKnA==", "signatures": [{"sig": "MEUCIQC84b9lp6YaxBnaLpie9nqvpuzKvAysxsYmdb8vYGOWYQIgX5oP2CbD4g5xHGAuS7z8HenJmJIPzatBx+rdqyH3j1M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140939}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13-rc.1746075822931": {"name": "@radix-ui/react-menubar", "version": "1.1.13-rc.1746075822931", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13-rc.1746075822931", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-collection": "1.1.5-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8-rc.1746075822931", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e507a2f3182e183a4099deca9f15fcb28b276c04", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-mxjQHX4vrsx1fphadBZTcYxug9IbW+sEKw0W+HlqIFlT8p3Aoo2e8kLfGUFpx11Wgc01JDpFwzmYcqeSCQ+eGw==", "signatures": [{"sig": "MEQCIFWhQ/FV6+oQqFgrKfxntLD+UiZKywQln552nbYvBmATAiBLqb2ZRvGoLUQFXBc1V47j7RSsFuLeD41UQwkRKnqYHQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140939}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13-rc.1746466567086": {"name": "@radix-ui/react-menubar", "version": "1.1.13-rc.1746466567086", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13-rc.1746466567086", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-collection": "1.1.5-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8-rc.1746466567086", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b624204f5fbfe4ca70430d45726fcefb3f42f9a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-LGVKR62WaTkXrENZJ9XPKBOkNjwMtOp25x2EVkJhonQxc0jOq61iSPNPSpb7MtgJpSubWIMbUuGInBL4XuXHrw==", "signatures": [{"sig": "MEYCIQDULnojDqGLtMpJUIUAs15nvd5qdccPaTKt4Mg1LCHx9gIhAONt4ogaliUXNlkjjBdaMXV4uAcykfzixtyudcEq/Dcm", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140939}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.13": {"name": "@radix-ui/react-menubar", "version": "1.1.13", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.13", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-collection": "1.1.5", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.8", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ca65f3b4dc690d297753558a9045fbd5ebc180e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.13.tgz", "fileCount": 9, "integrity": "sha512-R0MOXUaRocqkxRzXEvGA3qa1Ug2A56M33ehoABJ2vRoje0dIbbQJeryzAs0AKVCg+WFkrb4O+mm4776JjbSTbw==", "signatures": [{"sig": "MEYCIQD5WVuYhQYJT3Gx4qSJ42oU2xfr0kTIl0oJov1TKhtZLAIhAI54AlbZNeEzTbPnwFCEqAMTpDglDaK/wb8gHU+a0Phe", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140854}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.14": {"name": "@radix-ui/react-menubar", "version": "1.1.14", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-menu": "2.1.14", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-collection": "1.1.6", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-roving-focus": "1.1.9", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@repo/test-data": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "53ee2ecfbf6ac52b68fa3b53aad401b8886a9d9b", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.14.tgz", "fileCount": 9, "integrity": "sha512-nWLOS7EG3iYhT/zlE/Pbip17rrMnV/0AS7ueb3pKHTSAnpA6/N9rXQYowulZw4owZ9P+qSilHsFzSx/kU7yplQ==", "signatures": [{"sig": "MEYCIQCdCW7uVGPTDSv0ti7G936eEIxS7q48V7hYL+mwRvvzTwIhAIEvo4971XLo1Ek+ElB9GPkKtIXt3u+EnshS3dDcQeRs", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 140854}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.15-rc.1746560904918": {"name": "@radix-ui/react-menubar", "version": "1.1.15-rc.1746560904918", "dependencies": {"@radix-ui/primitive": "1.1.2", "@radix-ui/react-collection": "1.1.7-rc.1746560904918", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-direction": "1.1.1", "@radix-ui/react-id": "1.1.1", "@radix-ui/react-menu": "2.1.15-rc.1746560904918", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-roving-focus": "1.1.10-rc.1746560904918", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/test-data": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-Q7L0L23XhC+ziENLByd6tqgXamA3Q/qDmMYoQFUhxDaHwU/Yj7BQJVtEelbTQIdhIA4hedozhHhCcAk+qCHJSA==", "shasum": "40a6e91114d3f0aa75653e512872d581d8329236", "tarball": "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.15-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 140996, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEUCICEmZqGq5zl0pDtPpj3uIwGndrf3ks0bF0F35iWw3QntAiEAkxPN2PCiviYn+2IBY8sTEiyniQoDEGX41Bikivob8/Q="}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:50:04.591Z", "cachedAt": 1747660587724}