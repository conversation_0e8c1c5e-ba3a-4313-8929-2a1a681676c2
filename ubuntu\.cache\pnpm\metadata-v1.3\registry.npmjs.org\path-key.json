{"name": "path-key", "dist-tags": {"latest": "4.0.0"}, "versions": {"1.0.0": {"name": "path-key", "version": "1.0.0", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "5d53d578019646c0d68800db4e146e6bdc2ac7af", "tarball": "https://registry.npmjs.org/path-key/-/path-key-1.0.0.tgz", "integrity": "sha512-T3hWy7tyXlk3QvPFnT+o2tmXRzU4GkitkUWLp/WZ0S/FXd7XMx176tRurgTvHTNMJOQzTcesHNpBqetH86mQ9g==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIFil/aONubdcPkKI8x1nPcfZ4bI/VC1XTYu+D/7to/WdAiEA5TI3NcYdTOewnJeVl0vLUXM9NpV6hEPEVB6eC3g5l94="}]}, "engines": {"node": ">=0.10.0"}}, "2.0.0": {"name": "path-key", "version": "2.0.0", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "a07e1d3d81ee9a21e4fc70d0fd765f3022e6f70c", "tarball": "https://registry.npmjs.org/path-key/-/path-key-2.0.0.tgz", "integrity": "sha512-0ZKacolv78i1s63zunrC+Xdhbav6+B6aHhgCDcXCwGu4KN6jW8wD9SeLKN8G8EEgACsVTjTESQKNK6rGQ4HknA==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEUCIBHV76WmAV0orqHhSw0tE9fd826mKNdpmgmjaCvMo2mBAiEAtGQdify1uNB+/w+C5po1F16EDs557A/IvvGIok//7Oc="}]}, "engines": {"node": ">=4"}}, "2.0.1": {"name": "path-key", "version": "2.0.1", "devDependencies": {"ava": "*", "xo": "*"}, "dist": {"shasum": "411cadb574c5a140d3a4b1910d40d80cc9f40b40", "tarball": "https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz", "integrity": "sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIA1QymvHINnQoBSpli7ha3x7VFlfTiXfTS40xXQP15MXAiA/52DZRtxKaGFcNH6pX688Lpdr0QIkJCzU9letBPVwZA=="}]}, "engines": {"node": ">=4"}}, "3.0.0": {"name": "path-key", "version": "3.0.0", "devDependencies": {"@types/node": "^11.11.0", "ava": "^1.3.1", "tsd-check": "^0.3.0", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-zLY/S2T5y8zv1vEpx4VHguUgmtgkewofGKSpa7VmzdU3Jbu8JonUvt5hzjBpJvamSnusynqJiYwkbi22aTo7Rw==", "shasum": "4c459253329ac9abfd22a456a18a49bb3394f271", "tarball": "https://registry.npmjs.org/path-key/-/path-key-3.0.0.tgz", "fileCount": 5, "unpackedSize": 3721, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJchRuJCRA9TVsSAnZWagAAbW4P/ji9nDoz6ngkY0cPomuI\nQ+wclEXxSsySYPTjvcQYZQYSrJCpVUJIZ+Ut4KkFj6tidHxCzGPxKvGX6Jp/\nbYzelAwtg1X6oKQVhv4dBdV1+sCZWLz53mpIPNJb9rQBNcaPBHtc8iQu0Mww\nMBgGzvlobADDZQfk5pxdLOp92db/iQIOXP7OzPynS6mrLkU2pJpkx1KJjj0R\n+JZdd/4cEE4PlSM7UTwDZa/XlJpj5RXI2zjHn31bLz8ozgGb3i9rjEODlZI1\nc1Kk23QZYl3UFp2x7Ixj8fyZPIAZcaghBKiQt2kmQyDAF11T2vKKE6anSMM9\nh9FoHR9xqjuzFhcBOK+LhMCDrAw4wIxLsfBFwgkT4Ao8HJnMQnSoTHPcXAUB\nnLn6EQjrl7WknMpwzud3m9nlKNxsLmJi73WH/Agh82ltaT7N27msnV9Rr89z\nK5KsqEo1K165LV73O6pB2AnH7xJXslAPiXLZ5pa0R4EvjF+bUNDPZcWm9nMF\nQc7V3o5D+xkcpWFlbocgn60MoqBKcB0yWPwy2OHZQas/aIocRtGDTpdb4HaV\n+C/Zp87U7bCFDL5K5vWpNl5SPkswCXdqi7lt4zP5q6uVDxJtMQtfJId20YDd\nfmi0SpTStu87ak1WouJGrH4AtZG0UVj6scXaPsNnYR7B7qZRZJQcL51G4Aqa\nW4Pl\r\n=QINq\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIG3AyES+u89nXE4cU6XhdeoDV5tncQitKayomAF/4dnFAiATxi5f0Nnzv3gpy6uD92fc2mSpt/E+fAklFEOAyvGjyw=="}]}, "engines": {"node": ">=8"}}, "3.1.0": {"name": "path-key", "version": "3.1.0", "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-8cChqz0RP6SHJkMt48FW0A7+qUOn+OsnOsVtzI59tZ8m+5bCSk7hzwET0pulwOM2YMn9J1efb07KB9l9f30SGg==", "shasum": "99a10d870a803bdd5ee6f0470e58dfcd2f9a54d3", "tarball": "https://registry.npmjs.org/path-key/-/path-key-3.1.0.tgz", "fileCount": 5, "unpackedSize": 4172, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcpvs8CRA9TVsSAnZWagAATFUQAKL3ob3wLUEir7iegTpq\nFeruhlVSBc3qwBzdsuQRnkAYsLwRkOtN2H3Hzp3X+qeACGrP+gzEZ1qmar7n\nttq8C0GqAwtHMO8dFM6b/YxZx0b2haLzmjEGSir858dsTE3yfWTsDWsinO66\n3voFik5GijfRNeiIZZ7pYGX3YhW7WnuZe+21Yh9y4Kuvz6BTszJSQPQj4MHi\n2xcBynpP7048E7q+OKHO/UGYvodctGzyXfy37aoq+kDmQ9igp7xjZs6rxTWY\nVGXrG721L3OsCcSZTrpClVjxzKlzgj+C6aNj3IhqjQ/uBd9OwbJqktHqe+Vk\nNE4nvSAoFDOgf2utUOLsjrIgCH4NYgDQNwZ8pfyr1TGjjjwZfn9XmtjswArj\n2uydJO9pkrfemq7LS+LxcGydtaHqR27CTOigd13Aba1pguMAxN2/508Bx9jd\nY6xcwK1lxRDBZMGq8bmZVYlE74DnuieOd+BkX8wcYFQZoT1W0/XH1boDQgs5\nP/3C6kG1BOKAagPrvMJwr+PGXztFDAsxuvKYU+pMzpOmyYnIcXjd/1mn3DNH\njKjBbLxLV2AIpciRHpf2Jl/iI6RTXkaf8uZJrC/pgRQInwtNSKHRZPhFvkps\nqLaQRSKLEg+/KCfo7GAHonve3/5cJv7xtgnJabl9rAv3RSuw2KgZUn+7GSvQ\nZCAq\r\n=GJoB\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIH7KI5YhHlfYsY94EpCm5+1jOztBdxDCgeOnZGToRhVoAiBsfsXpU8CF5+8Yb1iS6aYUy4bPj0os/p9HOjzUdsivgA=="}]}, "engines": {"node": ">=8"}}, "3.1.1": {"name": "path-key", "version": "3.1.1", "devDependencies": {"@types/node": "^11.13.0", "ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "dist": {"integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "shasum": "581f6ade658cbba65a0d3380de7753295054f375", "tarball": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "fileCount": 5, "unpackedSize": 4553, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd2BEWCRA9TVsSAnZWagAAYX0P/Rz/b6MNfPe+VoHlRz8Q\nJQlrCaHeSa+2Hj4XhEt+DWDfpDMXX5OcgAOiz6JzF4Hnsx+VGpQZsYkp7F3q\noQjcL0pQ2sA25U14C5mP06VMKGMmsDnXMX/iIc7VYyC6/JUGdtxAOs42pAJG\n9fs8fha47T2gFWsAziytzdTODop1UCtbfDTsOrO81KUzMfhmLAxVaIBHztaD\nOgdxOeINVMrqyCplRnOYHXjjd10CbqVWBmqtyIAMJj84cHitF/03ZHhpvt1P\ngzpTkH60nVm3gaMnMz+akGLapyy2I72MjZcuRx3Tkw8Qqwm7lGygoE/UCtfR\noo5MtUSgnPPLZf/BHuRGwd2L8rCxqnaLYO6su05/1WC4MmGacRni2czBybTp\niTUh30Lzhwna47u+VfWTv77kPlV3IqcSSMnKVCwF4Ea4Z+diQ8K0BCzp/N7U\nXxpN1nh1jqR8kMcQr9ybgTsQ5Xv/x/KNvMNpGnBu/VfAuy0q7XG4k0efI4NJ\nGXtl3LzeTgw94KtVeaC5V/+iBijPvO2TGCoOoONaSkGPP8UlLaabCr62O76h\nxe15coJ04Yz8cPTzyOOMkoUJrtJmG6whwI/OqIxKaNKGUzL0u0YZDTepeJD2\nYw+LxPkIJwaw5ohi7nMFh6pR4Fva28KOQvbYUMM80/R1VZDvwMBqpQCJNlV0\nYPc9\r\n=1qcn\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEQCIAwOqg8TUPAYc0BU4fVgYQw5sg3cZlPq2S90+ei63ZrEAiAsWC5WjxqdP1lbFmNySWF/BHy6UfJwkONg7eFU55uHwA=="}]}, "engines": {"node": ">=8"}}, "4.0.0": {"name": "path-key", "version": "4.0.0", "devDependencies": {"@types/node": "^14.14.37", "ava": "^3.15.0", "tsd": "^0.14.0", "xo": "^0.38.2"}, "dist": {"integrity": "sha512-haREypq7xkM7ErfgIyA0z+Bj4AGKlMSdlQE2jvJo6huWD1EdkKYV+G/T4nq0YEF2vgTT8kqMFKo1uHn950r4SQ==", "shasum": "295588dc3aee64154f877adb9d780b81c554bf18", "tarball": "https://registry.npmjs.org/path-key/-/path-key-4.0.0.tgz", "fileCount": 5, "unpackedSize": 4069, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgcELkCRA9TVsSAnZWagAAy3gQAJ2VsZWgJZZQxBORfE1W\nYdWz8zTgZBp+ZWFa5frmfzVH4w4TiA1kns8EoSn+OFw4k4StZwkcfverCiXw\nSin2/so9+zIiSriM1di8lGs6h35YfOZLTW6lQEgm73JdfLTtGotPujV8g8l5\njB01BaBNUBxfvLb5fxZV6rauvHPG+tafhomm8m55aCA4q+xG27Ufol+1S6G1\nD47Bg3vFEvs/3UBS3CWYbydRYlkED773c2az4unrOVflYX0hPNaxVR5i23fb\n1JpgLi9h0t8+eMRiAe7CYsaSe+yH4BXl/Ld2fZVWr01BjNhcaIsaCHB+R/Ws\n1EWA222EPFxIQEofS4vQ2r07ikyirJXdPf9iCZaKpBjEvHGeEZR2oKQvadW8\nRS74BdIFTs4L47mWZ4V+nwOb98zJo9dlUIclpNuEkWb3q9TrtQ62jkcvgDX8\nF2msHMkUStD/l0vePQVQgHVM92xSXRNsJrWxiMZClLLZzYAX8gxqpIOxFUAT\nAT1LMfk0xofNZqsOVWrgW606aVlBNhZGr+J9dg58HuZXmnfEx/mBcsB0zwPM\nc21SSnsmUDnjHuX8PfJQzaat9sitxnQvNkjCYxecXgTh992BirzLju0gMc5l\nWIY0vEhBOcMDwyXcKvUNS+pEBWNt5xnalmBkD9CVZsdnRJjz+PYJIN2AwWVZ\n8OJJ\r\n=H3Kp\r\n-----END PGP SIGNATURE-----\r\n", "signatures": [{"keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA", "sig": "MEYCIQDweU+QrdjKgSpk78xRNP5Yfffb0RWhBJklnQJqATjPUAIhAOJWyOkUtdp+JMQdY8ZMD6v//I2JnKvIJrQ8s6/puTrJ"}]}, "engines": {"node": ">=12"}, "funding": "https://github.com/sponsors/sindresorhus"}}, "modified": "2023-06-17T00:09:27.470Z", "cachedAt": 1747660590218}