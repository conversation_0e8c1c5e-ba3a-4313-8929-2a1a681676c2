{"name": "@radix-ui/react-collapsible", "dist-tags": {"next": "1.1.11-rc.1746560904918", "latest": "1.1.10"}, "versions": {"0.0.1": {"name": "@radix-ui/react-collapsible", "version": "0.0.1", "dependencies": {"@radix-ui/utils": "0.0.1", "@radix-ui/react-utils": "0.0.1", "@radix-ui/react-presence": "0.0.1", "@radix-ui/react-polymorphic": "0.0.1"}, "devDependencies": {"parcel": "^2.0.0-beta.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "79be9f81cabcd7e9db9d75549483eda68336c779", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.1.tgz", "fileCount": 8, "integrity": "sha512-PZiqYHxqOB1CqaDk1TvzKyyb9yZsVxydGfRuNwro79bmV6QYtWoXU6U/+/Rzuc7EAvfA0RFt9vyiD3FAQXFreg==", "signatures": [{"sig": "MEUCIFrW496XBnO1WDGVv4VAX9PJIKpTOYiEPL4OE+pzw4jxAiEAsEJviHPOVxI4K4BEavSY2IZ4y0qKol77zHHMJmW7DAk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 26762, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJf2NbHCRA9TVsSAnZWagAAzKYP/0ekMNtrp6QBBYH/bLBE\n0Q79sZH7M9qnpLNR25bvvVJT6miXTWQ90gZ13WHPh4C5lUMOiFtipca4ZfU/\nZhRwxizI12a3MuzFTLmO76su0TY1gTwhVVxnElYCgxgTQQHuvuTrHchd28eM\nuvpI2d7mvD19FrbNO9Bl0rQ40HIXt9bHdJmt5jOBrFWVcTMlXGRPEp9SNNcx\nGsIHwaZu1yHuKhN2cWPKBzrpmzygXXPqG18Ise6e/93UMUWQPe3DGivr1yy1\n3ejbHF5OeefjqVNsa8GO2FQR/SdqAjbLLfXYdw5TlIFo301EY/WDb7dN5Q8k\nbmjtD8LforltUBUSSVoNqe8/GMYCfTwYALg2N4cOLrz6n4FPBLl/VsqmuUgV\nWCoiWNNhkY1e2LnGB6CPq6bnNRKu+vSjx72lvoEUzHrf2bXc0f4qW6+oA8lw\nfLvhqioQhOjBqXCQT64V5I0wkPkqMj5vmuTbd7Y+pkIhzVyh80k7iYi6fmcR\ninCHpqMZ3gDX4sJ6jZen1whIzC176e1D3LnGFxuf6SFl1PzCv5ls6fNRla4h\nqxe8upG55vQq862NgE4RHKCpGHJEI1UkGRU4Jfuo0orca0uy+lSiUx6dcTD+\nsEXKP6CrJ8ThsaFrFwEgW856WUtAFOlproBMdx9c5gU/bU1F9CvMSji0G4hx\nvt4/\r\n=ieKb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.2": {"name": "@radix-ui/react-collapsible", "version": "0.0.2", "dependencies": {"@radix-ui/utils": "0.0.2", "@radix-ui/react-utils": "0.0.2", "@radix-ui/react-presence": "0.0.2", "@radix-ui/react-primitive": "0.0.1", "@radix-ui/react-polymorphic": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c1298729f94004e62db625cfe00b77dd1755e10b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.2.tgz", "fileCount": 8, "integrity": "sha512-7zx4lh6jK8lbnydmvJin54buBJCUL7P1PUHF03tswABCp6lTnLyra0EvbXdiyLeqzhTKoKiG927g6FbyxFIk8A==", "signatures": [{"sig": "MEUCIFoVNwc0QRmqPcSomUO9oe4xcamOtIKrpakQ9X67Eh8/AiEAzFOnmU/H0GzeVlPzZ+nz0cyhw2o/I/QNyZhUgmCCFsA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 27152, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgCwvCCRA9TVsSAnZWagAA3cQP/A6FHNlJA/AFoWJL14PW\nMeqP+wqpK4HgUqZqfXz3YtmoKe24481SmBq6Z3etyAD+WOgWLZpWkLOAOEdZ\nsLcVSkmVE5wJZLAmnq5uFuxVe6khWSJBGYqhsT5c3Xzxm2+OA3BQm7lgqWCs\nZrG7Nyj6Vjn0feB9tkj+czu0rpG4NCFT17g+rkAoCII7YkbMc4rKZ1jk/KTD\nyhKfH1K/F7hMm52DK59SGsDiMi61r1spKoVfDMkA3suvMBJUhF/TyQikU7p3\n3/br6eOMNo0kJE1e9CoRB2tdyFelVZz1CpHVPo4ek+8eWhfDzoFs3+5ctxJv\ngeWmZvJlaRYThoZlvU6Gyg4PMNFLJ7bwnQNZOq8LO61GIzRAJOYc1CPf+dG0\nTH/JI3SCEcR8PgpOXVWAID7Uk0gwkB3hlHs42NOJAwOVPjQUxL4iBWiB/63U\nrqI2t6fAbnrHourFuwxuucU+/3L294dI/zBMfCE1D2VJRXJr2MWOsRur/2op\nKtZq5LlHfoN9/+AoEy2k+0tijDYeIoaT5YFgyM5GBXpkXQ4yblZ97Cs7sL5h\nXQw49pf5z6pyPv8kSvcTqmKod8TJaNG9z7ooQf8PSZSFBT03+XtFScZAPlhW\nyfvbewpDWwDS94G6T399GR67MncYuQRz6bj5i5OdGIlCnJZrPzkdbM8IzKbx\num58\r\n=vb2V\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.3": {"name": "@radix-ui/react-collapsible", "version": "0.0.3", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.3", "@radix-ui/react-presence": "0.0.3", "@radix-ui/react-primitive": "0.0.2", "@radix-ui/react-polymorphic": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "325237e3c34f987fd2144b0c57bbe7b9b1c07955", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.3.tgz", "fileCount": 8, "integrity": "sha512-tv0UnixGMOb/Emc4L6jeCafeAdMMhLhC3tKUh92adDTIuWpsF29sOjiSL1DWfgrlLJp9kwiAX0/Ogz/PgnGaSg==", "signatures": [{"sig": "MEYCIQCy8yPsx4Zg2+3+RHhoYHxow2HcgYC2l1uCiFLjgCgOwAIhAK9t0qR5NHP3Xc6vz/2trgLTpBuSXqlEPJVAWCd6PCrZ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28783, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgETtCCRA9TVsSAnZWagAAAmQP/iPmf/zeKxDB9yPpVfK6\nKqPP7jJBKBgl48U3kaCEql0rChVCJbHCsTscbz8ytxrx5W/DNrLTdju2Ck8Y\nH5HvPkLqqBED/Fx0W8virjxz95n0qbtETGPWHd0htEWMCToxNRq9NofZZ+DZ\n/RLcQuK8+D6Y7T0Mp50+hLe9VdzidPqgKy9Gv2BMpb36MGDxv15PQVLm0jgF\nvW4ryNvUq6fSHEDUGnkf7KeEoh4cWRrCAUIXNPB8XDbByuEXewkYp5PPTN32\n7ty+R4nY5tkCeQzCyXQ28bKQX4C1p+k1hxJNN+d6+S3kdMW0afY+6fbCqbDv\nlHFmN95Yy4wLGvVqQ2ik5Le5NokWXZaBZNJbFpNbr/nuVYm6Mo0a0XA00RWj\n8jvPN0/0Vlei48tZbfzAaaNflNa/P+3k/yxb+D0M6VbOyk6oxP6OmzwnC4XR\nE6Cgvb/YRw0EDm/dgjpTwD+Ymvlgw+vmM0Bk66B8Ajp8IEhh8QEh5Tr7Jrv6\n9qp/rPErHhvLFbNQaQ5am62RyF7moNVKkSWo2dfKpaM/GNCn+nTnhxVx2747\nAoiuxq4XhVxt/qpkr1VieiIqABj/CNxHf/9F2mK8TYLSgLpBYeE24LPy5DnJ\niD5wmvnjsKAjfCltmxI8MEONo80DbFj3W5PM/0+g6naifYHgTbDdpHRt1DrI\nFuSV\r\n=CHAA\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.4": {"name": "@radix-ui/react-collapsible", "version": "0.0.4", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.4", "@radix-ui/react-presence": "0.0.4", "@radix-ui/react-primitive": "0.0.3", "@radix-ui/react-polymorphic": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "02b945411f6c657b187967882f93e349031eee7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.4.tgz", "fileCount": 8, "integrity": "sha512-1u2Tf2pn7JewVRHxGuqTo0YMXWgd2LtronJxk2HPiAM1J/MANdkT956Oh4R6A7VpJwens1bw2YWIrV8dWI0ZXA==", "signatures": [{"sig": "MEQCIE4APO+jf9RrWAofWsohy022yKorEwQbYLm3IiZ4fSwVAiBnG7/8QvnarshLckn9FVnc0nNaHPX6PPFKLAG1h9ZUHQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 28620, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgFC/zCRA9TVsSAnZWagAAagIP/RlDYnwry69Q+cCSvsOz\nKYK3TVmmoY9WLdNDUNxb3eCcqjVXdoDzgyTPG/yiNyazbyzrLpe1fyIADwCk\nCtt6q6NqdMisSUwKmDzV7zCSijTAuvHaUxCtyVr0jI+nbKc3D8FcDCwoD+/o\nQaKOR7MCULWE3GEdMvBHMl/UfrsTlvOCt65LQYKJeP/uS3rS36Fm1ElG3/2k\nWnXwtsrwpzRAbMayHVLTCCJF2ifgFALJi2HfMmFmSHzUs1RjYVuCEi11+v+r\nUjibmaHpDFqNuC3hBgFdXxSqUtoKJxpPjQ6dfBjuGPOCnEBA3uZR+l5bHmVc\nqYRR0zGtOhobWllc2kcHWu+Ee+A0JLvYG7RfXo0KzuaV0EQqnfx7gUFVyWZe\n3EnXkhtZG7PGKs0hARg854k1iDS0gFfXBRPoB4UTpspwXWJ/jcT96ycqSmGz\nYbxoh0AP0KT5hz7uy79S26KQdQ0XbQeQn6tGDjpLqSeVfTIYAgA9lF9lSNIj\n4Awyf1FD3apbyRl541bG8DScVcVJ/cK1d1RV2hIJ/Tk+HdJnuLp1b1SpbZw7\nUSeUmeHh6kT3vOb7clJ5jtAya1APFvWE6HVnYZZ2C5o3V+uaNN6VtHb6Q8GF\n/Zpv2sSUi0FnRktqWfep9X/vvIfHX0IU1+vbyYGUlmW0tMR6LaipI9X7c3ua\ntvlD\r\n=naj6\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.5": {"name": "@radix-ui/react-collapsible", "version": "0.0.5", "dependencies": {"@radix-ui/utils": "0.0.3", "@radix-ui/react-utils": "0.0.5", "@radix-ui/react-presence": "0.0.5", "@radix-ui/react-primitive": "0.0.4", "@radix-ui/react-polymorphic": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "640d01edb1db4570bf310e67911968d276c54393", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.5.tgz", "fileCount": 8, "integrity": "sha512-L9BrsNz61nzeAyGeDY1PSwAGVmDK9v9iETiYtRdjQvMuTIGL2KfDBHeiqGYd3fPRbcqJjHP5IsEdpbAhPykWug==", "signatures": [{"sig": "MEUCIHlug0724ZHwPhAaU8un08TcKeWcIOQ08QGMlZPw/NeNAiEAzI0P3iicPnhhp9VcwGLukh689p1Y/xXZLHcPAMykfsQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 29433, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgI/VtCRA9TVsSAnZWagAAn7QP/jadrYxaTsGh6wlp2Ta/\n//rDdh9svTi627d08q989yUL7b8iWqkfKO7824L/DqvGGnFaB/t9XmpVnCiR\nEVVF7jwqiiDKhwqpy4glkGlsTO51TtuayGr/vOJDd3x95gPhNTNqtbU35D1B\nTcxnVSWH9afwn961FYsXh5Q/DcZBw3OmewsikznGzt+bOPO4vqwU8I3hUe3e\nH79HBE9xbc8jhRf/DHsEQboOYB6Oy02f9YWG+Y6PKSCgLkP5dCMVgDUG02YA\ne9bVoAImAsCfSyEjxrSKuIE+8V/o2EzE0cqC4/L+BsOi2spnqGtaFGkTxKjC\nEtBQ5Zs6aY7e4aPl21eDULQZOMEA9ouB88EYPHF7PmSCHVHfGxjs2v8adRU6\na0PzO2N56bpJv5ncviO7MYKyzABjc6vfGG+usJMOzLwW7iu2sVHOizw12buF\nhmSTXKgcknRBEiH7F+48LshHVu2/M+D0Hb47pmUJVuokAjNh1ZPV09MxCzoK\nwsKoNZ+rbZCvRlDoBqpIlQF/ZfD9gjjfeyRe8CMNMihfMhoPFgIfp5BxZqd4\nuKzKAd+ESzA+NbS8NYH1VroRQoWUz60jAWlIejSqLZEcyJ2HuNVfIIGAGoab\nnpTXCLZDwb0yEQ10ij0v0YxLpXE7fXml+/iWDWFd2TcNC6EhwXa+oqLl9s+0\nQMBg\r\n=tLZv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.6": {"name": "@radix-ui/react-collapsible", "version": "0.0.6", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-presence": "0.0.6", "@radix-ui/react-primitive": "0.0.5", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d482444cdd79c666173157dedf4a571dd7e9dbd8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.6.tgz", "fileCount": 8, "integrity": "sha512-aEUhDPaSNS6441mP0F8IRLNsUSiDzD1v+wARgkZAKMYMugyWZtSD6ir8A93KnQAp1B3Rzf/X2imFNzOFdpZExg==", "signatures": [{"sig": "MEUCIQDzh4Xs/IFmBWtNI+8sth9j0X3r1eehJWMRW0IENrVJ2wIgZdh1CAwLsSDVIds/rcXXQ3JL5BBlukTssHdVostzqmc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 25802, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgP+VECRA9TVsSAnZWagAA5eUP/RytgOtyYQWNK0yuOc9S\njCvVPK9qRO3/Wz5JOjJXvRw0Zi6IiLLdy5LrlqLVLlNJcvnqX584XTCq988s\nvhQZXnKEl7ZviZZeL75SCJiR0VgDaPbSQ5LnSZ6mICpBEhSQF+tJDoHnDLmF\n0PvgVuTDYsLZ/fdScbiwEQH8rJ1AIoScKvRzRxLtLzBOneArer/EQE/BnEBz\nbxeOGU/ajvNo2qFoSqaFDZCliCyeaqsP8bO3J0Y+5Nic56Z7IwYpM8iwATC/\n+RNIyEZwKfMTpB0zp6/DruXepk627NhL/xXW4B+V26jcHx28LItxWwbaJIqx\nKlXiUYXbnJqvci1+K4DqP4sYiKnpk3EX4hQSRFS43gQ0V17+FpFMi5Ly03zs\nhwv24DZx6ZtKwaA3bfMCAQKUccydicN8CTlkPAgJVuiSeLXdgiTkrPQW5ima\nnG0QICiSebSMChf1KAf62M0BGWcFGfkE7BvnyrP/quejzuNArjUwFybSZ3Bj\naWSV+8+5PVDKAehuYJT7YwpzxeBtbiTkzvQh5A0Nyle/LCPJ45xnwekeIF8V\nfqa2pX30CM5x3iFbTrKnrQPRVQzarLizCZtqIPiReHJI13lozEkcqav4VyLf\nKLvVh9+ipdgVcgXcbuWMbuCQw3QrAdoEujb16XWCWl8XPtqCUFdKZ5vwtUJp\nEDQs\r\n=Jl/N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.7": {"name": "@radix-ui/react-collapsible", "version": "0.0.7", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-presence": "0.0.7", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8d6a4d440b13b28e712f7ef977b9461768c9049a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.7.tgz", "fileCount": 8, "integrity": "sha512-gp8stnomAErMolLeG4D2G0WdUCFX6Fj8AA1Y4u5kUV5lxmCQJhOESVaWxPdGilYGtSNF+gAvuylTsm2VYoRoNg==", "signatures": [{"sig": "MEUCIQC7T/OCJ28D+p0f5A7WWwSYijQeg23RXTGnaIBtmKWU/wIgcil3P092d6Ecw2GeNxSUoT/9VmTYNHOdsgafjRfs1tA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35653, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgQmVxCRA9TVsSAnZWagAAWucP/3uWIkY8DezobmkdI5ih\nhlUu3829lZxxJdKXh4vvHDhkz1EdhFN90s+57XbOBCyhfTcs4pUasLV24ATH\n6xlC2cDaRrrCmfFFSDvrq5DpcUIwuUkXMSyE/nGyvJqvqdteX2v/Gt6RqgH8\nSU5ftXJkVObAO+OCml9Yd6NbtGApyMHHRDBCCUyQ0pBHB8FqdZ71uDh4yN1+\nUIfMC156YFXssVN7j55jBaEIaKvgO1L+jIew4gzxeFXJm5i22qbG37Uu7J6B\n7t46weR8YSkjUPtuaOd/MjsYdgFEC6hoUF8WeB0DAo5ZOCL8Oiv/oE1ftYRq\n3iYg7St/Z9s81MljklMdUK8/SqEXI0x9uzyyW0YrSk62O0q/fiYm9OMuNCeC\nbd/hTDYhj33d7KrR7eYcmiLM0TAL6RQAZtBzpdZYTQ1r80lP2DKp+VxMqIgk\nlme8iO7wqvp53jSXmMG5s+BQAOMKtYMGOELyAMVqDGGclkOa8OgFnfgtHsH3\noFJ7CvR55/KPkYJZpIY0VSSAEaEZ0dvz7wkOOq6eTPaEk0fZnKLCRHqUz22T\nRprmnou6TYjPLsSgmmhaNAVimedobaOxMmdGjMNyIJs6ESjZMCxcjY4jHfgh\nd8ItZVlplkoOkgqDR4o5Z2YwM8DP5xCXpBtWiGvtWtP560nPp5rTdf9dPFxI\n1Xxf\r\n=zi3T\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.8": {"name": "@radix-ui/react-collapsible", "version": "0.0.8", "dependencies": {"@radix-ui/react-id": "0.0.1", "@radix-ui/primitive": "0.0.1", "@radix-ui/react-context": "0.0.1", "@radix-ui/react-presence": "0.0.8", "@radix-ui/react-primitive": "0.0.6", "@radix-ui/react-polymorphic": "0.0.6", "@radix-ui/react-compose-refs": "0.0.1", "@radix-ui/react-use-layout-effect": "0.0.1", "@radix-ui/react-use-controllable-state": "0.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4d821065e8a7928c78d2731455c129e46292a87e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.8.tgz", "fileCount": 8, "integrity": "sha512-wA1vreVaTZcKWtNsfA46Dg3WuDOymTTYIhrQxp48Q7RrrvI4Tpg1RnBB5dp5zPjkgFzb/7Vkr5arv6MO1NbGgQ==", "signatures": [{"sig": "MEUCIFds1YRQ+qXX2/te+y+CvTQ8mlkoSeXrpHOWZ87nnxnUAiEAyUPMJGdMFE60F8ldP5VU3egv5P9jR6nX9yFFJoafzPA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33271, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWK9fCRA9TVsSAnZWagAAG9MP/AwZm8HPPnDGXlFVLAQF\nBaEbcoU/jPQLq7XwSpK93rvmYFo4euO3Cnohi/S/sUQe87uMjRKbIAULr5C5\nDBDIkRdyOVw6su08aLfM4QY+VKE6bFvWccZmYs7jK2H46GYGhbZjyQZ5siBX\nVcPWRcvQMp1dtKL3UHCBEYctmMzt8qOVajZXaPv7O/EGNdhjsSOwhOfWLxYa\nsgOc2xooQi7aOlFqw4f+v9OQTOm6ZYGDao0smxX7KikIqjxkdSWyVKCEYyIH\nBEihAX0gLbSp98ogiQ8YIGqtNum3lagmtjFpmtd5UDF/iXueQgUW2D8PnaFq\nBAC7bUkWj9g7lBX99TNcKVr1+pFkgncDv5A4MIz+DB+3mM/WiT4mrMXO4PoC\nqFmNdkAgs97KtSOXj5mi28zqV8yP8QkoG3K5c6s/s1NoMGsl+qFDLxXu8R7f\nlt+LY6AdoDt+KDbqI+iTEEiRr67zOHHSTYwoqvcCTkLLciUV0O4ng2oynQ7I\nxbxBVAKQKOjfCDjaC0konelRfUGpFxKiYcmgeOJhG6r7HuaFt9iDYLR7UTVA\nE5ezpcPGBZ1gO2qonbkM0qYFO+I+FcW3GEP8OOeHUl0HryAfaOmjWdjB7fd3\ncPLBFM1i51uQKeZora9DWM0ibCl31EdDiLZcdWhU2+XaDTrcmF5YW4n6tWOM\nsJo3\r\n=BlAC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.9": {"name": "@radix-ui/react-collapsible", "version": "0.0.9", "dependencies": {"@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@babel/runtime-corejs3": "^7.13.10", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-presence": "0.0.9", "@radix-ui/react-primitive": "0.0.7", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9f35c45594f1f2dc347a722f09cedfd2dc199418", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.9.tgz", "fileCount": 8, "integrity": "sha512-aLZjnzXY0AkhZqsfLaII3yVaFvk67nwc71cwVAazSf+kI/1QVXWDX/aTNoa/oQpMYRaNaOtziOU0Hj+XSleUfw==", "signatures": [{"sig": "MEUCIHtZFQ5M8g2oqiOhDGctUrMMxF6MjRY3q/lSGcBELcYIAiEA+e8acpCX0eZ/yZkI/PJs4sJOvDT3ZoYSunyQP0LBnGY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36998, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgWmOpCRA9TVsSAnZWagAAx0cP+QFQHSxMMaGskj5zpuBK\nmq5/o9+1Ya8msaElPANsbSEggbnJ2tsOffkwK5RZHNtP4Og5MbO4oc/vchpE\nHMEkn3xJSh0Rhxt/cDoUuahMb8qZ6kL8WQT2vbA5K0qpsbxT8nm1Nj2OBViW\nXRrtxLtlLnTWXAKP73nR5vB4BGL5ZuBOVr/nIAsRtaRFzxtqLBF1/VBu4aaY\ntqJ2KRrO1NIaw/fUSHOtswnwqGCI/LI9VWKh12vx6HEbRxNBY7Oa7RuX3nA4\nEj3vmCBf4qwLMPayvDQj3WSqAufaRCwNupQTc0Yz9uAmp0BiruRYMXIxvRUL\nbv3hLb1CmuQKnRQJf6moWAj9qBaUh63EvOnhhFFZvxUZDvUyU3uc4d2JajMp\nkvmG30E3ONi7bPC1ZEiGjsjnShFlNM+HG+FsIFOk30HMZyZRaUQP3cHKIGE9\nunXBAOr0g2OmeTWaXNQB4vH43Brjr8lPGKilQ3vmhwWOLVeyGbH5alBxaVA0\n1by1g7PrYt6vFmkhJ/vVcvw5i3PONL2HZj/vzyzuxcxBBrTwwtmpl/P9G22R\nHQmlkjDuNbevdRoHhdgtplhGj3v16/tJyLOLwxdrxFs1zuMb2evV5PVUJMiQ\ne7D79WLYOKVKG98aW7pnH9ao1NwZaNQoSoxqLk0F/N8yv7vI3cavo/7jVLQA\n5fIZ\r\n=C3tL\r\n-----END PGP SIGNATURE-----\r\n"}, "deprecated": "This package has been deprecated"}, "0.0.10": {"name": "@radix-ui/react-collapsible", "version": "0.0.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.2", "@radix-ui/primitive": "0.0.2", "@radix-ui/react-context": "0.0.2", "@radix-ui/react-presence": "0.0.10", "@radix-ui/react-primitive": "0.0.8", "@radix-ui/react-polymorphic": "0.0.7", "@radix-ui/react-compose-refs": "0.0.2", "@radix-ui/react-use-layout-effect": "0.0.2", "@radix-ui/react-use-controllable-state": "0.0.3"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "8e8f1ac44f22a7569f0b85757792339e36a26084", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.10.tgz", "fileCount": 8, "integrity": "sha512-dI34To8uF0sRD/tSnYdcdsikjc5ihfaMbCqzfvg+WWp7w97GmbvnyWIFf6I8bO76qJT/fXXNsFH8BkG5IsyjyQ==", "signatures": [{"sig": "MEQCIGSLtgxsLVAnQz3w+zsgf877T2pQzstFuOfyKYgJVW4jAiAKD5MpC2z8SN0IoEFn/1kryTo3ayZWtE8RcI95pFUW9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33122, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW0glCRA9TVsSAnZWagAAq3EP/02xTF4ICE2FezKrpovP\nn36H36XNh5dsQVjxy4plqzaZUHZqKuJ4yuDKGx3utOeQmWZbN/mZPcL2O+yW\nSKtu0/hetDFNGeP6Yv46SjcSWRdpimbe7Au7Kl6xLHrMN5x5xBKJ2/FBxuiF\ncGGtocmtFBCsHmY2TbO+WNUZ97uy5s0jzgpqp8zLp+BZbWsehhGVca/TUwPm\ngExJx9GcGPUwcHlI+kOsJ7HbE3pj0m0lk6qZdgJMNKjYAbbE6yl+dRdWPjwp\n0qFs/03ReBARm1SfzgeiFdOMqktSCQot9SBAqrNpknLkDfhi/az38iTprkTT\nO1Iy6CUUi/yUHpdjH1+ljbLKZAAIr9MeleUGb17GT109EUlOBjzvyBxA1/QQ\ndKGpCRMPSM4lAO5MjPIiFBBcq0q4HivXu6OrE9tPfylgOWzHd9ywFOj4OA1z\nsZ8agLRu2Gas26E8m62sXUv8XD+4ssRvViL4jUXsOebvw0Ta/iqBPfqHEAc3\nwwSkr+ytU6GOeVCJtvorpFtCsqpqnvmXn8Skn14u/P+YR66JTC9AAajfEZHW\nKrXluctdzV1LB0hpenvz7WGxnDccErE0jOcJJTcZH4HnxAX5UHhJfvZh9gLd\nLmNSB5VaYXqbjzdnYqXgJRxX9vg5K4gIcP/du2GHqzUzHbGjDTTY7nBtU3Z1\neiYY\r\n=JgQ4\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.11": {"name": "@radix-ui/react-collapsible", "version": "0.0.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.3", "@radix-ui/primitive": "0.0.3", "@radix-ui/react-context": "0.0.3", "@radix-ui/react-presence": "0.0.11", "@radix-ui/react-primitive": "0.0.9", "@radix-ui/react-polymorphic": "0.0.8", "@radix-ui/react-compose-refs": "0.0.3", "@radix-ui/react-use-layout-effect": "0.0.3", "@radix-ui/react-use-controllable-state": "0.0.4"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d8a5d1dfeb4c7588906ccbbba119ee15f7cbd835", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.11.tgz", "fileCount": 8, "integrity": "sha512-ibSLCzqUCv6EPrlL/6niGt5ZrGNHUi4PXevVjlbR6Q7fx0p0RHF9IWP5mAS0w4CZFMGvDOimcgawtc46uA6ScA==", "signatures": [{"sig": "MEQCIBwWmjHKmotfvuFv/H5c7ilvDayKqBsLSXvq/9hF4A47AiAxs3bQSEZPxdye1XjIRWzhM95WMYVkLb5hP1PfoExXow==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 33122, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW1HpCRA9TVsSAnZWagAAk8UQAIuA1RjrUpqWfUWVx4V7\ncHrkKns4yMq2nQ+2sOBk6IaSAVwiYfLnm7ONHztO9H7YJzRWIna2zHwW/tXY\nNplFMtFixrr/D2okviCzJ8Gi/J9lMcDoXmie1W6l17+nwotqNM0FMWrzNOZm\nG8IX4q2vvGEc4JKFgKTU1uJar/uyZomOuEw+Svo9rWlBz/5fAA6ckDIOsrK5\nQQb6G3/YjWxZbmVXl4OdLHKnlAbs8E11HCGU9xhxr/QHQGixNx0911ZFigTi\ngNEN/ir9zrUKBdIZcwl0PxCSOazA2ZfYfhZftGHvarcul14TIA9030PyO3jK\n+D+CBhTA2AK/KkHdSSPT6IHI+6n2lIRo7cpJDyd85W41V3h2/u8qDV475Nha\n4KaAGSVwoBn3w2brAmO1mA4Ihv/zHZKcjdPkO3QVfpBkppSBB20pnr9oPVU7\nOu6oxMkfOgNPICph8bhcZKsoMnpwAjArK7vKtcqJ92b+oTAhpmIdgRB6f6gC\n1asIsQPa4W7g3kKx61UwUBalz7EoYKy39TpCfdAqBEG9EEJ4pRru9rp74xyH\ndog1DqRvDq07hyuMZx8fVMXfEYYBTLKmlB0gyFDbyc+gVFNv7vpE1cZH5oW4\nZkBgrJcAtf95OoxIi/JRIrR97CmUnHkEjwws/9ZQQojyUlHMavIfGmhYt7Kx\nT1Rz\r\n=47qn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.12": {"name": "@radix-ui/react-collapsible", "version": "0.0.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.4", "@radix-ui/primitive": "0.0.4", "@radix-ui/react-context": "0.0.4", "@radix-ui/react-presence": "0.0.12", "@radix-ui/react-primitive": "0.0.10", "@radix-ui/react-polymorphic": "0.0.9", "@radix-ui/react-compose-refs": "0.0.4", "@radix-ui/react-use-layout-effect": "0.0.4", "@radix-ui/react-use-controllable-state": "0.0.5"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ed5ee2b157a249071c7318994906d3e89fcea8f6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.12.tgz", "fileCount": 8, "integrity": "sha512-4x3xiJTDVs7Pax4PT0qmoxdFQFcex3fRmSRR282sno/rlE2EPoNP2jFfmhTczp5buhBrSOIzPGEb1Fina8b6XQ==", "signatures": [{"sig": "MEUCICz4MAsBNSdNDSuv2AxCx6aCcx5u7DiyceBZqy+dFay/AiEAt1Mzvo2lthgNvpXFywF1smoakkfde3j5YRRCPKa70Gg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32520, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgW3vmCRA9TVsSAnZWagAAFCMP/1as8YR16wJ3lEGrY/Ve\nhEBSrBZJtxliXqeekaeHQTsMjxu8yDlZwDrca6il5N/GOm31JccM6riKPQSh\nYVHFJhxTTQ8vqdN/XiIw63NfH4wHznygpORAwUmgnsIFxDHv7aFC/RFoD3h5\neOtaA76NdZb4yB3gmxOqpR3xPPYJhjZK57qQu9QuSN8g4KGN1BD4nC88XqCM\nPtqcp4SM0tAlOKylbHdmRpPpVMUdGicPRWMXmBfHlDRB4snhVnHHlgg+4ewI\nsc3k0UWcOXK7f4UEHVd+ddTyUma9+mEd9LmHOKv1KgAW1c+fmOWzfY2Q6sCB\n8PQTIMvYOV21al0Irx9vIzBSJUVLDuj9VnZcCTHR3ejyW7w5ITWl8RK7GdYm\n/4THjPyrJjBSJhTXRT+jTAKb/osjGPHpDWE73y3mnl58SKq5mZrnrgl+2leJ\nzr16O8PWgJnQXEfjb7TmbwT/3auQumItTbj03JP632A7lf45xjzjb3kZbfXo\n71pSRlfwRshKk4xTE50oMaRqAkXwTmHUlzDjOLDU8st+D4duAuEpZzT465IY\nL750NVnZynlKJ7j5G/1X1JiQTck02pz9QFwyxtFEoZ9LL3ZjoHuv1T+UodIf\ntgXvYklAF1AOcXO/3kXWURo1qc4i9oxR/893aFB0F6JnhltcJf1IlHYtXUgF\n1IO3\r\n=fDG+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.13": {"name": "@radix-ui/react-collapsible", "version": "0.0.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.5", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.13", "@radix-ui/react-primitive": "0.0.11", "@radix-ui/react-polymorphic": "0.0.10", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dc662170673d725195a008acd539891cd1a11dfd", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.13.tgz", "fileCount": 8, "integrity": "sha512-MPcvUDAIQRyVozIPz1AUXPeXuujcyyDjHS6cZiGH6iOrwdLW7tTLVkELdsyLsePoMZILRwFvPJW6yfiaCtZG1g==", "signatures": [{"sig": "MEUCIESwLT6F2yIwblWm/78didnxlkhbYJCxjbLz5flh4zwfAiEAl1qidIUJL1hQvV3/i6unYBkKsKhdn869Uh3aa5a06DU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgXbmaCRA9TVsSAnZWagAAOC8P/RHvYo6592ZItiDSOSOM\n/s6s/pP9QCDwm97vyrYx5AoTFkk8RN7HUNGV90DEXtp2ar2gXFpQnJP+WCV8\n9p7dgYbGk7Dlbf3QxSICuvuAcMrG1jC9oSlw0vz0HSjPtFDevSiR7+5IGLZd\n2BRL8hb21th/y+U/48oCbnVMqe0ini4MXvKymLOxu5+WjraeDX/1XB46wGS7\n5/16JCVcrFgyHnGYFzlUW5zMYTkwV401Ytk8ZXlz/GZaKAxiG6xKTpwr1kdz\nDImKVoQd+NY1dT/zlV1Zqu5soHjtTP4DQj98v4dzjUqh72jYYyfl5ARpOuRe\nvXGGicZhMskM4gbAX3cy4dkvS5Sz38aJsa8VcTJrYACmmE5VPErhkduGPE6K\nInaC5Wtd77DSy0sUMv9dtfS6XJi8p8nV6/hvu6g8lLzuIIb12raZgcx+o1Eu\nDUKK0eBdRJ1DFzrYRP9ky1lYyZK1+9Ml6knRE+Zivrsm8E4+T+/thqW816Gz\na2yl6btMzo9+Dy5RxLl4ULM7SEQ1hObZfnqN8un6ElpGfR2AKVEQOWsmfroV\nTls6lV5ZE5aGo4RgjNrw/MvBG9sz9rArBmL4vaQtltqGHjt6u1/B+NQOj3qy\n/W55KriKkQCAIpXVG2hdUwW8pBbhP1NQ9yzuJX3PsjzA820gb52SwjshrzxW\ny71t\r\n=j7CC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.14": {"name": "@radix-ui/react-collapsible", "version": "0.0.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.13", "@radix-ui/react-primitive": "0.0.12", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9a232961a98538ace58cb5cc26af00dbd71dc4e6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.14.tgz", "fileCount": 8, "integrity": "sha512-DxGdREqbeLUcPUNjoBJ1krOYyWPRx8Ktreo/qgbQ//Ui4FRlA4IyuOq6x1pR2kkllnJ/CvhFZTR1JeeZ0b0U3g==", "signatures": [{"sig": "MEUCIHj7kIM5xPr0syHBXAGAOYt6ymWJVT730RzwCP7g08xDAiEAgS2cBNFbhrU0dPmic7GhjwjU+CGAGe2dTxE8F+DnU8c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32437, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgj/7LCRA9TVsSAnZWagAAlj4P/REbzUyvclBFnLU29io6\nPGiNjTxFZ404pERJ+QS+mnU4oXqF7tMQW1C5c4NTPk22VFNO2xyjbyFLzzAW\nWSHsfvcqil7hJ8snN7HX1Jb0KfyZoI5WcyqYFtmx32M64SQlgwzyU9B/0Yvg\nDncaTdXIQJ62+SV+1ro9/ZzvFPeebUgWarlXyQJ9CpxtkJGdskF29JHiJqdm\nB4M7OAuxQeh3ylYcEunk2A4tyagthBtL2sNu4I0yHafVrQsYKXZZaLxCnZcy\nRGsr5RL40Il7F8i+88V+wgJNUwvrM7WzGpgstqmhFXWIDsxsbPwoUPLTrSbn\n8oTApNdxWT6ycJURYfHDTQP7KON6n7sm/ClwPmZgbFpe2LL5JBkoRAS7CSQu\nkLo4psHTC7FnAt+mZaBfotwaPXqHFKpjNVTB5J+S8jBys+rdKn/ZdBjKsma8\nZoy8Zzy4ts5XIYfHgLZWSQik90JZWoc+2eXvS25wdyl1eO9zyg99XSumVIda\nWlNzFg6GOZmOXzcYItJE5bOJu12jzRDBsQow72/qXMJMWnNvLD6Ezfmu7eYh\n4J+277B6Ti36J7OnnRBg2VD/Pra3JME2eG2HFyv8nAw484ZXsH+2OgRjlHzk\n527bhaSsIVhuHTiQrPqAtNhQXXp/oNmG4uNG4DJpq+vgVPrjaJ4BOSuxEkdd\nRsZZ\r\n=8BW8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.15": {"name": "@radix-ui/react-collapsible", "version": "0.0.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.14", "@radix-ui/react-primitive": "0.0.13", "@radix-ui/react-polymorphic": "0.0.11", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a80641ed4f620d67c52b271adc4e27039c849212", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.15.tgz", "fileCount": 8, "integrity": "sha512-bLsGWzY/qJj09ZQWeZ0preb4Se2LtQXoO26Xp1uaRm9f4lQz6DakTvY4HxCQ1+kqX+CplRK0ZRFSWWvwvfAm/w==", "signatures": [{"sig": "MEQCIBBj/5uL+EReWVe+ixMXPov14Cy5WXviM8xVLrDyP2zvAiAc0GfOygyIg88H2B/UGpoliZnXtaq7DASkG1I3tmfaBg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32555, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgmlX2CRA9TVsSAnZWagAAxSQP/1kYxNgGw8B/3rDFyKRT\nknNibwOmm0GRFCjHi+BpPl7C9TJijd4JzGoqGh0KnX2PtkMIn4xlUwCyaIaA\nAj8R5BCNrIe4jvq/6tAOFo06orhJ7R8PuUwPW+kastfq+FUlXKPno/Z2/joR\nJsGwX67JroR9xUhHHunLTdh8nhAKbw68d1a3IxMVaSnRg0pFq1uLsEy99Kz9\nDIVUloI1A1R98k8KpvRxgD+TQvY9BFBWR2ZI1RlgwQQywfxHXbv/O3o1Hp/4\n6OM2kauG+/vButI2sQM43eHbHn5/uDmbhTS/tVDhjQGuYreV/EJwdoB8WSVV\nLe085TfSH4SLwxQXPP/Cb7Vr0FZGhnbXf3QBrP6CKK9BI86Jxb3cQVQBao01\nSstnGP3i6DHLOFPDf4EX/DPCdCvfnQ9ATMC4e3s/5sULNEVxNthRRJQni6Z+\nRNfvdgOkf84/5N9pRatBT65wLMdwMnOnDJiwc2z5DCoqcoYGcYOcJvrqmbNv\nEzKbQ91zIv5bkWHkjNsGPXkiH7FG2xLjcGcB2N0PnOzruVOdi1Z1QWnXSveh\ncR4UvDI7IbR2rwTlqkTDk68Dv1R7j/WlaD4Hz1dAs9eqXfLrCP9aQ3wrzpAl\nl/a1+7A9Z65L2eJ6gXSmaKOsE+exhuGA3Y0KPjMRmjCpVN3HDMPYjG8P+Alp\nu3dn\r\n=ziWR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.16": {"name": "@radix-ui/react-collapsible", "version": "0.0.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.14", "@radix-ui/react-primitive": "0.0.14", "@radix-ui/react-polymorphic": "0.0.12", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6a99068f70bb85a60f8cbd43f093bd3053ab61cc", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.16.tgz", "fileCount": 8, "integrity": "sha512-kY9wojEbrpTge6sz3BZl1oXer2Szhi+MW60TSDf14mL6l8+e4ugp5y2ItGDjcW5B7AzL00dsMtqlxuAkhFjWxQ==", "signatures": [{"sig": "MEUCIH8/oMG2sz8i1/lgIP8l9AJ8O21oTEnpfQwHi0FIoPxmAiEA1hlGDXF18aSoLj4cL1CFjKeyRUSVN/8DslRUVZIuu/o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJgyJ9mCRA9TVsSAnZWagAAFKUQAJZ4ir0TiKV7Mw0fPQf8\nReRDcUE8wi5M3JiPEkcN32PC40gdgdtMzRUaT+4dSBEyN378bHEruDaQD9PG\nWTHKjhdUu+vGqj2bawyex+v+af5WXi0Sk3nS5AmZpitVwbto6ztncj8tSrTt\nTg5gC0JYF0L+uBE5EJNq6MduSppyHE40tETWgsrkK3QlK4nPA7E+0fG9c/DQ\ndCmW9fwkmaeupqm+y5cuzFd2l+FRhTw3llPtKiegHV6dc0MkYFZ87G8OHz0b\nTf+/JYwTKzG8Nxlzq23YmAmvft7R3izY9SKCJPHwDm7jUqT/pDfhVLnD/scP\nZc/Ix8zCG0huQkMmPgDDUXytYmPfGIK8YNn1ANqlryO0tlDNcMGhfmwd8Xta\n6SlBVzwPof6Xgne7hKdXeeIkCFD989Rrn/fkfc1c16Xf0K43xN65AhOsa2vL\nUNH6efaZ1uqbA1OcYO52PsKEh1VaLoLjvT+gJHC53LtBJ7R0SASzDYJbVZ+v\nV/H+AJfhIAxWRDzMHRMkJKDQkbzleFm37db6+I1ynyJOJnOtxHjzWrVe+CIg\nhVr0BOxGmYkuV+vfXafYj1g1PNop4CU+boWnZ0yR6wYHTzTqTRZIvEjYbNhM\nMdHfEJV8NpqCzYMgRiRP0WR9vXjoNj2lzZtSHp4Dq7vone1tfNZ/DG+QuDRV\n+OTv\r\n=8Z8R\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.0.17": {"name": "@radix-ui/react-collapsible", "version": "0.0.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.0.6", "@radix-ui/primitive": "0.0.5", "@radix-ui/react-context": "0.0.5", "@radix-ui/react-presence": "0.0.15", "@radix-ui/react-primitive": "0.0.15", "@radix-ui/react-polymorphic": "0.0.13", "@radix-ui/react-compose-refs": "0.0.5", "@radix-ui/react-use-layout-effect": "0.0.5", "@radix-ui/react-use-controllable-state": "0.0.6"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "d778ec1d5b7b4543fd4db1b3e4be96c74568d441", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.17.tgz", "fileCount": 8, "integrity": "sha512-MiRf6eTNXBIEhOOK7F2kEJy6atr26PLVAT3gTONUvGTMPT2fUVZTOrG/KfnTyOuUoL4W468UPwTD1xN2L9tbUw==", "signatures": [{"sig": "MEYCIQDvCQJ8K6/J4dEVmDPdULHLZYBEu1kXwJr05bO0YIoPvQIhAJ6v4MGr3MQN1s97DScPet4XdCa1m/N5l2dr6dPj7h+O", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32621, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhCnTnCRA9TVsSAnZWagAAfIoQAIPHCIzgqqtssTiAJuME\nK6xY7KARor4ioGx395bpWNi5vbK5PB9iEflPQy5w2xXLT9r31NLwspkmc/i4\nYTL43LYjnrQwf3TOIlHurmRurZgDFPm1jk7IlJoCImRzB1FjnfOaJf6x9+fI\n1+BHNE2hzk2XmK4nQuZ1N3PvH7bTsHGzHCNMkD3PL/iCn1F+q9di8eNw1z9q\n4bD17I9f+3NgpYrWXOoORzuNnGjm/apxCfKwYZmxR9r0gD2qPTVoOtknK5LC\n0FdqgsYDcyFOahyouj540is7f3trrxfrDcAz7OF75Dx0NfcqFDAhSEZ+9Pa3\nCJoX9BBsBsyzM/CWDtIEUEXLsLP0kSsrKIJpJH5dxUtAZstFlBriE3fB37rP\nTU+yAqgcc1s/W+gsESvhlyikOR90JWCPd9ZK8xMQAr9wa0MZbxnEcnyjuFx4\ngwzoUJOM6TfHcBgwkF+xxq0b1CtriLDrQUMVZICx7IzP1G2tswQLCCEvqu7p\nPSefMVVvj94G3UY65QQlUhz4cc9h18eYy1hlzKO9BQ4GC0oqci4zoY7hD0YR\neHfBkII4cmMu0d5NdCkI+2hSSePkhHdgjlgIBKlRioQVbyUxO+YRruw7psIU\nw6R3VYT+prdVmjP1WKkNjn1MmFxGh8xf9m00xSPVYln0OQYMBR2+DjMsQFEa\n+S5b\r\n=WxzH\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.1", "@radix-ui/primitive": "0.1.0-rc.1", "@radix-ui/react-context": "0.1.0-rc.1", "@radix-ui/react-presence": "0.1.0-rc.1", "@radix-ui/react-primitive": "0.1.0-rc.1", "@radix-ui/react-compose-refs": "0.1.0-rc.1", "@radix-ui/react-use-layout-effect": "0.1.0-rc.1", "@radix-ui/react-use-controllable-state": "0.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "dc66f31d21659e6bdbbed1328e9377d2eb2bfb05", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.0-rc.1.tgz", "fileCount": 2, "integrity": "sha512-GYMFEHypMhwUEfCnCd+/FkU3xQUnNRiwo7oDpfst8gOwaucCLNJRrlemQwNK+EFZqMOoZDfWj8cBL3VbQRBynw==", "signatures": [{"sig": "MEUCIQCbBFURd3fGsZS2Mf42JXT27eKDhrg+QlaxjfZdi1weLwIgSwD+KkM9XQl8NeD4a3pW6ScReZdnkT5FLF6cnpfxx80=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 1374, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNgpUCRA9TVsSAnZWagAAhrUP/jgapJ0mjQPHHqkEuxY2\nSRA1uUlTT7LgolX+UoUBEMbKGWDhumGR1ARIbSYc01708ma3E4j16VoHeuyk\ns6HPus0CvQBRE9h/T+dATqebNa2LMpO+k6aI0gAjXF8UVVug+VvGAlR8fIIX\nkYFsbxC4v3mqsRRETldxXZIobMqlRjyiYLDLRM7AsN8LbDPTS2mo+YYvP9iC\nB18dL09e+WNv84BwLc4lBdC2tRIel9giYPkKkh32DRoTtxRmfJTliVPBxpzj\nu3qjdIc5zb6yneyj8MnV9rl9Lc6teyog4HSSctxoteyb/ykXITYre7P1LEPU\noTgtoGPlyTtw8JFto2szRj4P+6ZqnSx6weY+L46u7QUIk/4vS5TDVPmITC6D\n5NLSL/lM/QI34w1mBIK0LOdR0/nsaDbfYQuA4Ded2ITAvEBMmva3mZrD0qG4\nhGkg/J84S2a5kGoIdOld04zAseS7QVeSA2pPBfVPGEMF7nCgp9niRwbxYC0/\n3xBbOFxEPCuWCYtX30aE3EbPhEJ7HigmFWDNKfHXBcy2iclTMSbcnyoqvGYs\nNRb4EVV54gmXSjlxLZEqQEwetiAWI8+FNc2m9el4R6l0r6n9Lk4jkjH3K7Gx\nLkZc7Y+rP0Rkz/BcseAL5gxgWvSi02JOuyB2dip09DKIHozBqBljR9B+vlHM\nHc/P\r\n=fpVG\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0-rc.2": {"name": "@radix-ui/react-collapsible", "version": "0.1.0-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0-rc.2", "@radix-ui/primitive": "0.1.0-rc.2", "@radix-ui/react-context": "0.1.0-rc.2", "@radix-ui/react-presence": "0.1.0-rc.2", "@radix-ui/react-primitive": "0.1.0-rc.2", "@radix-ui/react-compose-refs": "0.1.0-rc.2", "@radix-ui/react-use-layout-effect": "0.1.0-rc.2", "@radix-ui/react-use-controllable-state": "0.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9b023e1a9287026dc6b85769c417bb1e983a32a8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-yoHyFh+YxWQHzGbx0H6GWPCWZJBDU2KRAMJcJWgTyo/1sL0KGeLWmyj4VE/bxVHUICoz5FJ21E/395o3RfHc4g==", "signatures": [{"sig": "MEQCIGHTTklqVp8ivDdgVMkf4IXc5Et2GApnubHLlGFAfm3YAiB7nJxKMi1Q1sLxQHC1oiQsnUH0qrBj2BVSsflfppTX0w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31491, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNhyLCRA9TVsSAnZWagAA+IgP/1JX+6JCbfGFywfA57MT\n7P821i6fXFIGLb4PdXhM/ej6z7yK9+oMDsLolWo26TUMm77Cm1MxtuNzdCa2\noo6TrjtjorgHCYvcG6uxQ6XxhGwfGNsCCT8MrvqpiSAS+ZLF3+sewMJrESdH\nmh613Tkuml9I8m2wAV+Um9uhofcD1kX9E4+CCPxVa28/k4yFwL6JmxvqQKT8\nOJMSRyyw716wXc3dWs4zVxZ4veuiy/BvU+aiTJuyJt0GBiUxHCLc9/smldPG\nENR3d82fvPFejPA+tOSUMRUtbuc8HBO+5YNjF7abUr7/VcPA2f1s/v+WfCVY\na07tUqT6QlN0DD/I+N5CooQzkq0Q6IKHUrDC2hlrtXFoRVbkI6uwtLk0r2hS\n3P2j3d8aLz8nYFFbT+Dj5hRxQ0FZUI3Vf2kVLjWuG+gJ0GB6ea1qajCH+/J3\nYtHwkau4e7R3PO7VgB7Ys7CvkMnc8jiw/x8JjiCVPZS/jK/DO0C0BsednWvQ\nYBJVhMYLHBM/rgckZMaZv8S8NinNG7W/IIxQgEZDeb4ZmKZeuJT8qcMQcQIt\nkDMEu9VxAQlfhXwR8PBs2IX6I3pA3Ag9HFWaWb+zInSqa6bH6XOIEL6GhirH\n+AE7xbQLoBFBE7MVASv5MQnzfSO4Hr0UVBBx4WK4THB1Cxv3nvCEXI2pqUhp\nVwCm\r\n=EZzT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.0": {"name": "@radix-ui/react-collapsible", "version": "0.1.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.0", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "62276b5300de7db9b9a809fd0996165890de0805", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.0.tgz", "fileCount": 8, "integrity": "sha512-YpNvWn35oTOG5ROujE4De+Y95Yt45x5XFjfQiUqbnUCOwviNMO5e1pnM1ycCI0vHabNmLP3EaTPzGx+eTffRVQ==", "signatures": [{"sig": "MEUCIQC5D4n6q1PteXmYz+PwQk5D4zkNAHRWqvMXE5W0n8oPzAIgJT6HH89Imuopp3kir9fZc2IiyYHzASOfatJZzQYuF0I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31417, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhNzmYCRA9TVsSAnZWagAA/QgQAIlSnx1grLPoDVCfNtVJ\n66RmXVA307M9DURB40phT9AN0fUtjS8j3BydN1xUTCncAabNueo/QZRlg8AY\nNanaJvnTp7YgVqSOvtdeXvMsxsGQZ1aL+ifovYMA+H82hm4iWOyou4rXjhWc\nY08aXwvj33An2q87O1bB/lxT2TtJxAxNvPbRB+Sq/UGFl/NEd1t00Igfe8wj\nr/jfrP8EhPCnTSmYXE+cPmAKp+q7u6pFTWN57367TzYhrHze4lH9qxvA2mca\n38uG6j5Cfd5qlAQbGtoU++9uTI33wCblRNYO8eNne7VcPFQRzuouLX1bcQIc\nxqrOLQuGG4NG2TPaw1LS0DIbqOPWq4YB+zPhUxyjCDPfFyy44OwKZ1lCnef1\noSnMTTdO+qqShQSWAkZVCi9uBhtrWjbVc8iLXkDfcJauZfH/drI3ZFVmDco2\nJ+Xay9a58EFeaFI8tXBWvIHqs7XncUYZT6486foBoW2N3qwTtjWEcDBquGPO\nMO7l8GbLQ3mhAvxiApL8PMYcLXyEMh9mj3pe5kpr72ufgwOcWGAUhwu0Gbgu\n4/MjFHO57ULzE7O2P53WnuUNMmMSQTa+6GS15X838hMazQs1nkrLLPMqOMyL\npmYPC2HNc5W6VintToqCmuQaUqqyfQDM9lTMRJk2hIqmZHzY4L3+AMmj0xzG\nhcdc\r\n=tgQs\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c272165d528a0ae26a681f5afc37c303024e8806", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Gpj9z+gc8RjuIOiULkRfUZjcpHUwnjdEvKyE8lmnwY+GCjQaYMKgkT/1U90+riyFumsy33DRSu+jSp+sTDKoWQ==", "signatures": [{"sig": "MEQCIHIpiBT4fw7C435GI5dvFFXBK3g/T/LwlYRq3sgaeSWZAiA6Mg10HByNf7UaYhzdUYdcQIQZdsPMt3J3PpXt+vfGGw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQImXCRA9TVsSAnZWagAANy4QAJu+TVJTnPEfiT2h6NSV\n3wKTf41juEQrbIXhlnehngg7Q0QQUq6nMmBcVSdpXY8KZpSDLrc0cxsihXoD\nHmjT04hSKgjWnYnquIXI2X8x1KQogE3jCnOyd47EEii2R9skW/qpKcT9eIOO\n7b8lxgUonGjra67JfXZ10hLkkuH2J9iZhdLVamyhSgA+kmg8GgVETrwCG7fz\n1uHo0/u6ZiCP1S5GngTcHoGvjjs9UcVyzwzulCN2LSMwcGbE67h5Vode7Ngb\nUSaG0bMnHm8F3ohpxr2kBtgHJ+NEN+aRw6ZzwlgliQQ/xukRWd0YFXoITOA8\n1BKn+cwYJSGx34XNWzy9Pz6sWQwJX+BygogkrpLzr+U/hOclB/vXL9siFS45\npEDy1bl45rO6mXGfxYEg2GZJ5VHfbpJCyqxdeEarxkH6lPNfWZ7EyQZ2uFyN\n0JXAeg2DtHu0ada5EKIt6ysdn82lha7GviXr0AUP7DTokVGx12xYzvwaD2WZ\nNiHzL/AkJK3T/qfG53XbzN8ij6i0dfdzu07Ik3HHlA3S3+45EBt4s+fhmGp4\nipADVT855+FaFzm9W1zF6zlZVZ2AiYgfdMaLpvbF4xvnTx1FI/92T3vowdb/\nVTmZqd8jMfVB+uxjheAdwK+M5zUqgJkOUbs/IpoMnj6aAwbPgTTGrb6UgnpE\nlG4V\r\n=+Npb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.2": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "7a54a4aafeb17410a5e67e4eb8ff0ce55e563b27", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-FcBLnpjiDwmU/FRfAugNXBdVGPmA5jFbcqV6FIny+QfYJdQXF6uqhrwbW6+4irNnFb9jOLoybNwxF+SNljFx/w==", "signatures": [{"sig": "MEYCIQCZTatbyZobdUTk5XEbwGFGdinZ9azaPk6gu4c41d4czQIhAOzdQnAaGicjJYemrp6m8ZCvcxcppvdukCDyIb7sUpFP", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQdvHCRA9TVsSAnZWagAAKU8QAItbHBWg5hA3aFoqyGVS\ndiCQ8XKEtYmp97ZeynKTb780eUii5Ysccrk3xkiIFTKQlGSoDi5KvIbS4mXZ\nlTNT0eN8NguJu/6otf0kViWyVONGiXYmPOMxJ1+jsYb6UeMspDylcl4gBP44\neY0NKbSC2CC1+gQ3DKkgD4Z8efmCp1MtXKoPoNVXhuRrGF9gZL5ZEMH+Xlzq\nXcOEOz6gqAgJua6TJOFHQiGSkDCOD5bTk4tE2H1rDS7sT5z6oHN6RZoVIv18\n2KXlL7g0zc2vXeVfBzYT9Kn4QH7rDoddeEVR8ellR2LKmlCgZGokohSw6Ldd\nbDJRTABSZUZ8n80OLuztVaGQsLSxMTlhixvvawAnGZbLawTTy/5vxA/VpXMm\nz1pMCcqqG0EJqqwv4B7o6I7HlqetuewdjhTXxBj0Knp48zLRn5bnFdpSO+rY\nvNpUVyI5iN/9P22JzVtdlU3LQQfcWO18I7iML/VanUGSuF8ndE/oJ03Geaic\nvRLkhEyeW6n/8xAwG4GoXmY1KjfYEfQXp4zIUn008uv0usw3Ti/S4cxO+nlU\nouJ7ODJhMuDSKSffdJvMiMKj3M6gZ9ron3lXvg0OrMczF9lgqOZ+BLEhiuJu\n8saEqvjhoAC5kC2ZQnfY/XMbAVsJEKQZztzThopzMFbDXexz5w05be5l3BAt\n9Lm0\r\n=bILU\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.3": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.0", "@radix-ui/react-primitive": "0.1.1-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "481c673bc27ad47f32f04fd852766d70817e9167", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-GWW1xZk/Foaks3nx2h4C29Aj0IUyb9+4m/uBxv4pcdeHPO+oGUQhS7hu/E42qiLMUoIi7qfY0xALwuDqsT94Nw==", "signatures": [{"sig": "MEUCIHG43DAcNLpyY6kPVJMDqvKb8/80HOVoUuxePLdf27xBAiEA2HdYTJRcQwAToPnr6vLDoZFm1IaaMA+ePrDOuItEvtU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31455, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ0TRCRA9TVsSAnZWagAAt94QAJ145mOb+3jM+jPkXs1w\nKCkeE/1fcQzFqA05cH8bS7JcyMCpyoBELmW8sY0Py527MExGsA5ouIcQ7rLH\n6VRC55/ZsOaY/2EskY+zCWO9uIc93eta6Q1jiq+5z75vNdnSP4R8yN6iXiwZ\neRCS9MNQ8eIKTldXcRRAp3rCi6lslBe0MQtdszbhq60SqhaMsdBoT+inG9cy\nDCYL5PunsHvCBTP/2Mru3ql0BDhydpNFdwPSB/Uele6y1psdldnYphe3spWU\nfaTJJem1aGiS/mUEujYloEbslZjrIr4lOMmfOGnD3goybV7vQUBhrmN/1n9O\n5Hm8mZmbV6KWzxfLqQOZl1tsaOrWD3geQpKsbsG8FN4G5G1D2l30vfDLmjYw\nl7j3R+v+ddgjeHaeHvln2XPvzDIEV/PfEioCPLZ/J0ey1m7JzeEzuNA6o9gk\nm1wfeobNTpQCZeCzi/Q4DxXXqp+45RwooSmKwZQehnvzJrEjwZd313BC9sV5\nx0i3j+zAw4uNQLDmw13SIonnr42FoiXi/V8ljkaMlBYDbfsWBon3ztQM5UQO\n7BrvvJuv6/xoTUjQuEOF3E5wdvY6N2bpYuaNBAsnOCDVe4U3lHTFCYb7rDQq\nNRRRPYrPUfV3JFLVZr1nPd7YoegSLngBMFwQiUPYWfwHKZay1niYHjmrLXIC\nbSKY\r\n=DEKy\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.4": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.1", "@radix-ui/react-primitive": "0.1.1-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "61f33bf97f47c9c00150322c09357e977a2064f1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-WhklP/rU8TvA5LvYAK9NEQ0CIWOUKnQAH9VsJW1vWW4ydjxnrolGYFTTQufaO5z9t5bThQtoDuSJWREJHJkkAA==", "signatures": [{"sig": "MEQCIDKFsIex3t4G/1+4Wd/1id9uplwoQ44SQyH0vLrCCgfJAiAz2IFaoqLt2gwQyjvZp/pmjxYvIaEdwCgyBhtRxhTaNw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31460, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhQ1y5CRA9TVsSAnZWagAAE+sP/0smZabPF9t0hLZvvJgN\njAXY9cQ/XNX+kva+G8akEp5jQ0FdrBAVvz7k9B7EDir6MG+Su2e4SLwv60JS\ncTRgDJDqTNlsBnR4gHVaV8jBwFs1x1Xl30ilNEjn2BkOOED01H8Vuzjgktvg\nK7y8UZkri3HrORVrf5j4iMEtDpXhFDg4lWutMIR9D5Q6F09ue0sioMymh/Ps\n+UqgnyV7MxIuzqfVgbSHMUwbMfpPjLKRRdi7Bn9jjHX69PgAwg5cZ8644SWo\nk5zqVUwVJI0bGWiRMxUOuGrqAkXvA6PaZmg9zlrS2EiEgLv0Y58h5tiZmDZD\n4M7VV5bEpmSOVBNpPqQH+fDd+XYgualAZF7ondFVGDyilHpjhqSVwU/Ebv+f\nNP9YxFQ+lDjAamhnZhloL6Ugo2Fp2aOdvLeuFssSvmPRiH4dskjDiFSdmRkJ\n+oQSjExE7ZqJzv3r0RW6y+puUKXCsme+pvAsbQrqk/O0knzWFuVYueb63pZh\nFvAkoLWswL8+ajb9GxkV/f7QE3+lyc+sQ/fwseaq6Yt8PBDIGBb0W2bZubBk\nGAmUHQHoli8jIbg6ZNrZmHzI8ldlOSDYywHt1Wld6uKyhXj37daYvQJSSJbO\nK0JPmlRajpGwvM89mDlK4hJJC5jeyqO8Tr6qWU8vOjck6at3IYGV5rLLLK/m\n6nsJ\r\n=8nWm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.5": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.2", "@radix-ui/react-primitive": "0.1.1-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5b3d11b5213d8b49dc2ce6067fdd14ea39186886", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-mSF1oA3WQTvve4yz8wAhIKGBYEWcO2/mKPrDAZ3tZ9UKzY48OQsZisdj8QSIznAbaUsDqoQfZdOWdKFMGXJXrA==", "signatures": [{"sig": "MEYCIQD8Woh+J7d9+wDlNoZMocQnPB8y0PkIi53BaZjIu7y4FAIhALetYO9JATjidRxE4Ln0v5UTyrVAt/AkFRJLyzmBW3U7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31452, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhRFh+CRA9TVsSAnZWagAA5z8P/jwCfepzQ7tRawYsft/1\n/oHNk+cbW2NbfQfO85FM8Y6ZkVQ40UJHCu0fZ9PniNOFyIIo+DAalq5rUOn2\nFe0x4iHU4ulYmxGpZ7qIFuHwfjl8J5a/hSEku59W8Usfp2qxnjphmlLpPaZ6\nJfJGEaUHxEEUi9z0JCbeEYx9gj6yrA83LO8zuf7c4sH3a78vs/M9hZgsPfq1\n1QuAalCVpHkh1xMNI1NQMDzOuag11P0UFDdDGKHdB9RzUwVIX68/w/kNvhO2\nZRTDXrqJrYW5dlry94E7pMQ9arPtGF7MlVNjI7s0qS37kmEca2hcESPWNrjn\n2fs9Ryqn87HvY8L+3wMsYnMidDyYgc2SnCFKAPU+d26BubvLrCD8h8g7i2vN\npN+sGLpMRP4LgloTybVymdihaOWOx9bnTfU3paPiwILeeXgD8jjbU5tJjJRo\nYFz/+S2k9QSbD8SHXVOeG64RpmQiHyPfHA3oUmOiR8/gIF515T/nv2jwuBMY\ninz6SBKked5RK76FOdqJNMBlW8Q97Pztc4j9QPevq7se2o/3EURaZoaFHsK4\n2BVH35TI2UGLU7sqpZlDWKQJtpbXobrIKX5totNg9zlYgB37gnhtAOuRohpv\nEc2GOdu8N+aLEgZFOrHTzEt3vaLRlN70nfWasHNJxg3rcSi0NV8OxDhTndfQ\nDJWp\r\n=e2v/\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.1-rc.6": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.3", "@radix-ui/react-primitive": "0.1.1-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ab673003ca0fbd118e77af019bfa860ca05f5674", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-5SmqYfxwIFLZN4NQ1kZroAe75E1iK9BvUp/U/qq0W71fvAbEGxtC3JYaFeui1BpEz1WIo4lJHw9XF0PlpUusEw==", "signatures": [{"sig": "MEYCIQC0htTVFYTon0+//2eRiGpQVH2wEDqcZAnyBLRTGPNMhwIhAPGaR6PWdUwBb7wCLUgnNKY1/vTA+CEum8AARIXVPU3s", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31452}}, "0.1.1-rc.7": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.4", "@radix-ui/react-primitive": "0.1.1-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e750bac9a59cb9f5a8f8f06ac031331a17cd98e0", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-qgmpVxdA9vaK2/2D9MX3VJXI5YgZwUPmbQIuzAhiZMxZxWGEWFcBZ64JRKYIZ5V1DDMw9c7oNl2cTNqPx5bYFQ==", "signatures": [{"sig": "MEUCIB36jrPNHiXvYMsyo4so1wdNTem2cyRkY2aU+k6kSRMnAiEAsc/dYSNf/v2FbGied6tPgE5Nqcar+sg2ZZiAqabazE8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31452}}, "0.1.1-rc.8": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.5", "@radix-ui/react-primitive": "0.1.1-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "551ddb8fd0068ed56bf09b69d3ad306b447bfe05", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-Qbn2jskv9XSKkftuM9HkfhZVpxeesKnFpXNATy8a9V9p14opL/5cuBqslAVXwvbAWFwqxT1qcjBZuVP3kSlB6Q==", "signatures": [{"sig": "MEUCIA6xqUxWHp325rmuNaaHgAs+ytSP+ekbs+fECTDEIhQ2AiEA0FVuahuniCCehjzgDfwNv/8ZS3c2OhNME69fNPolZCE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31452}}, "0.1.1-rc.9": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.6", "@radix-ui/react-primitive": "0.1.1-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "49f6732594ade0a2efa542d9eb0fb3dddf41c6c1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-k+zpVLSJXC1ew9mlklU9Ueh254sUCotzzAkAcP2Qr1UbxogD6m2wrzs71N/9W8wiannjiFV8XRhxyhptyxvrtw==", "signatures": [{"sig": "MEYCIQDZuKG//gD6OM+S+zkBnITvLSMYefQHwyXYyEzHkO5FIAIhAKDmg1sU9fVBC+DSnL2zNKXzeENl2zjdXUgB3v0EZth2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31452}}, "0.1.1-rc.10": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.7", "@radix-ui/react-primitive": "0.1.1-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "11beaee102fec09bbaa4d6117e1f18bbd28daa0b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-WwGgvqKivOf2T2xRfKw7HemUjhTzRAd9aRCi9CFgVXIYC4vromyWao1xmm+w0y4xU2C7JM0lRo60uSCtjpDiGg==", "signatures": [{"sig": "MEUCIH9qkLQcqoGVfB63a6G4DDuDdCr6ba5pLd4sqL46Af7CAiEAn0ZTnJ+BTKC2aNQYQLlUQMaa5iZ3btYBwQ0VNnQxKas=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 31454}}, "0.1.1-rc.11": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.8", "@radix-ui/react-primitive": "0.1.1-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f5f6ec71917a007e570479d08e2c4ef53d2bef1d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-QsIhsGLM29sAJlH5o7gQhKaBIa2TpQh4DCND9qkiaYWd66PshOwem219hA186ENs/wUwdH3nqsF8mkl/qGG9nQ==", "signatures": [{"sig": "MEUCIGkceLN6LmbneGy4epyXQnpV1dAWkh4nfS28HYA4exwbAiEAvBvLzcItPCe6QfqrmCxnumSs+UVbO8ARhJ22TLEpBd4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32338}}, "0.1.1-rc.12": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.9", "@radix-ui/react-primitive": "0.1.1-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f6230b63eccdd11636b9a240708859c8863aa8be", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-mFgqKAFsjyaSgTSjqb+sL+k8LgtxvH+pCe9KvObWzT34L72uJvlH0o/kK3p8ckJfvCA0xsMa70RNioTQ7w4h/A==", "signatures": [{"sig": "MEUCICeqHLuklVe+MkA613k2XXMglQl2VXVGEsTaCp+eb4QXAiEA2zttxh/RW8VdR1ZBLyFsFBUk6j11hMXzb35u4Gys2V4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32338}}, "0.1.1-rc.13": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.10", "@radix-ui/react-primitive": "0.1.1-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "ce87ed8dec892a3c0ebf41fb0e72a5e3232f112d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-gIjDefLHJUKaM0Z++09tFVqpJtx/hnOQ/YOZDJuZwHsRhbovg1bbAcxP+VjAu4U3gaisqrW2gfx9312ygTk5Vw==", "signatures": [{"sig": "MEYCIQDkwFJOWdTwr69pIUlvsyBxJLbEOhpGwuWLV4ESDsPf2wIhAOQaxjWMZJ6KpWJizXIoxjQQHP0b901ArKJFYsmFohen", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32339}}, "0.1.1-rc.14": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.11", "@radix-ui/react-primitive": "0.1.1-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "322b5acddc701bef31772c23c08e85db0e98ce3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-AA11uyC1aFejRFDPwhTANxf3pHW3v/9jkYmeC5aTjqAv4DH6iBo5EpQ2SNNa3uRHLvrDXTwFlj6Obxr4qx+S7g==", "signatures": [{"sig": "MEQCID6dAH/z4Cc6jY8tWW/F2l8q1tdAq2zrnKwRfX+fMz99AiBbP88d86ZxXhzt24+DM0l2oyinQcUBCst0rH0/3dbDMg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32339}}, "0.1.1-rc.15": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.12", "@radix-ui/react-primitive": "0.1.1-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "bee14dfb89807b50da082c68d8fdc4873405fb37", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-hoNF6wzSaZEZiEL2quKh62IN07m57vPt+665PLZzOCtD+g2ipDlSpkLpeJXqeM1BAeWvsmr5eEY+99VhhFHuVw==", "signatures": [{"sig": "MEUCIQCQsrMn6QTPufNO98tdc4PrrrjXa3Sd7H66J4ixXisTzgIgVeqItXr6C9qsHjLoYUU5/DFvvcnehbHPJKrNm8WU5Qc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32339}}, "0.1.1-rc.16": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.0", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.0", "@radix-ui/react-presence": "0.1.1-rc.13", "@radix-ui/react-primitive": "0.1.1-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3d8cd2915afd3cb0fd63ac354f058eb72c4ecba7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-MgbZ8+8h1YNmO3GBHaBl1QGI5WiQfr6EWb0o68+EGquqvoxW0sXh+igyHDX+hE3C6DLXglh5DV2C5LGdIUoi8w==", "signatures": [{"sig": "MEYCIQCV+uj9ZSNhyViu154GcEB1L/OxVUL3ZPu9kDtMSoAFxwIhAMivRV7+NYyyHHyS2T158dTMYQ8ukX3TLMX6kLIftisG", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 32339}}, "0.1.1-rc.17": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.1", "@radix-ui/react-presence": "0.1.1-rc.14", "@radix-ui/react-primitive": "0.1.1-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "23e6c6641f1fe51a80bf28902082e9da4d315f17", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.17.tgz", "fileCount": 8, "integrity": "sha512-iKvbed6Sl7xo0frFV3rby07hz4fJgtijJG/Uz+lPTAkJrWDGKf1fKFWnyxILOzifavrpxI6zhdt4aILivB5SWA==", "signatures": [{"sig": "MEUCIDcBpeHzrJSpS8Vq+CM65LjRW148HhATQUBHmtUVyqYnAiEAvhcuP6GNbYZKxcHxCbnew0F3kNSHfwu9qjXJ4QD/cLM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34606}}, "0.1.1-rc.18": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.2", "@radix-ui/react-presence": "0.1.1-rc.15", "@radix-ui/react-primitive": "0.1.1-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3acf09c1ca51d58112736e739eac055c9b008f28", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.18.tgz", "fileCount": 8, "integrity": "sha512-J1wwEtjXeqL5QiWkMx2zuueITbHCjFf5XjAF5Vbr+wzYpgiao9L/q9Uw5hYF0GZON7/Fh0wNG63fHAY7PsS0Zw==", "signatures": [{"sig": "MEQCIAL/+On9JdcVCsthaKNSpq+7WKBAcErhazs1z77RQ4UgAiBclHDj9BhsXjjPdxlLe3/RnkZ+9tak7NA7EI/jXgguzg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34606}}, "0.1.1-rc.19": {"name": "@radix-ui/react-collapsible", "version": "0.1.1-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1-rc.3", "@radix-ui/react-presence": "0.1.1-rc.16", "@radix-ui/react-primitive": "0.1.1-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0238bc4e4a975bcb502b1873a82658a7349663ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1-rc.19.tgz", "fileCount": 8, "integrity": "sha512-/zMKtwRODv/qhXRFR7e/XqmJ6k73tMGUeHQcrT12lAVvtdg3ro+w3IpzpWgZBEsZ3kXC3KIjqpgXt61u3KtSqA==", "signatures": [{"sig": "MEYCIQDFoFhpC4fD5vw2EP4avDFROZ7hcbgs2RBEyo7GpWzUfQIhAOzQnKG30qyXabWdXYOt0dLRZu8KK9b6fSeNhdvVpEDz", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34606}}, "0.1.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a3f257aa7ab27358ab2a16c6a5815a307a7ce474", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.1.tgz", "fileCount": 8, "integrity": "sha512-GIiCo8wYz53ZZEbp4LOkSysK8B+gZSi8/X/5NotBvyZpKntnf93i+NXPmtPPr+l0uPBr4EnEG1aZnItnrJpSEQ==", "signatures": [{"sig": "MEUCIAo+rgGKqchV2+bUlwUeFcC8Yf7295RMXtEzPfiNRrY/AiEAoEGvZYzTQH6x1t2fhvQqkJspLnkS5ZOKXlR5ugT2LXE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 34550}}, "0.1.2-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "1e5e19c758cd7ffb1609a6f9e3be1f0642d502b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-Bbzcuwpweb/bTrwrzwfkvoP+EfcPFAkQpXURLs9Dxx6yXFrLjT5ucL3sDyFwYVYeIONi/+rM23/hhhmyVlRfgw==", "signatures": [{"sig": "MEYCIQC4hy4ULak/9fkyWS0bcaN78r2zV+Pg2Sl54HqDIfiTAwIhALzVZ4krH/rRjpYB+bGE8aUj8NTj63D36Z/bUNjGUbxi", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhlmbUCRA9TVsSAnZWagAA45EP/3RpanphkJa3aTDcFa+Z\n7RrefJ298Z6sWiyypQ6SxwlhD5pu3tfOBJfF0/wlNCVvqlw6dLWlUds/BM/D\n/XasdEIRAZRpLxlrBs/D2a/DE7kbfqFM7KE56Cc7+fdZjLEygdmdAIm65b40\nR7kRaI9d87ApgQxeXvHqm/+DuUekZrZjMTOHtl2LAL+pAEezoq//tFGufK16\nPEMVfgy/GQxaGkEAxuwOkqsjQamvqxsoVSyEtrl7o4tW1jAZ1GFYrb4OnhdM\nknZYcoIvewxv1JYPZSAyuQDUXrhc26HeBjNdPp1VCAJKKf69I9IkUyxszYGQ\nE8eXBm328OG67kCRw+DZCLqPYgbP4ctCRuv/TomlFyXxyplBroIzjoi20TCK\nt8LV7v/+D5aTG3SWT2lUIOqjhF+gE5Yjbh9OzB0ppcU3lX7+rArzzTJXLGqc\nzxQKMMEAF00VVGOqV2oYMDR67e79PqjGWiz9GKu/4oNmGL6BZyqrlra+/HyF\nKlBTRSsazDnBOGfBCswwP8FzxuqwgaGuOHAIcMgAZC4GR/Kbn1vkb9runLYs\nIKeZ+C6Qq1DpJBk8XrICCaKlCtf2kNNeD3UxyfC23xvvoZUHakxYqvmOE4LQ\nkhGeuQHiPUZXwx8WYyAqdMjOmWpZNVbEpkywk8H3Uc7eJIU8fz+Y50TAF62R\n4fdl\r\n=c5qB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.2": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "62b8ac7d5cf5f95021561972f3ac5eefa45a687f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-rEn5Na0hB/f+vte2l6jtaaqzmSAgsswR+yBl77NLO2MGV2dFw084/EMKVZteiatGV5cx2vxHTZWFqfTRKv6wUg==", "signatures": [{"sig": "MEQCIH+j01ePYLW7b0njcjRo4psWJXTlsIM94CvbzssP9tToAiABN6rQN/rrhbyDcYQbEo+Vr1rSAja568/Zp+loky8E3w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhn5mxCRA9TVsSAnZWagAAJ0EQAJGUNUTXLuAuOeo7grIA\n7q2KjBmG21z1rr2zimAB02IsERDNff4uoW9BavH9il0xfDzdeJlohBRscyZe\nUUt/K/16byShd9LJVob9ncRFy42mX5364VnxnF4/AB9z2rRIei3GuB5NGQbW\nAAIg7OhxHsEGpSLZ/WReMGizM+MGcceqjWYAD+U3HeU90Vf+XSrBi5H8xrh+\nIzczrlxs0yLjI+gRUhr10dgth4fMYuUL+48/0w/xUlZDW7Wl42Rf6Tw1eH+P\nd9bRY8+nM7dEiOZd2rHihocTSXvm7GFhODVT222MfeO15JzDWYkuCPqPUWS6\nLF4INfZkhhIdHG0noasRBX87zVY2LFO67R7hh/ljyIvJPMJqmubCVSO67TZM\nzy6mXRN13/wIpkKCnFovBttEdK4+eLKUCa+dq4aTfXJfCNpU9bS0Ox7I4ouG\nfR+WscbKhEZrkRQBgTIojDsV0QEDocp3i9NapoUWv4U4Ozo3y4is9pvpPSCq\nRsR9I13VnmFnTAo/FMq0OG2KnsYilgBsiclIQcY9xPKzlSUlRpRfrOLbdtLG\nQVAnI+x8DYh42qH+QwrLxUV4hQuBY779DWQfRyVvcg6L5LgW22nTWhJs6l6B\nAajDOkRQtMgOguCGotQksXJO//nXYt5eK7J6i2/GLnMWRBBIpLPSo2Nurizq\nFbwh\r\n=3uvu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.3": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e04f994d73072af5499ffc017850d1f4dfde164e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-UuCcch2R0soI6St7fLlAAc/BDWQFvMlpIvZwRyM9yBgdEcxRGS9RMUWEWdRuDzFFMwnXJdKlX/sVEZFd57Bs9w==", "signatures": [{"sig": "MEQCIEZ7fcYnsaJDbgqafMcu3jBHsdRZ+K+yp7EuwdqoSgqdAiBuAFN5VuX79/UFcqTRp3/4dxPc+QdA4yPLtsHiL9x5nw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35921, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpN4ZCRA9TVsSAnZWagAAf9UP/imWE+TPDyiipvyZFIB/\nhRHeADrkF91JoWlUb9WXj1xba93fee10LA6ZHWp8bc4nrgUVWX1q0zH729+c\n/2f5CjqBTvZZ8fJJu/P2HY/w4SzH93StRCOMBHYe95ZxWeSaQQyNSUae1176\n6bLlDJss0jBIgitGka7Rw6/XD2XkA0c7SBh7bwtw5uz85VjgZ0/pvsSssNZQ\nXtWxjZHBUVmMg7GI7VWFXljc7ezbKZuv0h/4LidAYFyOvJvb0Hf3p48VH/Hn\nrKPuuN2qhnQvY3bDlg5gv6MiqLRO2N5Q0RvDpt2RggNrB/KLwJYkcIzdOic5\nYiDnWAAC8qe2kWydiykyzu5h8/IiJhtvpJIZmea2VddAOF3WuJK6jFptNh2D\nTK7WWeK2nbYguTeXMc8tao8nQTdF9sI7jUdLdn3nxfk7I8NQpksu/f7HNZzs\nr7tN0nx/l59HeAQ7YIpqTqzV3aIcOTdmp6Zh8Y67BUwcgkUDl/lmxh75qZZE\ncSK9bkVjIz+J01HOB2tv9AQCkWq392V7LNtu8O3u+ZNqIii99qu4h1gG0OgS\n5fHbpaar/TJozJuNCxY+ddjjHKMBftiavukpf5LnrWMfzHp627Q9cL9Sk3nx\nRtOWgY1vzp5GanVkbR1LWuSpdQRH2wRzXaX6SESeScy4QH2NmyVxQV2vVhMQ\nGWH7\r\n=UZtR\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.4": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "f3c18d3783fce7b0d23ce6e6d0949428a8f20b6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.4.tgz", "fileCount": 8, "integrity": "sha512-A2euPHg9y8YUezHhUVHVddzbGXvYujkstxkk/DRAsfRq25Pn698HwHh0hSx3No8BSc4Rgw9sVV+BSi9lqf/r9A==", "signatures": [{"sig": "MEYCIQDW0MNLNEE9hH/tIf1OkO3gchGi3mPfrrAqXNkTcCOffgIhAL74zoR0rgtdXoG7CCid7Up62rT7gSYXdSwDT3Quyw7I", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhpoTTCRA9TVsSAnZWagAAcxYP/iZME7FmRcFM9RUXvu01\n2yhQcByXkPhPrVnQTJiDgIEWMBGtx+tOK3tdVM9mARuJ+FfYZitMGU2vLCJ8\niYCcGnh/6T2X2IpSLAk/RfUgrV9r/baCzATsyswh4UV3vIJG/U/EsIP3F1Tr\nxVe9oSu0hhwYmyvoZpGgtiFbVeHgMi/YyHaIlY8oWVgT3a0HwY3C489NNLLt\ni6CcWcSPcv/qFioBooO4dDKe9DAozZEalvvPWkhsbE5K+K1CtoleJrh8J+Zp\n6UJMOBoS8V53+fsIWJz2vhKiHtotFw9NzdldwU4TtYGk9a+Be0LfR02Le5w9\nPXB12AiZ6kwVwEwHuv+IsmlomnMYcaOCW3Oq6YPZSF5EeRb6AwFPTUT6jd64\nRT7ooTZQCHn9wqrM9R9scVUE2GV5miYZSOLngwka2fQLgv0nqukel3XfzDKG\n9OK5LJRMxB3ay4ROF+wxzWBO1f+MKPrZFirvfWmtU3gC3tPmCXqDbEs7EqW5\n9SsVIKlNDeEUphKf0Etq4PDLz1OPTLnRYDG0Pl8/H2L9qPh82t/eOAbMAGcm\nnIo9je9SakVxF7mXagrUhF+FFe0D25R3de/QdKUGeOD+7a7dMbjYRTNA6FxU\ntFr8F528zsSOQy4S5t63tSGkNqzgIMnaz9JwlIeyEy5KbqSpHZ8RK6yuz87F\nHyzN\r\n=kocC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.5": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2ea2b2d9727e585c73acf087b0b7fa8f4444e7a9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.5.tgz", "fileCount": 8, "integrity": "sha512-AESyyLzt3Rye9GKtNpd2eP2OjmCnqYWcPN8/aiKcfZoZ3SsRjVLqHLxGgEpBJuzoraIFABoOWpuzqUhMtJjj8Q==", "signatures": [{"sig": "MEUCIQCZ/SmnHIt60k8pHt7XgJiyvN4hVyWv8ZdMvG0tjbfbyAIgAUIke5mDAQ7GkW3wLCdps15/Ewrir3xbVJZnVtx5eb8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqh/nCRA9TVsSAnZWagAA4ucP/jov3pkBjZNZQNqSZroY\nwrhAcOS3mNPzkBaKbD14Qi7fgOPrvOWbXt06s/Jcg99Cj1xGUh7TMG1cQqse\njmj9bZWu1zxcyW0sICqiZdc7993gPxsSnra3PBj5b3/sqNZ+PUqOiD6qhk7v\n3/sPb1UQrjC8CCvYuKhG/yH1FZTybUFRlb3zJjKTGauYOHJrRQP4PnQl3TAu\n854sDfUCvlZau9QsKOnPmidtOLAee8tMlsGkA3t1kbNgRVYmG9EfoEGMOJf2\nKQbffzsH1KgtgSCwz2IEI5TTf1NVdScgsn2cK/l6SCcKnEtE2BQAcGOfR3ZV\nI3i41RwrDP1rQJOUMpTSlkJ0hqNfX+phB6ogsBLUMjWck/qaqfopuY3lYH7D\nCjo59pKFuPIoYFRsrVIyVXcpYvkYVKDcb1vZkL6PN0w94jJpfQUUf4I6kEAQ\ngnbxQ3kEGx5khUx895OfPPkCzXtZTbjwgXr18icfZdFf93CQE83dcoBIe4EU\naxHBLNExiT/+HhSxO3XhmduczeVxUExLf0xTIxIeAJkAIae8q/k5rcbf6rqb\napRTootOu+KspfNAiQ1LhF7HC3QAWyyw9ZcY14waimzeeBBY0eL05YXLl+Nx\nlbyAo3AbgJfw1uf42ntBc728EYEiNdINts12205Rhc6mjy3XvsjJEH+OMedZ\nB2a1\r\n=k2vq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.6": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "030172963dd23eb3d2c1730d364ad95ad6547043", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.6.tgz", "fileCount": 8, "integrity": "sha512-i6y3lun/UXFgeEX04T3M+rzrxnyX7Fsik+MaQedgsZSpUtakhtvM+XI+IRwD2Bpt1NO1u31FKwIOLMslJ5WdNw==", "signatures": [{"sig": "MEYCIQDyHUaAN1u60S/E70Kz4d+n7XM9wyGOYot5D9G0ic9PwAIhAJ8UzVvLo15Xhy4K6YkyAQv5touT6oGgJP2OuP+9EBSM", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhqiNHCRA9TVsSAnZWagAATLAP/A3vR3e/iU2nTVR5xQ33\nNjd6nRcc/jOhzEzQJFGL/ZKBEE+j8tGa9ngkmtAKbddikr2uO5dJg3/a6v36\n2ikSqyx0qtYxrP2OyIJYJhN8YWskzQNmD7vSyoDvKbt5l+wYwAseYJjDoClN\n6eal6ION2W5EQAR/RUfH5xu/e+QrTNKxWbGsqPaxcWNvUvwFEOYYLEEI0I1w\nURuf+9y4fgNABvuNr7N82uXARAtR1zawp/n+q7Ccj1Ih+hT2Mxjb+wsweLwq\nd9mfo5aFEuc6wzm8ZVpm70x75rcWjjejxGxqCKxkzLaJa6+4tdh6E/tzpslt\noVyXHYWnZ/tixZK4BbDBpgREQYnCwMQ53V5zJVtOVXLJ8Z+6dIZR4xQlM4sc\nMLA3vJZ4xggRSCgz3GPEkuYF2JO9RwvN0Z/FqOPFX16igS8XMAXfAmX4aKh+\njWYCZaCiPgtRoonV5cwKpakdcpv3I/2yhbDnTpVEY0uGO2aZt3aN/UJAmOxz\nUv/KqwBdTjozAxuN6TaU56HMEuoZjbgv9BLsFcEw5j/w2TBa3iyVx0E2eD2U\n8HS5jJdpv0yZIjxmHoTXcEx4sS3ZX2chyOzVSpK9ofX61HRT1TcBGm1M/GU9\nxgf/rDO1pivsb/+fEVYQ/JwTl1Ig4onEKXc4Sz9HER9Z2RY2lOZR/+hzJMOF\n/v1x\r\n=CG6y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.7": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "db55b615473f09dd4f4fa963b35815712a71d396", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.7.tgz", "fileCount": 8, "integrity": "sha512-sbRxN3g0qGATS3pmqbbMi6ydMJVnHHuFTq1eoe2s3ocfQzWGbykdmAiPP8ZzUq3NPvL1gbMMm1X8T4OKpZgjeQ==", "signatures": [{"sig": "MEQCIGyrk5C65SbSHNXiSxNAL/h8QeQ++RDinKY7htCIXmuCAiAFZCNjaHkJqK1Z3yUpWZ6tY3pF+UzaedzrmTZBM+gOsQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhryiUCRA9TVsSAnZWagAARcsP/0KygENaG3r0CzhxYLui\nh7cESEv2RM3yTySuIp9WpBYjXCA3wk3etqGuy0WOHLzwgVl93L1NZbOnk04P\nNkVT8cnLmVEy0nreloAf1HK7HZWJgXkd3d7FY8y/wjP6ItGk8/3TjRNptz9S\nto0NZhHB3akKop0845xdiXJWqfK1BqLFHFZSnrdplMoc+7gO6EYDX2MzHF2s\nPlTlHbqKWf3kqJ0gMfJagcYHMkYvdFXAbQDCGIdVyeLWmH1Udq24PFYX4flf\nCZXS/PEkIcvKNPXJ9r1UuEoVm3/asx7LJQGdVFk7/ra8r65+WOudieZ/R/DH\nIaRS5ky56y6amdw/q4mYRyLQaUJSQtvKZMuIgxaxaF67Z5flzqaHZbhLi78z\nB8tuWZHL4DbFhtOJsBO2F0Dee+KS+I3FFM3tvXu5Urzh5TE4up6M8n9p7foA\nLPWnYJl5FtiUbmrZghTQ4JLO0nEMa7UCvqV/WfjU5RLo1JHeYHcf5BWH85Li\nmwQLdlZnsAn8toqVIjd7w28g7OOcQ3tR0yHCD90nY3FY4Th6iHdzOP5tewh6\n+F+VZRN4yYJm8EtccWmFmd+fRLL3PhyP/aQrZ9fxE721YxUCBJu58AahW//C\nl1ad0HOhYGpWndpAfjRWWH+LIZQOeif8RpXyx/1XwLF55Iz+gwQVRMemuX7P\n+mlg\r\n=mB0n\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.8": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "03a9a6c670c237ad9b209ac069f2f38d8a4ef78e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.8.tgz", "fileCount": 8, "integrity": "sha512-wN7e+Dk2r1YeIHsy3PoYI+ePH4L0GMsSlaGiItgsQ/S0iLy8i1yLQh2oHlBooMsCa91ULcW2Prpauntz24+KDQ==", "signatures": [{"sig": "MEQCIC3l7yzz5GgxC2AQYrsXL3X/7CCDQYS9kjhgNjhgMv8uAiB9pgleE/InaWTvD1QpQq3mtts67HdPRrMP5AcDXAPUgg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhrzQYCRA9TVsSAnZWagAAw0UQAJXOGnyzOECtoKlDr6Ea\nvVUOeu6gQ8LVcYYnKC1HtDfTHun66u1yEcYDvT4fjnsaItcP9KoyL8xmVOcg\nrmoN556YqtrvJvfhx92TwRJi+M5TfNOqODHxwm//cdo5gSZPqZQvTgsTVvgX\n6GEmFtSSXe4TZoqiPWSycDSIDxUrJ9/7GdHwv94xzGKZ9VPu5L31aQK/tUBb\nMMLXosUjz51OErWLD7qvju3gqTF912r19DmDV1I3spY6IxD3EqRXwSZ+NbjP\nOnvDmlS7pm7TVL7UbbwIW67DgHoZ4xeeToiPpIUmGerVtdUA0CbpuUqxm2/4\nza/PzdTO8BHj065Aaa65PxlTlj5pw2yCHtBtivQe1QFrfzk5TRx/JXSq30Q7\nkSTiqwATGlBdX2oU215qywxPk7Lci6z7XzXj/XAZ2uZ5vVM9bH04If5fgk6b\nCfzHuSxPIZoPBIfTVAwf2uOC7rQQkFVLc5juQACWBJP36+s24fhfN6m0uAkQ\n9ucmBAF2a/RIUddJ0ko7+R9qsZcgoXVJCQGAw6ZG5duk4Rhq+Wny8gs7pebp\nRW/+DIzkb9gKksd7gBEYnsknfMzKTCGGtdE1oNxrgVz1r8xuAsRuzriQ2sDQ\nk2R2eVPclgmWtNLOz7WWxLtEgk9aFHXy3LzdjM3oiU9VeXMzKtkehG9QypqO\nXFPV\r\n=RdZB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2-rc.9": {"name": "@radix-ui/react-collapsible", "version": "0.1.2-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "238c0fc22502f9b279e4ab7bba122ddd4b2898c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2-rc.9.tgz", "fileCount": 8, "integrity": "sha512-J36lJWz5BAHtqW7mY9O1b+fLJdWwmOqfu1Xh9lWltopExDDdKlzv7HuUzqVNn1RVF3vpP8WdcZEIyxpz2nUefQ==", "signatures": [{"sig": "MEQCIAYlssKey5ZsaDcRRAL8wyfxi4Ggo5cY/TLqv4wnaqKGAiA1T3sAGec27xpfAIbsvOq8ambL/DTGbepLHvaCEA8AEg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35931, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhr41uCRA9TVsSAnZWagAAw3kQAIn0H2HBJcZM5uBbYuty\nlfAz13IkkL50Z6KehHC8AY9BASLdCByhpSUcHXJAKKVuaM9IWN9gEuBsBbNP\nQJ9Dr8GxwZDKnWde0mdfOfAioY7VLuzXHWHL43nZfTnQ7FjMO3GG1z9APKAj\nS9ERDJzo85lTRojBu7EasdZ99EFXo5jH5+BOkWCStb19kZquY2kV2PLFQ7/c\nNg3ZkRcOjImqqVSEZMhjOChBfdwQcTSlyknIJdy1H1jkrbUUt5hgXV7p1Y6O\n2XkG72baALYtVVCZ4Bq6KCtiiOC5TYyesHs1UHzMdV+nhjJLBqI4jppHDdvk\njCgOPxhd6CcjGmrgBkZATNGKqm++jf1OwX57nYWSSqMaDk6LzHeJZQN6x/1u\nqVk2pJ6yU79ilgaQ6KFnJKZ6aYu4xgqMO3uCTOCzbDIKGboeI9ZjBebHX0ZD\nHbkcbjc0GJd/nDLFNvkwf193xk+aKUf0ewC6Mkj9Iu9es7voCt1CzG6ioUe3\nwBBe8qxwMaGRRnTtOhYqqUTLGCwr5Os6Dr+cNbrRWdOcDXvSvpFCuwys/+J5\ntJ0wTiH85KSLX9TQ9urPP2BLKEknHkgwoKcdgiwNo2UDiklyQ4wkbj7rRGsv\nrc/NPDoI+eJGBf59HabVPf+vUiIZCl5gtbSpiaeXlLgjqTbD69Ejt0TpYMeU\njBeT\r\n=53QI\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.2": {"name": "@radix-ui/react-collapsible", "version": "0.1.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "526d0535a8f6ce578f71c3ecdfa6e3829dcae35d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.2.tgz", "fileCount": 8, "integrity": "sha512-lSKlXlIqrysxdVlgdZ9ZsURU9be0sWAiauBZyGW3j4dMO82W8+7K3eTanp50VtTbCiD/m9tOvGsYXEtpZl8ygQ==", "signatures": [{"sig": "MEUCIEO75GGkoq0IUomUkqigpdveoVm/LqS/LTyQbdz/ndhgAiEA0faouI3nF2bAvMGeUayN+KD03fPlau1MWnnaQ6BY+UY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhshDnCRA9TVsSAnZWagAAROAP/jNFxkm15iUbTX/I8Dsk\nnv3E9lZjFIAUTAZpqpAnR397wqm+WvOeEEpq2Z7FL9/gpd9zBCzva1j/Mo6N\nYw2vNzbP+OzfNbCgXMrnS9gnV0rUNP6aPlCewqeDE/FTGsFYj+Hv1bRHbrUM\nemkttBJr7XaGNbkYKJgStQKUyDY+QVi7kHIPwFZ4+HgAobq998V8Y7/QqOve\nk+5JZ+U7hunlLPt2QGLFSwgMWV2J7ctvQ4n7uPxxjcyNh0YrFjlC1M6+LfoG\nG62/COgc38DWPbfiMTlhatkfCelHN44R3Qpvyusy2QaI8iwbCCqXpe9lCUim\n3RB4PYStAF7w/sLzA4+LWOD74jTUs6PPSQ5tjbpcMgImhhIJMXcRqIPxARZP\n0l2uO6ERsiOqzYYbFg2D46Q4ZF2bGI6iVjDEt5W0l+rBVF1f9RH0LyuhKlzf\ncTvWzI/uyzpYHr3gY2PdvDPcFoeSYU4KyGPit2xfd0NXM0FxOZ03YgizlOry\ngtSR/w/QV3RrW7JbzXouF53hu7UzHXKTlNN/ChSWDprGCp8A3E1X8NpZUWNj\niuQ7OYyv6emZa5+4xuE63TdOppC8ybdcN9gvVCgVhM1Emfkid7oQp4AfqgiL\na5gGyMQARQp/P+3t8SKq5ReI12OVkpHMa/+EUXQQ1mdVXROX3nVvsmqdnTnc\n+/XG\r\n=8hUL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "c3441a828fd465e04fb03699e510a75c22bc5168", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-HyYzw3Op2UgUG0YEqIpVIEwT+Lt6ibz9pbO7ffaP+cTOR7Hb8EjQVg30hXbnVs2HSLkIxYbpo0xdWfe/dDsFUA==", "signatures": [{"sig": "MEYCIQC+uOfrzWHlirPM+gcnu1wkZ/9Bww/RyGi4x3xFO/zIqgIhAKpkM5Kpg3vXUFkSMaDNLOFbU7/cahgJ1Iyx6pUeE4eN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35926, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhsy/6CRA9TVsSAnZWagAAGnUP/156eSNMzvRCfhgFuSk4\nyXb05G4bt0ONfBJMpaKjnE50J997YCyT0E05NC4LHcDN7lqCRDmwYfoSfnV7\nJ4/gSxrjqMX+dBq2VRAtffD5+Y3WeNf1nrTTjblTX47a9zLwCd1k7+QT7mBF\nh5UqsVl86BvjB/RM9sUyeA9KaTaHelEehkeenn75IjDc/OcgM47ls2kMqCSY\n9feQM4Osz5VeMgQT+QpkLxCl2dignNVZwXLvPnlnxquZJRKVhizE6lvStBye\njk08kbCwrfZznIuvPDIS/7v4793qqunsmYwDLkTr0OlCAMcXhruKNmo9O9dL\n6oLmZbXqyJztAqKCS1fHR2R/A7q4b/ya3DVq0iVpKJ5DzAR0uLOBRYIHT3L7\nSOw6uZvzejzD/z/6PrSgKPkA1ivmLCaOcTcG+Vcpo1VQ3+d4y6JJ6fPRz84d\nmbwTlURON91u/iucugSrPMWi4vZJmdaaLt9FFSs95Q6nMWfBEFY/+yRgZonI\nZR57Sl48ub7cqu7Eil8rYlT1ckLLnWhndv4xt3uWPqxVPOo7IwAobprxcKiw\n55zugp9P1+DDpnt/qzx+h8z+jgFnVYPxhSgRuk/d5F+2YqapLL56PIPUMopS\nZ51frG+gwSDuNpABDINpFXfIY1Aqr5hKsnDmlEYSCCuMq18CxzegL3SgQDU3\nm+lU\r\n=FQ2d\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.3": {"name": "@radix-ui/react-collapsible", "version": "0.1.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "5dd9ae27ac57127eedfb3d3651a25c9ca67a4be6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.3.tgz", "fileCount": 8, "integrity": "sha512-pucjN8mCD+rh4150BoG5jG+5qJ/hxcGHSWE79f3Y28BJ920cc6LXvh9VxwuO4hY1kb1XPZthtXcDlNF4Rguaxw==", "signatures": [{"sig": "MEQCIF2n7GGXxynW1POIrd7AMfdCVCq7vxU3mLleHaVgVtoPAiBGa6+Dxw33luoHonIydJWPuCFNPIhxtzXXWNomrakM9Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 35888, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhszrwCRA9TVsSAnZWagAAUeIP/0YupuH2Dd4EYHtnA7tW\nMYIIgsGGLuZ3X2tUtQpYQcROQ9o30+5YhiaiyaMTkBDhlzEAtJhh83Uu0fkC\nz+op6J7Uh/JNyfLrqY7ddn0/sPrJb1E6TAzJByvww2X62f5FuWw+GYqS4TDG\nPYeuzs/8YTCW8RXXrl2JoZO/GKYE+LTYVdtptL2lIEvra+AlDIZH+f6E5fZc\npML27ORA1A4vJnGY3opLWXdZGNqhjxnf6aGqx0fIAEJTeggsjExmDzHTl9gK\nVQF+5EIu6qQtkaoxHhXaHWFxPp20pwDuo5Y2kuAYvHPbtm1Wzhdz/9BfrOZe\nPnZ1zCR1ryiTdhUZj4ej8TTtefrQ9qIV9PAlQB6dBWZ1gtSmNvYjJtdDc8f1\nBOIMKsDzgvevr+zBKasWWKr2gjfR3LRjg03Vu+FvC1WFxeQVvKoPgD2AhlSb\nJ8lcGNw2xbzn3tu744SxiiMPeQ7c1F7G3EwmyNU0T9d3X24WhXTJNQ51eMLh\nOh6DmbDWIFWiv5PON7hmdR02bwydZ41Jo97OMq8XTkrbQBXAouv/K53ufTe0\n+TAZ/olqFtp2xN1QS2Zay+R5cL2zMAwbK/ol6UtHiK6NpQh2+k+ZsEprXgHh\nX+8iw6vrMmwZJyPMa/m3XSDNuHJcVmqs7tY9TenEn9qsrT7ZeKYU0wQo+HLj\nFNgC\r\n=saHX\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4": {"name": "@radix-ui/react-collapsible", "version": "0.1.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "e5a1ab32d1755e2c6db61a17efa2d3464cdc0632", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.4.tgz", "fileCount": 8, "integrity": "sha512-1qKR5Y3grdqYTjtZmUSIbOt5d/hj6tzT8Lc5cpGH71rI/AP20W13FhNQSDSZpq7kZO6FUGFOPUGK6tapjs4bBA==", "signatures": [{"sig": "MEUCIDu4pbxS23Va2qpUeZw+hxIA5+ZkVcks/BYwbCGQPZdpAiEAsGyNXifV2UFnwVXz9zLHBOq0bU+MibileGBdDY0feSM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36196, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs3zoCRA9TVsSAnZWagAAPP4P/1HGcQ5oC4OOqGm4ybjp\neQ6Z16h/X/UgfXY85y6xmUvCarB6VrOVWemgl4Rs7K58ZIFD2z2Rv+JP5a8P\nlpdlaA5cdP1d+LL8NDHe5SFgssH60+qt7bnrUr4rshVKTQaBe/wtl2SBK0vF\n963u3+InE9rCX0/QIo+LGyiXlU3vp5bfy0abeO77NV7WucXFpZcnixtxHW02\nn3ijSct48XzgCoCSTEpP8OiWzWSBanjzh2+0PWF01ftE/q7UtxzNQGMpHI/o\n4MfllPAtkI6WkKg1zeviY7amlexmN9n7UQ5JKYAzfxqtvY3Y+/oH9uC0fEi1\nji3KGu9rYCwr4y4je923kizbMn/pduBnAtARy1I9F7bDnJulhZgGyod4kuXu\nTBKXdoOe/FijcPK1wOvwi1iBfzE9o4h665ZBTEL0QwvQXvj9sqlAUwLd2KAi\n4yJopjz14FZSE566Bbsj85l4B5Sfh+4eX4WyfIWiG0hkruhHG6jjkkjU7zz+\nSqGN5kxTucQREJa8o7ojTpk279qG6Aise8DX6Ktc33q6MG7Ji321wc39wuJ9\nF3XNOx5X9LqLsiCqr3AsEfDTjq7HopjsJchLTmIv61rQQpCbEVHftOUbv1yU\ndT5U9IytasqZjSAD5WxWOIXPdg3waG95J1T+LjKy6wu6LTQmlkYCTAvZsPkr\n5q+o\r\n=oSyl\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.4-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.4-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "53fbe0485e866e86a4d3738395c536979ba4941d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-lgeAqWwvUdWSGQUFS3yiedXvONtQRCAwqVKi/4gmmIWqcT2ANlA/od12ZaeHbjLVLAnoAOT40pUmx3plMFxWBA==", "signatures": [{"sig": "MEYCIQDFQ8kwYgxOnjzLBWzf9/aXbw8NLgst8TRX5XI6nyMEcgIhAO4iirWX1XKfon85aSL11ZLmPSwqvw7tBxJJbTCRXIKS", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36229, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhs30aCRA9TVsSAnZWagAAcpgP/0H1lHQTcwfrHTbg4RRc\nNgk28MMbfs3+BVGIwUqUkLo8HTn8OkrhU/4HMKWIdSiW9xhBf5W+SMoKIDFl\ngxNN+Mbk8BDhsw3jFTDzqwLrD28cBQ72k086qFElslKHnjruahfczBzKUZKZ\nxrx5MtKXDDgOk8cEKCLUpnSsn1NVVDl+KWxPSM65ZJ89fi9s56zW8+B6lWqz\nTvmNO/yM29xiwiPUY65JhVWe6WPxyzcMJMmCUHnVDnrbGgKqk7S0kpsTDrWl\nSL1u8pyUln4WusAjyGNvAv8CioA4XSJms78qKGQXoTkvLyF4/TU7phPPOPaN\nOD1iScXHop+rWd88pysGp9TbzghQOY4MP8S7cPKAPWrJaBWdRgBQ/tUxEDEJ\nuh8MlRKqTh6ogWtCc6fXqes+Y4sIPNHguMrLXWRbb/f6F6lLGMCqvMaCJfob\neWnwfMHzoqH4mxPHuGYIh1iABoFPnTBwWDMGlYJ10wQ0ZWuJuwhsaKdV0g68\njOCvdi+EVaXtDq4KKews3qOKrSlHIxgQREv05NH+T9YLvmwhz3vODEALRVdA\nasJdLXmjbNxO2j5PfqU909sFWHKG6DM6G/SOW6/cMG1zcBgWr49mtmpuNRvC\ndJ4SRWzbpqx3TeztLgIQwFu2ut7jTGgMCJK8+97CXaao0iJCyVnSZ+yzksQr\nPBGO\r\n=SmHj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5": {"name": "@radix-ui/react-collapsible", "version": "0.1.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "086ebc9fb6ee35035dc3e755fa6940bc5b1e5206", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.5.tgz", "fileCount": 8, "integrity": "sha512-jhj0h+gGc04D04mQW1zJgBxMJecYV21XaeyghjyXtp+ObM4EHkXnfOA5MyRMzSNB4u8wQ6P1zGtAbxHz1A4Vvg==", "signatures": [{"sig": "MEYCIQDgANS/b/3zYblyF8M/8OZB35CgjAyoev2q37kN2yhKWgIhAMTq+DfCYet3c8jYTwB//vpVKocepbdDcq2lnC8nwcO1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36912, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLidCRA9TVsSAnZWagAAetcP/3Pi6Zb9G9D/AK/HKhHk\nPlkOmDXcfA1+VZJ4OqpOP/3csm8Xbkbz/D8WNFous0skAS0LNIfXXIqZ79V4\nGPZFL02JPNMjSzt57wPH5EUrjmitLDhoLC6tOa418raHThhLM7ceve47Fdm9\n/S1jvsQ/6pt3mwWcS96HAcyt574lCQNS0K++MIo25T9j40KbgE+EGprZ4SHh\nt/JWsZrQBB31BZs8TxK4Jyd8occTQ4BpiOE/A32xeHK6KCderJ0g2Mzd6rd2\nZ9WLgP1sVpbHmpmFmFqWonMNzR9nn79DorzF0M1buzTkDsSNuyGkD1nwsZst\nTIRvf+A6r7UPaYeAHJLgodnRVBSPSlh048yqhaBoCpB+BtjhPn45gVeTwvDg\nJ4a6ekVuOVzgmILTk79fil7c+JCLoAYbfdO9o50PCMNPj4XxNZzVaBE64rBl\nITOvQEEFtpNToUVhxbNnaPKvf46ZO8BffxFZDx53eBTDXkgE5iUoTPMqBOaB\nE5xqz6CuIp0AATgrUZKBgofgQM6n8SkTbjwDmPWn+HDhpmRaPcMHNDqZ2gGk\nNx8HaRFlzXu0jQdoQnHGz9PBRWpJOI1JfMFlFEdvWxt1nMt2fzNPPMfjJWwa\n7BwmBGeibgL5iXfMyZX2ECgVzzQvP3lbRelq041DjN85fIBNT5JYhrNoMcNl\n+9up\r\n=CmqT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.5-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.5-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.4-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.3-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "4a96db26803278c366b40b4b41c545becca90f03", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.5-rc.1.tgz", "fileCount": 8, "integrity": "sha512-tWwX8ErhPeEX0P32HLb0EXm1itowuvsaP5f4Ze89ojWWb2s3UR8UKXe6dHHyF7PpRn0C8/t3I7WnOcWyTMAC0A==", "signatures": [{"sig": "MEYCIQCZxwN9GCgmEP2N1uhjpchO8A8CFC6H60oBi5Mc4CFVEgIhAIyvz31usfsl+9wA17Qwr9Xg9Xqw+pblfRdoluDhGYdb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJhuLimCRA9TVsSAnZWagAAQ3UQAJ3YsceUm64xrQ+/KbpR\nfuiEpHP7pzNP0lnuZz0p40vWbJxu5o77x8uJA0zF26hPIctRdN8a6SRfDZAP\nLwKVaSz/0Ex/iipJ1dadRxNUEls8fiz4Aw5pPNhl2vzdiZfDcUDr5bhHt2pu\nHpdZXtWLqXreDAkrDhIhBBPGsvzwYsb3Z9mxBxFXMaAhlFbHYZJfrFv9SKBi\n/QPRVnDqzTvQOrw1C0XHR4TGHRb/IZ7mElWzvmfa8jRpEIoU1UCI2Bztq+Sb\nyDwkuWSqrw9idDJxDjIplGA0v8Axzs25zx1E2mktyN5kZZv+iILPQFLgQO/d\nDagzewK3ixBdImUs7tvsASSW3U9XcwO/g46VBRmRdn0VZkVQBThFdUFsJCOw\n5nVS9frAQATGpf+3clgSI1DY1jNKQ9jymGtsZ2BGRwnpxaBeWdXJ+Pwo5Zwa\nXyHwDpA6hKGqLCoBs9H6+RqjBDfu93RF1PkWjucVgrMSn//cVkB9UUAhZrKJ\nHkUdgH+8OXwXqeyNd27FgxoioiUwihOMZ6EvcA1uV0VAyfNqpqjUYvY+gSBh\nCnmzlencEtJCbPdMOkkc7B62lSVRes1YN5kW8pE5eRwNSq4QFelDEffcZ53a\n//20LdjaPIVZIm6W+0oBcDD+xwD/kU3kTEswAcC4SDWOz0XdYujtJopWOT40\n+xmt\r\n=nz70\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.1", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eb4f3b7b71a46bc84cee84b6cca811d2adaf9c99", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.1.tgz", "fileCount": 8, "integrity": "sha512-4CWgwAXHd7KTSdY48SLPNlEIiHdwamNBQVmmC/4vP6+h/ygclxT8TQbXfHDaKF8gutRJRjzbbATJHyTt6Qu8Lg==", "signatures": [{"sig": "MEUCIQD49xaVe0RX+pm4t/Q1YpfsKoV97y1pl/p0FpLZSqCmSQIgNB0g9m9eu5tdsaHDecxWWeVqlZh0j2k7O22QlooB3og=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh31qNCRA9TVsSAnZWagAAUKsP/36c6q4idl22UHJUK4Ml\nr3vgfpg0iKDk/hTSlJiE4glaKORqdatQTN7bX7hGpsGPwow4PSbA+uUuDz+o\nZNRw0YT56b7U/eznzqQ7h34LQRcxuyA0yJQvim8x9PXLagPCbfekOaXQRkUy\nO55gVSaGdFLjL9o9YgvIDizK34bV4e16pyAzeJSFfFSzLdWwYo5c+3xoUO8R\nDSsDuwmYDtXEomcLJxHOmkeTfd+IO9iCMY9+Yrz9Iq0amYGrMO9j0De4/gjW\nnBdSbf2u5QQqx633VaYerQrpDSwcOL48F2RlTpIpp7WiptY0/2CZt0GCIGmJ\ny0dJJRDEBDjQWj80UEE6Y37c45tz9RJn2bz/mfDz9CvQ3h2F8ig0nhQ/mprr\nu1ogHSqlgf7OiEjHNbaG8ZUeIc10bvSvOnpgHKqZoJB57SxjsjElVTMGfoHJ\ns7K2gd4t3zJgD4uliXuNh6JEtxJTV/8dgpHCZDuS3aB2EO4xfFh5lCdeVEz7\n6rjbXjSaqTP+x6CTmmGaCkSnqlWGEPWNJN3jxvk+52SzJinYSFAG//6j/fYC\nFmw8bu1x/hOfQedZjER6P2V3GHe/ETUoLS7nMxsQmM7JDytcRHdlzEyN8AvU\ndDHZCky31to3w7Q+5wiSLL5rcUWYyhCpY0hU7Xfr4ziuxg/1CNg8gk73Hw0s\nDJI1\r\n=KiKv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.2": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.2", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "157ca3b25fd0962424f8aede844aee4d03c5ae09", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.2.tgz", "fileCount": 8, "integrity": "sha512-wZLR1pZDvcPqjFjmZZyZPIsP/4LX4O6GH367qa8qYT0Ecxljxo1vfqiKG+QuxkBhUXJzR3X+OcR6famFKefLhw==", "signatures": [{"sig": "MEUCIEZoKwbGwcUpBuPOJqYQk2nTeKTdScCkcwUgkILbyz6YAiEA1j0YJu2jHVRU3c5Hk5D+MVP+QBbweV6rSWxalCQwqv8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4BDICRA9TVsSAnZWagAArAIP/0se7UOhOTxLuf75fyD0\nf+7cKxhRCupJJ2KjwA3vpIOf8gPeBUnLCWniZJRUazx1tAJZSqMjl8/LyC/i\nFiUonRGsLLi5HiW6sZNlsR5nxy3Abt7jkh54iReZKmwmEugu4M0mce3MpYXG\nV3G4+TzX490v7+x3kniBBdjdEDRiXt/NvzvFDEOQPUkl6p46MPB2+Qbdaoy4\nKomCepNq6R8Jqe80IanJCmeGuweHa6IgrOpe7t/Fv8UoouaagRFaQOb2D4cL\n6yfKCsHfwVVhtYXVpDyJj0s3e0TCb3ntu3xu8atDXpk6o/ioXK8/Ur+WXvwA\nRCSWKEhV4Ub1d/c5qas4f/TG5D2YsPtOuxWaHJA87MwuuPjSrfqBSlIG33C7\nmd82T2OhiV5/BJwvJV6Ki28LKpqcXLTNVbO5PX52ANgoTTo7RcRd5VRAXrlN\nykljlwYrga1XEthcniEPlKpjFUapTbpqaaqMrGXYqInz2rVv6bYQe4oxI86E\nJdQCf1EmZJkpIzkp3ABskgSpAAD3mxkCG0YFW4GDEGQgbRKgvhUMldLv5E1W\nqq6MQKdUU75B9zw51/geccYhEHQhKrPDHw8VZJ3c4bTkyeWjKcQiJzGbwGqy\nxSGL47Pv8MdRrNfezZ2Htu+AGIpBLUJxfqZeYBtlLPBZ9usTWnkjDi9Bs55X\nBRsS\r\n=Tk4i\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.3": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.3", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a7e4b90b794fdcde9389ee5dba63808de672e063", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.3.tgz", "fileCount": 8, "integrity": "sha512-CDSPlWZ6D6EJu9tZSruB9NesVfAU2P6Oxl0jSf3QlMNBQfoGQZYs1pKcPCgT8mNZuYW01t0yL2polYmTjL/ddA==", "signatures": [{"sig": "MEUCIGp6KyPcBDddGmOSv+VYdhHhMtT5mpL3OEePUur+0iI6AiEA2pF0czozRSmF2xK+U2STLfbLnoFP0O7VaDUb/HjCpW4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4ClsCRA9TVsSAnZWagAA6JUP/3iyfpCMDugrCWeeGCKW\njuNuyHmnr8mZCHMS56fBcQvhbQcxrnyXybI7uXuKf1Qksd0Agi3OCZm2C4bt\nyFSk5IrEDVByIH88vJ45HXzWLK/FJlaKj1Qykavaf0DbKNCHCBx4les4TG3Z\nsU4ncSlrhsAfbgtEcWrVLIcNDFj65awJp+3yE5vZxC74ajHqSZGDT9HaJNpu\n5BTs2jK+msDTta3LE1KCaRQHahazBoP8PLa5yOgBJoZKoCi7Nze6Se/tka3P\nV6qnfucedmrTXnSN8yVEsA0gWHeY9EZh8IqRgXD0uTPXEpuAV5t8mbyxGuYV\n/8UGLcSoZIAnGVHGMtm34jMOHenLwJ8Hicd9DiqAo5/KI2mNToUfiBQ44aci\n6QGxtEKhYgMV7M9ojo1rrGc07MTAN4briIRcjEKSeHnqKq012diVrx4C9UxN\ngSJDfobMU3FUBxa/9bRyoeD0+RM87vqteKRWzXnWqicE8NNJPaUheXArUenH\nRJdEOUJCTdSDFJMdU/kybwjboQy9z2r67H0mBV1VtN7uQbLv+m5Eaq1ZHfp4\nW/hl2yGgt90FZ9oryzGOaDTByQPUrBoLSG1tiehxLbfTjcRP8TxWxvrj2t0S\nrMnvRfp6C/jShF3m3Y20/ZbNGGE9JjFaT1GZwIRNzZ6sv6TH0BTp/1MKxQpl\nFBDa\r\n=pPgg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.4": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "a9f618419b1f9a96547a868d020fe4fa42ffc8ff", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.4.tgz", "fileCount": 8, "integrity": "sha512-asIjH3l0n1LT0k+ForPtVG1SOE1j84lJ5nbBwieEcuMOEfbTiy1WpVAmGgZQNZipQbGu0oSDhokx7pJnq/JVwQ==", "signatures": [{"sig": "MEUCIBCKzsK6Ha+Vanpf272p7SOQ4WGLlGcEw9RHdqqHGOudAiEAzdnxh0M7TkbCECkp1Ay7LraRzYb9fB0r8pC0v22Gd8g=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 36955, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh4GpXCRA9TVsSAnZWagAA9IIQAKKZvuRlxG13kFDJYPCJ\najfnmGr88NmRY3ykGWyxK22Okw6RV81N2RsoXXeROmk8kdJmYtDMywWCeBHN\nTPrEPGzSKjqgmblGxpgkbKS38y+Pg6VYTG34tCrFdNLdpxKDe1iqDHEDRwEo\nHFv+9l5EztxajJ5nTxoRqZizc9HhoBTLWjKa/OKHwv6Bg4H7+NatTZrPrnUK\n8sOdObO7k6swD9vXQx1ODdCvfTQSbss3PdJ7R1Cx8HO8qXpB0Oxshvjfs1Il\nzRSajlEdoe0eUcWDufuXEXQiqSMUcV2NS4dV0J58/I0DM2G7T9u6EDfNAnuZ\nNDbF8DtJ1PinNvXbU4cN3UrMU0ES4nKrFbkvlxUS6LkqsMsFM0wNzGA1NgV5\ni+xn1xaC5Sd7iclTeGxULdVAE+E19CJBykMxY6YTkba9WA9ETMwdcSsDND4B\nQoBMntSzaV/gpeOk3Hb6irDd6r3ivnU9vHLE2KwoniT5Mddy1XYl53trJ+VN\nc6CvgIfcgrxqvTwO9AY08RqE2d0rZc/sbu84suJg4uvj2clQsAOiRGAs6d5G\n1mGBS4giWkGXszUuM/VJcrgcOPT61EJ4h375vFMBexagCgUSVACWWSqDlbkx\nGsvpcAPddF2MqmSEsbvBlhgJ/JtTj0dABtT2aoFDigw7MVT7C0NisEG4Wd75\ndm1p\r\n=5Nry\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.5": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.5", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2dfdffd943080c6d4ade4a988d64cbe5a02f9c6a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.5.tgz", "fileCount": 8, "integrity": "sha512-mpFEy+9y0gI5qkkzlaAMJnWPJSOmep9/qc4tiOZA0YUK4QIDE2QK9uCR4Ep1ePdCiHW6YIl8PIisILRbzZ4QfA==", "signatures": [{"sig": "MEQCIDdNA8KrpiKQfk1JIbWVcqkPeOwkxhRyDOzPj4XcRTsGAiBtob4Fu7x7RzvNGrNti9sCsiw6TbFYF5mB7nW3vL4C+A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh5ZbdCRA9TVsSAnZWagAAR64P/05t638VdZfYnnPinr/4\nOEJPTvdPVjR+PbhvBrwEmVQZsciHgWI7dTbk07jiWrztgPfdz1Wl9ed62JSn\nDJAEgdrcjSctzVG8wcqcpH1OSnOrqNkAcmZ1oUnRPEgoA7NM2YOXdUGqzi54\nm56VGtbbjBEle/gX4BUvNRmEgZ4yTJae8WImU+MzWvQzzacj214nqvnjkAxM\nPrmdci1rRcMFg0D8aHUAvYlra8Q/6Bb57kqqpTTRlnAWG8NO9/10IhWI5Rrl\n6ojnBeR/y4Q13P8Aa8cYDAn6CR0/ZJBYv0nE/AygL+Ix83bL/bfAzvQZFzAE\n4rH0c9WQcGinVwXRxSCsucPsM+Tae7jC5Kc0O4pTvYYDKM3bZB3c5TIUAj0k\nPKliyvx4xqcgUE1E2W7//A9KmNTjb21y0T44vpjIWwAvsbLdM79C1oXkN3H8\nrIl/AHoGes68OE9jG25ib4Dr/5xRwQG+HSY8evg0wVqDHgce/sx9RjLzSLDk\nOdyLGEDnC6sWpCMNDo1hWFA3zk6RFMnnh9a2Zitp/GchRx1f0b/SsQJCJBqL\n7L12Tn3tLP6U90DSA9ks9zc9sntH3dCByGniQQ7cmn/46J3rFADSqmSMOFYQ\ndOIo1O/iCIB3VPdgfJqTeceTBs3sEKZCTlusYKjqo32CnY5HXUexu+E//jE7\n2qKA\r\n=0zY8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.6": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.6", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9a41e03a56482a1d3b1d567be8aec0052845cc40", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.6.tgz", "fileCount": 8, "integrity": "sha512-5FHAFmCbCNPDEDje4v4W7KY2M0uuWWnWl6SRXS7yI3Ppe2LNe1un8SYL6GUNTg+DQkuBNsQEU3lq3QpSLUWqfA==", "signatures": [{"sig": "MEUCIHFd3xAOuZZCmejC05VrgEBt6xJBothF2QCieyGcslY4AiEAy7BFdo6lgLta9bmRieFXxJr2qkz/NIPijXte2Y9BcqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6Yr4CRA9TVsSAnZWagAAwpsP/jWpF5UTUadd40iDT8hw\nJGb5QHlfVkreHmiuOKZYLypqQkaf8lW7szCvkavgxatPtRvchOhPV4eHH5mF\nrbp6QtDN8DjS/xBKLQ1gpV2tjVaCyfmF2iwZKTm78zDxFHypOc5Yf87TWkMp\nf5IKd0S1svz4+PHHIjKDXcF2GVOjXucyySXF32rkVfqX9Fr6q3sSDBplH2v/\nqbT6YU4OZV9/FLjuFvbNZOfuwA9LRG+68xBx4nnUiAZrOQV1H3YNG3TjBQ58\nss1kICM/SdiClBTVn2+1MNYY4GX9Y3tNJx2eGXrM4qqDEVceGJX4J5OnHPlm\n5/7WpGEUJsk4DLah9G4oBH5f4Mlv16N8FBr0u3I8kVw5vMMMyD64xzRBrnLZ\nwdk7BTjIoyberQLkBnTcul/whhp3ugtwFLRFq2CPF7LS9M837HUMeYllYsST\nCsQOHvruN4Qv/tIcY3iBLRHfyvfxPx5jZ93PubDYsbBu9moKoi4HPFzGY17E\n6N4yVgY0jenkA6UZfj+VAlEgoFHTTpXMyKcOVa0JGdV8qP05zgY99aXXgRUp\nZkdf+UxZ0B00HkyTCNMGyx38lnE4aiEnI0z5p9uzNrF7E5EsYuD2H2cQhCtb\nklWHONpKavS0gSioEhUVzWLEXd+Lrbb9oo0Z+GKnCu2hdeiI6j/XOXDW5kQV\nBkT9\r\n=ADfJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.7": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.7", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.7", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9d710952811d03475a5e22fbec7bda2f05f7e5ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.7.tgz", "fileCount": 8, "integrity": "sha512-H5uGsZc7W/jWdE2I0Kl6Kia6J2JLXO8WohVoJpeR27htnOaFGserWPdNFXewRVPxqmv72aPb/aYCEFpS0yH/mg==", "signatures": [{"sig": "MEQCID/VcqeI0Y8K1Y0eweGRdtuqX8ktF0X/rgqzmgVOycOBAiBfm4Ry8ipJCOI+ukL74juQbAQPh+VUJW7cbiRJzXWdew==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6scaCRA9TVsSAnZWagAA7voQAIVTRRtjv+wTSD49/mbg\nGNSCXfWrdu4aWJ+zoNUqYyWxoF7Tkhp1WV9IshJfm6XLFi5yv/x5N1mnMVR8\ngzZrAS5IAWAfZ4CpW8PLEBU3pee1fZAFVa6eqH7iK03ZaLw4rQx5XXd0a7MV\nNQzN0IQFoPX8odTbkcRVSXTCyRxcO7W+19/JgKXXwJS8soCcxMYQuz3OcTcN\nRcGLisgRMWB8z5PxGJYCaZ6O5QHMgdshawGi8EWIETg6T0YwvAfnjy6PEZ9W\nBWJfGUmivhIyPd9GvQX/YDnjNjY0Cx+aNzcfTFG/JSgPa8Nza1yjqJUxXs2V\nYvNdAyfzZ0NSoyeCwUNOn6Yw0b5zKP81KzqvF0+EFN8RUM4EbM305gIa9ovY\nCYqqHm3VST/7KQ5fWZpJCus9lBrB+p+M3EHwTka4Cp0w9QM1JRl7XJDxlIga\nYbHn3QLkz7PCm0xaWkF7LIqHgIjC23NPAjOexKAHi266HTzrKto+9oRoCD2c\ncdYIIAcQ1GclNoNr68nhCAIriHn0hk+vCTLJHnVQ/mJM6OoyfFXBqH7TQyGq\nxsgehjaD1aU30gH8aN1UA1Ymy6AS4f7jTqCeqsPFvwT7V+i0OW7qyqFuXDUY\nVhGkrVq3sNcKSxL7Mt8PjbVa9s8pTR2sH9l4ZHENmtEfFoNigXVwuNHwkcbJ\n2IcL\r\n=3brB\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.8": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.8", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.1", "@radix-ui/react-primitive": "0.1.4-rc.8", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3c2b97092ca5e87637a941ce33a96f8d624cef8d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.8.tgz", "fileCount": 8, "integrity": "sha512-lgTUMh61kt5hsyTK4L3T71dHJLICEWxSgRPyJasi3GOiA92tVk9/5rpyNIA7M1Fq0jDYEh2LEgCRgz4oACuz/w==", "signatures": [{"sig": "MEUCIQCBKzxTQODV9WLHUHo/9MLlcUvnMwkRTAXkD8dcfb4uugIgIUC7g1L/a93Nkqw4OwboiD+xbuS2cSTL/AxYnbrNc9I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37073, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh6xCcCRA9TVsSAnZWagAAadQQAKP6Hg9cO2mqr45/KvjY\nSqm0/KyQn4qWZroinBQjfGcYxY92FOkXBWLyQBjWN+VIBWlXLoNbFXgqtWrH\nEubKUjJ7nQfyCLpBRFuaQhbJmjesxWHn3S3oxtDevq+jbQ2i9TmPO0dFB3Zb\n/ZLds03d2lvah/XK6CtVUL6QrM+Js8kxBGNHsv3SHgcY/dFgncc8sWR0Htqe\n3UNLOIB1F5paoHTHLOeQCVjY2fidINdsM31VWIMhSEpEtSradRkJ6z5VKf4c\nle5hyYnRTKroU+hz7TbY7qbQf2Qnn/t+2eAhJ0lTCsffYyj5xq8GAEE2nDUx\nyH6ABZFXiwtE2uuLMAolmPNBlbY1T5RC8lxE/Zp3qXWvKt7ih1rMFL0ivAKK\nnzwdln6KTGWTMhZ1XhpieVDmvE5KnSDXSOJDaOk2jq2/6GcUkqWyQ4iaTGNz\nwYWeBcaHTaykTP1I5XcfC/gbLmAnxragQfybx/6dU8vdetO3kEvlknD2k6Xv\nn7svQ4t4BgCk4FGi0pUbRkPnZB9PwgCvTCLtXeno/Ft6oEBS4QKPEMJrPqZ2\nsEURSGgnde/bdQCe2vY354oqZA3Reki1w5HVBI+QEej9oRgEAEYYdGCnFVXS\n6g91jVGGiNXlQ7h8H2DKdauy3438d5HeIbag/3Ifvy4LFxJEfQUgJ9uX6sxW\nk7x+\r\n=dzLn\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.9": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.9", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.1", "@radix-ui/react-primitive": "0.1.4-rc.9", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2d32c18a1a1ee6df895dca43a0f02cae6ab99b69", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.9.tgz", "fileCount": 8, "integrity": "sha512-RaKg/mtzBpR9T/ly/GZJHH2MKY8IDOOOaCHmXvbqZxHkHkDO05LIBlRDOThL6y6BEv8q+jHdAPG5TUnunrtR/A==", "signatures": [{"sig": "MEUCIQCpBCQiLgcMPdNX5jWetHbaHo4fXbOsQTq+W2GT1MA33AIgb7Qeqf/LmZd83W5lQQ/z7VfL5hlGMiOJjdH0Pmc2JXc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37078, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh7xKBCRA9TVsSAnZWagAAhgQP/R3wpml0Kmz8nV5vsgJ3\nRwCyE/m9TBdUE6OApwy43HnTbH+DUjS8R96GM+juDvr0cFjCka2wBPujeWq8\nlnEBPARZLbU2QTDFRKAvW5q6Czmxj88V88ACSdScNi8j7PlNhZWbra6Alzcb\niOaM0mTA1Fj4jcKFcBb3YTPlHBYcbKooX1g9S+nY9Tdv8tIVFnA1sZjelqp2\nxHt4SPosNwFohSEoGe9A8+sU9Wj3I9nExaYYuYFg/DTFgivQnQmlnljKvCBL\npuoLLbkUeoU5B50/I3GO3C3WLlQTTnkr0zGnG494H+pJOcQ59WJqkHXTtN4B\nbXItjFFINY/b9SUjs8O6SoUgqKgaa6og4m1f8+e3wXju6cCZnjkz45pePYtR\n2/P9CQBHHFvjCanLguEGEHLC2PIqgxEHXSlg3yqoFXmWq0rPU9rTq+uYTEtB\nh2CRjhPiMrwFjamcljRCOAEYmshn0yYjYuLupKouObfZUg/DhaA5SIeUJY+G\nyObw656AitPHtMt3epTxS3fFezqGU24Cffq00oWTLL68XtORE5VLbwd5Bytm\nnDVt6Y5Tx1Cvl4UrZUjS83oslAAaj0co7FwbeTjax+C4ENfai7FWEx1n8U32\nnAkKp2d7YRkKuLJh/zsEAoSOPvZTrlfYT0pSrer2epy7udx0jjjbn3A9EgjG\n8Naw\r\n=Xb2h\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.10": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.2", "@radix-ui/react-primitive": "0.1.4-rc.10", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "b1d2c1ed49b7ca57fce5f9bcb4d4f7881c3d7666", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.10.tgz", "fileCount": 8, "integrity": "sha512-xyn70JYd5CiwkcgR/XFUKxNEvukCQ7a2tX6fyYrDMfnfggUbrI0g/CGVj9vnZTvEmQY1dTte2HzPbM0gWpMlUg==", "signatures": [{"sig": "MEQCIBNpToXocFTEgTJjbtg4f7xtR2bii2Ht4aUt7m59RJCVAiAYqblVlAQ3IBPzFfTEcDdfkkrXxvBq6oRjKUP6kY0uVA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8DzBCRA9TVsSAnZWagAAoRoP/3EXMENOJy/iM4j0fFaq\nX6jpRZY0Am05rernYMBCkDJX3AGh/hCn14/A78rU2dZsb8HSeijVWrP0Ocst\n60Za1M2la3IkCIyjwxLYxJ7QjlaO/Ft+nfTXUtEsiJEw37k+euZeo+AAmFp+\nJAL0Q19aCskWByXHsUcuiE9lEGjq5CFTxtnGsLj+I8XnGh9jy+i4thqxWpr+\nLsk0rKALfT9ad4IP5a11GJqgd9aipiFzUgr2LNvBpSFeYAk5Y0lDZ+AZAMTH\nwFjIn2CFiev11CiTvbkATufrV4YGoSrWLULpQwu9fP1a8R+7cyo0+hL9e1nI\njnOutnQehkmomhCKjn9h8RXqJNsDVPjq/pZQEu9C3bqKECgYOEUxLUdjvUhO\nxtFWQ754UgSfRncHPPx2LBdoEnsnoUHzJuEozY+Mgv0hZhRfl2aulxXxXLUF\nOgt7S7qiO/9N7t+NDUcyURP27VBzTA2gc4TXmfZNoOJWrrE9rFTHOEEaFe9E\nvh4De3YrqsIKFtMViXrhAEXo/NDSE52CsVoipno6x/NBBapn8FZu8FQMdsOu\nmh3JjljGrkhoxMTvtavCS8G59QjDnDlodS6kt4RnJ95XHQEYzD0hkzidt9cF\nmdI6kTMsaaBsHAjbptBTQsI087WpwI3i+ciIJLPNQ0oUSHFh7I3290J+NMs5\nm/Jj\r\n=XJKm\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.11": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.11", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.3", "@radix-ui/react-primitive": "0.1.4-rc.11", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9b7416740afc000a2471d1243a12f90352fe8d8c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.11.tgz", "fileCount": 8, "integrity": "sha512-op04abhCgqaO2baJPJFFo4gghMBbrw2TXpqpEpiGaRTN+o2bSa8VUBZzMhQT1yP2+CbD6GkNexQvkBzkE8XoSQ==", "signatures": [{"sig": "MEUCID6Ux8NZLuEXGvZEbMK6gll9vMIn9ZKreLwkWVatOZWTAiEA0BDiev6h+dwKZfyWJdOmOXVZXFwM5KYOzs3aIw0fz+0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh8SSACRA9TVsSAnZWagAAgkUP/iHTUY1aak6tmiZERLzf\nv4RDpV8ZQgV/p7/lRiHOkGhB1emlUzk7wVjZes57jnsica+JRXclzsl8h/eG\nkEisuyTtIF1AcOXtqQdOPMca/CExvVVHb3+EJ3+TmU4wsTeVUVDYcY7bVpzn\n3b7FvYB679yz46gcta4/XOxi3ArQX3h7Nmjdhuk7zV9WXAJl1Bo4p/gxbBq5\n87HpckBjKZKuM9PRAhodnMc32GIWV290xGxxD2xBFe5wXPa+kA0VPR551ubL\nsVGjji1Ut9bci7MJ6BISOQi2byaTgUrHb55mrlCDyCMbgNXkL3PdX9laliCn\ngNWaRfR2uLU6d1JUdZCDqLmE7BTrxNZmgGs7kw+hzsXzTDIsAxkzJenWajDc\npJAT3Omtx1Ydb8jODMnJuTGWW9/stboj2/Cgjwl2RdXUoFco1fktOOGfpv8j\nHCKL0pGIFwxteqEr2YZcCzTYI9arSrZ93zpFPq4c3HWcfEyM+QK2Ck/Z9c0X\nQS0dPcrYUEAbiEoFsxMAdOO78YvP+ter6CKGJvEawr1R6lquE+Wv3Ysr6bX5\ntbGAnspRN1yejVi1uiLkZ9taH57nkIM1y3W6rzTR5njVGwP1W4N/JYNJk6pu\nNxc9kzi062YvIafNs83gir1Vc/CocWRsW31QGR052rqZwAevdPWJnsWO5+zD\nQTiH\r\n=I098\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.12": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.12", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.4", "@radix-ui/react-primitive": "0.1.4-rc.12", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9dd4c3b5e20ff81ae6841828989f5916439e9eb1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.12.tgz", "fileCount": 8, "integrity": "sha512-KqZaojfrwevqlUvB/TG2nkurwGUMWvpVnd/MZywbTfotR5DzJ4AerjC+e7igZRc67S/MvSa+o1MmNsfOiB8d+g==", "signatures": [{"sig": "MEYCIQDMNogQQflRP7WRSPupc+jF5P62xJ1y6N4pak8MIW80jgIhAMHGpaKGhNLqrgp4mlUBs18ve4ClkcNc5Q0iHEDVblq2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37081, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh9DY9CRA9TVsSAnZWagAALOIP/00BRjOVICpmt9QDbJiC\nHsR3Kegg8UQIjkPh+jJzu7OVFP+TE9SClthPo/zXd2mcQ9ZC8escFh2EdRgz\n2kXoZ8BIoouVD/+XwjD9cmBgmLD+OWMGhCCkLlSnopzPbCf6nlubiT+EOHVt\nVG5RxhDmpfmLpvJmMOHWyraKAcVSRDHFKHM8zoO48X9YhmKxz6QVjYhJPzSQ\njkbDYbIjLR+kr88NbDzu2tPBTP0/W5SLJ5KPTSjqy88JbfqNHXZHeAxLBlkJ\nrozItsNhiqYEZuEZFHgGRXMO0aEPM/F1uAflcQ1EfNUyRecLMByO+ZQXKxTN\nUHONwqfbScrxPwGuI0JmEUz9HjJI533PFICq5r+bYDQei2M3j6pzrK3Lk9u+\nEK+ICs2kmqnrjMfeTsxBUic5yjS0iRrO1GF3HLoePZtmkldIqEt4h3iM/0qe\n6z5A0RIh9ZJSUaZK3RY5VREkt6nAQopO1mCQ8wT1UcaIJStjU2twCYy234O9\nCNiQ31KeCsc6TpkFKkZBQ8yMhOKduDARpphr9+SWaua5wSNBSVFyjt/8IHAT\nXRWKi10nyi4SGdI7hD17wDjblSmR2afvJ4wgrslmN9GJjifaX+Clr8WNRMhB\nvzLmWh3Qfdli23SU5AGllAGKRs7pEekN6iAOy0g6yUke3ThRBLw6HC9ZoGAL\nl7Qo\r\n=GV1E\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.13": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.13", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.5", "@radix-ui/react-primitive": "0.1.4-rc.13", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0a8a7d2e03814bfe19a0ed5f6732eb880940196f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.13.tgz", "fileCount": 8, "integrity": "sha512-IzE74joQFwAqKVICEkONK0lv1qX6xEKkXejKJ+BjSZZ8zCwSMA4ocIpE6qYm2HzpRAX5KACxFtYTn5zUXyQu1w==", "signatures": [{"sig": "MEUCIHbA8mQ0OLhY12ms4OWzqty1J1NjGDtjT+qlzN5srJxbAiEAkauwx+9UifrXgo8Z4qaKZQh1FD0aMmI+964ULN1GOxs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+WnACRA9TVsSAnZWagAAw8oP/01MNEIjuh0wr5zUe9AK\n6cv6qkuuz1NTK4+BYwuog/0pTwYwboVeB1CDnPEdkYRpmxFtI+DwWF+uzkJa\nSS8HViTdhZbaEvsmbAORd2vSHRBcCyJUi4KwswJ59Dl7AqB4CrAd8M40PXxI\neR8wzXM3YTvHbLbQ4+ZjVNCcmvTe1JCJKTPjTjBO4heUutvMU+ElzR20GcPA\nRbkbT9NOkMlPDozeP5C+OLzSDoOQXy12Gi53sfeJ2ppCb9DD+Sw6ho8YQNQK\n0G4VjpoY3BZlTgnXTZYAOIZ59jWvkADR3Vht6QNI2VKkf71PHnSKqwnZ+HrF\nn+EOlw1YNIvxqLSfabtpgJdfIdPT4hdUA+wqt74W8RtN8pZzfKE2MrnfY6tD\nRmgqVjGdYvFM90CSJvp5mgV69SUrQFeriQtLEZSEuk92UcwsADCdz5RTtMfs\nWbsDyjHoIEVNZOo7lcPf1U8rNhOpQO6nI0DljDe8CYFWU+V/E3aJxz8F1y+Z\n8Zjstlv19TAQNdt0a/7KwvBa284YF+QKjDldR8ksPwRNZZxTFM9FMg/wDawt\nJ+nxn+QoAB3At5cCvB2xcueyCatOxWCqmEmAfxp4f+zF2/kd7fGV5I7vW9qM\nkT8QW0BP0boguT1r4/moMPXYhcLf16BHUpGRrTPFoYY92ifMwABxqfGJpvT1\nVJw7\r\n=qaxL\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.14": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.14", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.6", "@radix-ui/react-primitive": "0.1.4-rc.14", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "6ad08880e0c03c3bf5557a239d524a725b2e4256", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.14.tgz", "fileCount": 8, "integrity": "sha512-PElHbdSTk3xtCN2iMGJ2ZByniejAPoGVDnAKd9+g/LQr+V8ZL24hLlazCjU2Lbc0rDmV/x5y4x7SmAbHgcjsuQ==", "signatures": [{"sig": "MEUCIBXMu0exy/KKa+OCgz7DxrFbRGldKpgc4Lcy9bNQrxBdAiEA5wCoDSK75n+DniEGC++TgS7PSDJ3FQz8TQQhOzxXw1E=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJh+rTZCRA9TVsSAnZWagAAtpwP/3iLrm4PyQw3cxZ0B6Bm\nHkzlPrPk/ed5iSpB3mrYUq++YxT2dDL9x93yAEIJGEpYRguWcFa4c5GKqcIK\nDNVBkWtJFG5vomsgx9ZIAlqHfBsNCGnX4ROgAuw5shWaD1YHdPGnuDQl9wNc\ni356EWSV4i8G/OsK8SAX9x+vSYMrzcFpFlwm53sM4Bf3G+JnS94sSDtZ73Fd\ngj4pruNhpNckzyNQntoO8FJo54DVFOQ2avZDxvo1ZRb+IiHmcd8tG639p/zn\nltfI3UFIi1KvavvAB0L1vDtuioHjUgItCdv5Szu/MxyjyWpZUxJHL4fBK+9+\nt22UoCcA/yKsfYL1y6yvH1xlgtoM6/baXqiRqX3cj7wRbg3zKpyNJvE63oFi\nPjjwL7cEDGqX1dhm1IG2sHwAx0ki/T1oMCWXSJ/uzlu1+GFGQJop7hswU6ah\nnnXcuO7HME6Wa0YwSpgHA2uxBOpFm9MORg8eltbcVCQFaGnMJcejLrKdo2XJ\nuPZ8zPsBHnQ4OdhOOx06F+PN3ooMnVrQG5Eol53yrPQJwRaDKVgT2tvTwfMw\n3MaNplIEXHuvYLaEJpdyYDM4EM9y7kHmNb3Amg0Cx0F15/4fP3Rs0wWlLAiG\nd6YtfR2xFxHQ39JSYGEToWkxZ0B7zqCOQ0tkrUfMjL31Ht9mvnr9J751L/DF\nWlNO\r\n=tqpu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.15": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.15", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.7", "@radix-ui/react-primitive": "0.1.4-rc.15", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0911d61674415bb8a2cc16ceec8102fa82682396", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.15.tgz", "fileCount": 8, "integrity": "sha512-RtZn6Kt4kYUPE5pL9ZrEagcKLVQdgzU3o2KJpYRSNGmQ29ir//xgl8I6W3Gv5YbsDg+UxQipPdFJrM5rmOkmZg==", "signatures": [{"sig": "MEUCIEQPOAUlCac8zJlXjmWUMV+s3ClBxRIPF/EPWbvR0dRNAiEA8AwAjR22g+/vFbbwVAgj7hvz2mzeSbS7tIPcie0UJfo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiC/miCRA9TVsSAnZWagAAihcQAIQ+C765lptefH42IIdk\nJFluQWuJ7UAryqJke4OUbLNG52dF4+BYrRh8i7hcQRE1O5wYb7ieAuzyJSCo\nyELnAIkNWXB7Uig+baratiBZeIcN2gNvftn/HP7Cn8EsK/RMD9YczOu5jg+1\n7Zo/JzDRSVuvfj0VvGgvDRd14awQInj5p44wPU834h8bowrwWebI5YllEYKO\n/jY0lnAMPo6jmg7VVb3ZLIKnjF/mTgzJ5tp5ihBhFnBnvBm4KCdgmgAXHPPz\n+tiTk8yp/dIL9rjkGlfxlhZPO5mrV8JGY/BlijmWwqPZPQPusahu+wkjA7zd\nXcruWxolHuUB8uKhu8YRdsC8dL6lLTHFxT1YOMAaFBCIzfb6oVcJsmWGSlyQ\n3pMreYUj9lZyQXcD88k1J3GAZxnSdVmJ9iDJk1/OYIsCZuz65w589z7X1uW+\nrv/nljP95cQHmdAvndDSI2zVGrgqn+uh+x6JPW2tT1TtRYLwLNzQRbAv66Lw\n00W2YfCm+l/evOg0Uzoex6kRVRvcC5LVFKuULu8nOYcfkXp4jfLLa3yJayFM\nZFR484KJaWzWuFtlS0M9uNrfbVpDeJTb+8vVBJzWJOCbFlgKGe/iaztn8owf\nWPipK6HvhKMqQmKXAKKGoMPH46vqFV0z5FiPtpfziwerMyUGF0Wj+/+KTpfh\nHXOl\r\n=Rv5N\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.16": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.16", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.8", "@radix-ui/react-primitive": "0.1.4-rc.16", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9f481a8e65998cc8c3236350a0fa02bd3c8b207b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.16.tgz", "fileCount": 8, "integrity": "sha512-v+1bHhODovMZ6DtOSx+6kpDkRHLOC6JcmGfVba3OoXqAgC4fj24Px6uEHuH64nTe2McvQTUhQUStqb96fbPq+Q==", "signatures": [{"sig": "MEUCIQDkpixOfO6ObKbXVkG0sa0NmmTQD+Nuyu002+DqGEXKsAIgMG28KDbwKpWxXpKORAO8lVbtXqWPtUmpHIQ6p8E+IzQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBG7CRA9TVsSAnZWagAADZsP/3zIGb+pJfEHCojOwRhL\nfHOCLhOb3o1sRQ8GoV2Ilc/pXxc71erd1KDp/rYjXjouayX1If1vvD1sGVYk\nmIzt39t8NarSzQbWCrMqHhbepBtxbTqJfRwi2qjlJHRnWYCsl62RDZuD554d\nsMKwDxBDkAVvxDe3N/6/PsgvY+F+018rB1Nwn8wrYr/dlX2Hn5KTQXRo1IL4\ngZYCLmawdc83EW0G2th4jA/TW0ejreer5v+aH0jeRc1LD9jALgvwunLU+Nps\nGFkMlbMvwGJvuZ5sxKZtx38KTfUdBsBWWLZWgOKfAPkr3arbIIJWCgUBYxjP\nmCDyeyXSsK6J7jNQ4eWjwXdH8V9CCDfPXpsNYhLSzkLo5Zu4Q55bttfG4qmt\nNcv90zBzyeFImlUnnmJVFXdr+ESUtu+0kF2lQCrvLmX9rnCjryEbLidK5kcP\nNLe1TJxxAn4AqT/SkD1ADKZjrYh81V61CypY0Z6lArkanRSedrWRO8TtoE7Q\nckyorP9gi0ULLsr9hfjth0A7BEaZAYZiyOInEXwYpQZJys/amHAFqAic36+c\noQjo/6azxlLQVMMw8EZjUgpx4PcHrl9ZZqvntxrSjIKhyp3xXiHVAAn/pBYp\nl1uQnTp59VQ3HgacuCDBMy8pyihXXluO+whWXzv0ZyfMdrh0UYVK8CGM4BaN\nWZYa\r\n=HB4Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.17": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.17", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.9", "@radix-ui/react-primitive": "0.1.4-rc.17", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "719870c275b37ea7baca4b65ddcdb15d3053952b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.17.tgz", "fileCount": 8, "integrity": "sha512-GIpGnBgZ20P0W07QjUBRbdLChbHbo/saCe0gtnoWdBsM3xzL3179qC9LNDLgWytxDlgy9SFYebxIANCW8WFYeg==", "signatures": [{"sig": "MEUCIQDmiPut9mt8e/PQqRmJ+AfnHoF9O3o83ACguSApZNDANQIgXs6IWy4wWT3iN2dltyP35mxpz9p3jk1pZU119DjyCUE=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37957, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJiDBXCCRA9TVsSAnZWagAAjZ8P/2cSzIh7UGuF/JffigP9\nGeAC8gTnfjEyWlAVrSn5yniyvrvJvOqop3UM2aicx3ObCji1yVt35CbRm7HF\nVTQZnV15TG72MKEm9j5Hf/dSmOx5ulmQlTd6Ya0CXK23m8NcoLbMKnOu6oPd\nn8lksOXMI/2HeYYf84UqNLNSeQwXu13ysZSDexWbUvRHwxr+Qu1Bp+bHiCuW\niXS2s4pfzfVM2YAaH7EWpJHHqgCyU9Y5d4ql15m2ull4kQCnFfrOfy9ivCjk\nUVfAisb6FP3QQFUDWnjioDq08qEaGWVyKJNdKIZbUH19Aj004bPFRH51jtbv\nuWvW4N1RDHrC+5FuazKWSWrNpLXUBu+mYEXDMsUVvj8pkIq2qxxApuv+uKsL\npWq3bi06gb6Ef7dIxuI/DiLgOkPcfMSiXRZtSOjgRmtEt769M1JAElsTxMQA\nHvr4BfK/ekUqkp9MbhKXP1cOA44aWVBWHaB3EZMgI4og7cdGIfUce6hPxUWL\nT+EFdCJi1wHCIcLdbJWeYxAaqj5PRgD0tag3aKDntXkM9OVlXGubr7/VamXz\nbC5NoBrEuMPB8tCO4pyNoz6SC//0lRRxh8JWXPJE+ewfgXWtF7wKBCyfv2bB\nrQlmhs2TGWkgJJ7WTEzNqk7GIPmfSw5eHI3XNgmnbKFwKgESAafnkCjwJYNb\n0H8O\r\n=QZdg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.18": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.18", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.10", "@radix-ui/react-primitive": "0.1.4-rc.18", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "0de6e578ba31f631c5afe3e8dec926bc29be6e99", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.18.tgz", "fileCount": 8, "integrity": "sha512-HvYrtVy+ge4mD99JWd8hyJhek76RL4i/XokVpWcBcfsNsXadFNOU+UiFBA8+ifgjKXSV8PsGxHi44+pfIaLNeA==", "signatures": [{"sig": "MEUCIDKfFvK9O1G/OAOCC7a1BA3k9mOdKAhzLaywOo3i76FkAiEA99nJxXe4cRVIukltxMhJuncM4jxGizveznaaPOy3Yn8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiDlkyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrYLg//QZbSqEkjuHlm4QDaHevmyG1MdP/6gL9j3W4tMidtYJ4k1UIP\r\nWDgInIB3f7UHiZ9zeeCRun0euunQKy2/5OYDt9p1fQc4rFuXYrOlJTkwt/X/\r\nJ/hUpV+yO7PrIVhDRUa/EPI0/+PSJBEg9rlXTP7c4FEnaIRC1A7QQVWNZZ75\r\ncjq6sGCHGWgnU2hSKRmcSXima19K6MF0lCxY5sIDpw2IzXjEjltGQPgPsgXx\r\nULeqdRo7kZp9wLVaYdHZLGOnK0CSu4KnzVO36ORNtUmO4phURsEL7JAemBN9\r\neMFdrYwQycezjJ83peAdGfUwPlDtvLbRG2uSQjCDziUZj65pAt1wqOaLMnwg\r\nBYuzpk4M6YM92ZKzZaR3GDQyVudjCvjsxyaUtnPheOPiLjh1NtHw5lIYS6/i\r\nhUZlavbW+wOXDZcocKjn8yUOuFEMXwjhVQmo4078B1uQ1zqzE+DHlPce4i20\r\njsOoohaWTL105Xly4QPPy2nUV4gS8SsIFB+Zb3312ObH9gWaC5CHecBsbbce\r\nAxtfJAK+xmv7bnaJmNvz5SH9U41ST8qbJ35GIyjPBLUDHdK6Bq0ouzPZh6FH\r\nfpjf3N3/f1SWQu90c169Rwl6CVwz3uUBaT6ZiLtHJRGBE94+He2Nn+nsUNHj\r\nvP//U76+nZbQaKZQbQ+PXVXCt36DKcBx6Nw=\r\n=tleC\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.19": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.19", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.11", "@radix-ui/react-primitive": "0.1.4-rc.19", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "9b495e086868a960dfdee792b69f8bfa1635f82c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.19.tgz", "fileCount": 8, "integrity": "sha512-20zX2iWnNGpRsgD1ULuEa2iq7uYIFI724xgWwW4KwwA8TjFrbpMlXIQhKttIRyyAmyQVkMFJwtpeFtl/+1D+2A==", "signatures": [{"sig": "MEUCICNa06wMS0YYvIJyWfN9oZQV434VP/K/aR5HImkXZunuAiEA1ywlOMIBgvjEad30x/GvJyVhZJvcBcYaMvZCd5fsWes=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkUUACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmotow//VYXJZQiQ6dknDhfdqdq3lgxCoqZE9Bksn6mTy2d1S2Y3SASM\r\nxvEexXecSdaygTBJ7OAOK4JLN5IH7wI6gZMOAQCPUzc2QDgJOMTgK6MghL95\r\n5Eq861SPp5wD//l5ia7YdwWjJk7YN6Th59Kx3wLoAQrRTx5onUeq4AyImmjI\r\nF1nZwr6YHgv/zBkGtOinWZag81AGgnyo1/So6abAvWr68vzLgeLZgQl+XP6T\r\nD/yP1yAXaWWv4+jYZHJy7xWxwxocBj01ntY00Nt/MP4l8jjsn0iMr/4HIRhW\r\nrFpWzHmylVs024uDxTdu+YdH34RYCIE0ZB2njjVSNksi696OB5knvF1uL9Fp\r\nK5Lc6ZGcBJ1uvW3Yn3HNih18D+xOoO3tWprK0qLonBA7S8pgKD4ORWzxShHh\r\n5ubNIM1JrPiSeYhFilLt8GJkeclm878YHPCZEcWUkcW99BjI5YKVAPkOoFq8\r\n9QqHwACs2dScE6w84S+bMlPwWw+MXUl/oS5fsp0yXemAC0Rs1p66k3eXLwOJ\r\n+89jh76Rr9X2bJO/MJQwtaYzVoD3AuxNslbiveIffx6bgWWx8fWFDlvFWiSt\r\nSctZYv/i/wHSrb8XLTnYce3stC9Y/e7E3aoW6P6m+xqIQSEWV01P1OS1WCoj\r\n4sIrs1DXSLsDioJEEilXon2gstIPAPOiqKA=\r\n=Eql9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.20": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.20", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.12", "@radix-ui/react-primitive": "0.1.4-rc.20", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "2d09e51a585878e65916fe2ff91d0a612aeec3d5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.20.tgz", "fileCount": 8, "integrity": "sha512-c9TRRD7gom6S2JUN/x44K1XSf0qWyBGK+Y5DzVGI4E50cUeTrjR6Dlc97Jw1J97MvdkUnl9mgkvLYth7s+eaUg==", "signatures": [{"sig": "MEYCIQDASRejGNPbUSqRSjbUarZpBtoyRwL24WTNRIGyBMoO4wIhAJIR/O78CXvO1kL6D2ESv25heJzG2GIzUPavPuChAUq2", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkciACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoQvw//aSBrHBEEnpTFGMzUhFAOH/AlGhN00lzzVjK1z270SD4BvzsO\r\n0pJSZTMbukLfR66GvX/zt2fHd+czZUQoG90iUlL1cwxGbSQBejqUJHLlS5yw\r\n0uJzL0S8GUHL0mQri0B6QiOQQPTobvYFCGDDxFTDWmWMM3IvzMeLMZVu6EYo\r\nq6TAWzO1UrDSDEhxn5Gv38pORFoCXfoy0ZLmoVDlwI4V9o7YzfdSG90TkagN\r\nxOWnoh/NjZUNpl1MYbMjRTqVblmpVy6b5HmFvF6N8jrpiP6Apx/fwcGKDUE8\r\n+ydCrshkLy/bB/LNLu8cDFZAseTWIpjBmcMNhDGD5OFZu4Chq4HnfRUFwLh2\r\nxZVyfXxLe8gvAQTHprG4dNAHWNRiKTEqA37g6tciW3F/M3rrnYDajaVN375W\r\n+NLG2sDSJQQmeqLnEwqcs4mHAgNZfmBcrdKU/NbM4qCuyGteVeJmaVUkcjXN\r\nB8p8eGKd+RmZ4duAI5jHhCk95/r8RZ7ulOfqAg81mtjKfNxJKOYJwSpvijPd\r\nMZLu8FbEhLL/yHi4e5Min3uvxiMUORCIM0+GprcORsdiifHTCE5Dbo17nuPR\r\nhKWSNCWTyAn2rX5MygQYsCX9OfYU3GiD/c44XaUoDst7tjZGoDXRrardXW01\r\nK5rBd+xi9lC4BlSDEN8z5sP83WJvljxrhzU=\r\n=F63L\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.21": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.21", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.13", "@radix-ui/react-primitive": "0.1.4-rc.21", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "81dc9ff0157223a22d01e13fdeba63d484475c42", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.21.tgz", "fileCount": 8, "integrity": "sha512-pRKbTXKlRSq6BHqjBLIiR0BqQ7HWxe5TNUF84on5AomVBv9yY9Uq89W9wtUNCDcABvOi4QSSqoYwIufN+zSeNQ==", "signatures": [{"sig": "MEYCIQDkuk92J/t/kSKSzZP7zS7fkBYMbXCx0tviE/IpxmWp2gIhAJX171nT1e3UQvuImp0cgUOG2WhhbVOraYpuQMss0qHI", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFkyrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmokug/+IVZsNv+TitFLOUZmsRFgBXfCfZQGd2r/SoqtUxKfDa0Xioe4\r\nEa4r/je3K6oO0h2XqWq24qMZldKOROU17YbWNgqj9oD9AVOhH3vVAajE96Ny\r\n33hlKI/mOLsWMtabGoMOMhtsWVqJif97QOjSjCA/LplH5dJXM1Me53aIWAU+\r\nsEi4IymD1HWmvcLcdUl8cTKt9XTOZURsjPV0wFUkMFN06GBPapLz9N36R/pM\r\nLttK+QHdwPQRWUo09CdVHpP9lvTRdMxVjzU7agDE7btE1dHkj0KpwUDg6ftH\r\npFRHXrz6cH7uoFnCIKrrGrJWTHhEGoIYhtZM3TEYA5EPHSrvvWkCMEejftEy\r\nupOTo+QMUxcW22LDD4wUsWazwUGPJTGm7IrdxkS6QUz3fVP8RnzBGTsR9ezn\r\n6PtDbWTNvhcPLa5UBZXTDm3UDGY6iTkY2lkDk9OdKNkvH3hSjCs5YQwJEHJT\r\n8ju4ikPFMyDnl+efDDx8O3JPrafI/pyK13Ntbvjk4RyVauJtpVzFNgF5+Pxb\r\nOCFHB06NCNd/DZtRr0n/d88i2SVlssHYktO5UsaC2onXr4RTpRtpr7NwNN+F\r\nS+G3TOvEJd8l+1Ws0ESgc2KNs5ZZ3mopvNp1Y8aMGT+ocmkjQkreRnRxsoul\r\nNLnG3l3VcZa0pOeOn7z0cMlWh8BwejLwf4s=\r\n=Gqv9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.22": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.14", "@radix-ui/react-primitive": "0.1.4-rc.22", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "117fe3fbe4b61b721593bf7ccaacd9b03e1ce26a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.22.tgz", "fileCount": 8, "integrity": "sha512-hywg0a2Agbu5uTfK2lL8eZmgv1zh0ZBNrY/Se6H10XXWD2fczwyvMDnKBIj6FdXQ/JcJCZXikkwHtf1Y1no70Q==", "signatures": [{"sig": "MEYCIQCzonqLvYE+oZOtsekGDSN9Nu49DPTAqCp0DLMKeaXlMAIhALT4jtZir4IyptpcxPhsPMkKJcmmCso1CcX8x9VmzyWQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFlNNACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmolgw/+J+5yaTZ94XR7tszRVH840RvEtoJoDliaISFqPegQxzfwCEiM\r\nHHUKlRB2xYbfRa/IignDXP8TKjIvzGZRH4BF7NaLJEjFoS0If0WNAEsoZ+p8\r\n6Z2PaRHE1X1psrnXuunGgM2fiL3dDmhIcVABgsomxP/N7oTHT5FC1RmLzbM8\r\nGslmQ11ooaFBgUpbR6H7BOrEt5YseoJU7+9Irdfc+H8P7rNy5mS0jyFpt7xg\r\n00zp67QZg05RWP500KU6UyNJnsITiV4tGz6HXi1YalzfrCA24o525SCVmEEt\r\n3bCljv9XbTxOHdUb5XNDD1BmU0WpWpt4y7xRNOZa503T0y4lKTWCHnpH0pgV\r\n9k1Ytn5deg0VM1uRY/4CHHSWElZROQmKe0V5sB66zes1VTETAgMquvuyWHy5\r\nqtEzEx+bfkd3AeEeBDqPD2/XEqToziETg9RGbJu8j00LcjzLjSg0SeI1wFyH\r\nqLTXz4h1aPJoiAqmwlBB2MWegCALyGbxoLIJvuy8zAKzIqtHvjUfh1A11ErF\r\nVqDttMQsKiiFmP81idwZTgebxRZQ75E1FNxJ+P6EkQV/v9nQ3iJtxFeitGTF\r\nmyjTRcI10APsYayNjvxg+SYoOnEn/fcmkpLZOdpkCtuIMS6VuGU9eI1Arrh/\r\nnN0wdqOYcyp+3UefyGKLNEn6xLIJtNv/eGs=\r\n=KIX5\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.23": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.15", "@radix-ui/react-primitive": "0.1.4-rc.23", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "eaa267c97ec05cd48a722a09bbaea3d5f2e588ea", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.23.tgz", "fileCount": 8, "integrity": "sha512-K5iPVTnMRKLczeRw5MVzCixuAD9qzGDBHtLk6jRLk1nwbjKWZ+rVY/H9MgwGP/+IV0ev7j0PGZ25me7/tF4L5A==", "signatures": [{"sig": "MEYCIQDz6K8IHZYS0vg4qyTY0yEsYPe3GaXQKnkQZbLvX+X6wwIhAJ4fb4EwazXmxQXtgkMM82UOwbzPqdWD5KIKBqayIE40", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiFpDEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtqQ//TotqgczbCKjll3VXU6fGGFjrUpQav2UBu5j/pM3/1kvAE/dE\r\nBkfRKX4bqUfDcRS06bfQcaOi/yPB/jEkQHHREIybRC58g731nGMR2uo7Pnlr\r\nQxdbbIySCDMcJ1YgQ35BUXXbpzBey03WuJIJxksoR0FVfwz74OZ1OGG7FYv/\r\n2amu0Y1r6p2Bz87p9oS/ADO/p23vIKnqGP1lvNMNmG3A/FhTSsFhBrpczz8e\r\nABJIXwBCLSbCWIvAeDEepk2rdBQd6OAS7tHUT77h+lBNYvZu1Y80sWJRB7oB\r\nurG6jarYjGZ2T4nwoA92l7AZqtbo9uHmjZwHAgwPj/YTLgPJJm1zTgjLUMfW\r\nXqiuvxGklrfWmO6R53UEM84priyjJB0R9HOxr4M0wsOKKJdQ3FNoSNz9b+dN\r\neTvMglm9d0WPDVR5IpfGlWbdOKNrm50R29T4WuAU/QLEU5FeaRWjckr5A0yd\r\nq5Ens+M2fB06Y7eHqrfDyCoh0ENW/BvrKOSzCnfm/+M12QvRWWtUGhtZmN60\r\nh+w7zgWxKV0PLpAtXrTpGsdh8Rm0Q6ZhNUQM6uHleQTnGUqb4D72BC6/fgBx\r\nUeRP6ByFwj/oHse5rNU6DdXVI6P7uMPPRQfWIGFkQjMq3pMaJ8ia5NCsj0mx\r\nWDpU+CDug3gZ03NuxiY9qSidPQqKvS8Nqxg=\r\n=qkFE\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.24": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.16", "@radix-ui/react-primitive": "0.1.4-rc.24", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "199b46a8816540a9d1bdc28953226c56f6efc91e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.24.tgz", "fileCount": 8, "integrity": "sha512-MA1jpgiQlOmwYm5p2AFny+T6vDUGj6iKMAaxNd6KlbR8pkrAkQx21hcEXQxq4L44EzEX1u3paLqFhNM52GoRJA==", "signatures": [{"sig": "MEUCICbBN4Uahsy+LEdaqnZ5SzQbwe8z5parFX3TA8QEUMRvAiEAxEqxflNi3ouOiu6q6Wznt2n59V/sWI6OSI3DYX7sFI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF30mACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo/xQ//Tngq7kTsLqqVdrIKzF9E0uao56EG/WwwdY564qOSEYK3S6WO\r\nlzejq4n6Nv1RO9mBQhObmGG/lok962TBR4k9HRK4AIE6fmAc2HntvMBwyqty\r\noKOvq1p7UnO/zIgNECZ9+ZV3h9wLlVRchpPmXQCmVKK4hvUk5nzNRX73Mnt6\r\n0VOmQwgdLKk7lHHBnCiFweXAiiav4b/XHPKlZL5hS0SJ1nfMe/IPjkGPUncT\r\nsRjCclE85LjlSJSkZbsrfohbu51cvCQ5nrzXI7CQQcBlFowaohdCsEOnNhcM\r\nRs246itNkbQMBT/nFAx88V0tHcSplN5+v4hti17mUdnvBLi8AZF5KV3ifIB3\r\nxh51ZB1ncA+LxcLpan5osr4xVQxDS2s9lgEitp7WEOKY+IO6QO8hxNiBcmy0\r\nGtY+3eDAbXqaJbuEqrnLHvY+36bzhqpHV4ynYSnc+kv8DBr5U6XRdmAJuvPR\r\nxpHFJ+5ac2GG2/SBrMzXMfz65QC/own9QQA4YkWdyvsu7dpS1p6OJCcATt7g\r\nxK/15f1/2t8OhVG52rOlmsPO9GOjh5ew6v8XbjqDMDUPGGb0ZXfVcMCO6g0I\r\nE4J2lldgtsOPQm/JiEuN8eMk82d9sM0ePY8xjN+czn3ldU6+GqP5YXM6Dw/l\r\nPim8jubD/VRjis1xzDkzjFJOsiE9H7gCb64=\r\n=DUfq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.25": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.17", "@radix-ui/react-primitive": "0.1.4-rc.25", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "30bd66fe929fb21bb66acf0c286f4a0904676671", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.25.tgz", "fileCount": 8, "integrity": "sha512-TD+uqJNx32iLkmMcatWGByY4WnuVo8dSSv32L7qPpjAopfwVfWyix6IDEWADIVgSYC5ml111YjVmq/0eb4kN+Q==", "signatures": [{"sig": "MEUCIFR2T6my2yiPrIMKwr5idKMv3LjfkF/mFKAp8RUpBxAKAiEArrgud7RGcTAvgOflpFEnjvddYztO4Hcs0P4sWbXjC7A=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF4XLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqIZRAAik0PJ5qRlpF2XncqlYYi0ZdRijM44RdqJyWfla0tu/KIoVfE\r\nvQNv889rldcJdkoVK6mfBbzsbalOLPrpYnkuRQnsP+Aoh54XpVVsih3SV9HM\r\nFEgeRao7HJl/2j8lxU2swLV5T46AqR9hZvXaTjBh7ZNUNGmZrcRsH93/Wepj\r\nM+JYWl1dJUkPYfhBxJyS8Nm42Sev6lGZRm3E6EDp9nG1kovYJZRRNu63pfb3\r\nRTUukZSUvnNIBvnAG1PK9NSZe9I+3lBwlb/06ZA60QTQqXuluBy83bQKrtWs\r\nzKZge3Me3BNt2zjaLQuiUDbP4QX3AZiGsFUFWmORpKIC8itbNTPBMVYZqnwB\r\nKoWsmq9MhoKxMCQT7919Sb5kw/RaQdDBRLXDemQY+P6hoG4qmTpdJMI2QyzX\r\nOUwEGjig/yqBA/BnJWCbytwBEu3xfOGXlxHQ2fPQlukERPKKObg8GOADwP5E\r\nNEV5JUr7J7k02GE/K8QujtEN2RSI+52ttAKZ4z/6O/veOil/KPYOVuOkf1Vz\r\nG5usZzDnYvscXoOSlh1oTILdIXdo/VlJBiL2WhSj1AVkwLZtFC/dQoNsaOw0\r\nZh2Axc9yTbicMgrldvoZmncjcDYY459RkdMqo4E5vIOoDnaogIUr7+IjKa1V\r\nhB0i6CO9M1UYdq/OAXAhQEhtZBeAx4Mizs0=\r\n=EcYr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6-rc.26": {"name": "@radix-ui/react-collapsible", "version": "0.1.6-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2-rc.18", "@radix-ui/react-primitive": "0.1.4-rc.26", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "658c8557c43673aeebd1aa9e98792d2af5e99242", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6-rc.26.tgz", "fileCount": 8, "integrity": "sha512-Tie+qzyRnL8+4nGXd3N1JqS/Wvb9QslZNHSNsghw+DfiHB05bn8Oh1Y/gup3HxgSTxVHqSSjeQa5rEGiUPHyng==", "signatures": [{"sig": "MEQCIA0KfzTNBgo1uKiHg1UZN92aMw8Uq1JkyOFpP6FviUylAiBHv0TlgpvKZ7+jrO802IAdOncntK9hX6l9LheIANJqKA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37958, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8ZGACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp1fA/+JUJ9xv5YDk10nTOaRP7zZjr59ZbeOLJN0MCXeXrGhNV5xtYj\r\n3iTZ9uUXRUmsphqE/iz/kfR8Ntz0K4SKFeJVximgtfWNO2SCwXDXnzJCj5K3\r\nkNcTae7UuTiDbD9njeOVnx38I8b5tDyO/mZf5o0GAck+TBDF5Qy2zzF1FTGa\r\na03oqEmYWlCxW/ipHZ7SXlhtUpOsXhZcB6DkoM7XI+wVuOmJK3iDWeXY70sX\r\nabciMQLg+XzjKSYPr+ZL2IAln3HIvxKlQ1Gs3AyW8emqop0mUH90nKWl5gHe\r\nKXeGKszgNwZ6BxVq2H0V/bzpr8iU00zoVs3RwSPmkZZYzjXATW5dptr4RFkZ\r\nNMin17kDELBTpLT1Ui1j5e53cb40LQf55PX4ZSniOQEnaXibcMyQDU4gi5w3\r\nb2ctN/V3Zmf6qi07tQHxE2/aH4TVTe8ZXxOV82uLqXI+dQDAlEVF7ucBooyi\r\nzgH7LTYIPXRBBjf6eMpW4MNqgaeNj7uTr3xr0GP0ldv1uFntt2vsPXEI2mZ3\r\nkGcXxcqlQ4WiK1MC1UIFnq8KhRlp6HNWnd7ow5aQujbJqozZ/eLEsseSltzw\r\nug2UcDr8+06ArcEAAPizyanpLTtcNmeVIFB/ICppo9ROuKDxKle2EcBPOAtq\r\nam2/JtH8f6c0VSs+HonbZlEY7lXJ3dWZyTA=\r\n=OVBY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.6": {"name": "@radix-ui/react-collapsible", "version": "0.1.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.1", "@radix-ui/react-presence": "0.1.2", "@radix-ui/react-primitive": "0.1.4", "@radix-ui/react-compose-refs": "0.1.0", "@radix-ui/react-use-layout-effect": "0.1.0", "@radix-ui/react-use-controllable-state": "0.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0"}, "dist": {"shasum": "3eeadac476761b3c9b8dd91e8a32eb1a547e5a06", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.6.tgz", "fileCount": 8, "integrity": "sha512-Gkf8VuqMc6HTLzA2AxVYnyK6aMczVLpatCjdD9Lj4wlYLXCz9KtiqZYslLMeqnQFLwLyZS0WKX/pQ8j5fioIBw==", "signatures": [{"sig": "MEUCIQCoXBR/ipyBW/DgI1nwslPkWQC0dIcNJrrzq3cthCMOwwIgCx/yi7NeKUknndbAOCDl5xTWMre/Rwdauff9EWbYjfQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37906, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiF8kCACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqbnhAAg2S7W7w7M6Akk57krl+4cSrQDnj7bJWHW3OC7Q31KGLVwtjf\r\nT+H7Xwc4wiS+ier3KSh5hhrHJ8X3VxwSqTTq0aH/xL7a+gnUbHLgV5358RQG\r\nlbX8R5XHHC7ToBtxYzUy+dWGcMkSjt2uu7Iv+t3+XlMpgCx2TN1LiFX4Apk2\r\nuVnCYumEFpu/2afqvYd7m9XmrJ7YRiDKbXu4fUx45lSsQHGF7Av1HFwrWeZl\r\nYgs/6MaLAAqHYKdFjTlTtXtJMcx3GoKC5X9dAICWW/fNLE/M+K6fd0YTVX3c\r\nktvpQ1+fbVeFZegc17vHsqrwtN3eAEJZL+VnwxRw30mI7W+oyjw2AzzPbljF\r\nDo9Y6aDgeWUEVnbgwPTSf8fKwcM9TvcI+RzVENSixpxEdgRuAVpOLk9hMQT+\r\nkDR8YGkItZaYHaB1pcAAn8iTCP/2P0xEw9/jZblraRL6qwgkScFTzJPJDpsH\r\nEGiU/ptuhoBHYWoIGNGSlCN9OVSTawpaPeXaHzAu4cKFnqWzfFrmemN8zG/v\r\nkd4m38bOPWJiWRw6AvNSlyIotu/nzYAe9FH07vQ90jN/c592ashp5SmyqbsI\r\nqWvvDEjRIQjKQz5IuRKyh9XfwzHRNOHLtFmOFQEsKstB64eS8pety0jrgxjK\r\nKqnZCejO/nyd7HJP1BrgZN/MO+5Xes9Ygww=\r\n=I3UM\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.1": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.1", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.1", "@radix-ui/react-presence": "0.1.3-rc.1", "@radix-ui/react-primitive": "0.1.5-rc.1", "@radix-ui/react-compose-refs": "0.1.1-rc.1", "@radix-ui/react-use-layout-effect": "0.1.1-rc.1", "@radix-ui/react-use-controllable-state": "0.1.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "911809911800ae27cd55a3be9a24739a11d6dc5d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.1.tgz", "fileCount": 8, "integrity": "sha512-X05PNYe+nivA70njTUCNlDJ6x2XANxDvx/1796J8lWob2unDf3nPWOCRXHULKFA/TASlNplzsliuIU2iMK5SBQ==", "signatures": [{"sig": "MEQCIDODl9I3QcE66TW3QD1DA8ZwbeiLuP7LoDMW0M46qbxiAiB0vo4ejUIynveUY9MG7m5Eyqs7e2lMG4MiMhw1TvV5bg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWAPxACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrZww/+Omdwzh8cPCnZcfPTK8xRMvXeDawZq+JH+VCYxYpuuP3O8i6H\r\nqWktpa1rmsgvrUkYWi6XWf4yL1OA5VcrRDgniJVQimNCNzYsmS9nqtWUgvUC\r\n5SjO3sz6LcYDrazRHwVpULgnte3E6TRjtrjSQBcLA96IimjeMOcvhrkTKa/1\r\nKEVXsFRTFgCtum3Br82bmVk+ZOICPdD9Ze8Au/UoAYXzTdTTNRKEj56tOahN\r\njfMny8KPYQRsm0Z5e/qQbeOlfymWwcrjTMwMtdjXhj3h0MQGMKIt8cL2UsYl\r\nbu1EmQtv3VH2zoDjwTDav+JPonEnJEnb972b8ykUsjvZIEf6RRRyKo3itXEA\r\nQXuc/UC0eZb6HU03JqgmBLRM+qrdakZjt0K/VEKvx4kWnsPltY0v2Jhg0d82\r\nA6sFfzXlbkCbI36xhjlXVm+9MKOjOaTN4KqEso4dN/d0zeq3x5+HWbJOjCyb\r\nf8QRERTH5Gzy8xG1Gn8XKNmcH952942X+zdyA5U/Za52qp7uPvB88pQhxNfL\r\nlu/4Qo/3nuZcoD6Sa9coPz0xm1IZ3R+kLsPBdtbJjUfT28QVfQ3FspF1aEaL\r\nx7juiHB2meU3GfCuqKjqpY0+gdjqiLu3hA/ZrI45ADjOy1XmmZl78k362HBv\r\nOTckD5j4MHTgLdsoDwKPGYUduCC2a1Ri65o=\r\n=7iV7\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.2": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.2", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.2", "@radix-ui/react-presence": "0.1.3-rc.2", "@radix-ui/react-primitive": "0.1.5-rc.2", "@radix-ui/react-compose-refs": "0.1.1-rc.2", "@radix-ui/react-use-layout-effect": "0.1.1-rc.2", "@radix-ui/react-use-controllable-state": "0.1.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bb4eb7ea803d5a070ee8dff8dc22925ac9cdd216", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.2.tgz", "fileCount": 8, "integrity": "sha512-5Ppjslqei1gBvcJEq8LvePKa6IX1YXrkqTE46NCQskFUqYZERU4xTJDRLa9Lf0hRZErDDxgidyTfSOcBd48MKw==", "signatures": [{"sig": "MEYCIQDvseUg9d+R3zTS8JrgAqBhVZ++0JHpsCuMX12W11lt6QIhAJdEjNNxlQlLWyVwqHphjsls2ryOAwKgTKtXUW60m+Vm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 37983, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWCOgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTzQ/9F8AVDXM9N7P3ztxfjJ/NCBGN+JdvBegYNFCVzytNnuln4WVx\r\njinPnNkoYUSBhZfhhAn7niHOWxzFqNagk0xu0ifvVLShrzbw4Fq/Rn992Jlq\r\nI8DzkV3o1A8E9UXRfT3tFOk9cOCfkwQxoqOAU6FX3h9qBu+U5tL+rDyh0Woo\r\n6utGfMaeftiedtxV1EbLl+PLSwtKn6dMhAoYsyxnnEJ6EetuwyWN21dCNVF+\r\npgHwmvmYohJ4uHXtBo8gqPDANJU8S8bf7Zspeo5mRQKR42nrWIvcW9s807/+\r\nYzv+9Qms89TBI5wB3ehPrHLXWxT8bKR8ZBdJ61SRBoEkKCKc4ZhwXUAe9BPf\r\nnBp9JrfmfOGBG9M1nF5u6LzgAANYVQryNvkDXZEZw1Q0PtjY1bbxMy6PdYZf\r\nsfySn2gk/7I63xxNZ5xOdqB9kJkZF2hJgT1i6N9rVpbG0KFIvgmb6mNk6e2z\r\nG06v5OErSdFKXIePIkr4DXMzO5FWOQ2TNiSmGW2m+06uqCVF41BA0JpBESLz\r\n7PyG8D+b7F6oObSpNWOERVQHLxN6pI94BfgzX5LB6G0NytZuiNLFhBMGhtr7\r\nxYLXGeEM91ibmFakvedlEODGcd67ugXzEtQD3n6HDNXMUQc39h8JKruXwiOY\r\n7N6Nhe/geAB8ERyvv5BBJpRlDk+xHK4ab4o=\r\n=ddix\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.3": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.3", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.3", "@radix-ui/react-presence": "0.1.3-rc.3", "@radix-ui/react-primitive": "0.1.5-rc.3", "@radix-ui/react-compose-refs": "0.1.1-rc.3", "@radix-ui/react-use-layout-effect": "0.1.1-rc.3", "@radix-ui/react-use-controllable-state": "0.1.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "84cc802609143154dc83f3a2ffbc41237d0dfe30", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.3.tgz", "fileCount": 8, "integrity": "sha512-vaxJqWPB8CgccWDRqUrPVftYd4V2fSAmr9XpIZRWIuTppR5z22GZksBkLLmjdAJC018qY3kfQ+LD9Z4XYyporQ==", "signatures": [{"sig": "MEUCIC+YyUUNKGMZSB+8y3XsfTgsxip92Vyy3E5gwbDpVgPHAiEAn2bU0OcvlmowDONbOT9aQkSIYqbzNi6uiK7veTKEYRs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57370, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiWDSuACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrtTxAAlLnnMlSxvtp8v3VV63rtrQFharuVrspflwLCY7oW5YvWxN3y\r\n0d8RjxAWHLf07FsWRZYCMc/JLAwE74YYMdSa7pQU+m4zHq9HQ038u8bYasdb\r\nyCH3k+kI/+oW2091as7X6xVDZXp+hoMWuNqFmXbwPaXOaJzwDEtpCd5et3pt\r\nMBe7iaRbIQF/7R5LZgplmiRJAD0ZMYiopZuDJ7IzratJwxXC9BRLO6QKv7tR\r\ny84YepCE/cAPt7YKyJ/B1WlGAYMrwKrWoV/tCTmuIWsqf5CBZ1UpCAF/nvZE\r\n3R63i44f70C2ZMAUYwkYEWYHZWRaOQdElqYgNXkICzK3l2q/3hLBhcAY+BOU\r\nx2THbA6guigStOW952Bs7onvD49Mb79wkUQzdQdX1mhEAYYNF/45O4xemFD6\r\n9wedfgI5BKSe+TAcFqPGt0K1TlFR593xWbjHTaU1fwL5BwpBc61Xotv4QKaJ\r\nEC/zWi4IUJ393fMbv4c0tUD7FxE/DmGca/kbuPeez5eFILU2j350Y/boI4TD\r\nQvw11XrTOf1v/VOo3Q6dHZjqYdgg9p+AVw37S4bhvTST3kEYhdwdZfr3D/02\r\nLTRbeN7ClXLUDeS3kvYEwCZOspLmZ9TIP3NnRIK++PcH02xK1SwCISpngF7C\r\nGbfZmO0DxMDSsczzjqtrrOtid5he2wRy+DM=\r\n=neoY\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.4": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.4", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.4", "@radix-ui/react-presence": "0.1.3-rc.4", "@radix-ui/react-primitive": "0.1.5-rc.4", "@radix-ui/react-compose-refs": "0.1.1-rc.4", "@radix-ui/react-use-layout-effect": "0.1.1-rc.4", "@radix-ui/react-use-controllable-state": "0.1.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "db967f8a06d29348b5774de0bde6a1e374d6df4b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.4.tgz", "fileCount": 8, "integrity": "sha512-GylQsMgsDh9abn+8zSkFFMWb8taAFWIh+G1MoKvDgZKzGEfedr04SANsWe92aO0YywQuDyd2su2kTfDQ8IFdjg==", "signatures": [{"sig": "MEYCIQCKPNOCZk0dAl+bNz2HlpT3NIHy2PM498h0E+wOLDrALAIhAKTF+o6YEPJ/balyyTz15AOboGE/1qVJQV5YHE2FB5GQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiaRrPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrvYRAAjOo1Xo9fshjnvnRCNoluCjxdhq3XbYmhVzaVt3OambohXsbo\r\nD9+Ip/LhEO3NwymBYQPQ2UzD0duusS+UeKVpjAvb81IYVewuM+5Hr0vUdHh0\r\nznBhxwcUAkuprNYXJb4nEHkZmVPtvc42YVOMxoLqKi9PTLhzjZOXuQgFFWSS\r\no+sbT20CHf1U1KvS1yz8Yh+X+K92gwTWgaSUHmFyshlLnG+TK10/F8pcvXmM\r\nj4/Xwg2HvIBpKctlTVyLF8qzlmc8P4B+1GnITVmGGuZchpevhs2j3pmhRlkC\r\nV8Axm234BENw0SJfx+qsl7ThLseMtRKHsi4ys2sfGbUJbYYXKz1R946Drq3d\r\nKipXPU+fw4U5GyGnwifoonGyBuv6RI3AWhyG8Y2fAjtgtFYuVjWrUQz9M6EX\r\nyTv4uNYPuOzTLZc+H9a6vUvUrDOsS+abg88il5FxDuYGZ4W+RXf10IPopzSE\r\n9tJ+hzIkbxwtv7/NSmdmOSjeVRTTUGfbNf+M4+F3V57jc0LjhymEuhLj4hxw\r\nZOJc3vgqTb+DqRdHVen9xDnh9inCufd9dGzXvw63D2Ia6zb7gN28wSFDma8Y\r\n5tyvyqzdCv2fGx2ExeDUxecpNErpFc0/MSxUl0A+Y+M0Ps1k9kiYcRSfH3a6\r\nxcGxgO3q+FE3ont6nhwjvzI5pJETjv7mIcU=\r\n=Ppe9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.5": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.5", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.5", "@radix-ui/react-presence": "0.1.3-rc.5", "@radix-ui/react-primitive": "0.1.5-rc.5", "@radix-ui/react-compose-refs": "0.1.1-rc.5", "@radix-ui/react-use-layout-effect": "0.1.1-rc.5", "@radix-ui/react-use-controllable-state": "0.1.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7f70cebf9f68358ef5e3339d6966ad4452b2c4c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.5.tgz", "fileCount": 8, "integrity": "sha512-QcCAYCOQYzA361zUm78fLW/q+bg6xcjGOGfDCAgEqa9TwOOlSndwtfRss2vj+PFQEG/aAj0OyrCMTmuRdoapOA==", "signatures": [{"sig": "MEUCICHUnf1ro1S2D8YheXS6ThClV6p7JoZD3L0wsfnY9exmAiEAuFQt3qguO+spKaYxtgUd3jc0oidFwZQA5dNigMHQ974=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiapgJACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrsLA/9G4eEaRcoKdjL4KSKYsPjX0WrmBxCZhmuqU/BVuENiihFoS+l\r\n4W96KfpVqxXs2V3ypUW6t/x3zDfIXwZxJuK+2NphU8o93v7Edj7kzE0II7u7\r\n8rCDGlKmxDNCUOwF7rqpBsu7aB3Ew2wCc5ABw70V9eeVtdnGEh2dOdyNEl6w\r\nKCg0lzFY5SBNuPKfg/y1I8xHaYY8sFz5R/TwWXemdPWWnjtsA+iaX90b2amm\r\nRQxGRuiFi/1v2EidTPJVEpjJRilzrPwiQNzMJ/Q4ygB0UNF/YzfCWcSsVnfL\r\nbpfd52DWvTfoGAqKphQPq54UPv76RiUuJZqS3nu7v7v1TMOpdyqZP3tYbIHG\r\nTQrwBg7189MIMXaaskOtMU3R+4u711S7sWvlYimNko7d84QGRNYc0Uvdyjcs\r\nMHL4oFWaeZRZ07GzTiPobjKyAxJmALAVgqIT5yj6u903vvj688+eC6bPuxB5\r\nMoIMfi3C2Rup7SjikcdjSCjI/ppRBNTIs4KbcRIAhQR6Wj0vNcl14koGoVdq\r\nBFxnVRSsuQbP4U4x+5Q2xTXduIK5uhvc4IBP+LGfAdU+FAajj0/ddcGaO94J\r\ntvYFCivNt7w3oQbxh5mmaNN75kiFu70e5aZ/qZNYH+k6DWaoT4IO/x5ROirV\r\n9nYwjoVsGb3oSvl3Gx+qxmdRlayCsA/P0K8=\r\n=u+/w\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.6": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.6", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.6", "@radix-ui/react-presence": "0.1.3-rc.6", "@radix-ui/react-primitive": "0.1.5-rc.6", "@radix-ui/react-compose-refs": "0.1.1-rc.6", "@radix-ui/react-use-layout-effect": "0.1.1-rc.6", "@radix-ui/react-use-controllable-state": "0.1.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c4b639d6ffd011575896912a4b68f8ed3c0dcf4e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.6.tgz", "fileCount": 8, "integrity": "sha512-wgYx1ymRfgQj9LFtHm8/X7k0N24pH9RpGuU7ZkiHhRGeoC4LiqfLouBQ8ebaRNXSFfrIshWKrxLf8sn2TK1vjQ==", "signatures": [{"sig": "MEQCIGZkpX64kvtXEu/GlMiDFqqGzUB37n70l/IZIUZRpdf+AiAgparzFYI9tPKFaOLfq4yUeer66RuOiYdnDPzNWkiquw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia8xZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQeA/7B4AVuAEahAKfN/ML/mxrbpYO1rh1uaNulask5nOD2cU+vZ4E\r\nmAGavXj0RjOqkAGRyiYRpDUuPC095dKCG6CbbBHszumkXrpifwOpbhQiwbt8\r\nLjYzevrj0UOOA30FH6qEGZqUt0wJn7WMQih2H3RgnYGt6vnm0tmmI0Ua6nEz\r\neMQDVYY/y71zoc5MsiIR6zOtYfJpds+hZ69I4pNM7C9/8FYu+0Tw25qQhcgO\r\nGRabPeWamUhDmy5mJ0cfFDTnz0ZK2nP5Hq2xytczoCt1g8W5HeD9cOPn6D8F\r\nSDZB0RxU/UfLDquRj0Ol6j5d7rd5dbf53hbJr7EaZHFtsjEy6Z2hdKyRENZZ\r\n2jxWzNi+OYhUYJXd48V9EfX3frU9RkM1Q3V3E/CQ4lw6Tl4QU/RNEXbrn6QL\r\nJ1iYdIxF9N+RqQlaeGlFPhRKXYbA3tersB/4uoAy+dYiaVE3eUahw54iICeW\r\nkJHG8yjzxp15pC8a/5a8qMLIVLfPnnT73DWFYmpKlDAa7wa+PatY0RFBiTDW\r\n1MIO96kLtXkvOxXGzJR9P8/FIavwFCwot+CPox43phWiYW1Rtu3ffaNjQpNO\r\nci/x77S7lSd7/EtN+SXGsnUeS3X1zDfuPRN7hE4QQJuNurTKcmd8DQku68E6\r\nPqpIVLKyvSNx2q1vReOgfezYUMnvS/Oy9MM=\r\n=H7vx\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.7": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.7", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.7", "@radix-ui/react-presence": "0.1.3-rc.7", "@radix-ui/react-primitive": "0.1.5-rc.7", "@radix-ui/react-compose-refs": "0.1.1-rc.7", "@radix-ui/react-use-layout-effect": "0.1.1-rc.7", "@radix-ui/react-use-controllable-state": "0.1.1-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "33e7c935cf17fc2ddb83bfbbae0598d08deaaab3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.7.tgz", "fileCount": 8, "integrity": "sha512-jbsIQdY/H3VCoIpi6jsyfVh+mYB9bl6Y0habBHEpMwNZReJ4Eslc3Mx9YRQb0QltP928qP5RlqSGhM21qRYB6Q==", "signatures": [{"sig": "MEYCIQC/CsYPem6C0sYSyOgAnoCAJH4NK6/3pVWEdysp5RuXCgIhAMfOjHeEIUMPsVyyVAQkPdMiUvPeTphoY5lSXdccHcyU", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJia91QACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrXFA/9FabblpL/xtitIvXtWZN0UVm0Uvi+iPD5YMea896aM0lYUPos\r\nTq/N8MP9eqPXmS4O77zQKShoPXgnojWXerYLcR9IddYDbZdjjArK8EL2OAwc\r\n1LWLdet3y7DE7GeGKwHaH0P+Wxhh4wnGckkgK1x6wWdN2Q5P+N3krPPCaspB\r\nUm2Go/7B6nOr8XO2liMuKdc8LWKP/y2vwV5DZ1gohaFIoK5GHq3Y17mu8wqC\r\nBHYnlLvh1FN6DiA6r30eyRL9zSETsWt4miOpAdh4P9/73vRIgvUxZ+p3qwYu\r\nMG5uRysey749uA6Ju51TKJDzbhupQX2s5liGN+YlnC2Lq+xkfToMu0ekhFaw\r\naFCbKfofflIEQCB4m38gjrTjuSEwi8ERZ/NRofZMGqI6KfNQFfs6YLrz4r9O\r\n//tWzZv5ucZWCjbfxs+1oPUzntC7BOHWixEXsuyJuahG8AAYceiy8uif+wQG\r\n/CHJF5SOSiqFJuH1mhKTdqgoRGjC+F8N6o3HgLZ44OTLncbGZBXheX7d5LWR\r\nwLWU/Is5FA362W5C+nhsLE1W5e4h9S+JnVpScj377pB/UuIUr8WFSnCjiU1e\r\nP+NYd6kD+0QUl112MTQKNC6abZeIIbPBnvhvuS4YR1mKYEOWpfLEiOKGreby\r\n7rQGbXmiLJY5J04HSHjEPCxIfbF8ouewGc0=\r\n=T0Cp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.8": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.8", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.8", "@radix-ui/react-presence": "0.1.3-rc.8", "@radix-ui/react-primitive": "0.1.5-rc.8", "@radix-ui/react-compose-refs": "0.1.1-rc.8", "@radix-ui/react-use-layout-effect": "0.1.1-rc.8", "@radix-ui/react-use-controllable-state": "0.1.1-rc.8"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "dbce2658cd73be577223e8b65034d5854a109c7c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.8.tgz", "fileCount": 8, "integrity": "sha512-H4qrapt1hWcnzjYHHQEy+VCSHQxvjFiqvHblYcM/ocx2z0jYDTs+zsZCca25VhPbmCHD7+EjDhGZ/5Wop13kSQ==", "signatures": [{"sig": "MEUCIH/xAIwW3YvB7QjDp2JTAlSPwigo2Xz5/t5a04KdgYFRAiEAwiO3JBRJ9lHU6BJcqEcXbZwsPKBrJZZDg1HOuaUUMrw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJicVhpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmprpw//RTVbyHCGAaclzCnqVjNJ+zEOOzijm/iWmGfoHRmjxg9Epgkx\r\n8BvDeF+b8G03JN+4kXAufpHpqAqwjw8SPz+a49uDuDCTGl8BpZdZC+BXgtdu\r\nWq0DRDb9p+JfLAYbDpYkKCGsowRDJTaX/9VCPfswBnnyGzQqeI0iX7wjs7hl\r\nVh8jrB/s2dccy7GpIc8UbT1RwOkGJZSEd4cdO+mzdt+a0NBOLHPMUH5ltVGe\r\nTNDiCv2cXbuJs2kvrutY/020rqCBnCI9bJi3zrKAoHTFcIv4mfV4V9oBPfkV\r\n8IvVatnBNYNMhPHgWQqZ4oJF3i1ZHRHTo2i+cmI6vhu7RQz0sKKI9FS0qKrg\r\nRTNQ4jUs1MacsY+9VI/e7wP2vCZP8vJJC9gfGzvK8uAna6MmRgWlucPLyDrp\r\nuSmJUpJD2po9o5Znig7Ml1h+Vqg4bXs+HzAmBkY1WJ9xWQ7Tpz/FCYwEFyT/\r\n+xCa3A3kywvFT6gnxB3PxXslgRT4Vpr6wtisH3v6JjMFbTHddsEOkiZMqQtO\r\ndA919JBf5JOkHoFFyTsr63X4DeoPq8qqnZPWtm45yHtgZd9c4gUnVdf/hp4Q\r\ns64WHPYFL9A6twKy4lNVp/NJshCYUTpTlxnPqUEG+r7u0trc17XPFk/bJuq7\r\n7lI++bEe1hnR83HFezAJmV4H/pd/A2auMPM=\r\n=P959\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.9": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.9", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.9", "@radix-ui/react-presence": "0.1.3-rc.9", "@radix-ui/react-primitive": "0.1.5-rc.9", "@radix-ui/react-compose-refs": "0.1.1-rc.9", "@radix-ui/react-use-layout-effect": "0.1.1-rc.9", "@radix-ui/react-use-controllable-state": "0.1.1-rc.9"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ab5417a30ad94693ea351142792cd435fcfd7c3d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.9.tgz", "fileCount": 8, "integrity": "sha512-zDo21F+AUFoZqYSCDlnjgyAYG5vsnqNQ1McuMFTIeM8WL4ktNjAmNf/yHsbsyYPphfWioiw5EFNgwI+73sw4Vw==", "signatures": [{"sig": "MEYCIQD4/meqk1ZfnQDFC4dRs6WxbnOVMlCySSym4HJLgLOcjQIhAOfGO3k3qfmFjan9KlvdpDHms7P9o1DfDGMg2CIEU8GQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57414, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidNhTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpPtw/9GGWNe8VsIsbQKPnXLezjPlqQP2yoWfGy1Hw7m300YgtS3MtR\r\nEYwzoyGzbE31JLOcfqbCDWepXliZ0a/5pGIhWrQ+1m/5oh3J6NqV9ivsdOeO\r\nOeWLRA0wQT3Jicofutn/tDAcFzjkKe9QCJ67FSDxfpwUBoMmqsYvUI8AejyZ\r\n56XFq9/q0rSq3OhXc3BbqPn4i8SnsYFDr1GIUOh3GjhCOQOew4NwdEwmuYHr\r\nus4i7uD2KEueopnT7n2uqTfEfHcOWIzRBKveCNinoZxGP25nwYmCTcTdkivt\r\niE94Rcl1m5e+jXjDNMkibUN0QWkwEEbvVEUG2KAouOXs7N2gciTcz9A0IyqX\r\nMasTPhp3LHv6uRCNwApL02rR0MgEN53OI263X0JZr4a4Kz4eh5LNxtZSFY9l\r\nDx7Kas5rI8I/bOSw5ru6BTc6yb+o5VmWJgERp5vjRASTIiqV6vGt3hsHGqxb\r\ncHeV6o6tkrnB56BfHElUKPGzxaqLrfp0NtH0u8SGrAtQsi5JBHwLSGEMoVH7\r\nNiD8kkAyCvry6EpatLCBkGlHQAB0j2HrkL0X/pLYwXiC/U5I6JRNT+G2wPHh\r\nRT15UrAo2RxP84MDep2GrH+I/eQxtu6eYbiNP2HhvkM/A8K2iBlgMh1NSkg1\r\n0bZTwKcMxp5lNhZdGvJczJyE4JmtUxpApoM=\r\n=J05b\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.10": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.10", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.10", "@radix-ui/react-presence": "0.1.3-rc.10", "@radix-ui/react-primitive": "0.1.5-rc.10", "@radix-ui/react-compose-refs": "0.1.1-rc.10", "@radix-ui/react-use-layout-effect": "0.1.1-rc.10", "@radix-ui/react-use-controllable-state": "0.1.1-rc.10"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "49ed088696528ae264e560246e09e4d1ccd592b8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.10.tgz", "fileCount": 8, "integrity": "sha512-aYRfn/X44EaWKv9jMOepsUK11Oq/WNrs/BqHSpYVYmoiZuHZ9PYAmne6GHp2ziVcA03Bbm1RtqvgPpwv//rb9w==", "signatures": [{"sig": "MEUCIQCbsnxk77I+Z774bC2ZK8+ZC4gQtwiVML4XFvIgtfEeIAIgYIFTcIAzwzq2am764DTajCkvb69TUq94YWsS4sPmnQo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidN9zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrp2Q//d1zQKq0j4qRk+5nJkMipG0v5d0N8l0afwrN5Lzk3wCEeltDQ\r\nq9Bbknmg+moy91BLhQ5J06YEEOInlSP1BRWHaP7hNIOd7ejG+7Ad1EChkKX2\r\n0orLmxX9CDK5nr4/9UFeuQrmou9X5Pu8suTvYJV2FOWyhg24kVZfLZx4a9P7\r\nJm2OiWP15XN12fANYN8ZGnAlhXweVAiUZ91OTf8vWXPCk1vbZEt6cXm/sjpv\r\nCWlKO8y4kdGOffW7T4tENYd8rQz+arm2ZYgxhV6joeUy2mSerddv26pN8TIj\r\nGgWRwo/UR4MyclTdjfdYA3Wph/WO5MxcDHkjekJKeW4ASlZER39XS6QQGqmY\r\nmVJpeJC7gNTRhbr5at7eVMlGB7ODCV4ivjYyQHC25N3f2cakJ+auSjevnKYf\r\nmx5fuk5FDwPg/tmnA5TMgJIXPxQhGidg+mrrstTOYK214puLqMb5Dn05vHA8\r\nA/24+JWhAWeguTW52DfdpUe9CFGtd2XJFUeMiECCsfnYxcfOsDG+zIa2Xfc7\r\n+YusdSIYhF5xMB48ZwmOp8zqAm6FI3nxP8vS7tQ4/ck56RPDfxQxzLQLRu3I\r\n26gqsHYqKHTjPXoNTJwJ9jHSR3huEgkz/3tnko6X0ilhhlD3TrgD8auRve37\r\npha5BZDSRsXG6ZMdpwvFDHnygdXCBSkTmsI=\r\n=xs7W\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.11": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.11", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.11", "@radix-ui/react-presence": "0.1.3-rc.11", "@radix-ui/react-primitive": "0.1.5-rc.11", "@radix-ui/react-compose-refs": "0.1.1-rc.11", "@radix-ui/react-use-layout-effect": "0.1.1-rc.11", "@radix-ui/react-use-controllable-state": "0.1.1-rc.11"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a4882a4541527833ab2d9276df72393dab98c84b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.11.tgz", "fileCount": 8, "integrity": "sha512-PQaQGKKAaAP+FhAyoBxSZykj3ERF7uqAjrLu8bdBeYwZgEQDbkOBoYZ/qWi84bm184VOgVXldCmWy+boYlbndw==", "signatures": [{"sig": "MEUCIBh1g6VFe8dHIVzL7EISjQESr+w9KIZLJZMcjm9cpxMUAiEA9n3zpwtmrI6ELLLYitsdNaZvlVv4pz/JiSezK4Y+u9k=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJidSkyACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqliw/+I/Nr60naWwf2Ew/IqsdzJTpJgXNXe9BBV7vVTB3ZVUFkWscP\r\n0avnTdo8U5d+1A/piUnAq05G2TOg1Z49G10Tg/Sq3XZB3mwGqOJCWNZGhiYu\r\nBrruT0f1k3epG5PsUBnRFDJozr8izs5yJRb2v6/5dVwKkKQcRzUYNJ6pJPLN\r\no+GJrfgNbnW7kwYzWQZG4+BE7cNTwiM5C28y5Ae1h7bixkCu2w2+045lgAlc\r\nksHZCjU9ksTDQU0yfnuSQReeaz2lzaoq2SVtdpHwLxFCl5MqKhyqL4UmtVhO\r\njSxNWDa4Do0ov/Yp+IiPmsoSP3TSmuokx6puonTiI+o0Fw4ec0/jtCYmktJM\r\nOyXroo84GJb2oBnld0TKh6agvv/ctQFU+qIXYdhKUqXVsyjNdzWNIY4xwZ7q\r\n0i+6QJCe50Rn0HzyzKBpyJfvTMP5JY71S/HNvYLY/MzATfglaXuUrN2QBbio\r\nkebPF1zO0wWBCyg4eGA9UVDAZVhdvIlxlVyQt2IeQtxbv34Q4P+Y9FMzSPeY\r\nDn/J8DZesFYnTsxQS+DRsltQwrRkGC1LJTxOwE0ms5P22hv11XtAmzikzacV\r\n9e79NUdWchKUSTzAdHEfNtnrjvjEy82M/gnT8OpWqMivx2TdR1y/mak6zuz0\r\nukai1jKz/gpzY3KQ0Sumc/iVOjf9inv4jDk=\r\n=EG0Y\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.12": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.12", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.12", "@radix-ui/react-presence": "0.1.3-rc.12", "@radix-ui/react-primitive": "0.1.5-rc.12", "@radix-ui/react-compose-refs": "0.1.1-rc.12", "@radix-ui/react-use-layout-effect": "0.1.1-rc.12", "@radix-ui/react-use-controllable-state": "0.1.1-rc.12"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f56267597074d84a7c763254bc6b880f2bc46901", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.12.tgz", "fileCount": 8, "integrity": "sha512-FCvZJaQ6qtPoRtlcX3YPIz8hsK6MnBx8eCkOxfTE/a8U7gXjWYWCRxoYqR85+2rD3quQn+LIv4k4T0WB0+I2qQ==", "signatures": [{"sig": "MEYCIQDNq5SFFE6pY5Zb5H1d5PDCFPMGT5+ldW6HUMZM07h8aQIhANV3xfWAvhtaCZGByIfcdzO3rsBAweDzdcj9a+FHW0yk", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJieofpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmru7w//YCh+hX0pAY4oOPVegSVHxnij1hhyvNeTRsURBWLiAwKYbccL\r\nhdoCdrGAXdvzGS7zTHk67opdVgWSgmQHICNXkCiuNUwziv5N8W4Y3kZZVjc4\r\nC40ztf1qAzPHuXlZcXB2RzpySr0cSjVBUwQPbx4hwxv0KFT53KYeGUVmJSnX\r\n9w2p51W2gCSeCaRrdu3vjc9Y+dh/qmyzfMTFOqdeHOLITK/1ugmViOoyc7FI\r\n1A8FkDmg25igzsjM/xcMqykmQRDwBp6Uhg2Ig2oR+1XFPnUBr4G4Ua8DiHh+\r\nH5k3QdCKM946388nTuFga7BSOq9RIQoFFv3ttJKB//7v+XbTuMomtaVugQ0Q\r\n/YffDHs6ZUzEfte6OM7WnUEhs8/HedGtwwf2YAK+xxJxiCcHKXsOtZJiKoBC\r\nKyW7AFDQJ5dV/OjG8RlrwZkI1C9/kY92y4/cxpD6h1/Fg8bNAh8HtZP6EN4q\r\nXiXm7AnClblEc0P4VaOvxeTC68jEGpC7sUdykvRqAsZNsZnRmKctxQ+MDNSO\r\nVfIhZCeeYM+RUqWTNG4vii4ePOlf32FGo9Tnq83vYQrT82UnmnLkrShY9Eu/\r\nf2rtdrA/U29xXxgHfxCybeBcYnvMnjatJ8S3F2xQiuU8nLzBqF0UNxy/CK+m\r\nc68Z7V3plsJP/cV4qsyVnEe3KqwiR3JAqn0=\r\n=AV3d\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.13": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.13", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.13", "@radix-ui/react-presence": "0.1.3-rc.13", "@radix-ui/react-primitive": "0.1.5-rc.13", "@radix-ui/react-compose-refs": "0.1.1-rc.13", "@radix-ui/react-use-layout-effect": "0.1.1-rc.13", "@radix-ui/react-use-controllable-state": "0.1.1-rc.13"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b784283ea95828909fb62f6acef01bfc48ede092", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.13.tgz", "fileCount": 8, "integrity": "sha512-iDZI+DM3GbSSm5kJ3PMNH2DQEIVr/P1qp5USGazK6uNVe+nQvN7wOMbaNFKay4zMmucnpiYnUMBU1nwiQWZ8bA==", "signatures": [{"sig": "MEYCIQDDlvg1b8dDYSanDgmLLLSwKA3qPhN5PqDwk0VfRQExfAIhALx87zw69f+DPUxY+O2LShBY6KhKiFUSPUNLWr/Y09Fo", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiepIwACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrc7Q//aY5ly6hgegLE9yX2ULcv7p8WzfFtaJcJ0bi9aGv89T2v+/4T\r\nrYZ1Cswm1CTb4Yk+mWy8QAyo1qM0kXC+Znbk/P32859MNqJNTcdaPMy8mwVv\r\nbA7v2EXD3amQ2noZYHItsDGVNQd9rXhhzuyeDDdmzP+rMEZcVVc2HH/YEcG6\r\nzkq+fd/nIRFPAuKtlbTHQeWcHNU6NU2Sw+hlDXj1GnnziXPS+3hmZThRNkYu\r\ngVhNgFXhMxpFV6lFxj+A1gFKWfezBGQhkmwwjCpgdhci3GRLQLYkas0iQgv9\r\nu3opE2+ZkZcyUFeqRHuipIO8QFfuDBLxDhrlFPMi3XzaF/XWU1ZjqKfhbKta\r\nP+AUXaR6pNztcojnLiajWBHTc4koP6LjEBwmhtnyT5OS8NPNF4oX8ZGz+uws\r\nctr1wUv6jEJti66O9NEINkywRHJDVMnE+7eCBLloQQ7Bw+dFF6QR4sEqmthy\r\n+qGAy11BX/8Z5ZYJNHdupvKJhu+QG0g5OLmTegUAtjpdFZRGOYWR2Zvfhd9/\r\nwmUd5zxpic+Tf+lDcZcNrENUcTUqz6bJx4cOExsXsHVtP+aamwImJDL0Uzrb\r\n+IVvo3y/x0hw/mFrqISPZmBfHUKCDQ+Ht3vqK3vaR/EXy5zKLVz46cw1sCmr\r\nZOrzK6jmgGPIElxevoFhvmvJHAYkEFsyGCI=\r\n=8h04\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.14": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.14", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.14", "@radix-ui/react-presence": "0.1.3-rc.14", "@radix-ui/react-primitive": "0.1.5-rc.14", "@radix-ui/react-compose-refs": "0.1.1-rc.14", "@radix-ui/react-use-layout-effect": "0.1.1-rc.14", "@radix-ui/react-use-controllable-state": "0.1.1-rc.14"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "433a1a20f2e164d720c5a37bf3d2dd5bc7c7a993", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.14.tgz", "fileCount": 8, "integrity": "sha512-eYheARtmMPCKRWPZZVZ3KAw0vyQOxcE47Ct/I/VlPAhrOb49MlO3OC8MFvG0ulX9XqUjstBUdRutufVBq/PUdQ==", "signatures": [{"sig": "MEUCIFJyyg/MAda/xYonBjM77IqMM68vXyEAT8zr61F+Fpv7AiEAralodRjuDHkIVUUtTyu6qSk2RmBJ8pqcPV+76geSg6s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJie8pIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrfvw/9En4QyOTiS56l91SLzXGgBxEhJqfD89+w2wKVv1s+2/WQfQEz\r\n2ydAIMqboHNa1Z1rG5qH8ak0A6RcZRfJ2NxMq6F0TKsSP3u6JLHdjFVI7WmI\r\nKWWAZ8Bhjm1L4igbDo5LkwY6KKEhf+FsOEzErc+1YlT6h3tsVgQpASdw1RhU\r\no8RNt3ma6a8z7ZW/7zG4ogoxGFTrZ7dZR42PqWpKn9JsfQGV69x7fDiuD3Kg\r\n+4sXyLZlJTPtPAGUk+o7d/OkCO2AYxgZT7xLbgVAv7Je6ymF+pTgo7zpkerl\r\n3nHigqNLiY4546y1Oc2yj0OAioYWAIOUHqcjteuSxSHjKp06a4+VJMSkz7/s\r\nUk5DNfBWShKn1NzoYqVefoL+FeMAb/Tv+WGEDBCoognmBoqRUYK7isIDrPb9\r\nqYjWlKPcBLLUwi7s0D8J+BRnn7exAZvlHWnvy7IgNfgVmg9FWpjqok8ymhLU\r\n08xMAOigVJpka7NzF1+/U7L0+OANAjZZ9+DxSYHe4MTc7eeMLw4kHoY5/gfV\r\nEtxJs3dNMefUxDJxVlnQhzjgxC/Ewwf807L1XBvRyYVVE0hPh9qQO1xQ4Z5Z\r\nNvlJ3Efmb31EYuKtJLKMd8ieSADB0ck8WYsAlvQ6lPSjFNt2wDPYapotdexl\r\n5IPwAmD0V+sHTMfw8gACov/FxEJELowEk/w=\r\n=nahQ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.15": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.15", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.15", "@radix-ui/react-presence": "0.1.3-rc.15", "@radix-ui/react-primitive": "0.1.5-rc.15", "@radix-ui/react-compose-refs": "0.1.1-rc.15", "@radix-ui/react-use-layout-effect": "0.1.1-rc.15", "@radix-ui/react-use-controllable-state": "0.1.1-rc.15"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "48830a6ec05e865d112920eedf91ace7ab143c55", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.15.tgz", "fileCount": 8, "integrity": "sha512-XAwyrv99PE7wXiIDYd1l2zACJQ1/EANpkK9t90w/ie/bSUlrKyxKZtwT6JyVyaaznCERAdIvG08zEFbnHHOXsQ==", "signatures": [{"sig": "MEYCIQDCqAqXOlLS+1zIoZo+PFnzuNAFFZhy7ZKEjBKVSAlSYwIhANddlE84w+N8Ci1i48qeeXmT/LPfKS+K6Nqf7fsDV9IF", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifAzrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmopxA//b8SLqEKLWM8lj9HHg+4OX00IfjK2hWKymTZ0RJOhVWKNX5+b\r\ngowkSax+jT1jLrVRuiQ1/kY/mPPE9Y0SqQOoqql5VtgSUu1nBP5QYoMOpcf2\r\n2uSJ2fMeKHJRi59T110OC+/tlSSa4Mo5kjVL0gYs4+chdec/Tbdpayu3onhY\r\numP49oM629CJxlmV1BNpbN8e6vQTSrxUPp3/9963fgTyJ2V+oCZJfMFgccq+\r\nP6MPUWxKTE+uyxS36L1t2fBAHq2hG2L1gyBboMw4ANH4PJequKz8L3aqvsl8\r\noJgMez8/HH/G5DfP/UUmIQVIpYp6JQlL0avsEn+9vuuzWEd+HjX0wggWlPbN\r\nNJx0aYlagxlCv9ji2ltLKwiYnH9CRvJ4+iayIS284aryOHGyP9VZkOwnEmxv\r\nG7IjVwQx4dNbHRMjN1fHE/luNoHSNw69ftmsh8mSIHUkD78ixo/ABUHXy5G/\r\nfOBXfjFLtWjRXyQrxEENvpodKGZZl/40+MgXltp0L2xkL6xf9BJgXa99D7UT\r\nakkJrE3cN0uN5QytCLwFR7k5rh8Z6Izzl4IADcNL2YcYAKzn2cnkx9bgtze1\r\n/bcsCBjaTRwO6xRyreOpmFDhWTfBpZJ7ih2VrEhreHGHxHWRB0WpeQj074dB\r\n8AwKfPy8UxlbRtyvaWip+7Y5Y+FWktT7aJM=\r\n=7bNb\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.16": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.16", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.16", "@radix-ui/react-presence": "0.1.3-rc.16", "@radix-ui/react-primitive": "0.1.5-rc.16", "@radix-ui/react-compose-refs": "0.1.1-rc.16", "@radix-ui/react-use-layout-effect": "0.1.1-rc.16", "@radix-ui/react-use-controllable-state": "0.1.1-rc.16"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "73dfeae871125fe8d13baa73bb086757460c0743", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.16.tgz", "fileCount": 8, "integrity": "sha512-LFGZBwSIspqTN/oiX3ySxJOaFNZoZB3mNAXvZMAAjf62xTtG56bpCgqZDhS3QFp0GzNoW2CPdvBoWbrNP2wf5g==", "signatures": [{"sig": "MEYCIQCPgxUa6oPUs+fSLcuX+Gkcu2kh4oVn3/tlWpG8iRZcpgIhANdNUPI/PKd0Ri9A6i1KOQwEkoHL+Ind5PNR+M7veFdn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifTrLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryFA/9G9b7BqEa1KiS1KmdasO2nZVlf4XBr5lR/fH3USFuc4euHsAp\r\nXPUkG7fwPdJwu6pBSKxsviRwP0SPvgMnRMJe1cadKc/0RyWUuIpeEmuHeU7E\r\nalMbsXxyrdj/JyG4iCXUW+lbwBHPN3/St8qjKXjhVjMg40NYctUM1I124qvF\r\nYSferGBSKCmRZUT+ZOM3D58b+EkTn1/0Kda+tswCoIWUUq96HM0IlNoBn3on\r\nbxkRWch6ukbM1JpWgmfnQa4rX2qOVtdm4WkZNUVah1wHNlMkR+yL7biCGFjd\r\nbALmTQl/pBgHVh04/PsrCS7XvSuZtdMtttIjH5rUwvVMHwKegRSZXjk5z+aG\r\nintQ+Y/tBQmQ9JLr8P6xdGFRoQ7FY9URFIuwfJArMn3hIx4qbFLv34FYi1KC\r\n4UZNkSX+A3EtGVosN7gyZtWqA7uY+3S4Mm9sN+4NS+AzRXIx6PapNEKWqVRT\r\nsEkiaJFtrxWusrJ7UgpBHWK5ZmZ1dU83GA+ErY7UqhTkmuOJ/aNwNr3fUrvu\r\n9ryHK/Qa+DRU9jOABSgXYcy8SxyHqy+2Y0t5Y7gP1pe5gABbj3RbqvJA8+Gt\r\nWh0Jv2H8Gya3LYkLBMLQqYWfw81MECXdUl1dvtjdlOXXyMePmhTWv9Ggcpwq\r\n/G2BbV/5GRpKCwXPNeA8rqyfmMHdsuHIhHc=\r\n=VAAr\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.17": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.17", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.17", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.17", "@radix-ui/react-presence": "0.1.3-rc.17", "@radix-ui/react-primitive": "0.1.5-rc.17", "@radix-ui/react-compose-refs": "0.1.1-rc.17", "@radix-ui/react-use-layout-effect": "0.1.1-rc.17", "@radix-ui/react-use-controllable-state": "0.1.1-rc.17"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d5cb9a7a2ecd48e1b5bb8afb2884f65b9a0aaf31", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.17.tgz", "fileCount": 8, "integrity": "sha512-LF8J8r0kNA4dYP4D8kYyKXz0HlHsm8/gubG/X59ZiCgjSNu7+7IUbwhCwwccihIrH1kSj1qL7O5/HUEAB+A9qQ==", "signatures": [{"sig": "MEQCIEw1i2+4JD/2vKrcMJ4AdSEFamykHPxClPgNo0sxmxftAiB5xgwwpuqLjCRL4Hz0iMjwgVKHRfqABSs1b+xhLMlE6Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJifh0BACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrOoxAAoh0mjUTMOYUL5t0lVPFW4wjoQnL1ew7Rb0Mq73z7BOwgtjda\r\ng4YNdWUdYmfi5i9LwQo2ms3Wi4NUeP5K6b267apreXwzCuea2tL3RBtMt31p\r\n99RFhTNNgGWZU2L+onPU5u2WupOCJtTF+DXSnQAJbhlJ8/vGLQZLPBF/P3jf\r\nJeZDW3QAi6JlPNaR5GVkvX3TvNe6cGSw05A40RtDiv9YiIjsSMo+slaGC8CH\r\nSjgjOAuVmZTF86X+XuqDS2f7TYV4eXcAa3AZP7dhBJf2Y2BoDg7UXn/zG2UA\r\nmcJbTUZjNOTUyxhyrHw1At/16HTva2zwtP7YTUiUv/kqGyvDlmAvEx5Ebb1o\r\nb/W4W7MzcxQ6wdeO50OXTVdDybE/atOrjAx/wqsZg717OGhkHvPICxerYBDR\r\nFhgvxN/3KQWb7m6rkHVpCwB4VQ7kd8k6RpWMIR3Dld0oWEmvmZb3w73Lfeev\r\nd++J4SjWsXQP2XtR+djU4S/HK2xZEjmsqeFd1RqGLywxqf85PAAQ1ZkjJqLq\r\ny6BkVMqQIpVEUMhR8B6wtZ2sPDtOnMk80F2uLb+ufLjox0tmvezUsamSsQNr\r\nJZQnpnGqjqAkWkL8TvUZ3+xn1+tFua2Q1/r7K+MafmRc1Lq1PmqHjPcqBXNc\r\ngUoCHUN1JXe43ixYMRhhfKnhqJ1PfrV2fc0=\r\n=u3Mg\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.18": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.18", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.18", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.18", "@radix-ui/react-presence": "0.1.3-rc.18", "@radix-ui/react-primitive": "0.1.5-rc.18", "@radix-ui/react-compose-refs": "0.1.1-rc.18", "@radix-ui/react-use-layout-effect": "0.1.1-rc.18", "@radix-ui/react-use-controllable-state": "0.1.1-rc.18"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "feb6c9a379f8b01bf732d8cea9dcb6be607a1792", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.18.tgz", "fileCount": 8, "integrity": "sha512-KDxmc2CaFkG7RZo0m9f0zbNw70oR3pjFmOhUaBF2vJUw0a/Wi43KN456sSWt0BbF2zDX+QUiI8yU4QxI0WS4qg==", "signatures": [{"sig": "MEYCIQCQEVgsJp07Ak1xb8kYgaHLvQ/5Cy1g418Dna4lNky4pgIhAIfyp4tUDyvMHDleVVqHT4DYwSJttDWKuiC4Z3tHjehD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJihQzpACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpQRA//UWDNQGqFdAd5Mr0/2S/ri3BYo7dOskLmTv777EvuoDJ5Zj2E\r\nCDBAqZ10k8e07AMwLYwcF+k1Ty10LTSPooxXpPFu6nV4IdYNcYcEg1JMLxn+\r\nJspcgoVQVknbQqg2fLpCj/ecsJ6VsuJW9SoK5CjGnocquVx+hDEvMPxm0iGT\r\n2gjk4dja+JPV0cKSMJjzig8yCZqS8oi3/X0keRjL93YPEXC06WUSWLeCsouE\r\nvHoEP7hLNpWIJvis51+KfQKoQS1+OCQEqrn2LkhAXNiFVFjUFRSkC/TgCh1Z\r\nkC+pvdCDAol1zr/J1B4Qv73OtXphCPMO9pj+9OIlzSsmA3zOaZAnWfPizhjW\r\naq5pJRCW75eoyuIBiiRtblyP4SlTrXpRDAGm+ofvt2zE4gJ92vbJbZdwc0xa\r\nGr+E+7RrnevDD7g+KQPregXoqqK7SUvs+sbWJ9T6Viyjz27jZba48LrMMfBU\r\nKCknXSYMu8HoGjtBws+VExIya95JMc5Po5x7eiVcgHxN3ouGBCvW6libeefq\r\nm8ZMSsD+iM2V+rVKAbs+kjUKe0yPCq7o6NlUON/af2lVEcLfzpSNkoSHZ8Xb\r\nr7SYlYRyC9oFcswi5x2dwJsPtZMZKGZGhpvHH64mEm32nSUI7NoIz6DsG16c\r\n7ugUAoQo88oEYyYlhMwERxcuhchmwbHHCMY=\r\n=u02+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.19": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.19", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.19", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.19", "@radix-ui/react-presence": "0.1.3-rc.19", "@radix-ui/react-primitive": "0.1.5-rc.19", "@radix-ui/react-compose-refs": "0.1.1-rc.19", "@radix-ui/react-use-layout-effect": "0.1.1-rc.19", "@radix-ui/react-use-controllable-state": "0.1.1-rc.19"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a58f6223b9578e063da0b43fc064365894194614", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.19.tgz", "fileCount": 8, "integrity": "sha512-vPS62tg1cmbLY4hQWjVKxnwlSOlXTFDv+3STGaC2yrFFqLDk5s+o6X2KXrlA/FttWtEf4OOHK9y7N7MoNgYEOw==", "signatures": [{"sig": "MEYCIQCR5MYZEbG83KuXf+7aSM6F57pg6jTu9/8/RZ+TDzue1wIhAIwIWDyYIA8LTSwXEG6SDrX0rOORZBMGY0iE+4VIfBXV", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih2WLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpG8g//WV0bHkV/ji/8JLbfXNTxl+T4KjVmGzNeTXU4lpMXaJBrCBnt\r\nDkLWXLoHdsa+wGuai4eVj+d/fAo2t3Uz00Yfp/5+nNA/QPTmIFJQmtLp9dIi\r\nvl4/PubCj/HnPQCrvvhVCCsoiJufPfSYGMREdrheOUOHSc/KFB+tbABTZeHu\r\n+TMdB3KWPaeWPR76GSp2C/z+9C71kVVcUDiUN4OMX6QqcrPYpnVwE95wFjb0\r\nMo96he0nvZ6x0gm3RpZ7BFdAILfDPfOuCJ3y/h7vZPPipC02i4liouaT+qUJ\r\nXpyrlVeW1eDKO0d1pkDkFMGO5tmLBbHfWPubrVoYUmakoGn+rLrV3o+g0URl\r\nkCeA+uU5egygsOwjEWssrGpvxdr290B8HS50RAupEz25R+iQCCWydDnble+7\r\neo/xkLtFUrXaVuBg3hftznrU9AtvbqTgr7CMWxQtvzpnUJ7GTJiYgq4Epfdu\r\ncNUI0IsyzOtNwN2V/TEdudSrSuPKvZj19aMaqruHPNtNWvszOZ5eB270uRA5\r\nqGqv0ESw23udLArvq7lWaD6+MOcje1+CUh53Ho2wKxXFEn8FqsZ7WkUdw6ss\r\nO5a3+UjAo7uy0Vhf6Uoub5fQhifKhMj6pfzXJ+C2U+MEoRHe0eKKTT0UC+i7\r\nK2HJhhZ+PdQudmXjUkWOVOxjQUT3GLJPYK0=\r\n=HRke\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.20": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.20", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.20", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.20", "@radix-ui/react-presence": "0.1.3-rc.20", "@radix-ui/react-primitive": "0.1.5-rc.20", "@radix-ui/react-compose-refs": "0.1.1-rc.20", "@radix-ui/react-use-layout-effect": "0.1.1-rc.20", "@radix-ui/react-use-controllable-state": "0.1.1-rc.20"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9897be6c1c15e3d8b9954f344a4bd1b48d924fdc", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.20.tgz", "fileCount": 8, "integrity": "sha512-3cwVeAlrUK3antq55CE0y69kbyy89OV1/T7DZ9uYdbVOJrv9aA/bv8aMG+7dMoT7puK+ARKRvKYbxoLme7Fdig==", "signatures": [{"sig": "MEUCIC1oTrH+ax01ssWM91P1lYf1bk60I4DL8iLgzr4anyQqAiEAmzMcyTsmGFGqTlcx/AKxpzPyr2NqVemzeIq9iJetEF0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih3bMACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoVaw/+L1BavT7Mgc7zBJVkIXRj4SL+TnQWOXk/ujVZUn52ZSYjBY3I\r\nuYOYMzhUpBLv7/yxpW0f51bQgNwpQhhXk+zo0HunYoEqtKczyg+oz7NmWg3i\r\n5MZA2FzpOxZsmERB1Islcmmb6ec1wibafU532fC+diP0jCGi4fDWwetJpIKk\r\nUQCdvkh30Z8tNbeT18EObWtce/LAFosvn7PIQ2fqZqmbvgtrZyAFXBVXXBOW\r\nrF6efJsguvD+BNQAbJsuUp8CDaxjuunIEMXQOLC0ZlabMehowUe0DntaigE5\r\nX7oLL4FJlELz5o11Vrrucqn+6UW2akaNt9siHjyzhRYF+I0pTdtP9cayro2c\r\nXsi9lzui6cDD1sTGjmncE7fCva+Pk3iyebDJSRIX0G2qJtBLWaSHfRJnCYMl\r\nmME0dobDOZ4a8X0UHr0LAWiY8sdvYzHe4w0M2N+sMjwMH/rR2NRQf4RFhKNk\r\nNwPmnVteEYqCzE30z9XkWpxY/UdhngQHyZ1zKEDgSfjQL3Fhc0DSzdod7M1a\r\nY9gWhrtcAgMO1fBu94SJtrFD+N/+44Yc3SdtXB5UifzLZxkt5BPbe6IPuEkG\r\nygHxSG3OJ6gQEDl3ViWzeu++x/oFVpCpigocr2AI9NaXI2Znd7AcA5yH+eBu\r\naWK+rg4KbSEmZ/wl8OtxyIagtQhH4pBTD+g=\r\n=fHZ2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.21": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.21", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.21", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.21", "@radix-ui/react-presence": "0.1.3-rc.21", "@radix-ui/react-primitive": "0.1.5-rc.21", "@radix-ui/react-compose-refs": "0.1.1-rc.21", "@radix-ui/react-use-layout-effect": "0.1.1-rc.21", "@radix-ui/react-use-controllable-state": "0.1.1-rc.21"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "304ccb8dc940d3b25d2db42879a9c6e3d700150a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.21.tgz", "fileCount": 8, "integrity": "sha512-+umNEgb0BaNVmU99mPUyPExBtaSZSyA7VTqFN2mxocWkjPvX6pcBU3O/JyFREUVPRboD4HbKnTjEvvEwXcl8Dw==", "signatures": [{"sig": "MEQCIBLeqRvWwr91jKCpj/IRyGi1v+k+y4YdG0By2hVWFVueAiB3wIIWrmJLoFtdpHpHoFS7GEoO5byIAXvPCDqA/GPWLQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJih59jACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp97w/9EO8cZIMGpEbAuJrStX2wVJNI857D2WsuuiAV8+nnFXPkf/WL\r\n8Gho3fZU/vsWJ4/gzVmmu0nsBhIpM0tNEiITqfWHisSftvvnlweNieSeGS8p\r\neZ/bEFcQpeOXmTKSWxLbxz9hXsoh7xWYlJMBMynl1aMuLIo5Wbd2Joh7stEn\r\nacnILiFqH4qcU3FMjGsMELd1beXVyKLuMgPPQPWVNIzwwCEMeFWhI0nc8sSv\r\nVAhcBzWBlePlu/jY1yF2g6PgKBm70785Qe4IN9OyujHrEkCPCZat1WizTUlm\r\nIDOH44a+xVSp6DhfpTtvOo1PQPYnua4PneNXINOfkIJ2RVMT7lLCh5mWu4kK\r\naRKSUlSDm7562aE2RVCk0TSv78go77HpaTxLJLBooNCND3pIrKGEwjyQk4qL\r\nI0nxNwtEMAJSBFLr6ns/tdTGNTG2OCClnwEVvyY3NyqqUj31Kd8lHcgZdoRU\r\nE+/Pa9jEuJ0+fIAjIH3ifZA+ntV7oYpf85a5ruo7j7/2yP4IBAaJNaWSYWYf\r\nvo0a9QqmHTbFSabF96HOUJimfOBa8O05BE6kkDp8C3zyR3i3cgMZqGPmqGxO\r\nv5UWd06s2kpIDvdg1UOYKgmiP/6F+iLAmoKaIh4/2TYeJxfKxT+W6X4nCFu9\r\nj86fYcVaUJYkygupp/jv9SsGaf2s04pdBts=\r\n=efhq\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.22": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.22", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.22", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.22", "@radix-ui/react-presence": "0.1.3-rc.22", "@radix-ui/react-primitive": "0.1.5-rc.22", "@radix-ui/react-compose-refs": "0.1.1-rc.22", "@radix-ui/react-use-layout-effect": "0.1.1-rc.22", "@radix-ui/react-use-controllable-state": "0.1.1-rc.22"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "16140f6c8bc307607cd4ffdc0851fa0b6440d70b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.22.tgz", "fileCount": 8, "integrity": "sha512-AhFll7hMKbTH734xgqak6wmp8eA7bszkX8emMwtRN/Qseq8k18Xh33WdrV7NRzQgu5+FHBqpOVq6fprrgy9BBA==", "signatures": [{"sig": "MEUCIEfqOMGkuaa97SnqI/T7zbnvOnKDdNi90mTlq0XB0yAjAiEAlpQjJyPxZlWa+MlX7VlNc96iP3laPurQx9ChUqZkNCw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJii09nACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpe9A/9G2awan3f8d6kjscX1Ix3EpIVDCdMfNeYvFkO0Orj3f6bsPJr\r\nfYFFcjXcH/lv0BQUfeDfHO/GlhgFUMdBwE4ak82BtTRYy3leVL2pIgN/gTey\r\n2XCIPb/f9GJsyl3SEsym+0ixRPQJdkHFTaFLv/6i7wb1i4xFX9SrjCJOb5rs\r\nq86UMxh1HVyHNfFZgKE2uiztiMzXGRogSizmLsG2L4hQMv4H5hBMTStyQJHe\r\nArTdZdtuWzyYRN83vchHvAX97ZgE3L7kHoFRzAVS8ef5WaADiVFllQxqhpLd\r\nwpcZG3Oc8YQtJ/TBn5BhUSe0FJmQ2vtKAZxmCuXamTv3MEJKOlJFkPaSBe2t\r\n2L0E6kjc+QswsloUkKqB8EuBzWhj8sxXO1R/62hB7gwJb0yytGes/23IqrIg\r\niaS5Mlnl9srA7kdlFrZgJBSO7kzdxl01OEau7P/Ei3lIDIS8ghqOFHfQUjRR\r\nPU7No1hW0Dx6ZG/WO3pvDkU4miwnZhR24GM2jdScVf4j+CMdrTpaHEFHUIxQ\r\nos2w+96xcEZ88EvSpkErjp2XBwmI2bxUycXzYpMCM2214RR3EUJMs81SSUnL\r\na55UpEz+5t3qTmrZFoS+1lskhfl4qsKCUD77i/eI+QleCmxNUO+itzBFu8sc\r\nOds6yfzmwrWRioU6MDe+XXJfqJGPtQJfgOg=\r\n=EpX+\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.23": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.23", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.23", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.23", "@radix-ui/react-presence": "0.1.3-rc.23", "@radix-ui/react-primitive": "0.1.5-rc.23", "@radix-ui/react-compose-refs": "0.1.1-rc.23", "@radix-ui/react-use-layout-effect": "0.1.1-rc.23", "@radix-ui/react-use-controllable-state": "0.1.1-rc.23"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "973a3dadda15f76a06b27dd7e5201a7b1702966c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.23.tgz", "fileCount": 8, "integrity": "sha512-UdjlQuHtfJPhQClahizsVFmRica0C6JYJ+WjH+Zl2km0qTyoWZJvpcFMqU7DHReYvGWlT5662t2T721uhkQVvw==", "signatures": [{"sig": "MEUCIHFoqx4kr+QIN0RjC05X/Cds0MDdPCglVXDLUN1p0/LuAiEAipJzuiM8T8bDYkrJJhLXQx3LjuqNivXxdR2IPwT5bJg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijKGsACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrGkA/+OHlFnCsMznrE70YDibOFcm6G6VCjrbFo1HvcMSke+Y/tbiLs\r\nGXn/WSCR2oCsZhLw8C9LyC9+qOJEJjV4WHUKAWyLA3JaArZoqIdoCKnFYtv5\r\nc/zR1UnrudQAXMhOgnrg3E2LvVcSGjr8lYHhbQmNEEdfhReAzijtQwBWm4Zr\r\nG3X5UCXR9HxMTQuv4R7B3ZrgirEfH9Ph0KOYV9IyPir7cKGOkla9YQNoBKJS\r\nLPvBVHDQI+kW4zCX8fC6q1+qqBeMdI0xCoaElzFbGOWLoVaqk1JNLsoPBX+5\r\naHVjQAca1gYYNWU3pOXEM//8HqJScU306TJkluJ68ebwrUlaEQ7BLbUw7GCm\r\nmj3Stdd+8ECBA9uz6KxPHvp38R5Y4cYci/WkJXKrarxcWxCXUCTIZ91gQjA/\r\ne6tp8yUkbg3VlVmjQ2uh1LgalrCZCOqfZc/fac2QQceBZLuhj+9hjvQ/mZMg\r\nhHh3m6oZsGYkmt1jr0WQUB23+ahdpp5xP4a8Zjsae+1/PgSjhOvObSp2lm0o\r\nOyFKp/0WOSz054yzI5OCiPf0vOIdINi4AQdPMhHiUCvk8jfeY0nEUcKMzd1P\r\nOaMyjTGcL3a5UzaBQqkPIgQ6xR3AwKBDZqY1NjcEluJip27dERR4BL9OAIM9\r\nxgRjMwBPAm6PLMTR/ZAWeOlrd/LpyHfiHaY=\r\n=v2ip\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.24": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.24", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.24", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.24", "@radix-ui/react-presence": "0.1.3-rc.24", "@radix-ui/react-primitive": "0.1.5-rc.24", "@radix-ui/react-compose-refs": "0.1.1-rc.24", "@radix-ui/react-use-layout-effect": "0.1.1-rc.24", "@radix-ui/react-use-controllable-state": "0.1.1-rc.24"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "00058d29c781894309ae0da5c85bbe25c1f854a7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.24.tgz", "fileCount": 8, "integrity": "sha512-tttp/RTTFH1VdpJ1QcV3ACNinwdv9Gj76HeFr8C0IMYDNlA160owHO2P7GiBPiJt6dWucaPt6UbNTUUYkJb6yg==", "signatures": [{"sig": "MEYCIQC8I7gz7vVuHYxHRR/eRuvYdFCdYd3kkUSEf6wYpVl97wIhAKV7O5fpS8IdaROk8XUwYCVq2Kj5/KN30n0Iw/M+ONAQ", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijLhDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpWfw//QCzUQug/v7Z3m/Hw3TxXoH1dN25acvR/GrukuwkV+xbtM+MN\r\nlvbqtWb2byTYUouzOQKzPwcGGz/LnvjIuJZs3zAg7P7uHzbBRANAVsQ8fgjG\r\nYG2GRYh3223fIi6gLQ0yMYjgl/6j9J1RH9/OrHaGXSk1uVqrD0ulVaqH3TB7\r\nyiCJFt0YeOMvpHa6rcaEIcjFWvKgNBl16iT5gijT2gPDPlmljePuoBPDi3aB\r\n6INe9mXOxGLRd2gcsAKkfPScKRzL4F7/YvAEmuRKqgSYajhJ4i7+Cd2s39/g\r\n6Mqns18KV5Wls1YYF533agEJhEW5iOBJDf+5mFze1K7Q06zmL/BmC+1uNN8j\r\nCeCezJj2dXMdvhToIq2BNqNKoLg3MfGdBDLpPzGf7tsNloTi3cexuFTWKKwP\r\ng2ESmleW3W89b9jRse70EDypm29Ng2mRIm+pzIl3txdDYp3bpysRSD9rVlcb\r\nE6bidgupaHF7BqTPJhE1Z6DQnFy6kDLiznEI8WRAeApkooU2tLM38EZgDwVZ\r\nzaN2cACgFgIhdwcR/amDLXKrzUw1DKTNJWsWAr9p0lUgwNM2ve0cpBqA9nCg\r\nLge4pxqNXbfgFoqdzQ3moT+rdPXk6tMe1uTFjD/6j0vzQAIa7D9dkw1HpNrK\r\niPxjUeN8A3MH1ienHRJrC6gL5XSG4QLiGoo=\r\n=FBBv\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.25": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.25", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.25", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.25", "@radix-ui/react-presence": "0.1.3-rc.25", "@radix-ui/react-primitive": "0.1.5-rc.25", "@radix-ui/react-compose-refs": "0.1.1-rc.25", "@radix-ui/react-use-layout-effect": "0.1.1-rc.25", "@radix-ui/react-use-controllable-state": "0.1.1-rc.25"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "731f6b6e16db10b8297171c79076df126213c41b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.25.tgz", "fileCount": 8, "integrity": "sha512-RCY6fPdSywbVOvGk8LC3KoBSLpPZv2HYeItspLAlREolC4sGHKGn1VCCaLS0j9J9Rg1rW/Rq9BDg3QdJb5L/Cw==", "signatures": [{"sig": "MEYCIQCSH+vDrn1iKa5l9Yrrq5bkZt6+c1rfpyb7LE0lSUPflgIhAMbHTE9/wn1DfAm368TIqXZiOkQhBIE0sLYexi18ImH+", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijj3PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqPrg//e43Nc8EbMgMTi+04y77GO/Ml852AWM3iNX40THj/ALjqmqye\r\ndv30IhM40rEASIsUyya16kM+UAa6H+E7hDt0VxlRcndzWRtheT3NIc9fIbFL\r\nXJwpRGvW6Feh6djV4hEYnKsM1btxFzpMgJpN7ENA7owz1y6zX3e5z5etpaOJ\r\nRsfvIHvAAmzvEcuFBQBHAIC2lyo597FMcj3y+cy446tBsul95nfcHQXW459h\r\nYRF7gOIJ1zFeQM4L4OilQlYReZuA8edE93XVlCl0On+MgIRAFYWvmEUvGrSV\r\nf2NFtszEJpDr9C5xPR6sQE8XcqZq4yezq3RFjTC9+lxo8VWUbM+THlOb6sYR\r\noGLRqvNVxLXSlzh9wSBzJzhtOB81Eo9nOxquZcJ8Ib+HySWZ62XPY1sTPdXP\r\nPQ/Cimq94sz/hHESTilVNzjaF/1oKDO8Qi9X7Duf5hIX7rQiH0asOKkbbmPv\r\nEJNgZbvf1ealm0jk7ZFMg8Y3+2+yFabIxLLl34HrBmR9pF6JFjlKHcd7cAvY\r\ne42ZXlRw8cPd6u47/xknXsf/+wYLGbqvSTzvLzPl8wDKY/5TfcWwwxo0a48n\r\n4uzNbZ+PoPJYcynScd71D248AkVxEcBmpq0z7mSn9ZYdbP7yG/WuiqS6KPvc\r\navEW+Ia5K3v/BgLbqQcbcA7i8x/gQWxNgUA=\r\n=XLCK\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.26": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.26", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.26", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.26", "@radix-ui/react-presence": "0.1.3-rc.26", "@radix-ui/react-primitive": "0.1.5-rc.26", "@radix-ui/react-compose-refs": "0.1.1-rc.26", "@radix-ui/react-use-layout-effect": "0.1.1-rc.26", "@radix-ui/react-use-controllable-state": "0.1.1-rc.26"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "65de30eb6fe1c16f2862ba756d2560fac17583df", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.26.tgz", "fileCount": 8, "integrity": "sha512-mffAlVptqxSli0njyvcSzq1jfLcCMov0QTxi5F+Viy2ELUIuIqnzIM/xljgrKDvis7UIZemi8WKaO2umWqY1Aw==", "signatures": [{"sig": "MEUCIAWR/vmaDnpHVocTjOW8ZEDaz7enWrl1wgh9cNEXrkMtAiEAj/rE6v4t+U587xGbp3gWG9TVIkLoZlPJLGuxeUGP+Cs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJijl0sACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr2gA/+MMfUDZnSm9hYfMCpw58AqJ6DqGRAmNvs2vFTuaGRUXlqBV5a\r\n94FIJBVWB2uUxD48zGFyMiM3OCW7NSyUcb7ca3IDm2L2f05X/lTLWiN2qrYW\r\n19ivWOH9VcN0YJmvSn4zIc/AlJGDPzZHNmv8r9Al3sdsPTab4r7HXuvsOt3Z\r\nTERE245VgZmYHvlIqWwXMW6/Gf4KPXX7tSsAqiEnos3NibDg9QWOTKTLyIm5\r\nGC3twXqw7e9VC/PWtQB93quCPcn4pQ8di2fhAkmvk4qJgmWPAExAxJdcbuMn\r\naWwqXJ2/2Gk5+m25vy0c0cQBH2DoT1pW3oAnvPo0sZIHaHf6xZwqSIOCkxZ9\r\nICHdXmnK3q2K3gW0Cn8mR7bGAL6HH4ZkRteC4YFSzvW3lQx28CNPWO+Pbkcl\r\noxRSBqLpjWqpsQQN/LGkk4VQtn/KFZxEJqVzAS8Z06z3PR1H6JT2Wf/Fm+WB\r\nj+TRi7lf1AT37iQlEGBPsUReA7Cr6T5nmylszQIu24ZoGpyjq23iR0lSqtWr\r\nyl6G3QnQ1LuZozRJmV9N05ywG+0dkg16S2YksWGnKUagH9p/rbi7r0UdJ4xS\r\ne915KOLBKuBFszue2SqveoVLSbHjSg+Lj/5coq52inRQlx23ltypnlw7PQra\r\nVi+RiYNi5cZq7IGjIjt4SFgiquMF1Nk1lEw=\r\n=KOgp\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.27": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.27", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.27", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.27", "@radix-ui/react-presence": "0.1.3-rc.27", "@radix-ui/react-primitive": "0.1.5-rc.27", "@radix-ui/react-compose-refs": "0.1.1-rc.27", "@radix-ui/react-use-layout-effect": "0.1.1-rc.27", "@radix-ui/react-use-controllable-state": "0.1.1-rc.27"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "64cc6aa336f55efe8307bfb314c044143ccc85fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.27.tgz", "fileCount": 8, "integrity": "sha512-+9w6q0wWynMPdIVsYQqcTpb5yrfhR49YK8QY41QnyxISPoq6CIKo2SR6kEu5VBlU1wKxRlZEVXF/wXng7JeHog==", "signatures": [{"sig": "MEYCIQCZIGU0aggi2akklvSpgtC7hy+qg8Dyf5GxmEwOaosGCQIhAPaxD8Dk85rnfjUtzPtKZ5yUJ6XTxWQRJOh+idkRP5eY", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJikJ09ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmocsw//bnI4jjeq1zWhozVQz2fLziUMHS0M62vYxPgYdEG83KiFA4Y5\r\ngCC8Ca8XgRu316gDtyFmG3IEQ5HF2mOmYEYwlKj/r7m+flUYf5JS+x85iqQU\r\nhgPoAhK+pQCNfdr+2Y9ePWbuu9tN+kr11ZDmZPeFlRXkTBkNurLY3QgQGUvu\r\nANkwhYiZPr7HrqK+wWp/AIHWJKm4VBtD0/r5/Nenw7/jpD2G6Ua581sxtiNA\r\nIiSi6VGE17KyeF9T/WIOH0heYxGpwIdDRVsZB9eb3arcY2KoAiW1fOJhMVsR\r\nqt5G2FKNTpP8IVM/A7oR4ASFqNsAmKCv26wKH+KQP+HpIdOXFp6M7E1iTSZj\r\nqRnn0wvs6PyoGsbGZaI1xbtuxJIMJh996PaiswQIlooX95nN9OpHTGVDF4sq\r\nLdbwFZQ+Y49MSXdOrdJur20r6SBlbyGkTpRWL58Hnwlnlm9Ccb1UN/8gSY+x\r\neOjd7BcIEgrxp1d+yqiMsPMfyd8KYmdTZC3/bpz1+Jif29GJTQCh/evjSp+z\r\nGK88lNYw5VXvyEVRuOJ27kmsz53SVczHxwZI9TRHbGAtBBkuJ0I6QWM462rZ\r\njZ0FLoKVSlXf7Q+xX/ntzg/hLeXMFKMJyA2mdOiZ0olZyLL/Jk74+XWT4rRx\r\nsHjjPCId8xmle0my1I48WWCniWfZtY6ylzo=\r\n=G4Ac\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.28": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.28", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.28", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.28", "@radix-ui/react-presence": "0.1.3-rc.28", "@radix-ui/react-primitive": "0.1.5-rc.28", "@radix-ui/react-compose-refs": "0.1.1-rc.28", "@radix-ui/react-use-layout-effect": "0.1.1-rc.28", "@radix-ui/react-use-controllable-state": "0.1.1-rc.28"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ed1912f31cc724cc075cf1aed5c6d18b461cc503", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.28.tgz", "fileCount": 8, "integrity": "sha512-qhIPlHT1sJTMyYRswOS9SU1IBcKhrXC04Tod/G8gYkZLanOA62FJ3XxQVL/puTq97GMtGg5FRt82s1SinofQuQ==", "signatures": [{"sig": "MEUCICDkBGv3gk8sbBXfVTXQolasbwwwn8Vdj7IaTWA8RB8sAiEAierpqignsz3eQd38iL08aPmo5OXHnchJWTwRYQmUtV4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildM/ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrpww//XgJLeerIZ85A7bfIQMFqbfLUPZm1h2vB1/fux8vhhGHNE4PI\r\ncoXbFyAi9SkIhOYoD6bgeY/OmJDAphGn3ofHMn8HZLYvlljXdhlfloFPbaCp\r\n6UdagaumIRwCWkw6Pxk76cEIc8kk+KQciTldnG3MxFOA0HeVaxZZpjIix82m\r\ngY0Z1W4NGi3lrMgGcWQjAwALmX5ic08K2AwI1sTuQpr/mAepsGt2klAMsDjg\r\nxUbHhtumHhJHJnOV8J/WfgXFXJ2GFhyv9GlxzvdOuNXBsfRcYhbd05SA33cW\r\n023lmKAlp6Jx7PCAqqtXe2h52c/I98onIIdagnXI9Rvz+4HPm1O0k1B3RDmx\r\n+UUGi6ITmbfrSRU01BkVdhRosw1GMeKnPPyT/z+Od6HhnuXZr9LiuVuBEykI\r\nwNR4odzNt7nEBJUBcF1zuwjuOZO+jm/abCDxiyfoEXylcJdaJXWbFsMgNW9Y\r\nfK8gOyOk5Ci/DRGGQbZpBk1aZKRxaJutP6GQabnujPn9f1n33TsbA2YUxpdK\r\nFOceCktPwOGKcd09/q8fa4ME/reWjhYp3dBVdSaAhmz647y5Ht8VTAec87w7\r\n4ZKpQOLJBsIypF68IbFZIM35SaAj6GvoqV/0JTXULX38W7JHzAeaSywPDYmg\r\njBJOaBULxPkVURyz3xH/x3IWN4A9I+5XE6E=\r\n=iUTu\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.29": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.29", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.29", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.29", "@radix-ui/react-presence": "0.1.3-rc.29", "@radix-ui/react-primitive": "0.1.5-rc.29", "@radix-ui/react-compose-refs": "0.1.1-rc.29", "@radix-ui/react-use-layout-effect": "0.1.1-rc.29", "@radix-ui/react-use-controllable-state": "0.1.1-rc.29"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3560c31414d3c09830c1222e3fdef72659a702ef", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.29.tgz", "fileCount": 8, "integrity": "sha512-VUBEIMdr6z/ul5x+dWL59n19jfkbI+IU7ilafI20Vb/bW2H/inuhV1HbaXQYKek6BC0a7/Rwqcd7Z/8O0QqoYw==", "signatures": [{"sig": "MEYCIQCtigA1/IhsuU4BQC4d21l3xBcEWFMk+smm7cjKMDxGOgIhAOOwH37aWqK3nxfGWZT+zP7yX3b7XiXZfIpkfKMZNAfW", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJildqgACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmo3fA//cBMTIFQRQQQ16adNom/tofIYZseGSPjeAzJB0IqmS3lSoEAZ\r\nYLZpljjpfKsN+U78N2wh1+eZ+sfhrDmz9Q2OLsBIF++unlFI2jg+iTQ6aCdP\r\nziBArvuwAV85XsftYUr9NxvrwRb5qSBwJD9n1RMsTjANgmXVsL7JQv+J3RE2\r\n1fF+TNihJbWiFkVYbLC+BkgtI8CvN5dsh+NUYijj49mJezQORUU9EuG5fs3r\r\n1AHsEb7gSLQMIBTx5CK4/fXQEogqPvD9IgulRBxv3n76Xl2nTtcW/zMwiS/F\r\nMvJUtBwGoht9r/EaAyNcXyln+eAfw2MxhEFmzxXDdvDnuEPOAKwcZG4Q2xHX\r\nOuXnWapTZQMLUJcw7P4YXfqtq8QMQH1Tsi9BHBxWh/1TQAkxg+PPL5rz2zl3\r\nj9lfttUFqAJ1o95lG4qPWWBy9ZLfQ26s/SomjIYQN8AkrITehxINybegmXMy\r\nkRClv2Im+DmvnS4jmBKOI97d/bFmDzcwkh/7tJPye7WGpQx2f9tseNQzF8QE\r\nXyJY2FYaL7fJJvi/c2nuR7ZhcqqKDhk68pHFj99p93WQb6SkcsMzg3Tks7y0\r\nsQ98FABdTmNT41Z/Y0ZQ/DKcvKTTLSuIavzRgpYIkPDd0uQElZPs6PNVuXbO\r\nFcxAAZQxdgf4LKkeAcZKEeqN+s/UaMK9P9A=\r\n=B1FN\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.30": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.30", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.30", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.30", "@radix-ui/react-presence": "0.1.3-rc.30", "@radix-ui/react-primitive": "0.1.5-rc.30", "@radix-ui/react-compose-refs": "0.1.1-rc.30", "@radix-ui/react-use-layout-effect": "0.1.1-rc.30", "@radix-ui/react-use-controllable-state": "0.1.1-rc.30"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0684a488b9940559e291f17c266afab5ec33e2ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.30.tgz", "fileCount": 8, "integrity": "sha512-Ghp5uzYD71k05PtQUN4cTSN1BpDQW3CT2HzcvS/mHMCZGGZksWw8Ac2bK5NeFHt9IYKFoklnPHVLnIXbBdobyw==", "signatures": [{"sig": "MEUCIAeXfVM2s+VZF1tdd6Ld2+PqJwsVWAcxmxvmEbm4BLcLAiEAjqshDVqlgle8AEf66/IDrDQadbXijrpwreCdPmYXUls=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJile1yACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoH4w/7Bnbey7ZmdEDZGbR9EbWKQ/JSnEFjYWun2Y5yENd9MuUiEjai\r\nVrwbsSir/pBpuPV6LKWv7Eyk0kno7Iaj/K642ja436q57qZBaP1jwT0fUZwd\r\ne2B41rxTp31gALhq1ZqjYOb1EN1TP9MGw+8349zxFflyVyVg+Eow0sWaLqQd\r\nzmLMrOFizquNd87FNoIfV0+SpatmkvXGzNU2xKIqWAEgo+dKjKj6fCCU6MtD\r\nJH+B76dNMs42eYZtHXpZdXvD55fMvanmZQeN/X2DbJB/PBUiWAS3aWiBfFxd\r\nwjrm31W4N5yTEqcB4A03olME1QbBbC+NChbKx2Jktw2dfqgozDZN6G/Kv4/H\r\nL0YQ72LNuRIfCkuXdwu75VCzEPoDHY8H2lc4nTjLEornb4SltTyWZFWu+tQT\r\nDdBjlVeSUeh5cZsvOfBd2Cy7473U2IYTHRX4ulUo05raMX8a9ZbjjxXcJB/q\r\nf4egJ6rW11+i/QzkBAFPQv+qNpaFg1wbl3JtgN202FIy8YwKD2ruwRDOhem+\r\noTkf9gW9hPAF7nwpZ+qJiz0nfnHyTXy8/LLrnEP0ImYGDHRBLjHXUBN6dY55\r\nYDpVtNaq67MCkbJrbU8GQ8eJmE/vouxf92iKKkjD/rQinv0Xu3hCLWuPwYww\r\n1zjGJzaeOtvk9OqryrfHppTNllo7s+dlJAw=\r\n=dSnh\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.31": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.31", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.31", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.31", "@radix-ui/react-presence": "0.1.3-rc.31", "@radix-ui/react-primitive": "0.1.5-rc.31", "@radix-ui/react-compose-refs": "0.1.1-rc.31", "@radix-ui/react-use-layout-effect": "0.1.1-rc.31", "@radix-ui/react-use-controllable-state": "0.1.1-rc.31"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "61a24feaf6c737e26c863d389bbdd9f125df942f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.31.tgz", "fileCount": 8, "integrity": "sha512-Kz5Xi6oTJfED8UyT/kwYLjcHz2Y7akoq5ZscRqkv/WasYVUKRAUPIe23qb/yz203oWV0QOWom5LZTMmWFB5Lmg==", "signatures": [{"sig": "MEUCIQCveC7Lms1qqNkU8fnshI9J9V1AUAy9MjczjCDAugxS5QIgRiRGElSn8Y4Go2cDrWkVMxf5SzjaHMVTB7nS2EpqmqQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJil3W3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmq9Dw//ddRmcERNRR7fKJ2f0mEDt8+JiRW/lkv3ZghgGlC6/d1DJysB\r\nd7vKZ6VF/3LisxsZe3CgCY3szSYV6Whsue55HiTc7D+cFQxQ0HZDeFkQtpJz\r\nlEKfV3N/o5IRWSzszfvTqprQ83aBOqAn1vgZAQl+aFA49dRo174rp2Qx0SbM\r\noWBq3XXILG8Gcwsu6Ek/DDB07Uy6/gJWLEIy26V6vBroPpP0tMPihRLht4sJ\r\n5ln6NPE4Do7Jat/Rx2JpIUJM5zJR7PHpOAjQylBLarC+z5V4QbJ3i5IOQfvU\r\nvvhzP75LouhYpQc+mJeblyPY6uYz4JaI3lQhoA8xJIlom2hpnMMpxhO8TckD\r\nNQCh6O945cZGaFHDUMuOFNDbXDcqosBzD52+ngnFisdNNDQ7iasS48j51JO2\r\n/2pmbFfNISDjX8PmvyDrfhZNdrPl3+njZj8BbBoy5pz4h+i2dAtKZcTMFsy/\r\nPoius4k+fBgXHtSGdlB4ZEozzGXgQgLpz2M60vsqHA/rXTvdoheFkU/nOG0S\r\nZYDBAR6X+w6orozqWsOnRvl6+N2OGWyxCSL+GgCyDZrSETXCEq+6klLaIO2A\r\nYV4DMLCEZfLfaQ+OnRDKUmoL+B0Vs5L9O2HK5kC5paPhymiEHhX/gpf3kc6x\r\nbkofX/mXFMdF1K9iXI642Z/4acbtQDwto7w=\r\n=jiV1\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.32": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.32", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.32", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.32", "@radix-ui/react-presence": "0.1.3-rc.32", "@radix-ui/react-primitive": "0.1.5-rc.32", "@radix-ui/react-compose-refs": "0.1.1-rc.32", "@radix-ui/react-use-layout-effect": "0.1.1-rc.32", "@radix-ui/react-use-controllable-state": "0.1.1-rc.32"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "12dc4abbb4b0ffe4b8738c8f1e2a96b48eabea67", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.32.tgz", "fileCount": 8, "integrity": "sha512-gWXByRtwWVxJkOj/Jl7jm4e4xvDRCgkM4zQCUOaaeymTq8WTqf2WPwt97KZpUzMmqD5LTZX3pSbGwB/KDwpuhQ==", "signatures": [{"sig": "MEUCIDlfDNDXcDKlYoxr/vmxe6x3wU64KIJVbJ0gSCbGJehQAiEAkAqkv3GI76VV8oj071kOVH4ozPPsad8h6JsE9V6S0Yw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiniRWACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrKuw/9Hxp+Mm8v3PasN2ZV2tHIG2kcVn9uoJDAjpbmtC/XZyR0hB1n\r\nS9C0wZmHUScHOjc/NRcTrbtgWhteIEC8toziYf1Hu9CTXbexr3u1gX4w/q9G\r\nSx8ySjjOCnR14Li37jkEUUcSEARb8oLvkkw8Y5A3EPT0jrPkQwX3RKsMfJnf\r\n7JL0dvKzTMMmdTmenrhrgr+GjcWHSaWlcuD5NF6UDeRgDKkdTuoyt/ISN3gc\r\ndNij/xeRrDpSSfkTdgzTBf/uK4GEqv0lVhEfT3DuUt03jAfFp88NFe0CVs8Q\r\n7C4RABxNwkVrKgTFXJ6fuKrnzS6Fn/8CLh8dY0z5fwYkjE+oDbHSZyUPFWAU\r\nEPPIJEqrxkEsdY5L4+s7JaIfLLqXVe4pNnUDkaNzrWklg6vdZx35ITl7a/P9\r\nspU8yKwwIjtakeq/dQ2G/rZ1SiDXe9tNp6bgGn64NcOfb5qcCPUjceQfK9XV\r\nPbOhu/KSVxQmospL1pP7W9qq9/AA1tMmCLxaDazg1BZyNoXFp2AspZ1qLe8N\r\nm/r2WloSd//c8iGZFuM4n4oVrR8Qc09/AaymmrwJpfdIzhQHl0bWmlPah7jz\r\nHXGVki0TrodE3ZlaNZXvFl5DAFdgdgTaISQgXbz95qXlt9wFTRKjPaFLwMNl\r\nlEnWkk8YVwh7Gi871HPxnYzkhN5PnLnOYJw=\r\n=KrDJ\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.33": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.33", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.33", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.33", "@radix-ui/react-presence": "0.1.3-rc.33", "@radix-ui/react-primitive": "0.1.5-rc.33", "@radix-ui/react-compose-refs": "0.1.1-rc.33", "@radix-ui/react-use-layout-effect": "0.1.1-rc.33", "@radix-ui/react-use-controllable-state": "0.1.1-rc.33"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3a36d1be957eaa3a1164f62cf6c7bb0271efe62c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.33.tgz", "fileCount": 8, "integrity": "sha512-2QaoxgRqF0I+GdQ6T7LRyTqXvcUwfmtKlyMxKmIG7L37CmkX6potqwTWo+PCSmo3wcFJ2/ANyVmHYrvOXzvc4g==", "signatures": [{"sig": "MEUCIAW/L5IJwG4wpyWalOGQNNnpAlj4jh8+szcAGIp4/dvrAiEA7e3FtHnws8zPsOxJaQzk9ywFLkAZO/3ig/5QkhTBa90=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioHb2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmobcw//f7VaILUOB0r7B9ZAEfwk0s2CRcQz6uQLQTuUA92zIqMB5Q8g\r\netUd6pfRaAcm0BEGAqvrRz1E+4ykgEAfUADY2lq2wZsbk/cRsyTWKYT0eKwT\r\nxOCUSouefhMLVcNBh4fkjwq4okPMAFxAafw/Gi8whzaZChSeaGWFOXwXddy3\r\nE/CjMfn6ksFjNAmcx7Ax8EO8g78o3cFrROaFRTltoxw0lW5Pibxq2/+XrsmR\r\nIMj3syNW6Ha8OyPRKpVHLj0otJQQl4qPbaZdvQTF4oJS0E5UqEjjoGn/3hUZ\r\nA+4k924nWeFTX7ROzn6tjLgTBAkN7AuU/iy1mUDOS99cqxp+pXkJfT1eArTh\r\nw8F7WA1dEElIoZmaMonOSVblTBYqpO4kcsyxrGhcW4yaPWEW0trFmysH7ZpG\r\ng9dFUqFvVo8uYhry2d8n8sED4t3jl+Ir7o8nLaU00vep5xfli1zXlUiFZ6Jt\r\nj+TIVfSUzakRp1rL2LIXDx55Kr6BgcStyEXh9hsyD2y/Ou1NQ25ZVezoJss+\r\nYoWb3AXIDqv3a5HsKjUaReC5DRFTkcerIQrI7OGfkGLho+r7p3bQ/NUaciCb\r\niKo7fCf59YhkC2dU3DCRUnci3mN4yCN/j7r4dTX3YocJgis75eZF1crT9Lbx\r\nAHzyygzeXpnHpEyKV7R5mZMCEqEGYJmsOqM=\r\n=+rf9\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.34": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.34", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.34", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.34", "@radix-ui/react-presence": "0.1.3-rc.34", "@radix-ui/react-primitive": "0.1.5-rc.34", "@radix-ui/react-compose-refs": "0.1.1-rc.34", "@radix-ui/react-use-layout-effect": "0.1.1-rc.34", "@radix-ui/react-use-controllable-state": "0.1.1-rc.34"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "283913efa51be50c29d94f284c9479f1c942177b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.34.tgz", "fileCount": 8, "integrity": "sha512-q1gkglN2SjMM3dEeAA/ZzxpMaIU/aBu/idjhSVcpZVvRUqIiGbp7BJnO+rqa/x7iroCXLwobyYwSq7ku6fQl0g==", "signatures": [{"sig": "MEUCIQCFd2NgABw3noKMEnDTL/XNY6JNj+d0LarLQ9Fu9Ts++AIgZV5Bd6TLNzKRqdIV0yeHAVRAraDLPYJlPfwoABjLRKc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioH9dACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmr+Kg/+N4Uqx6j3/K+Ojvr5AS9uP2j7uupC8xJtLOYBrss0CciXkABL\r\nIs9TAaJ13iXpc9nRs/Oj3qvVLI59pTDwqf7dviZ86SCXhqOyAUZIVDA4HONv\r\nXjbNxvW1aEatn4HnjZWkd4q481HhxtC49xs4bAVZa71k38r+LiVWCryk6rl2\r\nBvv8m1rYqJm1btMnPjy3gg6+hUIuQV49DywjZCpwZR2e9avCeZH0fAB1U0wx\r\n3gFih06AASThgOqTOsIPtxMWyqt2LmSlwtTOYm3iFRZUJi/eIGQ2NAGqnIIb\r\ngKngJKie9kzDKYdjP/wTRzrnyBkIQUdRCoKD0a330Hl1hiurTJ/+8mzYKjpF\r\nRMS0LSXFBGABroYs4HM6bXCjN3V5fnWpDPh+XYUUJRGqGsUN5dCtJDV5X6cu\r\nCptguDvylHuk+ijQr/t2yyhHSvctA0gQS/WkTQbIoESiMsrm4kOmbNrgswy2\r\nOUQD2hAh/plLLaD2DRVEUKUxRbaa9qaaovujtWONjCH6zDV1I3qFSgJuUNeM\r\nnZmMBlyGMuX6MnAU0tJREGNMAHS9H1Mme4Hbun+xy45JCCKEYUzKTmj3OsjO\r\nIHugwFmh4W0ObRtCAzjf7+ayHhbqjF33RtSTwAa/b3pTb3vhZbMZnMNDbE43\r\n83tPesa2BJcowx8qaQlJA6plaLQWfHOVB20=\r\n=WE5A\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.35": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.35", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.35", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.35", "@radix-ui/react-presence": "0.1.3-rc.35", "@radix-ui/react-primitive": "0.1.5-rc.35", "@radix-ui/react-compose-refs": "0.1.1-rc.35", "@radix-ui/react-use-layout-effect": "0.1.1-rc.35", "@radix-ui/react-use-controllable-state": "0.1.1-rc.35"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "1fd90b6192774e0158b83a3355114a90f11cc8ce", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.35.tgz", "fileCount": 8, "integrity": "sha512-VEFJqfVJFFlvSHmxftFGIn+CgJWuh8IiCe2NVJdytvLVh1Wsra69BtvE0JWHnPmN0O3YtJpaFt7vIZmb99Wmmg==", "signatures": [{"sig": "MEUCIQDcGU2NP4phpYPXb1uenRKD55IgnqqacGi1+e7Jdfp39AIgbWOu248DyuBGK1kaNRDwfkRhj6znRJp1vX0K/tB+ARk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJioOYZACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrEdA/+KDD4u41tfPIeYPwk44mRpdd9i55XVbPO4D7X4iQEcNbsN5v+\r\nXt0r+S4t9xFYpFvYLw7wPttsaDNL8CNe06wfpb+a5pHEB6GpGdFKy4zsZVRL\r\n/qk/wo6/jXemCkiCbb7DNi4c4Yiv5D9c0YQyutWlVzSr6Z2asZ46CKys8hR3\r\n2o99ppmsqbs4+fitGbe0y9f9ZLlh7+rQ4GrFzuj7zSrScYfZ5mtA9SU4Ejyq\r\najHs3zp1y8ke24FzihrwhraEM8Pg6k66wxk39mkusaRfzxuW2yfoVoCokEAb\r\nZatKcBgxORRZNjS/LGiuy0+J5AyLzURlplFPhjgiknYjvagp+FqpF/K9KnXS\r\nERs4JCjBG/7avqh6fcpMWpdA8Cvjqt/ryiWiNO1UsO2lH4iOaihlwLVs7DHk\r\nYRV++TkgOHEHbm14lXToWelHf47EoedC410FSjNRxC0XkqKErOaz8uobh2sR\r\nDPc1c/F11bhEVeTq9cEO0G1QiWhzWZiH51DJKmEKDpPTjrrVDomRjCOsFRlC\r\nitR4eDa7VZiHzdTTQ/+5avR5T9LLLcqtMoJ2+xhXwr7P0xdhewDS4pCO3kha\r\nDWSfP9R6bzGY53JEl+QOaGHGDHST+kze+HoJAPuszGIqi7e4eplT/KkF9Sh/\r\nmFax6iAoz13Y+ZH4szjLhqjSwo2pxB7Fl08=\r\n=dmkD\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.36": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.36", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.36", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.36", "@radix-ui/react-presence": "0.1.3-rc.36", "@radix-ui/react-primitive": "0.1.5-rc.36", "@radix-ui/react-compose-refs": "0.1.1-rc.36", "@radix-ui/react-use-layout-effect": "0.1.1-rc.36", "@radix-ui/react-use-controllable-state": "0.1.1-rc.36"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5d5ba59fa55f47dc643e9fc8633b6974fc77fdf9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.36.tgz", "fileCount": 8, "integrity": "sha512-81mUK+TBgekj9s9HhC8JP0uthFCPnoQtR7Xqckw1d2J/QVTF7La9TG+eHNaC/amEJzvUGCX4laOm8EgDYDG3GQ==", "signatures": [{"sig": "MEQCIGyzkEuQKZd8W0lS6Zs41lkhL65v62k6KhyVQHclQLs7AiBQQthh9fywJl8SWpuGMqWiO6ow6Rpy8ON9Dl4Pgaz/7w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0IEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrmTxAAlE/T9kbf1BuAhNA5AdlZ3OWABhAU9EIdjLoyz2bf6XT3C4eb\r\nchUBfYqp7acbtT/I+OvOB00NIeDZ1kc32AqyWdEIlGAXILaTbrhQoYL7nRFN\r\nUz6JHn2MA4wmtXsqu5zyJyrSVXVpnNbaIXXeIrMh8Q2XUZFEZAbLPPoAdX4x\r\n1k0oifGTH2z9XDMbB0V13dBhLdz+Ed+ML/VqZNezvhz2FyFxXP8kPAc5QbKy\r\n9VRvmDU3RUlVAQzx2neQ6pWy630Akj3I+eKE9C4m93X4AR+KvA5SxqmyCQJo\r\nP9jk0HTwmaUPy8Psq0LMCd9aRYTy+/Dgu0Ftnnu6jF0XYi1EY4gRoOmQWV/L\r\ncn9+Q7ezv2/bVJlDT8VVVLvb65vyD1wT9bMbc3ropWUwsbgR3zSGe9nrpoZi\r\nfPOrdfZ5fQyrJivgMjPm60GMu+8H2W2jRCjPiPeto4ZbVZ8OK6QDgz5KMRUy\r\ntjgqA/fxEIzGEo+vtQCwwBSkTVs0jw3mhOwflh2Fib76rIP5e2TqMuXAMNoo\r\n4R78IJJNOgZBN7p7qQwDgqHYJGcdaRpjiSLWM2UE3+23vvJNV4PSjaexr1Qy\r\n3WloIwSa1l6qXftFaywEiOLv5S4Qkd000TcdhnHVYxEo9az+eXRUJ7/gBjWl\r\nYRc9Rt4tbhgXYtrE9V/BVV6tH9DzS7/NzJo=\r\n=PDRT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.37": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.37", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.37", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.37", "@radix-ui/react-presence": "0.1.3-rc.37", "@radix-ui/react-primitive": "0.1.5-rc.37", "@radix-ui/react-compose-refs": "0.1.1-rc.37", "@radix-ui/react-use-layout-effect": "0.1.1-rc.37", "@radix-ui/react-use-controllable-state": "0.1.1-rc.37"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e87c8b64f84f962e553e4ec7586669a04a7da851", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.37.tgz", "fileCount": 8, "integrity": "sha512-hi4GE5pcEB9XgFRlyJjQX3GzxObH2dZLtLEM+VcPVfnxLOc9letqsB4ieB5BvqZB2EKNUF5yfvYZtXyUI1BsOA==", "signatures": [{"sig": "MEYCIQD+teO7KRTY+yMY3A+UqJx2pIRkTH8SL5Dy71VV4EspzwIhAIvjTlcNjO6Nvxwv2lIiVIBjIAkbDiQAPjAk7S2VI0hm", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJio0ncACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmruMA//UV/YlgrTUOuytglueMIgqBDDAExtaKXuIY0fVLX7bX/h6e2p\r\n1lj6qtb8Wk6QnhTdC44yTOr+Q8nVU9ZcHjq6jit3g0waGnwuLJnQna8Zjrpe\r\n3qpk0h2v3JmJbtXkgU434QKFSw4Qt+lfCJohpsLsPppDxjtdrCR2wvj0v7RA\r\nWPq2YF3iSxCdAT1TnpQViZ2otAgQpaUtpzN9ouLtR9VlQ47gqHa/S4TXCA1b\r\n//kavPiLOyPm+q3Pn3bzYqXniTjjPIC0t9Ic2EB47sGBakpjqbJBrXVgbwyS\r\n4sGc0nvdtxEwW8J5e8KuvYR17Yf6jNDr7xkBnYZ4KAvHmQiPmhf388EEqJ3t\r\nnGWZiQVQyLLbANVl2cqy2BEn+ZjlLIXqpS6+0qS97dCXLjZeKfs6ML1BbKh3\r\nCXugBJ3ZhPMm37Gc/KpSSu7/eHy6UTMcewH3kvT1UGlb3UsmeSoYb9nsYiIm\r\ncYkYBGNKq9K21Nhipl+lse1vTtVxitzEz0mjVp8BzNB5y8EEYqrojRNRxfSw\r\nGGZgepqwAZEDida7r+94nmVQpmZfF3SWIgWO8syHKy19w/AOynomzw2/5UbA\r\nc+Ivr/aB6Y0avowR9zZ3VIEkLEfdE7qERrVjkOC+2p2DcWRvUBMIYhGcWP5E\r\nK5EOLIbUp7Fes8WLRqFAaK6bGPOEO5NTHdo=\r\n=G9Kt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.38": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.38", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.38", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.38", "@radix-ui/react-presence": "0.1.3-rc.38", "@radix-ui/react-primitive": "0.1.5-rc.38", "@radix-ui/react-compose-refs": "0.1.1-rc.38", "@radix-ui/react-use-layout-effect": "0.1.1-rc.38", "@radix-ui/react-use-controllable-state": "0.1.1-rc.38"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "620cb5de506c72ca053e81746920640923e5b9ad", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.38.tgz", "fileCount": 8, "integrity": "sha512-Hk/rnfLGjySqzdCR1pcxGK1XhPFf9KLIQgJSeaWj4Terv0H3nHfDgXdX1Pv3DlsYwRMwjWwYL5ZZc1FaM419aw==", "signatures": [{"sig": "MEYCIQDZCUxAPCo3O1vjSaUozXmQ009Q9hRF2R+J6Fx5Na4zswIhAIlbpws/mbeSrulSXtzGVM07X4QHwtDWYtRVW5KSKXDe", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJipzpbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDcA//UWF9QAV0X0BRoxknsEjTt3m8Kb/iJQ+Mo39XkmnntVa+PNCo\r\nSiQXo/Cbnpl/A2ctNCIs3vaOZOFi5RdORgkmObRB8+YFCToqxbPmV53clrMM\r\nYIWM4duEVfUQ04C5VJLB6jWgBifJhlyZPxzgWiKoNR5zoFJF19BbEIl+xpgo\r\nnfqs/VxIQBHB+TRBEf3N1NYNH+LsiGVvqcmKB82gRKxSLAzjCfIqBT17Ul9M\r\nmHatrgIk3yrePM6OeSS4GNtfWj2P0DkELHMcVxs38QHOyptPf0Zj6xrauxc2\r\nz5TzuZ6L6dOHMnYX/Djhz0b6WAquy04KENblNxWGkiS1Sc+Z2DPCZgTFEWZI\r\nxurgusFjeMPHtKTiVUeDmWNqMzZusSwXbsqyp3N7AMjacAcKOK5pUCoks8g4\r\nOFuk/9dQJE7a8wGVnTAJM0g4YDTRl+3WU9TdyI0+VYzpYloAQEaxA7H26FGa\r\nvhHf2Aefr4LN4V3+Rw5OzvLGG/jNHKPNYDo6jUOMtlz06mDq50v7uVodgB1b\r\n59t4eQlpJdNcaaiigSCXZw/Wmy+LY3i2XZU0hjZT5rTImPnhitPJdAEwIfaf\r\noNHMg1AQ783Y/khnuEJpGlN5UdaRoMURa28jH4IFmTmu3jSbQe43HP6gKqbT\r\nmkVsJTyuWYXCndp4mQlO1TFtjhmy0l8Ando=\r\n=LX94\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.39": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.39", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.39", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.39", "@radix-ui/react-presence": "0.1.3-rc.39", "@radix-ui/react-primitive": "0.1.5-rc.39", "@radix-ui/react-compose-refs": "0.1.1-rc.39", "@radix-ui/react-use-layout-effect": "0.1.1-rc.39", "@radix-ui/react-use-controllable-state": "0.1.1-rc.39"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "16de8da984d45d7b074b47ae4dc4a9f0e3d029b0", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.39.tgz", "fileCount": 8, "integrity": "sha512-OaqulZTLR79QVoW2E9ocQ4Q7dzgqET8ovyd6PtjYwX4bNKT4p2MM/tHY1GlugXluhc9F5nrAkBuJnzGvicQg1w==", "signatures": [{"sig": "MEUCIAhR8/Xm3u4geZZM4E1P/VCzyqE5J48l3FN9jU37XKo6AiEAttOwVhmZPPrUG8sV0VI1Db+WHNNaQ3M0LAKRPDCRZI8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiqz9VACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmobhw/7BlLXzgO+uUtglAAdvSXKMmsOpLPeVVqlVqn3ekDXlYsg+Ugd\r\nH+dkUWjpYJMTt7s5i6ZzoO5NexSWy5HbOelpBlKDCRX4FF6i3s91JqcnGvZ0\r\n+/+dIhtWYyEiY45JyUTrbPq1Sm9iQukM2HhMaQWOpGm/hm3CUOo+C8iVWWYm\r\n12LlHN07/tPjJ4PD9TA08BUA8zeEYn2MohlW6rD6ybSAbjrpuRbqt4TBwFR3\r\nBiP4OjYGWTZpfhUSrDKzFdefYoDa8m/YNLX4Ku8l2KG/sLvDJn1lASLdrxv+\r\nmNvepHUGgncsN3KQwDcivOG1RUZAli36e+zYh4jVYD/sWI8CqG5donHQCbHS\r\nTIFiM1IS52AO52eC0oNfc/5tDL6FfG61sOGh1+3hMDjG+gRvjatuf0YPwB63\r\n00e9fvNMgFyO6zbc0pNptn+BwsoBKDooktSZhfxw/KXmnF4dYuNVCvJtAp/V\r\nzfUGSjOu6pv70mLq3vyyOdli0pQAxeh/aDL/tpBVxELCtvg1F77bH6fHA2Eh\r\nwPwu6qDISQ8xas0bent9CkGYkhkw6IDL4dqaI/nFQbKKdpaK4j1irXSqpZc2\r\nrfNvdGaezUE3cYM8F/JFRSTcEtko7tFchjXyJZOqqYtq0g/HERRQh/MJ8AeA\r\nupvGRXjVkXCQb8wRzdVB9xntgjRX6ReWfMw=\r\n=3MIw\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.40": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.40", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.40", "@radix-ui/primitive": "0.1.0", "@radix-ui/react-context": "0.1.2-rc.40", "@radix-ui/react-presence": "0.1.3-rc.40", "@radix-ui/react-primitive": "0.1.5-rc.40", "@radix-ui/react-compose-refs": "0.1.1-rc.40", "@radix-ui/react-use-layout-effect": "0.1.1-rc.40", "@radix-ui/react-use-controllable-state": "0.1.1-rc.40"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "bebf285b8beb6181b917fa84685ba4cea67891f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.40.tgz", "fileCount": 8, "integrity": "sha512-r2C4VlKnjJgQmI5ErzhOTZj8c0ZJwrxDKFnBlVKYUR3M7mCyAJiZF5d2YAt1Nl7zaGiTUs2d2T/kZS0XrVN+5Q==", "signatures": [{"sig": "MEQCICLwNKKQIaPkX+7YHLMJXLMpLlcyPiAebvYuWn5dmSI2AiAYtFbfrlqoVVIDe4di228giUmP97UfjHPMh1j5RxNN4g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57422, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJiq0VhACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoCQ/9E+ZpLB5L843M0u/DarNh1XyGRmLPR3cl+EiNH9DiB8Ka7Zug\r\nd3PHajwNQVmJ5P6b6k8Xv/Ynk4SW1xN7hozPb1/DMzEQ4ELN1nI5w6a3wQYn\r\nNM6YE1KVq5imN34TfQ+2vpFDDhhPR2f1fUZ3XYRe1bxZx26ioCOrj96wZXCz\r\n8huInwRZKDZ0Wrp1y7RWukHfggChx/4+wg5FC7TdcYnbRPiUbYHFx43F4uF/\r\ncIpkpqS5RcsFtcXetYIi5FPDLh6GmIJNAVAoiTdgxIPGipOWj+CCPRXUdswq\r\nMHslwBtYvO91nYYJdxxrl7HsHupQgAwFj0+9DQelQ87LTteAXVsB2Lqrf0hm\r\nOG+SqAn5MBm7t6M9lrm+JgLNJkDzdZngXUVDtqvAPPI2vxGegKlyLPGx0FqW\r\n4ZaHXa7b29rPawIjQwPSEPoeX/NlAKVyBUrhksJbvARBUou8m3TT+5HzAOC0\r\nBZ3xY5AmNHVI4nrQ11MLR5UCwv0oCqPh/YfY9/YIaFwPs1Rl9VWa1num418B\r\nqwVFmCEvkvlujB3zmYGthOcBvzGe1jhRcmwj1aYmwQNFWowHSXQ3cAyahbVR\r\n5NX2y7bQsvg2M392e0xkpGPb6KZOPKYVgw7YLybBrXQUzDDRgZbYVSNtp9Jq\r\nRB3xpoplXiaAZhGH1YSdRfA9Lzpv4UAYIfM=\r\n=AEL2\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.41": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.41", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.41", "@radix-ui/primitive": "0.1.1-rc.1", "@radix-ui/react-context": "0.1.2-rc.41", "@radix-ui/react-presence": "0.1.3-rc.41", "@radix-ui/react-primitive": "0.1.5-rc.41", "@radix-ui/react-compose-refs": "0.1.1-rc.41", "@radix-ui/react-use-layout-effect": "0.1.1-rc.41", "@radix-ui/react-use-controllable-state": "0.1.1-rc.41"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "06e9525e0b047023f280f1e0bae1d32be34f7954", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.41.tgz", "fileCount": 8, "integrity": "sha512-cctS+pX4ZfH1Q3hMA8O1yugjNgMIqkx8TrTzJ8etHFjGNtnDS7uQzKjnxFxoxQ6cYwk2SkQa5jzl2JCMXxe3+A==", "signatures": [{"sig": "MEUCIQDpq9uIft6ik7Y4TQmUCOsoVWUE+Xgwjasn3dr5i+VAGwIgBDpfY8hdd0UWDfHgKMGkKu6txr65cAw5Zbs47vBa8fk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJisaY8ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmptsRAAneYGqkPuw45ysB64fiV7EoayAuDAZaqVvF6257TVueHy+Z95\r\n9ovuarnKFW6bPYiPPW0dj+ZyenXHvC2TF5UG8bJ84WbWR0s+k4GYiy8C9xjX\r\n82X6QUV3/CIRbYZK/XSC3cc/I2etT5XcPZnVTfFalz3OfstrD+SOCasJMjP7\r\nfVTOLCH74WeAOKgIlcmcD2wUYwxd02KkRn+zVBPSTeP3s2ExdxkcYsVJnMZj\r\nrV/MZ1haj+5STgG6GWTbfZ3+ZEWG8Za6AxOzt+mWIjCocxOps+7WksZjyU6L\r\nvB9RxFErP1ygMwYBWUpsQa5AyZYgg8+XBO/D5QN6bWzlO1IqGRQizh5nclfp\r\nc9Nxw1tD310dXW41Z41HVn3A16fBowjjWH/OOGUDvqnL70llMx7RG54AASZR\r\n5xhg2FjEWiQOMNbc+amyObYUJW6H5GuuJuWpzuo4fXnKq7MWyG/0y4zKYLkc\r\nfeqkXI87HeveJICAKqVgT71UQbWqzHVVJKFSac96tp9AJgf8n1nZPPnabDAA\r\nOlStxHUK0htNnq4v5YTGUMUxobn3oamxCstvmrod/VJ7ViVO2VWMYrNhmflQ\r\ns0y6qxaVEp1yDWG1DvIdUFkzDSyL26QTBO0GLC1LLhZ6cuxG+dP1h4GOTP6q\r\n7huaprz3bB8Iy/wS9+Qm/xrOAMuNnDQ4l8c=\r\n=/oDj\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.42": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.42", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.42", "@radix-ui/primitive": "0.1.1-rc.2", "@radix-ui/react-context": "0.1.2-rc.42", "@radix-ui/react-presence": "0.1.3-rc.42", "@radix-ui/react-primitive": "0.1.5-rc.42", "@radix-ui/react-compose-refs": "0.1.1-rc.42", "@radix-ui/react-use-layout-effect": "0.1.1-rc.42", "@radix-ui/react-use-controllable-state": "0.1.1-rc.42"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d46057bb3a60edcc5704955cadc14366d7590cd4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.42.tgz", "fileCount": 8, "integrity": "sha512-CF4ex0dPOmyG97AtQUPe7wYKgAmFSgfyz1Sofi1mB6fXsSMQQgtp80tZXEOH3zhmVTqHjVR3335g0RJn5v19rw==", "signatures": [{"sig": "MEYCIQD4V3/EN3b0I2HCsH8X9zLVOlYThHXl2iBdJVHhhUl/+AIhAMZcPNof9A6EGUCbjkeKeviIuZkdCD3XTlA+C2Npncbx", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvdTACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrxSA/+KgPt3DAOWCZg2QcZ39NGfk/cNKL+vvSX0Npj+BkNiPEnVb5C\r\ngIN0JikP7gsNZ1e+IIoNjPMc6/Z4tnqPFEODjltrsdrufXQsu5xrcBi8BDwn\r\nS7u6GyzEbuAJSUBE+oLxLqpAKT2H8ZhdI5BWmYS3C9GPTFIe3w3M5SZSjT6V\r\n8IYWrSwPwqBYJ6cV/ogB5f+93qwyz7I/I0AH4aBri64MlRk2DEIMLHrCAYdd\r\nqn8gMpTzOdP6+iOa3w+96kjMPRwOuhe2XaPwroysVIuOkoOzz0oiVCnMQciu\r\nsAy3cktTuC+pZKfUqlLZ16qu+Hb+kDMV6xsWwLax9+z264D0MtW9tPLzlwPa\r\n17x6bEy5Ya2pRCz1W7mVBsXoJ5blFfZ28e8Um2X1tQkrefF75wdSSOYbmmmS\r\n0aDIIo1UN0o7ClK1ZhbPfxUGMFPTRuxqZXlwrF0bkrxmLHn4CceEyAVeybty\r\nxNGOov2WfdUaQoRU5JmEvcuVOQlKbk416xFqMoEBgC105yq2k0x0tPbuYKNT\r\nbeRj6/B7PXgSIc5Y+2rx/0ednxjzh264uV1GMDj5I9H8EnSRdfIxikEHR4xO\r\nhq6nDYfzQuXBLoKWfrOxKKWGI75cLbJ7do7zEsoS8ReXjn/PZrJuAwwj8WZa\r\nTJl/vgLkqP68ET4g7wirbtCmAoAwsxOaaDw=\r\n=Vjoc\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.43": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.43", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.43", "@radix-ui/primitive": "0.1.1-rc.3", "@radix-ui/react-context": "0.1.2-rc.43", "@radix-ui/react-presence": "0.1.3-rc.43", "@radix-ui/react-primitive": "0.1.5-rc.43", "@radix-ui/react-compose-refs": "0.1.1-rc.43", "@radix-ui/react-use-layout-effect": "0.1.1-rc.43", "@radix-ui/react-use-controllable-state": "0.1.1-rc.43"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "af682dd1545123d07267705987ba4d32e3ab361a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.43.tgz", "fileCount": 8, "integrity": "sha512-v1dXZNPRcGZ7A3/R9cobyGnntrNz4ZVE6k3LYWb5kAMciXKpxNdiIf/u4ocfIU0YbvEzCdwPyiyS5a6r2tQTBg==", "signatures": [{"sig": "MEQCIHSoj5gD3Exqmw4+pdshtEcrfEP9urJ+fciZg9UJnH4LAiAY+5xWt+09qc6SNzc1/PG+WL6JEk19zhws77XvwnToxg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJixvrmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmrzqw/9HM1fp6chFf3rW/RH1LL8b+/0ZAboJgC21x/tejDkuGlhCHtN\r\nQi+0yTUI+pCjsTb1u1c0bTqRM8JWZcIZp+1/JUs/gy7M8lQZ/tFIgsjO2a3Q\r\nZGtkdWLCGkyL1NoEJg1ASDeEJAAZDeeWlrPyp0hYnam/5zWrT25q5ArLCp3M\r\nTFH++hEZwhW9mvIrsR1KlMQNZCcRPkxMUso5d+xAwQtzw+nII2SHaaIF+5eg\r\nKA3Po7jRd8T6QNFgybS2Cg1yExfC6wWfUZ5I5kGuppUlMtxIMOFtJrjTaZTy\r\nRh5zwyeiZlXNcvmfQ9yuvOQNLTDqNLgb+AAmXl48Y9Q4Vrc+nxP3EdXhuO+l\r\nZEpnzwk7Shl5sYI9SnIhKL5X8/9Ei6m1/oXeyvv0bjKldTvtEaS3cK7hBTsK\r\nCA7BgMB19ebthWzSKSULomM4qaap9p/am92n+LnhLqo6d6BU7TZsfU1VzqhI\r\neYLT3Hu+AA3Ve3YH4cJIn7AqhMi7YGqLfw3TfZN8f8un/gezGE5vJnKFs0+/\r\nRdiA7ItgRfvftJjtdhVnhm76+c41QvS3z9uD5XUCNRsPVganqCDxxGQa+Miw\r\nTyHswl2rjmVwVwPN8AANojCsCR2bq2twrU1HLxp7HM/jAkDHXe66W140gxd2\r\ndZKRoukWxiaIvu227J3bwJlZAIMBsff5i2I=\r\n=Icl8\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.44": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.44", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.44", "@radix-ui/primitive": "0.1.1-rc.4", "@radix-ui/react-context": "0.1.2-rc.44", "@radix-ui/react-presence": "0.1.3-rc.44", "@radix-ui/react-primitive": "0.1.5-rc.44", "@radix-ui/react-compose-refs": "0.1.1-rc.44", "@radix-ui/react-use-layout-effect": "0.1.1-rc.44", "@radix-ui/react-use-controllable-state": "0.1.1-rc.44"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "ac5ce566dc146b64fc6a2106871cc75d0a36e9be", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.44.tgz", "fileCount": 8, "integrity": "sha512-M6XArWWqtu4lFZk7x1myKkVloJFRwJgZtRXgtsiF3KOqmZrV7ZqTKxVyzbwyHeRcpNFcbf+REd+CyuRW8i1KIA==", "signatures": [{"sig": "MEYCIQC0Cs7IkFrdWqb6sr3QE6l9AaX24QAjzX27rDYo+kaVjQIhALJieH06YpGB4clhKpgBFP6uUAt5BH6I7uUGtZIM1dB3", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1XGBACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmowIw//X999isiNUXW//K8q736HhAz4v/EMLenWa0gS3dSQJhMTTH/u\r\n4fvGqZf17BHNM7No1d1O8IzjjpjiQi/1YAci7EhHUBn0EXcWB3vFePbBILkd\r\nFw8IVryuX2dEtk9lAngGHNZ/ja6RUkz44UnPeN0kProG/g5DMF01Q/PM4mi2\r\nRfJ8PaW8vWhSsFSAqeMqH/wmfa2Xbo8qBZwBTTb4gJafmrAoc2cfohVk4Uiu\r\nQQxGTauMjILY3RXeapw8BFFxE5plbKhPxJNQrFQ+Xf7iVH8J+wg5GNjk2FaF\r\n7keRy0Z6NuITWGOyXmDGUWjqIkwZ8XhV3Y+J6aGd5BgKefzeL1y853X6rf/3\r\nyAU2zg6pgLuwim5teYCRFQzpSR+Gj0+vmyqJrxAsAkJCpH3DpnMOmLmFif+u\r\nQJIIY5XzXGDqseYP9w53awz/EzvujD/2V/ECcu4ZUs0FiAyVmxdPmzDltBFy\r\nQS+RzzRfOfhoh9g23WQuG0kspq3Wd+Be3Nd3jkSsPhoeeFsWlx9984GitSJj\r\n26F6+QNNN3wbcUGBwlGogxowNMPKi34Sx5bIwclZ3roXo7cdeKdd8Xt2SikS\r\nDTN59Jn9vt556yEcAdevMUKeiuh5G01lK4QS/Yu4FnTzmNmNrZCQcnGPnNEi\r\nUe+Q1g2n+6SLOfZtJZs2qNaDfBXA7WPf7dY=\r\n=v9sV\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.45": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.45", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.45", "@radix-ui/primitive": "0.1.1-rc.5", "@radix-ui/react-context": "0.1.2-rc.45", "@radix-ui/react-presence": "0.1.3-rc.45", "@radix-ui/react-primitive": "0.1.5-rc.45", "@radix-ui/react-compose-refs": "0.1.1-rc.45", "@radix-ui/react-use-layout-effect": "0.1.1-rc.45", "@radix-ui/react-use-controllable-state": "0.1.1-rc.45"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e7b80afb98c76f590c28679fcbc3f3ff75d0a9c7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.45.tgz", "fileCount": 8, "integrity": "sha512-AEDwQHOSkdidZNffxLyZcB6kXNtsPVN452MQi2ROn5DiIqzKeT0XXjPn0lbv9QPEge7BWp+l/aaiVgSGqDebzQ==", "signatures": [{"sig": "MEYCIQCvjfkcDqwEvHuu0lRSeueGZpLubrYVdnfMM4h/eQIGMwIhAPiDoWCoyy9pH2V9X/oCX42Ga/6EC7SX1vLDm/OVtO+o", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi1wVjACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoU3Q//VE1XpvdCxIB9PMU2PjCj4JkWSWQFs3WapxIs/udvR7U/NVjp\r\nudl/OMav3AH5S3DgVObXqY2Gu0G3eZ8X5hl1VUtRxYwGjaWH8ogDXVzAUSMj\r\nAnExNJXXsnrsaUg0b9evHK4TJokgLIcRMvJXKS73j7P8Ay4OF6Vgb5vZPMDV\r\nXoW1h43JrkkWAaWN8fDyC0tljf+Yy8rVjQGbfioQYwDj0MbuBxmtouuqgsqp\r\ndtxbSqngg/UAnBhSE8rzb9MNTH+In192PFs4ZpYI22jCvTCpeTBrPVvy8aFS\r\nYfj10+cqcR4lnbhK+fH5CpcLebtQE4w9fuacnI8BnqWB0Rto/eALrZ6i5e8a\r\ndQprPfJ3Q8WF1GCO/Gt6MF9u6gqTnYELSsZt6fQNgi2+tSnnVmr4z7cs1odt\r\nNkke5CBxu3JcLsV9o9c5EGPQPxdls+R31wgXIrEONgBxrgB1yaL0kA9G+GLh\r\nSzH0S+OPtwvLDvtwkMb+Eg4smB6riMh+JQvJKCmjAZXJGiZCH8vMh+MZU9X0\r\nNBXxtKYhq2M75o4clA8hPvPFTRxH5rUDF605IyTmFwOa5FhzwgvIN/7wSD21\r\nFxRIVqiBA8iFolWTQYoY3nptEcq656N91KDnmIr71Ct4BOitdGHmg5hcx/4u\r\n9S6khRSIB25dJUtXfRUOrM3scH5ZClI3Bk8=\r\n=mDmT\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.46": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.46", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.46", "@radix-ui/primitive": "0.1.1-rc.6", "@radix-ui/react-context": "0.1.2-rc.46", "@radix-ui/react-presence": "0.1.3-rc.46", "@radix-ui/react-primitive": "0.1.5-rc.46", "@radix-ui/react-compose-refs": "0.1.1-rc.46", "@radix-ui/react-use-layout-effect": "0.1.1-rc.46", "@radix-ui/react-use-controllable-state": "0.1.1-rc.46"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "36cd26fa228300f9c2219327656f6e4ce9fa9049", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.46.tgz", "fileCount": 8, "integrity": "sha512-OT167T4qCwHYaSOgXbIloWbOvVMEo4PmWNk1dlUl96X2iQLzoE8qMn3I9GeIJF8l0atL+i6tHf/mjNJomijpCg==", "signatures": [{"sig": "MEUCIGowr9dB2y4V+DodBo/D0fPEs0ojwwGSfyVsgPKg3iSrAiEArE/POWNIxVat/DKMO02ZTOfV1OTHgZwY/EJHbnSEZnY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi197PACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpTFw//eDYh1D19Sj0Vc9WtkPnKwlVxi0hSHCfNCZmYeBDoJeYbNYN3\r\nIlRS8mKAcKSYhunN5C88/FMAkDf+lXbBXEgpK6rmsayydR9v8AqYM4ptrx9N\r\nK9eY8Q/H/hi99pI7WTo05rle2X3tZmU2s7gCpq9MQSM9vQeU9AuRZL5sJF7Q\r\nHfODqJaLIvx1NXhefthVYPFU78QqouJV1+WpBCUQOOq38e3YV24xLNqfK5zg\r\nC1tZOpt4p5JENr42tCq9r1If8bAOPCOcO7jC0wA5N7RP3eelrVcVmbacQh67\r\nXGRSDecgPFYwnIoadrY9HY+uAsqDtAX/lUNGUVJ8c+XXMh8SVDkZw6fE1Q9p\r\nUphBiutyUd+Z8gGEfmEsRn5GH2cpCgICCKBpCq2MCWonh4ly98y0WhQhyD6k\r\nHaFGSsTSSyYhP2QELPO/RDgVAHZNCaeq6TsnYmg7IF1mN8K24x7UgcZv7bX9\r\nrKbsN+AQWVCMLjF9vhaw2YsFDj5/jBIIEw3pl5/DTua9OVnowLZD9FX9nylE\r\n1Rdks9GOv2cBgkyzjV0ChpfHWdbk+VyAoGLG3K+n2zDcOC2w5RraucAnbuJf\r\naDQYXSvoYGgwBDg5FuRap6+NwvsT9LA2xlmNYm0kkY7kftHysLhhdH/7tX0G\r\nkCGvNyuI3NyKoRF3s9oqv5lkO44uNOSE41U=\r\n=dyQt\r\n-----END PGP SIGNATURE-----\r\n"}}, "0.1.7-rc.47": {"name": "@radix-ui/react-collapsible", "version": "0.1.7-rc.47", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "0.1.6-rc.47", "@radix-ui/primitive": "0.1.1-rc.7", "@radix-ui/react-context": "0.1.2-rc.47", "@radix-ui/react-presence": "0.1.3-rc.47", "@radix-ui/react-primitive": "0.1.5-rc.47", "@radix-ui/react-compose-refs": "0.1.1-rc.47", "@radix-ui/react-use-layout-effect": "0.1.1-rc.47", "@radix-ui/react-use-controllable-state": "0.1.1-rc.47"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "f471017b8e63165e86a62fccd4db36da6d292f19", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.1.7-rc.47.tgz", "fileCount": 8, "integrity": "sha512-NlE6/gDTr8Tt4KE0lY3BZGxGMz+oWZY3vHE+rmp2cV6GrzN588F4aja3RqyNVi5aft646OLKRB9SQAS+AKgY7g==", "signatures": [{"sig": "MEYCIQCHRH3yyaPMKDT7aFJpNt6mMsonWo9XIizMcEA4Ww6dKwIhAMBs23AriQnVJGMXRfCvgtsd9iA3wJG6dMXFsDzaZZDr", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56686, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2CDPACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpZYA//e2RiI96wumpM6847WOr/1c8+35QeIg2RrvS2N3HGFqzK7Rtd\r\nE5hDmAYDkhoE7F1XCwJRpgzLGj3xPaS3ujxAOOUZ5ZWKtuvq9xEiRD/dGCXb\r\n5Oam1x3MI4BIShkibIW5KldGYS7JmoUYEUgOp6Ehn1jbgVSeL09dvYn3UOfH\r\nnk1MQEhdLs3RBCkv6POcHr1pwkUkZ3a0vR3fV0YFpeZukPTV0+4sc5xOd9xI\r\nYPrE+dBszaB2ulP3+eC5Pwt9IsMZPGde4aFiz6TkIgz2gF3fiUI2x93TadgS\r\nL5d318Zlovj7GEldVY65UIj9+ade5uyMf+UlNWiI4Fh2o53xernoH5Gyjpaj\r\ndjzJKz6ybZtQjdx3s6Nzt6XwtXJfZw7ZwZBgV2n3NmkRC2eT0PuewUKt/K3h\r\nOnU+UopyYW0RxUaNzToR+c1EVGOuwVPeLuU3AgITIeYa9xz6RiEocRinDrPB\r\nKUU8/GyG6tcoTCu/fkKaHYlmHNQq5YA3iVOnPkc5WX899MEI8DXUiRpdb+0t\r\n8wLfVMYbaBtrYTD/FgY4KsDVoK1l89b5wi8HhQhY6qAmmvticjQlhr74ksJw\r\ntCKjtn/WU5cqL+Ij6xPqWlH8Zta0FgW6ztWnwffrQIP3kOYujgmK7CeFyRAV\r\nikkvhCqIHFUPVJrAH7CbVLl/m10yUgtb2kk=\r\n=Us2S\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.0.0-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0-rc.1", "@radix-ui/primitive": "1.0.0-rc.1", "@radix-ui/react-context": "1.0.0-rc.1", "@radix-ui/react-presence": "1.0.0-rc.1", "@radix-ui/react-primitive": "1.0.0-rc.1", "@radix-ui/react-compose-refs": "1.0.0-rc.1", "@radix-ui/react-use-layout-effect": "1.0.0-rc.1", "@radix-ui/react-use-controllable-state": "1.0.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "b4c43821568da5b5222bbabdfa597e6bb2e4f670", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-YH7GmJTFdnr91RaaeVqLyzhhmLsaXEPwzDa0Sr/D7z1h+FxYd/3S49A4vf0wQxXk6t+cxJ8WgfLaH9JUB4EiOg==", "signatures": [{"sig": "MEUCIQC55kmDCVUT7s/mlHDMOsZOfPrSPF+KJ3iuVum6mBGWgQIgOE96T8I9DWROBOiHCJc6gMij4BMAkKCMvqmlh0tM9CA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56678, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2Eu2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrCzxAAh55e2JLNi67GFZaerQ0+uaapElZIfIrsnij4ku+fNckheucG\r\nAPSZcjZcaEAw2wigN6mRfMopSEjwrHo3WkNXFan8dJfBW/wJ1AUOVSYLfNiu\r\nZtekXW3Pj0Rd8JmuMdctOhbT4nxs/bhYjtnTSANTJMnudgikIC2NwOGaFnz0\r\nldF8ZhHjdLBUHAVvdJ0RBFt5vRPoemcggPGJxuRG+zunjWpDSNVznZwWw8Es\r\n0URTwnTtT8qoxiSJAwvDqpngxur5yp2LqTAwIsQe3qUXZT2x0CSx+GOie1uK\r\n/B8WE2992c2WmLZTm7hsEt1jXtmEin2ecW3ViqeG3wTxb8EXTlBMi5orE3XR\r\nhdSY1QysUA5/uyr4dt9h4uyX3ETCk/TLdG0FYiVrooXK7url05NT44Sybk4M\r\n4DhsRep8g41ccCBgtvmFrFoH9Qqf/El2IhQ1O8i0MDPwNmH5eOoQBIHyCzu2\r\n7fhKEsrbGdIsJcssjX+05h/e87lbDKQ/LOHTMsUJA0G7DSnglOFLEof1oc1N\r\nMrXl6/pMUZX6kVCOtoLzXYtE2z/4j1ldfTjot91VCh6MD26J32uxWfd6epob\r\nM/vcvMCOI7WpCTRFptIvUv/JQkBwfrxuncZyyzPPaDefHgwHeIN4UtUkP2RY\r\n8Zs74qrp8gpWJrwc6jZ3QbPmpnO+qOoGLj0=\r\n=8q76\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.0": {"name": "@radix-ui/react-collapsible", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.0", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0d94fc847c2d4bee1ab646d15c87bd3be6448873", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.0.tgz", "fileCount": 8, "integrity": "sha512-NfZqWntvPsC43szs0NvumRjmTTJTLgaDOAnmVGDZaGsg2u6LcJwUT7YeYSKnlxWRQWN4pwwEfoYdWrtoutfO8g==", "signatures": [{"sig": "MEUCIHeNmUGekHq9Ma/muGAbW27ViX41vhthx4a7AgpC4666AiEApNEjdEXy2jiVlva/ru2B4zLnu1+mXx/y6rKx1VcT/8M=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56605, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJi2E4RACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrRxA/9GVsDvQsFtCKtJ3HY+r3N8trzVAuiYJWhMw/dn8fjlCp3F66O\r\n22Os4p5MpOflCc49cIv4zjslDrdHp6q8bcbvvlwQuOQ3ryGWIF8rltLEkctP\r\nwJjU3Ao4nWTBzB9XCXvtnIJ2LZwbyUEDWkJx8ZAgDkNMJlGSh/nb0OXiyZJd\r\nkreC52A9PizATF+SJsLNltn6YaNXmEk4oQP+s2rkUQKUguRPYc1cix4Lxxul\r\nYkUlZnZ5024ilJBPb4nixhglMJ4IAtdBU464KhSZj/zLLZSs6a1X4mTcK4dq\r\nha9nuXce+RfCTsMVQgrLam+A3fEjCQEszgQjIrR2cUcQVSkFY8YZOamAyfG6\r\nx/MwXak2B9TvPcOxyhjvtD+Lkx0naXpLZGne4aUnhAYSDJgCQ/KcUtksOQl+\r\nhNll6mRYdVi6I3ICIUym0k872U5tvjtdl3x6OULOjXp9K3NgoGblyS9xjeUD\r\ntKdF1KsA1iJgDWBkJ8iHJrPrRc0qAHfkOCGKhMZBDbcUzjPjXi8OLt6xVcdp\r\noyP7H66H6ROSvq+4b4m1ODPk/XxMBOhhGfjSQxsvVsI/UGmnfedD364ydwUQ\r\nSHmuO7g364zSZ+zwOiia9qW4TOnZk9HTLXpkQxwejyraCZKiDRcFgu/v7xb6\r\nM4mfEqA8uRxFwrQW0RINKXpyWgazLUBU4Y0=\r\n=tkbr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c53f34a6476dbe6724ce8ec14182f55d58e97788", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-REH5MfeZKYKxbOdKnn3ujoQIhhTJCM9hUmytSFwaEJVg6PlaEyu87115Are+l52QYCrsa9bwLsdmL4rD06Ehfg==", "signatures": [{"sig": "MEYCIQCCz8fj3/6hkfCzvaBHUP1oHcdakN4rn29WR8tWfpdpJAIhAKa8IJOAjxI2RHs5JGEZZ6nVc2P2bNOV1pgLhK22Uydc", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjMbskACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqnshAAkI6BNz2mqK9RwYnQo16+YSJppODH5W3MiO3uULkc8W4j0xvS\r\nvHGl6mSkL0wYHtUz0pFAUMmp2Pa8LBX+MXgfQkmvpNZhRG/WU0l9tbvS2xHP\r\nmSGM4lD6uydUnpgfNut8+VlYX3d9c156rUcaNM3qAqriBehUAMk6bobX5YZM\r\no0NNTBDzqardVYhioEot6feRtQkoSQky80bn9mMIXM/gNrSlKNt15ZaTWqR9\r\n1Fs5wY0Ot5b4MP+deLEPw8Xsc8x4ZbyKs0+FJ1475EG6eLeko1m13Y+pHXkj\r\npaXzjMuRxbG6kSfRemSp37rcGGwrTYggZvZeq0yTm1HK5kxtEs0C8sIOKMYy\r\nPkqIaIAlfe8PyoFp4mUJX9AqQwVHQBj/TwFQaadt2ytHDdUDMhjRqWVzhHY1\r\nmmurCeseohy0eknj3T0igmPUWfmQt6sIsBOEjpU75M20+ReZDpEw0iRT/i9s\r\noouM6JyuMyTH2JeytEaq/c95VNGJ6EEz9MXDcjAF7eNSVpNByTmOZ/Jb3vOD\r\nRSP0PMHYKRwdIcRpv3wgtFO7XsHHWUyRBJ05ci+ryRToYJqZ1dTAqMkE5Oaf\r\nHG98d4v9BzxoLt+bxtVmoB9vLNQW1kFIWJr8taoirdWUC+Cm+pm8Y9qufRng\r\nRu3KGwEjcKu/znae6vHxmhan2PnNuAsVnJg=\r\n=+zqs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.2": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "01f09df7ad46b15ef10faf3c530636e9bfed6f35", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-PRVcPVMiwUOo3dQcTWkMq6h9V+c2WTb+OQEPZjFub5aW1C9K/vgmUmXAqBBZoZI57OHytutHzyboezIZWdzeOA==", "signatures": [{"sig": "MEQCIGSChsmxhDA6Gx/xEzP4mewAvGyReYdYS4xalePvlB0lAiAn4R+e0UuwkYAkYEN1bYMyO5eZSZc/iX7Ctyafkl+UhA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNKy9ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqrfhAAlAR2rprjrwwO24NPQGxYnv+VIv9zGRhMUbSHE6IOACyPotqK\r\nfGa+RyMTR9pe6pRInuip9Dmhh9B8U/I4YnGH68iAKD8xMfOySE7NEHbPk/KQ\r\nwTNhxi3gOYxOV/fclR5oXEz9ZQoG6+ZsXJB732TqZEpkLURFxX47itDQ81m9\r\nk1MlyDVAefXn+Vto2V2qWyILKw4Z8UjEuviceGZ5SVp3bkz8DYeMbOIhd6iT\r\nxIqbqpMJW1Ymb4ASkL8P4dRNgJcnj5MFbGe3UstmPHHS2RdQo3oEK5lApRNE\r\nPHq21sSJX3p1IinNQwxbD7a09Qtb3LCgoMTIZomJIaZj+qT3tB/TOFE6+B8i\r\n7dZ/8clF1noOus3kprqO0SOVatTupnxE6L5kOtRXUzkZ5y3lQQDtn2u07Oid\r\nZwMxYDVZyO9ogyefvTY1D9JL3cIxjuS83PNBpz/iHQZrjZdlmlr6WP71j62U\r\nwtZ3v1TLFRFtPyhfBGhfQiq03ZBfoUfRNHV5AUm0fs0b96lYOtjjf3hgBVUE\r\nZRkeCYVr3VKwZOJ36an2V+QiipSjxCloqJbpiU46ztWEKAYJ4aX4P8Bhfydo\r\nNeQS7Q4VIfWZkzzRra8CXMmapGwFB5vbwAL5MYECRSCPw6po/uXLYfLAvEbM\r\nWSVzqNSQRU89CEXj8+aQU1qHzYokG4N0ia4=\r\n=1q2y\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.3": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5c5fb870dc104722e381952408de8bec01f4ba89", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-iEtNoFeyhtQSdX6HGM7cCSwc4ydmSy06samHscuqhxncWP2yV5kozNjaX9jK2Quq7wJBt8FvPOZvt3Izrvbs+w==", "signatures": [{"sig": "MEQCIBRSGmh3ZLWrNJJDygfbduBMkpjHUTOl1yxX2QCUL08/AiBIQ7kZPTVld/DAvz8E338bXyICYeGhIwNo1iIXFhMATg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNdb2ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrPOg/+OsyRz1+QCO3M9SvXulaqIL9ylBwtm6gUvj9usISVwKyHbr3H\r\nk+X3dV2GQSXP7uHy2o7yTHnykdkIVZT+pUJi6V/q/CunfV4/ZZKzZIhR3Xbi\r\n+Z4r2MnXW2JcH12zbLQaIG1iy3n6D6fvnEhRSaMbXfyrYiNMpuniIsbfmZM6\r\nPvB8QtskRNRexM9G7+IQqfWfvM2zKdLiPLojciLHAHG1yqUtKvinox4gz8yR\r\n0qAbbK0wW89VIDcS749DrDIB+JKSa0lK4HpxXbXD628PweJDKT9lPtyVDMc/\r\nMlwY1/2WI/2ueZnYpsIgFEK2InCOMQ1b+lAte79K0hjS+8xTsxZbE6hi0sOi\r\nmrrrcbG2UvXr+gxl5+N6DTXsACOeDQFGg8G2sPLHMj9t6QKUjcGdCumpQS4G\r\ntAmkaA6z4z18yxA2MlUxqVXGyET/SkR8WU7yNzxfYJJN4UO93gXNzNDVAqEj\r\nfttJp/mFmTxqDR7RiwiB/8V1nVuHADSy3ruSBPQi379u8SZSBaSGgRfnGqYu\r\nr1bmmVeXCFR9Uct4qUQbQoXnLSsjLVdBCkMS6+krBc6AkQW4rYruuMtdeupr\r\nWXV78PRHu7Mwm7ttkK5zlotCewun9x02Ep0MC1jYwdx/YKSS3dIGPqyZmjtT\r\numNXZnN4p2XO8oOav5z85oxw4znaKeTF8y0=\r\n=UdHP\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.4": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7ce66331b3312fdeba415b483d5bbe6bbdd28a34", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-PJPHVJ29zl+/cGjXPBLexdC0D56rCjXsV7ccxyt03dQ2Ni7L/FWSFSEcZfZNWmuPPWzqW/C5zAhcRH4xbea9Hw==", "signatures": [{"sig": "MEQCIBqmTgEZgyAU87ywDMxw5JDW8Vgb5y4qQPiDf2uxqdEbAiAvK7JWeRpvtKi7tVxQ6QoYXhRu5n3Wvpz7G/1u6R939g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNfAmACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrDbQ/+P156djWuMQAtbCKN57F2TkeUEX1UIEqDYnJSB3V53SuRFQZE\r\nTvL96yUUyu9V4pUuq8varsLvcC6H17YJRsBTd+5ebh6mlOgkPfp7k908tpSS\r\nTGLY4yXPFZ8CPP1sFDsjoMIePeoj3L8lWRTXTcw4b96HKX7cCBvzEsAI658Z\r\n6czRysIOtpkaloPUwClkePt1UI5aaCiOeKSwqTZQj0CsQ88Hnz147Ujmm5A0\r\nh5GzIqqwf/JcPsIbmuaLRRKKOP0zYh8dhhUOEA5wL+fmR6f0uRDoAn/yW74d\r\nVw7SGEErXiio2mlX/fKvwHL+a7JxKmvLJ/ASfAzdN6U5NMAizJseGDUm4bep\r\nE0UBBHFwGnBvwwDqOuHo8hKiq1RMiZMcZUTkBy/s9BihB60r4iz9YcoT0nQb\r\nGKHb4Ihez7vv0ny/FwUNdEjHmjHfVGw+jgqdEtlL6FRw1Zf4CrK8zx2vmS0D\r\n1Wnkgi+0EegCZ8gn/gTJ+FniGbYbtmBtA5gO1uk6Bo+HCltecdVDsJwI1tw8\r\nVLBcY3FXyEmk3f2bl1lXrom7hXxazYuwBxgX2h26KDBBc32kr0tJ2qilyUaW\r\nCsNXD3M/Rzi0o3yIBip74gF6RcCM5cgOjgD7sbaSGhcyfilOJhC6OyvBTIYQ\r\nXDlNn5pyreu/O5OkmmPscwxwSXmfwNczSvk=\r\n=LDdr\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.5": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d58f263dda99ac339722218daaea20be10b7e9e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-6YuPS8J7tQRohEEF0pqM34tu4t+6f+zqHi2k5vEfJwP/fGtG1+K3IfkmJReZHAkMMduyLc9P1QWdWMyFavN5Ew==", "signatures": [{"sig": "MEUCIHtcqN2apzmZwGM0aVUlogIqky8ovRsN+eWIxXxlSLNGAiEA/4qtokX52g8h6tMtW20q7UIWhFDRh6w7ioUTbMGgaak=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNr1zACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpV6A//Se0LoqlWdNvV8yHDEI5ELrPfPGc0GhJTicWTyuuIkQaCfiHZ\r\nQ/jrFJHR89jxuFEI3m2zbcaA067KpmHGrF7nr8tsvYzBxolx0+zlWf7eQf1K\r\n64IHZgN8tZ59CiAAvPp+qCat6JeCcvlDCiEIhKnHHoucrko4r0aTS2wmugVh\r\nsFpna+s1kN8gdE80q6y6US6wW1yN0N+61ayNyKj+aPX9HEOLSisQR0qD/hrI\r\ndjFg8/2YnqZbiEJsR+WRTO6aylqpK62Hb7V/RbwjeUyow+uFBYDRdOCxSBi4\r\n/haMZchKBUozdOw8nFaIeGREs/HPvGj5wucc80AZ61Oo3C/dj4QYgSuMpZcD\r\nkEXeyOPftPyYL3cOF4/j2aPKKpBhvuE3ql11wyYrSZ0lx96Sn5Tc6Z5XLwjC\r\nFORx9/vez44ThHDe/zzUMbxGuJVLeFx/5na99LoEWX2V/wMxFNIQ4pu/7jOb\r\nQH9fHX12V5wa4QqKHg3XbDYefO6yc059+Xag0gXOQVM6FDZ2Gr8KzHuq2Hag\r\nR5yFA2kJndoTSeEBQzRq+H+Qph53Qjy+bJLy+8771Tn1pmRYKsA7khIkw0hf\r\npdKlM8tqfQQPCgx4PmmOPtjtIS33Y3rKPwn237bMi2d5VVjB4pWetUGNoyja\r\nSlXmqToCeFYKA1CV0ZNIvq2yB7OJFkTuq6g=\r\n=n28J\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.6": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.6", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a73f9b553ec4f4a9c76c88116a070e2af8322e05", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-fRhfWX9gmrqr2SgkqrQNeD/YB4o96IbFYCGvVjZRzGK0d/pr/RkkZPWW/0RIQCDCb1phbb4TrBrCaTrc99iO9w==", "signatures": [{"sig": "MEQCIFHs3RNJiB3F/tEJsNA+bVoyTGWEWgqizLmigPSyH5+CAiAE2E2rnaflxu2B4Uz0bngnoYVBqEJsLcwBKboTctSZGg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwO5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoWQ//fFm9s6NImWjLTMepnw/ZjBgPmK60zlYLQ1QBtWM9ogeZ9mtF\r\nCtCtZFNXSzcKQd2/CO3T03CtmAR4AVD4TX3sIcm8K06xJwm3FH5ByWqMdtdg\r\nrTPWJXzPU/AjrydlleJe7+4oTVeU9drDYQ+v6pt8KgKvEycMF0kKRLqtTW5t\r\n1s+mh5E2Yw+eFWnRg+0iExE5ezTAu9BgFK5yX52gSdt3CjsvHTnSe43HkRZM\r\n7BNy3va7n6lOfb0KMbw3vy2/emtoMIrGQi9qSplRgVWHkP8+i3i8BCgdFuzw\r\nwp5GR8KteCN08a6XnLxs3TQDDx9cK96elYEbG8w+Phc9ad2vlUCBw4fCm9Sl\r\nuX0CRp7ccU3BzsOwrBgi31Ztz0QGk2ZQMYQw7FR3Ksf74i1x5I8W6HKn9omq\r\nRKHFXCQmw29F64HLcbPJY5SmSo/vkxtMGsiLIdv8DabadCA8UGoXVYl6X1yp\r\np27LOKQFW6DEErvbu8cDbYHP7s9CRuqIsdrdOY1xg9NG5vM0ZcRqQMkz88NI\r\n2RINIsaRRHEwaqZ1UINxp6jwVENW2M0TkmVIPQIwUr3ACKZioI7pX/Btar9H\r\nt7NJVchB7qFNhLSxluzlllnUyZM9w58uGd8Le6YUGfgjFwUnbknlRtL93qNj\r\no3ElWP5sI9ubFabVF3HyKKu45ZGQ/ickUrM=\r\n=upOs\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.7": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.7", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "9d9dc28247672aaeb2808b0121a4d196d967199c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-5KWMxq9FZebVLek429rNbSi7XN/nmjn5ppmtKRyzFGe6eRwIplvnNL2M7ItUG7euhY+xWJcvmaJ0RSR03Y3fOA==", "signatures": [{"sig": "MEUCICMB/cqRSXv7zFPMxjPa1LvIpCSePvSd41plGFcA1EZmAiEA2QtHPdYnfqXpq4D5ADAa2UkQReeGAW2OI/NPT9yv6JY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----<PERSON><PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjNwwqACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmp9Uw//X4/aK2kAlepxnIc3gpnPvlTd5tisZvIfCn4QSlF5C19epEgo\r\nMsSehkGsG96F4fBAccBGpMzO3F5lrHQJURM0JeLW8E0APf+tKP4cs4SAl7b1\r\n43XtwN9C7Kk00GZOVQ99YFjb+p8q8x0EHUWUReCdoLFFZYZDoetQqAol08Y2\r\n4qiBVI6yYLeU7Jc7xh2nXuy1+q/hsY4ca0g7Kkc4Gvm/WNq/PgTIuPjQME72\r\nIzP1Unmx1DcbhK3PhupBbjnZdf43/6WjqkexyGOQ8/UY/ACgJthyyy7BvyfZ\r\n13NSDL9qdMRmeREt+2A0WVJpJlhU8QapgKgz+zcW5zGgHJNlPJo3PNaKuCe4\r\nD6IXTxHxBKMCIgSlJ5ZRgJ13LTnTduUCVCy2ZSEgcecVkqRHJg8PtLJnK79I\r\njrurvmDZuru6z4iVtKGkAYvNDFpwPks7JQWaKi/UAW1KtfXHdLHdAC0he4Sr\r\nr+3zifVG9FAzZOI2bsohcu1WjEl2bldKkaXmiNb+vAEbD/jIYZd4Lm/CPa1z\r\nnhBSq97MSwCr6Ws7ylHYUobWK37JawrLmMozuK+UuBPFJDoNsfaBMpSHaKD3\r\nOGnwWRX1N5TzjAwSdMnlbH810VcemQEiiGxRi3kg7MxXEj945pxrv8zE8Lc0\r\nbOecXAoiNKeM+vx9oBVM9nQO3SUOUhe4esY=\r\n=AWZL\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.8": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.8", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "82b8a75823651b9e362e15bee9a9da57364534d2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-YCmoTqUAzwXhezyo9drPgdTVzrjKAj2N1QB7bGxjHocj8oPTxvVHCi03v7hyRIXfyc7j1Xt0CPtMMlfUfPvtPQ==", "signatures": [{"sig": "MEYCIQDwhCppjAf+UkmUwStUem3JurxBRqj8moD/m3433Eb27QIhAJCyoMelJ5p8gme9Gj/DT3ZlaIg2t6sn2QTN/3Utwo/D", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ+gLACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmryQw//bZ/Bm4BBzhvQWGZGwHdRd1dHhQ4ndFoPtwEtsPC1fSsN2kBL\r\ndqKs5qbsa6Ag8OD3YYC5vHcss6BGxIVb9uIw07YJdWY0VZRc24wnrkJ1c2m/\r\nJ0mH/wGuDLtYNooqEOCM5n/dQ601zBqsnt5j3e24OZsECvlUJnyOSSkBrxmZ\r\ncG79IzyU282LdW6OyhaQRFPXmv4kz1Kiuqul2riqhLC1XACTL3IgrEKm+XZ0\r\nVZn00jct87zFUuhG54xDyVwdcf85BlqP/hBuv2Oi/qNP9oHQJe7g8AuniQkH\r\nGy9GRxQclRYA1bFXbU5aD607TOoKi4ybDSbjJJTIe+9sPeT4faLDQhi67Ij3\r\nlVLL/VMVVFR31idq+zvOHdWOjdMQXnP43jII59Bz9hsuf0OTAmIpTe7pk/Gg\r\nfX6nFosVf2gUtHEXNf+DKAYufavuRNazDxlFGpAKcsRY+f7H0KSwstt7LtNG\r\nNd097H+y+kNWdLll41ogjPsyCPVMqkLp16Nf4UwKKw6eMj29kwFmI9sv4maU\r\ndRMHkVap7YEVeFf0Qr9nmuP5btVM+ViuWS8sDm0+g9jHB17WA+52ioeciF/w\r\n+JQ/m+/GCxJybPP/5J/uONBlmFCZCJ8XjMxLx/2LiL5yZRA3ZYpAjAK0k3OB\r\neMIbLb3neAfUZquvb6Mr1NmxV4jr4nlU+14=\r\n=8fNc\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.9": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.9", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "14cdf676a3cd6df36872bd786b479a279b64c44c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-ONwBlzprqieX2SvefOyhp6UkFQ1Mg02Nr+wOlrqeJmL2fV4e5X/Tre0TK6AWqmHZWyxdm5pIoN8wImlmpkrudA==", "signatures": [{"sig": "MEQCIC8jA+4v5Kh2MTHSbQ9uBLg8oUXkimLYkgWvoESkS8D1AiAiemlA9AkXI2X3gon2McR0b74FHIMDCGt6xusadYHu6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56643, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjQ/a3ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmorwg//RMjdJIPS8rhgvRYeUcXQCYcthJuWY2x+/3PYI27rVJz2xO4D\r\n1s2VxTM3IXBo5dwF6bPlmBOwgR1pc2AmL5wt3q6qNmaI+9Zk7j484XlBkI1B\r\n6nVaLq9VLnVcGqfTy3UPxau3+trm3GvcYEMcTMPxYo1Ab2FKDR48VxzwwTlc\r\nHD8voI/EAY6ZfaAfZTcJtxSdju2m1xr0RvgIoSZsWZeRadZrnzQbiasXlRIr\r\nqkMQRQ3hSzM63Z+iImqWzW8Ut9iOpgyePgQBw9g0mV1JsVjRk8lg1Kiamrtd\r\n77XCA2NiQSTp+fqyfuBnMUmqE7sRntNI1aD4eekjuSY2NAN/r3xu/qrpeQyB\r\nUBvV3FxCQYgACjnOAfwyPAvFk2xmfcw+hoUJAS60U+S2MV5O0WnCEQNLWc/W\r\nhFp8ma+/OHe5PTTPAKHiGOwftf8UPx1KJbjwfNYqut7WKpHl2OB/V0g4TJaB\r\n/C9vmaLZQoduiCB6QgGTgaPx6fJavMur2eLRmRtyBpokCuJpvYOIP6IeehJr\r\nz/xD9MdRtvG4meHH6dejLKEp+Gx2fjphjmSBZmez2KgegOqaAO+VG0ouX/38\r\nxYT8jSrxbfqVaEH92u6JmrKf5WhVKB3C/afXueh/um2DAk5hP7C3W4LDL1PZ\r\n5yZNZ+I315d0GeQjirqwh/GuuOXeyuhFtM0=\r\n=moGt\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.10": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.10", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "5334686be22639460c9d26da751399882c6f8361", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-EbOLtMxRUUcikDo0aHyVcUKsnKB+Wt7u1Dk7L7aPkdIUjK+kaM7fnzhOKg8WGA8Yl/dimS8E2czQoAFd5SLWIA==", "signatures": [{"sig": "MEUCIQDCwQ0KYINDnv+JtSSMl5NpBs6bTF/TaNkFKDspTqR9xwIgG3YV1jTT024VImwmlxl5LYOuiIHGFOGqbpHFn7FR7Nw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRABbACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmqi/w//baYnWs50jmhWHzlOVJPGfHpH2z2f3mxpmIIZioIwxsNGmp6+\r\nMWbp95UazoUXtZwItx1tyXCJetvS3nNkJ6lGuiWCiKA6PA/2YOKv8xH0ZYg6\r\nF+xEDzLnGLGyUsU+GWrlcEB1poXo84yvm2n42PxJGDVPgUk1osdJ9OSiitzL\r\n5IZFOzQmZqHW/Iso5ErcFxfFiH0pqu6Ki/8u+74DCuHPe33V1UzqkstZz/Wn\r\n60ID8SVmvsbyH/s8/u0o2Y6Hj6nK0yqEIk6YSURAFaTQgy7jIKpeMGm6mJJp\r\n07cM79t+EjTed1DOzxE++J9/xPE+tkAhVE+ripPOagDwi/keeR21iV11xBmi\r\niVX4HtTdL93D80YExfX6fgsHaHyPUaezR+hA/R84P8FElERwSBsTeWylK+GC\r\n3pyAEsGGv5DXoQjy+Z4b3yUZOqfOysUz3ypxAGexU6wUUf4IwOeXVMBKpxhK\r\nMPcOpzO0y27q+7Bq/iBQjKfQsbvL0LOIwsuTFD+j6QPJpJJkLrOk1xo235gV\r\n5msjarD0reck+hDAPUIF0Ch35Uv5IK3UkXq130uQDLDmKQoU9iw27v4v9yEh\r\nt9M9lFkvQR+dyTc9hQMa5YQrUlUm1Idc8lRv7U+xDOoLT6DgR0gJSWWfMv62\r\nzJ4NtY3gi6qSZaSDUNLABWV3fVykJ1M/ZcA=\r\n=7k1a\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.11": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.11", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c7dc27d87c091d5add520054a01f868dd78f12ac", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-GVcpJl9nJcCYcWXwEpcwVnJbI1Ryzhwsr+Q9A6oKyB7uhkr6gn+S38IgGhDnH1S9fRaQ4MppK/hjJHeztEwCXA==", "signatures": [{"sig": "MEUCIQDs4jVyPVgbz3lG+QnzhK4d1Hf9rCqtcE0ZnyV+Akzw9gIgK72QPRLAcmPzmZgSKDMSrmMiTmfbi2kIpxfJOUfkiMs=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 56645, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRRxIACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmog3A/9FYGbkbE/6s6g0FUzTm74gR17mA4b+bLLclpi+GZ6uWEtjznL\r\nCS9bSCQmN6Z+Gu63EQlzRBNmv5gkKG+DlOUYa9lxT1KdRH7ca/YAXeokx9g+\r\nTPEcqAfV4N3R6IIBCi3y68ia2A/EYdURmRQbX6xV6ndaU+k5cNJFTBe5ofsr\r\n6DBPWKHyePQaRUWiB+oo0Nqj/6EKqCupIYMTerKAn25jUvthd/pQTYjnHU+z\r\ngWP3NBDMpE2+ihJtZr9BrrIRhjJcyY5+yffyoPZBWYg34NJB7e7aTGdXhqan\r\navY8LrUrTiOmAQMLSCwTsx5ut4gkWVcrsCoUA5ozcorYfqXQp2NwE+Dhy5ds\r\nQVLgFFxuAMDA8+5K1ABZ4PI/+2fZyqik72zUUWNgWzJUhcplIIQBXKJlGtVT\r\naxFnHZfXbnLmCCv53zFpYO3fmt0kXrc5i6iFTKs5uvOy/PKCUOvTPxSHfgFU\r\nd0Xiq4SjvTaoCPIJ2t6SGvyeArJv2yvH5TDgT708EzdJHF7iirWlRlwy+iMj\r\nyxuNwkzukr3gryqTqmVKIIkwIa/nyrFb+K0Ap5tGKf9XQT8+wSfVsMNUa0st\r\nTbcDlBfnXgkz8xNsG7ZA5L+Umf/a2p+SmgdRjAsoWUnSh1InDrrGgVfRNPfj\r\nj23ZIjlXOa5jBFdHTEmT9Qhya1MW6MGBUvQ=\r\n=TFh+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.12": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.12", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.12", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "3e11a71df4b30d782f20331fd321767160a1ec2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-F+UdlSY/7bwdt0V6jF6v7yJWn8rfCEoq4J+zOvZp489CX9TuVO4+DhzswyYQtlSBBWDHZ1Xp//zPnzHnHaWdXw==", "signatures": [{"sig": "MEQCIAVzRskRqmMqvaoBSvYpapB64wryCT0DUiv59oekDf5DAiBAEKyOT4xbPVEOx4j5WRqs8qlo/wRC4heTjH/YS9LLHg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRVL1ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmodIA//ay889wrg22qhjhbpBmSe1UkXlRTyHgtecvXn8BrBXuqJ9f/W\r\nmvVsETLs8BoS+k1yeT8LLoolp/maJTzEahr/4slNlonYHYASzbPfpstU7nKG\r\n7My9CEL0pPFMbz44cfp+fjmBJy9HUtYK1TVWD1EOOGyxWyIbGm5z9/n0oDt4\r\nFcFJIi1M2/mdLc38qQJ3r68/RvsJwvQ8XNhC0C3CBbR1NSZzdFt67n5upc7g\r\nIpmPmUqBZ10CRPuCHswrpvm+tGNTRRAc4Ik7yQ6YpIZyJnlXBkpcHJV5BeVG\r\nAX0FwycRr2QffdpyJloXlm1iZMYvKtEwRUtgxVZ98DpPk2ahTr25CzoEpVVi\r\n0sgTNC6DcBs7uJhutBaok0SIHhat+ktw05JReI/cDl3Mv7XsAy4rVu2OLHQ7\r\nbzt5S+vpuUPUAEmwVY0HcSvck/IfRBWA16q5FDkGqCUCLF13+HoESPmzbJ6I\r\nB3lAEk9JnKyP0POH+bMUl6W1C4+D7QLFBJ2YXK9601hHbwu/JPyGLhDMR90f\r\nbRC18zweuZFD7x4gbjyEQ9l3l4wa9pPdpbgeJrY0T+OPmZRSVJjMQ7wohCUR\r\nW8XCzMy5GmJ0Ew++FQw8VIr9VxPEYsbPrLk9IqiodSiydqfXTTXFjExO9BB2\r\n3SDbF5yJZM6ma+cycXssOwPCjLsBkLJX418=\r\n=J2s6\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.13": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.13", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.13", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "be942d8c669d89b82b2de8c5b294a5454451c3ee", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-q+ASvnv3pMisHaC0eocHtXYPz7PYSPIjE6ZOcD5Sr5A13IQ68nMtDk3czHU8pm4XHW7ETTw9NQhI/IIyBxxy5Q==", "signatures": [{"sig": "MEYCIQCR0qyBrOzbEMEWNBplo01JYiEtD/LjIiDcVGfV4p/xnQIhAODDeBjbiEsNjAlZbKrr/CwFGh7/L4FktuJ5S195j2FD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRnKEACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmotVA/9HqeBOcELC6WtX23VHHEY2HRxRLlbYlOObhJ0zmsw7Bej+YJ+\r\n09J2moNIVWBe2PXDkB2UrqHVralEaux7IJyH/4Bqz0enyLtYN04KO9a7lk10\r\n9vR+lzjXUUOaRnUFvLn39arwkDEJL7c1QtWgZe6MAtqXAIVHdwwM39IoTuQe\r\n6vQG9VJwKDfl27RFzOlwl8xBf0hU2DLP6ASJ+xoS3aCH9v4QnN3RbkU6WMlY\r\nH4pLAUws4ZE5Ux2oxO/g823L98a3ba8uFPQjk2Pz2/91F0xbvmKakeh97DAW\r\nH0R3ZvjqSKWe8DqngBmAODUM+5Tf2d9dmKo0x7uftcMKtR8ExQigAV83qGmC\r\n2uzu0zP9w7rHrWWKRTyW5W60ShcN8VVhGGPLaO6iJnMLhsprLJTPgl0K/RPu\r\nWGyOC2EU+a6xetuLUEldtNhDnhCovy9MaWmO4jT7XwOrOLFH02nBWSixrYVF\r\noLYHEeHArnEwB1w0JSby3tqg8h6JCkMdsqMUpwImmLrvHnjKvV8nnCzgjDdK\r\ntgTgZvIjDeDL0/rpfYo/u9DbiYCDoGGo0dhWNnJZqAcLaPw1E3JekHDFop0Y\r\nFhmlovTH0zGkW5/HoHOy0HdH2C7clLXZzuU1+L9McVVBL/fCInio9iE7FFwy\r\nZqsBWseCTExciMm+yJSybyOpWZlvFiMJzcQ=\r\n=w/2H\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.14": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.14", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.14", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e0d7addef65673178f3b88c570289927eedf3270", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-ixhx7bgFlHXt1xkw9YzZmfZVmhKe0uHutu+Bw43Yi4sqoveSmZ09TqJxqIMs5HcGkOb9JoUYEB3tMBo4y2v8wQ==", "signatures": [{"sig": "MEUCIBQLD8Fbk7AKXB3K++EWY32UxmBNMOgGxVlKI0J71NMuAiEAonLNxYMog9mF8GH4tLg98QOq6f+zw/Nbnv2OUZjeMXM=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjRqwiACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqoLBAAoKjOxMB26Eglwxq+pKulZnq0NrD+DRFfJCF1EmU8MPTiwcd6\r\nFD6mkJM8fHiT+txwLsxnQFpmG8RJCYx01oL9HFJOUwQ/lwcUI/jl4e0H78ew\r\nDLgxgTzEVsUJsEkVDHWQzBzGxdha6i82b9rN6dHOavNBCDeLaz4IIjTAtDSV\r\nKuZDMIYfuR8eJxA2taV/Rkt9ad5fZbuoJaW1eU1ghEmI+FiPtSGdG6GANTc3\r\ntXHg7b+B22VIEslq7Cyjj2lVTI9yb3Wl0oTn8EQakUYOaDfWnqRca+QIPXFs\r\nywwCHcwFb5YcIZdLT93xqjISxwokiS7hwPk/EzeXi6eeGKUeNmctcJHod0Pb\r\nMI6oJcr3fR8EdcljjYOCCtIc0Zn9m0rwab9fgTl4svkmQm+E2vgZ6LTP/q/T\r\noe90TGhXknoXZgLuY0IsCQ5vm6xVWHmxC1tLa88aaABm/zPwJ0SrYx2KxsAB\r\ndVI4DpCn+NRzr+0kGsM6nYiyEJwR33bDVh5r23fw2HtxKW/uRzJwYj1m3u1x\r\ncQ2A89I736pWb+7Jsm8IV/Geyz28pgBsCyiIs5jExqepjoSpjLRb7QNVGlcc\r\n/tnX94MkwFEgvSVDsKehNnyvYSizn6tk9XLOT2yLhzDratLp8mzRuKNVBCOR\r\nWB9ffOl/kWids4qYtv6PP0tzUQbEzzk7KoI=\r\n=McL7\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.15": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.15", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.15", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "e80c1521eea5942c508f72c7cf1ee734611ef074", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.15.tgz", "fileCount": 8, "integrity": "sha512-V4/AtVfQWayvbVWv149ASQnUE4NtNvqhm/AhxnVMOPiN2jH+qrXnfDzjZLuC6CBmr0aNa0BxYaBtEdvg9CqmWg==", "signatures": [{"sig": "MEUCID9F7HIOD69oZqSJ+GBO9UGJW7RWZO3SJr2w0Vz+ICA8AiEA6k2fo1KTv3PaRhDl6ewrI8xCFu3kn3fPXlX4zIv3yi0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjSUJ5ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmqwDxAAmHDNZAyDD76IdF5XdgTm2/kcOCEN6bm4zDqM3mIPTPIo+51o\r\nRNAt5/5C6AzZM74AjuViKvmaetlQcTkOA3gi/UhDxiWasaayBXa7RKb12f4m\r\n5ELvdXM9KY2lJ3L0ACwsg/kbTXmOwApE9lqR/71fkm22pAO6xnDiyK3M+jgM\r\nYjqq/ysCbQpsuF8SrskhFgc4cbX6EmBvhnKM7udI8ylVxjRIS5raPVmRl5v3\r\n73p0ciy2hJwm+h9IQcyufsg9p0qmPeYUDmtXodeElciR4OqedJ+NoCtanKJ7\r\nlmJB9NeiFQJ7x09f63DSXCZprOA/sqIXbe91EdOGIitJUZIdPZD93EdEhu2o\r\n16DpNFLkpPb1AbOWSnMBA5dGeXX5Jn3qUlqfP5Jm3wPuhu/cymjUv/KO/Of5\r\nX2EuiZ5pF+x8J+Yqy/M8p8qqCW+eNXz1BCZCycbDSn7ZL56S+B/rY0tfwOOs\r\nVdtLqaVABu40NC6gGhpk+Z++VU0rfwB0Tt1XSaovKtDRDf7FDpSVToZTVNsd\r\n0pudLZdnEtMPxyJkm42PNGf79nHY4VRUNywDjbgNH6pT5HmfZ4cWDrszRJHd\r\nAIgjJ9UozL7YivzH08OjwmqxQxIE5G1M8MvrU2fxyy9Qfbhr9koTy33HfwSC\r\nCMInyoaZXa3GyDd1RoGPNrDMsFBHkXW08cE=\r\n=7iY0\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1-rc.16": {"name": "@radix-ui/react-collapsible", "version": "1.0.1-rc.16", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1-rc.16", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "d29bcf54d471b1a46a877c8c77c3ecfef914eaee", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1-rc.16.tgz", "fileCount": 8, "integrity": "sha512-g9x4Xvu/pe+1jQBzla9JkAqsWaYSFl7PbIBq/UflY8DQIF2vWaRDGB8+Hxy7Z/mrbCR2qTXv1mrgYgqWTOPFsw==", "signatures": [{"sig": "MEQCIDxxY+7NxUpqgggg0ee6PCSSYiPFLkt0gn7ebq0a3b+bAiBrwuRK3lGmBLY2q8lEK0tdrN/wO4I4vTlOLx2KoC9wyg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55349, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTRejACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2Vmpk4xAAkN4zjOX/AuE9V2W1mx9sIW90NIUFp1z8jnhocXwKl45rQ2WB\r\nITX+v3+vDUEjFmzvomX9koB30gbpKkqPOK4egg4ji2c6mXFe61VJSFapaPSf\r\nV6lUe3UvLk6DeMx9Nn/xooh6fk5sbeS0Gtt02bte7D6hqCuUMTXU2CNH+Kjt\r\nmlIzLaXqtwQtNBsdXK+kiB38cTpF7EVG+hZB46EyUZkt2DL+0rw/AWBm0qJs\r\n1I8weZyRlii6Mq+D5Big7MPCuugZ6T9fdo6uhmVH6EC21eBekRIUZ5+rwQaM\r\nnFuvKC+HIZjywDg8FzfhG6fzwbSaNnZfqbXOHTBROPdI4MXwK+gu0G3yMgZ8\r\ng8DA9+DeIs4u+4xoRLCSXCDYz/GY/Zyad8n2DbT+3Ebgzr6HnvCM0jeNcC2C\r\nMEMFQGiNBUrGVIdBOSujam1U0YIDmc01BBqfJZJaN3SnRfbvlVR4fL0qLVY4\r\nSjWwS9z0Kel8cMJTtqNMVkTYTgyul1GNnW+Wu/3muJV+TP036U65k7buwH17\r\n9Yxi0URm7anWMJx2vrM02XcsRX0lJwqE0aAysiW2Kwf20/CyjCYWYJZsUeah\r\n+Z1gtZFptLgf6ZOXfW0zMdmjr69npeTpmrzm48lLdRkRECKq4ua21LGfiMEi\r\nwTZWNcnf2KVf2xptJp47uov0wDZRH9r+/eY=\r\n=1xun\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.1": {"name": "@radix-ui/react-collapsible", "version": "1.0.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "7184312ce18e703628b9ef75cdc44b896d6f06b1", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.1.tgz", "fileCount": 8, "integrity": "sha512-0maX4q91iYa4gjt3PsNf7dq/yqSR+HGAE8I5p54dQ6gnveS+ETWlMoijxrhmgV1k8svxpm34mQAtqIrJt4XZmA==", "signatures": [{"sig": "MEUCIHELIggKtfK+ZAC7otxAGPtn65mgdc2YXAt1shv1f/+BAiEA9/KtgN8E25TOBun0H/+0w6R9Lv0dMuhf81gbx955mfY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJjTSU7ACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpuAQ/9GKLc8A18FlRyW8iya7EaQ0caWohh3e/QsLgux1hVzybCpFxG\r\nT+hotUYxeINGuGpFwvl05I5NLLBwAaXv3wtzrulOk75bTYQLK5jGg2/QZ6da\r\ndWJeNsJq0KnZTo5fD9JBYWM/KYhnJaEckRVRRwVdfuFzX/gFdeG/iHdOfMt3\r\njrzyD8fG0942nAlvDTqM/r/WUDc4Ck257CxplH7pYQq+r98u1zx2/1OXuXjh\r\ng+spG9FgusVwX1WQ3AWuSYUe6KmCt8o9Xxu2UzKSwkBWWtWW+7M8A5k+DRKO\r\n/aRCbItP4rRrPK24ruKCx3uNAhFmvzEa/YTdldlh8ynHTYCca/f8zJzvscPB\r\ni+ipKtZNP8KW2h3uIJr0TtjVimCi57gWtKUjTrHB1VF/28KLuUOeHVEFJesz\r\n6VTUuthnjbwmyaqJr9d7gcdVGghSGQy5XxkiJgtZNWsIDFcR3nCbXPF7BTkI\r\nL1C34Mc2TMAgPcjovJKghg5ZtSjUps1h22E6Hv8b02T/4VRnm+dQOWiOS9GQ\r\nv1plgLzfKyayFWdfflXPumOBOxNXtZmkuMbrbzumT7Lo+nIIQdL1YpsWyuh7\r\nGPsM3nbC0MeOr6OdYv8qpG1OkVQs1B64EA0Mo661klE4pxwI1UJR/bbYaT7y\r\nCL/i9J9gufj9+veWiyMneuhWDOv0Ehm0qZE=\r\n=YbZ+\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.0.2-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.2-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "a2f34b9edab3a231d96ce19d8e116d8bf605a464", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-PgbPZwFSiK11TLGz9J5vlNkrslwIewjaiqWVUn3EY6wUiOyU3Ze2aLQtsFZ80VN1Bq7fF1cqUdN0SBZ1+h8mEg==", "signatures": [{"sig": "MEYCIQCcnZjQ5FjhLJ2XT+ZGU+AlWHo2BLksvqYVxejfKCFP1wIhAPcq9BLcx+A0p1oCWUf7XgjQwGvr6V7N2c4C+shdOSRD", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkBzfDACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmpoSw/+Jm2TeGk7c3x+oUrkDLJ/OZW+PnZMhY8ok9J75a1vJx9emuYI\r\nxSxsLpqkIULa8jdQ4B31GQnFgCNGzj20IrRkQJNb+yK5uJ8DSqOaXCv2xOA6\r\nk6HBpD1lQuZBSQ6XaXNpHlcKNKF7gQMd3PX6IgLKn07TtRxrD84+AP6wre0C\r\nFfQ5HCL/PxwzIdm1ltjbmIwvd/cqCJSS32J4XXyVIRg+0fh4pTxqYRzTDi4M\r\nhplHjkL0pgz+l0KAgkO6rsx+e1lIQMfwF1PjuQYSNZnQktRP5vna+62wuJao\r\nYsfAZRGvSfKTtOdZaRgi+4skQ9RuxpYjllNoKmzwpnwSduLgeX/jQdHv+IlT\r\nSztTR5Ji1A/l5RpZYpZXvVJz2tUAnqtrLZBFg4ohoxOazyjc9ODKqVtd2dpS\r\nqsg66YPEDrzxPXPSylbeNhkzJvQSjbjGJgz0dDI4CUKtoHKU5d0yk2rAHro5\r\nBgWUnqBwrmNMxMaSTgRby+JR7O9F4dU00PM0an61Qq4ZNWzBtW/j4tBfbS1H\r\n5t9YXzNO+IrsgqXSwJINB3qtKyh159MuS3G4jD4KDgccCW65Bz3CCYJU5pJn\r\nnr1+L8Vl0Dahl0GGo7RawGkzgVPOaWguYbEqfN3YBAH20yj7UKDOL6OJZgRf\r\nh/9M0sI54dbYBVamR0ZZy4OdTjYZkKNCbWc=\r\n=Wtkw\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.2": {"name": "@radix-ui/react-collapsible", "version": "1.0.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "0583470c7caa8cd1ab6f606416288d19b3baf777", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.2.tgz", "fileCount": 8, "integrity": "sha512-QNiDT6Au8jUU0K1WV+HEd4loH7C5CKQjeXxskwqyiyAkyCmW7qlQM5vSSJCIoQC+OVPyhgafSmGudRP8Qm1/gA==", "signatures": [{"sig": "MEYCIQC+9ukhu5hXoeIDiN5bejmRhUdSJIjTc9GVB7GrLyBXeQIhAKulEL9zhJol4xbaU0BZZWOdwqe5gKzlG6/8Dpefxsl7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55309, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkCJakACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmoOGw//f30k0TuvZng108W8mX2IkvXLB6+dlDzvsKY/Oa+UH5j85Y5m\r\noabrQoDwQ6iuVnjLk23MEENE2QDd4M+SofK3eaAjWkLdCyAV5uOGnQ5ACp5j\r\nKtZwvIsHnioDx6tiNXObPCjWKX8c2g1NUFAd7F3F/eIr0Vuw+M4gSdTGf4oq\r\nwgJM4PCQPzD9Dcy2R1NC41RU3x/EnXS+7IQ6xBMeJ+fc6QWpVJAs44Nqilsd\r\n5L6msJKuPU2pAE4qDJcKv68wTDq6g+f4JbBcuekS4xJY2PKZDgxycS5KdELD\r\nPD6A7mSKrdnqN5FGOkGkPU938VfNuGDb9VNU+2I6p+o+rmkmn18zlFHzFeQI\r\n0lcUnVgxtDeLBgpM3wprYXdJB7iw2ahqGK7Sia3a8t8gfhyvX3Y44XNZoMWj\r\ntE2gNxGQKrUK5mennyoRgunxDmQQNRX6RCxAHR9neqkVYEzSNkMwo4XhGDJk\r\nwphpTr0wW6/OIict4xmov9KzsFxEyrD/oRg60aPy2PV95EEUNylHy1wUN6nh\r\nUMMsw6agrIARRKzo7PepcPYLoatXFuQmqpTdJiIZ9MX+YYwHDHa6q3HbKhEi\r\nhenP+M5pUu3RqRm0AmyCJkHCgMyiG+3dK806d9Hs9XxEDlIgB5vFPglHdrC3\r\nQTFDbNWTjNIvxW8XKhcMs9IVnm1pjyzey5I=\r\n=yKW8\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.1", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.1", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "6f4eae2a7a7e87a3abb0c2277a1fe4b77ef3be9e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-qkStIINiTCgvVxPfP6iiop67wXLTFwuDA6fl5AzbX8dkJ3QtFDwPr4KF+fzLzF5Dx7GsdlLfRRSL2NSc1S1Hsg==", "signatures": [{"sig": "MEUCIQDmqShxVx/TvyfRIIGtBtF8B2SM5lHYjwZaCQuV54u1hgIgduI7+NBwgdbIMlWbxXcTi3L/2hcw3II5zlG7mgtfO/I=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55347, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkU8wrACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrqgQ//akr0LR6ECBHGzA7Fl4k8L1XzzDaobifLQfy8ssZU1XJxqiWD\r\nnK3g3mooZLn4ZebsPbMuM/U7Gt78IFAZ1pD1zh20urmcBA4sz3ALVbLAta7m\r\nOYka06/+M7/REBj+kfmf77GSVW1YanU3oAFPE/fIAnRaxwd0VLIGE1oHQCaB\r\nxjE35+rPO4PIJCCBOUArfoZ7ve+iS/gr/aLT8pSRBnIE1pi+L1JOS/9ht3c7\r\nqIXeloBtxVzhfFb2hr0bQSuXJDKk2LiWzdoTU7ErYk7TY+UC2QWaPxbv+AXZ\r\nrJ6hBQKXvtnKK29B7yOr/uEWGxFL8J3L1xS9W3Ativqq8Utz6aOa0EMkleXQ\r\nJaqoI1JTV4XiM2Pe3w34kSPCUOO/mVBx6/Mq3mm8ATlKL+ZWtrevP6LDtTyf\r\no+F1IxaWqs8SNvXZxkHe1NFUXURrBl/BvaEBimtfZGEJpSC38owGRsMbCHh0\r\nrKHM534yBE8uajy2OAEsfC8hnRyH1vYfPfivqyY1VZ0WBhwBIgm/ylRCZE4E\r\nIRN4BuwuNCP0XQe1fBH5N3YxIxkX4M6SgWucz5CrHhtcJTHoGMQvgSyl1fDX\r\ny4MAoYZh6F7PNBFqre+hpcQp3bh/lahxpbjaJP4gVozS+DO2gi7A6bXXe8in\r\nb2hh80EhTbUmBTflYCAwvOt4syuH97tZoIo=\r\n=bZlK\r\n-----END PGP SIGNATURE-----\r\n"}}, "1.0.3-rc.2": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.2", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.2", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "370678875804b645e164adec5f1e4a460ab3b120", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-uYrf7hiG3EEiMIgL1fjvcpdRK5CcBvlPdfAO0Lz6CnRRg52l1OTeGKzI+qgS/056GObMyhL1ruBn7RmCsk4HLg==", "signatures": [{"sig": "MEUCIFRnOp7Md2L82S3vrQ5oGFRJ9CPxcKB7ElMACOPc+TgAAiEA7atXT+LXSBaMrBePIstVQO/ux8pj4yRB462+tOJoaWQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55347}}, "1.0.3-rc.3": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.3", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "302cf59eb74069b920fcd9f8d640eeaa37e6df3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-a3iXXeKZN/8yvvAogAHlLIQKAq3RqJlZdQ5tKOf2LDxQNTvfzcAu/6bynyI3vFpDsJziJHaRWSrR7SACXKeJkw==", "signatures": [{"sig": "MEQCIEfw2UVvdmiEtxsF0zxJ0jY4Bo0oi4FFj4hwXcvL/T02AiAP/t0DGs24+UXPgTfufzHUTyA0zRVyY4wnls6aLtqhrA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55347}}, "1.0.3-rc.4": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.4", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.4", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "aa9c71d9037a1064f0ea893223ce2aa9000b13a6", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-nz9cWd2x72k6FPDhG8SXAz7dPcy0gy3vTrHkjmPI3y3HaFoVxX4qxOC/f7V1RqNEhPfoOxCi6L2mu8Z6uJk2zw==", "signatures": [{"sig": "MEYCIQDVOkWXPm6lc2so953tnzHk2t2NYTeEqDIvQRmq+a/lvgIhAN3CTPFPMLOsumfa87HpSbZIWDHXyarkRFP7TB3ZOA2a", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55347}}, "1.0.3-rc.5": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.5", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.0", "@radix-ui/primitive": "1.0.0", "@radix-ui/react-context": "1.0.0", "@radix-ui/react-presence": "1.0.0", "@radix-ui/react-primitive": "1.0.3-rc.5", "@radix-ui/react-compose-refs": "1.0.0", "@radix-ui/react-use-layout-effect": "1.0.0", "@radix-ui/react-use-controllable-state": "1.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "c5f42459b494b1252c8ee4570933f6b8a5fbfe36", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.5.tgz", "fileCount": 8, "integrity": "sha512-MXqAxG8R3grNOVlbpBZCLhs3FojBBTn2cNHUV9Hs7EYrRAtDo09vUjihexwwpAOmIwiEAi09xoVwzD4AEDJyZQ==", "signatures": [{"sig": "MEYCIQD21DMcy2MD4xJwN5Ol2wewDYBRknIrwB916MK4tQblOwIhAP/PU0IGKK3+gW0N4wP2Hd70cqnaN6Oj8heWwFGOWXYw", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 55347}}, "1.0.3-rc.6": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.6", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.1", "@radix-ui/primitive": "1.0.1-rc.1", "@radix-ui/react-context": "1.0.1-rc.1", "@radix-ui/react-presence": "1.0.1-rc.1", "@radix-ui/react-primitive": "1.0.3-rc.6", "@radix-ui/react-compose-refs": "1.0.1-rc.1", "@radix-ui/react-use-layout-effect": "1.0.1-rc.1", "@radix-ui/react-use-controllable-state": "1.0.1-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "adbf8d0c7d53452ee13474ecb963888cd04b4554", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.6.tgz", "fileCount": 9, "integrity": "sha512-WRnVNdCD2A2/Jtw19HOdOuU+I27v8Jtfz17fYwDIqKwdbImKy7kQEfzd+zBl2+4/fj71lf4LcC7rraGI5VWXvw==", "signatures": [{"sig": "MEUCIQDcvbXIVoTisknWXOK9xuC6P9yBExPdd8y6RiRuam1s3QIgB1WbgNJO863TT0PZwdLPmzNuDj4Ple9Z/Zr54t8Wf2c=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57370}}, "1.0.3-rc.7": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.7", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.2", "@radix-ui/primitive": "1.0.1-rc.2", "@radix-ui/react-context": "1.0.1-rc.2", "@radix-ui/react-presence": "1.0.1-rc.2", "@radix-ui/react-primitive": "1.0.3-rc.7", "@radix-ui/react-compose-refs": "1.0.1-rc.2", "@radix-ui/react-use-layout-effect": "1.0.1-rc.2", "@radix-ui/react-use-controllable-state": "1.0.1-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0"}, "dist": {"shasum": "030f8ff1220dd2454b392f4e5c20ac7148919b7d", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.7.tgz", "fileCount": 9, "integrity": "sha512-DeEycdFfGayVEOUp0htIYRprbmc0USM4H0slsFyIR2IpbGZrGV9oQBHGsdiCYsypA+3jtwgbrC1vsF26ZFcuhA==", "signatures": [{"sig": "MEUCIE5nl3Km1jGsj5PhEM4tbm+D2VWndfBVajut8V2H9DxwAiEAxZeKZT+0YHcaEXfZGR4PWXj3UJlFi1edHqIZ55f/mI0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57370}}, "1.0.3-rc.8": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.8", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.3", "@radix-ui/primitive": "1.0.1-rc.3", "@radix-ui/react-context": "1.0.1-rc.3", "@radix-ui/react-presence": "1.0.1-rc.3", "@radix-ui/react-primitive": "1.0.3-rc.8", "@radix-ui/react-compose-refs": "1.0.1-rc.3", "@radix-ui/react-use-layout-effect": "1.0.1-rc.3", "@radix-ui/react-use-controllable-state": "1.0.1-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9f037fbf8e644ddebd1b0c8149d0ea670ecbdd67", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.8.tgz", "fileCount": 9, "integrity": "sha512-YE0KPLut/SiRuw/9zZDLDt7yUuEzd17WR3omCXAf7QWzT7ppoLGclmtkgHRRUEiGBFUS24LnE9hnDQDi77w1JQ==", "signatures": [{"sig": "MEUCIEDGvrmBnEzGpQ1YLeAPJltu+lH1HzqaHqpZoig/Uq3/AiEAnQYDbnQ+d0mxqssa7zhMK/T22WzyBqkjnqIYCPcetek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57564}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.9": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.9", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.4", "@radix-ui/primitive": "1.0.1-rc.4", "@radix-ui/react-context": "1.0.1-rc.4", "@radix-ui/react-presence": "1.0.1-rc.4", "@radix-ui/react-primitive": "1.0.3-rc.9", "@radix-ui/react-compose-refs": "1.0.1-rc.4", "@radix-ui/react-use-layout-effect": "1.0.1-rc.4", "@radix-ui/react-use-controllable-state": "1.0.1-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "431f5804106697a126039a85fc8a6cbe1ed3187e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.9.tgz", "fileCount": 9, "integrity": "sha512-AdH/YcztZgseEBtZvmetj7REjaz0HlCpN1jZgIzGkuEo4/gjIydj7S9F09JH6ncVxocJ9Sj7CVwpsn/7mZUVbQ==", "signatures": [{"sig": "MEQCIDmM/7p/7BcQ/oryuAoJT+woVSCavW728JknBxTE9NsPAiB8jJOtsE78LC1S1C4r5RQmZ5CJeBpRqMTenTYkEBNJ+w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57564}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.10": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.10", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.5", "@radix-ui/primitive": "1.0.1-rc.5", "@radix-ui/react-context": "1.0.1-rc.5", "@radix-ui/react-presence": "1.0.1-rc.5", "@radix-ui/react-primitive": "1.0.3-rc.10", "@radix-ui/react-compose-refs": "1.0.1-rc.5", "@radix-ui/react-use-layout-effect": "1.0.1-rc.5", "@radix-ui/react-use-controllable-state": "1.0.1-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4c1a04bc27040399025cee1712393f263f4b770f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.10.tgz", "fileCount": 9, "integrity": "sha512-Wuq+MTGFrIRwU8Eu6SS3bV0/Y2vj4mbLhshjIwr/94BlVz/GVbIs5h5d8KN1kUB73NM3dqLFoBJfBs9gzaYisQ==", "signatures": [{"sig": "MEYCIQD3PVSfgWH2uACT9DiCSBjx0XzzNc3FSGh9UM+XFbbInQIhAI7gVvu539NGx47/rcib7cqrkD2RFBjCPFg0wiErjOk1", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57566}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3-rc.11": {"name": "@radix-ui/react-collapsible", "version": "1.0.3-rc.11", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1-rc.6", "@radix-ui/primitive": "1.0.1-rc.6", "@radix-ui/react-context": "1.0.1-rc.6", "@radix-ui/react-presence": "1.0.1-rc.6", "@radix-ui/react-primitive": "1.0.3-rc.11", "@radix-ui/react-compose-refs": "1.0.1-rc.6", "@radix-ui/react-use-layout-effect": "1.0.1-rc.6", "@radix-ui/react-use-controllable-state": "1.0.1-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8d7867dcd47cf369d9a6f32227ecc7d02eb83b8a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3-rc.11.tgz", "fileCount": 9, "integrity": "sha512-Q4cWXVRioOMuNJ3uSodFmLY/zwil4Eld7paW7TwGvldMK5yTCxupJIkwKIIksQgq3qOPAmLryl5VpCs2cReRng==", "signatures": [{"sig": "MEUCIQCad706FmPBZ7e81D8SaPmulMBNFP5wTtNA4nDwOhReIAIgYrdT2cSTxEFQWLibyJekqXacgMHgDEkWN9Aq1l+u3q8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57566}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.0.3": {"name": "@radix-ui/react-collapsible", "version": "1.0.3", "dependencies": {"@babel/runtime": "^7.13.10", "@radix-ui/react-id": "1.0.1", "@radix-ui/primitive": "1.0.1", "@radix-ui/react-context": "1.0.1", "@radix-ui/react-presence": "1.0.1", "@radix-ui/react-primitive": "1.0.3", "@radix-ui/react-compose-refs": "1.0.1", "@radix-ui/react-use-layout-effect": "1.0.1", "@radix-ui/react-use-controllable-state": "1.0.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "df0e22e7a025439f13f62d4e4a9e92c4a0df5b81", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.0.3.tgz", "fileCount": 9, "integrity": "sha512-UBmVDkmR6IvDsloHVN+3rtx4Mi5TFvylYXpluuv0f37dtaz3H99bp8No0LGXRigVpl3UAT4l9j6bIchh42S/Gg==", "signatures": [{"sig": "MEUCIDQYEaf3pYilFlXPqeYwpsLA2saeCoCMw6CzpVgCSQ/6AiEA1ehjweer3iVu2FJ70FAij8KkmrlnWJZ+3z0SHBD06AQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 57491}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.1.0-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.1", "@radix-ui/primitive": "1.1.0-rc.1", "@radix-ui/react-context": "1.1.0-rc.1", "@radix-ui/react-presence": "1.1.0-rc.1", "@radix-ui/react-primitive": "1.1.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0-rc.1", "@radix-ui/react-use-controllable-state": "1.1.0-rc.1"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1ab3668fb74fc8b45f353da41bf13ca43edea261", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0-rc.1.tgz", "fileCount": 8, "integrity": "sha512-q+pW+iI1UL7Xf7JheK15nzGIYyODPE1RxgOx1rMf7a69TuDSfwo11gAcXPffsIwygcia8RiG1M0pMwWKEP+jxQ==", "signatures": [{"sig": "MEUCIFT0KH//zz4vxx8/f69vvNRBkkBKmVuiK349xxR4iR12AiEA+SSFO9NXUVikRP+AHiwdL+lMJ0XDi8wSQtDe4h6lSf4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43517}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.2": {"name": "@radix-ui/react-collapsible", "version": "1.1.0-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.2", "@radix-ui/primitive": "1.1.0-rc.2", "@radix-ui/react-context": "1.1.0-rc.2", "@radix-ui/react-presence": "1.1.0-rc.2", "@radix-ui/react-primitive": "1.1.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0-rc.2", "@radix-ui/react-use-controllable-state": "1.1.0-rc.2"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e3d92da3d45b050f8600e0c9b672bdaaddcb2b9c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0-rc.2.tgz", "fileCount": 8, "integrity": "sha512-m5l5t+F/bMttwm7uZ8fmeVljYU/3suWdXZn2ssY4pnS+Js1JwVQq7U3fieispb6ChOoivHQE7rBXkRkoUFlctA==", "signatures": [{"sig": "MEQCIAWysOig3m/d1RczwfMLLvr0Wi7srRcDQxvU2XXJ2/jnAiADKzn1U5rG4Ja7FHkdw8FhFALbwRrrde+jGweRknj1LQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43549}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.3": {"name": "@radix-ui/react-collapsible", "version": "1.1.0-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.3", "@radix-ui/primitive": "1.1.0-rc.3", "@radix-ui/react-context": "1.1.0-rc.3", "@radix-ui/react-presence": "1.1.0-rc.3", "@radix-ui/react-primitive": "1.1.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0-rc.3", "@radix-ui/react-use-controllable-state": "1.1.0-rc.3"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0", "react-dom": "^16.8 || ^17.0 || ^18.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bd27c5854b12f02207c54ba7c9d0b140d5019f76", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0-rc.3.tgz", "fileCount": 8, "integrity": "sha512-qqLIH6hYi0c/pZ6Sk44C35LF6tvT6WfYno2FgNequufw6+l3oKluXF6yrYbyYscT/JaEVz/APScQ318sj6gu3g==", "signatures": [{"sig": "MEQCIHZNTD4W1J/n/+vzpwni6r9TIiDiPRfRZ4L+A2fDjWJxAiAFRVaDgMd0S3HmE6nat//GYVObFp+1fNvLYnGD5viwxQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43777}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.4": {"name": "@radix-ui/react-collapsible", "version": "1.1.0-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.4", "@radix-ui/primitive": "1.1.0-rc.4", "@radix-ui/react-context": "1.1.0-rc.4", "@radix-ui/react-presence": "1.1.0-rc.4", "@radix-ui/react-primitive": "2.0.0-rc.1", "@radix-ui/react-compose-refs": "1.1.0-rc.4", "@radix-ui/react-use-layout-effect": "1.1.0-rc.4", "@radix-ui/react-use-controllable-state": "1.1.0-rc.4"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4bba5c57405d564ed95d708a64abef8a0caa9ee4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0-rc.4.tgz", "fileCount": 8, "integrity": "sha512-+lZQ/eV+kU1DbD5pagjZjfqRvw9QdMnd7s+7C7IHV1jbReaRRR6tUWd2jsbUedKOM6d6SWy+jZEnrZuKiI2U4w==", "signatures": [{"sig": "MEQCIEGrSDlueOaLxUf1tQvqh1wI2N43QgDgjk58c2gM16IUAiB8lB/jNGKrlz1UrnuPR1AsmYAy3B4K5wgNQ4GEuI1cDA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.5": {"name": "@radix-ui/react-collapsible", "version": "1.1.0-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.5", "@radix-ui/primitive": "1.1.0-rc.5", "@radix-ui/react-context": "1.1.0-rc.5", "@radix-ui/react-presence": "1.1.0-rc.5", "@radix-ui/react-primitive": "2.0.0-rc.2", "@radix-ui/react-compose-refs": "1.1.0-rc.5", "@radix-ui/react-use-layout-effect": "1.1.0-rc.5", "@radix-ui/react-use-controllable-state": "1.1.0-rc.5"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fdfe1922202cc5f5a9907411e1904afce75e755b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0-rc.5.tgz", "fileCount": 8, "integrity": "sha512-wgwgezXuGCb3qU/dtPiK0ukzpomObQjA4p4mXvl0v5Us8rnYMGq35h3284pLAn/RD3tnpleMisUWcXnd8WuruA==", "signatures": [{"sig": "MEQCIBRKbYWDNxB0Fspw8O1dMUG65ca6BZBboCjZXk6YTfTaAiBrh6GfNIxoaHnCp2ef6zOAM7zfF1/RIKk2Vr1NNAEUxA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.6": {"name": "@radix-ui/react-collapsible", "version": "1.1.0-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.6", "@radix-ui/primitive": "1.1.0-rc.6", "@radix-ui/react-context": "1.1.0-rc.6", "@radix-ui/react-presence": "1.1.0-rc.6", "@radix-ui/react-primitive": "2.0.0-rc.3", "@radix-ui/react-compose-refs": "1.1.0-rc.6", "@radix-ui/react-use-layout-effect": "1.1.0-rc.6", "@radix-ui/react-use-controllable-state": "1.1.0-rc.6"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0b15601f3c9b2867c4e6591f157cb6848dc6fc57", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0-rc.6.tgz", "fileCount": 8, "integrity": "sha512-QV8v0TSuPp5P0g7vnN72r4yhROGC8THIlNcisWiBIotj/rsAlcEEQOwNoAK7n2zafC7ApKwEa9Q5cb4V6Xl4ag==", "signatures": [{"sig": "MEQCIDO2FHLMLW+AvaUNFlO+9Cc+AlbTXNtqF4Zm4lbCPtMOAiBPGNRbAOmsp5FxFHfJFsQLIQ4OzSgNT1WdI907Ndijfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0-rc.7": {"name": "@radix-ui/react-collapsible", "version": "1.1.0-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0-rc.7", "@radix-ui/primitive": "1.1.0-rc.7", "@radix-ui/react-context": "1.1.0-rc.7", "@radix-ui/react-presence": "1.1.0-rc.7", "@radix-ui/react-primitive": "2.0.0-rc.4", "@radix-ui/react-compose-refs": "1.1.0-rc.7", "@radix-ui/react-use-layout-effect": "1.1.0-rc.7", "@radix-ui/react-use-controllable-state": "1.1.0-rc.7"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "be0d8bf9592cf2b9d84c9d89a81a5a2256a38079", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0-rc.7.tgz", "fileCount": 8, "integrity": "sha512-AvH67xIwRs4SkvsCxB9CeQ24rUB0skldQKLWl5j5c8Ovt3pjwnCJq0LCFZPMYGFsT0YMrw2HWwGKWN41Up3Vwg==", "signatures": [{"sig": "MEYCIQDK6iT9cllJwKjtLzOxmvzM1USvKVq6A8OWkDDHiLOhIAIhAIlAQbZKg33zKBXfVkRfcFe+kXQc7ZzCgfnA77GXoUBb", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43539}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.0": {"name": "@radix-ui/react-collapsible", "version": "1.1.0", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.0", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4d49ddcc7b7d38f6c82f1fd29674f6fab5353e77", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.0.tgz", "fileCount": 8, "integrity": "sha512-zQY7Epa8sTL0mq4ajSJpjgn2YmCgyrG7RsQgLp3C0LQVkG7+Tf6Pv1CeNWZLyqMjhdPkBa5Lx7wYBeSu7uCSTA==", "signatures": [{"sig": "MEUCIFTLeeQetkWSDc2dzwwNYZt8LcGrftJE77kJbkODPXTAAiEA2zgdwvtjdtspP1TGvShzjMn9vi++qBWsgvM+aTIpx8s=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43466}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f9bcc8f95bef9fbd8b5ff2dd8e037718632e09ed", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.1.tgz", "fileCount": 8, "integrity": "sha512-lLnHeVtYBSE/7nz0K/NfxDeoQa2a32wJwCGrZ7sdy7uU6SSHAdDKwO1tYZnAhhUQgyyKbjuAHbPN+yyzgRNJwQ==", "signatures": [{"sig": "MEQCIBcBn1BC1U2SuGzvi4yGm2BmlCdIjXpl4yr9eLifw1lZAiB2OunWcmPhfh3c0PeisKBJuIetbNuExBNW5GBtW8cf4Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.2": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.2", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "521a7b0113d601ea3bd1bff0a1bcee8dc6c920ae", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.2.tgz", "fileCount": 8, "integrity": "sha512-uMOjzb8JVyL6EO34l7bM6iY94D9nxhQ12d4YX1BMwl3k+Rr8jXLAOgHipX+xoOUVrin3oPDout84WxzKK64PnQ==", "signatures": [{"sig": "MEUCIQD4sn5D5DO0piys1hBoLmlek39dPhKHB9yn6eQ7+3WviwIgTACEj/hlBJqg409U6y/rOYBXzm/v5s9mYq7wjZ+A1bU=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.3": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.3", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7210a8b13c7bd472bbd6ba93924e28b8d7a469f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.3.tgz", "fileCount": 8, "integrity": "sha512-T6EsTMtjl46f+xZkeMA6FJ+Se/jwmtNYuBOtv6p2NUpfLrv/bJyDfvy0Tn2lMyJcJLXBVRdKVZSAafBK29w7dA==", "signatures": [{"sig": "MEYCIQCIx4sjSc7kqdg1/WEg9M/OPw267dIa89wYzCww11ZloQIhAI7NAqyEVRm23r4ABB0Xy5FIwPmWjuLR4sXeC+22Qlwj", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.4": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.4", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "42b00363e577c65e8e7bbde7669f62d5dd27bbc5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.4.tgz", "fileCount": 8, "integrity": "sha512-ZnpZBlnRnLanfbloDwqkHm6jBgDhmCbMyvPMtWst8e3Vnv0ZeKXQoqQXrg/Bj5ceZI3spyQmY86GZPKCAyuD2w==", "signatures": [{"sig": "MEQCIF8z3Hob5NigroXGUZvjrs4VJFQ+WSaDdWiDAK8Fz1U1AiBHAND7Zz9v9+uNGdaxwKN3LclV9C7ONozCF7ywkIQ+rA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.5": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.5", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e58c94e194d79dedfa26565c4bed3cb54714c1c3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.5.tgz", "fileCount": 8, "integrity": "sha512-RlMN4qmlG/R2YqD/5f08RVK4vfRqoWuOzPfJ7/vC755JiaxVtc4vPuF6oYhuUqJBig7jkHs9pbC3L7pKVymW+g==", "signatures": [{"sig": "MEYCIQCsT/Yx54+rA<PERSON>nzaZ11HakeZ+7h0ROmnzMwh7cyIekLUQIhAK0vCXapgP+OfSRymDxti39acznkMnM9LEZmyBsyR4F7", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.6": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.6", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "327ae2515be4e8ffcfbfe74fd1f877bf199bca42", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.6.tgz", "fileCount": 8, "integrity": "sha512-A2bwMrqaqk4uUgljf/mAonHx/BqO13qpML3/6hzVKJToapev0sdj6kI0pHGWsViNlm/AmiegP8rBKZSlYTgghQ==", "signatures": [{"sig": "MEYCIQDkDMR+XRU6t26ZQfEK72vVHAG0F1GslMZUDx1vugw7twIhAJ1TQ7UZ/ZtZsCUg+zkeTocYKaNuSijIRq2rsnAiluIn", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.7": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.7", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6cfadcb58439becd423f2277a973e7d7209cd2d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.7.tgz", "fileCount": 8, "integrity": "sha512-/WQac0CAyb4DCNdy+iv0eg41p7NCA2DJaq63blgxfel/38kDu7Y06rHsWsQ27qBKzbOg2cAsqVbkGiOaJHZorw==", "signatures": [{"sig": "MEUCIHDuD3XZhd77Ux83+1T4+N1UtZYF4FoYV2Y0dOuEH3KXAiEA3ke0V++ycUDTq9utibujpa743pVO0hx/XjPNJeR57zc=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.8": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.0", "@radix-ui/react-presence": "1.1.1-rc.8", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "86036633616e6207284deb3d84178c8ae12a8e9b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.8.tgz", "fileCount": 8, "integrity": "sha512-3vxy93prV7cHZEdBAw5QarPXaVxMlvuH9P7IjQH0wyarn82x5lmdjKiG7sjhW/7RDRtTlCCQEm6smQHIOUDhCA==", "signatures": [{"sig": "MEQCIE1v0d39Gq+3wTv3/f8tbmnt0UnYcdM22NEQJkVimVu6AiAHOLcOVkVQ85ftGl7/cThQynonw5dpOxvs+iu8r9LN7A==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43504}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.9": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.2", "@radix-ui/react-presence": "1.1.1-rc.9", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "bbad01c34be3e151f70207e01f409922761c0daf", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.9.tgz", "fileCount": 8, "integrity": "sha512-9RdOEPr2JkuRSaMQ1i+Prx9yO51ZtxE7cF7zUA/eOkiKefHujbMWuo4x5Of1fIe82xZ/kvom5g6Jv6dWNIaCjg==", "signatures": [{"sig": "MEQCIC/Try7xwuiu396CoZQ3yLsp8T7XUiJVQX/c0+rv+csxAiBpkeR0mIDaLg68gwAe5evaKwwuPx6xYRF2vd8beX4lUA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43509}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.10": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.3", "@radix-ui/react-presence": "1.1.1-rc.10", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c108b27440a174236d8480b0e28eb15edb56102a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.10.tgz", "fileCount": 8, "integrity": "sha512-4+UcJ2oAPkZ3URwR6zCE2E4OWxIuvNLVxFLJ3HhclPcbw2JqbA2frImIUV5bdLCZ/rYLerWwKWSv1hDzDbYj4Q==", "signatures": [{"sig": "MEQCIHrDEmW2dfkoT64dHMURG8jkbQ21DDYRMc42xYwdc4tUAiATpMhP6zAJjJLJJmdvpHLuFFGecz+i9EZPrJ/iooqpsg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.11": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.11", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.4", "@radix-ui/react-presence": "1.1.1-rc.11", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5b274d5b520841376df2fddec3bb33c879c21e30", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.11.tgz", "fileCount": 8, "integrity": "sha512-hI9cqQhJsQihcMHXII37zSjeNNNhTT975m9E20w2NXCnfIZRjSZwhyiHdQoKyGYUvTJ77blhgXiCHWwL4DbubQ==", "signatures": [{"sig": "MEUCIBdmWBVJ2O7ffCgtWbfHKoEdsx0k+5zUspzGIaKRiX1LAiEAq0V+SES+ugNlusx4m2Y4D3tbYZ97gdz89XK36TvO+GI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.12": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.12", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.5", "@radix-ui/react-presence": "1.1.1-rc.12", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ae47245ad98928a9347dc4f2c238ea95ae0e13af", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.12.tgz", "fileCount": 8, "integrity": "sha512-xZ8/e0QiNW66YFplsrmksdvFQYUfmJI6G3/U3y65cncUi61hnxxYLNnV7FP0wB5/472ap/ss6lwMYiRZISx0SQ==", "signatures": [{"sig": "MEUCIQCeh3fTw8kMUobxYJ9nWW4QooLG+7zjVm/4z53AJaWsFgIgRQ+3SoiNG/T24fR6OA9EkHjvrMoaAbZuRbDuVMkNm8Y=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.13": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.13", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.6", "@radix-ui/react-presence": "1.1.1-rc.13", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c9edf5cb15e56706a18017f155ec7e42aa0a2785", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.13.tgz", "fileCount": 8, "integrity": "sha512-QG5Yoww16aQZglEOXON+OliIOTR2Ea5+MFOITFimxR4H96ZWfncjsWBCasjNGRm2gWu5Rluv1hHE89ImxapsfA==", "signatures": [{"sig": "MEQCIC6GaH7SJLQbMexpVTjAs5f2LBzGRnTZ4GcRruxirZtOAiAu85LU126UuA6or3pqhoQt8PddfqvZsMReubb+E1qlpw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1-rc.14": {"name": "@radix-ui/react-collapsible", "version": "1.1.1-rc.14", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1-rc.7", "@radix-ui/react-presence": "1.1.1-rc.14", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "fbf5f654cae87dcb5692a87fdf0b6ef93668e5c5", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1-rc.14.tgz", "fileCount": 8, "integrity": "sha512-2uABN0ymNTBHzdfaiTu4oWa7rjHBOUCzaHaiCxSOxVafltea1jwzI+qOWdabt3ohZfz3vEUTbkYiEsTedQqJzA==", "signatures": [{"sig": "MEUCIHmmZ1JD/ydRUx+KX5pdxlwz1nBYX5o1ZUryCrWZONHvAiEAr4/KSGtonSImYOarFRDEpx6BYO9uD93pNv48GbJckO4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43511}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.1": {"name": "@radix-ui/react-collapsible", "version": "1.1.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.0", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.1", "@radix-ui/react-primitive": "2.0.0", "@radix-ui/react-compose-refs": "1.1.0", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1382cc9ec48f8b473c14f3779d317f0cdf6da5e9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.1.tgz", "fileCount": 8, "integrity": "sha512-1///SnrfQHJEofLokyczERxQbWfCGQlQ2XsCZMucVs6it+lq9iw4vXy+uDn1edlb58cOZOWSldnfPAYcT4O/Yg==", "signatures": [{"sig": "MEUCIByWGGhLQQmS3TD4FvGG05EIPGeljlGnqdWRCm1OOKbPAiEAqkxFRmhexDwBhhrdnTS0E7gLxzNHEAOu7Lw8q668Zos=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43466}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.1.2-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.1", "@radix-ui/react-primitive": "2.0.1-rc.1", "@radix-ui/react-compose-refs": "1.1.1-rc.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5d2ea8d1b29bb40cf5c617054626846124a8dcf8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.2-rc.1.tgz", "fileCount": 8, "integrity": "sha512-ZUquD63OGa5o96FMy5lebpVWGwyHaWEgdOVzetttUhlqzOR7L8wQlgGX0zRS/arfXd/afQrg5FJRr/U0/l2FbA==", "signatures": [{"sig": "MEUCIFLpybSY+bOOM965zwOmbH6IjLmh4/FNw5J3DvJuqHshAiEAzQB5UAnZHlmqTQXOvpnksoPODTbHpCluA0CwacY7Ufg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43289}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.2": {"name": "@radix-ui/react-collapsible", "version": "1.1.2-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.2", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.2", "@radix-ui/react-primitive": "2.0.1-rc.2", "@radix-ui/react-compose-refs": "1.1.1-rc.2", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7ae98b0d35a7b996396ef3ab534b754b4a5e45e8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.2-rc.2.tgz", "fileCount": 8, "integrity": "sha512-8AmfkZLZaDZtmyngpIWuOSuSIpr+66v+JG5ndqb9g+3nfUo53qqDVVkaG61xtWp8dFhA6N0hR6X3ybatR8Ug9Q==", "signatures": [{"sig": "MEQCIGouHFrS5PKHMUoQL1AUCKGQJPkdypatjw5ei35nOZtgAiAz+QWzTAob2CInqgvwS9zlM2sIvJmoS7yVd7PFgKlDtQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43289}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2-rc.3": {"name": "@radix-ui/react-collapsible", "version": "1.1.2-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1-rc.3", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2-rc.3", "@radix-ui/react-primitive": "2.0.1-rc.3", "@radix-ui/react-compose-refs": "1.1.1-rc.3", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9b5ac47b9c7d1cd7def44a1a3a263f1f21701271", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.2-rc.3.tgz", "fileCount": 8, "integrity": "sha512-2JBrZWqyoVI7LaWT/2hmDiiea8H39OgEFAjsFXrsNsQN8jDowBJwfvnnRw3dpvD7E7HeN1isH44V8rg5P13sHA==", "signatures": [{"sig": "MEUCIQCpgUTQ7LMdHiqqZ07L06ZkHWvF56dfVpsQ7tlY8QPPkwIgBe3OLAqB/3FdHhZdx9yox42qvrXhsWc9IoNy3AO/2j0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43289}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.2": {"name": "@radix-ui/react-collapsible", "version": "1.1.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "42477c428bb0d2eec35b9b47601c5ff0a6210165", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.2.tgz", "fileCount": 8, "integrity": "sha512-PliMB63vxz7vggcyq0IxNYk8vGDrLXVWw4+W4B8YnwI1s18x7YZYqlG9PLX7XxAJUi0g2DxP4XKJMFHh/iVh9A==", "signatures": [{"sig": "MEYCIQD3DaPcTQOqMzga7GS17t0YDpgwd3odX3/sXioaU2GLwQIhAIaxvCkga3LgMf8bomaYl3NVyLkPHTx30gXCvOj1nU5e", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43236}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116175529": {"name": "@radix-ui/react-collapsible", "version": "0.0.0-20250116175529", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d46845fbb9aa9a7d4dbcdf2c1b5f4c6bcf57c187", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.0-20250116175529.tgz", "fileCount": 8, "integrity": "sha512-xYsZHQXGPHMli+nOC/Ws4NfaDdo1F9MwmPwGb6U5OU6RpyTgMISP8KgZUyTQUuNrjB9Oc9CK054IeseIchmdyQ==", "signatures": [{"sig": "MEUCIQCZTlq/+ncBT7w/IRplWNYKNsXYB3LOygiYIsHn4iacyQIgPc9R0L7HotkxLX64Q5liqbUE9JMWDlevMN4fOgcBgC0=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43269}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116183145": {"name": "@radix-ui/react-collapsible", "version": "0.0.0-20250116183145", "dependencies": {"@radix-ui/react-id": "workspace:*", "@radix-ui/primitive": "workspace:*", "@radix-ui/react-context": "workspace:*", "@radix-ui/react-presence": "workspace:*", "@radix-ui/react-primitive": "workspace:*", "@radix-ui/react-compose-refs": "workspace:*", "@radix-ui/react-use-layout-effect": "workspace:*", "@radix-ui/react-use-controllable-state": "workspace:*"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7890532680e770a2743641b885b4a1af3dd78860", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.0-20250116183145.tgz", "fileCount": 8, "integrity": "sha512-6hAXdiO54harhCz34tsAgMrJfoRf8l44jlKeXXO3OpH6T53+3NlOpjGWULeTLA5fOvwRQCkF++FKun5Nf2li8g==", "signatures": [{"sig": "MEQCIGJhDb6qdzNJXxYOE/JwsIwouWqy2/ycYxCXs6Msw8IOAiBpj5WpSDFVZu+6UKHZjt4uZnZdMoKDFgxODEeT8lKSfg==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43269}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116193558": {"name": "@radix-ui/react-collapsible", "version": "0.0.0-20250116193558", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116193558", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f1f0c8b402320dcf06ed784c2e059190c4a352c4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.0-20250116193558.tgz", "fileCount": 9, "integrity": "sha512-knQk2JVr9o/hsKxwjSV7UhhNPKuYdzJE+uVwsLEdvCvDEMzTan0O8VQUO9TMPHlfpyYdvnDdmy/JvT4iYVCUmQ==", "signatures": [{"sig": "MEQCICsOrbVVMI9iI687UokJvGTbXRkanjfnErNLbympoVyFAiADkuOoD9BieXjPeK7tZDG+83ESd5k3EldkJG4wQMLo5g==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43388}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "0.0.0-20250116194335": {"name": "@radix-ui/react-collapsible", "version": "0.0.0-20250116194335", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "0.0.0-20250116194335", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6451b116cc38362084edbdd2566bdef23288d1e4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-0.0.0-20250116194335.tgz", "fileCount": 9, "integrity": "sha512-WcrL75Hes90OVwqEOKjbJoN5RtMj7vRYFvJ2jd374lbImfclxtCXJQ5PaC8ta3sgJJRyvxMFFIWEwKPJkzdtSQ==", "signatures": [{"sig": "MEQCIAdOI/ITTawHA6bqyy4J3ZCfA2ZxqgCYY1JmAl40lz53AiBZZMZlvvcW0ZeYV+mRumIM27zJX6Mw8oDlXHivQs0UPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 43388}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.1.3-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.2-rc.1", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5fbc3115d8c2fa3ece4e345b86ec22581b7da9d7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.3-rc.1.tgz", "fileCount": 8, "integrity": "sha512-pELokp4v8ZR8Ej714wafuR4pwO++Kd1sMYWG11J7vHaBWgTlfWupwHhmkva+ILnXctzsgZrQ/eUXgJb6vNJhog==", "signatures": [{"sig": "MEUCIHNpO9hZ9CWs4E29TLSrwdFC8OKyoSEiW3MEcnlqt3Z0AiEAo7U/BffWihaGc5W/Bo+LGzKJWhirVUrq4RKAYdgxBEg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43487}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.2": {"name": "@radix-ui/react-collapsible", "version": "1.1.3-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.2-rc.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ce217659908825fb1abbfb071ed01611b4fec6d9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.3-rc.2.tgz", "fileCount": 8, "integrity": "sha512-mKsvipVa7D3ROLrRLx1r8y30jDMQrd/LdPCJdhgt/Ku8cDq5kFv2909rNs41szmqVqk8UG5uHKCHRSUnbZ0iUw==", "signatures": [{"sig": "MEYCIQDIDmooR6F3/RE2Em7pgPV1Ygnbi1nhhakvL02sKcuFtgIhAK2qQ/qdkKavGEYRl8S5+uYu3rWHZ7SkVPu9b4Rxxtyi", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43487}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.3": {"name": "@radix-ui/react-collapsible", "version": "1.1.3-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.2-rc.3", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5bacdd122959fe5840218e827559c8c6ff6fa861", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.3-rc.3.tgz", "fileCount": 8, "integrity": "sha512-XvZoPqGB5Ya5rxa717KKzJUzJ1wbqAixxDhY0+8Srhmd7kdXo8faxX5r0Luy6k2nNiRue5cjlU/vLpvlOtcdSQ==", "signatures": [{"sig": "MEQCIEJVFo+2iYSsQnSQvQr3z2jjBcB2BBa38cyCodCiESPLAiBj1C+vLAb22bXaK+AoNvoQVkSAYJ79H4MNHvQcdZTSXA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43591}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3-rc.4": {"name": "@radix-ui/react-collapsible", "version": "1.1.3-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.2-rc.4", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "07ce48f5dd8b964a30967b0cbf2c9ffada6a42f3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.3-rc.4.tgz", "fileCount": 8, "integrity": "sha512-AFCmtaSzx990QF8/8hfiYWadp4+Ql459elBr/mim+5f9GPN8PgdPkbAP2Qu6yNKJLdhxDrF2cJ5gMh/cDWaElg==", "signatures": [{"sig": "MEYCIQDjD3Xbhxquw9pXJMEofMJl7LZLMk90bqZJfO6pVPR6wgIhAP9uilH8CIoyo9bszXYmjsaObDXWbCPcvWdmdLjM4tT2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43591}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.3": {"name": "@radix-ui/react-collapsible", "version": "1.1.3", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.2", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "522a5749646b7a393c9e9049165a9a6dfa924e91", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.3.tgz", "fileCount": 8, "integrity": "sha512-jFSerheto1X03MUC0g6R7LedNW9EEGWdg9W1+MlpkMLwGkgkbUXLPBH/KIuWKXUoeYRVY11llqbTBDzuLg7qrw==", "signatures": [{"sig": "MEUCIQDXxdgVW4o/KzhGf8LX/wKnPjZCAQsVLgUBzaMnqwS44QIgZl/Wyn5CrxCfDbyit2lHSU/7CCYBgvAWuHHVs4Qfemo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43553}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.1": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.1", "dependencies": {"@radix-ui/react-id": "1.1.0", "@radix-ui/primitive": "1.1.1", "@radix-ui/react-context": "1.1.1", "@radix-ui/react-presence": "1.1.3-rc.1", "@radix-ui/react-primitive": "2.0.2", "@radix-ui/react-compose-refs": "1.1.1", "@radix-ui/react-use-layout-effect": "1.1.0", "@radix-ui/react-use-controllable-state": "1.1.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "618070eb6a9e4c1688ff3fd00346dd78056dc774", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.1.tgz", "fileCount": 8, "integrity": "sha512-PzWUaDBHDIy98nChnZ4In8tNWl82aXqGf/t+uJOWRMsNSLCeY5VGXz1BvR1QDwsu/vefJPcGyhoU/qzGJCzWXA==", "signatures": [{"sig": "MEUCIA6ItnFQADz8nf95bQ1u8HcWX6sISYMDQba3UQMIf+JAAiEApgb22KR0c3Ro8pXwRXryWvbJbGXoAOIF1XP0iP6PC/8=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43591}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.2": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.2", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.1", "@radix-ui/primitive": "1.1.2-rc.1", "@radix-ui/react-context": "1.1.2-rc.1", "@radix-ui/react-presence": "1.1.3-rc.2", "@radix-ui/react-primitive": "2.0.3-rc.1", "@radix-ui/react-compose-refs": "1.1.2-rc.1", "@radix-ui/react-use-layout-effect": "1.1.1-rc.1", "@radix-ui/react-use-controllable-state": "1.1.1-rc.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0854622b3cda5c2f92559c203f4baaa0b14fc83f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.2.tgz", "fileCount": 8, "integrity": "sha512-E+hbjEhgnfDaL2SmxKm85DPCHuKe6HfxlEe2a45MZXXfRA4/oWNzjCwM/VE5Twx/jD4TQa2Nn6iBNi3Os+ExFQ==", "signatures": [{"sig": "MEUCIQDWNmvg+TMmqPQCV6dZSXak5OynYZbd4sp38APqek66qwIgHMd1qyMN4tbU5zDzU+rkF8fuvOVpHBlEMDq6A1Gxdpk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.3": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.3", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.2", "@radix-ui/primitive": "1.1.2-rc.2", "@radix-ui/react-context": "1.1.2-rc.2", "@radix-ui/react-presence": "1.1.3-rc.3", "@radix-ui/react-primitive": "2.0.3-rc.2", "@radix-ui/react-compose-refs": "1.1.2-rc.2", "@radix-ui/react-use-layout-effect": "1.1.1-rc.2", "@radix-ui/react-use-controllable-state": "1.1.1-rc.2"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e17fdd09f7abb7f7e15f27f6735f287c40966b2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.3.tgz", "fileCount": 8, "integrity": "sha512-ZIrSz4iUTyf15h1HlIZt7fHCMwWOvv2geTY1FI//ulEXKosMTAFLmKIUmu4YZ/wDf6p6hVG/FYHB+Xhhq85l9Q==", "signatures": [{"sig": "MEYCIQCN24on9th7xPD1KlERH1Eb3aLlqQnR+YNZPSS1af0NkAIhAKfj3awMNCl0HbFeVNMSRDY/+9SEaIDqJhrTEx3EaRf2", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.4": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.4", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.3", "@radix-ui/primitive": "1.1.2-rc.3", "@radix-ui/react-context": "1.1.2-rc.3", "@radix-ui/react-presence": "1.1.3-rc.4", "@radix-ui/react-primitive": "2.0.3-rc.3", "@radix-ui/react-compose-refs": "1.1.2-rc.3", "@radix-ui/react-use-layout-effect": "1.1.1-rc.3", "@radix-ui/react-use-controllable-state": "1.1.1-rc.3"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a4d3dc003b40b17f861dd0921915bad3cda931c9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.4.tgz", "fileCount": 8, "integrity": "sha512-EgHeMLxa2Izcs3sScXAo2r+4U1fsq3DiWP0FS1GpK5egjqtQCGoOSlu6kN2l1YWMHOPSm/TqCHnQhIA0/Y6d/A==", "signatures": [{"sig": "MEUCIBOswfmGD2sUDr7V+n93eERofSSup9zXanIsv/29Tfs3AiEAsVkY9rGVZza27tXhy/lsldEgvy3n+S0gUE881WtQ4FI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.5": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.5", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.4", "@radix-ui/primitive": "1.1.2-rc.4", "@radix-ui/react-context": "1.1.2-rc.4", "@radix-ui/react-presence": "1.1.3-rc.5", "@radix-ui/react-primitive": "2.0.3-rc.4", "@radix-ui/react-compose-refs": "1.1.2-rc.4", "@radix-ui/react-use-layout-effect": "1.1.1-rc.4", "@radix-ui/react-use-controllable-state": "1.1.1-rc.4"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ee4451ab1cad6df7a62360f3292d9f9eaf558a95", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.5.tgz", "fileCount": 8, "integrity": "sha512-FIlYbDUHoB4KMgoW+RTUp6+Z+pwVtH544iZmBrYQ3pLXKzCzqFOtHOpZkxXCTEXOoNUFJ4VTKU7reJMYSGI2BQ==", "signatures": [{"sig": "MEUCIHDIav8lVq0meJmBrrdRcTx0OG9FIGumxX2rzMQdjdwvAiEA0jycGKNksoiOJN+s5DMr/x8bXR2+ZLeREDkDFthkaiQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.6": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.6", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.5", "@radix-ui/primitive": "1.1.2-rc.5", "@radix-ui/react-context": "1.1.2-rc.5", "@radix-ui/react-presence": "1.1.3-rc.6", "@radix-ui/react-primitive": "2.0.3-rc.5", "@radix-ui/react-compose-refs": "1.1.2-rc.5", "@radix-ui/react-use-layout-effect": "1.1.1-rc.5", "@radix-ui/react-use-controllable-state": "1.1.1-rc.5"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9f56e32d3546541106cc822387745a21c85401e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.6.tgz", "fileCount": 8, "integrity": "sha512-mtNY1GdXG/5bTDzMsXo1gGhRp7Uq9tRFlfsrxHWq7sPgSNJSDZge9OwTfKENLfhVZ8+QadR2fEaR33Mdd68FVA==", "signatures": [{"sig": "MEUCIQC4yuAAgaGqgMuVV/ydppr2AVbfilsSIWkJjji7BGCQ7wIgCp1hSGe7txzCoLIHLkk85mHWXr+nr+VUDh+C1XqkMmQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.7": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.7", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.6", "@radix-ui/primitive": "1.1.2-rc.6", "@radix-ui/react-context": "1.1.2-rc.6", "@radix-ui/react-presence": "1.1.3-rc.7", "@radix-ui/react-primitive": "2.0.3-rc.6", "@radix-ui/react-compose-refs": "1.1.2-rc.6", "@radix-ui/react-use-layout-effect": "1.1.1-rc.6", "@radix-ui/react-use-controllable-state": "1.1.1-rc.6"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "998c6dd53676737ddc8b54c991b6f5f8c6fdc85b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.7.tgz", "fileCount": 8, "integrity": "sha512-FxrtxHZ9i9my+J5P5w88nw6jY7igTbqsegkiRdqgnVbpkTctOo5yUudY09Ki1/5q3CTBvPf80vvy3KfiT+OGLQ==", "signatures": [{"sig": "MEYCIQC6y1ew7Naz0FaRGYBaHsdE95N4KbhwFG5RVXylNfidVgIhAIkd9J6Qgz9f4mGCR8bvA9IOaQ9Ul8lBw+yqI/8BNe5t", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.8": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.8", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.7", "@radix-ui/primitive": "1.1.2-rc.7", "@radix-ui/react-context": "1.1.2-rc.7", "@radix-ui/react-presence": "1.1.3-rc.8", "@radix-ui/react-primitive": "2.0.3-rc.7", "@radix-ui/react-compose-refs": "1.1.2-rc.7", "@radix-ui/react-use-layout-effect": "1.1.1-rc.7", "@radix-ui/react-use-controllable-state": "1.1.1-rc.7"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dbdd737dce3769b23bf6e09158d8af4de26d7060", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.8.tgz", "fileCount": 8, "integrity": "sha512-UsNH6C1vs/aYHsCE8cIeJNyqYMzDJkQCbg1VsGXWjEEURXuRpjZ6oOa2/lLnCCs0OB/FmFbJ1k4fTFlmATFhdQ==", "signatures": [{"sig": "MEUCIE0e8pO2crUwKQJaL8APaSu1kkbPpxTdt+iEmUhGDc3oAiEA3xlmUA2Mscn5ghi5Ww2ENVj5lpySeUcCxUI5Jp0mG3Y=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43632}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.9": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.9", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.8", "@radix-ui/primitive": "1.1.2-rc.8", "@radix-ui/react-context": "1.1.2-rc.8", "@radix-ui/react-presence": "1.1.3-rc.9", "@radix-ui/react-primitive": "2.0.3-rc.8", "@radix-ui/react-compose-refs": "1.1.2-rc.8", "@radix-ui/react-use-layout-effect": "1.1.1-rc.8", "@radix-ui/react-use-controllable-state": "1.1.1-rc.8"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8c355b4203d1a621fb99e45776ecefbb96f14b04", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.9.tgz", "fileCount": 8, "integrity": "sha512-FKmRzQnCylbHVvvlZI8tYhCxhkNZzEM6VZp8+MOR2IqOO0bNXda6ZMgSIBAgeoCPjISu3n+0EvFt6cDIj1W7yg==", "signatures": [{"sig": "MEQCIFcoJ+nSwEjFjG3R/JkUAwMP7e571cU1BZ8pBGZiO8LjAiA8oKT4P8pyF/Yrew/bpkwvguXhjUOUzs32orKf+EmSOg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44023}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4-rc.10": {"name": "@radix-ui/react-collapsible", "version": "1.1.4-rc.10", "dependencies": {"@radix-ui/react-id": "1.1.1-rc.9", "@radix-ui/primitive": "1.1.2-rc.9", "@radix-ui/react-context": "1.1.2-rc.9", "@radix-ui/react-presence": "1.1.3-rc.10", "@radix-ui/react-primitive": "2.0.3-rc.9", "@radix-ui/react-compose-refs": "1.1.2-rc.9", "@radix-ui/react-use-layout-effect": "1.1.1-rc.9", "@radix-ui/react-use-controllable-state": "1.1.1-rc.9"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "d4f48522b8ef8f6d8335a02f4e3e391930043321", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4-rc.10.tgz", "fileCount": 8, "integrity": "sha512-C6acxm+vi8sjxHpdupBxL0K1Sr46mQexq5f2bOhkaYGriC4ZmtkfrHBTN/wQULit7OyMe6Umzx94ugkzQqoBZQ==", "signatures": [{"sig": "MEQCIB36VVFIyVo0RtpwzeqbxI9yN5BgRA6x5FIcwlhEEeCoAiAKt4Lj0k6mVHuPR13XwvzrAbKUBbFOwrRTfb5rNV7hSQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44025}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.4": {"name": "@radix-ui/react-collapsible", "version": "1.1.4", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.1.1"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "59729939c0c5db978934c688aa5f8229876d8f3f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.4.tgz", "fileCount": 8, "integrity": "sha512-u7LCw1EYInQtBNLGjm9nZ89S/4GcvX1UR5XbekEgnQae2Hkpq39ycJ1OhdeN1/JDfVNG91kWaWoest127TaEKQ==", "signatures": [{"sig": "MEUCIQC7F9kRQuIn/7U6oldx05/rBaJ3UYRexeFBMN6R2NqxdQIgfix5oX8zcRDQSBuf8H7jWFQfNE1tkZbBHtatycj/odk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 43950}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744259191780": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744259191780", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259191780"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "71e141c643160ad9c2185190df90792fe1f37b57", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744259191780.tgz", "fileCount": 9, "integrity": "sha512-5Lh19P7ihpMPO2YuRY6ryGxeaLa8+3ejSU/HHn9Qf1snj+mTHJzZ457RSfueFx8MTNZ0UcP9/dNhtZoYXdDHLw==", "signatures": [{"sig": "MEUCIQCHLVtrO5VzAIlMe5MTAKUK/5ED1WS6JwK7Q+PjPik9+AIgefV8TB+bzUZhzn6vSD9o5Zsf6589YbmlBBNC9rVUxaE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744259481941": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744259481941", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.0.3", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744259481941"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "4864450efa0de510d21340b02819fed206c170bf", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744259481941.tgz", "fileCount": 9, "integrity": "sha512-5ePu6OWk3YDzyPAX6AQ0PenfJGsEU0jcxSloqqc+IJVuw3GVkCyobNXqD9Uxcb19aoIAzcnudDXXFn3r6Y4RhQ==", "signatures": [{"sig": "MEQCICOD6jXaBTxgU7CjGEoRVhGd2jVe/9jE5/5Dg3WVeu9xAiAFa0gHnhsb75cnaK7zT82ms25fnaaHvfVAxHhv7ciJlQ==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744311029001": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744311029001", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744311029001", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744311029001"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "182755c239154cf0d1a56867874c4b9de27c71e3", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744311029001.tgz", "fileCount": 9, "integrity": "sha512-HVdfKfuGdQR80YgmljPw4k7MNMlUt3+JWVsc18rLy34XMnEXjMHMhjqDLiGigHGOJvmm/6khtrkPoNhnV9d8IA==", "signatures": [{"sig": "MEYCIQCfz5FllcDBGtwZvoigXMqbI7/Jafj8L1/XCtqpZEhUjgIhALJp/ATRvOqrdu9hmqzwUSztzwd48uuHjyiZeoz9vd4a", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744416976900": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744416976900", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744416976900", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744416976900"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "62638fe4e664667f4c53f4545dfd96f59442530e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744416976900.tgz", "fileCount": 9, "integrity": "sha512-8UDSMetK+aMJr4vXuvMvFUmlz5RQWiHy8Nr/e3O2wtkX5hngR52LHdFdMgx+eaR1kCdL2PlYhqjvmIvXusGktw==", "signatures": [{"sig": "MEUCIBy7JDBaSxU5TevHbQtSFkYOVaglCkFIqYbWDPnU3ZhQAiEA6j+G2oHql9CDFLqZD0q+lyyjowKF26o+DSe03ncCRAg=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744502104733": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744502104733", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744502104733", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744502104733"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ff37c913062350ed811d6516135363742715710b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744502104733.tgz", "fileCount": 9, "integrity": "sha512-PlqvHFo0nl7eLIfnoAs3gYDxYGOO03/etp8+GjlOFI7B0hYI2GbJMHzQJj7LxycwPtKTTaVFnmiaHzqBgX2+Fg==", "signatures": [{"sig": "MEUCIQD1w0P+ThWyzuvI3KpFu3fm5mfLiGIbsgUGj+eqRbtvRAIgLg+o2x8GmdT+id9CZ06N5jlQEn6V8zFVKQylD6Vs2yc=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744518250005": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744518250005", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744518250005", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744518250005"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7a30f2de3eb5c29b096157b94123939d5aaa6b6b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744518250005.tgz", "fileCount": 9, "integrity": "sha512-dnmZ4lySWwFv3tk5zjZzMk9D5VPYyiIpUyIFCJdXgA8xiGxikBxp40IkyyYBaNeOU/Fd+1pwWZtpqVrbQ5Xqlw==", "signatures": [{"sig": "MEYCIQD36XI8OkigIFUbLVFBjPPriVjU81NOPWxThH5RAkI8uwIhAOTlb3FhyMRGYghn2v/LR4X0NflQf0GxRYwn+5MgfWxX", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744519235198": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744519235198", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744519235198", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744519235198"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "03a8633fb11b70394c2b4e6991df8c8ca5aa8114", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744519235198.tgz", "fileCount": 9, "integrity": "sha512-6mSI50hUw322qpPotmZZuh3cEf1iCSRCYHTCZlF9z7j+4I+LBtUqg/ZoZmeayQ/oDbFSqvJrjkEmO5igmmJoZw==", "signatures": [{"sig": "MEYCIQCxapSDU5YD4Jx2dI4YN8ecymzMra5X8yvWTYHkreMcrgIhAMegElAjcQgIzE7VhPaM7+rSoh1lPmqByUBRd7lAy7I0", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744574857111": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744574857111", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744574857111", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744574857111"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5711d07469314174ba44733ba5a6777d9769b18a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744574857111.tgz", "fileCount": 9, "integrity": "sha512-X/HvIEHMeO0StAQWqYiIUKGJBl/o6917Dd76SjDXV9S8eGEYb4qQ9V8OAFN8pFuoIbgDtN9Ao93cNhkQRDje4Q==", "signatures": [{"sig": "MEUCIQDLshgm2HDYK4oJB8/PDLzU1PF+81aPiBHh3/oEdVtTeQIgMR+X+Ls2YG6mCRZ7Un2mmG6KGE/UPR66fCrrbM6Ej6k=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744660991666": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744660991666", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744660991666", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744660991666"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "0ec410868777599e9eb4473260a83291f76cd8ba", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744660991666.tgz", "fileCount": 9, "integrity": "sha512-WomlPrawkTYiGhoDUEIXvToNKA1WTy+MM6FmpQzitZml0Zpoz/8c/2Fv+AJH0dkUMQR084JPBx3jVBssGBd5KA==", "signatures": [{"sig": "MEUCIQCkGh02hMP5YDgDk8kLU3RQPvE5DiHNdQQXIG1BatydRAIgAMhgcy0EgWz7/qTNRPUndM8tnDSt8A54t4Fe6Xtio5U=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44532}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744661316162": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744661316162", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744661316162", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744661316162"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "9480cb988331da88b94985624cee6389a6c9ef2b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744661316162.tgz", "fileCount": 9, "integrity": "sha512-SknvnfkAAyQcU9VggOpAS+h7osmIAwebhMhRiU/1HvcPlLxzmNbkUWtQNgVgxtRiDkeE+b8xwnh71oYZBsEqpg==", "signatures": [{"sig": "MEUCIQDNqF8ZtvvWhzKpYkAUc0Xycj6P5i10bQQJt3K7bDyliAIgFfs/rlaMiswwrDFas0idl8hvDubzcbaUAzEccl4BcBo=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744830756566": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744830756566", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744830756566", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744830756566"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "aa281b0dea98b721aee7bb2a72eec9fc7d68db4f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744830756566.tgz", "fileCount": 9, "integrity": "sha512-sF4r0vyzS3L2m9SYB23UCnc7V2icKjxnSccNZPLjCetq2Tj+IhRd+OQKJVLZWYbQwC3mVnDpjk2AJd4oSgz1Hg==", "signatures": [{"sig": "MEUCICgDuzYTWfhio8bselcuom48tfWY3/1oNn03i9T0j/4gAiEA6Ilgd+aqMbEysIKNbLgwHYvoLUl48ao+mauBf0m9nQk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744831331200": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744831331200", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744831331200", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744831331200"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "28557a159bf644e41e3a6656c7a8601440775b62", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744831331200.tgz", "fileCount": 9, "integrity": "sha512-CZCugKMZCqbo7r3poLuDd9HTSknVTxXykccPk5WpXgJbtQb9zep87YBARl9wp94ngAu0MFSUIFUzbsh99cockQ==", "signatures": [{"sig": "MEYCIQCYGCXkAwa6l+8Yu6YyqC+3N2htHKFvgl9+7YFAfsNPEgIhAPb+hHXIDZ7m/Hh+WiK21/TWPsR/wy1ap347CCHGUlFR", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744836032308": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744836032308", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744836032308", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744836032308"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "82d548509ee8be799609534120bda2e0b3186db4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744836032308.tgz", "fileCount": 9, "integrity": "sha512-ZaFRtRS7mEOxgb+WPfgWuRl7Y+/NnRPFHdVAlD7F/pDCRCcftMLcVjchoTsedcOQwuOUr0ryFF81XQZ1arPhOw==", "signatures": [{"sig": "MEQCIGO8kdGUqVG8VMnWGxx/W1QwzIobP1nQ6SoQl20MUqQ0AiB2kyO921NKLs9oLXKj20uod/pOZBZqMeTq9CsRvUQf8A==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744897529216": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744897529216", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744897529216", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744897529216"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "f9e11134100d59c2073e33690d39ca4eef6d38fa", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744897529216.tgz", "fileCount": 9, "integrity": "sha512-Zw2zVKVday+LDpf+VJvtl8FuVpy3f4ydluLsKd6Yt/lEWt4eVYsZDqxWK6K1DiTLkEMHvVkczgA5hdx99DZw4Q==", "signatures": [{"sig": "MEUCID/4vA2WSc7/eoQPzq1Fiyq+SmKjjaZg2bUhf3vdAebRAiEA+dn7HTuAdyzBLX7r+XdZ/c4wR5Zkj3s+jiX1cwprGvQ=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744898528774": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744898528774", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744898528774", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744898528774"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "1f619c3e6946ef2c147ef79f012ff190ede8d04e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744898528774.tgz", "fileCount": 9, "integrity": "sha512-GBAlthk+t7rr1B1ZFcKgXVjFrcpU3HeZtdLiPd9MhUMUq2vGo+TR3UNB/PhmYtPuziZ7xIMZd1qNo3p6OS2LBw==", "signatures": [{"sig": "MEUCIQDFjlorB8VVo31DuvD502oFeSwRfXV0QhBmNYSU1PKRFAIgISMnp25cuBOJcIzTROVrAJE/Dn/xinXxIHB5QWCbK2Q=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744905634543": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744905634543", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744905634543", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744905634543"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c7429141dc6f9170852ad79d82d3105b8b18b00c", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744905634543.tgz", "fileCount": 9, "integrity": "sha512-ntT4k6XOywmjvaxSIuo+6SCi0SclkA978KsSuQy7bKf3fUZw3gIytgPrGA6CyPQDlpOUAOSo8LNc8HjD0tS+mw==", "signatures": [{"sig": "MEUCIQDopI28T2B8WcYMTbupRdzM5qfMsEH27WbfHn/QS58+6QIgEeQtYBb8Ol48DdAkIrsPXLd39uCA0/njvll7Aip/FZA=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5-rc.1744910682821": {"name": "@radix-ui/react-collapsible", "version": "1.1.5-rc.1744910682821", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0-rc.1744910682821", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0-rc.1744910682821"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c02316dfdd8040c961b8fa242f7b145b0c2deedc", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5-rc.1744910682821.tgz", "fileCount": 9, "integrity": "sha512-fz/XLmYIFvvYFa0+77CDoOJU5uugWu5p/HJz0CbxW1biVeMLUwJaQ8HOGwK2I09bj0M/Q+mx5T9TUv/WHhakLw==", "signatures": [{"sig": "MEUCIHnbtCo2uFDLCDTtHUfBoFJSSLRpE2KOC6SgyFimTdybAiEA/U9jIi5RNemSP0BOd9aLKYBXDkg+S/JHm7rVz8zMqzI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44698}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.5": {"name": "@radix-ui/react-collapsible", "version": "1.1.5", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.0"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "526746c6e6285bf6a1aa0d764c2ff1874d6d9bb9", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.5.tgz", "fileCount": 9, "integrity": "sha512-OAjDeL9DXNPO6ALnCTPC3vX2wdU97047D/41RP9d9dyyvipwvQqgcCB/ey0qsGEIzTwfbf26Wdpw4nv+YhyzHQ==", "signatures": [{"sig": "MEYCIQDfBoYyE7ZKfztHhaYkLB56Omumzdh6rIJbawjrCxAj9wIhAPCQe5PqfannrjWDNSDMFn0GIZ+lMWas/l5g66lQSSwx", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744998730501": {"name": "@radix-ui/react-collapsible", "version": "1.1.6-rc.1744998730501", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998730501"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "74b3c5bf4ab68ff6a11c65a1a87fd762d79d2b27", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.6-rc.1744998730501.tgz", "fileCount": 9, "integrity": "sha512-6/aFkJ645o6heHAgCS6B3A+GRrh34Zunb8QR+alzco2x3mZXPs0ITNyzmoARNagA8Fc6NUXDP/yN2lvBolSaOw==", "signatures": [{"sig": "MEQCIGEOMQzdjrPajY0OPYcLE4ysBazXouOEMbQNGMifewKAAiBVZ+4UXgqXQDwnCsUFIr60QcJJwl8DN1ikfAWHL/yQ8Q==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744998943107": {"name": "@radix-ui/react-collapsible", "version": "1.1.6-rc.1744998943107", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744998943107"}, "devDependencies": {"react": "^19.0.0", "eslint": "^9.18.0", "react-dom": "^19.0.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "34220fcafa922be204c81f4a5ffd9d4f3e2d86bb", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.6-rc.1744998943107.tgz", "fileCount": 9, "integrity": "sha512-Saz2GcMktpi1oTssJ54FVwKaUuAfmYt3SHVVe1vBlDFk9y3luFv8nhdPnHLABccIQ/lf1iZkH80w5bxBrpHKfw==", "signatures": [{"sig": "MEUCIAihWhnlPhpHXa+motafMyG4kI6mc6EbMigXR5CekVySAiEA8qGtv5BGJWsMW+zqM4RnHy4Gf6et6rlRqW1wWr7qFgI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6-rc.1744999865452": {"name": "@radix-ui/react-collapsible", "version": "1.1.6-rc.1744999865452", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1-rc.1744999865452"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "456ba39a64b9dcb85ddd08532a7cec02d3bc38fe", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.6-rc.1744999865452.tgz", "fileCount": 9, "integrity": "sha512-voC2Vd7p7DeRtaxX+PrYE4SIsZyINa7C0HhBDSzI57n1+y1J5U9kzaUZtwSl8ubZC76418Plt2Y78nDTFet4fw==", "signatures": [{"sig": "MEQCIFgsVPmGyGjK92PKTYiGrBcVM5DmS2D8tc1WisdouOMKAiBJ1yPkCPSpFgvc0LAAsO9YYLUwRQ1MiwlbdUVOSMGYtg==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.6": {"name": "@radix-ui/react-collapsible", "version": "1.1.6", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.1"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5cd61d7c2ef95cc9ccdcf9212c94f725cbaff31a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.6.tgz", "fileCount": 9, "integrity": "sha512-L2K6a4DB0xg2gAhVqdyI7tDdjG995l7AgHVLgk2LB1nnmtJtxbg7pF6+LhMABmuahs3DvI4aq3QqBlhOA9e1gA==", "signatures": [{"sig": "MEYCIQCzyHlqdBs5Yc5kAd6vHNW1HvcCbPbr9l1At40xzQWgHwIhAOWAXYardzzk7SW+AixbrFfnmsh3cjM7moXeFGMI2P0y", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745001912396": {"name": "@radix-ui/react-collapsible", "version": "1.1.7-rc.1745001912396", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745001912396"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "dcb1d613387ed7cd5de841b3f5a100b4b90949b7", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.7-rc.1745001912396.tgz", "fileCount": 9, "integrity": "sha512-0wOT+dIAszRDq+WUBhl65CkrFPqwHzgMEL7u+YB6GdU7nW1HBPucTuByMdLXRE3h56/OEoOEMIPnZoP91TvphA==", "signatures": [{"sig": "MEUCIClwYYcBgDZJ2xC59kKNQ9JJKoSO12w5K6nh6AuSXYNUAiEAjeHrJ1XdHge7CKk2U2sPrfgXCRewn7ejtDxKjFfwU10=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7-rc.1745002236885": {"name": "@radix-ui/react-collapsible", "version": "1.1.7-rc.1745002236885", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2-rc.1745002236885"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "c14437e4fa2b481b7e1466d2e4474ff4ce584e9a", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.7-rc.1745002236885.tgz", "fileCount": 9, "integrity": "sha512-Ob2a3T3IHtgIM8NfBvkXo97pC2HCY8Z8JcjE+RF/o2V5rrAiTFePbUgLEIU6W75N2ZER5AyQU1HfTEQE7KFCCA==", "signatures": [{"sig": "MEUCIFXRhiYFEbg51rLTZ3X26Iohgmbxh04BR/vXljaTzsCSAiEAmYXxtwpFxOyB98L4Ll2+XYmLc7goHvn/s/TPJFD27iI=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.7": {"name": "@radix-ui/react-collapsible", "version": "1.1.7", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.3", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "5eb24b59963156b58ea1eadf5c056d75505da1f8", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.7.tgz", "fileCount": 9, "integrity": "sha512-zGFsPcFJNdQa/UNd6MOgF40BS054FIGj32oOWBllixz42f+AkQg3QJ1YT9pw7vs+Ai+EgWkh839h69GEK8oH2A==", "signatures": [{"sig": "MEUCIQCOifZOb+N4L7FnwdkyErfVfbPF0/VGpA59AofPwpCo3QIgXC6oaY9rcRl+kwX0VBCEuaoYrec3+ZBN73mz8laYkvk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745097595920": {"name": "@radix-ui/react-collapsible", "version": "1.1.8-rc.1745097595920", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745097595920", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "2ea1e22efae0db0affd7f62fc46377363c644b06", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.8-rc.1745097595920.tgz", "fileCount": 9, "integrity": "sha512-Hlz9khQsuH8FbFF88ljzc1QlR1IdgEs+R0pkgurluCD0t6tS72XZswnVdN/PoM4tD0XN8yqe86IcXQjH6cl8kA==", "signatures": [{"sig": "MEQCICP4FKCYU9uNimIOB0vZMsWCAgGET+lgyb3wdmVNPUAuAiAt/qAireFhQ2hjNL+z0f51ePWWoyVQDwUThfVEo/V1dA==", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8-rc.1745339201309": {"name": "@radix-ui/react-collapsible", "version": "1.1.8-rc.1745339201309", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4-rc.1745339201309", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "b9c1ee47a611c13cd79ce580cd57ea86a73d48f2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.8-rc.1745339201309.tgz", "fileCount": 9, "integrity": "sha512-KWfrXaHKJDgzOYvcZbwRNTQFd63Jm3HqaXPwrnrB6y2ibHjHQrRszbIlXi25jmGplIlPIXoLUPH+tJgjnVzirw==", "signatures": [{"sig": "MEUCIQDPrgcScR8363Eqx4usurMXvFopBfvCtvEpB8d3ZG2W3gIgUQJCauJfWjMjL77KSmsMOxvwb+mjZOt5/0W5QCnngR4=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.8": {"name": "@radix-ui/react-collapsible", "version": "1.1.8", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.0", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "133554937046480339d640bf7d80d341af205dc0", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.8.tgz", "fileCount": 9, "integrity": "sha512-hxEsLvK9WxIAPyxdDRULL4hcaSjMZCfP7fHB0Z1uUnDoDBat1Zh46hwYfa69DeZAbJrPckjf0AGAtEZyvDyJbw==", "signatures": [{"sig": "MEUCIQDib+Z9EEclnb+A9i9YtQS+t/h3DLGd5uVKydY5W4wsiQIgIkDeZz0UZh82LjRcbrlUeF/hpGNLSvyw4+7fGgdpjBU=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1745345395380": {"name": "@radix-ui/react-collapsible", "version": "1.1.9-rc.1745345395380", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1-rc.1745345395380", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "e3524ae0e576dddfb5b40d6748e3b9e1e7fc34d4", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9-rc.1745345395380.tgz", "fileCount": 9, "integrity": "sha512-6qSSOshgii/KifZ49q/Nay5fXAHZdyh+fbhtiDu0bza81EkEndgaNQHwUCgiC3d9dJ17fwrtX0Amvg7IxSffYQ==", "signatures": [{"sig": "MEUCIQCTOdwyu9Xmfh5Vd3jYRJi38sVqRRKNHBfsHXpYZFpzeAIgbcxYUSALB6X9CrC+cwlRxDbm1MyRSCiJ627PdWDQ3uE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1745439717073": {"name": "@radix-ui/react-collapsible", "version": "1.1.9-rc.1745439717073", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1-rc.1745439717073", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "46d4b3d0c99c90afbeabb2d13c7d2f37458bf84e", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9-rc.1745439717073.tgz", "fileCount": 9, "integrity": "sha512-wZy8sHTLv4YWw6jsi7FPcOjlXjg+MYWa/m3zYNFaBB7SJXcb3Y3DB8FPHq3vniY2sY9l40TWOR7hyAKQ6lg8rg==", "signatures": [{"sig": "MEUCIQDBdvNv9gu+975hs83KyT1JxgEWm9I3KbRSgw0wo7xWzwIgLYvTKyRn+XhHTeMRjduddxlVAWdioqmmzx8Aujo8q6M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1745972185559": {"name": "@radix-ui/react-collapsible", "version": "1.1.9-rc.1745972185559", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1-rc.1745972185559", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "6c24588e0c74cf68483e1e4ff8efbd1dcc8ea6ab", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9-rc.1745972185559.tgz", "fileCount": 9, "integrity": "sha512-aBfcjl1lkRLoHREaBvlwynHF3nisSvqv2xhgd6n5pg+xu+8htUk1x1wkyEb+kzkgl5yC24oe+WKUlwijl8BgLg==", "signatures": [{"sig": "MEUCIGSY07FoZX6wECHmgde8D0KZKKtYoeMtRdd+LbBv7u94AiEAtzcZd7DcqsKKiJh35oOKv5GeW/w1lvfu68iFoFeO1OE=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1746044551800": {"name": "@radix-ui/react-collapsible", "version": "1.1.9-rc.1746044551800", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1-rc.1746044551800", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "8725ee80972eeb2dd7f361688363659d90da65fc", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9-rc.1746044551800.tgz", "fileCount": 9, "integrity": "sha512-nCwKkB8fOXhnLlOxiwNor3nFn6FMg5EHiP6oWBp9EFaAcBnC861heaCvYhecdSWmMvVpdILzsgHJ+FcgfdSeyg==", "signatures": [{"sig": "MEUCIQDjZWecf0v6VmQeQxC9lV8MGoQppe4jRzWwEuygdJ8YmwIgIuAEBaivnBCN4dFaXj+aeC5T31HP9tguYDgJByuPuTk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1746053194630": {"name": "@radix-ui/react-collapsible", "version": "1.1.9-rc.1746053194630", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1-rc.1746053194630", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "3b98bf36b817f586d31fa6e850673e9cbdb2e424", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9-rc.1746053194630.tgz", "fileCount": 9, "integrity": "sha512-HpMps4lh8+px9zB4R7Egbu43Lqbg9Ax6nlU0s4y1jkR+z3b7JDe5qfLQVMWtBGKKbDJ6Olym4UFeiHhSAAwaNw==", "signatures": [{"sig": "MEUCICziiy+Q5ndSKuUaj9ZQjdyxs9XGefTiBwH0xXAr4hAJAiEAvZFA0v6qdizchwt5mQc6LDz6lcn44NUYXCYf0HqiV34=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1746075822931": {"name": "@radix-ui/react-collapsible", "version": "1.1.9-rc.1746075822931", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1-rc.1746075822931", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "7e620b8ba7fdd90e919d526881109dae41ce8b26", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9-rc.1746075822931.tgz", "fileCount": 9, "integrity": "sha512-VdV2cpvsp26vSj1ZbC1jN1galY7Qp0kugZMxB4GQHMZ8ekfyhv/HujKKiDTlsxKBHMFir13qCIb5bGHSvheQcA==", "signatures": [{"sig": "MEUCIBTCa9Yw003f5/N/x1t5L5z2Pymf60ZDrPRlEWeYJGUhAiEA3TW7Ul75XdsLmcGZeO/JsmF+sT2NUzgQ0rxMg2vSj2I=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9-rc.1746466567086": {"name": "@radix-ui/react-collapsible", "version": "1.1.9-rc.1746466567086", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1-rc.1746466567086", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "41305eaf77bea351c4e7d48b21fa96e021eb7a2f", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9-rc.1746466567086.tgz", "fileCount": 9, "integrity": "sha512-cLsDE+GtOl54aXP/X+d+RK6+2U74UNTODH0kJy4q4W6Y4aJJpluNKoWPx1OyGMo0YUfD6h+hntGDJhQnfu9i3g==", "signatures": [{"sig": "MEYCIQDfL1XIbzZeW7nJy/yKFUGMSAYFAWQdaklvBvnDbINtsgIhAM0nw9zwoSAn79blpMi1dLM7RaCXze7lI/cmf6ieNoKB", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44681}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.9": {"name": "@radix-ui/react-collapsible", "version": "1.1.9", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.1", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "ee4e63999a47231b075b5b9d7756affaab879d51", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.9.tgz", "fileCount": 9, "integrity": "sha512-XszBVO1e+6jkdYy1oc6CAZy9ZLqm+hfySOt5uBO98DlzGvYxaOZEvf4nU5uIkiHTkWLqsGdD+6oHO2TKjIRCwA==", "signatures": [{"sig": "MEUCIQC9fwClr8GFvAxaI6Tih6xF5ewDIT8gNUJF9gKX1/NClAIgWxsRucB5nc2vBHon9fxSqcDgVvkgzLwApYFgo7On3qk=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44647}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.10": {"name": "@radix-ui/react-collapsible", "version": "1.1.10", "dependencies": {"@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-primitive": "2.1.2", "@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-use-controllable-state": "1.2.2"}, "devDependencies": {"react": "^19.1.0", "eslint": "^9.18.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@types/react": "^19.0.7", "@repo/builder": "0.0.0", "@types/react-dom": "^19.0.3", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "@types/react": "*", "@types/react-dom": "*"}, "dist": {"shasum": "a0e75e5cd9666e8c8100d539a9f57c50d113e38b", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.10.tgz", "fileCount": 9, "integrity": "sha512-O2mcG3gZNkJ/Ena34HurA3llPOEA/M4dJtIRMa6y/cknRDC8XY5UZBInKTsUwW5cUue9A4k0wi1XU5fKBzKe1w==", "signatures": [{"sig": "MEUCIQCoxoH9QL79KmIciAY56zaKBxoojz3irT1IZicNq9p3FAIgM9BlNj0Aea/q3giql/Hl8Tipv4EF9B/VUNihm7urL/M=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 44648}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}, "1.1.11-rc.1746560904918": {"name": "@radix-ui/react-collapsible", "version": "1.1.11-rc.1746560904918", "dependencies": {"@radix-ui/react-compose-refs": "1.1.2", "@radix-ui/react-context": "1.1.2", "@radix-ui/react-id": "1.1.1", "@radix-ui/primitive": "1.1.2", "@radix-ui/react-use-controllable-state": "1.2.2", "@radix-ui/react-presence": "1.1.4", "@radix-ui/react-use-layout-effect": "1.1.1", "@radix-ui/react-primitive": "2.1.3-rc.1746560904918"}, "devDependencies": {"@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.7.3", "@repo/builder": "0.0.0", "@repo/eslint-config": "0.0.0", "@repo/typescript-config": "0.0.0"}, "peerDependencies": {"@types/react": "*", "@types/react-dom": "*", "react": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom": "^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc"}, "dist": {"integrity": "sha512-LEDMjv8ID1BWs1CvJg1QsaKrQ4NoYVm8+PhAhvF0P/YeykVKWnk5WqpRIF9J4GEGGnfLNKHrTl5H4WmgOF3FeQ==", "shasum": "2d6719bfd1e2b23d1be3526ca69f4517c1c935a2", "tarball": "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.11-rc.1746560904918.tgz", "fileCount": 9, "unpackedSize": 44694, "signatures": [{"keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U", "sig": "MEYCIQCWvSXT2/RDsjNRBdK2W2zlwLm3mrVE167RW27thUfRRAIhAModmCQfhIikzCUWdtKzmBNiGa2bBFRziJopPcpLnJRz"}]}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "@types/react-dom": {"optional": true}}}}, "modified": "2025-05-06T19:48:48.612Z", "cachedAt": 1747660587607}